package com.car.voc.mail;

import com.car.voc.model.risk.RiskAllTypesModel;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.misc.ExchangeVersion;
import microsoft.exchange.webservices.data.core.enumeration.property.BodyType;
import microsoft.exchange.webservices.data.core.service.item.EmailMessage;
import microsoft.exchange.webservices.data.credential.ExchangeCredentials;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.property.complex.FileAttachment;
import microsoft.exchange.webservices.data.property.complex.MessageBody;
import org.springframework.beans.factory.annotation.Value;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Base64;

public class ExchangeMailUtils {
    @Value("${voc.mail.url}")
    private String mailServer;  //邮件服务器地址
    @Value("${voc.mail.username}")
    private String user;//登录用户名
    @Value("${voc.mail.password}")
    private String password;//登录密码

    public void setMailServer(String mailServer) {
        this.mailServer = mailServer;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    private String domain;//域

    public ExchangeMailUtils(String mailServer, String user, String password, String domain) {
        this.mailServer = mailServer;
        this.user = user;
        this.password = password;
        this.domain = domain;
    }

    public ExchangeMailUtils(RiskAllTypesModel model) {
        this.mailServer = model.getMailServer();
        this.user = model.getUser();
        this.password = model.getPassword();

        System.setProperty("javax.net.ssl.trustStore", model.getPath());
        System.setProperty("javax.net.ssl.trustStorePassword", model.getStroepassword());
    }

    /**
     * 创建邮件服务
     * @return 邮件服务
     */
    private ExchangeService getExchangeService() {
        ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2007_SP1);
        //用户认证信息
        ExchangeCredentials credentials;
        if (domain == null) {
            credentials = new WebCredentials(user, password);

        } else {
            credentials = new WebCredentials(user, password, domain);
        }
        service.setCredentials(credentials);
        try {
            service.setUrl(new URI(mailServer));
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        return service;
    }
    /**
     * 发送带附件的mail
     * @param subject         邮件标题
     * @param to              收件人列表
     * @param cc              抄送人列表
     * @param bodyText        邮件内容
     * @param attachmentPaths 附件地址列表
     * @throws Exception
     */
    public void send(String subject, String[] to, String[] cc, String bodyText, String attachmentPaths,String contentText)
            throws Exception {
        ExchangeService service = getExchangeService();
        EmailMessage msg = new EmailMessage(service);
        msg.setSubject(subject);
        if (attachmentPaths != null) {
//            msg.getAttachments().addFileAttachment("image.jpg",attachmentPaths);
            if(bodyText.contains("data:image")){
                bodyText = bodyText.split(",")[1];
            }
            attachmentPaths = convertBase64ToImage(bodyText,attachmentPaths);
            FileAttachment attachment = msg.getAttachments().addFileAttachment("mail.jpg",attachmentPaths);
            attachment.setContentId("image.jpg");
            attachment.setContentType("image");
            String conet = contentText+"<br>"+ "<img src=\"cid:image.jpg\"> <br>";
            conet = conet + "<a href='http://cc.dfes.com.cn/api/oauth2/login/'> 立即登陆VOC系统，查阅风险详情分析，确认并提交处理 >>>  链接至话务平台对应的登陆页面 </a>";
            msg.setBody(MessageBody.getMessageBodyFromText(conet));
        }else{
            MessageBody body = MessageBody.getMessageBodyFromText(bodyText);
            body.setBodyType(BodyType.HTML);
            msg.setBody(body);
        }

        for (String toPerson : to) {
            msg.getToRecipients().add(toPerson);
        }
        if (cc != null) {
            for (String ccPerson : cc) {
                msg.getCcRecipients().add(ccPerson);
            }
        }

        msg.send();
    }

    /**
     * 发送不带附件的mail
     * @param subject  邮件标题
     * @param to       收件人列表
     * @param cc       抄送人列表
     * @param bodyText 邮件内容
     * @throws Exception
     */
    public void send(String subject, String[] to, String[] cc, String bodyText) throws Exception {
        send(subject, to, cc, bodyText, null,null);
    }


    public static String convertBase64ToImage(String base64Str, String path) {
        byte[] imageBytes = Base64.getDecoder().decode(base64Str);
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage image = ImageIO.read(bis);
            bis.close();
            File outputFile = new File(path);
            ImageIO.write(image, "png", outputFile);
            return outputFile.getAbsolutePath();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
//        ExchangeMailUtils utils = new ExchangeMailUtils();
//        utils.send("test", new String[]{"<EMAIL>"},new String[]{"<EMAIL>"},"test");
    }
}
