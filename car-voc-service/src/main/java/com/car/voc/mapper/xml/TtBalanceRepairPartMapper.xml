<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TtBalanceRepairPartMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TtBalanceRepairPart">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="dealerCode" column="DEALER_CODE" jdbcType="VARCHAR"/>
            <result property="serviceCode" column="SERVICE_CODE" jdbcType="VARCHAR"/>
            <result property="roItemId" column="RO_ITEM_ID" jdbcType="DECIMAL"/>
            <result property="roNo" column="RO_NO" jdbcType="VARCHAR"/>
            <result property="obligatedNo" column="OBLIGATED_NO" jdbcType="CHAR"/>
            <result property="chargePartitionCode" column="CHARGE_PARTITION_CODE" jdbcType="VARCHAR"/>
            <result property="manageSortCode" column="MANAGE_SORT_CODE" jdbcType="VARCHAR"/>
            <result property="storagePositionCode" column="STORAGE_POSITION_CODE" jdbcType="VARCHAR"/>
            <result property="isMainPart" column="IS_MAIN_PART" jdbcType="DECIMAL"/>
            <result property="partNo" column="PART_NO" jdbcType="VARCHAR"/>
            <result property="partName" column="PART_NAME" jdbcType="VARCHAR"/>
            <result property="partQuantity" column="PART_QUANTITY" jdbcType="DECIMAL"/>
            <result property="unitCode" column="UNIT_CODE" jdbcType="CHAR"/>
            <result property="priceType" column="PRICE_TYPE" jdbcType="DECIMAL"/>
            <result property="priceRate" column="PRICE_RATE" jdbcType="DECIMAL"/>
            <result property="storageCode" column="STORAGE_CODE" jdbcType="CHAR"/>
            <result property="activityNo" column="ACTIVITY_NO" jdbcType="VARCHAR"/>
            <result property="highLimitPrice" column="HIGH_LIMIT_PRICE" jdbcType="DECIMAL"/>
            <result property="partCostPrice" column="PART_COST_PRICE" jdbcType="DECIMAL"/>
            <result property="partSalesPrice" column="PART_SALES_PRICE" jdbcType="DECIMAL"/>
            <result property="partCostAmount" column="PART_COST_AMOUNT" jdbcType="DECIMAL"/>
            <result property="partSalesAmount" column="PART_SALES_AMOUNT" jdbcType="DECIMAL"/>
            <result property="discountAmount" column="DISCOUNT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="realReceiveAmount" column="REAL_RECEIVE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="sender" column="SENDER" jdbcType="VARCHAR"/>
            <result property="receiver" column="RECEIVER" jdbcType="VARCHAR"/>
            <result property="sendTime" column="SEND_TIME" jdbcType="VARCHAR"/>
            <result property="isExported" column="IS_EXPORTED" jdbcType="DECIMAL"/>
            <result property="batchNo" column="BATCH_NO" jdbcType="DECIMAL"/>
            <result property="preCheck" column="PRE_CHECK" jdbcType="DECIMAL"/>
            <result property="discount" column="DISCOUNT" jdbcType="DECIMAL"/>
            <result property="isAllowDiscount" column="IS_ALLOW_DISCOUNT" jdbcType="DECIMAL"/>
            <result property="isObligated" column="IS_OBLIGATED" jdbcType="DECIMAL"/>
            <result property="labourCode" column="LABOUR_CODE" jdbcType="VARCHAR"/>
            <result property="packageCode" column="PACKAGE_CODE" jdbcType="VARCHAR"/>
            <result property="isFinished" column="IS_FINISHED" jdbcType="DECIMAL"/>
            <result property="repairTypeCode" column="REPAIR_TYPE_CODE" jdbcType="CHAR"/>
            <result property="isDiscount" column="IS_DISCOUNT" jdbcType="DECIMAL"/>
            <result property="isNeedChoose" column="IS_NEED_CHOOSE" jdbcType="DECIMAL"/>
            <result property="isPrint" column="IS_PRINT" jdbcType="DECIMAL"/>
            <result property="activityType" column="ACTIVITY_TYPE" jdbcType="DECIMAL"/>
            <result property="realStoragePosition" column="REAL_STORAGE_POSITION" jdbcType="VARCHAR"/>
            <result property="diffPartNo" column="DIFF_PART_NO" jdbcType="VARCHAR"/>
            <result property="diffPartName" column="DIFF_PART_NAME" jdbcType="VARCHAR"/>
            <result property="diffPartPrice" column="DIFF_PART_PRICE" jdbcType="DECIMAL"/>
            <result property="diffPartQuantity" column="DIFF_PART_QUANTITY" jdbcType="DECIMAL"/>
            <result property="diffAmount" column="DIFF_AMOUNT" jdbcType="DECIMAL"/>
            <result property="partNature" column="PART_NATURE" jdbcType="DECIMAL"/>
            <result property="partGroup" column="PART_GROUP" jdbcType="DECIMAL"/>
            <result property="partSort" column="PART_SORT" jdbcType="DECIMAL"/>
            <result property="spCode" column="SP_CODE" jdbcType="VARCHAR"/>
            <result property="spApplyCode" column="SP_APPLY_CODE" jdbcType="VARCHAR"/>
            <result property="spSn" column="SP_SN" jdbcType="DECIMAL"/>
            <result property="applyNo" column="APPLY_NO" jdbcType="VARCHAR"/>
            <result property="activityCreditsPackCode" column="ACTIVITY_CREDITS_PACK_CODE" jdbcType="VARCHAR"/>
            <result property="hqVwDealerAmount" column="HQ_VW_DEALER_AMOUNT" jdbcType="DECIMAL"/>
            <result property="hqVwHqAmount" column="HQ_VW_HQ_AMOUNT" jdbcType="DECIMAL"/>
            <result property="promotionType" column="PROMOTION_TYPE" jdbcType="DECIMAL"/>
            <result property="isCreditsActivity" column="IS_CREDITS_ACTIVITY" jdbcType="DECIMAL"/>
            <result property="actItemId" column="ACT_ITEM_ID" jdbcType="DECIMAL"/>
            <result property="actCreditsSource" column="ACT_CREDITS_SOURCE" jdbcType="DECIMAL"/>
            <result property="isStaffVehicle" column="IS_STAFF_VEHICLE" jdbcType="DECIMAL"/>
            <result property="dKey" column="D_KEY" jdbcType="DECIMAL"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateDate" column="UPDATE_DATE" jdbcType="VARCHAR"/>
            <result property="ver" column="VER" jdbcType="DECIMAL"/>
            <result property="orderPrice" column="ORDER_PRICE" jdbcType="DECIMAL"/>
            <result property="entityCode" column="ENTITY_CODE" jdbcType="VARCHAR"/>
            <result property="balanceNo" column="BALANCE_NO" jdbcType="CHAR"/>
            <result property="dbName" column="DB_NAME" jdbcType="VARCHAR"/>
            <result property="syncTime" column="SYNC_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DEALER_CODE,SERVICE_CODE,
        RO_ITEM_ID,RO_NO,OBLIGATED_NO,
        CHARGE_PARTITION_CODE,MANAGE_SORT_CODE,STORAGE_POSITION_CODE,
        IS_MAIN_PART,PART_NO,PART_NAME,
        PART_QUANTITY,UNIT_CODE,PRICE_TYPE,
        PRICE_RATE,STORAGE_CODE,ACTIVITY_NO,
        HIGH_LIMIT_PRICE,PART_COST_PRICE,PART_SALES_PRICE,
        PART_COST_AMOUNT,PART_SALES_AMOUNT,DISCOUNT_AMOUNT,
        REAL_RECEIVE_AMOUNT,SENDER,RECEIVER,
        SEND_TIME,IS_EXPORTED,BATCH_NO,
        PRE_CHECK,DISCOUNT,IS_ALLOW_DISCOUNT,
        IS_OBLIGATED,LABOUR_CODE,PACKAGE_CODE,
        IS_FINISHED,REPAIR_TYPE_CODE,IS_DISCOUNT,
        IS_NEED_CHOOSE,IS_PRINT,ACTIVITY_TYPE,
        REAL_STORAGE_POSITION,DIFF_PART_NO,DIFF_PART_NAME,
        DIFF_PART_PRICE,DIFF_PART_QUANTITY,DIFF_AMOUNT,
        PART_NATURE,PART_GROUP,PART_SORT,
        SP_CODE,SP_APPLY_CODE,SP_SN,
        APPLY_NO,ACTIVITY_CREDITS_PACK_CODE,HQ_VW_DEALER_AMOUNT,
        HQ_VW_HQ_AMOUNT,PROMOTION_TYPE,IS_CREDITS_ACTIVITY,
        ACT_ITEM_ID,ACT_CREDITS_SOURCE,IS_STAFF_VEHICLE,
        D_KEY,CREATE_BY,CREATE_DATE,
        UPDATE_BY,UPDATE_DATE,VER,
        ORDER_PRICE,ENTITY_CODE,BALANCE_NO,
        DB_NAME,SYNC_TIME
    </sql>
</mapper>
