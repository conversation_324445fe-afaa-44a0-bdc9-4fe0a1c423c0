package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.SoundContentVo;
import com.car.voc.model.NPSFilterCriteriaModel;
import com.car.voc.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @ClassName VocNPSAnalysisMapper.java
 * @Description TODO
 * @createTime 2023年07月31日 13:39
 * @Copyright voc
 */
public interface VocNPSAnalysisMapper extends BaseMapper<NPSFilterCriteriaModel> {
//    List<NPSParticipantScoreVo> participantScore(@Param("model") NPSFilterCriteriaModel model);
//    List<NPSParticipantScoreVo> participantScoreDefault(@Param("model") NPSFilterCriteriaModel model);
//    List<NPSAreaScoreVo> participantAreaScoreRanking(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSParticipantScoreVo> participantScoreFilter(@Param("model") NPSFilterCriteriaModel model);
//    List<NPSAreaScoreVo> participantAreaScore(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSAreaScoreVo> participantAreaScoreChian(@Param("model") NPSFilterCriteriaModel model);

    List<NPSParticipantProportionVo> participantProportion(@Param("model") NPSFilterCriteriaModel model);

//    NPSParticipantScoreVo analysisTrendNation (@Param("model") NPSFilterCriteriaModel model);


    List<NPSAnalysisTrendVo> analysisDefault(@Param("model") NPSFilterCriteriaModel model);

    List<NPSAnalysisTrendVo> analysisTrendDefault(@Param("model") NPSFilterCriteriaModel model);

    List<NPSAnalysisTrendVo> analysisTrendArea(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSAnalysisTrendVo> analysisTrendArea2(@Param("model") NPSFilterCriteriaModel model);

    List<NPSAnalysisTrendVo> analysisTrendArea3(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSAnalysisTrendVo> analysisTrendAreaSum(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSAnalysisTrendVo> analysisTrendProvince(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSAnalysisVo> analysisTrendFilter(@Param("model") NPSFilterCriteriaModel model);

    List<NPSCarScoreVo> carSeriesScoreDistribution(@Param("model") NPSFilterCriteriaModel model);

    List<NPSCarEmotionVo> carSeriesEmotionDistribution(@Param("model") NPSFilterCriteriaModel model);

    List<NPSCarEmotionTrendVo> carSeriesEmotionTrend(@Param("model") NPSFilterCriteriaModel model);

    List<NPSHighHotWordsVo> focusAnalysisHotWords(@Param("model") NPSFilterCriteriaModel model);

//    List<NPSParticipantProportionVo> focusDistributionScore(@Param("model") NPSFilterCriteriaModel model);

    List<NPSFousDistributionVo> focusDistributionEmotion(@Param("model") NPSFilterCriteriaModel model);

    List<NPSIntentionAnalysisProportionVo> intentionAnalysisProportion(@Param("model") NPSFilterCriteriaModel model);

    List<NPSIntentionAnalysisProportionVo> intentionAnalysisTrend(@Param("model") NPSFilterCriteriaModel model);

    IPage<NPSDataVo> list(IPage<NPSDataVo> page, NPSFilterCriteriaModel model);

    Map<String, String> userAndStatistic(@Param("model") LabelDetailFilterModel model);

    List<HomePurposeTrendVo> trendChangeLabelList(@Param("model")  LabelDetailFilterModel model);

    List<String> getAnswerCodes(@Param("model")  LabelDetailFilterModel model);
}
