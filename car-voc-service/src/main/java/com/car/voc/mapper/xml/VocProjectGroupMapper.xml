<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocProjectGroupMapper">
    <resultMap id="BaseResultMap" type="com.car.voc.entity.VocProjectGroup">
        <id column="id" property="id"/>
        <result column="project_name" property="projectName"/>
        <result column="leader_id" property="leaderId"/>
        <result column="leaderName" property="leaderName"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <select id="pageList" resultMap="BaseResultMap">
        select project.id,
        project.project_name,
        project.leader_id,
        leader.realname as leaderName,
        project.brand_code,
        project.remark,
        project.`status`,
        project.create_time,
        project.update_time,
        project.create_by,
        project.update_by
        from voc_project_group project
        left join sys_user leader on leader.id = project.leader_id and leader.del_flag = 0
        where
            project.del_flag = 0
            <!-- 返回该项目的所有组员 -->
            <if test="keyword != null and keyword != ''">
                AND ( project.id IN (
                SELECT DISTINCT vpgm.group_id
                FROM voc_project_group_member vpgm
                JOIN sys_user su ON su.id = vpgm.user_id
                WHERE (su.realname LIKE CONCAT('%', #{keyword}, '%'))
                )
                or project.project_name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="brandCode != null and brandCode != ''">
                AND project.brand_code LIKE CONCAT('%', #{brandCode}, '%')
            </if>
        ORDER BY project.create_time DESC
    </select>

    <select id="getMemberByIds" resultType="com.car.voc.entity.VocProjectGroupMember">
        select vpgm.id, vpgm.group_id, vpgm.user_id,su.realname as userName from voc_project_group_member vpgm
        left join sys_user su on vpgm.user_id = su.id and su.del_flag = 0
        where vpgm.group_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
        and vpgm.del_flag = 0
        order by su.create_time desc
    </select>

    <select id="getSeriesByIds" resultType="com.car.voc.entity.VocProjectGroupSeries">
        select vpgs.id, vpgs.group_id, vpgs.brand_code, vpgs.series_code from voc_project_group_series vpgs where vpgs.group_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
        and del_flag = 0
    </select>

    <!-- 获取项目组及其用户 -->
    <select id="getProjectWithUsers" resultType="java.util.Map">
        SELECT
            p.id as project_id,
            p.project_name,
            u.id as user_id,
            u.realname as user_name
        FROM voc_project_group p
                 LEFT JOIN voc_project_group_member pm ON p.id = pm.group_id and pm.del_flag = 0
                 LEFT JOIN (SELECT
                                u.*
                            FROM sys_user u
                                     inner JOIN sys_user_role r ON u.id = r.user_id and r.del_flag = 0
                                     inner JOIN sys_role ro ON r.role_id = ro.id and ro.del_flag = 0 AND ro.role_type = 2 and ro.role_status = 1
        ) u ON pm.user_id = u.id and u.del_flag = 0
        WHERE p.del_flag = 0
          AND p.brand_Code like concat('%',#{brandCode},'%')
          and p.status = 1
        ORDER BY p.id, u.id
    </select>
</mapper>
