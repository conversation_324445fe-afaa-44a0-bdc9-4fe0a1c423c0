package com.car.voc.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRoleBusinessTag;
import com.car.voc.vo.risk.TagRoleVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* @Entity com.car.voc.domain.SysRoleBusinessTag
*/
public interface SysRoleBusinessTagMapper extends BaseMapper<SysRoleBusinessTag> {

    @Select("select * from SYS_ROLE_BUSINESS_TAG where role_id = #{roleId} and brand_code= #{brandCode} and del_flag= '0' ")
    List<SysRoleBusinessTag> listByRoleId(@Param("roleId") String roleId,@Param("brandCode") String brandCode);

    List<TagRoleVo> queryRoleNameByTagCode(@Param("codes") String[] codes);
}
