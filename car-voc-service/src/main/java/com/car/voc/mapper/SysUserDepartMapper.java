package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.SysUser;
import com.car.voc.entity.SysUserDepart;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface SysUserDepartMapper extends BaseMapper<SysUserDepart> {

    @Select("select r.* from sys_user_depart u left join sys_depart r on u.dep_id = r.id where u.del_flag='0' and r.del_flag='0' and  u.user_id = #{userId}")
    SysDepart getDepartInfoByUserId(@Param("userId") String userId);

    @Select("<script>" +
            "select u.user_id userId, u.dep_id departId " +
            "from sys_user_depart u " +
            "where  u.del_flag='0' and u.user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    List<Map> getDepartInfoByUserIds(@Param("userIds") List<String> userIds);

    @Select("SELECT us.* FROM sys_user_depart ud LEFT JOIN sys_user us ON ud.user_id=us.id WHERE   ud.del_flag='0' and us.id is not null and us.status=1 and us.del_flag=0  and ud.dep_id = #{id}")
    List<SysUser> queryUsersByDepId(String id);
}
