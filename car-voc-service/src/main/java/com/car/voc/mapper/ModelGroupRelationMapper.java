package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.ModelGroupRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * @Description: 车型组管理
 *
 * @Date:   2021-04-09
 * @Version: V1.0
 */
public interface ModelGroupRelationMapper extends BaseMapper<ModelGroupRelation> {

    @Select("SELECT m.NAME FROM VOC_MODEL_GROUP_RELATION r LEFT JOIN VOC_BRAND_PRODUCT_MANAGER m ON r.CAR_SERIES_CODE=m.CODE WHERE m.DEL_FLAG=0 AND r.MODEL_GROUP_ID=#{id}")
    Set<String> queryCarNameByModelGroupId(@Param("id") String id);

    @Select("SELECT m.* FROM VOC_MODEL_GROUP_RELATION r LEFT JOIN VOC_BRAND_PRODUCT_MANAGER m ON r.CAR_SERIES_CODE = m.CODE WHERE m.DEL_FLAG = 0 AND r.MODEL_GROUP_ID =#{id}")
    List<BrandProductManager> queryCarByModelGroupId(String id);
}
