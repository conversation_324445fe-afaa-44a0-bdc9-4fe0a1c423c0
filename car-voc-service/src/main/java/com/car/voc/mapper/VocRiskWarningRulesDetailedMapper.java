package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.VocRiskWarningRulesDetailed;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskWarningRulesDetailedMapper.java
 * @Description TODO
 * @createTime 2023年02月06日 13:57
 * @Copyright voc
 */
public interface VocRiskWarningRulesDetailedMapper extends BaseMapper<VocRiskWarningRulesDetailed> {
    List<VocRiskWarningRulesDetailed> getWarnRuleId(@Param("id") String id);
}
