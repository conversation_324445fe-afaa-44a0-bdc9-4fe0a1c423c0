package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRole;
import com.car.voc.entity.SysUserRole;
import com.car.voc.vo.auth.RoleAuthTree;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 角色表 Mapper 接口
 * </p>
 *
 *
 * @since 2018-12-19
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     *
     * @Date 2019/12/13 16:12
     * @Description: 删除角色与用户关系
     */
    @Update("update  sys_user_role set del_flag='1' where role_id = #{roleId} and del_flag='0' ")
    void deleteRoleUserRelation(@Param("roleId") String roleId);
    @Select("select * from sys_user_role where role_id = #{roleId} and del_flag='0' limit 1")
    SysUserRole getUserRoleByRoleId(@Param("roleId") String roleId);

    @Select("select * from sys_role where id = #{roleId} and  del_flag='0' ")
    SysRole getRoleByRoleId(@Param("roleId") String roleId);
    /**
     *
     * @Date 2019/12/13 16:12
     * @Description: 删除角色与权限关系
     */
    @Update("update  sys_role_permission set del_flag='1' where role_id = #{roleId} and del_flag='0' ")
    void deleteRolePermissionRelation(@Param("roleId") String roleId);

    List<RoleAuthTree> roleDataChannel(@Param("roleId") String id);
    List<RoleAuthTree> roleDataSeries(@Param("roleId") String id);
    List<RoleAuthTree> roleDataArea(@Param("roleId") String id);




    @Select("SELECT * FROM ( SELECT d.* FROM sys_role d LEFT JOIN sys_user_role ud on d.id=ud.role_id WHERE d.del_flag='0' and ud.del_flag='0' and ud.USER_ID= #{userId,jdbcType=VARCHAR} ) t WHERE 1=1  LIMIT 1")
    SysRole getRoleByUserId(@Param("userId") String userId);

    List<RoleAuthTree> roleDataPermission(@Param("roleId") String id);
    List<RoleAuthTree> relationBusinessTag(@Param("brandCode") String brandCode,@Param("roleId") String id);
    List<RoleAuthTree> relationQualityTag(@Param("roleId") String id);
    List<RoleAuthTree> brandCodeDataChannel(@Param("brandCode") String brandCode,@Param("roleId") String id);
    List<RoleAuthTree> brandCodeDataSeries(@Param("brandCode") String brandCode,@Param("roleId") String id);
    List<RoleAuthTree> brandCodeDataArea(@Param("brandCode") String brandCode,@Param("roleId") String id);

    SysRole getRoleById(@Param("userId") String userId);

}
