<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.SysCaseClassifyMapper">

    <select id="getCaseBrowseNum" resultType="com.car.voc.vo.SysCaseClassifyListVo">
        SELECT
            scc.*,
            count(cl.id) as caseNum,
            SUM(cl.browse) as browseNum
        FROM
            sys_case_classify scc
        LEFT JOIN
            case_library cl
        ON scc.id = cl.case_classify_id
        WHERE 1 = 1
        <if test="id != null and id != ''">
            AND scc.id = #{id}
        </if>
        <if test="status != null and status != ''">
            AND cl.status = #{status}
        </if>
        GROUP BY scc.id
    </select>

    <select id="getCaseBrowseNumList" resultType="com.car.voc.vo.SysCaseClassifyListVo">
        SELECT
            scc.*,
            count(cl.id) as caseNum,
            SUM(cl.browse) as browseNum
        FROM
            sys_case_classify scc
        LEFT JOIN
            case_library cl
        ON scc.id = cl.case_classify_id
        GROUP BY scc.id
    </select>

    <update id="updateExamine">
        update case_library set update_by = #{model.updateBy},status = #{model.status}
        <if test="model.status == 2">
            ,reason = #{model.reason}
        </if>
        where id = #{model.id}
    </update>
</mapper>
