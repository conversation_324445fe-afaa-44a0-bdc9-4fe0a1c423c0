package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.entity.VocRiskHandlingRecord;
import com.car.voc.vo.risk.RiskAllTypesVo;
import com.car.voc.vo.risk.RiskProcessTimeoutVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskHandlingRecordMapper.java
 * @Description TODO
 * @createTime 2023年02月07日 11:35
 * @Copyright voc
 */
public interface VocRiskHandlingRecordMapper extends BaseMapper<VocRiskHandlingRecord> {
    @Select("SELECT\n" +
            "\trw.*,\n" +
            "\tus.realname AS processUserName,\n" +
            "CASE\n" +
            "\t\tWHEN rw.bind_type = 1 THEN\n" +
            "\t\tdp.depart_name ELSE pg.project_name \n" +
            "\tEND AS processDepartName \n" +
            "FROM\n" +
            "\t( SELECT * FROM VOC_RISK_HANDLING_RECORD ${ew.customSqlSegment}) rw\n" +
            "\tLEFT JOIN SYS_USER us ON rw.PROCESS_USER_ID = us.ID\n" +
            "\tLEFT JOIN SYS_DEPART dp ON rw.process_depart_id = dp.id\n" +
            "\tLEFT JOIN voc_project_group pg ON rw.process_depart_id = pg.id")
    List<VocRiskHandlingRecord> listExtend(@Param(Constants.WRAPPER) QueryWrapper<VocRiskHandlingRecord> wrapper);

    @Select("select \n" +
            "vrhr.id ,\n" +
            "vrhr .warning_risk_id ,\n" +
            "vrhr .process_user_id ,\n" +
            "vrhr .process_depart_id,\n" +
            "vrhr .create_time ,\n" +
            "vrhr .reminder_frequency ,\n" +
            "vrhr .last_reminder_time ,\n" +
            "TIMESTAMPDIFF(DAY,date_format(vrhr .last_reminder_time,'%Y-%m-%d'), NOW()) AS daysDi7,\n"+
            "TIMESTAMPDIFF(DAY, date_format(vrhr .create_time,'%Y-%m-%d'), NOW()) AS daysDifference,\n" +
            "su.realname,\n" +
            "sd.depart_name,\n" +
            "CASE vrat .risk_type WHEN '1' then '业务问题'  WHEN '2' THEN '质量问题'  END  AS riskType,\n" +
            "vrat.risk_name ,\n" +
            "CASE vrat .risk_type WHEN '1' then concat('#',concat(vbt1.name,':',vbt .name),'#')  WHEN '3' THEN vrat .risk_name  else concat('#',concat(vfp3.name,':',vfp.name),'#')  END  AS riskName,\n" +
            "vrat.brand_code,\n" +
            "vbpm.name as brandName \n" +
            "from \n" +
            "voc_risk_handling_record vrhr \n" +
            "left join voc_risk_all_types vrat on vrhr .warning_risk_id =vrat.id and vrat.risk_type !=3\n" +
            "left join sys_user su on vrhr .process_user_id =su .id \n" +
            "left join sys_depart sd on vrhr .process_depart_id =sd.id\n" +
            "left join voc_business_tag vbt on vrat.risk = vbt .tag_code and  vrat .risk_type=1\n" +
            "left join voc_business_tag vbt1 on vbt1.tag_code =left(vrat.risk,7) and  vrat .risk_type=1 \n" +
            "left join voc_fault_problem vfp on vrat .risk =vfp .code and vrat .risk_type=2\n" +
            "left join voc_fault_problem vfp3 on vfp.pid =vfp3.id \n" +
            "left join voc_brand_product_manager vbpm on vrat.brand_code =vbpm.code and vbpm.p_id =0\n" +
            "where \n" +
            "vrhr .process_status in (0,2)\n" +
            "and TIMESTAMPDIFF(DAY, date_format(vrhr .create_time,'%Y-%m-%d'), NOW())>=7\n")
    List<RiskProcessTimeoutVo> processTimeoutRemind(@Param(Constants.WRAPPER) QueryWrapper<VocRiskHandlingRecord> queryWrapper);
}
