<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocRiskAlertReviewerMapper">


    <select id="queryByPage" resultType="com.car.voc.vo.risk.RiskAlertReviewerVo">
        select
               ar.id,
               ar.REVIEWER_USER_ID as reviewerUserId,
               us.realname as reviewerUserName,
               us.username as reviewerUserNo,
               ar.REVIEWER_DEPART_ID as reviewerDepartId,
               dp.depart_name as reviewerDepartName,
               ar.REMARK as remark,
               ar.brand_codes as brandCodes
        from VOC_RISK_ALERT_REVIEWER ar
        left join SYS_USER us on ar.REVIEWER_USER_ID=us.ID and us.del_flag = 0
        left join SYS_DEPART dp on ar.REVIEWER_DEPART_ID = dp.id and dp.del_flag = '0'
        where 1=1
        and ar.del_flag = 0
        <if test="model.searchKeyword != null and model.searchKeyword != ''">
            AND
            (
            us.realname LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            dp.DEPART_NAME_ABBR LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            ar.REMARK LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            dp.DEPART_NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')
            )
        </if>
        <if test="model.brandCodes != null and model.brandCodes != ''">
            and ar.brand_codes like CONCAT('%', #{model.brandCodes} ,'%')
        </if>
        group by ar.id,ar.create_time
        order by ar.create_time desc
    </select>

    <select id="getByIdVo" resultType="com.car.voc.vo.risk.RiskAlertReviewerVo">
        select
                us.id as reviewerUserId,
               us.realname as reviewerUserName,
                dp.ID as reviewerDepartId,
               dp.depart_name as reviewerDepartName
        from  SYS_USER us
        left join SYS_USER_DEPART ud on us.id=ud.USER_ID
        left join SYS_DEPART dp on ud.DEP_ID = dp.id
        where 1=1
        and us.del_flag = 0
        and ud.del_flag = '0'
        and dp.del_flag = '0'
        <if test="model.reviewerUserId!= null and model.reviewerUserId!= ''">
            and us.id=#{model.reviewerUserId}
        </if>

    </select>

    <select id="selectByBrandCodeTagCodeUserId" resultType="com.car.voc.vo.risk.RiskAlertReviewerVo">
        select
            vrpct.process_user_id as reviewerUserId,
            vrpct.process_depart_id as reviewerDepartId
        from
            voc_risk_alert_reviewer vrar
                left join
            voc_risk_process_recipient_tag vrpct on vrar.id = vrpct.recipient_id
                left join
            voc_business_tag vbt on vrpct.tag_id = vbt.id
        where 1 = 1
        and vrar.del_flag = 0
        and vrpct.del_flag = 0
          and vrar.reviewer_user_id = #{userId}
          and vrpct.brand_code = #{brandCode}
        <if test="riskName!= null and riskName!= ''">
          and vbt.tag_code = #{riskName}
        </if>
    </select>

    <select id="problemOverviewLevel" resultType="com.car.voc.vo.risk.RiskAllTypesVo">

        SELECT
        CASE
        WHEN new_risk_level IS NOT NULL THEN new_risk_level
        ELSE risk_level
        END AS riskLevel,
        COUNT(*) AS riskLevelNum
        FROM
            voc_risk_all_types rw
        WHERE
            1 = 1
          AND rw.DEL_FLAG = 0
          AND rw.if_risk = '1'
        <if test="model.brandCode != null and model.brandCode !='' ">
            and rw.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
            and rw.create_time>=#{model.startDate}  AND #{model.endDate}>=rw.create_time
        </if>
        GROUP BY
        riskLevel
    </select>
    <select id="processingProgress" resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
            COUNT(case WHEN rw.`risk_state` = 3 THEN 1 end) as complete,
            COUNT(1) total
        FROM
            voc_risk_all_types rw
        WHERE
            1 = 1
            AND rw.DEL_FLAG = 0
        <if test="model.brandCode != null and model.brandCode !='' ">
            and rw.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
            and rw.create_time>=#{model.startDate}  AND #{model.endDate}>=rw.create_time
        </if>
    </select>
    <select id="problemOverviewDepart" resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        select rhr.*,case when bind_type = 1 THEN sd.depart_name else vpg.project_name end as departName from (SELECT
                           hr.bind_type,
                           hr.process_depart_id  AS departId,
                              count(DISTINCT rw.id) AS total
                       FROM voc_risk_all_types rw
                                LEFT JOIN (SELECT vhd.*
                                           FROM voc_risk_handling_record vhd
                                                    RIGHT JOIN (SELECT max(create_time) AS create_time, warning_risk_id
                                                                FROM voc_risk_handling_record
                                                                WHERE del_flag = 0
                                                                GROUP BY warning_risk_id) t
                                                               ON vhd.warning_risk_id = t.warning_risk_id AND
                                                                  vhd.create_time = t.create_time
                                                                where vhd.del_flag = 0) hr
                                          ON rw.id = hr.warning_risk_id
                       WHERE 1 = 1
                         AND rw.DEL_FLAG = 0
                        <if test="model.brandCode != null and model.brandCode !='' ">
                            and rw.brand_code = #{model.brandCode}
                        </if>
                        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
                            and rw.create_time>=#{model.startDate}  AND #{model.endDate}>=rw.create_time
                        </if>
                       GROUP BY hr.process_depart_id) as rhr
            left join sys_depart sd on rhr.departId = sd.id and sd.del_flag = 0 and rhr.bind_type = 1
            left join voc_project_group vpg on rhr.departId = vpg.id and vpg.del_flag = 0 and rhr.bind_type = 2
        where 1=1
            and rhr.departId is not null
    </select>
</mapper>
