<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TtSalesAtsRepairRoNtMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TtSalesAtsRepairRoNt">
            <result property="serviceCode" column="SERVICE_CODE" jdbcType="VARCHAR"/>
            <result property="roCreateDate" column="RO_CREATE_DATE" jdbcType="VARCHAR"/>
            <result property="useKind" column="USE_KIND" jdbcType="VARCHAR"/>
            <result property="useKindDesc" column="USE_KIND_DESC" jdbcType="VARCHAR"/>
            <result property="businessKind" column="BUSINESS_KIND" jdbcType="VARCHAR"/>
            <result property="inMileage" column="IN_MILEAGE" jdbcType="DECIMAL"/>
            <result property="roTroubleDesc" column="RO_TROUBLE_DESC" jdbcType="VARCHAR"/>
            <result property="vin" column="VIN" jdbcType="VARCHAR"/>
            <result property="modelCode" column="MODEL_CODE" jdbcType="VARCHAR"/>
            <result property="salesDate" column="SALES_DATE" jdbcType="VARCHAR"/>
            <result property="license" column="LICENSE" jdbcType="VARCHAR"/>
            <result property="deliverer" column="DELIVERER" jdbcType="VARCHAR"/>
            <result property="seriesCode" column="SERIES_CODE" jdbcType="VARCHAR"/>
            <result property="serviceAdvisor" column="SERVICE_ADVISOR" jdbcType="VARCHAR"/>
            <result property="serviceAdvisorAss" column="SERVICE_ADVISOR_ASS" jdbcType="VARCHAR"/>
            <result property="bookingOrderNo" column="BOOKING_ORDER_NO" jdbcType="VARCHAR"/>
            <result property="roNo" column="RO_NO" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="VARCHAR"/>
            <result property="updateDate" column="UPDATE_DATE" jdbcType="VARCHAR"/>
            <result property="syncTime" column="SYNC_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        SERVICE_CODE,RO_CREATE_DATE,USE_KIND,
        USE_KIND_DESC,BUSINESS_KIND,IN_MILEAGE,
        RO_TROUBLE_DESC,VIN,MODEL_CODE,
        SALES_DATE,LICENSE,DELIVERER,
        SERIES_CODE,SERVICE_ADVISOR,SERVICE_ADVISOR_ASS,
        BOOKING_ORDER_NO,RO_NO,CREATE_DATE,
        UPDATE_DATE,SYNC_TIME
    </sql>
</mapper>
