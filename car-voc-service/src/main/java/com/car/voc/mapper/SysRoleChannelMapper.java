package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRoleChannel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* @Entity com.car.voc.domain.SysRoleChannel
*/
public interface SysRoleChannelMapper extends BaseMapper<SysRoleChannel> {

    @Select("select * from SYS_ROLE_CHANNEL where role_id = #{roleId} and brand_code= #{brandCode} and del_flag ='0' ")
    List<SysRoleChannel> listByRoleId(@Param("roleId") String roleId,@Param("brandCode") String brandCode);


}
