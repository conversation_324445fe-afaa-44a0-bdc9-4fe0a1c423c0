package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.entity.VocProjectGroup;
import com.car.voc.entity.VocProjectGroupMember;
import com.car.voc.entity.VocProjectGroupSeries;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目组信息表(VocProjectGroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-02 14:32:36
 */
public interface VocProjectGroupMapper extends BaseMapper<VocProjectGroup> {

    /**
     * 获取项目组及其用户
     */
    List<Map<String, Object>> getProjectWithUsers(@Param("brandCode") String brandCode);

    Page<VocProjectGroup> pageList(Page<VocProjectGroup> page, @Param("brandCode") String brandCode, @Param("keyword") String keyword);

    List<VocProjectGroup> pageList(@Param("brandCode") String brandCode, @Param("keyword") String keyword);

    List<VocProjectGroupMember> getMemberByIds(@Param("ids") List<String> ids);

    List<VocProjectGroupSeries> getSeriesByIds(@Param("ids") List<String> ids);
}

