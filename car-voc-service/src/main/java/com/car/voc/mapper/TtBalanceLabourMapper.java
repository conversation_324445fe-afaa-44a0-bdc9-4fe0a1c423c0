package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.TtBalanceLabour;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* @Entity com.car.voc.entity.TtBalanceLabour
*/
public interface TtBalanceLabourMapper extends BaseMapper<TtBalanceLabour> {


    @Select("select * from TT_BALANCE_LABOUR where SERVICE_CODE = #{serviceCode} and trim(balance_no) = #{balanceNo}")
    List<TtBalanceLabour> getOneByBalanceNoss (String balanceNo, String serviceCode);

}
