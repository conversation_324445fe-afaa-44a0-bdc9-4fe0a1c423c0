<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.BrandProductManagerMapper">

    <select id="queryByPage" resultType="com.car.voc.entity.BrandProductManager">
        SELECT
            id AS id,
            del_flag AS delFlag,
            create_by AS createBy,
            create_time AS createTime,
            update_by AS updateBy,
            update_time AS updateTime,
            industry AS industry,
            car_type AS carType,
            car_space_type AS carSpaceType,
            type AS type,
            relation_id AS relationId,
            NAME AS name,
            english_name AS englishName,
            country AS country,
            remark AS remark,
            show_img AS showImg,
            screen_img AS screenImg,
            p_id AS pId,
            ALIAS as alias,
            enable as enable,
        energy_type as energyType,
        energy_supply_type as energySupplyType,
            industry_text AS industryText,
            CODE AS code,
            sort_no AS sortNo,
            has_child AS hasChild
        FROM
            voc_brand_product_manager
        WHERE 1=1
        AND del_flag =0
        and p_id !='0'
        <if test="model.delFlag != null">
            AND del_flag = #{model.delFlag}
        </if>
        <if test="model.industry != null">
            AND industry = #{model.industry}
        </if>
        <if test="model.brandCode != null and model.brandCode !='' ">
            and p_id in (select id from voc_brand_product_manager where code =#{model.brandCode})
        </if>
        <if test="model.searchKeyword != null and model.searchKeyword !=''">
          and (
            NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            english_name LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            alias LIKE CONCAT('%', #{model.searchKeyword} ,'%')
            )
        </if>

        ORDER BY sort_no asc

    </select>

</mapper>
