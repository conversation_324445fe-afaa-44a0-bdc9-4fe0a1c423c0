<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TtDealerAfterMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TtDealerAfter">
            <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
            <result property="afterSalesCode" column="AFTER_SALES_CODE" jdbcType="VARCHAR"/>
            <result property="dealerName" column="DEALER_NAME" jdbcType="VARCHAR"/>
            <result property="dealerShortname" column="DEALER_SHORTNAME" jdbcType="VARCHAR"/>
            <result property="lastAfterSaleCode" column="LAST_AFTER_SALE_CODE" jdbcType="VARCHAR"/>
            <result property="parentCode" column="PARENT_CODE" jdbcType="VARCHAR"/>
            <result property="areaCode" column="AREA_CODE" jdbcType="VARCHAR"/>
            <result property="agencyCode" column="AGENCY_CODE" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="PROVINCE_CODE" jdbcType="VARCHAR"/>
            <result property="cityCode" column="CITY_CODE" jdbcType="VARCHAR"/>
            <result property="countyCode" column="COUNTY_CODE" jdbcType="VARCHAR"/>
            <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BRAND,AFTER_SALES_CODE,DEALER_NAME,
        DEALER_SHORTNAME,LAST_AFTER_SALE_CODE,PARENT_CODE,
        AREA_CODE,AGENCY_CODE,PROVINCE_CODE,
        CITY_CODE,COUNTY_CODE,ADDRESS
    </sql>
</mapper>
