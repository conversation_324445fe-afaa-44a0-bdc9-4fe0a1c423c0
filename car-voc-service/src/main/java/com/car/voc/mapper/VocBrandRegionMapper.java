package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.ModelGroup;
import com.car.voc.entity.SysDictItem;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.vo.RegionalDictVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 车型组管理
 *
 * @Date:   2021-04-09
 * @Version: V1.0
 */
public interface VocBrandRegionMapper extends BaseMapper<VocBrandRegion> {

    List<RegionalDictVo> queryBrandRegion(@Param("brandCode") String brandCode, @Param("roleId") String id);

    @Select("select vbr.regional_code as itemValue, vbr.regional_name as itemTextEn, vbr.regional_name as itemText from voc_brand_region vbr group by vbr.regional_code,vbr.regional_name union all select vbr.community_code  as itemValue, vbr.regional_name as itemTextEn, vbr.community_name  as itemText from voc_brand_region vbr group by vbr.community_code,vbr.community_name union all select vbr.city_code  as itemValue, vbr.regional_name as itemTextEn, vbr.city_name  as itemText from voc_brand_region vbr where vbr.city_code is not null group by vbr.city_code,vbr.city_name")
    List<SysDictItem> queryInternational();
}
