<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.SysUserMapper">

    <!-- 根据用户名查询 -->
    <select id="getUserByName" resultType="com.car.voc.entity.SysUser">
        select *
        from sys_user
        where username = #{username}
          and del_flag = 0
    </select>

    <!-- 批量删除角色的与用户关系-->
    <update id="deleteBathRoleUserRelation">
        delete from sys_user_role
        where role_id in
        <foreach item="id" collection="roleIdArray" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量删除角色的与权限关系-->
    <update id="deleteBathRolePermissionRelation">
        delete from sys_role_permission
        where role_id in
        <foreach item="id" collection="roleIdArray" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!-- 查询用户的所属部门名称信息 -->
    <select id="getDepNamesByUserIds" resultType="com.car.voc.vo.SysUserDepVo">
        select d.depart_name,ud.user_id from sys_user_depart ud,sys_depart d where d.id = ud.dep_id and ud.user_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getRolNamesByUserIds" resultType="com.car.voc.vo.SysUserDepVo">
        select d.role_name as depart_name,ud.user_id from sys_user_role ud,sys_role d where d.id = ud.role_id and ud.user_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <!-- 更新空字符串为null -->
    <update id="updateNullByEmptyString">
        UPDATE sys_user SET ${fieldName} = NULL WHERE ${fieldName} = ''
    </update>


</mapper>
