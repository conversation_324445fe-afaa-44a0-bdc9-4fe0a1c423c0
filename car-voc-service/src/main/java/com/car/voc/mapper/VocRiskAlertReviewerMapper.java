package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.entity.VocRiskAlertReviewer;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.model.risk.RiskAlertReviewerModel;
import com.car.voc.vo.risk.RiskAlertReviewerVo;
import com.car.voc.vo.risk.RiskAllTypesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @version 1.0.0
 * @ClassName VocRiskAlertReviewerMapper.java
 * @Description TODO
 * @createTime 2023年02月06日 10:29
 * @Copyright voc
 */
public interface VocRiskAlertReviewerMapper extends BaseMapper<VocRiskAlertReviewer> {
    IPage<RiskAlertReviewerVo> queryByPage(Page<RiskAlertReviewerVo> page, @Param("model") VocRiskAlertReviewer model);

    RiskAlertReviewerVo getByIdVo(@Param("model") RiskAlertReviewerModel model);

    List<RiskAlertReviewerVo> selectByBrandCodeTagCodeUserId(@Param("userId") String id, @Param("brandCode") String brandCode, @Param("riskName") String riskNameSubstring);

    List<RiskAllTypesVo> problemOverviewLevel(@Param("model") FilterCriteriaModel model);

    List<RiskAllTypesVo> problemOverviewDepart(@Param("model") FilterCriteriaModel model);

    RiskAllTypesVo processingProgress(@Param("model") FilterCriteriaModel model);
}
