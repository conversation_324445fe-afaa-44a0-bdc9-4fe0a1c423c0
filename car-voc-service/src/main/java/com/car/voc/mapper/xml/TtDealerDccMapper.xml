<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TtDealerDccMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TtDealerDcc">
            <result property="dealerCode" column="DEALER_CODE" jdbcType="VARCHAR"/>
            <result property="dealerName" column="DEALER_NAME" jdbcType="VARCHAR"/>
            <result property="dealerShortName" column="DEALER_SHORT_NAME" jdbcType="VARCHAR"/>
            <result property="dealerEnglishName" column="DEALER_ENGLISH_NAME" jdbcType="VARCHAR"/>
            <result property="dealerBillCode" column="DEALER_BILL_CODE" jdbcType="VARCHAR"/>
            <result property="brand" column="BRAND" jdbcType="VARCHAR"/>
            <result property="saleType" column="SALE_TYPE" jdbcType="VARCHAR"/>
            <result property="dealerTypeCode" column="DEALER_TYPE_CODE" jdbcType="VARCHAR"/>
            <result property="dealerTypeName" column="DEALER_TYPE_NAME" jdbcType="VARCHAR"/>
            <result property="dealerTypeDetailCode" column="DEALER_TYPE_DETAIL_CODE" jdbcType="VARCHAR"/>
            <result property="dealerTypeDetailName" column="DEALER_TYPE_DETAIL_NAME" jdbcType="VARCHAR"/>
            <result property="areaCode" column="AREA_CODE" jdbcType="VARCHAR"/>
            <result property="agencyCode" column="AGENCY_CODE" jdbcType="VARCHAR"/>
            <result property="smallAreaCode" column="SMALL_AREA_CODE" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="PROVINCE_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DEALER_CODE,DEALER_NAME,DEALER_SHORT_NAME,
        DEALER_ENGLISH_NAME,DEALER_BILL_CODE,BRAND,
        SALE_TYPE,DEALER_TYPE_CODE,DEALER_TYPE_NAME,
        DEALER_TYPE_DETAIL_CODE,DEALER_TYPE_DETAIL_NAME,AREA_CODE,
        AGENCY_CODE,SMALL_AREA_CODE,PROVINCE_CODE
    </sql>
</mapper>
