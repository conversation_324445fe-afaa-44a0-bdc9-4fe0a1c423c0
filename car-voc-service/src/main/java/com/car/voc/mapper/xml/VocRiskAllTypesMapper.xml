<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocRiskAllTypesMapper">
    <select id="auditList"  resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
        rw.ID,
        rw.RISK_NAME as riskName,
        rw.RISK_ID as riskId,
        rw.AUDIT_USER_ID as auditUserId,
        us.USERNAME as auditUserName,
        rw.AUDIT_DEPART_ID as auditDepartId,
        dp.DEPART_NAME_ABBR as auditDepartName,
        rw.AUDIT_TIME as auditTime,
        rw.CONFIRMATION_TIME as confirmationTime,
        rw.RISK_TYPE as riskType,
        rw.RISK,
        rw.WARNING_NUM as warningNum,
        rw.STATISTIC as statistic,
        rw.USER_NUM as userNum,
        rw.CREATE_TIME as createTime,
        rw.RISK_STATE as riskState,
        rw.risk_Level as riskLevel,
        rw.new_risk_level as newRiskLevel
        FROM
        VOC_RISK_ALL_TYPES rw
        left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
        left join SYS_DEPART dp on rw.AUDIT_DEPART_ID = dp.id
        <if test="model.userId != null and model.userId !='' ">
        left JOIN VOC_RISK_HANDLING_RECORD hr ON rw.id=hr.warning_risk_id
        </if>

        WHERE
        1=1
        AND rw.DEL_FLAG =0
        and rw.IF_RISK=1
        and us.del_flag = 0
        and dp.del_flag = '0'
        <if test="model.riskState != null">
            AND rw.RISK_STATE = #{model.riskState}
        </if>
        <if test="model.riskType != null and model.riskType !='' ">
            AND rw.risk_type = #{model.riskType}
        </if>

        <if test="model.tagType != null and model.tagType ==2 ">
            AND rw.RISK_STATE !=0

            <if test="model.userId != null and model.userId !='' ">
                AND hr.PROCESS_USER_ID= #{model.userId}
            </if>
        </if>



        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and rw.CREATE_TIME>=#{model.startDate}
            AND #{model.endDate}>=rw.CREATE_TIME
        </if>

        <if test="model.tagType != null and model.tagType ==2 ">
            <if test="model.column != null and model.column !='' ">
                order by ${model.column} ${model.order}
            </if>
            <if test="model.column == null or model.column =='' ">
                ORDER BY rw.STATISTIC DESC,rw.id asc

                <!-- order by rw.RISK_STATE desc,rw.CREATE_TIME desc -->
            </if>
        </if>
        <if test="model.tagType != null and model.tagType ==1 ">
            <if test="model.column != null and model.column !='' and model.order !='' and model.order != null ">
                order by ${model.column} ${model.order} ,rw.id asc
            </if>
            <if test="model.column == null or model.column =='' ">
                ORDER BY rw.STATISTIC DESC,rw.id asc

               <!-- order by rw.RISK_STATE desc,rw.CREATE_TIME desc -->
            </if>
        </if>
    </select>

     <select id="taskListTop"  resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
        rw.ID,
        rw.RISK_NAME as riskName,
        rw.RISK_ID as riskId,
        rw.AUDIT_USER_ID as auditUserId,
        us.USERNAME as auditUserName,
        rw.AUDIT_DEPART_ID as auditDepartId,
        dp.DEPART_NAME_ABBR as auditDepartName,
        rw.AUDIT_TIME as auditTime,
        rw.CONFIRMATION_TIME as confirmationTime,
        rw.RISK_TYPE as riskType,
        rw.RISK,
        rw.WARNING_NUM as warningNum,
        rw.STATISTIC as statistic,
        rw.USER_NUM as userNum,
        rw.CREATE_TIME as createTime,
        rw.RISK_STATE as riskState,
         rw.risk_level as riskLevel,
         rw.new_risk_level as newRiskLevel
        FROM
        VOC_RISK_ALL_TYPES rw
        left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
        left join SYS_DEPART dp on rw.AUDIT_DEPART_ID = dp.id
        WHERE
        1=1
        AND rw.DEL_FLAG =0
        and rw.IF_RISK=1
        AND rw.RISK_STATE >0
         and us.del_flag = 0
         and dp.del_flag = '0'
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and rw.CREATE_TIME>=#{model.startDate}
            AND #{model.endDate}>=rw.CREATE_TIME
        </if>
         ORDER BY rw.audit_time DESC

    </select>

    <select id="riskLevelDis"  resultType="java.util.Map">
        select
        risk_level,
        count(risk_level) as riskLevelNum
        from
        (

        select
        vrwrd.risk_level
        from(

            select
            tdver.risk_index,
            tdver.brand_code
            from voc_risk_all_types vrat
            left join tf_dwd_voc_emotion_risk tdver on vrat .risk_id =tdver .id
            where 1 = 1
            and tdver.risk_index is not null

            and vrat .risk_state >0
            and vrat .del_flag=0
            <if test="model.startDate !=null and model.startDate != ''and
                                            model.endDate !=null and model.endDate != ''">
                and vrat.create_time>=#{model.startDate}
                AND #{model.endDate}>=vrat.create_time
            </if>

        ) vv

        left join

        voc_risk_warning_rules vrwr on vrwr.brand_code = vv.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd
        on  vrwr.id = vrwrd.warn_rule_id
        where 1 = 1
        and vrwr.risk_type = '风险事件洞察'
        and vrwrd.type = '2'
        and ( vv.risk_index > vrwrd.risk_level_min and vrwrd.risk_level_max > vv.risk_index )


        union all

        select
        vrwrd.risk_level
        from(

        select
        tdvqr.risk_index,
        tdvqr.brand_code
        from voc_risk_all_types vrat
        left join tf_dwd_voc_quality_risk tdvqr on vrat .risk_id =tdvqr .id
        where 1 = 1
        and tdvqr.risk_index is not null
        and vrat .risk_state >0
        and vrat .del_flag=0
        <if test="model.startDate !=null and model.startDate != ''and
										model.endDate !=null and model.endDate != ''">
            and vrat.create_time>=#{model.startDate}
            AND #{model.endDate}>=vrat.create_time
        </if>

        ) vv

        left join

        voc_risk_warning_rules vrwr on vrwr.brand_code = vv.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd
        on  vrwr.id = vrwrd.warn_rule_id
        where 1 = 1
        and vrwr.risk_type = '质量问题风险'
        and vrwrd.type = '2'
        and ( vv.risk_index > vrwrd.risk_level_min and  vrwrd.risk_level_max > vv.risk_index)

        union all


        select
        vrwrd.risk_level
        from(

        select
        tdvqr.risk_index,
        tdvqr.brand_code
        from voc_risk_all_types vrat
        left join tf_dwd_voc_user_risk tdvqr on vrat .risk_id =tdvqr .id
        where 1 = 1
        and tdvqr.risk_index is not null
        and vrat .risk_state >0
        and vrat .del_flag=0
        <if test="model.startDate !=null and model.startDate != ''and
										model.endDate !=null and model.endDate != ''">
            and vrat.create_time>=#{model.startDate}
            AND #{model.endDate}>=vrat.create_time
        </if>

        ) vv

        left join

        voc_risk_warning_rules vrwr on vrwr.brand_code = vv.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd
        on  vrwr.id = vrwrd.warn_rule_id
        where 1 = 1
        and vrwr.risk_type = '高频投诉用户'
        and vrwrd.type = '2'
        and ( vv.risk_index > vrwrd.risk_level_min and   vrwrd.risk_level_max > vv.risk_index)

        ) vq
        group by vq.risk_level


    </select>


<!-- <select id="riskLevelDis"  resultType="java.util.LinkedHashMap">-->
<!--     SELECT-->
<!--     sum(CASE WHEN tdvur.risk_index >= 90 OR  tdver.risk_index >= 90 OR tdvqr.risk_index >= 90 THEN 1 END) AS "S" ,-->
<!--     sum(CASE WHEN (tdvur.risk_index > 70 AND 90 > tdvur.risk_index) or 	(tdver.risk_index > 70 AND 90 > tdver.risk_index) OR (tdvqr.risk_index > 70 AND 90 > tdvqr.risk_index) THEN 1 END) AS "A" ,-->
<!--     sum(CASE WHEN (tdvur.risk_index > 50 AND 70 >= tdvur.risk_index) or (tdver.risk_index > 50 AND 70 >= tdver.risk_index) OR (tdvqr.risk_index > 50 AND 70 >= tdvqr.risk_index) THEN 1 END) AS "B" ,-->
<!--     sum(CASE WHEN (tdvur.risk_index > 30 AND 50 >= tdvur.risk_index) or (tdver.risk_index > 30 AND 50 >= tdver.risk_index) OR (tdvqr.risk_index > 30 AND 50 >= tdvqr.risk_index) THEN 1 END) AS "C" ,-->
<!--     sum(CASE WHEN (tdvur.risk_index >= 0 AND 30 >= tdvur.risk_index) or (tdver.risk_index >= 0 AND 30 >= tdver.risk_index) OR (tdvqr.risk_index >= 0 AND 30 >= tdvqr.risk_index) THEN 1 END) AS "D"-->
<!--     from voc_risk_all_types vrat-->
<!--     LEFT join tf_dwd_voc_user_risk tdvur on vrat .risk_id =tdvur .id-->
<!--     LEFT JOIN tf_dwd_voc_emotion_risk tdver ON vrat.risk_id = tdver.id-->
<!--     LEFT JOIN tf_dwd_voc_quality_risk tdvqr ON vrat.risk_id = tdvqr.id-->
<!--     where-->
<!--    &lt;!&ndash; 状态(0:待审核,1:已审核,2:待处理,3:已处理;4:部分处理)&ndash;&gt;-->
<!--     vrat .risk_state >0-->

<!--    and vrat .del_flag=0-->
<!--     <if test="model.startDate !=null and model.startDate != ''and-->
<!--                  model.endDate !=null and model.endDate != ''">-->
<!--         and vrat.create_time>=#{model.startDate}-->
<!--         AND #{model.endDate}>=vrat.create_time-->
<!--     </if>-->


<!-- </select>-->
<select id="riskDepsList"  resultType="java.util.Map">
    select
    sd.depart_name as  departName,
    count(distinct vrhr.warning_risk_id ) as total
    from voc_risk_all_types vrat
    left join voc_risk_handling_record vrhr on vrat .id =vrhr .warning_risk_id
    left join sys_depart sd on vrhr .process_depart_id=sd.id
    where
    <!-- 状态(0:待审核,1:已审核,2:待处理,3:已处理;4:部分处理)-->
     vrat .risk_state >0
    and vrat .del_flag=0
    and sd.del_flag = '0'
     <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
         and vrat.create_time>=#{model.startDate}
         AND #{model.endDate}>=vrat.create_time
     </if>
    group by sd .depart_name
    order by total desc

</select>
<select id="processingProgress"  resultType="java.util.Map">
    SELECT
    (SELECT count(1) from voc_risk_all_types
    where risk_state >0
    AND del_flag =0

    <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
     and create_time>=#{model.startDate} AND #{model.endDate}>=create_time

        </if>
        ) AS total,
    (SELECT count(1) from voc_risk_all_types  where risk_state =3

    AND del_flag =0
    <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
        and create_time>=#{model.startDate} AND #{model.endDate}>=create_time
    </if>
    ) AS complete;
</select>

    <select id="riksListByStatus" resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
            rw.ID,
            rw.RISK_NAME as riskName,
            rw.RISK_ID as riskId,
            rw.AUDIT_USER_ID as auditUserId,
            us.USERNAME as auditUserName,
            hr.process_depart_id as auditDepartId,
            hr.bind_type AS bindType,
            dp.DEPART_NAME_ABBR as auditDepartName,
            rw.AUDIT_TIME as auditTime,
            rw.CONFIRMATION_TIME as confirmationTime,
            rw.RISK_TYPE as riskType,
            rw.RISK,
            rw.WARNING_NUM as warningNum,
            rw.STATISTIC as statistic,
            rw.USER_NUM as userNum,
            rw.CREATE_TIME as createTime,
            rw.RISK_STATE as riskState,
            rw.risk_Level as riskLevel,
            rw.new_risk_level as newRiskLevel,
            rw.brand_code as brandCode,
            hrd.num as  processingFrequency,
            rw.IF_RISK as ifRisk,
            hr.process_status as processStatus,
            hr.process_user_id as processUserId,
            rw.statistic_type as statisticType
        FROM
            VOC_RISK_ALL_TYPES rw
                left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
                left join SYS_DEPART dp on rw.AUDIT_DEPART_ID = dp.id
                left JOIN VOC_RISK_HANDLING_RECORD hr ON rw.id=hr.warning_risk_id
                left join (select count(*) as num,risk,brand_code from voc_risk_all_types  where risk_state=3  group by risk,brand_code) hrd on hrd.risk= rw.risk and hrd.brand_code= rw.brand_code

        WHERE
            1=1
          AND rw.DEL_FLAG = 0
          AND rw.RISK_STATE >0
            <if test="model.processUserId != null and model.processUserId !='' ">
                and hr.process_user_id = #{model.processUserId}
            </if>
            <if test="model.riskLevel != null and model.riskLevel !='' ">
                and case
                when rw.new_risk_level!='' then rw.new_risk_level = #{model.riskLevel}
                when rw.new_risk_level is null || rw.new_risk_level='' then  rw.risk_Level = #{model.riskLevel}
                end
            </if>

            <if test="model.brandCode != null and model.brandCode !='' ">
                and rw.brand_code = #{model.brandCode}
            </if>

            <if test="model.riskType != null and model.riskType !='' ">
                AND rw.RISK_TYPE = #{model.riskType}
            </if>

            <if test="model.startDate !=null and model.startDate != ''and
                      model.endDate !=null and model.endDate != ''">
                and rw.CREATE_TIME>=#{model.startDate}
                AND #{model.endDate}>=rw.CREATE_TIME
            </if>
            <if test="model.tag != null and model.tag !='' ">
                and rw.RISK_NAME like CONCAT('%', #{model.tag} ,'%')
            </if>

            <if test="model.warnPeriod != null and model.warnPeriod !='' ">
                and rw.statistic_type = #{model.warnPeriod}
            </if>
        group by rw.id
    </select>

    <select id="riskListRole"  resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        select
            *
        from
            (
                SELECT
                    rw.ID,
                    rw.RISK_NAME as riskName,
                    rw.RISK_ID as riskId,
                    rw.AUDIT_USER_ID as auditUserId,
                    us.USERNAME as auditUserName,
                    rw.AUDIT_DEPART_ID as auditDepartId,
                    dp.DEPART_NAME_ABBR as auditDepartName,
                    dp.DEPART_NAME as departName,
                    rw.AUDIT_TIME as auditTime,
                    rw.CONFIRMATION_TIME as confirmationTime,
                    rw.RISK_TYPE as riskType,
                    rw.RISK,
                    rw.WARNING_NUM as warningNum,
                    rw.STATISTIC as statistic,
                    rw.USER_NUM as userNum,
                    rw.CREATE_TIME as createTime,
                    rw.RISK_STATE as riskState,
                    rw.risk_Level as riskLevel,
                    rw.new_risk_level as newRiskLevel,
                    rw.brand_code as brandCode,
                    count(1) - 1 as processingFrequency,
                    rw.IF_RISK as ifRisk,
                    hr.process_status as processStatus,
                    hr.process_user_id as processUserId,
                    rw.statistic_type as statisticType
                FROM
                    VOC_RISK_ALL_TYPES rw
                        left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
                        left join SYS_DEPART dp on rw.AUDIT_DEPART_ID = dp.id
                        left JOIN VOC_RISK_HANDLING_RECORD hr ON rw.id=hr.warning_risk_id
                WHERE
                    1=1
                  AND rw.DEL_FLAG =0
                  AND rw.RISK_STATE = 3
                group by rw.risk_id

                union all

                SELECT
                    rw.ID,
                    rw.RISK_NAME as riskName,
                    rw.RISK_ID as riskId,
                    rw.AUDIT_USER_ID as auditUserId,
                    us.USERNAME as auditUserName,
                    rw.AUDIT_DEPART_ID as auditDepartId,
                    dp.DEPART_NAME_ABBR as auditDepartName,
                    dp.DEPART_NAME as departName,
                    rw.AUDIT_TIME as auditTime,
                    rw.CONFIRMATION_TIME as confirmationTime,
                    rw.RISK_TYPE as riskType,
                    rw.RISK,
                    rw.WARNING_NUM as warningNum,
                    rw.STATISTIC as statistic,
                    rw.USER_NUM as userNum,
                    rw.CREATE_TIME as createTime,
                    rw.RISK_STATE as riskState,
                    rw.risk_Level as riskLevel,
                    rw.new_risk_level as newRiskLevel,
                    rw.brand_code as brandCode,
                    '0' as processingFrequency,
                    rw.IF_RISK as ifRisk,
                    hr.process_status as processStatus,
                    hr.process_user_id as processUserId,
                    rw.statistic_type as statisticType
                FROM
                    VOC_RISK_ALL_TYPES rw
                        left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
                        left join SYS_DEPART dp on rw.AUDIT_DEPART_ID = dp.id
                        left JOIN VOC_RISK_HANDLING_RECORD hr ON rw.id=hr.warning_risk_id
                WHERE
                    1=1
                  AND rw.DEL_FLAG = 0
                  AND rw.RISK_STATE != 3
                group by rw.ID
            ) rw
        where 1 = 1
        <if test="model.brandCode != null and model.brandCode !='' ">
            and rw.brandCode = #{model.brandCode}
        </if>
        <if test="model.riskState != null and model.riskState !='' ">
            <if test="model.riskState == '0'">
                and rw.riskState = '0'
            </if>
            <if test="model.riskState == '1'">
                and rw.processStatus = '0'
            </if>
            <if test="model.riskState == '4'">
                and (rw.processStatus = '2' or rw.riskState = '7')
            </if>
            <if test="model.riskState == '3'">
                and rw.processStatus = '1'
                and (rw.riskState = '3' or rw.riskState = '7')
            </if>
            <if test="model.riskState == '5'">
                and rw.riskState = '5'
            </if>
            <if test="model.riskState == '6'">
                and rw.processStatus = '3'
            </if>
        </if>
        group by rw.ID
        <!--         				待审核
                         and rw.riskState = '0'
                             已审核 待处理
                         and rw.processStatus = '0'
                             确认完成 等待处理 处理中
                             and rw.processStatus = '2'
                             已处理
                            and rw.processStatus = '1'
                             and rw.riskState = '3'
                             已撤回
                             and rw.processStatus = '3'
                             已取消
                            and rw.riskState = '5' -->



    </select>

    <select id="riskList"  resultType="com.car.voc.vo.risk.RiskAllTypesVo">
    select
        <if test="model.groupByName != null and model.groupByName !='' ">
            <if test="model.groupByName == 'rw.riskLevel'">
                rw.riskLevel,
                rw.newRiskLevel,
                count(rw.riskLevel) as riskLevelNum,
                count(rw.newRiskLevel) as riskLevelNum1
            </if>
            <if test="model.groupByName == 'rw.departName'">
                rw.departName as departName,
                rw.auditDepartId as departId,
                count(distinct rw.id) as total
            </if>
        </if>

        <if test="model.groupByName == null or model.groupByName == '' ">
            *
        </if>

    from
        (
        SELECT
        rw.ID,
        rw.RISK_NAME as riskName,
        rw.RISK_ID as riskId,
        rw.AUDIT_USER_ID as auditUserId,
        us.USERNAME as auditUserName,
        hr.process_depart_id as auditDepartId,
        hr.bind_type AS bindType,
        dp.DEPART_NAME_ABBR as auditDepartName,
        dp.DEPART_NAME as departName,
        rw.AUDIT_TIME as auditTime,
        rw.CONFIRMATION_TIME as confirmationTime,
        rw.RISK_TYPE as riskType,
        rw.RISK,
        rw.WARNING_NUM as warningNum,
        rw.STATISTIC as statistic,
        rw.USER_NUM as userNum,
        rw.CREATE_TIME as createTime,
        rw.RISK_STATE as riskState,
        rw.risk_Level as riskLevel,
        rw.new_risk_level as newRiskLevel,
        rw.brand_code as brandCode,
        rw.cancel_time as cancelTime,
        ifnull(hrd.num,"0") as processingFrequency,
        rw.IF_RISK as ifRisk,
        MAX(hr.process_status) as processStatus,
        hr.process_user_id as processUserId,
        rw.statistic_type as statisticType
        FROM
        VOC_RISK_ALL_TYPES rw
        left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
        left JOIN (select
        vhd.*
        from
        voc_risk_handling_record vhd
        right join
        (
        select
        max(create_time) as create_time ,
        warning_risk_id
        from
        voc_risk_handling_record
        group by
        warning_risk_id ) t on
        vhd.warning_risk_id = t.warning_risk_id
        and vhd.create_time = t.create_time) hr ON rw.id=hr.warning_risk_id
        left join SYS_DEPART dp on hr.process_depart_id = dp.id
        left join (select count(*) as num,risk,brand_code from voc_risk_all_types  where risk_state=3  group by risk,brand_code) hrd on hrd.risk= rw.risk and hrd.brand_code= rw.brand_code
        WHERE
        1=1
        AND rw.DEL_FLAG =0
        AND rw.RISK_STATE = 3
        group by rw.risk_id

        union all

        SELECT
        rw.ID,
        rw.RISK_NAME as riskName,
        rw.RISK_ID as riskId,
        rw.AUDIT_USER_ID as auditUserId,
        us.USERNAME as auditUserName,
        hr.process_depart_id as auditDepartId,
        hr.bind_type AS bindType,
        dp.DEPART_NAME_ABBR as auditDepartName,
        dp.DEPART_NAME as departName,
        rw.AUDIT_TIME as auditTime,
        rw.CONFIRMATION_TIME as confirmationTime,
        rw.RISK_TYPE as riskType,
        rw.RISK,
        rw.WARNING_NUM as warningNum,
        rw.STATISTIC as statistic,
        rw.USER_NUM as userNum,
        rw.CREATE_TIME as createTime,
        rw.RISK_STATE as riskState,
        rw.risk_Level as riskLevel,
        rw.new_risk_level as newRiskLevel,
        rw.brand_code as brandCode,
        rw.cancel_time as cancelTime,
        hrd.num as processingFrequency,
        rw.IF_RISK as ifRisk,
        MAX(hr.process_status) as processStatus,
        hr.process_user_id as processUserId,
        rw.statistic_type as statisticType
        FROM
        VOC_RISK_ALL_TYPES rw
        left join SYS_USER us on rw.AUDIT_USER_ID=us.ID
        left JOIN (select
        vhd.*
        from
        voc_risk_handling_record vhd
        right join
        (
        select
        max(create_time) as create_time ,
        warning_risk_id
        from
        voc_risk_handling_record
        group by
        warning_risk_id ) t on
        vhd.warning_risk_id = t.warning_risk_id
        and vhd.create_time = t.create_time)  hr ON rw.id=hr.warning_risk_id
        left join SYS_DEPART dp on hr.process_depart_id = dp.id
        left join (select count(*) as num,risk,brand_code from voc_risk_all_types  where risk_state=3  group by risk,brand_code) hrd on hrd.risk= rw.risk and hrd.brand_code= rw.brand_code
        WHERE
        1=1
        AND rw.DEL_FLAG = 0
        AND rw.RISK_STATE != 3
        group by rw.ID
    ) rw
        where 1 = 1
        <if test="model.riskLevel != null and model.riskLevel !='' ">
            and case
            when rw.newRiskLevel!='' then rw.newRiskLevel = #{model.riskLevel}
            when rw.newRiskLevel is null || rw.newRiskLevel='' then rw.riskLevel = #{model.riskLevel}
            end
        </if>

        <if test="model.brandCode != null and model.brandCode !='' ">
            and rw.brandCode = #{model.brandCode}
        </if>

        <if test="model.ifRiskType != null and model.ifRiskType !='' ">
            and rw.ifRisk = #{model.ifRiskType}
        </if>
        <if test="model.riskType != null and model.riskType !='' ">
            and rw.riskType = #{model.riskType}
        </if>

        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and rw.createTime>=#{model.startDate}
            AND #{model.endDate}>=rw.createTime
        </if>

        <if test="model.tag != null and model.tag !='' ">
            and rw.riskName like CONCAT('%', #{model.tag} ,'%')
        </if>

        <if test="model.warnPeriod != null and model.warnPeriod !='' ">
            and rw.statisticType = #{model.warnPeriod}
        </if>

        <if test="model.reviewerPermission.size() == 0">
            <if test="model.riskIdList != null and model.riskIdList.size()>0">
                and ( rw.id in
                <foreach item="item" collection="model.riskIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
                <if test="model.processStatus != null and model.processStatus != ''">
                    and rw.processStatus = #{model.processStatus}
                </if>

                <if test="model.processUserId != null and model.processUserId != ''">
                    and rw.processUserId = #{model.processUserId}
                </if>
                <if test="model.auditUserId != null and model.auditUserId !='' ">
                    or rw.auditUserId = #{model.auditUserId}
                </if>
                )
            </if>
        </if>

        <if test="model.reviewerPermission != null and model.reviewerPermission.size()>0">
            and
            <foreach item="item" collection="model.reviewerPermission" separator="or" open="(" close=")" index="">
                (
                    <if test="item.brandCode != null and item.brandCode != ''">
                        rw.brandCode = #{item.brandCode}
                        <if test="item.riskTypeStatus != null and item.riskTypeStatus.size()>0 ">
                            and rw.riskType in
                            <foreach item="status" collection="item.riskTypeStatus" separator="," open="(" close=")" index="">
                                #{status}
                            </foreach>
                        </if>
                        <if test="model.processStatus != null and model.processStatus != ''">
                            and rw.processStatus = #{model.processStatus}
                        </if>
                    </if>

                    <if test="item.tagCodes != null and item.tagCodes.size()>0">
                        and (
                        <foreach item="itemTagCodes" collection="item.tagCodes" separator="or" open="(" close=")" index="">
                             rw.riskName like CONCAT('%', #{itemTagCodes} ,'%')
                        </foreach>
                        )
                    </if>

                <if test="model.riskIdList != null and model.riskIdList.size()>0">
                    or ( rw.id in
                    <foreach item="item" collection="model.riskIdList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                    <if test="model.processStatus != null and model.processStatus != ''">
                        and rw.processStatus = #{model.processStatus}
                    </if>

                    <if test="model.processUserId != null and model.processUserId != ''">
                        and rw.processUserId = #{model.processUserId}
                    </if>
                    )
                </if>
                )
            </foreach>
        </if>

<!--        <if test="model.reviewerPermission == null and model.reviewerPermission.size() == 0">-->
<!--            <if test="model.riskIdList != null and model.riskIdList.size()>0">-->
<!--                or ( rw.id in-->
<!--                <foreach item="item" collection="model.riskIdList" separator="," open="(" close=")" index="">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                    <if test="model.processStatus != null and model.processStatus != ''">-->
<!--                        and rw.processStatus = #{model.processStatus}-->
<!--                    </if>-->

<!--                    <if test="model.processUserId != null and model.processUserId != ''">-->
<!--                        and rw.processUserId = #{model.processUserId}-->
<!--                    </if>-->
<!--                )-->
<!--            </if>-->
<!--        </if>-->

        <if test="model.groupByName != null and model.groupByName !='' ">
            <if test="model.groupByName == 'rw.riskLevel'">
                group by ${model.groupByName}
            </if>
            <if test="model.groupByName == 'rw.departName'">
                and rw.departName is not null
                group by ${model.groupByName}
            </if>
        </if>
        <if test="model.column != null and model.column !='' ">
            order by ${model.column} ${model.order}
        </if>
        <if test="model.column == null or model.column =='' ">
            order by createTime desc, riskid desc
        </if>


    </select>


    <select id="getRiskAlert" resultType="com.car.voc.vo.risk.RiskAlertReviewerVo">
         select
               ar.id,
               ar.REVIEWER_USER_ID as reviewerUserId,
               us.realname as reviewerUserName,
               us.username as reviewerUserNo,
               ar.REVIEWER_DEPART_ID as reviewerDepartId,
               dp.depart_name as reviewerDepartName,
               ar.REMARK as remark,
               ar.brand_codes as brandCodes
        from VOC_RISK_ALERT_REVIEWER ar
        left join SYS_USER us on ar.REVIEWER_USER_ID=us.ID and us.del_flag = 0
        left join SYS_DEPART dp on ar.REVIEWER_DEPART_ID = dp.id and dp.del_flag = '0'
        where 1=1
          and ar.del_flag = 0
          and ar.REVIEWER_USER_ID =#{reviewerUserId}
         group by ar.id
        order by ar.create_time desc
    </select>

    <select id="riskListManage" resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
            rw.ID,
            rw.RISK_NAME AS riskName,
            rw.RISK_ID AS riskId,
            rw.AUDIT_USER_ID AS auditUserId,
            rw.AUDIT_TIME AS auditTime,
            rw.CONFIRMATION_TIME AS confirmationTime,
            rw.RISK_TYPE AS riskType,
            rw.RISK,
            rw.WARNING_NUM AS warningNum,
            rw.STATISTIC AS statistic,
            rw.USER_NUM AS userNum,
            rw.CREATE_TIME AS createTime,
            rw.risk_Level AS riskLevel,
            rw.new_risk_level AS newRiskLevel,
            rw.brand_code AS brandCode,
            rw.cancel_time AS cancelTime,
            rw.if_risk AS ifRisk,
            rw.statistic_type AS statisticType,
            rw.risk_state AS riskState,
            COALESCE(hrd.processingFrequency, 0) as processingFrequency
        FROM
            voc_risk_all_types rw
                LEFT JOIN ( SELECT ifnull(count(*),0) AS processingFrequency, risk, brand_code FROM voc_risk_all_types WHERE risk_state = 3  AND DEL_FLAG = 0 GROUP BY risk, brand_code ) hrd ON hrd.risk = rw.risk
                AND hrd.brand_code = rw.brand_code
        WHERE
            1 = 1
          AND rw.DEL_FLAG = 0
          and (rw.risk_type = 1 AND EXISTS (SELECT 1 from sys_role_business_tag where del_flag = 0 and role_id = #{model.roleId} and brand_code = #{model.brandCode} AND substring(rw.risk_name, 1, 8) = tag_code)
            OR (rw.risk_type = 2 AND TRUE = #{model.qualityBoolean})
            OR (rw.risk_type = 3 )
            OR (rw.risk_type = 4 AND TRUE = #{model.qualityBoolean})
            OR (rw.risk_type = 5 ))
        <if test="model.brandCode != null and model.brandCode !='' ">
            and rw.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
            and rw.create_time>=#{model.startDate}  AND #{model.endDate}>=rw.create_time
        </if>
        <include refid="risk_level_query" />

        <if test="model.ifRiskType != null and model.ifRiskType !='' ">
            and rw.if_risk = #{model.ifRiskType}
        </if>
        <if test="model.riskType != null and model.riskType !='' ">
            and rw.risk_type = #{model.riskType}
        </if>
        <if test="model.processingFrequency != null and model.processingFrequency !='' ">
            and COALESCE(hrd.processingFrequency, 0) = #{model.processingFrequency}
        </if>
        <choose>
            <when test="model.riskState != null">
                <choose>
                    <!-- 待处理状态 -->
                    <when test="model.riskState == 1">
                        and rw.risk_state in (1,2,4)
                        and EXISTS (SELECT 1 FROM voc_risk_handling_record WHERE DEL_FLAG = 0 AND process_status = 0 AND warning_risk_id = rw.ID)
                    </when>
                    <!-- 处理中状态 -->
                    <when test="model.riskState == 4">
                        and rw.risk_state in (1,4,7)
                        and EXISTS (SELECT 1 FROM voc_risk_handling_record WHERE DEL_FLAG = 0 AND process_status in (2, 1) AND warning_risk_id = rw.ID)
                    </when>
                    <!-- 其他状态 -->
                    <otherwise>
                        and rw.risk_state = #{model.riskState}
                    </otherwise>
                </choose>
            </when>
        </choose>

        <if test="model.auditDepartId != null and model.auditDepartId !='' ">
            AND rw.id IN (
                SELECT
                    DISTINCT warning_risk_id
                FROM
                    voc_risk_handling_record
                WHERE
                    1 = 1
                    AND DEL_FLAG = 0
                    AND process_status != 3
                    AND process_depart_id = #{model.auditDepartId}
            )
        </if>

        <if test="model.tag != null and model.tag !='' ">
            and rw.risk_name like CONCAT('%', #{model.tag} ,'%')
        </if>

        <if test="model.warnPeriod != null and model.warnPeriod !='' ">
            and rw.statistic_type = #{model.warnPeriod}
        </if>

        <if test="model.column != null and model.column !='' ">
            order by ${model.column} ${model.order}
        </if>
        <if test="model.column == null or model.column =='' ">
            order by createTime desc, riskid desc
        </if>
    </select>
    <sql id="risk_level_query">
        <if test="model.riskLevel != null and model.riskLevel != ''">
            AND (
            (rw.new_risk_level IS NOT NULL AND rw.new_risk_level = #{model.riskLevel})
            OR
            (COALESCE(rw.new_risk_level, '') = '' AND rw.risk_level = #{model.riskLevel})
            )
        </if>
    </sql>
    <select id="riskListProcessingPersonnel" resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
            rw.ID,
            rw.RISK_NAME AS riskName,
            rw.RISK_ID AS riskId,
            rw.AUDIT_USER_ID AS auditUserId,
            rw.AUDIT_TIME AS auditTime,
            rw.CONFIRMATION_TIME AS confirmationTime,
            rw.RISK_TYPE AS riskType,
            rw.RISK,
            rw.WARNING_NUM AS warningNum,
            rw.STATISTIC AS statistic,
            rw.USER_NUM AS userNum,
            rw.CREATE_TIME AS createTime,
            rw.risk_Level AS riskLevel,
            rw.new_risk_level AS newRiskLevel,
            rw.brand_code AS brandCode,
            rw.cancel_time AS cancelTime,
            rw.if_risk AS ifRisk,
            rw.statistic_type AS statisticType,
            rw.risk_state AS riskState,
            COALESCE(hrd.processingFrequency, 0) as processingFrequency
        FROM
            voc_risk_all_types rw
                LEFT JOIN ( SELECT ifnull(count(*),0) AS processingFrequency, risk, brand_code FROM voc_risk_all_types WHERE risk_state = 3 AND DEL_FLAG = 0 GROUP BY risk, brand_code ) hrd ON hrd.risk = rw.risk
                left JOIN VOC_RISK_HANDLING_RECORD hr ON rw.id = hr.warning_risk_id AND hr.DEL_FLAG = 0
        WHERE
            1 = 1
          AND rw.DEL_FLAG = 0
          AND rw.RISK_STATE > 0
          AND hr.process_user_id = #{model.processUserId}
        <if test="model.brandCode != null and model.brandCode !='' ">
            and rw.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
            and rw.create_time>=#{model.startDate}  AND #{model.endDate}>=rw.create_time
        </if>
        <include refid="risk_level_query" />
        <if test="model.ifRiskType != null and model.ifRiskType !='' ">
            and rw.if_risk = #{model.ifRiskType}
        </if>
        <if test="model.riskType != null and model.riskType !='' ">
            and rw.risk_type = #{model.riskType}
        </if>
        <if test="model.processingFrequency != null and model.processingFrequency !='' ">
            and COALESCE(hrd.processingFrequency, 0) = #{model.processingFrequency}
        </if>
        <choose>
            <when test="model.riskState != null">
                <choose>
                    <!-- 待处理状态 -->
                    <when test="model.riskState == 1">
                        and rw.risk_state in (1,2,4,7)
                        and EXISTS (SELECT 1 FROM voc_risk_handling_record WHERE DEL_FLAG = 0 AND process_status = 0 AND warning_risk_id = rw.ID and process_user_id =#{model.processUserId})
                    </when>
                    <!-- 处理中状态 -->
                    <when test="model.riskState == 4">
                        and rw.risk_state in (1,4,7)
                        and EXISTS (SELECT 1 FROM voc_risk_handling_record WHERE DEL_FLAG = 0 AND process_status = 2 AND warning_risk_id = rw.ID and process_user_id =#{model.processUserId})
                    </when>
                    <!-- 其他状态 -->
                    <otherwise>
                        and rw.risk_state = #{model.riskState}
                    </otherwise>
                </choose>
            </when>
        </choose>

        <if test="model.auditDepartId != null and model.auditDepartId !='' ">
            AND rw.id IN (
            SELECT
                DISTINCT warning_risk_id
            FROM
                voc_risk_handling_record
            WHERE
                1 = 1
                AND DEL_FLAG = 0
                AND process_status != 3
                AND process_depart_id = #{model.auditDepartId}
            )
        </if>

        <if test="model.tag != null and model.tag !='' ">
            and rw.risk_name like CONCAT('%', #{model.tag} ,'%')
        </if>

        <if test="model.warnPeriod != null and model.warnPeriod !='' ">
            and rw.statistic_type = #{model.warnPeriod}
        </if>

        <if test="model.column != null and model.column !='' ">
            order by ${model.column} ${model.order}
        </if>
        <if test="model.column == null or model.column =='' ">
            order by createTime desc, riskid desc
        </if>
    </select>

    <select id="riskListReviewer" resultType="com.car.voc.vo.risk.RiskAllTypesVo">
        SELECT
            rw.ID,
            rw.RISK_NAME AS riskName,
            rw.RISK_ID AS riskId,
            rw.AUDIT_USER_ID AS auditUserId,
            rw.AUDIT_TIME AS auditTime,
            rw.CONFIRMATION_TIME AS confirmationTime,
            rw.RISK_TYPE AS riskType,
            rw.RISK,
            rw.WARNING_NUM AS warningNum,
            rw.STATISTIC AS statistic,
            rw.USER_NUM AS userNum,
            rw.CREATE_TIME AS createTime,
            rw.risk_Level AS riskLevel,
            rw.new_risk_level AS newRiskLevel,
            rw.brand_code AS brandCode,
            rw.cancel_time AS cancelTime,
            rw.if_risk AS ifRisk,
            rw.statistic_type AS statisticType,
            rw.risk_state AS riskState,
            COALESCE(hrd.processingFrequency, 0) AS processingFrequency
        FROM
            voc_risk_all_types rw
                LEFT JOIN ( SELECT ifnull(count(*),0) AS processingFrequency, risk, brand_code FROM voc_risk_all_types WHERE risk_state = 3 AND DEL_FLAG = 0 GROUP BY risk, brand_code ) hrd ON hrd.risk = rw.risk
                AND hrd.brand_code = rw.brand_code
        WHERE
            1 = 1
            AND rw.DEL_FLAG = 0
            AND (
                (rw.risk_type = 1 AND EXISTS ( SELECT 1 FROM voc_risk_alert_reviewer reviewer INNER JOIN voc_risk_process_recipient_tag business ON reviewer.id = business.recipient_id and business.del_flag = 0 WHERE reviewer.del_flag = 0 and reviewer.reviewer_user_id = #{model.processUserId} AND business.brand_code = #{model.brandCode} and rw.RISK_NAME = business.tag_code )
                    and EXISTS (SELECT 1 from sys_role_business_tag where del_flag = 0 and role_id = #{model.roleId} and brand_code = #{model.brandCode} AND substring(rw.risk_name, 1, 8) = tag_code))
                OR (rw.risk_type = 2 and EXISTS (SELECT 1 FROM voc_risk_alert_reviewer reviewer INNER JOIN voc_risk_process_recipient_base as recipient ON reviewer.id = recipient.recipient_id and recipient.del_flag = 0 WHERE reviewer.del_flag = 0 and reviewer.reviewer_user_id = #{model.processUserId} and recipient.type= 2 and recipient.brand_code = #{model.brandCode})
                    and true = #{model.qualityBoolean})
                OR (rw.risk_type = 3 and EXISTS (SELECT 1 FROM voc_risk_alert_reviewer reviewer INNER JOIN voc_risk_process_recipient_base as recipient ON reviewer.id = recipient.recipient_id and recipient.del_flag = 0 WHERE reviewer.del_flag = 0 and reviewer.reviewer_user_id = #{model.processUserId} and recipient.type= 3 and recipient.brand_code = #{model.brandCode}))
                OR (rw.risk_type = 4 and EXISTS (SELECT 1 FROM voc_risk_alert_reviewer reviewer INNER JOIN voc_risk_process_recipient_base as recipient ON reviewer.id = recipient.recipient_id and recipient.del_flag = 0 WHERE reviewer.del_flag = 0 and reviewer.reviewer_user_id = #{model.processUserId} and recipient.type= 4 and recipient.brand_code = #{model.brandCode})
                    and true = #{model.qualityBoolean})
                OR (rw.risk_type = 5 and EXISTS (SELECT 1 FROM voc_risk_alert_reviewer reviewer INNER JOIN voc_risk_process_recipient_base as recipient ON reviewer.id = recipient.recipient_id and recipient.del_flag = 0 WHERE reviewer.del_flag = 0 and reviewer.reviewer_user_id = #{model.processUserId} and recipient.type= 5 and recipient.brand_code = #{model.brandCode}))
                OR EXISTS (select 1 from VOC_RISK_HANDLING_RECORD hr where hr.DEL_FLAG = 0 and rw.id = hr.warning_risk_id AND hr.process_user_id = #{model.processUserId})
            )
            <if test="model.brandCode != null and model.brandCode !='' ">
                and rw.brand_code = #{model.brandCode}
            </if>
            <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
                and rw.create_time>=#{model.startDate}  AND #{model.endDate}>=rw.create_time
            </if>
            <include refid="risk_level_query" />
            <if test="model.ifRiskType != null and model.ifRiskType !='' ">
                and rw.if_risk = #{model.ifRiskType}
            </if>
            <if test="model.riskType != null and model.riskType !='' ">
                and rw.risk_type = #{model.riskType}
            </if>
            <if test="model.processingFrequency != null and model.processingFrequency !='' ">
                and COALESCE(hrd.processingFrequency, 0) = #{model.processingFrequency}
            </if>
            <choose>
                <when test="model.riskState != null">
                    <choose>
                        <!-- 待处理状态 -->
                        <when test="model.riskState == 1">
                            and rw.risk_state in (1,2,4,7)
                            and EXISTS (SELECT 1 FROM voc_risk_handling_record WHERE DEL_FLAG = 0 AND process_status = 0 AND warning_risk_id = rw.ID)
                        </when>
                        <!-- 处理中状态 -->
                        <when test="model.riskState == 4">
                            and rw.risk_state in (1,4,7)
                            and EXISTS (SELECT 1 FROM voc_risk_handling_record WHERE DEL_FLAG = 0 AND process_status = 2 AND warning_risk_id = rw.ID)
                        </when>
                        <!-- 其他状态 -->
                        <otherwise>
                            and rw.risk_state = #{model.riskState}
                        </otherwise>
                    </choose>
                </when>
            </choose>

            <if test="model.auditDepartId != null and model.auditDepartId !='' ">
                AND rw.id IN (
                SELECT
                    DISTINCT warning_risk_id
                FROM
                    voc_risk_handling_record
                WHERE
                    1 = 1
                    AND DEL_FLAG = 0
                    AND process_status != 3
                    AND process_depart_id = #{model.auditDepartId}
                )
            </if>

            <if test="model.tag != null and model.tag !='' ">
                and rw.risk_name like CONCAT('%', #{model.tag} ,'%')
            </if>

            <if test="model.warnPeriod != null and model.warnPeriod !='' ">
                and rw.statistic_type = #{model.warnPeriod}
            </if>

            <if test="model.column != null and model.column !='' ">
                order by ${model.column} ${model.order}
            </if>
            <if test="model.column == null or model.column =='' ">
                order by createTime desc, riskid desc
            </if>
    </select>
</mapper>
