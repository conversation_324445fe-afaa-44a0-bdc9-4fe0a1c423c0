package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.entity.VocRiskWarningRulesDetailedRiskLevel;
import com.car.voc.entity.VocRiskWarningRulesNew;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskWarningRulesMapper.java
 * @Description TODO
 * @createTime 2023年02月06日 10:28
 * @Copyright voc
 */
public interface VocRiskWarningRulesMapper extends BaseMapper<VocRiskWarningRules> {
    List<VocRiskWarningRulesNew> selectRuleList(@Param("brandCode")  String brandCode);

    String selectRuleDetailList(@Param("riskType") String riskType, @Param("brandCode") String brandCode,@Param("riskIndex")BigDecimal riskIndex);

    List<VocRiskWarningRules> getRiskType(@Param("riskType") String riskType);
}
