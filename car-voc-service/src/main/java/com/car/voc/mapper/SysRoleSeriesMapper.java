package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRoleSeries;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* @Entity com.car.voc.entity.SysRoleSeries
*/
public interface SysRoleSeriesMapper extends BaseMapper<SysRoleSeries> {

    @Select("select * from SYS_ROLE_SERIES where role_id = #{roleId} and brand_code= #{brandCode} and del_flag= '0' ")
    List<SysRoleSeries> listByRoleId(@Param("roleId") String roleId,@Param("brandCode") String brandCode);
}
