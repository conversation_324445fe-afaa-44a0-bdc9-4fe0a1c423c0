package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysUser;
import com.car.voc.vo.SysUserDepVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 *
 * @since 2018-12-20
 */
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * 通过用户账号查询用户信息
     *
     * @param username
     * @return
     */
    public SysUser getUserByName(@Param("username") String username);


    void deleteBathRoleUserRelation(String[] roleIds);

    void deleteBathRolePermissionRelation(String[] roleIds);

    List<SysUserDepVo> getDepNamesByUserIds(@Param("userIds")List<String> userIds);
    List<SysUserDepVo> getRolNamesByUserIds(@Param("userIds")List<String> userIds);
    /** 更新空字符串为null【此写法有sql注入风险，禁止随便用】 */
    int updateNullByEmptyString(@Param("fieldName") String fieldName);

}
