package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.entity.VocRiskProcessRecipient;
import com.car.voc.entity.VocRiskProcessRecipientBase;
import com.car.voc.entity.VocRiskProcessRecipientTag;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskProessRecipientMapper.java
 * @Description TODO
 * @createTime 2023年02月06日 11:18
 * @Copyright voc
 */
public interface VocRiskProessRecipientMapper extends BaseMapper<VocRiskProcessRecipient> {
    @Select("Select * from voc_risk_process_recipient_tag where recipient_id = #{id} and del_flag=0 ")
    List<VocRiskProcessRecipientTag> selectByProessRecipientTag(@Param("id") String id);

    @Select("Select * from voc_risk_process_recipient_base where recipient_id = #{id} and del_flag=0 ")
    List<VocRiskProcessRecipientBase> selectByProcessRecipientBase(@Param("id") String id);

    @Update("update voc_risk_process_recipient_tag set del_flag=1 where recipient_id = #{id} and del_flag=0 ")
    Integer delByProessRecipientTag(@Param("id") String id);

    @Update("update voc_risk_process_recipient_base set del_flag=1 where recipient_id = #{id} and del_flag=0 ")
    Integer delByProessRecipientQualityTag(@Param("id") String id);

    @Select("select\n" +
            "*\n" +
            "from\n" +
            "voc_risk_alert_reviewer vrar\n" +
            "left join \n" +
            "voc_risk_process_recipient_tag vrprt on vrar.id = vrprt.recipient_id\n" +
            "where 1 = 1\n" +
            " and vrar.del_flag=0 " +
            " and vrprt.del_flag=0 " +
            "and vrar.reviewer_user_id = #{userId}"
    )
    List<VocRiskProcessRecipientTag> queryPermissionByUserId(@Param("userId") String userId);

    @Select("SELECT\n" +
            "    brand_code,\n" +
            "    risk_type\n" +
            "FROM\n" +
            "    voc_risk_all_types risk\n" +
            "    INNER JOIN voc_risk_handling_record record \n" +
            "        ON record.warning_risk_id = risk.id \n" +
            "        AND record.process_user_id = #{userId}" +
            " and risk.del_flag=0 "+
            " and record.del_flag=0 "+
            " group by brand_code,risk_type"
    )
    List<VocRiskAllTypes> queryPermissionByProcessUserId(@Param("userId") String userId);

    @Select("select\n" +
            "*\n" +
            "from\n" +
            "voc_risk_alert_reviewer vrar\n" +
            "inner join \n" +
            "voc_risk_process_recipient_base vrprt on vrar.id = vrprt.recipient_id and vrprt.del_flag=0\n" +
            "where 1 = 1\n" +
            " and vrar.del_flag=0 " +
            "and vrar.reviewer_user_id = #{userId}")
    List<VocRiskProcessRecipientBase> queryPermissionQualityByUserId(@Param("userId") String userId);

    @Select("select * from voc_risk_process_recipient_tag where process_user_id = #{userId} and del_flag=0  ")
    List<VocRiskProcessRecipientTag> queryHandlePermissionByUserId(String userId);

    @Select("select * from voc_risk_process_recipient_base where (risk_user_id = #{userId}) and del_flag=0 ")
    List<VocRiskProcessRecipientBase> queryHandlePermissionQualityByUserId(String userId);


    @Select("select vrprt.* from voc_risk_alert_reviewer vrar left join voc_risk_process_recipient_base vrprt on vrar.id = vrprt.recipient_id where vrprt.del_flag=0 and vrar.del_flag=0 and vrar.reviewer_user_id =#{userId} and vrprt.brand_code =#{brandCode}")
    List<VocRiskProcessRecipientBase> queryPermissionByUserIdAndBrandCode(@Param("userId") String userId, @Param("brandCode") String brandCode);
    @Select("select vrprt.brand_code , vrprt .brand_code_name , vbt.tag_code from voc_risk_process_recipient_tag vrprt left join voc_business_tag vbt on vrprt .tag_id =vbt .id where vrprt.del_flag=0  and vrprt .recipient_id =#{id} and length(vbt .tag_code )>13")
    List<VocRiskProcessRecipientTag> queryBrandTagByReId(String id);

    @Insert("<script>" +
            "INSERT INTO voc_risk_process_recipient_base" +
            "(id, recipient_id, brand_code, brand_code_name,  risk_user_group_id, " +
            "risk_user_group_name, risk_user_id, risk_user_name, bind_type, " +
            "type) VALUES " +
            "<foreach collection='list' item='item' separator=',' >" +
            "(#{item.id}, #{item.recipientId}, #{item.brandCode}, #{item.brandCodeName},#{item.riskUserGroupId}" +
            ", #{item.riskUserGroupName}, #{item.riskUserId}, #{item.riskUserName}, #{item.bindType}, " +
            "#{item.type})" +
            "</foreach>" +
            "</script>")
    Integer saveBatchQualityTags(@Param("list") List<VocRiskProcessRecipientBase> qualityTags);

    @Insert("<script>" +
            "INSERT INTO voc_risk_process_recipient_tag" +
            "(id, recipient_id, brand_code, brand_code_name, tag_id, tag_code, " +
            "tag_name, tag_parent_id, tag_parent_name, process_depart_id, " +
            "process_depart_name, process_user_id, process_user_name,bind_type) VALUES " +
            "<foreach collection='list' item='model' separator=','>" +
            "(#{model.id}, #{model.recipientId}, #{model.brandCode}, " +
            "#{model.brandCodeName}, #{model.tagId}, #{model.tagCode}, " +
            "#{model.tagName}, #{model.tagParentId}, #{model.tagParentName}, " +
            "#{model.processDepartId}, #{model.processDepartName}, " +
            "#{model.processUserId}, #{model.processUserName}, #{model.bindType})" +
            "</foreach>" +
            "</script>")
    Integer saveBatchRecipientTags(@Param("list") List<VocRiskProcessRecipientTag> recipientTags);
}
