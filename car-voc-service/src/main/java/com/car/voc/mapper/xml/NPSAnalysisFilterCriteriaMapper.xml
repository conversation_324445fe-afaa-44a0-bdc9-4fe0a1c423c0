<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocNPSAnalysisMapper">

   <sql id="queryCom_nps_required_attrs_filter">
		( publish_date    is not null and
			statistic       is not null and
			scores          is not null and
			energy_type     is not null and
			province        is not null and
			tag_type        is not null and
			stage           is not null and
			 status         is not null and
			brand_code      is not null and
			car_series_code is not null and
			classify        is not null and
			content_type    is not null )
	</sql>
    <sql id="queryCom_nps_default_attrs_filter">
		<if test="model.tagType == 1 or  model.tagType == 2 ">
			and tag_type = #{model.tagType}
		</if>
		<if test="model.brandCode != null and model.brandCode != ''">
            AND brand_code = #{model.brandCode}
        </if>
        <if test="model.carSeries != null  and model.carSeries.size() >0">
            AND car_series_code in
            <foreach item="item" collection="model.carSeries" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
		<if test="model.carSeriesPowers != null and model.carSeriesPowers.size()>0">
			and car_series_code in
			<foreach item="item" collection="model.carSeriesPowers" separator="," open="(" close=")" index="">
				#{item}
			</foreach>
		</if>
        <if test="model.channelIds != null  and model.channelIds.size() >0">
            AND channel_id in
            <foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.channel != null  and model.channel.size() >0">
            AND content_type in
            <foreach item="item" collection="model.channel" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.stage != null and model.stage != ''">
            AND stage = #{model.stage}
        </if>
        <if test="model.classify != null and model.classify !=''">
            AND classify = #{model.classify}
        </if>
        <if test="model.energyType != null and model.energyType !=''">
            AND energy_type = #{model.energyType}
        </if>
        <if test="model.sex != null and model.sex !=''">
            AND client_sex = #{model.sex}
        </if>
        <if test="model.carSpecificUYears != null and model.carSpecificUYears !=''">
			and start_time is not null and car_specific_time is not null
			<if test="model.carSpecificUYears == 'R1'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &lt; 0.5 )
            </if>
            <if test="model.carSpecificUYears == 'R2'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &gt;= 0.5
                    and DATEDIFF( start_time ,car_specific_time ) / 365 &lt; 1)
            </if>
            <if test="model.carSpecificUYears == 'R3'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &gt;= 1
                    and DATEDIFF( start_time ,car_specific_time ) / 365 &lt; 2)
            </if>
            <if test="model.carSpecificUYears == 'R4'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &gt;= 2
                    and DATEDIFF( start_time ,car_specific_time ) / 365 &lt; 3)
            </if>
            <if test="model.carSpecificUYears == 'R5'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &gt;= 3
                    and DATEDIFF( start_time ,car_specific_time ) / 365 &lt; 4)
            </if>
            <if test="model.carSpecificUYears == 'R6'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &gt;= 4
                    and DATEDIFF( start_time ,car_specific_time ) / 365 &lt; 5)
            </if>
            <if test="model.carSpecificUYears == 'R7'">
                and ( (DATEDIFF( start_time ,car_specific_time ) / 365 ) &gt; 5	)
            </if>
        </if>
		<if test="model.firstDimensionCodes != null  and model.firstDimensionCodes.size() >0">
            AND first_dimension_code in
            <foreach item="item" collection="model.firstDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
		<if test="model.secondDimensionCodes != null  and model.secondDimensionCodes.size() >0">
			AND second_dimension_code in
			<foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
				#{item}
			</foreach>
		</if>
        <if test="model.firstDimensionCode != null and model.firstDimensionCode != ''">
            and first_dimension_code = #{model.firstDimensionCode}
        </if>
        <if test="model.secondDimensionCode != null and model.secondDimensionCode != ''">
            and second_dimension_code = #{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode != null and model.thirdDimensionCode!= ''">
            and three_dimension_code = #{model.thirdDimensionCode}
        </if>
		<if test="model.province != null and model.province.size()>0">
			and province_code in
			<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
				#{item}
			</foreach>
		</if>
		<if test="model.provinceCodesPowers != null and model.provinceCodesPowers.size()>0">
			and province_code in
			<foreach item="item" collection="model.provinceCodesPowers" separator="," open="(" close=")" index="">
				#{item}
			</foreach>
		</if>
    </sql>

	<sql id="queryCom_nps_default_date_filter_group_2">
		where
        <if test="model.dateUnit == 0">
			dateUnit  = concat(year(date(#{model.endDate})),'-', week(date(#{model.endDate})) )
        </if>
        <if test="model.dateUnit == 1">
			dateUnit  = concat(year(date(#{model.endDate})),'-', month(date(#{model.endDate})) )
        </if>
        <if test="model.dateUnit == 2">
			dateUnit  = concat(year(date(#{model.endDate})),'-', quarter(date(#{model.endDate})) )
        </if>
        <if test="model.dateUnit == 3">
			dateUnit  =  concat(year(dateStr) )
        </if>
		 <if test="model.dateUnit == -1">
			dateUnit between concat(year(date(#{model.startDate})), '-',month(date(#{model.startDate})),'-', day(date(#{model.startDate})) )
				and concat(year(date(#{model.endDate})), '-',month(date(#{model.endDate})),'-', day(date(#{model.endDate})) )
         </if>
    </sql>

	<sql id="queryCom_nps_default_date_filter_with_1">
		with date_filter as (
			select
				case
					when dateUnit = 0 then concat(year( startDate ), '-', lpad(date_format( startDate , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( startDate ), '-', lpad(date_format( startDate , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year( startDate ), '-', lpad(quarter( startDate ), 2, 0) )
					when dateUnit = 3 then concat(year( startDate ))
					when dateUnit = -1 then DATE_SUB( endDate , interval if(diff_days &lt; diff_other, diff_other , diff_days ) day)
					else DATE_SUB( endDate , interval if(diff_days &lt; diff_other, diff_other , diff_days ) day)
				end as startDate,
				case
					when dateUnit = 0 then concat(year( endDate ), '-', lpad(date_format( endDate , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( endDate ), '-', lpad(date_format( endDate , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(endDate), '-', lpad(quarter(endDate), 2, 0) )
					when dateUnit = 3 then concat(year(endDate))
					when dateUnit = -1 then endDate
					else endDate
				end as endDate,
				case
					when dateUnit = 0 then concat(year( DATE_SUB( endDate , interval  @id  week) ), '-', lpad(date_format( DATE_SUB( endDate , interval  @id  week) , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( DATE_SUB( endDate , interval  @id  month) ), '-', lpad(date_format( DATE_SUB( endDate , interval  @id  month) , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year( DATE_SUB( endDate , interval  @id  quarter) ), '-', lpad(quarter(DATE_SUB( endDate , interval  @id  quarter) ), 2, 0) )
					when dateUnit = 3 then concat(year( DATE_SUB( endDate , interval  @id  year) ))
					when dateUnit = -1 then DATE_SUB( endDate , interval  (diff_days- @id ) day)
					else DATE_SUB( endDate , interval  diff_days- @id day)
				end as startDate_r,
				case
					when dateUnit = 0 then concat(year( DATE_SUB( endDate , interval ( @id +diff_other)  week) ), '-', lpad(date_format( DATE_SUB( endDate , interval ( @id +diff_other) week) , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( DATE_SUB( endDate , interval ( @id +diff_other)  month) ), '-', lpad(date_format( DATE_SUB( endDate , interval ( @id +diff_other) month) , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year( DATE_SUB( endDate , interval ( @id +diff_other)  quarter) ), '-', lpad(quarter( DATE_SUB( endDate , interval ( @id +diff_other)  quarter) ), 2, 0) )
					when dateUnit = 3 then concat(year(  DATE_SUB( endDate , interval ( @id +diff_other)  year )))
					when dateUnit = -1 then DATE_SUB( endDate , interval  (diff_days *2 +1 - @id) day)
					else DATE_SUB( endDate , interval  (diff_days *2 +1 - @id) day)
				end as startDate_rc,
				case
					when dateUnit = 0 then concat(year( DATE_SUB( endDate , interval diff_other  week) ), '-', lpad(date_format( DATE_SUB( endDate , interval diff_other week) , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( DATE_SUB( endDate , interval diff_other  month) ), '-', date_format(DATE_SUB( endDate , interval diff_other  month), '%m') )
					when dateUnit = 2 then concat(year( DATE_SUB( endDate , interval diff_other  quarter) ), '-', lpad(quarter( DATE_SUB( endDate , interval diff_other  quarter) ), 2, 0) )
					when dateUnit = 3 then concat(year( DATE_SUB( endDate , interval diff_other  year) ))
					when dateUnit = -1 then DATE_SUB( endDate , interval  (diff_days +1) day)
					else DATE_SUB( endDate , interval  (diff_days +1) day)
				end as endDate_rc,
				diff_days,
				diff_other,
				dateUnit,
				@id := @id +1
			from (
				select
					*,
					case
						when dateUnit = -1 then if(abs(DATEDIFF(startDate, endDate )) &lt; diff_other, diff_other , abs(DATEDIFF( startDate, endDate )) )
						else diff_other -1
					end as limit_,
					abs(DATEDIFF(startDate, endDate )) as diff_days
				from
					tf_dws_voc_nps_supplt,
					(
						select
							@id := 0,
							1 as diff_other,
							#{model.dateUnit} as dateUnit,
							date( #{model.startDate}) as startDate,
							date( #{model.endDate}) as endDate
					) a_
			)b_
			where @id &lt;= limit_
		)
	</sql>
	<sql id="queryCom_nps_default_date_filter_with_6">
		with date_filter as (
			select
				case
					when dateUnit = 0 then concat(year( startDate ), '-', lpad(date_format( startDate , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( startDate ), '-', lpad(date_format( startDate , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year( startDate ), '-', lpad(quarter( startDate ), 2, 0) )
					when dateUnit = 3 then concat(year( startDate ))
					when dateUnit = -1 then DATE_SUB( endDate , interval if(diff_days &lt; diff_other, diff_other , diff_days ) day)
					else DATE_SUB( endDate , interval if(diff_days &lt; diff_other, diff_other , diff_days ) day)
				end as startDate,
				case
					when dateUnit = 0 then concat(year( endDate ), '-', lpad(date_format( endDate , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( endDate ), '-', lpad(date_format( endDate , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(endDate), '-', lpad(quarter(endDate), 2, 0) )
					when dateUnit = 3 then concat(year(endDate))
					when dateUnit = -1 then endDate
					else endDate
				end as endDate,
				case
					when dateUnit = 0 then concat(year( DATE_SUB( endDate , interval  @id  week) ), '-', lpad(date_format( DATE_SUB( endDate , interval  @id  week) , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( DATE_SUB( endDate , interval  @id  month) ), '-', lpad(date_format( DATE_SUB( endDate , interval  @id  month) , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year( DATE_SUB( endDate , interval  @id  quarter) ), '-', lpad(quarter(DATE_SUB( endDate , interval  @id  quarter) ), 2, 0) )
					when dateUnit = 3 then concat(year( DATE_SUB( endDate , interval  @id  year) ))
					when dateUnit = -1 then DATE_SUB( endDate , interval  abs(diff_days- @id) day)
					else DATE_SUB( endDate , interval  diff_days- @id day)
				end as startDate_r,
				case
					when dateUnit = 0 then concat(year( DATE_SUB( endDate , interval ( @id +diff_other)  week) ), '-', lpad(date_format( DATE_SUB( endDate , interval ( @id +diff_other) week) , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( DATE_SUB( endDate , interval ( @id +diff_other)  month) ), '-', lpad(date_format( DATE_SUB( endDate , interval ( @id +diff_other) month) , '%m'), 2, 0) )
					when dateUnit = 2 then concat(year( DATE_SUB( endDate , interval ( @id +diff_other)  quarter) ), '-', lpad(quarter( DATE_SUB( endDate , interval ( @id +diff_other)  quarter) ), 2, 0) )
					when dateUnit = 3 then concat(year(  DATE_SUB( endDate , interval ( @id +diff_other)  year )))
					when dateUnit = -1 then DATE_SUB( endDate , interval  (diff_days *2 +1- @id) day)
					else DATE_SUB( endDate , interval  (diff_days *2 +1 - @id) day)
				end as startDate_rc,
				case
					when dateUnit = 0 then concat(year( DATE_SUB( endDate , interval diff_other  week) ), '-', lpad(date_format( DATE_SUB( endDate , interval diff_other week) , '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year( DATE_SUB( endDate , interval diff_other  month) ), '-', date_format(DATE_SUB( endDate , interval diff_other  month), '%m') )
					when dateUnit = 2 then concat(year( DATE_SUB( endDate , interval diff_other  quarter) ), '-', lpad(quarter( DATE_SUB( endDate , interval diff_other  quarter) ), 2, 0) )
					when dateUnit = 3 then concat(year( DATE_SUB( endDate , interval diff_other  year) ))
					when dateUnit = -1 then DATE_SUB( endDate , interval  (diff_days +1) day)
					else DATE_SUB( endDate , interval  (diff_days +1) day)
				end as endDate_rc,
				diff_days,
				diff_other,
				dateUnit,
				@id := @id +1
			from (
				select
					*,
					case
						when dateUnit = -1 then if(abs(DATEDIFF(startDate, endDate )) &lt; diff_other, diff_other , abs(DATEDIFF( startDate, endDate )) )
						else diff_other -1
					end as limit_,
					abs(DATEDIFF(startDate, endDate )) as diff_days
				from
					tf_dws_voc_nps_supplt,
					(
						select
							@id := 0,
							6 as diff_other,
							#{model.dateUnit} as dateUnit,
							date( #{model.startDate}) as startDate,
							date( #{model.endDate}) as endDate
					) a_
			)b_
			where @id &lt;= limit_
		)
	</sql>

	<!--
		dateStr|dateStr_|scores|answer_code|code  |total|

		2023-07|2023-07 |  10.0|1302       |440000|    1|
		2023-07|2023-07 |   9.0|792        |140000|    1|
		2023-07|2023-07 |   9.0|994        |330000|    1|
		2023-07|2023-07 |  10.0|1239       |130000|    1|
		2023-07|2023-07 |  10.0|757        |220000|    1|
		2023-07|2023-07 |  10.0|818        |210000|    1|
		2023-07|2023-07 |   9.0|879        |110000|    1|
		2023-07|2023-07 |  10.0|1120       |420000|    1|
		2023-06|2023-06 |  10.0|743        |500000|    1|
		2023-06|2023-06 |   9.0|790        |510000|    1|
		2023-06|2023-06 |  10.0|915        |440000|    1|
		2023-06|2023-06 |  10.0|1007       |230000|    1|
		2023-06|2023-06 |   9.0|950        |340000|    1|
		2023-06|2023-06 |   9.0|1013       |310000|    1|
		2023-05|        |      |           |      |     |
		2023-04|        |      |           |      |     |
		2023-03|        |      |           |      |     |
		2023-02|        |      |           |      |     |
		2023-01|        |      |           |      |     |
	-->

	<sql id="queryCom_nps_core_data_emotion">
		select
			dateStr,
			code,
			sum(detractor) as detractor,
			sum(neutral) as neutral,
			sum(recommend) as recommend,
			sum(positive) as positive,
			sum(negative) as negative,
			sum(neutral2) as neutral2,
			sum(consult) as consult,
			sum(complaint) as complaint,
			sum(suggest) as suggest,
			sum(other) as other
		from
		(
			select
				first_dimension_code as code,
				sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
				sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
				sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
				sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
				sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
				sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
				sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
				sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
				sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
				sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
				sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
				case
					when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
					when dateUnit = 3 then year(date(publish_date))
					when dateUnit =-1 then date(publish_date)
					else date(publish_date)
				end as dateStr
			from
			tf_dws_voc_nps_record t,
			(
				select
					min(startDate) as startDate,
					max(endDate) as endDate,
					dateUnit
				from
					date_filter) df
				where
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
					and
					case
						when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
						when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
						when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
						else 0
					end = 1
				group by dateStr, code
			)f
		group by dateStr, code

	</sql>
	<sql id="queryCom_nps_core_data_scores">
		select
			sum(user_total) as user_total,
			dateStr,
			avg(avg_scores) as avg_scores ,
			province_code,
			sum(detractor) as detractor,
			sum(neutral) as neutral,
			sum(recommend) as recommend
		from
			(
				select
				count(distinct user_id) as user_total,
				province_code,
				sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
				sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
				sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
				avg(scores) over(partition by user_id,
				date(publish_date),
				classify ,
				answer_code,
				province_code ) as avg_scores,
				case
					when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
					when dateUnit = 3 then year(date(publish_date))
					when dateUnit =-1 then date(publish_date)
					else date(publish_date)
				end as dateStr
			from
				tf_dws_voc_nps_record t,
				(
				select
					min(startDate_r) as startDate,
					max(startDate_r) as endDate,
					dateUnit
				from
					date_filter) df
			where
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				and
					case
						when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
						when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
						when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
						else 0
					end = 1
			group by
				dateStr,
				user_id,
				classify ,
				answer_code ,
				province_code ,
				car_series_code
			)f
		group by
			dateStr,
			province_code
	</sql>


	<sql id="queryCom_nps_core_data_scores_distinct">
		select
			sum(user_total) as user_total,
			dateStr,
			avg(avg_scores) as avg_scores ,
			province_code,
			sum(detractor) as detractor,
			sum(neutral) as neutral,
			sum(recommend) as recommend
		from
			(
			select
				count(distinct user_id) as user_total,
				province_code,
				if(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0) ,  count(distinct user_id), 0 ) as detractor,
				if(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0) ,  count(distinct user_id), 0 ) as neutral,
				if(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0) ,  count(distinct user_id), 0 ) as recommend,
				avg(scores) over(partition by user_id,
				date(publish_date),
				classify ,
				answer_code,
				province_code ) as avg_scores,
				case
					when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
					when dateUnit = 3 then year(date(publish_date))
					when dateUnit =-1 then date(publish_date)
					else date(publish_date)
				end as dateStr
			from
				tf_dws_voc_nps_record t,
				(
				select
					min(startDate_r) as startDate,
					max(startDate_r) as endDate,
					dateUnit
				from
					date_filter) df
			where
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				and
					case
						when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
						when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
						when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
						else 0
					end = 1
			group by
				dateStr,
				user_id,
				classify ,
				answer_code ,
				province_code,
				car_series_code
			)f
		group by
			dateStr,
			province_code
	</sql>


	<sql id="queryCom_nps_core_data_scores_chian">
		select
			dateStr,
			avg(avg_scores) as avg_scores ,
			province_code,
			sum(detractor) as detractor,
			sum(neutral) as neutral,
			sum(recommend) as recommend
		from
			(
				select
				province_code,
				sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
				sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
				sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
				avg(scores) over(partition by user_id,
				date(publish_date),
				classify ,
				answer_code,
				province_code ) as avg_scores,
				case
					when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
					when dateUnit = 3 then year(date(publish_date))
					when dateUnit =-1 then date(publish_date)
					else date(publish_date)
				end as dateStr
			from
				tf_dws_voc_nps_record t,
				(
				select
					case
						when dateUnit = -1 then min(startDate_rc)
						else min(endDate_rc)
					end as startDate,
					max(endDate_rc) as endDate,
					dateUnit
				from
					date_filter) df
			where
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				and
					case
						when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
						when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
						when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
						else 0
					end = 1
			group by
				dateStr,
				user_id,
				classify ,
				answer_code ,
				province_code ,
				car_series_code
			)f
		group by
			dateStr,
			province_code
	</sql>

<sql id="queryCom_nps_core_data_scores_chian_distinct">
		select
			dateStr,
			avg(avg_scores) as avg_scores ,
			province_code,
			sum(detractor) as detractor,
			sum(neutral) as neutral,
			sum(recommend) as recommend
		from
			(
				select
				province_code,
				if(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0) ,  count(distinct user_id), 0 ) as detractor,
				if(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0) ,  count(distinct user_id), 0 ) as neutral,
				if(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0) ,  count(distinct user_id), 0 ) as recommend,
				avg(scores) over(partition by user_id,
				date(publish_date),
				classify ,
				answer_code,
				province_code ) as avg_scores,
				case
					when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
					when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
					when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
					when dateUnit = 3 then year(date(publish_date))
					when dateUnit =-1 then date(publish_date)
					else date(publish_date)
				end as dateStr
			from
				tf_dws_voc_nps_record t,
				(
				select
					case
						when dateUnit = -1 then min(startDate_rc)
						else min(endDate_rc)
					end as startDate,
					max(endDate_rc) as endDate,
					dateUnit
				from
					date_filter) df
			where
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				and
					case
						when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
						when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
						when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
						when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
						else 0
					end = 1
			group by
				dateStr,
				user_id,
				classify ,
				answer_code ,
				province_code ,
				car_series_code
			)f
		group by
			dateStr,
			province_code
	</sql>

	<sql id="queryCom_nps_core_data_focus">
			select
				dateStr,
				code,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
			(
				select
					<if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
						first_dimension_code  as code ,
					</if>
					<if test="model.dimensionRsLevel == 1">
						second_dimension_code as code ,
					</if>
					<if test="model.dimensionRsLevel == 2">
						three_dimension_code as code ,
					</if>
					<if test="model.dimensionRsLevel == 3">
						topic_code as code ,
					</if>
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
				tf_dws_voc_nps_record t,
				(
					select
						min(startDate) as startDate,
						max(endDate) as endDate,
						dateUnit
					from
						date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and
						case
							when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
							when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
							when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
							else 0
						end = 1
					group by dateStr, code
				)f
			group by dateStr, code
	</sql>

	<sql id="queryCom_nps_core_data_focus_chian">
			select
				dateStr,
				code,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
			(
				select
					<if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
						first_dimension_code  as code ,
					</if>
					<if test="model.dimensionRsLevel == 1">
						second_dimension_code as code ,
					</if>
					<if test="model.dimensionRsLevel == 2">
						three_dimension_code as code ,
					</if>
					<if test="model.dimensionRsLevel == 3">
						topic_code as code ,
					</if>
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
				tf_dws_voc_nps_record t,
				(
					select
						case
							when dateUnit = -1 then min(startDate_rc)
							else min(endDate_rc)
						end as startDate,
						max(endDate_rc) as endDate,
						dateUnit
					from
						date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and
						case
							when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
							when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
							when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
							else 0
						end = 1
					group by dateStr, code
				)f
			group by dateStr, code
	</sql>

<sql id="queryCom_nps_core_data_carseries_distinct">
			select
				sum(user_total) as user_total,
				dateStr,
				code,
				sum(detractor_dis) as detractor_dis,
				sum(neutral_dis) as neutral_dis,
				sum(recommend_dis) as recommend_dis,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive_dis) as positive_dis,
				sum(negative_dis) as negative_dis,
				sum(neutral2_dis) as neutral2_dis,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
			(
				select
					count(distinct user_id) as user_total,
					car_series_code  as code ,
					if(if(scores &gt;= 0	and scores &lt;= 6,1 ,	0) ,count(distinct user_id),0 ) as detractor_dis,
					if(if(scores &gt;= 7	and scores &lt;= 8,1 ,	0) ,count(distinct user_id),0 ) as neutral_dis,
					if(if(scores &gt;= 9   and scores  &lt;= 10,1 ,0) ,count(distinct user_id),0 ) as recommend_dis,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					if(if(dimension_emotion  = '正面',1 ,	0) ,count(distinct user_id),0 ) as positive_dis,
					if(if(dimension_emotion  = '负面',1 ,	0) ,count(distinct user_id),0 ) as negative_dis,
					if(if(dimension_emotion  = '中性',1 ,	0) ,count(distinct user_id),0 ) as neutral2_dis,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
				tf_dws_voc_nps_record t,
				(
					select
						min(startDate) as startDate,
						max(endDate) as endDate,
						dateUnit
					from
						date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and
						case
							when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
							when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
							when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
							else 0
						end = 1
					group by
						dateStr,
						user_id,
						classify ,
						answer_code ,
						province_code ,
						car_series_code
				)f
			group by dateStr, code
	</sql>

	<sql id="queryCom_nps_core_data_carseries_chian_distinct">
			select
				dateStr,
				code,
				sum(detractor_dis) as detractor_dis,
				sum(neutral_dis) as neutral_dis,
				sum(recommend_dis) as recommend_dis,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive_dis) as positive_dis,
				sum(negative_dis) as negative_dis,
				sum(neutral2_dis) as neutral2_dis,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
			(
				select
					car_series_code  as code ,
					if(if(scores &gt;= 0	and scores &lt;= 6,1 ,	0) ,count(distinct user_id),0 ) as detractor_dis,
					if(if(scores &gt;= 7	and scores &lt;= 8,1 ,	0) ,count(distinct user_id),0 ) as neutral_dis,
					if(if(scores &gt;= 9   and scores  &lt;= 10,1 ,0) ,count(distinct user_id),0 ) as recommend_dis,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					if(if(dimension_emotion  = '正面',1 ,	0) ,count(distinct user_id),0 ) as positive_dis,
					if(if(dimension_emotion  = '负面',1 ,	0) ,count(distinct user_id),0 ) as negative_dis,
					if(if(dimension_emotion  = '中性',1 ,	0) ,count(distinct user_id),0 ) as neutral2_dis,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
				tf_dws_voc_nps_record t,
				(
					select
						case
							when dateUnit = -1 then min(startDate_rc)
							else min(endDate_rc)
						end as startDate,
						max(endDate_rc) as endDate,
						dateUnit
					from
						date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and
						case
							when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
							when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
							when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
							else 0
						end = 1
					group by
						dateStr,
						user_id,
						classify ,
						answer_code ,
						province_code,
						car_series_code
				)f
			group by dateStr, code
	</sql>

	<sql id="queryCom_nps_core_data_carseries">
			select
				sum(user_total) as user_total,
				dateStr,
				code,
				sum(detractor_dis) as detractor_dis,
				sum(neutral_dis) as neutral_dis,
				sum(recommend_dis) as recommend_dis,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
			(
				select
					count(distinct user_id) as user_total,
					car_series_code  as code ,
					if(if(scores &gt;= 0	and scores &lt;= 6,1 ,	0) ,count(distinct user_id),0 ) as detractor_dis,
					if(if(scores &gt;= 7	and scores &lt;= 8,1 ,	0) ,count(distinct user_id),0 ) as neutral_dis,
					if(if(scores &gt;= 9   and scores  &lt;= 10,1 ,0) ,count(distinct user_id),0 ) as recommend_dis,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
				tf_dws_voc_nps_record t,
				(
					select
						min(startDate) as startDate,
						max(endDate) as endDate,
						dateUnit
					from
						date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and
						case
							when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
							when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
							when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
							else 0
						end = 1
					group by
						dateStr,
						user_id,
						classify ,
						answer_code ,
						province_code
				)f
			group by dateStr, code
	</sql>

	<sql id="queryCom_nps_core_data_carseries_chian">
			select
				dateStr,
				code,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
			(
				select
					car_series_code  as code ,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
				tf_dws_voc_nps_record t,
				(
					select
						case
							when dateUnit = -1 then min(startDate_rc)
							else min(endDate_rc)
						end as startDate,
						max(endDate_rc) as endDate,
						dateUnit
					from
						date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and
						case
							when dateUnit = 0 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )  between  df.startDate and df.endDate then 1
							when dateUnit = 1 and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 2 and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between  df.startDate and df.endDate then 1
							when dateUnit = 3 and year(date(publish_date)) between  df.startDate and df.endDate then 1
							when dateUnit = -1 and date(publish_date) between  df.startDate and df.endDate then 1
							else 0
						end = 1
					group by
						dateStr,
						user_id,
						classify ,
						answer_code ,
						province_code
				)f
			group by dateStr, code
	</sql>

	<sql id="queryCom_nps_core_data_emotion_intention_trend">
			select
				df.startDate_r as dateUnit,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
				date_filter df
			left join (
				select
					dateStr,
					sum(detractor) as detractor,
					sum(neutral) as neutral,
					sum(recommend) as recommend,
					sum(positive) as positive,
					sum(negative) as negative,
					sum(neutral2) as neutral2,
					sum(consult) as consult,
					sum(complaint) as complaint,
					sum(complain) as praise,
					sum(suggest) as suggest,
					sum(other) as other
				from
					(
					select
						sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
						sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
						sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
						sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
						sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
						sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
						sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
						sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
						sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
						sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
						sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
						case
							when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
							when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
							when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
							when dateUnit = 3 then year(date(publish_date))
							when dateUnit =-1 then date(publish_date)
							else date(publish_date)
						end as dateStr
					from
						tf_dws_voc_nps_record t,
						(
						select
							min(startDate_r) as startDate,
							max(startDate_r) as endDate,
							dateUnit
						from
							date_filter) df
					where
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
						and case
							when dateUnit = 0
							and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) ) between df.startDate and df.endDate then 1
							when dateUnit = 1
								and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between df.startDate and df.endDate then 1
								when dateUnit = 2
									and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between df.startDate and df.endDate then 1
									when dateUnit = 3
										and year(date(publish_date)) between df.startDate and df.endDate then 1
										when dateUnit = -1
											and date(publish_date) between df.startDate and df.endDate then 1
											else 0
										end = 1
									group by
										dateStr )f
				group by
					dateStr ) t on
				t.dateStr = df.startDate_r
			group by
				df.startDate_r
			order by
				df.startDate_r desc
	</sql>

	<sql id="queryCom_nps_core_data_emotion_intention_trend_chian">
		select
			df.endDate_rc as dateUnit,
			sum(detractor) as detractor,
			sum(neutral) as neutral,
			sum(recommend) as recommend,
			sum(positive) as positive,
			sum(negative) as negative,
			sum(neutral2) as neutral2,
			sum(consult) as consult,
			sum(complaint) as complaint,
			sum(complain) as praise,
			sum(suggest) as suggest,
			sum(other) as other
		from
			date_filter df
		left join (
			select
				dateStr,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				sum(positive) as positive,
				sum(negative) as negative,
				sum(neutral2) as neutral2,
				sum(consult) as consult,
				sum(complaint) as complaint,
				sum(complain) as praise,
				sum(suggest) as suggest,
				sum(other) as other
			from
				(
				select
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend,
					sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
					sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
					sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral2,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other,
					case
						when dateUnit = 0 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) )
						when dateUnit = 1 then concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) )
						when dateUnit = 2 then concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) )
						when dateUnit = 3 then year(date(publish_date))
						when dateUnit =-1 then date(publish_date)
						else date(publish_date)
					end as dateStr
				from
					tf_dws_voc_nps_record t,
					(
					select
						case
							when dateUnit = -1 then min(startDate_rc)
							else min(endDate_rc)
						end as startDate,
						max(endDate_rc) as endDate,
						dateUnit
					from
						date_filter) df
				where
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_required_attrs_filter"/>
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
					and case
						when dateUnit = 0
						and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%u')+ 1, 2, 0) ) between df.startDate and df.endDate then 1
						when dateUnit = 1
							and concat(year(date(publish_date)), '-', lpad(date_format(publish_date, '%m'), 2, 0) ) between df.startDate and df.endDate then 1
							when dateUnit = 2
								and concat(year(date(publish_date)), '-', lpad(quarter(date(publish_date)), 2, 0) ) between df.startDate and df.endDate then 1
								when dateUnit = 3
									and year(date(publish_date)) between df.startDate and df.endDate then 1
									when dateUnit = -1
										and date(publish_date) between df.startDate and df.endDate then 1
										else 0
									end = 1
								group by
									dateStr)f
			group by
				dateStr ) t on
			t.dateStr = df.endDate_rc
		group by
			dateUnit
		order by
			dateUnit desc
	</sql>


	<sql id="queryCom_nps_core_data_emotion_intention_trend_common">
		select
			dateUnit,
			detractor,
			replace(format(ifnull(detractor / scores_total * 100, 0), 2), ',', '') as detractorP,
			case
				when detractor_c = 0 and detractor != 0  then '999999'
				when detractor = 0 and detractor_c != 0 then replace(format(ifnull(-detractor_c * 100, 0), 2), ',', '')
				when detractor != 0 and detractor_c = 0 then replace(format(ifnull(detractor  * 100, 0), 2), ',', '')
				when detractor != 0 and detractor_c != 0 then replace(format(ifnull((detractor - detractor_c) / detractor_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as detractor_c,
			neutral,
			replace(format(ifnull(neutral / scores_total * 100, 0), 2), ',', '') as neutralP,
			case
				when neutral_c = 0 and neutral != 0  then '999999'
				when neutral = 0 and neutral_c != 0 then replace(format(ifnull(-neutral_c * 100, 0), 2), ',', '')
				when neutral != 0 and neutral_c = 0 then replace(format(ifnull(neutral  * 100, 0), 2), ',', '')
				when neutral != 0 and neutral_c != 0 then replace(format(ifnull((neutral - neutral_c) / neutral_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as neutral_c,
			recommend,
			replace(format(ifnull(recommend / scores_total * 100, 0), 2), ',', '') as recommendP,
			case
				when recommend_c = 0 and recommend != 0  then '999999'
				when recommend = 0 and recommend_c != 0 then replace(format(ifnull(-recommend_c * 100, 0), 2), ',', '')
				when recommend != 0 and recommend_c = 0 then replace(format(ifnull(recommend  * 100, 0), 2), ',', '')
				when recommend != 0 and recommend_c != 0 then replace(format(ifnull((recommend - recommend_c) / recommend_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as recommend_c,
			positive,
			replace(format(ifnull(positive / emotion_total * 100, 0), 2), ',', '') as positiveP,
			case
				when positive_c = 0 and positive != 0  then '999999'
				when positive = 0 and positive_c != 0 then replace(format(ifnull(-positive_c * 100, 0), 2), ',', '')
				when positive != 0 and positive_c = 0 then replace(format(ifnull(positive  * 100, 0), 2), ',', '')
				when positive != 0 and positive_c != 0 then replace(format(ifnull((positive - positive_c) / positive_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as positive_c,
			neutral2,
			replace(format(ifnull(neutral2 / emotion_total * 100, 0), 2), ',', '') as neutral2P,
			case
				when neutral2_c = 0 and neutral2 != 0  then '999999'
				when neutral2 = 0 and neutral2_c != 0 then replace(format(ifnull(-neutral2_c * 100, 0), 2), ',', '')
				when neutral2 != 0 and neutral2_c = 0 then replace(format(ifnull(neutral2  * 100, 0), 2), ',', '')
				when neutral2 != 0 and neutral2_c != 0 then replace(format(ifnull((neutral2 - neutral2_c) / neutral2_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as neutral2_c,
			negative,
			replace(format(ifnull(negative / emotion_total * 100, 0), 2), ',', '') as negativeP,
			case
				when negative_c = 0 and negative != 0  then '999999'
				when negative = 0 and negative_c != 0 then replace(format(ifnull(-negative_c * 100, 0), 2), ',', '')
				when negative != 0 and negative_c = 0 then replace(format(ifnull(negative  * 100, 0), 2), ',', '')
				when negative != 0 and negative_c != 0 then replace(format(ifnull((negative - negative_c) / negative_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as negative_c,
			consult,
			replace(format(ifnull(consult / intention_total * 100, 0), 2), ',', '') as consultP,
			case
				when consult_c = 0 and consult != 0  then '999999'
				when consult = 0 and consult_c != 0 then replace(format(ifnull(-consult_c * 100, 0), 2), ',', '')
				when consult != 0 and consult_c = 0 then replace(format(ifnull(consult  * 100, 0), 2), ',', '')
				when consult != 0 and consult_c != 0 then replace(format(ifnull((consult - consult_c) / consult_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as consult_c,
			complaint,
			replace(format(ifnull(complaint / intention_total * 100, 0), 2), ',', '') as complaintP,
			case
				when complaint_c = 0 and complaint != 0  then '999999'
				when complaint = 0 and complaint_c != 0 then replace(format(ifnull(-complaint_c * 100, 0), 2), ',', '')
				when complaint != 0 and complaint_c = 0 then replace(format(ifnull(complaint  * 100, 0), 2), ',', '')
				when complaint != 0 and complaint_c != 0 then replace(format(ifnull((complaint - complaint_c) / complaint_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as complaint_c,
			praise,
			replace(format(ifnull(complain / intention_total * 100, 0), 2), ',', '') as praiseP,
			case
				when complain_c = 0 and complain != 0  then '999999'
				when complain = 0 and complain_c != 0 then replace(format(ifnull(-complain_c * 100, 0), 2), ',', '')
				when complain != 0 and complain_c = 0 then replace(format(ifnull(complain  * 100, 0), 2), ',', '')
				when complain != 0 and complain_c != 0 then replace(format(ifnull((complain - complain_c) / complain_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as complain_c,
			suggest,
			replace(format(ifnull(suggest / intention_total * 100, 0), 2), ',', '') as suggestP,
			case
				when suggest_c = 0 and suggest != 0  then '999999'
				when suggest = 0 and suggest_c != 0 then replace(format(ifnull(-suggest_c * 100, 0), 2), ',', '')
				when suggest != 0 and suggest_c = 0 then replace(format(ifnull(suggest  * 100, 0), 2), ',', '')
				when suggest != 0 and suggest_c != 0 then replace(format(ifnull((suggest - suggest_c) / suggest_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as suggest_c,
			other,
			replace(format(ifnull(other / intention_total * 100, 0), 2), ',', '') as otherP,
			case
				when other_c = 0 and other != 0  then '999999'
				when other = 0 and other_c != 0 then replace(format(ifnull(-other_c * 100, 0), 2), ',', '')
				when other != 0 and other_c = 0 then replace(format(ifnull(other  * 100, 0), 2), ',', '')
				when other != 0 and other_c != 0 then replace(format(ifnull((other - other_c) / other_c * 100, 0), 2), ',', '')
			    else format(0,2)
			end as other_c,
			scores_total,
			emotion_total,
			intention_total
		from
			(
			select
				dateUnit,
				ifnull(detractor , 0) as detractor,
				ifnull(lag(detractor) over(order  by dateUnit ) ,0) as detractor_c,
				ifnull(neutral , 0) as neutral,
				ifnull(lag(neutral) over(order  by dateUnit ) ,0) as neutral_c,
				ifnull(recommend , 0) as recommend,
				ifnull(lag(recommend) over(order  by dateUnit ) ,0) as recommend_c,
				ifnull(positive , 0) as positive,
				ifnull(lag(positive) over(order  by dateUnit ) ,0) as positive_c,
				ifnull(neutral2 , 0) as neutral2,
				ifnull(lag(neutral2) over(order  by dateUnit ) ,0) as neutral2_c,
				ifnull(negative , 0) as negative,
				ifnull(lag(negative) over(order  by dateUnit ) ,0) as negative_c,
				ifnull(consult , 0) as consult,
				ifnull(lag(consult) over(order  by dateUnit ) ,0) as consult_c,
				ifnull(complaint , 0) as complaint,
				ifnull(lag(complaint) over(order  by dateUnit ) ,0) as complaint_c,
				ifnull(complain , 0) as praise,
				ifnull(lag(complain) over(order  by dateUnit ) ,0) as complain_c,
				ifnull(suggest , 0) as suggest,
				ifnull(lag(suggest) over(order  by dateUnit ) ,0) as suggest_c,
				ifnull(other , 0) as other,
				ifnull(lag(other) over(order  by dateUnit ) ,0) as other_c,
				(ifnull( detractor , 0) + ifnull( neutral , 0) + ifnull( recommend , 0) ) as scores_total,
				(ifnull( positive , 0) + ifnull( neutral2 , 0) + ifnull( negative , 0) ) as emotion_total,
				(ifnull( consult , 0) + ifnull( complaint , 0) + ifnull( complain , 0) + ifnull( suggest , 0) + ifnull( other , 0) ) as intention_total
			from
				(
				select
					c1.dateUnit,
					ifnull(c1.detractor , 0) as detractor,
					ifnull(c1.neutral , 0) as neutral,
					ifnull(c1.recommend , 0) as recommend,
					ifnull(c1.positive , 0) as positive,
					ifnull(c1.neutral2 , 0) as neutral2,
					ifnull(c1.negative , 0) as negative,
					ifnull(c1.consult , 0) as consult,
					ifnull(c1.complaint , 0)as complaint,
					ifnull(c1.complain , 0)as praise,
					ifnull(c1.suggest , 0) as suggest,
					ifnull(c1.other , 0) as other
				from
				(
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_emotion_intention_trend"/>
				)c1
			union all
			(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_emotion_intention_trend_chian"/>
			)
			)v1
		) vf
		group by dateUnit
		order by dateUnit desc
	</sql>


	<sql id="queryCom_nps_core_data_emotion_intention_trend_common2">
		select
			dateUnit,
			detractor,
			replace(format(ifnull(detractor / scores_total * 100, 0), 2), ',', '') as detractorP,
			case
				when detractor = 0 and detractor_c = 0  then format(0, 2)
				when detractor = 0 and detractor_c != 0 then replace(format(ifnull(-detractor_c * 100, 0), 2), ',', '')
				when detractor != 0 and detractor_c = 0 then replace(format(ifnull(detractor  * 100, 0), 2), ',', '')
				when detractor != 0 and detractor_c != 0 then replace(format(ifnull((detractor - detractor_c) / detractor_c * 100, 0), 2), ',', '')
			end as detractor_c,
			neutral,
			replace(format(ifnull(neutral / scores_total * 100, 0), 2), ',', '') as neutralP,
			case
				when neutral = 0 and neutral_c = 0  then format(0, 2)
				when neutral = 0 and neutral_c != 0 then replace(format(ifnull(-neutral_c * 100, 0), 2), ',', '')
				when neutral != 0 and neutral_c = 0 then replace(format(ifnull(neutral  * 100, 0), 2), ',', '')
				when neutral != 0 and neutral_c != 0 then replace(format(ifnull((neutral - neutral_c) / neutral_c * 100, 0), 2), ',', '')
			end as neutral_c,
			recommend,
			replace(format(ifnull(recommend / scores_total * 100, 0), 2), ',', '') as recommendP,
			case
				when recommend = 0 and recommend_c = 0  then format(0, 2)
				when recommend = 0 and recommend_c != 0 then replace(format(ifnull(-recommend_c * 100, 0), 2), ',', '')
				when recommend != 0 and recommend_c = 0 then replace(format(ifnull(recommend  * 100, 0), 2), ',', '')
				when recommend != 0 and recommend_c != 0 then replace(format(ifnull((recommend - recommend_c) / recommend_c * 100, 0), 2), ',', '')
			end as recommend_c,
			positive,
			replace(format(ifnull(positive / emotion_total * 100, 0), 2), ',', '') as positiveP,
			case
				when positive = 0 and positive_c = 0  then format(0, 2)
				when positive = 0 and positive_c != 0 then replace(format(ifnull(-positive_c * 100, 0), 2), ',', '')
				when positive != 0 and positive_c = 0 then replace(format(ifnull(positive  * 100, 0), 2), ',', '')
				when positive != 0 and positive_c != 0 then replace(format(ifnull((positive - positive_c) / positive_c * 100, 0), 2), ',', '')
			end as positive_c,
			neutral2,
			replace(format(ifnull(neutral2 / emotion_total * 100, 0), 2), ',', '') as neutral2P,
			case
				when neutral2 = 0 and neutral2_c = 0  then format(0, 2)
				when neutral2 = 0 and neutral2_c != 0 then replace(format(ifnull(-neutral2_c * 100, 0), 2), ',', '')
				when neutral2 != 0 and neutral2_c = 0 then replace(format(ifnull(neutral2  * 100, 0), 2), ',', '')
				when neutral2 != 0 and neutral2_c != 0 then replace(format(ifnull((neutral2 - neutral2_c) / neutral2_c * 100, 0), 2), ',', '')
			end as neutral2_c,
			negative,
			replace(format(ifnull(negative / emotion_total * 100, 0), 2), ',', '') as negativeP,
			case
				when negative = 0 and negative_c = 0  then format(0, 2)
				when negative = 0 and negative_c != 0 then replace(format(ifnull(-negative_c * 100, 0), 2), ',', '')
				when negative != 0 and negative_c = 0 then replace(format(ifnull(negative  * 100, 0), 2), ',', '')
				when negative != 0 and negative_c != 0 then replace(format(ifnull((negative - negative_c) / negative_c * 100, 0), 2), ',', '')
			end as negative_c,
			consult,
			replace(format(ifnull(consult / intention_total * 100, 0), 2), ',', '') as consultP,
			case
				when consult = 0 and consult_c = 0  then format(0, 2)
				when consult = 0 and consult_c != 0 then replace(format(ifnull(-consult_c * 100, 0), 2), ',', '')
				when consult != 0 and consult_c = 0 then replace(format(ifnull(consult  * 100, 0), 2), ',', '')
				when consult != 0 and consult_c != 0 then replace(format(ifnull((consult - consult_c) / consult_c * 100, 0), 2), ',', '')
			end as consult_c,
			complaint,
			replace(format(ifnull(complaint / intention_total * 100, 0), 2), ',', '') as complaintP,
			case
				when complaint = 0 and complaint_c = 0  then format(0, 2)
				when complaint = 0 and complaint_c != 0 then replace(format(ifnull(-complaint_c * 100, 0), 2), ',', '')
				when complaint != 0 and complaint_c = 0 then replace(format(ifnull(complaint  * 100, 0), 2), ',', '')
				when complaint != 0 and complaint_c != 0 then replace(format(ifnull((complaint - complaint_c) / complaint_c * 100, 0), 2), ',', '')
			end as complaint_c,
			praise,
			replace(format(ifnull(complain / intention_total * 100, 0), 2), ',', '') as praiseP,
			case
				when complain = 0 and complain_c = 0  then format(0, 2)
				when complain = 0 and complain_c != 0 then replace(format(ifnull(-complain_c * 100, 0), 2), ',', '')
				when complain != 0 and complain_c = 0 then replace(format(ifnull(complain  * 100, 0), 2), ',', '')
				when complain != 0 and complain_c != 0 then replace(format(ifnull((complain - complain_c) / complain_c * 100, 0), 2), ',', '')
			end as complain_c,
			suggest,
			replace(format(ifnull(suggest / intention_total * 100, 0), 2), ',', '') as suggestP,
			case
				when suggest = 0 and suggest_c = 0  then format(0, 2)
				when suggest = 0 and suggest_c != 0 then replace(format(ifnull(-suggest_c * 100, 0), 2), ',', '')
				when suggest != 0 and suggest_c = 0 then replace(format(ifnull(suggest  * 100, 0), 2), ',', '')
				when suggest != 0 and suggest_c != 0 then replace(format(ifnull((suggest - suggest_c) / suggest_c * 100, 0), 2), ',', '')
			end as suggest_c,
			other,
			replace(format(ifnull(other / intention_total * 100, 0), 2), ',', '') as otherP,
			case
				when other = 0 and other_c = 0  then format(0, 2)
				when other = 0 and other_c != 0 then replace(format(ifnull(-other_c * 100, 0), 2), ',', '')
				when other != 0 and other_c = 0 then replace(format(ifnull(other  * 100, 0), 2), ',', '')
				when other != 0 and other_c != 0 then replace(format(ifnull((other - other_c) / other_c * 100, 0), 2), ',', '')
			end as other_c,
			scores_total,
			emotion_total,
			intention_total
		from
			(
			select
				dateUnit,
				ifnull(detractor , 0) as detractor,
				ifnull(lag(detractor) over(order  by dateUnit ) ,0) as detractor_c,
				ifnull(neutral , 0) as neutral,
				ifnull(lag(neutral) over(order  by dateUnit ) ,0) as neutral_c,
				ifnull(recommend , 0) as recommend,
				ifnull(lag(recommend) over(order  by dateUnit ) ,0) as recommend_c,
				ifnull(positive , 0) as positive,
				ifnull(lag(positive) over(order  by dateUnit ) ,0) as positive_c,
				ifnull(neutral2 , 0) as neutral2,
				ifnull(lag(neutral2) over(order  by dateUnit ) ,0) as neutral2_c,
				ifnull(negative , 0) as negative,
				ifnull(lag(negative) over(order  by dateUnit ) ,0) as negative_c,
				ifnull(consult , 0) as consult,
				ifnull(lag(consult) over(order  by dateUnit ) ,0) as consult_c,
				ifnull(complaint , 0) as complaint,
				ifnull(lag(complaint) over(order  by dateUnit ) ,0) as complaint_c,
				ifnull(complain , 0) as praise,
				ifnull(lag(complain) over(order  by dateUnit ) ,0) as complain_c,
				ifnull(suggest , 0) as suggest,
				ifnull(lag(suggest) over(order  by dateUnit ) ,0) as suggest_c,
				ifnull(other , 0) as other,
				ifnull(lag(other) over(order  by dateUnit ) ,0) as other_c,
				(ifnull( detractor , 0) + ifnull( neutral , 0) + ifnull( recommend , 0) ) as scores_total,
				(ifnull( positive , 0) + ifnull( neutral2 , 0) + ifnull( negative , 0) ) as emotion_total,
				(ifnull( consult , 0) + ifnull( complaint , 0) + ifnull( complain , 0) + ifnull( suggest , 0) + ifnull( other , 0) ) as intention_total
			from
				(
				select
					c1.dateUnit,
					ifnull(c1.detractor , 0) as detractor,
					ifnull(c1.neutral , 0) as neutral,
					ifnull(c1.recommend , 0) as recommend,
					ifnull(c1.positive , 0) as positive,
					ifnull(c1.neutral2 , 0) as neutral2,
					ifnull(c1.negative , 0) as negative,
					ifnull(c1.consult , 0) as consult,
					ifnull(c1.complaint , 0)as complaint,
					ifnull(c1.complain , 0)as praise,
					ifnull(c1.suggest , 0) as suggest,
					ifnull(c1.other , 0) as other
				from
				(
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_emotion_intention_trend"/>
				)c1
			)v1
		) vf
	</sql>

	<sql id="queryCom_nps_date_filter_with">
        with date_filter as (
            select ( @date := DATE_ADD( @date, INTERVAL 1 DAY )) AS startDate ,endDate,diff_days
            <if test="model.dateUnit == -1">
                FROM tf_dws_voc_nps_supplt,( SELECT @date := DATE_ADD( endDate, interval -diff_days-1 day )
            </if>
            <if test="model.dateUnit == 0">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 week))
            </if>
            <if test="model.dateUnit == 1">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 2 month))
            </if>
            <if test="model.dateUnit == 2">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 quarter))
            </if>
            <if test="model.dateUnit == 3">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 year))
            </if>
        ,b_.*
            from
            (
                select
                    startDate,
                    endDate,
                    abs(DATEDIFF( date(endDate), date(startDate) ) + 1) as diff_days
                from
                ( 	select
                date(#{model.startDate}) as startDate,
                date(#{model.endDate}) as endDate
                ) a_
            ) b_
            ) temp
            WHERE @date &lt; date(endDate)
        )
    </sql>

	<sql id="queryCom_nps_date_filter_with_1">
        with date_filter as (
            select ( @date := DATE_ADD( @date, INTERVAL 1 DAY )) AS startDate ,endDate,diff_days
            <if test="model.dateUnit == -1">
                FROM tf_dws_voc_nps_supplt,( SELECT @date := DATE_ADD( endDate, interval -diff_days-1 day )
            </if>
            <if test="model.dateUnit == 0">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 week))
            </if>
            <if test="model.dateUnit == 1">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 month))
            </if>
            <if test="model.dateUnit == 2">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 quarter))
            </if>
            <if test="model.dateUnit == 3">
                FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 1 year))
            </if>
        ,b_.*
            from
            (
                select
                    startDate,
                    endDate,
                    abs(DATEDIFF( date(endDate), date(startDate) ) + 1) as diff_days
                from
                ( 	select
                date(#{model.startDate}) as startDate,
                date(#{model.endDate}) as endDate
                ) a_
            ) b_
            ) temp
            WHERE @date &lt; date(endDate)
        )
    </sql>

	<sql id="queryCom_nps_date_filter_with_6">
        with date_filter as (
        select ( @date := DATE_ADD( @date, INTERVAL 1 DAY )) AS startDate ,endDate,diff_days
        <if test="model.dateUnit == -1">
			FROM tf_dws_voc_nps_supplt,( SELECT @date := DATE_ADD( endDate, interval -diff_days-1 day )
		</if>
		<if test="model.dateUnit == 0">
			FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 6 week))
		</if>
		<if test="model.dateUnit == 1">
			FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 6 month))
		</if>
		<if test="model.dateUnit == 2">
			FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 6 quarter))
		</if>
		<if test="model.dateUnit == 3">
			FROM tf_dws_voc_nps_supplt,( SELECT @date :=date(DATE_SUB( endDate , interval 6 year))
		</if>
        ,b_.*
        from
        (
        select
        startDate,
        endDate,
        abs(DATEDIFF( date(endDate), date(startDate) ) + 1) as diff_days
        from
        ( 	select
        date(#{model.startDate}) as startDate,
        date(#{model.endDate}) as endDate
        ) a_
        ) b_
        ) temp
        WHERE @date &lt; date(endDate)
        )
    </sql>

    <sql id="queryCom_nps_date_filter_chian_with">
        with date_filter as (
            select
            ( @date := DATE_ADD( @date, interval 1 day )) as startDate ,
            startDate_,
            endDate,
            diff_days
            from
            tf_dws_voc_nps_supplt,
            (
            select
            @date := DATE_ADD( startDate, interval diff_days-2 day ) ,
            endDate,
            startDate,
            DATE_SUB( startDate, interval abs(diff_days)+1  day ) startDate_,
            abs(DATEDIFF( date(endDate), date(startDate) ) + 1) as diff_days
            from
            (
            select
            startDate,
            DATE_SUB( startDate, interval 1 day ) as endDate
            from
            ( 	select
                date(#{model.startDate}) as startDate,
                date(#{model.endDate}) as endDate
            ) a_
            ) b_
            ) temp
            WHERE @date &lt; DATE_ADD( startDate, interval -1 day )
        )
    </sql>




    <sql id="queryCom_nps_default_date_filter_group_attrs">
        <if test="model.dateUnit == 0">
			concat(DATE_FORMAT( dateStr , '%Y' ),'-',DATE_FORMAT( dateStr , '%u' ) + 1) as dateUnit
        </if>
        <if test="model.dateUnit == 1">
            concat(year(dateStr),'-', month(dateStr) ) as dateUnit
        </if>
        <if test="model.dateUnit == 2">
            concat(year(dateStr),'-', quarter(dateStr) ) as dateUnit
        </if>
        <if test="model.dateUnit == 3">
            concat(year(dateStr) ) as dateUnit
        </if>
        <if test="model.dateUnit == -1">
            concat(year(dateStr), '-',month(dateStr),'-', day(dateStr) ) as dateUnit
        </if>
    </sql>

    <sql id="queryCom_nps_default_date_filter">
        <if test="model.startDate !=null and model.startDate != ''and
                      model.endDate !=null and model.endDate != ''">
            and date(publish_date) between date(#{model.startDate}) and date(#{model.endDate})
        </if>
    </sql>

	 <sql id="queryCom_nps_default_car_series_emotion_data">
		with core_data as (
			select
				f1.dateStr,
				f1.avg_scores,
				f1.consult,
				f1.complaint,
				f1.praise,
				f1.suggest,
				f1.other,
				f2.avg_scores as avg_scoresC,
				f2.consult as consultC,
				f2.complaint as complaintC,
				f2.complain as complainC,
				f2.suggest as suggestC,
				f2.other as otherC
			from
				(
				select
					df.startDate as dateStr,
					format(avg(scores), 2) as avg_scores,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other
				from
					(
					select
						*
					from
						date_filter df  ) df
				left join tf_dws_voc_nps_record t on
					df.startDate = date(publish_date)
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
					<if test="model.province != null and model.province.size()>0">
						and t.province_code in
						<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
							#{item}
						</foreach>
					</if>
					group by
						dateStr
				) f1
			left join (
				select
					DATE_ADD(df.startDate , interval diff_days day) as dateStr,
					format(avg(scores), 2) as avg_scores,
					sum(case INTENTION_TYPE when '咨询' then 1 else 0 end) as consult,
					sum(case INTENTION_TYPE when '投诉' then 1 else 0 end) as complaint,
					sum(case INTENTION_TYPE when '表扬' then 1 else 0 end) as praise,
					sum(case INTENTION_TYPE when '建议' then 1 else 0 end) as suggest,
					sum(case INTENTION_TYPE when '其他' then 1 else 0 end) as other
				from
					(
					select
						DATE_ADD(startDate , interval -diff_days day) as startDate,
						DATE_ADD(endDate , interval -diff_days day) as endDate ,
						diff_days
					from
						date_filter) df
				left join tf_dws_voc_nps_record t on df.startDate = date(publish_date)
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
					group by
						dateStr

			) f2 on  f1.dateStr = f2.dateStr
		)
	</sql>


    <!-- 调研人数统计 / 调研参与者分类占比 -->
    <select id="participantProportion"  resultType="com.car.voc.vo.NPSParticipantProportionVo">
		select
			replace(ifnull(sum(detractor), 0), ',', '') as detractor,
			case
				when detractor_c = 0 and detractor != 0  then '999999'
				when detractor = 0 and detractor_c = 0  then format(0, 2)
				when detractor = 0 and detractor_c != 0 then replace(format(ifnull(-detractor_c * 100, 0), 2), ',', '')
				when detractor != 0 and detractor_c = 0 then replace(format(ifnull(detractor  * 100, 0), 2), ',', '')
				when detractor != 0 and detractor_c != 0 then replace(format(ifnull((detractor - detractor_c) / detractor_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as detractor_c,
			replace(ifnull( format(( sum(detractor) / (sum(detractor) + sum(neutral) + sum(recommend))* 100 ) , 2) , 0), ',', '') as detractorP,
			replace(ifnull(sum(neutral) , 0), ',', '') as neutral,
			case
				when neutral_c = 0 and neutral != 0  then '999999'
				when neutral = 0 and neutral_c = 0  then format(0, 2)
				when neutral = 0 and neutral_c != 0 then replace(format(ifnull(-neutral_c * 100, 0), 2), ',', '')
				when neutral != 0 and neutral_c = 0 then replace(format(ifnull(neutral  * 100, 0), 2), ',', '')
				when neutral != 0 and neutral_c != 0 then replace(format(ifnull((neutral - neutral_c) / neutral_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as neutral_c,
			replace(ifnull( format(( sum(neutral) / (sum(detractor) + sum(neutral) + sum(recommend))* 100 ) , 2) , 0), ',', '') as neutralP,
			replace(ifnull( sum(recommend) , 0), ',', '') as recommend,
			case
				when recommend_c = 0 and recommend != 0  then '999999'
				when recommend = 0 and recommend_c = 0  then format(0, 2)
				when recommend = 0 and recommend_c != 0 then replace(format(ifnull(-recommend_c * 100, 0), 2), ',', '')
				when recommend != 0 and recommend_c = 0 then replace(format(ifnull(recommend  * 100, 0), 2), ',', '')
				when recommend != 0 and recommend_c != 0 then replace(format(ifnull((recommend - recommend_c) / recommend_c * 100, 0), 2), ',', '')
				else format(0,2)
			end as recommend_c,
			ifnull(sum(user_total), 0) as total,
			replace(ifnull( format( sum(recommend) / ( sum(detractor) + sum(neutral) + sum(recommend) ) * 100 , 2) , 0), ',', '') as recommendP,
			replace(ifnull( format( ( sum(recommend) - sum(detractor) )/ ( sum(detractor) + sum(neutral) + sum(recommend) )* 100 , 2) , 0), ',', '') as totalRecommendP,
			null as dateUnit
		from
			(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			select
				sum(user_total) as user_total,
				c1.code,
				ifnull(avg(c1.avg_scores) over(partition by c1.code), 0) as avg_scores,
				ifnull(avg(c2.avg_scores) over(partition by c2.code), 0) as avg_scores_c,
				ifnull(sum(c1.detractor), 0) as detractor,
				ifnull(sum(c1.neutral), 0) as neutral,
				ifnull(sum(c1.recommend), 0) as recommend,
				ifnull(sum(c2.detractor), 0) as detractor_c,
				ifnull(sum(c2.neutral), 0) as neutral_c,
				ifnull(sum(c2.recommend) , 0)as recommend_c,
				c1.national_ranking,
				c2.national_ranking as national_ranking_c
			from
				(
				select
					sum(user_total) as user_total,
					df.startDate_r as dateUnit,
					avg(avg_scores) as avg_scores,
					sum(detractor) as detractor,
					sum(neutral) as neutral,
					sum(recommend) as recommend,
					rank () over(
				order by
					avg(avg_scores) desc ) as national_ranking,
					<if test="model.province != null and model.province.size > 0">
						df.province_code  as code
					</if>
					<if test="model.province == null or model.province.size == 0">
						df.area_code  as code
					</if>
				from
					(
					select
						df.*,
						province_code ,
						area_code
					from
						date_filter df,
						(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						<if test="model.areas != null and model.areas.size()>0">
							where area_code in
							<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
			    		) pa
					group by
						province_code ,
						df.startDate_r )df
				left join (
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores_distinct"/>
				) t on
					t.dateStr = df.startDate_r
					and t.province_code = df.province_code
				group by
					code
				order by
					avg_scores desc ) c1
			left join (
				select
					df.startDate_rc as dateUnit,
					'2' as data_type,
					rank () over(
					order by avg(avg_scores) desc ) as national_ranking,
					avg(avg_scores) as avg_scores,
					sum(detractor) as detractor,
					sum(neutral) as neutral,
					sum(recommend) as recommend,
					<if test="model.province != null and model.province.size > 0">
						df.province_code  as code
					</if>
					<if test="model.province == null or model.province.size == 0">
						df.area_code  as code
					</if>
				from
					(
					select
						df.*,
						province_code ,
						area_code
					from
						date_filter df,
						(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						<if test="model.areas != null and model.areas.size()>0">
							where area_code in
							<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						) pa
					group by
						province_code ,
						df.startDate_r )df
				left join (
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores_chian_distinct"/>
				) t on
					t.dateStr = df.startDate_rc
					and t.province_code = df.province_code
				group by
					code
				order by
					avg_scores desc ) c2 on
				c1.code = c2.code
		)vv
	</select>






	<sql id="queryCom_nps_default_scores_data">
		with core_data as (
			select
				f1.code,
				f1.level,
				f1.dateStr,
				f1.ranking,
				f1.avg_scores,
				f1.detractor,
				f1.neutral,
				f1.recommend,
				f2.ranking as rankingC,
				f2.avg_scores as avg_scoresC,
				f2.detractor as detractorC,
				f2.neutral as neutralC,
				f2.recommend as recommendC
			from
			(
			select
					df.startDate as dateStr,
					rank  () over(partition  by df.startDate  order by avg(scores) desc ) as ranking,
					<if test="model.region != null and model.region != ''">
						df.area_code  as code, 2 as level,
					</if>
					<if test="model.region == null or model.region == ''">
						df.province_code  as code, 3 as level,
					</if>
					format(avg(scores), 2) as avg_scores,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend
				from
				(
					select * from date_filter df,
					( select * from tc_province_area
						<if test="model.province != null and model.province.size()>0">
							where province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
					) pa
				) df
				left join
				tf_dws_voc_nps_record t on df.startDate = date(publish_date) and t.province_code = df.province_code
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
				group by code,dateStr
			) f1
			left join (
				select
					DATE_ADD(df.startDate , interval diff_days day) as dateStr,
					<if test="model.region != null and model.region != ''">
						df.area_code  as code, 2 as level,
					</if>
					<if test="model.region == null or model.region == ''">
						df.province_code  as code, 3 as level,
					</if>
					format(avg(scores), 2) as avg_scores,
					rank  () over(partition  by df.startDate   order by avg(scores) desc ) as ranking,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend
				from
				(
					select
						DATE_ADD(startDate , interval -diff_days day) as startDate,
						DATE_ADD(endDate , interval -diff_days day) as endDate  ,
						diff_days,
						area_code, province_code
					from date_filter , tc_province_area
				) df
				left join
				tf_dws_voc_nps_record t on df.startDate = date(publish_date) and t.province_code = df.province_code
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
				group by code,dateStr
			) f2 on f1.code = f2.code and f1.dateStr =  f2.dateStr
		)
	</sql>


	<sql id="queryCom_nps_default_car_series_scores_data">
		with core_data as (
			select
				f1.code,
				f1.level,
				f1.dateStr,
				f1.ranking,
				f1.avg_scores,
				f1.detractor,
				f1.neutral,
				f1.recommend,
				f2.ranking as rankingC,
				f2.avg_scores as avg_scoresC,
				f2.detractor as detractorC,
				f2.neutral as neutralC,
				f2.recommend as recommendC
			from
			(
			select
					df.startDate as dateStr,
					rank  () over(partition  by df.startDate  order by avg(scores) desc ) as ranking,
					car_series_code  as code, 2 as level,
					format(avg(scores), 2) as avg_scores,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend
				from
				(
					select * from date_filter df,
					( select * from tc_province_area
						<if test="model.province != null and model.province.size()>0">
							where province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
					) pa
				) df
				left join
				tf_dws_voc_nps_record t on df.startDate = date(publish_date) and t.province_code = df.province_code
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
				group by code,dateStr
			) f1
			left join (
				select
					DATE_ADD(df.startDate , interval diff_days day) as dateStr,
					car_series_code  as code, 2 as level,
					format(avg(scores), 2) as avg_scores,
					rank  () over(partition  by df.startDate   order by avg(scores) desc ) as ranking,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend
				from
				(
					select
						DATE_ADD(startDate , interval -diff_days day) as startDate,
						DATE_ADD(endDate , interval -diff_days day) as endDate  ,
						diff_days,
						area_code, province_code
					from date_filter , tc_province_area
				) df
				left join
				tf_dws_voc_nps_record t on df.startDate = date(publish_date) and t.province_code = df.province_code
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
				group by code,dateStr
			) f2 on f1.code = f2.code and f1.dateStr =  f2.dateStr
		)
	</sql>








	<sql id="queryCom_nps_default_scores_data_chian">
		with core_data as (
			select
				f1.code,
				f1.level,
				f1.dateStr,
				f1.ranking,
				f1.avg_scores,
				f1.detractor,
				f1.neutral,
				f1.recommend,
				f2.ranking as rankingC,
				f2.avg_scores as avg_scoresC,
				f2.detractor as detractorC,
				f2.neutral as neutralC,
				f2.recommend as recommendC
			from
			(
			select
					df.startDate as dateStr,
					rank  () over(partition  by df.startDate  order by avg(scores) desc ) as ranking,
					<if test="model.region != null and model.region != ''">
						df.area_code  as code, 2 as level,
					</if>
					<if test="model.region == null or model.region == ''">
						df.province_code  as code, 3 as level,
					</if>
					format(avg(scores), 2) as avg_scores,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend
				from
				(
					select * from date_filter df,
					( select * from tc_province_area
						<if test="model.province != null and model.province.size()>0">
							where province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
					) pa
				) df
				left join
				tf_dws_voc_nps_record t on df.startDate = date(publish_date) and t.province_code = df.province_code
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
				group by code,dateStr
			) f1
			left join (
				select
					DATE_ADD(df.startDate , interval diff_days day) as dateStr,
					<if test="model.region != null and model.region != ''">
						df.area_code  as code, 2 as level,
					</if>
					<if test="model.region == null or model.region == ''">
						df.province_code  as code, 3 as level,
					</if>
					format(avg(scores), 2) as avg_scores,
					rank  () over(partition  by df.startDate   order by avg(scores) desc ) as ranking,
					sum(if(scores  &gt;= 0 and scores &lt;= 6, 1 ,0)) as detractor,
					sum(if(scores  &gt;= 7 and scores &lt;= 8, 1 ,0)) as neutral,
					sum(if(scores  &gt;= 9 and scores &lt;= 10,1 ,0)) as recommend
				from
				(
					select
						DATE_ADD(startDate , interval -diff_days day) as startDate,
						DATE_ADD(endDate , interval -diff_days day) as endDate  ,
						diff_days,
						area_code, province_code
					from date_filter , tc_province_area
				) df
				left join
				tf_dws_voc_nps_record t on df.startDate = date(publish_date) and t.province_code = df.province_code
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
				<if test="model.province != null and model.province.size()>0">
					and t.province_code in
					<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				</if>
				group by code,dateStr
			) f2 on f1.code = f2.code and f1.dateStr =  f2.dateStr
		)
	</sql>

	<select id="analysisDefault"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			<if test="model.province != null and model.province.size()>0">
			code , 	2 as level,
			</if>
			<if test="model.province == null or model.province.size == 0">
			null as code , 1 as level,
			</if>
			national_ranking,
			national_ranking_c,
			format(ifnull(avg(avg_scores), 0) ,2) as avg_scores ,
			format(replace(format(if(avg(avg_scores) is null, 0, avg(avg_scores) ),2) - format(if(avg(avg_scores_c) is null, 0, avg(avg_scores_c) ) , 2), ',', '') , 2) as avg_scores_c,
			null as dateUnit
		from
		(
		<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
		select
			c1.code,
			avg(c1.avg_scores) over(partition by c1.code) as avg_scores,
			avg(c2.avg_scores) over(partition by c2.code) as avg_scores_c,
			c1.national_ranking,
			c2.national_ranking as national_ranking_c
		from
			(
			select
				df.startDate_r as dateUnit,
				avg(avg_scores) as avg_scores,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				rank () over(
			order by
				avg(avg_scores) desc ) as national_ranking,
				<if test="model.province != null and model.province.size > 0">
					df.area_code  as code
				</if>
				<if test="model.province == null or model.province.size == 0">
					df.province_code  as code
				</if>
			from
				(
				select
					df.*,
					province_code ,
					area_code
				from
					date_filter df,
					(
					select
						province_code ,
						area_code
					from
						tc_province_area a
					where
					<if test="model.areas != null and model.areas.size()>0">
						area_code in
						<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
							#{item}
						</foreach>
					</if>
					<if test="model.province != null and model.province.size()>0">
						and province_code in
						<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
							#{item}
						</foreach>
					</if>
					) pa
				group by
					province_code ,
					df.startDate_r )df
			left join (
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores"/>
			) t on
				t.dateStr = df.startDate_r
				and t.province_code = df.province_code
			group by
				code
			order by
				avg_scores desc ) c1
		left join (
			select
				df.startDate_rc as dateUnit,
				'2' as data_type,
				rank () over(
				order by avg(avg_scores) desc ) as national_ranking,
				avg(avg_scores) as avg_scores,
				sum(detractor) as detractor,
				sum(neutral) as neutral,
				sum(recommend) as recommend,
				<if test="model.province != null and model.province.size > 0">
					df.area_code  as code
				</if>
				<if test="model.province == null or model.province.size == 0">
					df.province_code  as code
				</if>
			from
				(
				select
					df.*,
					province_code ,
					area_code
				from
					date_filter df,
					(
					select
						province_code ,
						area_code
					from
						tc_province_area a
					where
					<if test="model.areas != null and model.areas.size()>0">
						area_code in
						<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
							#{item}
						</foreach>
					</if>
					<if test="model.province != null and model.province.size()>0">
						and province_code in
						<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
							#{item}
						</foreach>
					</if>
				) pa
				group by
					province_code ,
					df.startDate_r )df
			left join (
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores_chian"/>
			) t on
				t.dateStr = df.startDate_rc
				and t.province_code = df.province_code
			group by
				code
			order by
				avg_scores desc ) c2 on
			c1.code = c2.code
		)vv
		order by national_ranking
	</select>

	<select id="analysisTrendDefault"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			<if test="model.province != null and model.province.size()>0">
			code , 	2 as level,
			</if>
			<if test="model.province == null or model.province.size == 0">
			0 as code , 1 as level,
			</if>
			replace(format(ifnull(avg(avg_scores),0), 2), ',', '') as avg_scores,
			format(replace(format(ifnull( avg(avg_scores), 0),2) - format(ifnull(avg(avg_scores_c),0) , 2), ',', '') , 2) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from
		(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_6"/>
			select
				dateUnit,
				avg_scores,
				lag(avg_scores) over(partition by code order by dateUnit ) as avg_scores_c,
				code
			from
			(
			select
				dateUnit,
				avg_scores  as avg_scores,
				avg_scores_c,
				code
			from
				(
				select
					df.startDate_r as dateUnit,
					avg(avg_scores) as avg_scores,
					0 as avg_scores_c,
					<if test="model.province != null and model.province.size > 0">
						df.area_code  as code
					</if>
					<if test="model.province == null or model.province.size == 0">
						df.province_code  as code
					</if>
				from
					(
					select
						df.*,
						province_code ,
						area_code
					from
						date_filter df,
						(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						where
						<if test="model.areas != null and model.areas.size()>0">
							area_code in
							<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						<if test="model.province != null and model.province.size()>0">
							and province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						) pa
					group by
						province_code ,
						df.startDate_r )df
				left join (
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores"/>
				) t on
					t.dateStr = df.startDate_r
					and t.province_code = df.province_code
				group by
					code,df.startDate_r
				order by
					avg_scores desc
			)c1
			union all
			(
				select
				df.endDate_rc as dateUnit,
				avg(avg_scores)  as avg_scores,
				0 as avg_scores_c,
				<if test="model.province != null and model.province.size > 0">
					df.area_code  as code
				</if>
				<if test="model.province == null or model.province.size == 0">
					df.province_code  as code
				</if>
			from
			(
					select
						df.*,
						province_code ,
						area_code
					from
						date_filter df,
						(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						where
						<if test="model.areas != null and model.areas.size()>0">
							area_code in
							<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						<if test="model.province != null and model.province.size()>0">
							and province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
					) pa
					group by
						province_code ,
						df.startDate_r )df
				left join
			(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores_chian"/>
			) t on t.dateStr = df.endDate_rc and t.province_code = df.province_code
				group by
					code, dateUnit
				order by
					avg_scores desc
			)
		)v1
		) vv
		group by dateUnit
		order by dateUnit desc
	</select>


	<select id="analysisTrendDefault2"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			code ,
			level,
			ifnull(avg_scores, 0) as avg_scores,
			ifnull(avg_scores_c, 0) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_1"/>
			select
				0 as code ,
				1 as level,
				ifnull( format(avg(avg_scores), 2 ), 0) as avg_scores,
				format( ifnull(avg(avg_scores), 0) -  ifnull(avg(avg_scoresC), 0) , 2 )as avg_scores_c,
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
			from(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_scores_data_chian"/>
				select
					dateStr,
					level,
					format(avg(avg_scores) ,2) as avg_scores,
					format(avg(avg_scoresC) ,2) as avg_scoresC
				from
				core_data
				group by dateStr
				order by dateStr desc
			)v
			group by dateUnit, code
		)vv
		order by dateUnit desc
    </select>


    <select id="analysisTrendArea"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			<if test="model.province != null and model.province.size()>0">
			code , 3 as level,
			</if>
			<if test="model.province == null or model.province.size == 0">
			code , 2 as level,
			</if>
			replace(format(avg_scores, 2), ',', '') as avg_scores,
			replace(format(format(ifnull(avg_scores,0) , 2) - format(ifnull(avg_scores_c,0) , 2) , 2), ',', '') as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from
		(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_6"/>
			select
				dateUnit,
				avg_scores,
				lag(avg_scores) over(partition by code order by dateUnit ) as avg_scores_c,
				code
			from
			(
			select
				dateUnit,
				ifnull(avg_scores, 0)  as avg_scores,
				avg_scores_c,
				code
			from
				(
					select
						df.startDate_r as dateUnit,
						avg(avg_scores) as avg_scores,
						0 as avg_scores_c,
						<if test="model.province != null and model.province.size > 0">
							df.province_code  as code
						</if>
						<if test="model.province == null or model.province.size == 0">
							df.area_code  as code
						</if>
					from
						(
						select
							df.*,
							province_code ,
							area_code
						from
							date_filter df,
							(
							select
								province_code ,
								area_code
							from
								tc_province_area a
							where
							<if test="model.areas != null and model.areas.size()>0">
								area_code in
								<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
									#{item}
								</foreach>
							</if>
							<if test="model.province != null and model.province.size()>0">
							and province_code in
								<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
									#{item}
								</foreach>
							</if>

								) pa
						group by
							province_code ,
							df.startDate_r )df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores"/>
					) t on
						t.dateStr = df.startDate_r
						and t.province_code = df.province_code
					group by
						code,df.startDate_r
					order by
						avg_scores desc
				)c1
				union all
				(
					select
					df.endDate_rc as dateUnit,
					avg(avg_scores) as avg_scores,
					0 as avg_scores_c,
					<if test="model.province != null and model.province.size > 0">
						df.province_code  as code
					</if>
					<if test="model.province == null or model.province.size == 0">
						df.area_code  as code
					</if>
					from
					(
						select
							df.*,
							province_code ,
							area_code
						from
							date_filter df,
							(
							select
								province_code ,
								area_code
							from
								tc_province_area a
							where
							<if test="model.areas != null and model.areas.size()>0">
								area_code in
								<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
									#{item}
								</foreach>
							</if>
							<if test="model.province != null and model.province.size()>0">
							and province_code in
								<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
									#{item}
								</foreach>
							</if>
								) pa
						group by
							province_code ,
							df.startDate_r )df
					left join
				(
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores_chian"/>
				) t on t.dateStr = df.endDate_rc and t.province_code = df.province_code
					group by
						code, dateUnit
					order by
						avg_scores desc
				)
			)v1
		) vv
		order by dateUnit desc
	</select>

    <select id="analysisTrendArea2"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			code ,
			level,
			ifnull(avg_scores, 0) as avg_scores,
			ifnull(avg_scores_c, 0) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_6"/>
			select
				code ,
				2 as level,
				ifnull( format(avg(avg_scores), 2 ), 0) as avg_scores,
				format( ifnull(avg(avg_scores), 0) -  ifnull(avg(avg_scoresC), 0) , 2 )as avg_scores_c,
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
			from(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_scores_data_chian"/>
				select
					dateStr,
					code,
					level,
					format(avg(avg_scores) ,2) as avg_scores,
					format(avg(avg_scoresC) ,2) as avg_scoresC
				from
				core_data
				group by code, dateStr
			)v
			group by dateUnit, code
		)vv
		order by dateUnit desc
    </select>


	<select id="analysisTrendArea3"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			<if test="model.province != null and model.province.size()>0">
			code ,3 as level,
			</if>
			<if test="model.province == null or model.province.size == 0">
			code , 2 as level,
			</if>
			national_ranking,
			national_ranking_c,
			format(ifnull(avg_scores, 0) ,2) as avg_scores ,
			format(ifnull(format(avg_scores ,2) - format(avg_scores_c ,2), 0) ,2) as avg_scores_c,
			null as dateUnit
		from
			(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			select
				c1.code,
				avg(c1.avg_scores) over(partition by c1.code) as avg_scores,
				avg(c2.avg_scores) over(partition by c2.code) as avg_scores_c,
				c1.national_ranking,
				c2.national_ranking as national_ranking_c
			from
				(
				select
					df.startDate_r as dateUnit,
					avg(avg_scores) as avg_scores,
					sum(detractor) as detractor,
					sum(neutral) as neutral,
					sum(recommend) as recommend,
					rank () over(
				order by
					avg(avg_scores) desc ) as national_ranking,
					<if test="model.province != null and model.province.size > 0">
						df.province_code  as code
					</if>
					<if test="model.province == null or model.province.size == 0">
						df.area_code  as code
					</if>
				from
					(
					select
						df.*,
						province_code ,
						area_code
					from
						date_filter df,
						(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						where
						<if test="model.areas != null and model.areas.size()>0">
							area_code in
							<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						<if test="model.province != null and model.province.size()>0">
							and  province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						) pa
					group by
						province_code ,
						df.startDate_r )df
				left join (
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores"/>
				) t on
					t.dateStr = df.startDate_r
					and t.province_code = df.province_code
				group by
					code
				order by
					avg_scores desc ) c1
			left join (
				select
					df.startDate_rc as dateUnit,
					'2' as data_type,
					rank () over(
					order by avg(avg_scores) desc ) as national_ranking,
					avg(avg_scores) as avg_scores,
					sum(detractor) as detractor,
					sum(neutral) as neutral,
					sum(recommend) as recommend,
					<if test="model.province != null and model.province.size > 0">
						df.province_code  as code
					</if>
					<if test="model.province == null or model.province.size == 0">
						df.area_code  as code
					</if>
				from
					(
					select
						df.*,
						province_code ,
						area_code
					from
						date_filter df,
						(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						where
						<if test="model.areas != null and model.areas.size()>0">
							 area_code in
							<foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						<if test="model.province != null and model.province.size()>0">
							and province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
						) pa
					group by
						province_code ,
						df.startDate_r )df
				left join (
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores_chian"/>
				) t on
					t.dateStr = df.startDate_rc
					and t.province_code = df.province_code
				group by
					code
				order by
					avg_scores desc ) c2 on
				c1.code = c2.code
			group by
		code )vv
		order by national_ranking
    </select>


	<select id="analysisTrendAreaSum"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			<if test="model.province != null and model.province.size()>0">
			code , 2 as level,
			</if>
			<if test="model.province == null or model.province.size == 0">
			code , 3 as level,
			</if>
			ifnull(avg_scores, 0) as avg_scores,
			ifnull(avg_scores_c, 0) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_6"/>
			select
				df.endDate_rc,
				df.startDate_r as dateUnit,
				replace(format(ifnull(avg(avg_scores), 0), 2), ',', '') as avg_scores,
				replace(format(ifnull(lag(avg(avg_scores)) over(partition by dateUnit ), 0), 2), ',', '') as avg_scores_c,
				replace(format(ifnull(sum(detractor), 0), 0), ',', '') as detractor,
				replace(format(ifnull(sum(detractor), 0) - ifnull(lag(sum(detractor)) over(partition by dateUnit ), 0) ,0), ',', '') as detractor_c,
				replace(format(ifnull(sum(neutral), 0), 0), ',', '') as neutral,
				replace(format(ifnull(sum(neutral), 0) - ifnull(lag(sum(neutral)) over(partition by dateUnit ), 0) ,0), ',', '') as neutral_c,
				replace(format(ifnull(sum(recommend), 0), 0), ',', '') as recommend,
				replace(format(ifnull(sum(recommend), 0) - ifnull(lag(sum(recommend)) over(partition by dateUnit ), 0) ,0), ',', '') as recommend_c,
				<if test="model.region != null and model.region != ''">
					df.area_code  as code
				</if>
				<if test="model.region == null or model.region == ''">
					df.province_code  as code
				</if>
			from
			(
				select
					df.*,
					province_code ,
					area_code
				from
					date_filter df,
					(
						select
							province_code ,
							area_code
						from
							tc_province_area a
					) pa
				group by
				province_code,
				df.startDate_r
			)df
			left join
			(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores"/>
			) t on t.dateStr = df.startDate_r and t.province_code = df.province_code
			group by df.startDate_r
			having dateUnit &lt;&gt; endDate_rc
			order by df.startDate_r desc
		)vv
	</select>




	<select id="analysisTrendAreaSum2"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			code ,
			level,
			ifnull(avg_scores, 0) as avg_scores,
			ifnull(avg_scores_c, 0) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_6"/>
			select
				code ,
				2 as level,
				ifnull( format(avg(avg_scores), 2 ), 0) as avg_scores,
				format( ifnull(avg(avg_scores), 0) -  ifnull(avg(avg_scoresC), 0) , 2 )as avg_scores_c,
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
			from(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_scores_data_chian"/>
				select
					dateStr,
					code,
					level,
					format(avg(avg_scores) ,2) as avg_scores,
					format(avg(avg_scoresC) ,2) as avg_scoresC
				from
					core_data
				group by code, dateStr
			)v
			group by dateUnit, code
		)vv
		group by dateUnit
		order by dateUnit desc
    </select>





	<select id="analysisTrendProvince"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			code ,
			3 as level,
			ifnull(avg_scores, 0) as avg_scores,
			ifnull(avg_scores_c, 0) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_6"/>
			select
				df.endDate_rc,
				df.startDate_r as dateUnit,
				replace(format(ifnull(avg(avg_scores), 0), 2), ',', '') as avg_scores,
				replace(format(ifnull(lag(avg(avg_scores)) over(partition by dateUnit ), 0), 2), ',', '') as avg_scores_c,
				replace(format(ifnull(sum(detractor), 0), 0), ',', '') as detractor,
				replace(format(ifnull(sum(detractor), 0) - ifnull(lag(sum(detractor)) over(partition by dateUnit ), 0) ,0), ',', '') as detractor_c,
				replace(format(ifnull(sum(neutral), 0), 0), ',', '') as neutral,
				replace(format(ifnull(sum(neutral), 0) - ifnull(lag(sum(neutral)) over(partition by dateUnit ), 0) ,0), ',', '') as neutral_c,
				replace(format(ifnull(sum(recommend), 0), 0), ',', '') as recommend,
				replace(format(ifnull(sum(recommend), 0) - ifnull(lag(sum(recommend)) over(partition by dateUnit ), 0) ,0), ',', '') as recommend_c,
				<if test="model.region != null and model.region != ''">
					df.area_code  as code
				</if>
				<if test="model.region == null or model.region == ''">
					df.province_code  as code
				</if>
			from
			(
				select
					df.*,
					province_code ,
					area_code
				from
					date_filter df,
					(
						select
							province_code ,
							area_code
						from
							tc_province_area a
						<if test="model.province != null and model.province.size()>0">
							where province_code in
							<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
								#{item}
							</foreach>
						</if>
					) pa
				group by
					province_code,
					df.startDate_r
			)df
			left join
			(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_scores"/>
			) t on t.dateStr = df.startDate_r and t.province_code = df.province_code
			group by df.startDate_r,code
			having dateUnit &lt;&gt; endDate_rc
			order by df.startDate_r desc
		)vv
	</select>

	<select id="analysisTrendProvince2"  resultType="com.car.voc.vo.NPSAnalysisTrendVo">
		select
			code ,
			level,
			ifnull(avg_scores, 0) as avg_scores,
			ifnull(avg_scores_c, 0) as avg_scores_c,
			0 as national_ranking,
			dateUnit
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_6"/>
			select
				code ,
				3 as level,
				format( ifnull(avg(avg_scores), 0), 2 ) as avg_scores,
				format( ifnull(avg(avg_scores), 0) -  ifnull(avg(avg_scoresC), 0) , 2 )as avg_scores_c,
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
			from(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_scores_data_chian"/>
				select
					dateStr,
					code,
					level,
					format(avg(avg_scores) ,2) as avg_scores,
					format(avg(avg_scoresC) ,2) as avg_scoresC
				from
				core_data
				group by code, dateStr
			)v
			group by dateUnit, code
		)vv
		group by dateUnit, code
		order by dateUnit desc
    </select>





    <!-- 车系及情感分析 /  调研车系分布 / NPS 维度分析 -->
    <select id="carSeriesEmotionDistribution1"  resultType="com.car.voc.vo.NPSCarEmotionVo">
		select
			car_series_code as car_series,
            format(total, 0) as total,
            replace(format((positive - negative) / (positive + negative) * 100 ,0 ), ',', '') as netPromoter,
			positive ,
			neutral,
			negative,
			positive1,
			neutral1,
			negative1,
            positive_c,
            neutral_c,
            negative_c,
			positiveP,
			neutralP,
			negativeP
		from
		(
            <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_1"/>
			select
				car_series_code,
				replace(format(total, 0), ',', '') as total,
				replace(format(positive, 0), ',', '') as positive ,
				replace(format(neutral, 0), ',', '') as neutral,
				replace(format(negative, 0), ',', '') as negative,
				positive1,
				neutral1,
				negative1,
                replace(positive_c, ',', '') as positive_c,
                replace(neutral_c, ',', '') as neutral_c,
                replace(negative_c, ',', '') as negative_c,
				replace(format((positive / total) * 100, 2), ',', '') as   positiveP,
				replace(format((neutral / total) * 100, 2), ',', '')  as neutralP,
				replace(format((negative / total) * 100, 2), ',', '')  as negativeP
			from
			(
				select
					c1.car_series_code,
					c1.positive + c1.neutral + c1.negative as total,
					c1.positive,
					c1.neutral,
					c1.negative,
					if(c2.positive is null ,0,c2.positive ) as positive1,
					if(c2.neutral is null,0, c2.neutral ) as neutral1,
					if(c2.negative is null , 0, c2.negative) as negative1,
					case
						when c1.positive is null and c2.positive is null   then format(0, 2)
						when c1.positive is null and c2.positive is not null then format(-c2.positive *  100, 2)
						when c1.positive is not null and c2.positive is null then format(c1.positive *  100, 2)
						when c1.positive is not null and c2.positive is not null then format((c1.positive - c2.positive) / c2.positive*  100 , 2)
					end as positive_c,
					case
						when c1.neutral is null and c2.neutral is null   then format(0, 2)
						when c1.neutral is null and c2.neutral is not null then format(-c2.neutral *  100, 2)
						when c1.neutral is not null and c2.neutral is null then format(c1.neutral *  100, 2)
						when c1.neutral is not null and c2.neutral is not null then format((c1.neutral - c2.neutral) / c2.neutral*  100 , 2)
					end as neutral_c,
					case
						when c1.negative is null and c2.negative is null   then format(0, 2)
						when c1.negative is null and c2.negative is not null then format(-c2.negative *  100, 2)
						when c1.negative is not null and c2.negative is null then format(c1.negative *  100, 2)
						when c1.negative is not null and c2.negative is not null then format((c1.negative - c2.negative) / c2.negative*  100 , 2)
					end as negative_c
				from (
					select
						car_series_code,
						format(avg(scores), 2) as avg_scores,
                        sum( CASE dimension_emotion WHEN '正面' THEN STATISTIC ELSE 0 END ) AS positive,
						sum( CASE dimension_emotion WHEN '负面' THEN STATISTIC ELSE 0 END ) AS negative,
						sum( CASE dimension_emotion WHEN '中性' THEN STATISTIC ELSE 0 END ) AS neutral
					from
						date_filter df ,
						tf_dws_voc_nps_record t
					where
						 car_series_code is not null and car_series_code != ''
						 and df.startDate = date(publish_date)
						and date(publish_date) between date(df.startDate) and date(df.endDate)
                        <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                        <if test="model.province != null and model.province.size()>0">
                            and province_code in
                            <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                        </if>
					group by
						car_series_code
				) c1
				left join
				(
					select
						car_series_code,
						format(avg(scores), 2) as avg_scores,
                        sum( CASE dimension_emotion WHEN '正面' THEN STATISTIC ELSE 0 END ) AS positive,
						sum( CASE dimension_emotion WHEN '负面' THEN STATISTIC ELSE 0 END ) AS negative,
						sum( CASE dimension_emotion WHEN '中性' THEN STATISTIC ELSE 0 END ) AS neutral
					from
						tf_dws_voc_nps_record t
					where
						 car_series_code is not null and car_series_code != ''
                        and date(publish_date) between
                        DATE_SUB( date(#{model.startDate}) , interval abs(DATEDIFF( date(#{model.startDate}), date(#{model.endDate}) )) +1  day )
                        and DATE_SUB( date(#{model.startDate}) , interval 1 day)
					    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                        <if test="model.province != null and model.province.size()>0">
                            and province_code in
                            <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                        </if>
					group by
						car_series_code
				) c2 on c1.car_series_code = c2.car_series_code
			) f1
		)vv
    </select>



    <!-- 车系及情感分析 /  调研车系分布 / 1:NPS得分 -->
    <select id="carSeriesScoreDistribution"  resultType="com.car.voc.vo.NPSCarScoreVo">
		select
			code as car_series,
			user_total as total,
			detractor,
			detractor_c as detractorC,
			detractorP,
			neutral,
			neutral_c as neutralC,
			neutralP,
			recommend,
			recommendP,
			recommend_c as recommendC,
			replace(format(ifnull((recommend - detractor) / (detractor + neutral + recommend) * 100, 0), 2), ',', '') as netPromoter,
			replace(format(ifnull((recommend - detractor) / (detractor + neutral + recommend) * 100, 0), 2), ',', '') as totalRecommendP
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			select
				user_total,
				code,
				detractor,
				replace(format(ifnull(detractor / scores_total * 100, 0), 2), ',', '') as detractorP,
				case
					when detractor_c = 0 and detractor != 0  then '999999'
					when detractor = 0 and detractor_c = 0  then format(0, 2)
					when detractor = 0 and detractor_c != 0 then replace(format(ifnull(-detractor_c * 100, 0), 2), ',', '')
					when detractor != 0 and detractor_c = 0 then replace(format(ifnull(detractor  * 100, 0), 2), ',', '')
					when detractor != 0 and detractor_c != 0 then replace(format(ifnull((detractor - detractor_c) / detractor_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as detractor_c,
				neutral,
				replace(format(ifnull(neutral / scores_total * 100, 0), 2), ',', '') as neutralP,
				case
					when neutral_c = 0 and neutral != 0  then '999999'
					when neutral = 0 and neutral_c = 0  then format(0, 2)
					when neutral = 0 and neutral_c != 0 then replace(format(ifnull(-neutral_c * 100, 0), 2), ',', '')
					when neutral != 0 and neutral_c = 0 then replace(format(ifnull(neutral  * 100, 0), 2), ',', '')
					when neutral != 0 and neutral_c != 0 then replace(format(ifnull((neutral - neutral_c) / neutral_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as neutral_c,
				recommend,
				replace(format(ifnull(recommend / scores_total * 100, 0), 2), ',', '') as recommendP,
				case
					when recommend_c = 0 and recommend != 0  then '999999'
					when recommend = 0 and recommend_c = 0  then format(0, 2)
					when recommend = 0 and recommend_c != 0 then replace(format(ifnull(-recommend_c * 100, 0), 2), ',', '')
					when recommend != 0 and recommend_c = 0 then replace(format(ifnull(recommend  * 100, 0), 2), ',', '')
					when recommend != 0 and recommend_c != 0 then replace(format(ifnull((recommend - recommend_c) / recommend_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as recommend_c,
				positive,
				replace(format(ifnull(positive / emotion_total * 100, 0), 2), ',', '') as positiveP,
				case
					when positive_c = 0 and positive != 0  then '999999'
					when positive = 0 and positive_c = 0  then format(0, 2)
					when positive = 0 and positive_c != 0 then replace(format(ifnull(-positive_c * 100, 0), 2), ',', '')
					when positive != 0 and positive_c = 0 then replace(format(ifnull(positive  * 100, 0), 2), ',', '')
					when positive != 0 and positive_c != 0 then replace(format(ifnull((positive - positive_c) / positive_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as positive_c,
				neutral2,
				replace(format(ifnull(neutral2 / emotion_total * 100, 0), 2), ',', '') as neutral2P,
				case
					when neutral2_c = 0 and neutral2 != 0  then '999999'
					when neutral2 = 0 and neutral2_c = 0  then format(0, 2)
					when neutral2 = 0 and neutral2_c != 0 then replace(format(ifnull(-neutral2_c * 100, 0), 2), ',', '')
					when neutral2 != 0 and neutral2_c = 0 then replace(format(ifnull(neutral2  * 100, 0), 2), ',', '')
					when neutral2 != 0 and neutral2_c != 0 then replace(format(ifnull((neutral2 - neutral2_c) / neutral2_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as neutral2_c,
				negative,
				replace(format(ifnull(negative / emotion_total * 100, 0), 2), ',', '') as negativeP,
				case
				when negative_c = 0 and negative != 0  then '999999'
					when negative = 0 and negative_c = 0  then format(0, 2)
					when negative = 0 and negative_c != 0 then replace(format(ifnull(-negative_c * 100, 0), 2), ',', '')
					when negative != 0 and negative_c = 0 then replace(format(ifnull(negative  * 100, 0), 2), ',', '')
					when negative != 0 and negative_c != 0 then replace(format(ifnull((negative - negative_c) / negative_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as negative_c,
				consult,
				replace(format(ifnull(consult / intention_total * 100, 0), 2), ',', '') as consultP,
				case
					when consult_c = 0 and consult != 0  then '999999'
					when consult = 0 and consult_c = 0  then format(0, 2)
					when consult = 0 and consult_c != 0 then replace(format(ifnull(-consult_c * 100, 0), 2), ',', '')
					when consult != 0 and consult_c = 0 then replace(format(ifnull(consult  * 100, 0), 2), ',', '')
					when consult != 0 and consult_c != 0 then replace(format(ifnull((consult - consult_c) / consult_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as consult_c,
				complaint,
				replace(format(ifnull(complaint / intention_total * 100, 0), 2), ',', '') as complaintP,
				case
					when complaint_c = 0 and complaint != 0  then '999999'
					when complaint = 0 and complaint_c = 0  then format(0, 2)
					when complaint = 0 and complaint_c != 0 then replace(format(ifnull(-complaint_c * 100, 0), 2), ',', '')
					when complaint != 0 and complaint_c = 0 then replace(format(ifnull(complaint  * 100, 0), 2), ',', '')
					when complaint != 0 and complaint_c != 0 then replace(format(ifnull((complaint - complaint_c) / complaint_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as complaint_c,
				praise,
				replace(format(ifnull(complain / intention_total * 100, 0), 2), ',', '') as praiseP,
				case
					when complain_c = 0 and complain != 0  then '999999'
					when complain = 0 and complain_c = 0  then format(0, 2)
					when complain = 0 and complain_c != 0 then replace(format(ifnull(-complain_c * 100, 0), 2), ',', '')
					when complain != 0 and complain_c = 0 then replace(format(ifnull(complain  * 100, 0), 2), ',', '')
					when complain != 0 and complain_c != 0 then replace(format(ifnull((complain - complain_c) / complain_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as complain_c,
				suggest,
				replace(format(ifnull(suggest / intention_total * 100, 0), 2), ',', '') as suggestP,
				case
					when suggest_c = 0 and suggest != 0  then '999999'
					when suggest = 0 and suggest_c = 0  then format(0, 2)
					when suggest = 0 and suggest_c != 0 then replace(format(ifnull(-suggest_c * 100, 0), 2), ',', '')
					when suggest != 0 and suggest_c = 0 then replace(format(ifnull(suggest  * 100, 0), 2), ',', '')
					when suggest != 0 and suggest_c != 0 then replace(format(ifnull((suggest - suggest_c) / suggest_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as suggest_c,
				other,
				replace(format(ifnull(other / intention_total * 100, 0), 2), ',', '') as otherP,
				case
					when other_c = 0 and other != 0  then '999999'
					when other = 0 and other_c = 0  then format(0, 2)
					when other = 0 and other_c != 0 then replace(format(ifnull(-other_c * 100, 0), 2), ',', '')
					when other != 0 and other_c = 0 then replace(format(ifnull(other  * 100, 0), 2), ',', '')
					when other != 0 and other_c != 0 then replace(format(ifnull((other - other_c) / other_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as other_c,
				scores_total,
				emotion_total,
				intention_total
			from (

				select
					user_total,
					c1.code,
					ifnull(c1.detractor ,0) as  detractor,
					ifnull(c2.detractor ,0) as detractor_c,
					ifnull(c1.neutral ,0) as neutral,
					ifnull(c2.neutral ,0) as neutral_c,
					ifnull(c1.recommend ,0) as recommend,
					ifnull(c2.recommend ,0) as recommend_c,
					ifnull(c1.positive ,0) as positive,
					ifnull(c2.positive ,0) as positive_c,
					ifnull(c1.neutral2 ,0) as neutral2,
					ifnull(c2.neutral2 ,0) as neutral2_c,
					ifnull(c1.negative ,0) as negative,
					ifnull(c2.negative ,0) as negative_c,
					ifnull(c1.consult ,0) as consult,
					ifnull(c2.consult ,0) as consult_c,
					ifnull(c1.complaint ,0)as complaint,
					ifnull(c2.complaint ,0) as complaint_c,
					ifnull(c1.complain ,0)as praise,
					ifnull(c2.complain ,0) as complain_c,
					ifnull(c1.suggest ,0) as suggest,
					ifnull(c2.suggest ,0) as suggest_c,
					ifnull(c1.other ,0) as other,
					ifnull(c2.other ,0) as other_c,
					(ifnull(c1.detractor ,0) + ifnull(c1.neutral ,0) + ifnull(c1.recommend ,0) ) as scores_total,
					(ifnull(c1.positive ,0) + ifnull(c1.neutral2 ,0) + ifnull(c1.negative ,0) ) as emotion_total,
					(ifnull(c1.consult ,0) + ifnull(c1.complaint ,0) + ifnull(c1.complain ,0) + ifnull(c1.suggest ,0) + ifnull(c1.other ,0) ) as intention_total
				from
				(
					select
						sum(user_total) as user_total,
						df.startDate_r as dateUnit,
						code,
						sum(detractor_dis) as detractor,
						sum(neutral_dis) as neutral,
						sum(recommend_dis) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
					date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries_distinct"/>
					)t on t.dateStr = df.startDate_r
					group by code
					order by dateUnit desc
				)c1
				left join
				(
					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor_dis) as detractor,
						sum(neutral_dis) as neutral,
						sum(recommend_dis) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
					date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries_chian_distinct"/>
					)t on t.dateStr = df.startDate_rc
					group by code
					order by dateUnit desc
				)c2 on c1.code = c2.code
			)v1
		) vv
		where code is not null
		order by total desc
	 </select>


	  <select id="carSeriesEmotionDistribution"  resultType="com.car.voc.vo.NPSCarEmotionVo">
			select
				code as car_series,
				user_total as total,
				positive,
				positive_c as positiveC,
				positiveP,
				neutral2 as neutral,
				neutral2_c as neutralC,
				neutral2P as neutralP,
				negative,
				negativeP,
				negative_c as negativeC,
				replace(format(ifnull((positive - negative) / (positive + negative) * 100, 0),2), ',', '') as netPromoter,
				replace(format(ifnull((positive - negative) / (positive + negative) * 100, 0),2), ',', '') as totalRecommendP
			from (
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
				select
					user_total,
					code,
					detractor,
					replace(format(ifnull(detractor / scores_total * 100, 0), 2), ',', '') as detractorP,
					case
						when detractor_c = 0 and detractor != 0  then '999999'
						when detractor = 0 and detractor_c = 0  then format(0, 2)
						when detractor = 0 and detractor_c != 0 then replace(format(ifnull(-detractor_c * 100, 0), 2), ',', '')
						when detractor != 0 and detractor_c = 0 then replace(format(ifnull(detractor  * 100, 0), 2), ',', '')
						when detractor != 0 and detractor_c != 0 then replace(format(ifnull((detractor - detractor_c) / detractor_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as detractor_c,
					neutral,
					replace(format(ifnull(neutral / scores_total * 100, 0), 2), ',', '') as neutralP,
					case
						when neutral_c = 0 and neutral != 0  then '999999'
						when neutral = 0 and neutral_c = 0  then format(0, 2)
						when neutral = 0 and neutral_c != 0 then replace(format(ifnull(-neutral_c * 100, 0), 2), ',', '')
						when neutral != 0 and neutral_c = 0 then replace(format(ifnull(neutral  * 100, 0), 2), ',', '')
						when neutral != 0 and neutral_c != 0 then replace(format(ifnull((neutral - neutral_c) / neutral_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as neutral_c,
					recommend,
					replace(format(ifnull(recommend / scores_total * 100, 0), 2), ',', '') as recommendP,
					case
						when recommend_c = 0 and recommend != 0  then '999999'
						when recommend = 0 and recommend_c = 0  then format(0, 2)
						when recommend = 0 and recommend_c != 0 then replace(format(ifnull(-recommend_c * 100, 0), 2), ',', '')
						when recommend != 0 and recommend_c = 0 then replace(format(ifnull(recommend  * 100, 0), 2), ',', '')
						when recommend != 0 and recommend_c != 0 then replace(format(ifnull((recommend - recommend_c) / recommend_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as recommend_c,
					positive,
					replace(format(ifnull(positive / emotion_total * 100, 0), 2), ',', '') as positiveP,
					case
						when positive_c = 0 and positive != 0  then '999999'
						when positive = 0 and positive_c = 0  then format(0, 2)
						when positive = 0 and positive_c != 0 then replace(format(ifnull(-positive_c * 100, 0), 2), ',', '')
						when positive != 0 and positive_c = 0 then replace(format(ifnull(positive  * 100, 0), 2), ',', '')
						when positive != 0 and positive_c != 0 then replace(format(ifnull((positive - positive_c) / positive_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as positive_c,
					neutral2,
					replace(format(ifnull(neutral2 / emotion_total * 100, 0), 2), ',', '') as neutral2P,
					case
						when neutral2_c = 0 and neutral2 != 0  then '999999'
						when neutral2 = 0 and neutral2_c = 0  then format(0, 2)
						when neutral2 = 0 and neutral2_c != 0 then replace(format(ifnull(-neutral2_c * 100, 0), 2), ',', '')
						when neutral2 != 0 and neutral2_c = 0 then replace(format(ifnull(neutral2  * 100, 0), 2), ',', '')
						when neutral2 != 0 and neutral2_c != 0 then replace(format(ifnull((neutral2 - neutral2_c) / neutral2_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as neutral2_c,
					negative,
					replace(format(ifnull(negative / emotion_total * 100, 0), 2), ',', '') as negativeP,
					case
						when negative_c = 0 and negative != 0  then '999999'
						when negative = 0 and negative_c = 0  then format(0, 2)
						when negative = 0 and negative_c != 0 then replace(format(ifnull(-negative_c * 100, 0), 2), ',', '')
						when negative != 0 and negative_c = 0 then replace(format(ifnull(negative  * 100, 0), 2), ',', '')
						when negative != 0 and negative_c != 0 then replace(format(ifnull((negative - negative_c) / negative_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as negative_c,
					consult,
					replace(format(ifnull(consult / intention_total * 100, 0), 2), ',', '') as consultP,
					case
						when consult_c = 0 and consult != 0  then '999999'
						when consult = 0 and consult_c = 0  then format(0, 2)
						when consult = 0 and consult_c != 0 then replace(format(ifnull(-consult_c * 100, 0), 2), ',', '')
						when consult != 0 and consult_c = 0 then replace(format(ifnull(consult  * 100, 0), 2), ',', '')
						when consult != 0 and consult_c != 0 then replace(format(ifnull((consult - consult_c) / consult_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as consult_c,
					complaint,
					replace(format(ifnull(complaint / intention_total * 100, 0), 2), ',', '') as complaintP,
					case
						when complaint_c = 0 and complaint != 0  then '999999'
						when complaint = 0 and complaint_c = 0  then format(0, 2)
						when complaint = 0 and complaint_c != 0 then replace(format(ifnull(-complaint_c * 100, 0), 2), ',', '')
						when complaint != 0 and complaint_c = 0 then replace(format(ifnull(complaint  * 100, 0), 2), ',', '')
						when complaint != 0 and complaint_c != 0 then replace(format(ifnull((complaint - complaint_c) / complaint_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as complaint_c,
					praise,
					replace(format(ifnull(complain / intention_total * 100, 0), 2), ',', '') as praiseP,
					case
						when complain_c = 0 and complain != 0  then '999999'
						when complain = 0 and complain_c = 0  then format(0, 2)
						when complain = 0 and complain_c != 0 then replace(format(ifnull(-complain_c * 100, 0), 2), ',', '')
						when complain != 0 and complain_c = 0 then replace(format(ifnull(complain  * 100, 0), 2), ',', '')
						when complain != 0 and complain_c != 0 then replace(format(ifnull((complain - complain_c) / complain_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as complain_c,
					suggest,
					replace(format(ifnull(suggest / intention_total * 100, 0), 2), ',', '') as suggestP,
					case
						when suggest_c = 0 and suggest != 0  then '999999'
						when suggest = 0 and suggest_c = 0  then format(0, 2)
						when suggest = 0 and suggest_c != 0 then replace(format(ifnull(-suggest_c * 100, 0), 2), ',', '')
						when suggest != 0 and suggest_c = 0 then replace(format(ifnull(suggest  * 100, 0), 2), ',', '')
						when suggest != 0 and suggest_c != 0 then replace(format(ifnull((suggest - suggest_c) / suggest_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as suggest_c,
					other,
					replace(format(ifnull(other / intention_total * 100, 0), 2), ',', '') as otherP,
					case
						when other_c = 0 and other != 0  then '999999'
						when other = 0 and other_c = 0  then format(0, 2)
						when other = 0 and other_c != 0 then replace(format(ifnull(-other_c * 100, 0), 2), ',', '')
						when other != 0 and other_c = 0 then replace(format(ifnull(other  * 100, 0), 2), ',', '')
						when other != 0 and other_c != 0 then replace(format(ifnull((other - other_c) / other_c * 100, 0), 2), ',', '')
						else format(0,2)
					end as other_c,
					scores_total,
					emotion_total,
					intention_total
				from (

					select
						user_total,
						c1.code,
						ifnull(c1.detractor ,0) as  detractor,
						ifnull(c2.detractor ,0) as detractor_c,
						ifnull(c1.neutral ,0) as neutral,
						ifnull(c2.neutral ,0) as neutral_c,
						ifnull(c1.recommend ,0) as recommend,
						ifnull(c2.recommend ,0) as recommend_c,
						ifnull(c1.positive ,0) as positive,
						ifnull(c2.positive ,0) as positive_c,
						ifnull(c1.neutral2 ,0) as neutral2,
						ifnull(c2.neutral2 ,0) as neutral2_c,
						ifnull(c1.negative ,0) as negative,
						ifnull(c2.negative ,0) as negative_c,
						ifnull(c1.consult ,0) as consult,
						ifnull(c2.consult ,0) as consult_c,
						ifnull(c1.complaint ,0)as complaint,
						ifnull(c2.complaint ,0) as complaint_c,
						ifnull(c1.complain ,0)as praise,
						ifnull(c2.complain ,0) as complain_c,
						ifnull(c1.suggest ,0) as suggest,
						ifnull(c2.suggest ,0) as suggest_c,
						ifnull(c1.other ,0) as other,
						ifnull(c2.other ,0) as other_c,
						(ifnull(c1.detractor ,0) + ifnull(c1.neutral ,0) + ifnull(c1.recommend ,0) ) as scores_total,
						(ifnull(c1.positive ,0) + ifnull(c1.neutral2 ,0) + ifnull(c1.negative ,0) ) as emotion_total,
						(ifnull(c1.consult ,0) + ifnull(c1.complaint ,0) + ifnull(c1.complain ,0) + ifnull(c1.suggest ,0) + ifnull(c1.other ,0) ) as intention_total
					from
					(
						select
							sum(user_total) as user_total,
							df.startDate_r as dateUnit,
							code,
							sum(detractor) as detractor,
							sum(neutral) as neutral,
							sum(recommend) as recommend,
							sum(positive_dis) as positive,
							sum(negative_dis) as negative,
							sum(neutral2_dis) as neutral2,
							sum(consult) as consult,
							sum(complaint) as complaint,
							sum(complain) as praise,
							sum(suggest) as suggest,
							sum(other) as other
						from
						date_filter df
						left join (
							<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries_distinct"/>
						)t on t.dateStr = df.startDate_r
						group by code
						order by dateUnit desc
					)c1
					left join
					(
						select
							df.startDate_r as dateUnit,
							code,
							sum(detractor) as detractor,
							sum(neutral) as neutral,
							sum(recommend) as recommend,
							sum(positive_dis) as positive,
							sum(negative_dis) as negative,
							sum(neutral2_dis) as neutral2,
							sum(consult) as consult,
							sum(complaint) as complaint,
							sum(complain) as praise,
							sum(suggest) as suggest,
							sum(other) as other
						from
						date_filter df
						left join (
							<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries_chian_distinct"/>
						)t on t.dateStr = df.startDate_rc
						group by code
						order by dateUnit desc
					)c2 on c1.code = c2.code
				)v1
			) vv
			where code is not null
			order by total desc
	  </select>

	  <select id="carSeriesEmotionDistribution2"  resultType="com.car.voc.vo.NPSCarScoreVo">
		select
			code as car_series,
			emotion_total as total,
			positive ,
			positive_c as positiveC,
			positiveP,
			neutral2 as neutral,
			neutral2_c as neutralC,
			neutral2P as neutralP,
			negative,
			negative_c as negativeC,
			negativeP,
			replace(format((positive - negative) / (positive + negative) * 100 ,0 ), ',', '') as netPromoter,
			replace(format(ifnull(recommend / scores_total * 100, 0),2), ',', '') as totalRecommendP
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			select
				code,
				format(detractor ,0) as detractor,
				replace(format(ifnull(detractor / scores_total * 100, 0),2), ',', '') as detractorP,
				replace(format(ifnull((detractor - detractor_c ) / detractor_c * 100 , 0),2), ',', '') as detractor_c,
				format(neutral ,0) as neutral,
				replace(format(ifnull(neutral / scores_total * 100, 0),2), ',', '') as neutralP,
				replace(format(ifnull((neutral - neutral_c ) / neutral_c * 100 , 0),2), ',', '') as neutral_c,
				format(recommend ,0) as recommend,
				replace(format(ifnull(recommend / scores_total * 100, 0),2), ',', '') as recommendP,
				replace(format(ifnull((recommend - recommend_c ) / recommend_c * 100 , 0),2), ',', '') as recommend_c,
				replace(format(ifnull(scores_total, 0),0), ',', '') as scores_total,
				format(positive ,0) as positive,
				replace(format(ifnull(positive / emotion_total * 100, 0),2), ',', '') as positiveP,
				replace(format(ifnull((positive - positive_c ) / positive_c * 100 , 0),2), ',', '') as positive_c,
				format(neutral2 ,0) as neutral2,
				replace(format(ifnull(neutral2 / emotion_total * 100, 0),2), ',', '') as neutral2P,
				replace(format(ifnull((neutral2 - neutral2_c ) / neutral2_c * 100 , 0),2), ',', '') as neutral2_c,
				format(negative ,0) as negative,
				replace(format(ifnull(negative / emotion_total * 100, 0),2), ',', '') as negativeP,
				replace(format(ifnull((negative - negative_c ) / negative_c * 100 , 0),2), ',', '') as negative_c,
				replace(format(ifnull(emotion_total, 0),0), ',', '') as emotion_total,
				format(consult ,0) as consult,
				replace(format(ifnull(consult / intention_total * 100, 0),2), ',', '') as consultP,
				replace(format(ifnull((consult - consult_c ) / consult_c * 100 , 0),2), ',', '') as consult_c,
				format(complaint ,0) as complaint,
				replace(format(ifnull(complaint / intention_total * 100, 0),2), ',', '') as complaintP,
				replace(format(ifnull((complaint - complaint_c ) / complaint_c * 100 , 0),2), ',', '') as complaint_c,
				format(complain ,0) as praise,
				replace(format(ifnull(complain / intention_total * 100, 0),2), ',', '') as praiseP,
				replace(format(ifnull((complain - complain_c ) / complain_c * 100 , 0),2), ',', '') as complain_c,
				format(suggest ,0) as suggest,
				replace(format(ifnull(suggest / intention_total * 100, 0),2), ',', '') as suggestP,
				replace(format(ifnull((suggest - suggest_c ) / suggest_c * 100 , 0),2), ',', '') as suggest_c,
				format(other ,0) as other,
				replace(format(ifnull(other / intention_total * 100, 0),2), ',', '') as otherP,
				replace(format(ifnull((other - other_c ) / other_c * 100 , 0),2), ',', '') as other_c,
				replace(format(ifnull(intention_total, 0),0), ',', '') as intention_total

			from (

				select
					c1.code,
					ifnull(c1.detractor ,0) as  detractor,
					ifnull(c2.detractor ,0) as detractor_c,
					ifnull(c1.neutral ,0) as neutral,
					ifnull(c2.neutral ,0) as neutral_c,
					ifnull(c1.recommend ,0) as recommend,
					ifnull(c2.recommend ,0) as recommend_c,
					ifnull(c1.positive ,0) as positive,
					ifnull(c2.positive ,0) as positive_c,
					ifnull(c1.neutral2 ,0) as neutral2,
					ifnull(c2.neutral2 ,0) as neutral2_c,
					ifnull(c1.negative ,0) as negative,
					ifnull(c2.negative ,0) as negative_c,
					ifnull(c1.consult ,0) as consult,
					ifnull(c2.consult ,0) as consult_c,
					ifnull(c1.complaint ,0)as complaint,
					ifnull(c2.complaint ,0) as complaint_c,
					ifnull(c1.complain ,0)as praise,
					ifnull(c2.complain ,0) as complain_c,
					ifnull(c1.suggest ,0) as suggest,
					ifnull(c2.suggest ,0) as suggest_c,
					ifnull(c1.other ,0) as other,
					ifnull(c2.other ,0) as other_c,
					(ifnull(c1.detractor ,0) + ifnull(c1.neutral ,0) + ifnull(c1.recommend ,0) ) as scores_total,
					(ifnull(c1.positive ,0) + ifnull(c1.neutral2 ,0) + ifnull(c1.negative ,0) ) as emotion_total,
					(ifnull(c1.consult ,0) + ifnull(c1.complaint ,0) + ifnull(c1.complain ,0) + ifnull(c1.suggest ,0) + ifnull(c1.other ,0) ) as intention_total
				from
				(
					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor) as detractor,
						sum(neutral) as neutral,
						sum(recommend) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
					date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries"/>
					)t on t.dateStr = df.startDate_r
					group by code,dateStr
					order by dateUnit desc
				)c1
				left join
				(

					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor) as detractor,
						sum(neutral) as neutral,
						sum(recommend) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
					date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries_chian"/>
					)t on t.dateStr = df.startDate_rc
					group by code,dateStr
					order by dateUnit desc
				)c2 on c1.code = c2.code
			)v1
		) vv
	 </select>

    <select id="carSeriesScoreDistribution2"  resultType="com.car.voc.vo.NPSCarScoreVo">
		select
			code as car_series,
			replace(format(recommend / total * 100 ,0 ), ',', '') as netPromoter,
			format(detractor,0) as detractor,
			case
				when detractor = 0 and detractorC = 0  then format(0, 2)
				when detractor = 0 and detractorC != 0 then replace(format(-detractorC * 100, 2), ',', '')
				when detractor != 0 and detractorC = 0 then replace(format(detractor  * 100 , 2), ',', '')
				when detractor != 0 and detractorC != 0 then replace(format( (detractor - detractorC) / detractorC * 100 , 2), ',', '')
			end as detractorC,
			replace(ifnull(format(detractor / total * 100 , 2), 0), ',', '') as detractorP,
			format(neutral,0) as neutral,
			case
				when neutral = 0 and neutralC = 0   then format(0, 2)
				when neutral = 0 and neutralC != 0 then replace(format(-neutralC * 100, 2), ',', '')
				when neutral != 0 and neutralC = 0 then replace(format( neutral * 100, 2), ',', '')
				when neutral != 0 and neutralC != 0 then replace(format( (neutral - neutralC)  /neutralC * 100  , 2), ',', '')
			end as neutralC,
			replace(ifnull(format(neutral / total * 100, 2), 0), ',', '') as neutralP,
			format(recommend,0) as recommend,
			case
				when recommend = 0 and recommendC = 0   then format(0, 2)
				when recommend = 0 and recommendC != 0 then replace(format(-recommendC* 100 , 2), ',', '')
				when recommend != 0 and recommendC = 0 then replace(format(recommend* 100 , 2), ',', '')
				when recommend != 0 and recommendC != 0 then replace(format((recommend - recommendC) / recommendC * 100  , 2), ',', '')
			end as recommendC,
			replace(ifnull(format(recommend / total * 100, 2), 0), ',', '') as recommendP,
			format(total,0) as total,
			replace(ifnull(format(recommend / total * 100 , 2), 0), ',', '') as totalRecommendP,
			dateUnit
		from
		(
			select
				code,
				ifnull(sum(detractor), 0) as detractor,
				ifnull(sum(detractorC), 0) as detractorC,
				ifnull(sum(neutral), 0) as neutral,
				ifnull(sum(neutralC), 0) as neutralC,
				ifnull(sum(recommend), 0) as recommend,
				ifnull(sum(recommendC), 0) as recommendC,
				ifnull( sum(detractor) + sum(neutral)  + sum(recommend) ,0) as total,
				dateUnit
			from
				(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with"/>
				select
					code ,
					ifnull(sum(detractor) ,0) as detractor,
					detractorC,
					ifnull(sum(neutral) ,0) as neutral,
					neutralC,
					ifnull(sum(recommend) ,0) as recommend,
					recommendC,
					(ifnull(sum(detractor) ,0) + ifnull(sum(neutral) ,0) + ifnull(sum(recommend) ,0)) as total,
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
				from
				(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_car_series_scores_data"/>
				select
					dateStr,
					code,
					ifnull(format(avg(avg_scores) , 2), 0) as avg_scores,
					ifnull(format(avg(avg_scoresC) , 2) , 0) as avg_scoresC,
					ifnull(format(sum(detractor) , 0) , 0) as detractor,
					ifnull(format(sum(detractorC) , 0) , 0) as detractorC,
					ifnull(format(sum(neutral) , 0), 0) as neutral,
					ifnull(format(sum(neutralC) , 0) , 0) as neutralC,
					ifnull(format(sum(recommend) , 0) , 0) as recommend,
					ifnull(format(sum(recommendC) , 0) , 0) as recommendC
				from
					core_data
				group by dateStr, code
				)vv
				group by dateStr, code
			)v2
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_2"/>
			group by dateUnit, code
		)vv
		where code is not null
	</select>



    <!-- 车系及情感分析 /  调研车系分布 / 情感趋势变化 -->
    <select id="carSeriesEmotionTrend2"  resultType="com.car.voc.vo.NPSCarEmotionTrendVo">
        select
            dateStr,
            replace(total, ',', '') as total,
            replace(positive, ',', '') as positive,
            replace(if(positiveP is null, 0, positiveP), ',', '') as positiveP,
            replace(format( if(positiveC = 0 , positive * 100 , ( positive - positiveC ) / positiveC * 100 ), 2), ',', '') as positiveC ,
            replace(negative, ',', '') as negative,
            replace(if(negativeP is null, 0, negativeP), ',', '') as negativeP,
            replace(format( if(negativeC = 0 , negative * 100, ( negative - negativeC ) / negativeC * 100 ), 2), ',', '') as negativeC ,
            replace(neutral, ',', '') as neutral,
            replace(if(neutralP is null, 0, neutralP), ',', '') as neutralP,
            replace(format( if(neutralC = 0 , neutral * 100, ( neutral - neutralC ) / neutralC * 100 ), 2), ',', '') as neutralC
        from
        (
            <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_6"/>
            select
                dateUnit as dateStr,
                total,
                positive,
                format((positive / total ) * 100, 2) as positiveP,
                format(lag(positive, 1, 0) over(order by dateUnit ), 2) as positiveC,
                negative,
                format((negative / total) * 100, 2) as negativeP,
                format(lag(negative, 1, 0) over(order by dateUnit ), 2) as negativeC,
                neutral,
                format((neutral / total ) * 100, 2) as neutralP,
                format(lag(neutral, 1, 0) over(order by dateUnit ), 2) as neutralC
            from
            (
                select
                    sum(positive) as positive,
                    sum(neutral) as neutral,
                    sum(negative) as negative,
                    sum(negative) + sum(negative) + sum(negative) as total,
                    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
                from(
                    select
                        c1.dateStr,
                        if(sum(positive) is null,
                        0,
                        sum(positive)) as positive,
                        if(sum(neutral) is null,
                        0,
                        sum(neutral)) as neutral,
                        if(sum(negative) is null,
                        0,
                        sum(negative)) as negative
                    from
                        (
                        select
                            df.startDate as dateStr
                        from
                            date_filter df )c1
                    left join
                    (
                    select
                        date(publish_date) as dateStr,
                        sum( CASE dimension_emotion WHEN '正面' THEN STATISTIC ELSE 0 END ) AS positive,
                        sum( CASE dimension_emotion WHEN '负面' THEN STATISTIC ELSE 0 END ) AS negative,
                        sum( CASE dimension_emotion WHEN '中性' THEN STATISTIC ELSE 0 END ) AS neutral
                    from
                        date_filter df,
                        tf_dws_voc_nps_record t,
                        (
                        select
                            province_code ,
                            area_code
                        from
                            tc_province_area a ) pa
                    where
                        dimension_emotion is not null
                        and df.startDate = date(publish_date)
                        and date(publish_date) between date(df.startDate) and date(df.endDate)
                        <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                        <if test="model.province != null and model.province.size()>0">
                            and t.province_code in
                            <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                        </if>
                        group by
                            dateStr
                    )c2 on
                    c1.dateStr = c2.dateStr
                    group by dateStr
                )f1
                group by dateUnit
                order by dateStr desc
            )c1
            order by dateStr desc
        )vv
    </select>

    <select id="carSeriesEmotionTrend"  resultType="com.car.voc.vo.NPSCarEmotionTrendVo">
		select
			dateUnit as dateStr,
			emotion_total as total,
			positive,
			positiveP,
			positive_c as positiveC,
			negative,
			negativeP,
			negative_c as negativeC,
			neutral2 as neutral,
			neutral2P as neutralP,
			neutral2_c as neutralC
		from
		(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_6"/>
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_emotion_intention_trend_common"/>
		)vv
	</select>

    <select id="carSeriesEmotionTrend1"  resultType="com.car.voc.vo.NPSCarEmotionTrendVo">
        select
            dateStr,
            replace(total, ',', '') as total,
            replace(positive, ',', '') as positive,
            replace(if(positiveP is null, 0, positiveP), ',', '') as positiveP,
            replace(format( if(positiveC = 0 , positive * 100 , ( positive - positiveC ) / positiveC * 100 ), 2), ',', '') as positiveC ,
            replace(negative, ',', '') as negative,
            replace(if(negativeP is null, 0, negativeP), ',', '') as negativeP,
            replace(format( if(negativeC = 0 , negative * 100, ( negative - negativeC ) / negativeC * 100 ), 2), ',', '') as negativeC ,
            replace(neutral, ',', '') as neutral,
            replace(if(neutralP is null, 0, neutralP), ',', '') as neutralP,
            replace(format( if(neutralC = 0 , neutral * 100, ( neutral - neutralC ) / neutralC * 100 ), 2), ',', '') as neutralC
        from (
            select
                dateUnit as dateStr,
                total,
                positive,
                format((positive / total ) * 100, 2) as positiveP,
                format(lag(positive, 1, 0) over(order by dateUnit ), 2) as positiveC,
                negative,
                format((negative / total) * 100, 2) as negativeP,
                format(lag(negative, 1, 0) over(order by dateUnit ), 2) as negativeC,
                neutral,
                format((neutral / total ) * 100, 2) as neutralP,
                format(lag(neutral, 1, 0) over(order by dateUnit ), 2) as neutralC
            from
                (
                <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_6"/>
                select
                    if(sum(total) is
                    null,
                    0,
                    sum(total)) as total,
                    if(sum(positive) is null,
                    0,
                    sum(positive)) as positive,
                    if(sum(negative) is null,
                    0,
                    sum(negative)) as negative,
                    if(sum(neutral) is null,
                    0,
                    sum(neutral)) as neutral,
                    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
                from
                (
                    select
                        positive,
                        negative,
                        neutral,
                        total,
                        c1.startDate as dateStr
                    from
                        date_filter c1
                    left join (
                        select
                            date (publish_date) as dateStr,
                            sum(STATISTIC) as total,
                            sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
                            sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
                            sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral
                        from
                            date_filter df,
                            tf_dws_voc_nps_record t
                        where
                            dimension_emotion is not null
                            and df.startDate = date(publish_date)
                            and date(publish_date) between date(df.startDate) and date(df.endDate)
                            <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                            <if test="model.province != null and model.province.size()>0">
                                and t.province_code in
                                <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                                    #{item}
                                </foreach>
                            </if>
                        group by  dateStr order by
                        dateStr desc
                    ) c2 on c1.startDate = c2.dateStr
                    group by c1.startDate
                ) g
                group by dateUnit
            ) vv
            order by dateStr
        )vv
    </select>

    <!-- 反馈聚焦分析 / 高频热词 -->
    <select id="focusAnalysisHotWords"  resultType="com.car.voc.vo.NPSHighHotWordsVo">
        SELECT
            if(keyword is null  , '', keyword) as keyword,
            replace(if(statistic is null  , '', statistic), ',', '') as statistic,
            replace(if(positive is null  , '', positive), ',', '') as positive,
            replace(if(neutral is null  , '', neutral), ',', '') as neutral,
            replace(if(negative is null  , '', negative), ',', '') as negative
        from
        (
            select
                t.EMOTION_KEYWORD as keyword,
                sum(STATISTIC) AS statistic,
                sum( CASE dimension_emotion WHEN '正面' THEN STATISTIC ELSE 0 END ) AS positive,
                sum( CASE dimension_emotion WHEN '负面' THEN STATISTIC ELSE 0 END ) AS negative,
                sum( CASE dimension_emotion WHEN '中性' THEN STATISTIC ELSE 0 END ) AS neutral
            from
                tf_dws_voc_nps_record t
            where
                t.EMOTION_KEYWORD is not null and t.EMOTION_KEYWORD != ''
                <if test="model.province != null and model.province.size()>0">
                    and t.province_code in
                    <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                and date(publish_date) between date(#{model.startDate})  and date(#{model.endDate})
                <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                <if test="model.emotion != null and model.emotion !=''">
                    AND dimension_emotion = #{model.emotion}
                </if>
            group by EMOTION_KEYWORD
            ORDER BY STATISTIC DESC
        ) t
        limit #{model.topn}
    </select>


    <!-- 反馈聚焦分析 / 聚焦分布  2: 情绪 -->
    <select id="focusDistributionEmotion"  resultType="com.car.voc.vo.NPSFousDistributionVo">
		select
			'' as dateStr,
			<if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
				code as first_dimension_code ,
			</if>
			<if test="model.dimensionRsLevel == 1">
				code as second_dimension_code ,
			</if>
			<if test="model.dimensionRsLevel == 2">
				code as three_dimension_code ,
			</if>
			<if test="model.dimensionRsLevel == 3">
				code as topic_code ,
			</if>
			emotion_total as total,
			positive,
			replace(format(ifnull(positive / emotion_total * 100, 0), 2), ',', '') as positiveP,
			positive_c as positiveC ,
			neutral2 as neutral,
			replace(format(ifnull(neutral2 / emotion_total * 100, 0), 2), ',', '') as neutralP,
			neutral2_c as neutralC ,
			negative,
			replace(format(ifnull(negative / emotion_total * 100, 0), 2), ',', '') as negativeP,
			negative_c as negativeC
		from
		(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			select
				code,
				case
					when detractor_c = 0 and detractor != 0  then '999999'
					when detractor = 0 and detractor_c = 0  then format(0, 2)
					when detractor = 0 and detractor_c != 0 then replace(format(ifnull(-detractor_c * 100, 0), 2), ',', '')
					when detractor != 0 and detractor_c = 0 then replace(format(ifnull(detractor  * 100, 0), 2), ',', '')
					when detractor != 0 and detractor_c != 0 then replace(format(ifnull((detractor - detractor_c) / detractor_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as detractor_c,
				neutral,
				case
					when neutral_c = 0 and neutral != 0  then '999999'
					when neutral = 0 and neutral_c = 0  then format(0, 2)
					when neutral = 0 and neutral_c != 0 then replace(format(ifnull(-neutral_c * 100, 0), 2), ',', '')
					when neutral != 0 and neutral_c = 0 then replace(format(ifnull(neutral  * 100, 0), 2), ',', '')
					when neutral != 0 and neutral_c != 0 then replace(format(ifnull((neutral - neutral_c) / neutral_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as neutral_c,
				recommend,
				case
					when recommend_c = 0 and recommend != 0  then '999999'
					when recommend = 0 and recommend_c = 0  then format(0, 2)
					when recommend = 0 and recommend_c != 0 then replace(format(ifnull(-recommend_c * 100, 0), 2), ',', '')
					when recommend != 0 and recommend_c = 0 then replace(format(ifnull(recommend  * 100, 0), 2), ',', '')
					when recommend != 0 and recommend_c != 0 then replace(format(ifnull((recommend - recommend_c) / recommend_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as recommend_c,
				positive,
				case
					when positive_c = 0 and positive != 0  then '999999'
					when positive = 0 and positive_c = 0  then format(0, 2)
					when positive = 0 and positive_c != 0 then replace(format(ifnull(-positive_c * 100, 0), 2), ',', '')
					when positive != 0 and positive_c = 0 then replace(format(ifnull(positive  * 100, 0), 2), ',', '')
					when positive != 0 and positive_c != 0 then replace(format(ifnull((positive - positive_c) / positive_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as positive_c,
				neutral2,
				case
					when neutral2 = 0 and neutral2_c != 0  then '999999'
					when neutral2 = 0 and neutral2_c = 0  then format(0, 2)
					when neutral2 = 0 and neutral2_c != 0 then replace(format(ifnull(-neutral2_c * 100, 0), 2), ',', '')
					when neutral2 != 0 and neutral2_c = 0 then replace(format(ifnull(neutral2  * 100, 0), 2), ',', '')
					when neutral2 != 0 and neutral2_c != 0 then replace(format(ifnull((neutral2 - neutral2_c) / neutral2_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as neutral2_c,
				negative,
				case
					when negative_c = 0 and negative != 0  then '999999'
					when negative = 0 and negative_c = 0  then format(0, 2)
					when negative = 0 and negative_c != 0 then replace(format(ifnull(-negative_c * 100, 0), 2), ',', '')
					when negative != 0 and negative_c = 0 then replace(format(ifnull(negative  * 100, 0), 2), ',', '')
					when negative != 0 and negative_c != 0 then replace(format(ifnull((negative - negative_c) / negative_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as negative_c,
				consult,
				case
					when consult_c = 0 and consult != 0  then '999999'
					when consult = 0 and consult_c = 0  then format(0, 2)
					when consult = 0 and consult_c != 0 then replace(format(ifnull(-consult_c * 100, 0), 2), ',', '')
					when consult != 0 and consult_c = 0 then replace(format(ifnull(consult  * 100, 0), 2), ',', '')
					when consult != 0 and consult_c != 0 then replace(format(ifnull((consult - consult_c) / consult_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as consult_c,
				complaint,
				case
					when complaint_c = 0 and complaint != 0  then '999999'
					when complaint = 0 and complaint_c = 0  then format(0, 2)
					when complaint = 0 and complaint_c != 0 then replace(format(ifnull(-complaint_c * 100, 0), 2), ',', '')
					when complaint != 0 and complaint_c = 0 then replace(format(ifnull(complaint  * 100, 0), 2), ',', '')
					when complaint != 0 and complaint_c != 0 then replace(format(ifnull((complaint - complaint_c) / complaint_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as complaint_c,
				praise,
				case
					when complain_c = 0 and complain != 0  then '999999'
					when complain = 0 and complain_c = 0  then format(0, 2)
					when complain = 0 and complain_c != 0 then replace(format(ifnull(-complain_c * 100, 0), 2), ',', '')
					when complain != 0 and complain_c = 0 then replace(format(ifnull(complain  * 100, 0), 2), ',', '')
					when complain != 0 and complain_c != 0 then replace(format(ifnull((complain - complain_c) / complain_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as complain_c,
				suggest,
				case
					when suggest_c = 0 and suggest != 0  then '999999'
					when suggest = 0 and suggest_c = 0  then format(0, 2)
					when suggest = 0 and suggest_c != 0 then replace(format(ifnull(-suggest_c * 100, 0), 2), ',', '')
					when suggest != 0 and suggest_c = 0 then replace(format(ifnull(suggest  * 100, 0), 2), ',', '')
					when suggest != 0 and suggest_c != 0 then replace(format(ifnull((suggest - suggest_c) / suggest_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as suggest_c,
				other,
				case
					when other_c = 0 and other != 0  then '999999'
					when other = 0 and other_c = 0  then format(0, 2)
					when other = 0 and other_c != 0 then replace(format(ifnull(-other_c * 100, 0), 2), ',', '')
					when other != 0 and other_c = 0 then replace(format(ifnull(other  * 100, 0), 2), ',', '')
					when other != 0 and other_c != 0 then replace(format(ifnull((other - other_c) / other_c * 100, 0), 2), ',', '')
					else format(0,2)
				end as other_c,
				scores_total,
				emotion_total,
				intention_total

			from (

				select
					c1.code,
					ifnull(c1.detractor ,0) as  detractor,
					ifnull(c2.detractor ,0) as detractor_c,
					ifnull(c1.neutral ,0) as neutral,
					ifnull(c2.neutral ,0) as neutral_c,
					ifnull(c1.recommend ,0) as recommend,
					ifnull(c2.recommend ,0) as recommend_c,
					ifnull(c1.positive ,0) as positive,
					ifnull(c2.positive ,0) as positive_c,
					ifnull(c1.neutral2 ,0) as neutral2,
					ifnull(c2.neutral2 ,0) as neutral2_c,
					ifnull(c1.negative ,0) as negative,
					ifnull(c2.negative ,0) as negative_c,
					ifnull(c1.consult ,0) as consult,
					ifnull(c2.consult ,0) as consult_c,
					ifnull(c1.complaint ,0)as complaint,
					ifnull(c2.complaint ,0) as complaint_c,
					ifnull(c1.complain ,0)as praise,
					ifnull(c2.complain ,0) as complain_c,
					ifnull(c1.suggest ,0) as suggest,
					ifnull(c2.suggest ,0) as suggest_c,
					ifnull(c1.other ,0) as other,
					ifnull(c2.other ,0) as other_c,
					(ifnull(c1.detractor ,0) + ifnull(c1.neutral ,0) + ifnull(c1.recommend ,0) ) as scores_total,
					(ifnull(c1.positive ,0) + ifnull(c1.neutral2 ,0) + ifnull(c1.negative ,0) ) as emotion_total,
					(ifnull(c1.consult ,0) + ifnull(c1.complaint ,0) + ifnull(c1.complain ,0) + ifnull(c1.suggest ,0) + ifnull(c1.other ,0) ) as intention_total
				from
				(
					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor) as detractor,
						sum(neutral) as neutral,
						sum(recommend) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
						date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_focus"/>
					)t on t.dateStr = df.startDate_r
					group by code
					order by dateUnit desc
				)c1
				left join
				(
					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor) as detractor,
						sum(neutral) as neutral,
						sum(recommend) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
						date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_focus_chian"/>
					)t on t.dateStr = df.startDate_rc
					group by code
					order by dateUnit desc
				)c2 on c1.code = c2.code
			)v1
		)vv
		order by (negative + positive) desc
	</select>

    <select id="focusDistributionEmotion1"  resultType="com.car.voc.vo.NPSFousDistributionVo">
		select
			dateStr,
			<if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
				code as first_dimension_code ,
			</if>
			<if test="model.dimensionRsLevel == 1">
				code as second_dimension_code ,
			</if>
			<if test="model.dimensionRsLevel == 2">
				code as three_dimension_code ,
			</if>
			<if test="model.dimensionRsLevel == 3">
				code as topic_code ,
			</if>
			replace(total, ',', '') as total,
			replace(positive, ',', '') as positive,
			replace(if(positiveP is null, 0, positiveP), ',', '') as positiveP,
			replace(format( if(positiveC = 0 , positive * 100 , ( positive - positiveC ) / positiveC * 100 ), 2), ',', '') as positiveC ,
			replace(negative, ',', '') as negative,
			replace(if(negativeP is null, 0, negativeP), ',', '') as negativeP,
			replace(format( if(negativeC = 0 , negative * 100, ( negative - negativeC ) / negativeC * 100 ), 2), ',', '') as negativeC ,
			replace(neutral, ',', '') as neutral,
			replace(if(neutralP is null, 0, neutralP), ',', '') as neutralP,
			replace(format( if(neutralC = 0 , neutral * 100, ( neutral - neutralC ) / neutralC * 100 ), 2), ',', '') as neutralC
		from
			(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_1"/>
			select
				dateUnit as dateStr,
				total,
				code,
				positive,
				format((positive / total ) * 100, 2) as positiveP,
				format(lag(positive, 1, 0) over(order by dateUnit ), 2) as positiveC,
				negative,
				format((negative / total) * 100, 2) as negativeP,
				format(lag(negative, 1, 0) over(order by dateUnit ), 2) as negativeC,
				neutral,
				format((neutral / total ) * 100, 2) as neutralP,
				format(lag(neutral, 1, 0) over(order by dateUnit ), 2) as neutralC
			from
				(
				select
					code,
					if(sum(total) is null,
					0,
					sum(total)) as total,
					if(sum(positive) is null,
					0,
					sum(positive)) as positive,
					if(sum(negative) is null,
					0,
					sum(negative)) as negative,
					if(sum(neutral) is null,
					0,
					sum(neutral)) as neutral,
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
				from
					(
					select
						positive,
						negative,
						neutral,
						total,
						code,
						c1.startDate as dateStr
					from
						date_filter c1
					left join (
						select
							date (publish_date) as dateStr,
							<if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
								first_dimension_code as code,
							</if>
							<if test="model.dimensionRsLevel == 1">
								second_dimension_code as code,
							</if>
							<if test="model.dimensionRsLevel == 2">
								three_dimension_code as code,
							</if>
							<if test="model.dimensionRsLevel == 3">
								topic_code as code,
							</if>
							sum(STATISTIC) as total,
							sum(case dimension_emotion when '正面' then STATISTIC else 0 end) as positive,
							sum(case dimension_emotion when '负面' then STATISTIC else 0 end) as negative,
							sum(case dimension_emotion when '中性' then STATISTIC else 0 end) as neutral
						from
							date_filter df,
							tf_dws_voc_nps_record t
						where
							dimension_emotion is not null
							and df.startDate = date(publish_date)
								and date(publish_date) between date(df.startDate) and date(df.endDate)
							<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
							<if test="model.province != null and model.province.size()>0">
								and province_code in
								<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
									#{item}
								</foreach>
							</if>
							<if test="model.tagType == 1 or  model.tagType == 2 ">
								and tag_type = #{model.tagType}
							</if>
							group by
								dateStr,
								code
							order by
								dateStr desc ) c2 on
						c1.startDate = c2.dateStr
					group by c1.startDate,code
				) g
				where code is not null
				group by dateUnit, code
			) vv
			order by dateStr
		)vv
	</select>
    <select id="focusDistributionEmotion2"  resultType="com.car.voc.vo.NPSFousDistributionVo">
        select
			topic_code,
			first_dimension_code,
			second_dimension_code,
			three_dimension_code,
			replace(format(if(positive is null, 0,positive ),2), ',', '')as positive,
			replace(format(if(positive_p is null, 0, positive_p ),2), ',', '') as positiveP,
			replace(if(positive_c is null , 0, positive_c), ',', '') as positiveC,
			replace(format(if(negative is null , 0, negative ),2), ',', '') as negative,
			replace(format(if(negative_p is null, 0, negative_p ),2), ',', '') as negativeP,
			replace(if(negative_c is null , 0, negative_c), ',', '') as negativeC,
			replace(format(if(neutral is null ,0, neutral ),2), ',', '') as neutral,
			replace(format(if(neutral_p is null , 0, neutral_p),2), ',', '') as neutralP,
			replace(if(neutral_c is null , 0, neutral_c), ',', '') as neutralC
		from (
			select
				l1.total,
				l1.positive,
				l2.positive as positive1,
				case
					when l1.positive is null and l2.positive is null   then format(0, 2)
					when l1.positive is null and l2.positive is not null then format(-l2.positive *  100, 2)
					when l1.positive is not null and l2.positive is null then format(l1.positive *  100, 2)
					when l1.positive is not null and l2.positive is not null then format((l1.positive - l2.positive) /l2.positive *  100 , 2)
				end as positive_c,
				if(l1.positive is null , 0, format( l1.positive / l1.total *100,2) ) as positive_p,
				l1.negative,
				l2.negative as negative1,
				case
					when l1.negative is null and l2.negative is null   then format(0, 2)
					when l1.negative is null and l2.negative is not null then format(-l2.negative *  100, 2)
					when l1.negative is not null and l2.negative is null then format(l1.negative *  100, 2)
					when l1.negative is not null and l2.negative is not null then format((l1.negative - l2.negative) /l2.negative *  100 , 2)
				end as negative_c,
				if(l1.negative is null , 0, format( l1.negative / l1.total *100,2) ) as negative_p,
				l1.neutral,
				l2.neutral as neutral1,
				case
					when l1.neutral is null and l2.neutral is null   then format(0, 2)
					when l1.neutral is null and l2.neutral is not null then format(-l2.neutral *  100, 2)
					when l1.neutral is not null and l2.neutral is null then format(l1.neutral *  100, 2)
					when l1.neutral is not null and l2.neutral is not null then format((l1.neutral - l2.neutral) /l2.neutral *  100 , 2)
				end as neutral_c,
				if(l1.neutral is null , 0, format( l1.neutral / l1.total *100,2) ) as neutral_p,
				l1.topic_code,
				l1.first_dimension_code,
				l1.second_dimension_code,
				l1.three_dimension_code
            from
            (
                <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_1"/>
                select
                    topic_code,
                    first_dimension_code,
                    second_dimension_code,
                    three_dimension_code,
                    dimension_emotion,
                    sum(STATISTIC) as total,
                    sum( case dimension_emotion when '正面' then STATISTIC else 0 end ) as positive,
                    sum( case dimension_emotion when '负面' then STATISTIC else 0 end ) as negative,
                    sum( case dimension_emotion when '中性' then STATISTIC else 0 end ) as neutral
                from
                    date_filter df,
                    tf_dws_voc_nps_record
                where
                    topic_code is not null
                    and dimension_emotion is not null
                    and df.startDate = date(publish_date)
                    and date(publish_date) between date(df.startDate) and date(df.endDate)
                    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                    <if test="model.province != null and model.province.size()>0">
                        and province_code in
                        <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
                <if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
                    group by first_dimension_code
                </if>
                <if test="model.dimensionRsLevel == 1">
                    group by second_dimension_code
                </if>
                <if test="model.dimensionRsLevel == 2">
                    group by three_dimension_code
                </if>
                <if test="model.dimensionRsLevel == 3">
                    group by topic_code
                </if>
                order by topic_code
            )l1
            left join
            (
                select
                    topic_code,
                    first_dimension_code,
                    second_dimension_code,
                    three_dimension_code,
                    dimension_emotion,
                    sum( case dimension_emotion when '正面' then STATISTIC else 0 end ) as positive,
                    sum( case dimension_emotion when '负面' then STATISTIC else 0 end ) as negative,
                    sum( case dimension_emotion when '中性' then STATISTIC else 0 end ) as neutral
                from
                    tf_dws_voc_nps_record
                where
                    topic_code is not null
                    and dimension_emotion is not null
                    and date(publish_date) between
                    DATE_SUB( date(#{model.startDate}) , interval abs(DATEDIFF( date(#{model.startDate}), date(#{model.endDate}) )) +1  day )
                    and DATE_SUB( date(#{model.startDate}) , interval 1 day)
                    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter" />
                    <if test="model.province != null and model.province.size()>0">
                        and province_code in
                        <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
                <if test="model.dimensionRsLevel == null or model.dimensionRsLevel ==''">
                    group by first_dimension_code
                </if>
                <if test="model.dimensionRsLevel ==1">
                    group by second_dimension_code
                </if>
                <if test="model.dimensionRsLevel ==2">
                    group by three_dimension_code
                </if>
                <if test="model.dimensionRsLevel ==3">
                    group by topic_code
                </if>
                order by topic_code
            ) l2 on l1.topic_code = l2.topic_code
            order by CONVERT(l1.total, SIGNED) desc
            limit 20
        )vv
    </select>



    <!-- 用户意图分析 / 占比-->

	<select id="intentionAnalysisProportion"  resultType="com.car.voc.vo.NPSIntentionAnalysisProportionVo">
		select
			dateUnit as dateStr,
			intention_total as total,
			consult ,
			consult_c as consultC,
			consultP,
			complaint,
			complaint_c as complaintC,
			complaintP,
			praise,
			complain_c as complainC,
			praiseP,
			suggest,
			suggest_c as suggestC,
			suggestP,
			other,
			other_c as otherC,
			otherP,
			replace(format(ifnull(complain / intention_total * 100 , 0), 2), ',', '') as totalpraiseP
		from
		(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_emotion_intention_trend_common2"/>
		)vv
	</select>

	<select id="intentionAnalysisProportion1"  resultType="com.car.voc.vo.NPSIntentionAnalysisProportionVo">
		select
			code as car_series,
			intention_total as total,
			consult ,
			consult_c as consultC,
			consultP,
			complaint,
			complaint_c as complaintC,
			complaintP,
			praise,
			complain_c as complainC,
			praiseP,
			suggest,
			suggest_c as suggestC,
			suggestP,
			other,
			other_c as otherC,
			otherP,
			replace(format(complain / intention_total * 100 , 0 ), ',', '') as netPromoter,
			replace(format(ifnull(complain / intention_total * 100 , 0), 2), ',', '') as totalpraiseP
		from (
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_1"/>
			select
				code,
				format(detractor ,0) as detractor,
				replace(format(ifnull(detractor / scores_total * 100, 0),2), ',', '') as detractorP,
				replace(format(ifnull((detractor - detractor_c ) / detractor_c * 100 , 0),2), ',', '') as detractor_c,
				format(neutral ,0) as neutral,
				replace(format(ifnull(neutral / scores_total * 100, 0),2), ',', '') as neutralP,
				replace(format(ifnull((neutral - neutral_c ) / neutral_c * 100 , 0),2), ',', '') as neutral_c,
				format(recommend ,0) as recommend,
				replace(format(ifnull(recommend / scores_total * 100, 0),2), ',', '') as recommendP,
				replace(format(ifnull((recommend - recommend_c ) / recommend_c * 100 , 0),2), ',', '') as recommend_c,
				replace(format(ifnull(scores_total, 0),0), ',', '') as scores_total,
				format(positive ,0) as positive,
				replace(format(ifnull(positive / emotion_total * 100, 0),2), ',', '') as positiveP,
				replace(format(ifnull((positive - positive_c ) / positive_c * 100 , 0),2), ',', '') as positive_c,
				format(neutral2 ,0) as neutral2,
				replace(format(ifnull(neutral2 / emotion_total * 100, 0),2), ',', '') as neutral2P,
				replace(format(ifnull((neutral2 - neutral2_c ) / neutral2_c * 100 , 0),2), ',', '') as neutral2_c,
				format(negative ,0) as negative,
				replace(format(ifnull(negative / emotion_total * 100, 0),2), ',', '') as negativeP,
				replace(format(ifnull((negative - negative_c ) / negative_c * 100 , 0),2), ',', '') as negative_c,
				replace(format(ifnull(emotion_total, 0),0), ',', '') as emotion_total,
				format(consult ,0) as consult,
				replace(format(ifnull(consult / intention_total * 100, 0),2), ',', '') as consultP,
				replace(format(ifnull((consult - consult_c ) / consult_c * 100 , 0),2), ',', '') as consult_c,
				format(complaint ,0) as complaint,
				replace(format(ifnull(complaint / intention_total * 100, 0),2), ',', '') as complaintP,
				replace(format(ifnull((complaint - complaint_c ) / complaint_c * 100 , 0),2), ',', '') as complaint_c,
				format(complain ,0) as praise,
				replace(format(ifnull(complain / intention_total * 100, 0),2), ',', '') as praiseP,
				replace(format(ifnull((complain - complain_c ) / complain_c * 100 , 0),2), ',', '') as complain_c,
				format(suggest ,0) as suggest,
				replace(format(ifnull(suggest / intention_total * 100, 0),2), ',', '') as suggestP,
				replace(format(ifnull((suggest - suggest_c ) / suggest_c * 100 , 0),2), ',', '') as suggest_c,
				format(other ,0) as other,
				replace(format(ifnull(other / intention_total * 100, 0),2), ',', '') as otherP,
				replace(format(ifnull((other - other_c ) / other_c * 100 , 0),2), ',', '') as other_c,
				replace(format(ifnull(intention_total, 0),0), ',', '') as intention_total

			from (

				select
					c1.code,
					ifnull(c1.detractor ,0) as  detractor,
					ifnull(c2.detractor ,0) as detractor_c,
					ifnull(c1.neutral ,0) as neutral,
					ifnull(c2.neutral ,0) as neutral_c,
					ifnull(c1.recommend ,0) as recommend,
					ifnull(c2.recommend ,0) as recommend_c,
					ifnull(c1.positive ,0) as positive,
					ifnull(c2.positive ,0) as positive_c,
					ifnull(c1.neutral2 ,0) as neutral2,
					ifnull(c2.neutral2 ,0) as neutral2_c,
					ifnull(c1.negative ,0) as negative,
					ifnull(c2.negative ,0) as negative_c,
					ifnull(c1.consult ,0) as consult,
					ifnull(c2.consult ,0) as consult_c,
					ifnull(c1.complaint ,0)as complaint,
					ifnull(c2.complaint ,0) as complaint_c,
					ifnull(c1.complain ,0)as praise,
					ifnull(c2.complain ,0) as complain_c,
					ifnull(c1.suggest ,0) as suggest,
					ifnull(c2.suggest ,0) as suggest_c,
					ifnull(c1.other ,0) as other,
					ifnull(c2.other ,0) as other_c,
					(ifnull(c1.detractor ,0) + ifnull(c1.neutral ,0) + ifnull(c1.recommend ,0) ) as scores_total,
					(ifnull(c1.positive ,0) + ifnull(c1.neutral2 ,0) + ifnull(c1.negative ,0) ) as emotion_total,
					(ifnull(c1.consult ,0) + ifnull(c1.complaint ,0) + ifnull(c1.complain ,0) + ifnull(c1.suggest ,0) + ifnull(c1.other ,0) ) as intention_total
				from
				(
					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor) as detractor,
						sum(neutral) as neutral,
						sum(recommend) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
					date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries"/>
					)t on t.dateStr = df.startDate_r
					group by dateStr
					order by dateUnit desc
				)c1
				left join
				(

					select
						df.startDate_r as dateUnit,
						code,
						sum(detractor) as detractor,
						sum(neutral) as neutral,
						sum(recommend) as recommend,
						sum(positive) as positive,
						sum(negative) as negative,
						sum(neutral2) as neutral2,
						sum(consult) as consult,
						sum(complaint) as complaint,
						sum(complain) as praise,
						sum(suggest) as suggest,
						sum(other) as other
					from
					date_filter df
					left join (
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_carseries_chian"/>
					)t on t.dateStr = df.startDate_rc
					group by dateStr
					order by dateUnit desc
				)c2 on c1.dateUnit = c2.dateUnit
			)v1
		) vv
	 </select>


    <select id="intentionAnalysisProportion2"  resultType="com.car.voc.vo.NPSIntentionAnalysisProportionVo">
		select
			total,
			replace(format(complain / total * 100 , 0 ), ',', '') as netPromoter,
			format(sum(consult), 0) as consult,
			case
				when consult = 0
				and consultC = 0 then format(0, 2)
				when consult = 0
				and consultC != 0 then replace(format(-consultC * 100, 2), ',', '')
				when consult != 0
				and consultC = 0 then replace(format(consult * 100, 2), ',', '')
				when consult != 0
				and consultC != 0 then replace(format((consult - consultC) / consultC * 100 , 2), ',', '')
			end as consultC,
			replace(format(ifnull(consult / total * 100 , 0), 2), ',', '') as consultP,
			format(sum(complain), 0) as praise,
			case
				when complain = 0
				and complainC = 0 then format(0, 2)
				when complain = 0
				and complainC != 0 then replace(format(-complainC * 100, 2), ',', '')
				when complain != 0
				and complainC = 0 then replace(format(complain * 100, 2), ',', '')
				when complain != 0
				and complainC != 0 then replace(format((complain - complainC) / complainC * 100 , 2), ',', '')
			end as complainC,
			replace(format(ifnull(complain / total * 100 , 0), 2), ',', '') as praiseP,
			format(complaint, 0) as complaint,
			case
				when complaint = 0
				and complaintC = 0 then format(0, 2)
				when complaint = 0
				and complaintC != 0 then replace(format(-complaintC * 100, 2), ',', '')
				when complaint != 0
				and complaintC = 0 then replace(format( complaint * 100, 2), ',', '')
				when complaint != 0
				and complaintC != 0 then replace(format( (complaint - complaintC) / complaintC * 100 , 2), ',', '')
			end as complaintC,
			replace(format(ifnull(complaint / total * 100 , 0), 2), ',', '') as complaintP,
			case
				when other = 0
				and otherC = 0 then format(0, 2)
				when other = 0
				and otherC != 0 then replace(format(-otherC * 100, 2), ',', '')
				when other != 0
				and otherC = 0 then replace(format(other * 100, 2), ',', '')
				when other != 0
				and otherC != 0 then replace(format((other - otherC) / otherC * 100 , 2), ',', '')
			end as otherC,
			replace(format(ifnull(other / total * 100 , 0), 2), ',', '') as otherP,
			format(other, 0) as other,
			case
				when suggest = 0
				and suggestC = 0 then format(0, 2)
				when suggest = 0
				and suggestC != 0 then replace(format(-suggestC * 100, 2), ',', '')
				when suggest != 0
				and suggestC = 0 then replace(format(suggest * 100, 2), ',', '')
				when suggest != 0
				and suggestC != 0 then replace(format((suggest - suggestC) / suggestC * 100 , 2), ',', '')
			end as suggestC,
			replace(format(ifnull(suggest / total * 100 , 0), 2), ',', '') as suggestP,
			format(suggest, 0) as suggest,
			replace(format(ifnull(complain / total * 100 , 0), 2), ',', '') as totalpraiseP
		from (
			select
				(ifnull(sum(consult) , 0) + ifnull(sum(complaint) , 0) + ifnull(sum(complain) , 0)+ ifnull(sum(suggest) , 0) + ifnull(sum(other) , 0)) as total,
				ifnull(sum(complain) , 0) as praise,
				ifnull(sum(complaint) , 0) as complaint,
				ifnull(sum(consult) , 0) as consult,
				ifnull(sum(other) , 0) as other,
				ifnull(sum(suggest) , 0) as suggest,
				ifnull(sum(complaintC), 0) as complaintC,
				ifnull(sum(consultC), 0) as consultC,
				ifnull(sum(complainC), 0) as complainC,
				ifnull(sum(suggestC), 0) as suggestC,
				ifnull(sum(otherC), 0) as otherC,
				dateUnit
			from
				(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_1"/>
				select
					ifnull(sum(consult), 0) as consult,
					ifnull(sum(consultC), 0) as consultC,
					ifnull(sum(complaint), 0) as complaint,
					ifnull(sum(complaintC), 0) as complaintC,
					ifnull(sum(complain), 0) as praise,
					ifnull(sum(complainC), 0) as complainC,
					ifnull(sum(suggest), 0) as suggest,
					ifnull(sum(suggestC), 0) as suggestC,
					ifnull(sum(other), 0) as other,
					ifnull(sum(otherC), 0) as otherC,
					ifnull( sum(consult) + sum(complaint) + sum(complain)+ sum(suggest)+ sum(other) , 0) as total,
					dateUnit
				from
					(
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_car_series_emotion_data"/>
					select
						ifnull(sum(consult) , 0) as consult,
						consultC,
						ifnull(sum(complaint) , 0) as complaint,
						complaintC,
						ifnull(sum(complain) , 0) as praise,
						complainC,
						ifnull(sum(suggest) , 0) as suggest,
						suggestC,
						ifnull(sum(other) , 0) as other,
						otherC,
						(ifnull(sum(consult) , 0) + ifnull(sum(complaint) , 0) + ifnull(sum(complain) , 0)+ ifnull(sum(suggest) , 0) + ifnull(sum(other) , 0)) as total,
						<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
					from
						(
						select
							dateStr,
							ifnull(format(avg(avg_scores) , 2), 0) as avg_scores,
							ifnull(format(avg(avg_scoresC) , 2) , 0) as avg_scoresC,
							ifnull(format(sum(consult) , 0) , 0) as consult,
							ifnull(format(sum(consultC) , 0) , 0) as consultC,
							ifnull(format(sum(complaint) , 0), 0) as complaint,
							ifnull(format(sum(complaintC) , 0) , 0) as complaintC,
							ifnull(format(sum(suggest) , 0) , 0) as suggest,
							ifnull(format(sum(suggestC) , 0) , 0) as suggestC,
							ifnull(format(sum(complain) , 0) , 0) as praise,
							ifnull(format(sum(complainC) , 0) , 0) as complainC,
							ifnull(format(sum(other) , 0) , 0) as other,
							ifnull(format(sum(otherC) , 0) , 0) as otherC
						from
							core_data
						group by
							dateStr )vv
					group by
						dateStr )v2
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_2"/>
				group by
					dateUnit
			)vv
		)v1
    </select>


    <!-- 用户意图分析 / 趋势-->
    <select id="intentionAnalysisTrend"  resultType="com.car.voc.vo.NPSIntentionAnalysisProportionVo">
		select
			dateUnit as dateStr,
			intention_total as total,
			consult,
			consultP,
			consult_c as consultC,
			complaint,
			complaintP,
			complaint_c as complaintC,
			praise,
			praiseP,
			complain_c as complainC,
			suggest,
			suggestP,
			suggest_c as suggestC,
			other,
			otherP,
			other_c as otherC
		from
		(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_with_6"/>
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_core_data_emotion_intention_trend_common"/>
		)vv
	</select>
    <select id="intentionAnalysisTrend1"  resultType="com.car.voc.vo.NPSIntentionAnalysisProportionVo">
        select
			dateUnit as dateStr,
			replace(format(complain / total * 100 , 0 ), ',', '') as netPromoter,
			format(consult, 0) as consult,
			case
				when consult = 0
				and consultC = 0 then format(0, 2)
				when consult = 0
				and consultC != 0 then replace(format(-consultC * 100, 2), ',', '')
				when consult != 0
				and consultC = 0 then replace(format(consult * 100, 2), ',', '')
				when consult != 0
				and consultC != 0 then replace(format((consult - consultC) / consultC * 100 , 2), ',', '')
			end as consultC,
			replace(format(ifnull(consult / total * 100 , 0), 2), ',', '') as consultP,
			format(praise, 0) as praise,
			case
				when complain = 0
				and complainC = 0 then format(0, 2)
				when complain = 0
				and complainC != 0 then replace(format(-complainC * 100, 2), ',', '')
				when complain != 0
				and complainC = 0 then replace(format(complain * 100, 2), ',', '')
				when complain != 0
				and complainC != 0 then replace(format((complain - complainC) / complainC * 100 , 2), ',', '')
			end as complainC,
			replace(format(ifnull(complain / total * 100 , 0), 2), ',', '') as praiseP,
			format(complaint, 0) as complaint,
			case
				when complaint = 0
				and complaintC = 0 then format(0, 2)
				when complaint = 0
				and complaintC != 0 then replace(format(-complaintC * 100, 2), ',', '')
				when complaint != 0
				and complaintC = 0 then replace(format( complaint * 100, 2), ',', '')
				when complaint != 0
				and complaintC != 0 then replace(format( (complaint - complaintC) / complaintC * 100 , 2), ',', '')
			end as complaintC,
			replace(format(ifnull(complaint / total * 100 , 0), 2), ',', '') as complaintP,
			case
				when other = 0
				and otherC = 0 then format(0, 2)
				when other = 0
				and otherC != 0 then replace(format(-otherC * 100, 2), ',', '')
				when other != 0
				and otherC = 0 then replace(format(other * 100, 2), ',', '')
				when other != 0
				and otherC != 0 then replace(format((other - otherC) / otherC * 100 , 2), ',', '')
			end as otherC,
			replace(format(ifnull(other / total * 100 , 0), 2), ',', '') as otherP,
			format(other, 0) as other,
			case
				when suggest = 0
				and suggestC = 0 then format(0, 2)
				when suggest = 0
				and suggestC != 0 then replace(format(-suggestC * 100, 2), ',', '')
				when suggest != 0
				and suggestC = 0 then replace(format(suggest * 100, 2), ',', '')
				when suggest != 0
				and suggestC != 0 then replace(format((suggest - suggestC) / suggestC * 100 , 2), ',', '')
			end as suggestC,
			replace(format(ifnull(suggest / total * 100 , 0), 2), ',', '') as suggestP,
			format(suggest, 0) as suggest,
			format(total, 0) as total,
			format(ifnull(complain / total * 100 , 0), 2) as totalpraiseP,
			dateUnit
		from
			(
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_date_filter_with_6"/>
			select
				ifnull(sum(consult), 0) as consult,
				ifnull(sum(consultC), 0) as consultC,
				ifnull(sum(complaint), 0) as complaint,
				ifnull(sum(complaintC), 0) as complaintC,
				ifnull(sum(complain), 0) as praise,
				ifnull(sum(complainC), 0) as complainC,
				ifnull(sum(suggest), 0) as suggest,
				ifnull(sum(suggestC), 0) as suggestC,
				ifnull(sum(other), 0) as other,
				ifnull(sum(otherC), 0) as otherC,
				ifnull( sum(consult) + sum(complaint) + sum(complain)+ sum(suggest)+ sum(other) , 0) as total,
				dateUnit
			from
				(
				<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_car_series_emotion_data"/>
				select
					ifnull(sum(consult) , 0) as consult,
					consultC,
					ifnull(sum(complaint) , 0) as complaint,
					complaintC,
					ifnull(sum(complain) , 0) as praise,
					complainC,
					ifnull(sum(suggest) , 0) as suggest,
					suggestC,
					ifnull(sum(other) , 0) as other,
					otherC,
					(ifnull(sum(consult) , 0) + ifnull(sum(complaint) , 0) + ifnull(sum(complain) , 0)+ ifnull(sum(suggest) , 0) + ifnull(sum(other) , 0)) as total,
					<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_date_filter_group_attrs"/>
				from
					(
					select
						dateStr,
						ifnull(format(avg(avg_scores) , 2), 0) as avg_scores,
						ifnull(format(avg(avg_scoresC) , 2) , 0) as avg_scoresC,
						ifnull(format(sum(consult) , 0) , 0) as consult,
						ifnull(format(sum(consultC) , 0) , 0) as consultC,
						ifnull(format(sum(complaint) , 0), 0) as complaint,
						ifnull(format(sum(complaintC) , 0) , 0) as complaintC,
						ifnull(format(sum(suggest) , 0) , 0) as suggest,
						ifnull(format(sum(suggestC) , 0) , 0) as suggestC,
						ifnull(format(sum(complain) , 0) , 0) as praise,
						ifnull(format(sum(complainC) , 0) , 0) as complainC,
						ifnull(format(sum(other) , 0) , 0) as other,
						ifnull(format(sum(otherC) , 0) , 0) as otherC
					from
						core_data
					group by
						dateStr )vv
				group by
					dateStr )v2
			group by
				dateUnit
			order by dateUnit
		)vv
    </select>


    <select id="list"  resultType="com.car.voc.vo.NPSDataVo">
        select
            t.*,
            pa.item_text as province
        from (
            select
                id, question_code, question_name, answer_code, stage, status, start_time, end_time,
                service_station_num, affiliated_factory_num, province_code, user_id,user_type,
                energy_type, chassis_number, client_name, client_sex, client_phone, scores, classify, question,
                problem_classify, data_type,car_series_code,
                if(car_specific_time is null , '', date(car_specific_time)) as car_specific_time,
                question_closely_notes, brand_code, car_group, car_type, data_source, channel_id,
                date(publish_date) as dateStr, topic_code, three_dimension_code as third_dimension_code,
                dimension_emotion,intention_type,first_dimension_code,second_dimension_code,three_dimension_code,
                sounds_content,content_type,sentence, city as city_code
            from tf_dws_voc_nps_record t
            where id is not null
                <if test="model.ids != null and model.ids.size()>0">
                    and t.id in
                    <foreach item="item" collection="model.ids" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.ids == null or model.ids.size()==0">
                    and date(publish_date) between date('${model.startDate}') and date('${model.endDate}')
                    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
                    <if test="model.keyword != null and model.keyword != ''">
                        and ( question_code like concat("%",#{model.keyword},"%")
                                or answer_code like concat("%",#{model.keyword},"%")
                                or question like concat("%",#{model.keyword},"%") )
                    </if>
                    <if test="model.province != null and model.province.size()>0">
                        and t.province_code in
                        <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
                </if>
        ) t
        left JOIN
        (
            select item_value ,item_text
            from sys_dict_item ,
                (
                    select id
                    from sys_dict
                    where dict_code ='province'
                ) dict
            where dict_id = dict.id
        ) pa on t.province_code = pa.item_value
        order by dateStr desc
    </select>

    <select id="userAndStatistic"  resultType="java.util.Map">
        select
		CONCAT(t.userCount,'') as userCount,
		CONCAT(t.intention,'') as intention
        from (
            select
			SUM(vid.STATISTIC) as intention,
			count(distinct vid.USER_ID) as userCount
            from tf_dws_voc_nps_record vid
            where vid.id is not null
					<include refid="publicDateFilterCriteria.queryPop_vid_emotion" />

					<if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
						AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
					</if>
					<if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
						AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
					</if>
					<if test="model.topicCode !=null and model.topicCode !=''" >
						AND vid.TOPIC_CODE=#{model.topicCode}
					</if>
					<if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
						AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
					</if>
                    and date(publish_date) between date('${model.startDate}') and date('${model.endDate}')
                    <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
					<if test="model.province != null and model.province.size()>0">
                        and vid.province_code in
                        <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
        ) t

    </select>

    <select id="trendChangeLabelList"  resultType="com.car.stats.vo.HomePurposeTrendVo">
		SELECT
		<include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr,
		SUM(vid.STATISTIC) as "intention",
		count(distinct vid.USER_ID) as "userCount"
		FROM
		tf_dws_voc_nps_record vid
		WHERE
		1=1
		<include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
		<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
		<if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
			AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
		</if>

		<if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
			AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
		</if>
		<if test="model.topicCode !=null and model.topicCode !=''" >
			AND vid.TOPIC_CODE=#{model.topicCode}
		</if>
		<if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
			AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
		</if>
		<if test="model.brandCode !=null and model.brandCode !=''" >
			AND vid.brand_code =#{model.brandCode}
		</if>
		group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
		ORDER BY dateStr desc
    </select>


	<select id="getAnswerCodes"  resultType="java.lang.String">
		select
		t.*
		from (
		select
		answer_code as answerCode
		from tf_dws_voc_nps_record vid
		where id is not null
			and date(publish_date) between date('${model.startDate}') and date('${model.endDate}')
			<include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
			<if test="model.province != null and model.province.size()>0">
				and vid.province_code in
				<foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
					#{item}
				</foreach>
			</if>
			<include refid="publicDateFilterCriteria.queryPop_vid_emotion" />

			<if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
				AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
			</if>

			<if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
				AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
			</if>
			<if test="model.topicCode !=null and model.topicCode !=''" >
				AND vid.TOPIC_CODE=#{model.topicCode}
			</if>
			<if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
				AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
			</if>
			<if test="model.brandCode !=null and model.brandCode !=''" >
				AND vid.brand_code =#{model.brandCode}
			</if>
		) t

	</select>




	<!--

        CREATE TABLE `tf_dws_voc_nps_record` (
          `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
          `question_code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '问卷编号',
          `question_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '问卷名称',
          `answer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '答卷编号',
          `stage` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '阶段',
          `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '状态',
          `start_time` datetime DEFAULT NULL COMMENT '开始时间',
          `end_time` datetime DEFAULT NULL COMMENT '结束时间',
          `province_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '省份',
          `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '城市',
          `service_station_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '服务站号',
          `affiliated_factory_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '分厂号',
          `car_specific_time` datetime DEFAULT NULL COMMENT '购车时间',
          `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '区域',
          `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户类型',
          `user_type` decimal(65,0) DEFAULT NULL COMMENT '用户类型',
          `user_level` decimal(65,0) DEFAULT NULL COMMENT '用户等级',
          `energy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '能源类型',
          `chassis_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '底盘号',
          `client_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '客户姓名',
          `client_sex` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '性别',
          `scores` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '分值',
          `classify` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '分类',
          `question` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '问题',
          `problem_classify` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '问题分类',
          `question_closely_notes` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '问题分类',
          `brand_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '品牌',
          `car_series_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '车系',
          `car_group` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '车型组',
          `car_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '车分类',
          `data_source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据来源：APP、DCC、400',
          `channel_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '公域渠道',
          `first_dimension_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '一级标签',
          `dimension_emotion` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '情感',
          `intention_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '意图',
          `create_time` datetime DEFAULT NULL,
          `update_time` datetime DEFAULT NULL,
          `publish_date` datetime DEFAULT NULL,
          `statistic` bigint NOT NULL DEFAULT '1' COMMENT '计数',
          `second_dimension_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '二级标签',
          `problem_level` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
          `three_dimension_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '三级标签',
          `topic_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '四级标签',
          `emotion_keyword` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '四级标签',
          `is_one_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
          `display_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户名称',
          `area_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '热词',
          `client_phone` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL,
          `sounds_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '声音片段内容',
          `data_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '数据类型',
          `content_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `index_statis` (`statistic`),
          KEY `index_pudate` (`publish_date`),
          KEY `index_topic` (`topic_code`),
          KEY `index_car` (`car_series_code`),
          KEY `index_channel` (`channel_id`),
          KEY `index_source` (`data_source`),
          KEY `index_province` (`province`),
          KEY `index_second_three` (`second_dimension_code`,`three_dimension_code`),
          KEY `index_area` (`area_code`),
          KEY `idx_01` (`publish_date`,`province_code`),
          KEY `idx_02` (`publish_date`,`province_code`,`brand_code`,`car_series_code`,`channel_id`,`stage`,`classify`,`energy_type`,`client_sex`,`car_specific_time`),
          KEY `idx_03` (`publish_date`,`province_code`,`first_dimension_code`,`second_dimension_code`,`three_dimension_code`),
          KEY `idx_04` (`publish_date`,`province_code`,`scores`)
        ) ENGINE=InnoDB AUTO_INCREMENT=5202 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='DWS业务nps统计表';
        INSERT INTO voc_test.sys_permission
        (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, `system`, menu_i18n, api_address)
        VALUES('d7d6e2e4e2934f2c9385a623fd98d611', NULL, 'NPS分析', '/stats/nps', 'layouts/RouteView', NULL, NULL, 0, NULL, NULL, 4.00, 0, 'setting', 1, 0, 0, 0, NULL, NULL, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, NULL, 0, 0, 0, 'nps_overview', '/stats/nps');



        INSERT INTO voc_test.sys_dict
        (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, `type`)
        VALUES('1684836789306658818', '拥车期', 'car_specific_round', '', 0, NULL, '2023-07-28 16:02:48', NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict
        (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, `type`)
        VALUES('1689579840495333377', 'NPS-阶段', 'nps_stage', '', 0, NULL, '2023-08-10 18:10:00', NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict
        (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, `type`)
        VALUES('1689579962344058881', 'NPS-分类', 'nps_classify', '', 0, NULL, '2023-08-10 18:10:29', NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict
        (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, `type`)
        VALUES('53aad639aca4b5c010927cf610c3ff9c', '省份', 'province', NULL, 0, 'admin', '2022-10-18 10:00:00', NULL, '2022-10-18 10:00:00', 0);


        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837012426854402', '1684836789306658818', '半年内', 'R1', '', 1, 1, NULL, '2023-07-28 16:03:41', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837063937101825', '1684836789306658818', '0.5~1 年', 'R2', '', 2, 1, NULL, '2023-07-28 16:03:53', NULL, '2023-07-28 16:04:02', NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837596248805377', '1684836789306658818', '1~2 年', 'R3', '', 3, 1, NULL, '2023-07-28 16:06:00', NULL, '2023-07-28 16:07:15', NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837680076165121', '1684836789306658818', '2~3 年', 'R4', '', 4, 1, NULL, '2023-07-28 16:06:20', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837680076165131', '1684836789306658818', '3~4 年', 'R5', '', 5, 1, NULL, '2023-07-28 16:06:20', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837680076165141', '1684836789306658818', '4~5 年', 'R6', '', 6, 1, NULL, '2023-07-28 16:06:20', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1684837680076165151', '1684836789306658818', '5 年以上', 'R7', '', 7, 1, NULL, '2023-07-28 16:06:20', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1689580186819018754', '1689579840495333377', '提车', '提车', '', NULL, 1, NULL, '2023-08-10 18:11:22', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1689580213138239490', '1689579840495333377', '维保', '维保', '', NULL, 1, NULL, '2023-08-10 18:11:28', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1689580252271095809', '1689579962344058881', '门店', '门店', '', NULL, 1, NULL, '2023-08-10 18:11:38', NULL, NULL, NULL, NULL);
        INSERT INTO voc_test.sys_dict_item
        (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key)
        VALUES('1689580275599851522', '1689579962344058881', '品牌', '品牌', '', NULL, 1, NULL, '2023-08-10 18:11:43', NULL, NULL, NULL, NULL);

        select *
            from sys_dict
            where id in (
            '1684836789306658818',
            '1689579962344058881',
            '1689579840495333377',
            '53aad639aca4b5c010927cf610c3ff9c'
            )
    -->
</mapper>
