<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.ProvinceAreaMapper">
    <select id="queryProvinceByAreaCode"  resultType="com.car.voc.vo.DictVo">
        SELECT
            pc.*,
            pn.ITEM_TEXT as text
        FROM
            (
                SELECT
                    pa.PROVINCE_CODE as  value
                FROM
                    TC_PROVINCE_AREA pa
                WHERE
                    pa.AREA_CODE=#{areaCode}
                    <if test="brandCode != null and brandCode != '' ">
                        AND pa.brand_code LIKE CONCAT('%', #{brandCode} ,'%')
                    </if>
            ) pc
                LEFT JOIN SYS_DICT_ITEM pn ON pc.value=pn.ITEM_VALUE
        WHERE
            pn.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'
    </select>


    <select id="newQueryProvinceByAreaCode"  resultType="com.car.voc.vo.NewDictVo">
        select
            pc.brandCode,
            area_code as areaCode,
            pw.item_text as itemText,
            group_concat(pn.ITEM_TEXT separator ',  ') as provinceTexts,
            group_concat(pc.value separator ',  ') as provinceCodes
            from
            (
            select
                pa.area_code,
                pa.PROVINCE_CODE as value,
                pa.brand_code as brandCode
            from
                TC_PROVINCE_AREA pa
                where 1=1
                <if test="brandCode != null and brandCode != '' ">
                    AND pa.brand_code LIKE CONCAT('%', #{brandCode} ,'%')
                </if>
                    ) pc
            left join SYS_DICT_ITEM pn on
            pc.value = pn.ITEM_VALUE
            left join SYS_DICT_ITEM pw on
            pc.area_code = pw.ITEM_VALUE
            group by
            pc.brandCode,
            pc.area_code
    </select>

</mapper>
