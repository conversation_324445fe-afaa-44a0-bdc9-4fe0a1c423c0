package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRoleArea;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* @Entity com.car.voc.entity.SysRoleArea
*/
public interface SysRoleAreaMapper extends BaseMapper<SysRoleArea> {

    @Select("select distinct vbr.province_code as code from sys_role_area sra left join voc_brand_region vbr  on vbr.community_code =sra.code where sra.del_flag='0' and sra.role_id =#{roleId} and sra.brand_code=#{brandCode} and vbr.province_code is not null")
    List<SysRoleArea> provinceListByRoleId(@Param("roleId") String roleId,@Param("brandCode") String brandCode);
    @Select("select * from SYS_ROLE_AREA where role_id = #{roleId} and brand_code= #{brandCode} and del_flag='0' ")
    List<SysRoleArea> areaListByRoleId(@Param("roleId") String roleId,@Param("brandCode") String brandCode);
}
