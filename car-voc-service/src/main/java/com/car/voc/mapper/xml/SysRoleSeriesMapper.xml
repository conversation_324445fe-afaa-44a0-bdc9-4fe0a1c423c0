<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.SysRoleSeriesMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.SysRoleSeries">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="roleId" column="ROLE_ID" jdbcType="VARCHAR"/>
            <result property="carCode" column="CAR_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ROLE_ID,CAR_CODE
    </sql>
</mapper>
