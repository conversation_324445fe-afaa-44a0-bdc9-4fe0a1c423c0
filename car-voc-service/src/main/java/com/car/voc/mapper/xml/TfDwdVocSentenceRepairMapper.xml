<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TfDwdVocSentenceRepairMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TfDwdVocSentenceRepair">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="balanceNo" column="BALANCE_NO" jdbcType="VARCHAR"/>
            <result property="serviceCode" column="SERVICE_CODE" jdbcType="VARCHAR"/>
            <result property="roNo" column="RO_NO" jdbcType="VARCHAR"/>
            <result property="oneId" column="ONE_ID" jdbcType="VARCHAR"/>
            <result property="userType" column="USER_TYPE" jdbcType="DECIMAL"/>
            <result property="userLevel" column="USER_LEVEL" jdbcType="DECIMAL"/>
            <result property="isOneId" column="IS_ONE_ID" jdbcType="CHAR"/>
            <result property="displayName" column="DISPLAY_NAME" jdbcType="VARCHAR"/>
            <result property="brandCode" column="BRAND_CODE" jdbcType="VARCHAR"/>
            <result property="carSeriesCode" column="CAR_SERIES_CODE" jdbcType="VARCHAR"/>
            <result property="carGroup" column="CAR_GROUP" jdbcType="VARCHAR"/>
            <result property="carType" column="CAR_TYPE" jdbcType="VARCHAR"/>
            <result property="firstDimensionCode" column="FIRST_DIMENSION_CODE" jdbcType="VARCHAR"/>
            <result property="secondDimensionCode" column="SECOND_DIMENSION_CODE" jdbcType="VARCHAR"/>
            <result property="threeDimensionCode" column="THREE_DIMENSION_CODE" jdbcType="VARCHAR"/>
            <result property="topicCode" column="TOPIC_CODE" jdbcType="VARCHAR"/>
            <result property="province" column="PROVINCE" jdbcType="VARCHAR"/>
            <result property="areaCode" column="AREA_CODE" jdbcType="VARCHAR"/>
            <result property="publishDate" column="PUBLISH_DATE" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BALANCE_NO,SERVICE_CODE,
        RO_NO,ONE_ID,USER_TYPE,
        USER_LEVEL,IS_ONE_ID,DISPLAY_NAME,
        BRAND_CODE,CAR_SERIES_CODE,CAR_GROUP,
        CAR_TYPE,FIRST_DIMENSION_CODE,SECOND_DIMENSION_CODE,
        THREE_DIMENSION_CODE,TOPIC_CODE,PROVINCE,
        AREA_CODE,PUBLISH_DATE,CREATE_TIME
    </sql>
</mapper>
