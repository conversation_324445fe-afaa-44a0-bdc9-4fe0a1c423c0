<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TtBalanceLabourMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TtBalanceLabour">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="dealerCode" column="DEALER_CODE" jdbcType="VARCHAR"/>
            <result property="serviceCode" column="SERVICE_CODE" jdbcType="VARCHAR"/>
            <result property="oldId" column="OLD_ID" jdbcType="VARCHAR"/>
            <result property="roNo" column="RO_NO" jdbcType="VARCHAR"/>
            <result property="labourId" column="LABOUR_ID" jdbcType="VARCHAR"/>
            <result property="roItemId" column="RO_ITEM_ID" jdbcType="DECIMAL"/>
            <result property="manageSortCode" column="MANAGE_SORT_CODE" jdbcType="VARCHAR"/>
            <result property="troubleDesc" column="TROUBLE_DESC" jdbcType="VARCHAR"/>
            <result property="activityNo" column="ACTIVITY_NO" jdbcType="VARCHAR"/>
            <result property="troubleCause" column="TROUBLE_CAUSE" jdbcType="VARCHAR"/>
            <result property="labourCode" column="LABOUR_CODE" jdbcType="VARCHAR"/>
            <result property="labourName" column="LABOUR_NAME" jdbcType="VARCHAR"/>
            <result property="labourPrice" column="LABOUR_PRICE" jdbcType="DECIMAL"/>
            <result property="stdLabourHour" column="STD_LABOUR_HOUR" jdbcType="DECIMAL"/>
            <result property="assignLabourHour" column="ASSIGN_LABOUR_HOUR" jdbcType="DECIMAL"/>
            <result property="labourAmount" column="LABOUR_AMOUNT" jdbcType="DECIMAL"/>
            <result property="workerTypeCode" column="WORKER_TYPE_CODE" jdbcType="CHAR"/>
            <result property="realReceiveAmount" column="REAL_RECEIVE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="discount" column="DISCOUNT" jdbcType="DECIMAL"/>
            <result property="discountAmount" column="DISCOUNT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="chargePartitionCode" column="CHARGE_PARTITION_CODE" jdbcType="VARCHAR"/>
            <result property="interReturn" column="INTER_RETURN" jdbcType="DECIMAL"/>
            <result property="preCheck" column="PRE_CHECK" jdbcType="DECIMAL"/>
            <result property="consignExterior" column="CONSIGN_EXTERIOR" jdbcType="DECIMAL"/>
            <result property="packageCode" column="PACKAGE_CODE" jdbcType="VARCHAR"/>
            <result property="repairTypeCode" column="REPAIR_TYPE_CODE" jdbcType="VARCHAR"/>
            <result property="repairTechnician" column="REPAIR_TECHNICIAN" jdbcType="VARCHAR"/>
            <result property="rpTechnicianName" column="RP_TECHNICIAN_NAME" jdbcType="VARCHAR"/>
            <result property="labourType" column="LABOUR_TYPE" jdbcType="DECIMAL"/>
            <result property="isNeedChoose" column="IS_NEED_CHOOSE" jdbcType="DECIMAL"/>
            <result property="isDiscount" column="IS_DISCOUNT" jdbcType="DECIMAL"/>
            <result property="repairItemType" column="REPAIR_ITEM_TYPE" jdbcType="DECIMAL"/>
            <result property="activityType" column="ACTIVITY_TYPE" jdbcType="DECIMAL"/>
            <result property="obligatedNo" column="OBLIGATED_NO" jdbcType="CHAR"/>
            <result property="discountStdLabourHour" column="DISCOUNT_STD_LABOUR_HOUR" jdbcType="DECIMAL"/>
            <result property="itemSeq" column="ITEM_SEQ" jdbcType="VARCHAR"/>
            <result property="applyNo" column="APPLY_NO" jdbcType="VARCHAR"/>
            <result property="isCreditsActivity" column="IS_CREDITS_ACTIVITY" jdbcType="DECIMAL"/>
            <result property="activityCreditsPackCode" column="ACTIVITY_CREDITS_PACK_CODE" jdbcType="VARCHAR"/>
            <result property="actItemId" column="ACT_ITEM_ID" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="manageLabourType" column="MANAGE_LABOUR_TYPE" jdbcType="DECIMAL"/>
            <result property="isStaffVehicle" column="IS_STAFF_VEHICLE" jdbcType="DECIMAL"/>
            <result property="dKey" column="D_KEY" jdbcType="DECIMAL"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateDate" column="UPDATE_DATE" jdbcType="VARCHAR"/>
            <result property="ver" column="VER" jdbcType="DECIMAL"/>
            <result property="stdLabourPrice" column="STD_LABOUR_PRICE" jdbcType="DECIMAL"/>
            <result property="viewStdLabourHour" column="VIEW_STD_LABOUR_HOUR" jdbcType="DECIMAL"/>
            <result property="entityCode" column="ENTITY_CODE" jdbcType="VARCHAR"/>
            <result property="balanceNo" column="BALANCE_NO" jdbcType="CHAR"/>
            <result property="dbName" column="DB_NAME" jdbcType="VARCHAR"/>
            <result property="syncTime" column="SYNC_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DEALER_CODE,SERVICE_CODE,
        OLD_ID,RO_NO,LABOUR_ID,
        RO_ITEM_ID,MANAGE_SORT_CODE,TROUBLE_DESC,
        ACTIVITY_NO,TROUBLE_CAUSE,LABOUR_CODE,
        LABOUR_NAME,LABOUR_PRICE,STD_LABOUR_HOUR,
        ASSIGN_LABOUR_HOUR,LABOUR_AMOUNT,WORKER_TYPE_CODE,
        REAL_RECEIVE_AMOUNT,DISCOUNT,DISCOUNT_AMOUNT,
        CHARGE_PARTITION_CODE,INTER_RETURN,PRE_CHECK,
        CONSIGN_EXTERIOR,PACKAGE_CODE,REPAIR_TYPE_CODE,
        REPAIR_TECHNICIAN,RP_TECHNICIAN_NAME,LABOUR_TYPE,
        IS_NEED_CHOOSE,IS_DISCOUNT,REPAIR_ITEM_TYPE,
        ACTIVITY_TYPE,OBLIGATED_NO,DISCOUNT_STD_LABOUR_HOUR,
        ITEM_SEQ,APPLY_NO,IS_CREDITS_ACTIVITY,
        ACTIVITY_CREDITS_PACK_CODE,ACT_ITEM_ID,REMARK,
        MANAGE_LABOUR_TYPE,IS_STAFF_VEHICLE,D_KEY,
        CREATE_BY,CREATE_DATE,UPDATE_BY,
        UPDATE_DATE,VER,STD_LABOUR_PRICE,
        VIEW_STD_LABOUR_HOUR,ENTITY_CODE,BALANCE_NO,
        DB_NAME,SYNC_TIME
    </sql>
</mapper>
