package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.vo.VocChannelCategoryVo;

/**
 *
 * @version 1.0.0
 * @ClassName VocChannelCategoryMapper.java
 * @Description TODO
 * @createTime 2022年10月17日 22:32
 * @Copyright voc
 */
public interface VocChannelCategoryMapper extends BaseMapper<VocChannelCategory> {
    IPage<VocChannelCategoryVo> queryByPage(Page<VocChannelCategoryVo> page, VocChannelCategory model);
}
