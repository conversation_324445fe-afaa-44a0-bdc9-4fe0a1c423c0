<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.entity.SysUserDepart">
    <select id="getUserDepartByUid" parameterType="String" resultType="com.car.voc.entity.SysUserDepart">
		SELECT *
		FROM sys_user_depart
		WHERE user_id = #{userId, jdbcType=VARCHAR}
    </select>
</mapper>
