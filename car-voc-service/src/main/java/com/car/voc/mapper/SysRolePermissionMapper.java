package com.car.voc.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRolePermission;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 角色权限表 Mapper 接口
 * </p>
 *
 *
 * @since 2018-12-21
 */
public interface SysRolePermissionMapper extends BaseMapper<SysRolePermission> {

    @Select("select * from SYS_ROLE_PERMISSION where role_id = #{roleId} and del_flag ='0' ")
    List<SysRolePermission> listByRoleId(String roleId);

    @Select("select DISTINCT rp.PERMISSION_ID  from SYS_USER u join SYS_USER_ROLE r on u.id = r.USER_ID join SYS_ROLE_PERMISSION rp\n" +
            " on r.ROLE_ID = rp.ROLE_ID join SYS_PERMISSION p on rp.PERMISSION_ID = p.id \n" +
            " where rp.del_flag='0' and r.del_flag ='0' and  u.id = #{userId} ")
    List<String> listKanBan(String userId);
}
