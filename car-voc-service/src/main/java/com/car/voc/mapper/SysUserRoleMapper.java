package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysRole;
import com.car.voc.entity.SysUserRole;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户角色表 Mapper 接口
 * </p>
 *
 *
 * @since 2018-12-21
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

	@Select("select role_code from sys_role where id in (select role_id from sys_user_role where  del_flag='0' and user_id = (select id from sys_user where del_flag=0 and username=#{username}))")
	List<String> getRoleByUserName(@Param("username") String username);

	@Select("select role_id from sys_user_role where user_id = #{userId} and del_flag='0' ")
	List<String> getRoleIdByUserId(@Param("userId") String userId);

	@Select("select r.* from sys_user_role u left join sys_role r on u.role_id = r.id where u.del_flag='0' and r.del_flag='0' and  u.user_id = #{userId}  limit 1")
	SysRole getRoleInfoByUserId(@Param("userId") String userId);

	@Select("select r.* from sys_user_role u left join sys_role r on u.role_id = r.id where u.del_flag='0' and r.del_flag='0' and u.user_id = #{userId}")
	List<SysRole> getRoleInfoListByUserId(@Param("userId") String userId);

	@Select("select * from TT_REPORT_CATEGORY where user_id = #{userId} and is_del =0")
	List<Map<String,Object>> getUserReportPermissionByUserId(@Param("userId") String userId);

	@Select("select * from TT_REPORT_VIEW_DEPART_USER where user_id = #{userId}")
	List<Map<String,Object>> getUserReportViewPermissionByUserId(@Param("userId") String userId);

	@Select("select DISTINCT p.API_ADDRESS from sys_permission p INNER JOIN SYS_ROLE_PERMISSION rp ON p.id = rp.PERMISSION_ID\n" +
			"INNER JOIN sys_user_role ur ON rp.ROLE_ID = ur.ROLE_ID where rp.del_flag='0' and ur.del_flag='0' and  ur.user_id = #{userId} and p.API_ADDRESS is not null")
	List<String> listUserPermission(@Param("userId") String userId);

}
