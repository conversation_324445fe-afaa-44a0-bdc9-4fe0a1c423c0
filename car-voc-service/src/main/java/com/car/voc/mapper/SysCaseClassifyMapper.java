package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.model.CaseLibraryModel;
import com.car.voc.entity.SysCaseClassify;
import com.car.voc.vo.SysCaseClassifyListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * TODO
 *
 * @Description 案例分类 Mapper
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
public interface SysCaseClassifyMapper extends BaseMapper<SysCaseClassify> {


    SysCaseClassifyListVo getCaseBrowseNum(@Param("id") String id,@Param("status") Integer status);

    List<SysCaseClassifyListVo> getCaseBrowseNumList(@Param("pageNo") Integer pageNo,@Param("pageSize") Integer pageSize);

    Boolean updateExamine(@Param("model") CaseLibraryModel clm);
}
