package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.TtBalanceRepairPart;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* @Entity com.car.voc.entity.TtBalanceRepairPart
*/
public interface TtBalanceRepairPartMapper extends BaseMapper<TtBalanceRepairPart> {

    @Select("select * from TT_BALANCE_REPAIR_PART where SERVICE_CODE = #{serviceCode} and trim(balance_no) = #{balanceNo}")
    List<TtBalanceRepairPart> getListByBalanceNoAndServiceCode(String balanceNo,String serviceCode);


}
