package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.entity.FaultProblem;
import com.car.voc.model.FaultProblemListQueryModel;
import com.car.voc.vo.FaultProblemListVo;
import com.car.voc.vo.FaultProblemVo;
import com.car.voc.vo.InternationalVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName FaultProblemMapper.java
 * @Description TODO
 * @createTime 2022年10月09日 13:52
 * @Copyright voc
 */
public interface FaultProblemMapper extends BaseMapper<FaultProblem> {
    IPage<FaultProblemListVo> queryByPage(Page<FaultProblemListVo> page,@Param("model")  FaultProblemListQueryModel model);

    void updateTreeNodeStatus(@Param("id") String id,@Param("status") Integer status);

    FaultProblemVo getItemById(@Param("id") String id);

    @Select("select name from VOC_FAULT_PROBLEM ${ew.customSqlSegment}")
    String getNameByCode(@Param(Constants.WRAPPER) QueryWrapper<FaultProblem> wrapper);

    @Select("select code ,name as text_cn from voc_fault_problem")
    List<InternationalVo> internationalCnTags();
    @Select("select code ,name_en as text_cn from voc_fault_problem")
    List<InternationalVo> internationalEnTags();


    @Select("select ta.* from (select * from VOC_FAULT_PROBLEM ${ew.customSqlSegment} ) as ta where 1=1 limit 1")
    List<FaultProblem> selectListCodeOrderBy(@Param(Constants.WRAPPER) LambdaQueryWrapper<FaultProblem> query);

    @Select("SELECT `code` FROM voc_fault_problem WHERE `name`=#{topic}")
    List<String> queryTopicCodesByTopicName(@Param("topic")  String topic);
    @Select("select distinct code from voc_fault_problem vfp where 9>length(vfp.code )")
    List<String> getTags2All();
}
