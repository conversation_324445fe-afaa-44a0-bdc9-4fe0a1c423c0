package com.car.voc.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.entity.BrandProductManager;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 品牌产品管理
 *
 * @Date:   2021-04-09
 * @Version: V1.0
 */

public interface BrandProductManagerMapper extends BaseMapper<BrandProductManager> {

    @SqlParser(filter=true)
    List<BrandProductManager> queryByPage(Page<BrandProductManager> page, @Param("model") BrandProductManager model);

    @Select("select * from voc_brand_product_manager where del_flag='0' and id=#{id}")
    BrandProductManager queryById(@Param("id") String id);

    @Select("select * from voc_brand_product_manager where del_flag='0' and code=#{code}")
    BrandProductManager queryByCode(@Param("code") String code);

    @Select("select name from voc_brand_product_manager where del_flag='0' and code=#{code}")
    String selectByBrandCode(@Param("code") String brandCode);

    @Select("select max(sort_no) as orderBy  from voc_brand_product_manager")
    String maxSortNo();

}
