<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocBusinessTagMapper">

	<update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update voc_business_tag set has_child = #{status} where id = #{id}
	</update>

	<sql id="querySelectFeild">
		g4.id, g4.name, g4.tag_code as tagCode,  g4.related_description as relatedDescription, g4.yndel, g4.pid
	</sql>
	<select id="queryByPage"  resultType="com.car.voc.vo.VocBusinessTagListVo">
		SELECT
			g1.name as firstDimensionName,
			g1.NAME_EN as firstDimensionNameEn,
			g1.tag_code as firstDimensionCode,
			g2.name as secondDimensionName,
			g2.NAME_EN as secondDimensionNameEn,
			g2.tag_code as secondDimensionCode,
			g3.name as thirdDimensionName,
			g3.NAME_EN as thirdDimensionNameEn,
			g3.tag_code as thirdDimensionCode,
			g4.name as topicName,
			g4.NAME_EN as nameEn,
			g4.tag_code as topicCode,
			g4.tag_scope as tagScope,
			g4.order_type as orderType,
			g4.related_departments as relatedDepartments,
			g4.enable as enable,
		    <include refid="querySelectFeild" />
		FROM
			VOC_BUSINESS_TAG g4
				LEFT JOIN VOC_BUSINESS_TAG g3 ON g4.PID=g3.ID
				LEFT JOIN VOC_BUSINESS_TAG g2 ON g3.PID=g2.ID
				LEFT JOIN VOC_BUSINESS_TAG g1 ON g2.PID=g1.ID
		WHERE
			1=1
		  and g4.OTHER=0
		  AND g1.TAG_CODE IS NOT NULL
		<if test="model.pid != null">
			AND g1.PID = #{model.pid}
		</if>
		<if test="model.searchKeyword != null and model.searchKeyword != ''">
			AND
			(
			g1.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
			g2.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
			g3.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
			g4.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')
			)
		</if>

	</select>
	<select id="vocTags"  resultType="com.car.voc.vo.thirdVo.VocTagVo">
		select
		    t1.*
		from
		(
		select
		vbt.id,
		vbt.name ,
		vbt.pid ,
		replace(vbt.tag_code,'GWM','B')  as code,
		vbt.enable,
		vbt.has_child,
		'1' as tagType,
		vbt.tag_scope ,
		vbt.order_type
		from voc_business_tag_new vbt
		WHERE
		1=1
		AND vbt.tag_code IS NOT NULL
		<if test="model.searchKeyword != null and model.searchKeyword != ''">
			AND
			(
			vbt.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')
			)
		</if>
		UNION all
		select
		vfp.id,
		vfp.name ,
		vfp.pid ,
		vfp.code as code,
		vfp.enable,
		vfp.has_child,
		'2' as tagType,
		vfp.tag_scope ,
		vfp.order_type
		from voc_fault_problem_new vfp
		WHERE
		1=1
		AND vfp.code IS NOT NULL
		<if test="model.searchKeyword != null and model.searchKeyword != ''">
			AND
			(
			vfp.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')
			)
		</if>

		) t1
	</select>
	<select id="relationBusinessTag"  resultType="com.car.voc.vo.auth.RoleAuthTree">
			select *
			from VOC_BUSINESS_TAG
		</select>

	<select id="getAllTags" resultType="com.car.voc.vo.VocBusinessTagVo">
		select tqtag.id, name, tag_code, pid,order_by from (
		select vbt.id ,vbt.name , vbt.tag_code , vbt.pid,order_by
		from voc_business_tag vbt
		where 9>length(vbt.tag_code)
		and enable=1  and tag_scope!=2
		union all
		select vbt.id ,vbt.name , vbt.code tag_code , 'qpid' as pid ,order_by
		from voc_fault_problem vbt
		where 6>length(vbt.code)
		and enable=1 and del_flag=0 and tag_scope!=2
		) tqtag
		where 1=1
		<if test='tags != null and tags.size() > 0'>
			and tag_code in
			<foreach collection='tags' item='tag' open='(' separator=',' close=')'>
				#{tag}
			</foreach>
		</if>
		union all
		select  'qpid' as id, '质量故障' as name,'Q0001' as tag_code,  '0' as pid, 9999 as order_by
		order by order_by asc
	</select>


</mapper>
