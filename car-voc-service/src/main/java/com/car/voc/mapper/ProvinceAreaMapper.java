package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.ProvinceArea;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.NewDictVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName ProvinceAreaMapper.java
 * @Description TODO
 * @createTime 2022年10月21日 16:54
 * @Copyright voc
 */
public interface ProvinceAreaMapper extends BaseMapper<ProvinceArea> {
    List<DictVo> queryProvinceByAreaCode(@Param("areaCode") String areaCode,@Param("brandCode") String brandCode);


    List<NewDictVo> newQueryProvinceByAreaCode(String brandCode);
}
