package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.entity.VocRiskAlertReviewer;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.vo.risk.RiskAlertReviewerVo;
import com.car.voc.vo.risk.RiskAllTypesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskWarningRecordMapper.java
 * @Description TODO
 * @createTime 2023年02月07日 11:30
 * @Copyright voc
 */
public interface VocRiskAllTypesMapper extends BaseMapper<VocRiskAllTypes> {
    IPage<RiskAllTypesVo> auditList(Page<RiskAllTypesVo> page, @Param("model") RiskAllTypesModel model);

    List<RiskAllTypesVo> riskList(@Param("model") RiskAllTypesModel model);

    List<Map<String, Integer>> riskLevelDis(@Param("model") FilterCriteriaModel model);

    List<Map> riskDepsList(@Param("model")  FilterCriteriaModel model);

    Map<String, Object> processingProgress(@Param("model")  FilterCriteriaModel model);

    IPage<RiskAllTypesVo> taskListTop(Page<RiskAllTypesVo> page,  @Param("model") FilterCriteriaModel model);

    List<RiskAllTypesVo> riksListByStatus(@Param("model") RiskAllTypesModel model);

    IPage<RiskAllTypesVo> riskListRole(Page<RiskAllTypesVo> page,@Param("model") RiskAllTypesModel model);

    RiskAlertReviewerVo getRiskAlert(@Param("reviewerUserId") String  reviewerUserId);

    IPage<RiskAllTypesVo> riskListManage(Page<RiskAllTypesVo> page, RiskAllTypesModel model);

    IPage<RiskAllTypesVo> riskListProcessingPersonnel(Page<RiskAllTypesVo> page, RiskAllTypesModel model);

    IPage<RiskAllTypesVo> riskListReviewer(Page<RiskAllTypesVo> page, RiskAllTypesModel model);
}
