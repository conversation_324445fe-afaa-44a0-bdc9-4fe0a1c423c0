<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocBrandRegionMapper">

   <select id="queryBrandRegion" resultType="com.car.voc.vo.RegionalDictVo">


       SELECT a.id as value, a.pid AS pid, a.NAME as text,a.application_type as type
            , (
           SELECT count(*) > 0
           FROM sys_role_area b
           WHERE a.id = b.code
             AND b.role_id = #{roleId}
             and b.brand_code =#{brandCode}
             and b.del_flag=0
           LIMIT 1
       ) AS checked
       FROM (
                select
                    vbr.regional_code as id,
                    vbr.regional_name as name,
                    0 as pid,
                    vbr.application_type
                from voc_brand_region vbr
                where
                    vbr.brand =#{brandCode}
                group by vbr.regional_code,vbr.regional_name,vbr.application_type
                union all
                select
                    vbr.community_code  as id,
                    vbr.community_name  as name,
                    vbr.regional_code  as pid,
                    vbr.application_type
                from voc_brand_region vbr
                where
                    vbr.brand =#{brandCode}
                group by vbr.community_code,vbr.community_name,regional_code,vbr.application_type
                union all
                select
                    vbr.province_code  as id,
                    vbr.province_name  as name,
                    vbr.community_code  as pid,
                    vbr.application_type
                from voc_brand_region vbr
                where
                    vbr.brand =#{brandCode}
                group by vbr.province_code,vbr.province_name,community_code,vbr.application_type
                union all
                select
                    vbr.city_code  as id,
                    vbr.city_name  as name,
                    vbr.community_code  as pid,
                    vbr.application_type
                from voc_brand_region vbr
                where
                    vbr.brand =#{brandCode}
                group by vbr.city_code,vbr.city_name,community_code,vbr.application_type
            ) a

   </select>

</mapper>

