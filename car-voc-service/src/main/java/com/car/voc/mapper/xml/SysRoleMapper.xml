<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.SysRoleMapper">


	<resultMap id="TreeModel" type="com.car.voc.model.TreeModel" >
		<result column="id" property="key" jdbcType="VARCHAR"/>
		<result column="name" property="title" jdbcType="VARCHAR"/>
		<result column="icon" property="icon" jdbcType="VARCHAR"/>
		<result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
		<result column="is_leaf" property="isLeaf" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 通过<resultMap>映射实体类属性名和表的字段名对应关系 -->
	<resultMap id="SysPermission" type="com.car.voc.entity.SysPermission">
	   <!-- result属性映射非匹配字段 -->
	    <result column="is_route" property="route"/>
	    <result column="keep_alive" property="keepAlive"/>
	    <result column="is_leaf" property="leaf"/>
	</resultMap>


	<select id="queryListByParentId" parameterType="Object"  resultMap="TreeModel">
		   SELECT
                   id
                   ,parent_id
                   ,name
                   ,icon
                   ,is_leaf
		   FROM   sys_permission
		   WHERE 1=1
		    <choose>
		   		<when test="parentId != null and parentId != ''">
		   			AND parent_id =  #{parentId,jdbcType=VARCHAR}
		   		</when>
		   		<otherwise>
		   			AND parent_id is null
		   		</otherwise>
		    </choose>
	</select>

	<select id="roleDataChannel" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
			select a.id,
				   a.PID,
				   a.NAME,
			    	(select count(*)>0
			    		from SYS_ROLE_CHANNEL b
			    			where
			    				a.id=b.CHANNEL_ID
			    			  	and b.role_id= #{roleId,jdbcType=VARCHAR}
							  	and b.del_flag="0"
			    			  	limit 1
			    	) isChecked
			from voc_channel_category a
	</select>
	<select id="roleDataArea" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
				select
				   a.CODE,
				   a.NAME,
			    	(select count(*)>0
			    		from SYS_ROLE_AREA b
			    			where
			    				a.CODE=b.CODE
			    			  	and b.role_id= #{roleId,jdbcType=VARCHAR}
							  	and b.del_flag="0"
			    			  	limit 1
			    	) isChecked
			from (select s.item_value as "CODE",s.item_text as "NAME" from sys_dict_item s
		   where dict_id = (select id from sys_dict where dict_code = 'area')) a
	</select>
	<select id="roleDataSeries" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
			select a.id,
				   a.P_ID,
				   a.code,
				   a.NAME,
			    	(select count(*)>0
			    		from SYS_ROLE_SERIES b
			    			where
			    				a.CODE=b.car_code
			    			  	and b.role_id= #{roleId,jdbcType=VARCHAR}
							  	and b.del_flag="0"
			    			  	limit 1
			    	) isChecked
			from VOC_BRAND_PRODUCT_MANAGER a
			where
			1=1
			order by a.SORT_NO asc
	</select>
	<select id="roleDataPermission" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
			select a.id,
				   a.PARENT_ID as pid,
				   a.NAME,
			    	(select count(*)>0
			    		from SYS_ROLE_PERMISSION b
			    			where
			    				a.id=b.PERMISSION_ID
			    			  	and b.role_id= #{roleId,jdbcType=VARCHAR}
							    and b.del_flag="0"
			    			  	limit 1
			    	) isChecked
			from SYS_PERMISSION a
			where a.id not in('d7d6e2e4e2934f2c9385a623fd98c6f1','c7d6e2e4e2934f2c9385a623fd98c353',
			'd7d6e2e4e2934f2c9385a623fd98c6f7','d7d6e2e4e2934f2c9385a623fd98c6f9','d7d6e2e4e2934f2c9385a623fd98c610') order by a.sort_no asc
	</select>
	<select id="relationBusinessTag" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
				select a.id,
					   a.TAG_CODE as code,
					   a.PID,
					   a.NAME,
					   a.ORDER_BY,
						(select count(*)>0
							from SYS_ROLE_BUSINESS_TAG b
								where
									a.TAG_CODE=b.TAG_CODE
									and b.role_id= #{roleId,jdbcType=VARCHAR}
									and b.del_flag="0"
									<if test="brandCode!=null  and brandCode!=''">
										and brand_code = #{brandCode,jdbcType=VARCHAR}
									</if>

									limit 1
						) isChecked
				from VOC_BUSINESS_TAG a
				where
				enable=1 and tag_scope!='2' and 10>=LENGTH(a.TAG_CODE)
		</select>
		<select id="relationQualityTag" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
					select a.id,
						   a.CODE,
						   a.PID,
						   a.NAME,
							(select count(*)>0
								from SYS_ROLE_QUALITY_TAG b
									where
										a.CODE=b.TAG_CODE
										and b.role_id= #{roleId,jdbcType=VARCHAR}
									  	and b.del_flag="0"
										limit 1
							) isChecked
					from VOC_FAULT_PROBLEM a
					where
						a.enable=1 and a.tag_scope!='2' and 10>=LENGTH(a.code)
			</select>

	<!-- 获取登录用户拥有的权限 -->
	<select id="queryByUser" parameterType="Object"  resultMap="SysPermission">
		   SELECT * FROM (
			   SELECT p.*
			   FROM  sys_permission p
			   WHERE (exists(
						select a.id from sys_role_permission a
						join sys_role b on a.role_id = b.id
						join sys_user_role c on c.role_id = b.id
						join sys_user d on d.id = c.user_id
						where p.id = a.permission_id AND d.username = #{username,jdbcType=VARCHAR}
								and a.del_flag="0"
								and b.del_flag="0"
								and c.del_flag="0"
					)
					or (p.url like '%:code' and p.url like '/online%' and p.hidden = 1)
					or p.url = '/online')
			   and p.del_flag = 0
			<!--update begin Author:lvdandan  Date:20200213 for：加入部门权限 -->
			   UNION
			   SELECT p.*
			   FROM  sys_permission p
			   WHERE exists(
					select a.id from sys_depart_role_permission a
					join sys_depart_role b on a.role_id = b.id
					join sys_depart_role_user c on c.drole_id = b.id
					join sys_user d on d.id = c.user_id
					where p.id = a.permission_id AND d.username = #{username,jdbcType=VARCHAR}
			   )
			   and p.del_flag = 0
			<!--update end Author:lvdandan  Date:20200213 for：加入部门权限 -->
		   ) h order by h.sort_no ASC
	</select>


	<!-- 根据用户账号查询菜单权限 -->
	<select id="queryCountByUsername" parameterType="Object" resultType="int">
		select sum(cnt) from (
		  select count(*) as cnt
			from sys_role_permission a
			join sys_permission b on a.permission_id = b.id
			join sys_role c on a.role_id = c.id
			join sys_user_role d on d.role_id = c.id
			join sys_user e on d.user_id = e.id
			where e.username = #{username}
			<if test="permission.id !=null and permission.id != ''">
				and b.id =  #{permission.id}
			</if>
			<if test="permission.url !=null and permission.url != ''">
				and b.url =  #{permission.url}
			</if>
		union all
		  select count(*) as cnt
			from sys_permission z
		    join sys_depart_role_permission y on z.id = y.permission_id
			join sys_depart_role x on y.role_id = x.id
			join sys_depart_role_user w on w.drole_id = x.id
			join sys_user v on w.user_id = v.id
			where v.username = #{username}
			<if test="permission.id !=null and permission.id != ''">
				and z.id =  #{permission.id}
			</if>
			<if test="permission.url !=null and permission.url != ''">
				and z.url =  #{permission.url}
			</if>
		) temp
	</select>




	<select id="brandCodeDataChannel" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
			select a.id,
				   a.PID,
				   a.NAME,
				   (select count(*)>0
			    		from SYS_ROLE_CHANNEL b
			    			where
			    				a.id=b.CHANNEL_ID
			    			  	and b.role_id= #{roleId,jdbcType=VARCHAR}
								and b.del_flag="0"
								<if test="brandCode!=null  and brandCode!=''">
									and brand_code = #{brandCode,jdbcType=VARCHAR}
								</if>
			    			  	limit 1
			    	) isChecked
			from voc_channel_category a where 1=1 and enable=1
			<if test="brandCode!=null  and brandCode!=''">
				and	a.brand_code LIKE CONCAT('%', #{brandCode} ,'%')
			</if>
	</select>


	<select id="brandCodeDataArea" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
		SELECT a.id,a.id as code, a.pid AS pid, a.NAME,a.application_type as type, (
		SELECT count(*) > 0
		FROM sys_role_area b
		WHERE a.id = b.code
		and b.role_id= #{roleId,jdbcType=VARCHAR}
		and b.del_flag="0"
		<if test="brandCode!=null  and brandCode!=''">
			and brand_code = #{brandCode,jdbcType=VARCHAR}
		</if>
		LIMIT 1
		) AS isChecked
		FROM (select
		vbr.regional_code as id,
		vbr.regional_name as name,
		0 as pid,
		vbr.application_type
		from voc_brand_region vbr
		where
		1=1
		<if test="brandCode!=null  and brandCode!=''">
		and	vbr.brand= #{brandCode,jdbcType=VARCHAR}
		</if>
		group by vbr.regional_code,vbr.regional_name,vbr.application_type
		union all
		select
		vbr.community_code  as id,
		vbr.community_name  as name,
		vbr.regional_code  as pid,
		vbr.application_type
		from voc_brand_region vbr
		where
		1=1
		<if test="brandCode!=null  and brandCode!=''">
		and vbr.brand= #{brandCode,jdbcType=VARCHAR}
		</if>
		group by vbr.community_code,vbr.community_name,regional_code,vbr.application_type
		) a
	</select>
	<select id="brandCodeDataArea1" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
		select
		area_code as CODE,
		pw.item_text as NAME,
		(select count(*)>0
		from SYS_ROLE_AREA b
		where
		area_code=b.CODE
		and b.role_id= #{roleId,jdbcType=VARCHAR}
		<if test="brandCode!=null  and brandCode!=''">
			and brand_code = #{brandCode,jdbcType=VARCHAR}
		</if>
		limit 1
		) isChecked
		from
		(
		select
		pa.area_code,
		pa.PROVINCE_CODE as value,
		pa.brand_code as brandCode
		from
		TC_PROVINCE_AREA pa
		where 1=1
		<if test="brandCode!=null  and brandCode!=''">
			and pa.brand_code LIKE CONCAT('%', #{brandCode} ,'%')
		</if>
		) pc
		left join SYS_DICT_ITEM pn on
		pc.value = pn.ITEM_VALUE
		left join SYS_DICT_ITEM pw on
		pc.area_code = pw.ITEM_VALUE
		group by
		pc.brandCode,
		pc.area_code
	</select>


	<select id="brandCodeDataSeries" parameterType="Object"  resultType="com.car.voc.vo.auth.RoleAuthTree">
			select a.id,
				   a.P_ID,
				   a.code,
				   a.NAME,
				   (select count(*)>0
			    		from SYS_ROLE_SERIES b
			    			where
			    				a.CODE=b.car_code
			    			  	and b.role_id= #{roleId,jdbcType=VARCHAR}
			    			  	and b.del_flag="0"
								<if test="brandCode!=null  and brandCode!=''">
									and brand_code = #{brandCode,jdbcType=VARCHAR}
								</if>
			    			  	limit 1
			    	) isChecked
			from VOC_BRAND_PRODUCT_MANAGER a
			where
			 enable=1
			<if test="brandCode!=null  and brandCode!=''">
				and a.code LIKE CONCAT(#{brandCode} ,'%')
			</if>
			order by a.SORT_NO asc
	</select>

	<select id="getRoleById" resultType="com.car.voc.entity.SysRole">
		select *  FROM sys_role where id = #{userId}
    </select>
</mapper>
