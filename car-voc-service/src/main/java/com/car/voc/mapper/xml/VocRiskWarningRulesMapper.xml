<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocRiskWarningRulesMapper">

    <select id="selectRuleList" resultType="com.car.voc.entity.VocRiskWarningRulesNew">
        select
        *
        from
            voc_risk_warning_rules
        where 1 = 1
        and brand_code = #{brandCode}
        ORDER BY create_time DESC
    </select>

    <select id="selectRuleDetailList" resultType="java.lang.String">
        select
            vrwrd.risk_level as riskLevel
        from
            voc_risk_warning_rules vrwr
                left join
            voc_risk_warning_rules_detailed vrwrd
            on vrwr.id = vrwrd.warn_rule_id
        where 1 = 1
            and vrwr.brand_code = #{brandCode}
            and vrwr.risk_type like CONCAT(#{riskType},'%')
            and vrwrd.type = '2'
            and ( #{riskIndex} >= vrwrd.risk_Level_min and vrwrd.risk_level_max > #{riskIndex} )

        order by vrwrd.risk_level_max desc
        limit 1
    </select>

    <select id="getRiskType" resultType="com.car.voc.entity.VocRiskWarningRules">
        select
        *
        from
        voc_risk_warning_rules vrwr
        where 1 = 1
        and vrwr.risk_type = #{riskType}
    </select>

</mapper>
