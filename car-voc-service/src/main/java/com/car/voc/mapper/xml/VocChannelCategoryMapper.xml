<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.VocChannelCategoryMapper">


    <select id="queryByPage"  resultType="com.car.voc.vo.VocChannelCategoryVo">
        SELECT
        a.*,
        CONCAT_WS('->',p1.name,p.name) as namep,
        CONCAT_WS(',',p1.id,p.id ) as pids
        FROM
        voc_channel_category a
        LEFT JOIN voc_channel_category p ON a.pid=p.id
        LEFT JOIN voc_channel_category p1 on p.pid=p1.id
        WHERE
        1=1
          <if test="model.searchKeyword != null and model.searchKeyword != ''">
            AND
            (
            a.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            p.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            p1.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            a.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')
            )
        </if>
        <if test="model.brandCode != null and model.brandCode != ''">
            AND a.brand_code LIKE CONCAT('%', #{model.brandCode} ,'%')
        </if>
        order by a.order_by asc


    </select>



</mapper>
