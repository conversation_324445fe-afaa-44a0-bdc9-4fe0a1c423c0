package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.model.VocBusinessTagListQueryModel;
import com.car.voc.vo.InternationalVo;
import com.car.voc.vo.VocBusinessTagListVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.auth.RoleAuthTree;
import com.car.voc.vo.thirdVo.VocTagVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Date:   2021-03-30
 * @Version: V1.0
 */
public interface VocBusinessTagMapper extends BaseMapper<VocBusinessTag> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

	@Select("select name from voc_business_tag ${ew.customSqlSegment}")
	String getNameByCode(@Param(Constants.WRAPPER) QueryWrapper<VocBusinessTag> wrapper);

    IPage<VocBusinessTagListVo> queryByPage(Page<VocBusinessTagListVo> page,@Param("model") VocBusinessTagListQueryModel model);

//	@Select("select id from voc_business_tag_new where brand=#{brand} and tag_code=#{parentTagCode} ")

	@Select("select id from voc_business_tag where brand=#{brand} and tag_code=#{parentTagCode} ")
	String queryIdByTagBrandAndCode(@Param("brand") String brand,@Param("parentTagCode") String parentTagCode);


//	@Insert("insert  into voc_business_tag_new(id,name,NAME_EN,enable,tag_code,tag_type,industry_id,has_child,pid,brand,other) " +
//			"values (#{tag.id},#{tag.name},#{tag.nameEn},#{tag.enable},#{tag.tagCode},#{tag.tagType},#{tag.industryId},#{tag.hasChild},#{tag.pid},#{tag.brand},#{tag.other})  ")

	@Insert("insert  into voc_business_tag(id,name,NAME_EN,enable,tag_code,tag_type,industry_id,has_child,pid,brand,other) " +
			"values (#{tag.id},#{tag.name},#{tag.nameEn},#{tag.enable},#{tag.tagCode},#{tag.tagType},#{tag.industryId},#{tag.hasChild},#{tag.pid},#{tag.brand},#{tag.other})  ")
	int addRecord(@Param("tag") VocBusinessTag vocBusinessTag);


	@Select("SELECT TAG_CODE AS code ,NAME AS value FROM VOC_BUSINESS_TAG")
	List<InternationalVo> internationalCnTags_();

    IPage<VocTagVo> vocTags(Page<VocTagVo> page,@Param("model")  VocBusinessTagListQueryModel vocBusinessTag);

    List<VocBusinessTagVo> getAllTags(@Param("tags")  List<String> tags);
}
