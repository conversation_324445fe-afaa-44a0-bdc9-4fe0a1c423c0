<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TtBalanceAccountsMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.TtBalanceAccounts">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="entityCode" column="ENTITY_CODE" jdbcType="VARCHAR"/>
            <result property="dealerCode" column="DEALER_CODE" jdbcType="VARCHAR"/>
            <result property="serviceCode" column="SERVICE_CODE" jdbcType="VARCHAR"/>
            <result property="balanceNo" column="BALANCE_NO" jdbcType="CHAR"/>
            <result property="branchCode" column="BRANCH_CODE" jdbcType="VARCHAR"/>
            <result property="lastBalanceNo" column="LAST_BALANCE_NO" jdbcType="CHAR"/>
            <result property="cardId" column="CARD_ID" jdbcType="DECIMAL"/>
            <result property="balanceBusinessType" column="BALANCE_BUSINESS_TYPE" jdbcType="DECIMAL"/>
            <result property="balanceBusinessNo" column="BALANCE_BUSINESS_NO" jdbcType="CHAR"/>
            <result property="balanceBusinessStatus" column="BALANCE_BUSINESS_STATUS" jdbcType="DECIMAL"/>
            <result property="balanceModeCode" column="BALANCE_MODE_CODE" jdbcType="CHAR"/>
            <result property="discountModeCode" column="DISCOUNT_MODE_CODE" jdbcType="CHAR"/>
            <result property="contractNo" column="CONTRACT_NO" jdbcType="VARCHAR"/>
            <result property="contractCard" column="CONTRACT_CARD" jdbcType="CHAR"/>
            <result property="invoiceNo" column="INVOICE_NO" jdbcType="VARCHAR"/>
            <result property="invoiceTypeCode" column="INVOICE_TYPE_CODE" jdbcType="CHAR"/>
            <result property="payTypeCode" column="PAY_TYPE_CODE" jdbcType="CHAR"/>
            <result property="sumAmount" column="SUM_AMOUNT" jdbcType="DECIMAL"/>
            <result property="totalAmount" column="TOTAL_AMOUNT" jdbcType="DECIMAL"/>
            <result property="tax" column="TAX" jdbcType="DECIMAL"/>
            <result property="taxAmount" column="TAX_AMOUNT" jdbcType="DECIMAL"/>
            <result property="netAmouccnt" column="NET_AMOUCCNT" jdbcType="VARCHAR"/>
            <result property="receiveAmount" column="RECEIVE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="subObbAmount" column="SUB_OBB_AMOUNT" jdbcType="DECIMAL"/>
            <result property="derateAmount" column="DERATE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="payOff" column="PAY_OFF" jdbcType="DECIMAL"/>
            <result property="estimateNo" column="ESTIMATE_NO" jdbcType="CHAR"/>
            <result property="balanceHandler" column="BALANCE_HANDLER" jdbcType="VARCHAR"/>
            <result property="isRed" column="IS_RED" jdbcType="DECIMAL"/>
            <result property="balanceTime" column="BALANCE_TIME" jdbcType="DECIMAL"/>
            <result property="squareDate" column="SQUARE_DATE" jdbcType="DECIMAL"/>
            <result property="balanceClose" column="BALANCE_CLOSE" jdbcType="DECIMAL"/>
            <result property="balanceCloseTime" column="BALANCE_CLOSE_TIME" jdbcType="DECIMAL"/>
            <result property="obligatedOperator" column="OBLIGATED_OPERATOR" jdbcType="CHAR"/>
            <result property="cancelCloseReason" column="CANCEL_CLOSE_REASON" jdbcType="VARCHAR"/>
            <result property="memberNo" column="MEMBER_NO" jdbcType="VARCHAR"/>
            <result property="activityCredit" column="ACTIVITY_CREDIT" jdbcType="DECIMAL"/>
            <result property="occurCredit" column="OCCUR_CREDIT" jdbcType="DECIMAL"/>
            <result property="totalCredit" column="TOTAL_CREDIT" jdbcType="DECIMAL"/>
            <result property="arrBalance" column="ARR_BALANCE" jdbcType="DECIMAL"/>
            <result property="insurationCode" column="INSURATION_CODE" jdbcType="CHAR"/>
            <result property="insurationNo" column="INSURATION_NO" jdbcType="VARCHAR"/>
            <result property="printBalanceTime" column="PRINT_BALANCE_TIME" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="shieldingDate" column="SHIELDING_DATE" jdbcType="DECIMAL"/>
            <result property="isShielding" column="IS_SHIELDING" jdbcType="DECIMAL"/>
            <result property="lockUser" column="LOCK_USER" jdbcType="VARCHAR"/>
            <result property="cancelBalanceReason" column="CANCEL_BALANCE_REASON" jdbcType="VARCHAR"/>
            <result property="cancelBalanceExplain" column="CANCEL_BALANCE_EXPLAIN" jdbcType="DECIMAL"/>
            <result property="cancelBalanceApplicant" column="CANCEL_BALANCE_APPLICANT" jdbcType="VARCHAR"/>
            <result property="cancelBalApplicantTime" column="CANCEL_BAL_APPLICANT_TIME" jdbcType="DECIMAL"/>
            <result property="couponTotalAmount" column="COUPON_TOTAL_AMOUNT" jdbcType="DECIMAL"/>
            <result property="materialDeductAmount" column="MATERIAL_DEDUCT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="labourDeductAmount" column="LABOUR_DEDUCT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="labourAmount" column="LABOUR_AMOUNT" jdbcType="DECIMAL"/>
            <result property="repairPartAmount" column="REPAIR_PART_AMOUNT" jdbcType="DECIMAL"/>
            <result property="receiveLabourAmount" column="RECEIVE_LABOUR_AMOUNT" jdbcType="DECIMAL"/>
            <result property="receiveRepairPartAmount" column="RECEIVE_REPAIR_PART_AMOUNT" jdbcType="DECIMAL"/>
            <result property="receiveAdditionalAmount" column="RECEIVE_ADDITIONAL_AMOUNT" jdbcType="DECIMAL"/>
            <result property="auditingStatus" column="AUDITING_STATUS" jdbcType="DECIMAL"/>
            <result property="auditingTime" column="AUDITING_TIME" jdbcType="DECIMAL"/>
            <result property="auditingRemark" column="AUDITING_REMARK" jdbcType="VARCHAR"/>
            <result property="dragonCardAmount" column="DRAGON_CARD_AMOUNT" jdbcType="DECIMAL"/>
            <result property="svwLabourDeductCredit" column="SVW_LABOUR_DEDUCT_CREDIT" jdbcType="DECIMAL"/>
            <result property="svwDeductCredit" column="SVW_DEDUCT_CREDIT" jdbcType="DECIMAL"/>
            <result property="svwCurrentUsedCredit" column="SVW_CURRENT_USED_CREDIT" jdbcType="DECIMAL"/>
            <result property="svwCreditAmountFactor" column="SVW_CREDIT_AMOUNT_FACTOR" jdbcType="DECIMAL"/>
            <result property="svwLabrDeductCrdtAmt" column="SVW_LABR_DEDUCT_CRDT_AMT" jdbcType="DECIMAL"/>
            <result property="svwDeductCrdtAmt" column="SVW_DEDUCT_CRDT_AMT" jdbcType="DECIMAL"/>
            <result property="svwCrrntUsedCrdtAmt" column="SVW_CRRNT_USED_CRDT_AMT" jdbcType="DECIMAL"/>
            <result property="skdHqtrsCrdtAmt" column="SKD_HQTRS_CRDT_AMT" jdbcType="DECIMAL"/>
            <result property="skdDealerCrdtAmt" column="SKD_DEALER_CRDT_AMT" jdbcType="DECIMAL"/>
            <result property="crmExchangeNo" column="CRM_EXCHANGE_NO" jdbcType="VARCHAR"/>
            <result property="recActStatus" column="REC_ACT_STATUS" jdbcType="DECIMAL"/>
            <result property="theoryTotalAmount" column="THEORY_TOTAL_AMOUNT" jdbcType="DECIMAL"/>
            <result property="submitTime" column="SUBMIT_TIME" jdbcType="DECIMAL"/>
            <result property="downStamp" column="DOWN_STAMP" jdbcType="DECIMAL"/>
            <result property="isUpload" column="IS_UPLOAD" jdbcType="DECIMAL"/>
            <result property="reasonTechNojoin" column="REASON_TECH_NOJOIN" jdbcType="VARCHAR"/>
            <result property="posAccount" column="POS_ACCOUNT" jdbcType="VARCHAR"/>
            <result property="dragonUseIntegral" column="DRAGON_USE_INTEGRAL" jdbcType="DECIMAL"/>
            <result property="repairType" column="REPAIR_TYPE" jdbcType="DECIMAL"/>
            <result property="rmbAmount" column="RMB_AMOUNT" jdbcType="DECIMAL"/>
            <result property="balanceType" column="BALANCE_TYPE" jdbcType="DECIMAL"/>
            <result property="repairOrderTypeCode" column="REPAIR_ORDER_TYPE_CODE" jdbcType="CHAR"/>
            <result property="discountOperator" column="DISCOUNT_OPERATOR" jdbcType="VARCHAR"/>
            <result property="claimPartDiscount" column="CLAIM_PART_DISCOUNT" jdbcType="DECIMAL"/>
            <result property="claimItemDiscount" column="CLAIM_ITEM_DISCOUNT" jdbcType="DECIMAL"/>
            <result property="techNorepairReason" column="TECH_NOREPAIR_REASON" jdbcType="DECIMAL"/>
            <result property="dxRemark" column="DX_REMARK" jdbcType="VARCHAR"/>
            <result property="vwEmployeeTag" column="VW_EMPLOYEE_TAG" jdbcType="DECIMAL"/>
            <result property="hqVwUsedCredit" column="HQ_VW_USED_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwUsedCreditAmount" column="HQ_VW_USED_CREDIT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="hqVwCreateCredit" column="HQ_VW_CREATE_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwPreDeductCredit" column="HQ_VW_PRE_DEDUCT_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwBalanceHqCredit" column="HQ_VW_BALANCE_HQ_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwBalanceDealerCredit" column="HQ_VW_BALANCE_DEALER_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwBalanceCreditFactor" column="HQ_VW_BALANCE_CREDIT_FACTOR" jdbcType="DECIMAL"/>
            <result property="hqVwPreDeductCreditAmount" column="HQ_VW_PRE_DEDUCT_CREDIT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="hqVwCardId" column="HQ_VW_CARD_ID" jdbcType="VARCHAR"/>
            <result property="hqVwErrorCode" column="HQ_VW_ERROR_CODE" jdbcType="VARCHAR"/>
            <result property="isUploadHqVw" column="IS_UPLOAD_HQ_VW" jdbcType="DECIMAL"/>
            <result property="hqVwCardType" column="HQ_VW_CARD_TYPE" jdbcType="VARCHAR"/>
            <result property="isCreditsSwap" column="IS_CREDITS_SWAP" jdbcType="DECIMAL"/>
            <result property="hqVwLabourHqCredit" column="HQ_VW_LABOUR_HQ_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwLabourDealerCredit" column="HQ_VW_LABOUR_DEALER_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwMultHqCredit" column="HQ_VW_MULT_HQ_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwMultDealerCredit" column="HQ_VW_MULT_DEALER_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwStdHqCredit" column="HQ_VW_STD_HQ_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwStdDealerCredit" column="HQ_VW_STD_DEALER_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqSynCode" column="HQ_SYN_CODE" jdbcType="VARCHAR"/>
            <result property="hqVwActDealerCredit" column="HQ_VW_ACT_DEALER_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwActHqCredit" column="HQ_VW_ACT_HQ_CREDIT" jdbcType="DECIMAL"/>
            <result property="honestBusiness" column="HONEST_BUSINESS" jdbcType="DECIMAL"/>
            <result property="hqVwEticketPoint" column="HQ_VW_ETICKET_POINT" jdbcType="DECIMAL"/>
            <result property="hqVwEticketPointValue" column="HQ_VW_ETICKET_POINT_VALUE" jdbcType="DECIMAL"/>
            <result property="labourRate" column="LABOUR_RATE" jdbcType="DECIMAL"/>
            <result property="partRate" column="PART_RATE" jdbcType="DECIMAL"/>
            <result property="labourPrice" column="LABOUR_PRICE" jdbcType="DECIMAL"/>
            <result property="isApp" column="IS_APP" jdbcType="DECIMAL"/>
            <result property="preServiceUseAmount" column="PRE_SERVICE_USE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="wechatGiveCredit" column="WECHAT_GIVE_CREDIT" jdbcType="DECIMAL"/>
            <result property="hqVwPropertyCode" column="HQ_VW_PROPERTY_CODE" jdbcType="VARCHAR"/>
            <result property="isUpdate" column="IS_UPDATE" jdbcType="DECIMAL"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="DECIMAL"/>
            <result property="dKey" column="D_KEY" jdbcType="DECIMAL"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateDate" column="UPDATE_DATE" jdbcType="DECIMAL"/>
            <result property="ver" column="VER" jdbcType="DECIMAL"/>
            <result property="additionalAmount" column="ADDITIONAL_AMOUNT" jdbcType="DECIMAL"/>
            <result property="containInsurance" column="CONTAIN_INSURANCE" jdbcType="DECIMAL"/>
            <result property="relateInsurationName" column="RELATE_INSURATION_NAME" jdbcType="VARCHAR"/>
            <result property="maintainSuggest" column="MAINTAIN_SUGGEST" jdbcType="VARCHAR"/>
            <result property="warrantyDays" column="WARRANTY_DAYS" jdbcType="DECIMAL"/>
            <result property="warrantyMileage" column="WARRANTY_MILEAGE" jdbcType="DECIMAL"/>
            <result property="isDeclare" column="IS_DECLARE" jdbcType="DECIMAL"/>
            <result property="realReceiveLabourAmount" column="REAL_RECEIVE_LABOUR_AMOUNT" jdbcType="DECIMAL"/>
            <result property="realReceiveRepairPartAmount" column="REAL_RECEIVE_REPAIR_PART_AMOUNT" jdbcType="DECIMAL"/>
            <result property="orderPriceAmount" column="ORDER_PRICE_AMOUNT" jdbcType="DECIMAL"/>
            <result property="nextMitainMileage" column="NEXT_MITAIN_MILEAGE" jdbcType="DECIMAL"/>
            <result property="nextMitainDate" column="NEXT_MITAIN_DATE" jdbcType="DECIMAL"/>
            <result property="preServiceAmount" column="PRE_SERVICE_AMOUNT" jdbcType="VARCHAR"/>
            <result property="rightAmount" column="RIGHT_AMOUNT" jdbcType="VARCHAR"/>
            <result property="rightRmbAmount" column="RIGHT_RMB_AMOUNT" jdbcType="VARCHAR"/>
            <result property="firstPrintDate" column="FIRST_PRINT_DATE" jdbcType="DECIMAL"/>
            <result property="vin" column="VIN" jdbcType="VARCHAR"/>
            <result property="ownerName" column="OWNER_NAME" jdbcType="VARCHAR"/>
            <result property="ownerMobile" column="OWNER_MOBILE" jdbcType="VARCHAR"/>
            <result property="ownerAppIdpid" column="OWNER_APP_IDPID" jdbcType="VARCHAR"/>
            <result property="deliverer" column="DELIVERER" jdbcType="VARCHAR"/>
            <result property="delivererMobile" column="DELIVERER_MOBILE" jdbcType="VARCHAR"/>
            <result property="delivererAppIdpid" column="DELIVERER_APP_IDPID" jdbcType="VARCHAR"/>
            <result property="syncTime" column="SYNC_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ENTITY_CODE,DEALER_CODE,
        SERVICE_CODE,BALANCE_NO,BRANCH_CODE,
        LAST_BALANCE_NO,CARD_ID,BALANCE_BUSINESS_TYPE,
        BALANCE_BUSINESS_NO,BALANCE_BUSINESS_STATUS,BALANCE_MODE_CODE,
        DISCOUNT_MODE_CODE,CONTRACT_NO,CONTRACT_CARD,
        INVOICE_NO,INVOICE_TYPE_CODE,PAY_TYPE_CODE,
        SUM_AMOUNT,TOTAL_AMOUNT,TAX,
        TAX_AMOUNT,NET_AMOUCCNT,RECEIVE_AMOUNT,
        SUB_OBB_AMOUNT,DERATE_AMOUNT,PAY_OFF,
        ESTIMATE_NO,BALANCE_HANDLER,IS_RED,
        BALANCE_TIME,SQUARE_DATE,BALANCE_CLOSE,
        BALANCE_CLOSE_TIME,OBLIGATED_OPERATOR,CANCEL_CLOSE_REASON,
        MEMBER_NO,ACTIVITY_CREDIT,OCCUR_CREDIT,
        TOTAL_CREDIT,ARR_BALANCE,INSURATION_CODE,
        INSURATION_NO,PRINT_BALANCE_TIME,REMARK,
        SHIELDING_DATE,IS_SHIELDING,LOCK_USER,
        CANCEL_BALANCE_REASON,CANCEL_BALANCE_EXPLAIN,CANCEL_BALANCE_APPLICANT,
        CANCEL_BAL_APPLICANT_TIME,COUPON_TOTAL_AMOUNT,MATERIAL_DEDUCT_AMOUNT,
        LABOUR_DEDUCT_AMOUNT,LABOUR_AMOUNT,REPAIR_PART_AMOUNT,
        RECEIVE_LABOUR_AMOUNT,RECEIVE_REPAIR_PART_AMOUNT,RECEIVE_ADDITIONAL_AMOUNT,
        AUDITING_STATUS,AUDITING_TIME,AUDITING_REMARK,
        DRAGON_CARD_AMOUNT,SVW_LABOUR_DEDUCT_CREDIT,SVW_DEDUCT_CREDIT,
        SVW_CURRENT_USED_CREDIT,SVW_CREDIT_AMOUNT_FACTOR,SVW_LABR_DEDUCT_CRDT_AMT,
        SVW_DEDUCT_CRDT_AMT,SVW_CRRNT_USED_CRDT_AMT,SKD_HQTRS_CRDT_AMT,
        SKD_DEALER_CRDT_AMT,CRM_EXCHANGE_NO,REC_ACT_STATUS,
        THEORY_TOTAL_AMOUNT,SUBMIT_TIME,DOWN_STAMP,
        IS_UPLOAD,REASON_TECH_NOJOIN,POS_ACCOUNT,
        DRAGON_USE_INTEGRAL,REPAIR_TYPE,RMB_AMOUNT,
        BALANCE_TYPE,REPAIR_ORDER_TYPE_CODE,DISCOUNT_OPERATOR,
        CLAIM_PART_DISCOUNT,CLAIM_ITEM_DISCOUNT,TECH_NOREPAIR_REASON,
        DX_REMARK,VW_EMPLOYEE_TAG,HQ_VW_USED_CREDIT,
        HQ_VW_USED_CREDIT_AMOUNT,HQ_VW_CREATE_CREDIT,HQ_VW_PRE_DEDUCT_CREDIT,
        HQ_VW_BALANCE_HQ_CREDIT,HQ_VW_BALANCE_DEALER_CREDIT,HQ_VW_BALANCE_CREDIT_FACTOR,
        HQ_VW_PRE_DEDUCT_CREDIT_AMOUNT,HQ_VW_CARD_ID,HQ_VW_ERROR_CODE,
        IS_UPLOAD_HQ_VW,HQ_VW_CARD_TYPE,IS_CREDITS_SWAP,
        HQ_VW_LABOUR_HQ_CREDIT,HQ_VW_LABOUR_DEALER_CREDIT,HQ_VW_MULT_HQ_CREDIT,
        HQ_VW_MULT_DEALER_CREDIT,HQ_VW_STD_HQ_CREDIT,HQ_VW_STD_DEALER_CREDIT,
        HQ_SYN_CODE,HQ_VW_ACT_DEALER_CREDIT,HQ_VW_ACT_HQ_CREDIT,
        HONEST_BUSINESS,HQ_VW_ETICKET_POINT,HQ_VW_ETICKET_POINT_VALUE,
        LABOUR_RATE,PART_RATE,LABOUR_PRICE,
        IS_APP,PRE_SERVICE_USE_AMOUNT,WECHAT_GIVE_CREDIT,
        HQ_VW_PROPERTY_CODE,IS_UPDATE,CREATE_BY,
        CREATE_DATE,D_KEY,UPDATE_BY,
        UPDATE_DATE,VER,ADDITIONAL_AMOUNT,
        CONTAIN_INSURANCE,RELATE_INSURATION_NAME,MAINTAIN_SUGGEST,
        WARRANTY_DAYS,WARRANTY_MILEAGE,IS_DECLARE,
        REAL_RECEIVE_LABOUR_AMOUNT,REAL_RECEIVE_REPAIR_PART_AMOUNT,ORDER_PRICE_AMOUNT,
        NEXT_MITAIN_MILEAGE,NEXT_MITAIN_DATE,PRE_SERVICE_AMOUNT,
        RIGHT_AMOUNT,RIGHT_RMB_AMOUNT,FIRST_PRINT_DATE,
        VIN,OWNER_NAME,OWNER_MOBILE,
        OWNER_APP_IDPID,DELIVERER,DELIVERER_MOBILE,
        DELIVERER_APP_IDPID,SYNC_TIME
    </sql>
</mapper>
