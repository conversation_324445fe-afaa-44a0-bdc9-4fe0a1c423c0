package com.car.voc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.voc.entity.SysDictItem;
import com.car.voc.vo.EnergyClassificationVo;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface SysDictItemMapper extends BaseMapper<SysDictItem> {
    @Select("SELECT * FROM sys_dict_item WHERE DICT_ID = #{mainId} order by sort_order asc, item_value asc")
    public List<SysDictItem> selectItemsByMainId(String mainId);
    @Select("select t3.*,sdi.item_text as type2Name  ,sdi.item_value ,sdi.description relevancy from sys_dict_item sdi right join ( select sd.id erid,er.item_text type1Name,er.item_value,er.id,er.dict_id from sys_dict sd right join ( select * from sys_dict_item sdi where sdi.dict_id  =(select distinct id from sys_dict sd where sd.dict_name ='能源类型' ) ) er on sd.dict_name  =er.item_text ) t3 on sdi.dict_id =t3.erid ORDER BY ISNULL(sdi.dict_id) ASC, sdi.dict_id, sdi.sort_order asc")
    List<EnergyClassificationVo> energyClassification();
}
