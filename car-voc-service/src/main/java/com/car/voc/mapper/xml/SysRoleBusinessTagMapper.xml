<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.SysRoleBusinessTagMapper">

    <resultMap id="BaseResultMap" type="com.car.voc.entity.SysRoleBusinessTag">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="roleId" column="ROLE_ID" jdbcType="VARCHAR"/>
            <result property="tagCode" column="TAG_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ROLE_ID,TAG_CODE
    </sql>


    <select id="queryRoleNameByTagCode"  resultType="com.car.voc.vo.risk.TagRoleVo">
        SELECT
        d.id as roleId,
        d.ROLE_NAME as roleName
        FROM
        SYS_ROLE_BUSINESS_TAG  td
        LEFT JOIN SYS_ROLE d ON td.ROLE_ID=d.ID
        WHERE
        1=1
          and d.ROLE_NAME is not null
          and td.del_flag= '0'
          and d.del_flag= '0'
        <if test="codes!=null  and codes.length>0">
            and td.TAG_CODE IN
            <foreach collection="codes" index="index" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

    </select>

</mapper>
