<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.TagDepartRelationMapper">



	<select id="queryDepartNameByTagCode"  resultType="java.lang.String">
		SELECT
		d.DEPART_NAME as departName
		FROM
		VOC_TAG_DEPART_RELATION  td
		LEFT JOIN SYS_DEPART d ON td.depart_id=d.ID
		WHERE
		1=1
		<if test="codes!=null  and codes.length>0">
			and td.TAG_CODE IN
			<foreach collection="codes" index="index" item="code" open="(" separator="," close=")">
				#{code}
			</foreach>
		</if>

	</select>

</mapper>
