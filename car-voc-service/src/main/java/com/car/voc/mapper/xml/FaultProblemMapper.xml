<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.voc.mapper.FaultProblemMapper">
    <update id="updateTreeNodeStatus" parameterType="java.lang.String">
        update VOC_FAULT_PROBLEM set has_child = #{status} where id = #{id}
    </update>
    <sql id="querySelectFeild">
        g4.id, g4.pid,  g4.describe
    </sql>

    <select id="queryByPage"  resultType="com.car.voc.vo.FaultProblemListVo">
        SELECT
        g1.name as problemName1,
        g1.CODE as problemCode1,
        g2.name as problemName2,
        g2.CODE as problemCode2,
        g3.name as problemName3,
        g3.CODE as problemCode3,
        g4.name as problemName4,
        g4.CODE as problemCode4,
        g4.enable as enable,
        g4.seriousness,
        g4.LAST_TAG_CODE AS lastTagCode,
        tg.NAME AS lastTagName,
        g4.CODE as code,
        g4.NAME as name,
        g4.order_type as orderType,
        g4.related_departments as relatedDepartments,
        g4.tag_scope as tagScope,
      <include refid="querySelectFeild" />

        FROM
        VOC_FAULT_PROBLEM g4
        LEFT JOIN VOC_FAULT_PROBLEM g3 ON g4.PID=g3.ID
        LEFT JOIN VOC_FAULT_PROBLEM g2 ON g3.PID=g2.ID
        LEFT JOIN VOC_FAULT_PROBLEM g1 ON g2.PID=g1.ID
        LEFT JOIN VOC_BUSINESS_TAG tg ON g4.LAST_TAG_CODE=tg.TAG_CODE
        WHERE
        1=1
        AND g1.CODE IS NOT NULL
        <if test="model.pid != null">
            AND g1.PID = #{model.pid}
        </if>
        <if test="model.searchKeyword != null and model.searchKeyword != ''">
            AND
            (
            g1.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            g2.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            g3.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')  OR
            g4.NAME LIKE CONCAT('%', #{model.searchKeyword} ,'%')
            )
        </if>

    </select>

    <select id="getItemById"  resultType="com.car.voc.vo.FaultProblemVo">
        SELECT
        tg1.name as firstDimensionName,
        tg1.TAG_CODE as firstDimensionCode,
        tg2.name as secondDimensionName,
        tg2.TAG_CODE as secondDimensionCode,
        tg3.name as thirdDimensionName,
        tg3.TAG_CODE as thirdDimensionCode,
        tg4.name as topicName,
        tg4.TAG_CODE as topicCode,
        g1.name as problemName1,
        g1.CODE as problemCode1,
        g2.name as problemName2,
        g2.CODE as problemCode2,
        g3.name as problemName3,
        g3.CODE as problemCode3,
        g4.name as problemName4,
        g4.CODE as problemCode4,
        g4.enable as enable,
        g4.seriousness,
        g4.describe,
        g4.LAST_TAG_CODE AS lastTagCode,
        g4.CODE as code,
        g4.NAME as name,
        <include refid="querySelectFeild" />
        FROM
        VOC_FAULT_PROBLEM g4
        LEFT JOIN VOC_FAULT_PROBLEM g3 ON g4.PID=g3.ID
        LEFT JOIN VOC_FAULT_PROBLEM g2 ON g3.PID=g2.ID
        LEFT JOIN VOC_FAULT_PROBLEM g1 ON g2.PID=g1.ID
        LEFT JOIN VOC_BUSINESS_TAG tg4 ON g4.LAST_TAG_CODE=tg4.TAG_CODE
        LEFT JOIN VOC_BUSINESS_TAG tg3 ON tg4.PID=tg3.ID
        LEFT JOIN VOC_BUSINESS_TAG tg2 ON tg3.PID=tg2.ID
        LEFT JOIN VOC_BUSINESS_TAG tg1 ON tg2.PID=tg1.ID
        WHERE
        1=1
        AND g1.CODE IS NOT NULL
        AND tg1.TAG_CODE is NOT NULL
        AND g4.ID= #{id}
    </select>

</mapper>
