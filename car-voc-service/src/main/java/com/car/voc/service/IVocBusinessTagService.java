package com.car.voc.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.dto.TagCacheDto;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.exception.BootException;
import com.car.voc.model.VocBusinessTagListQueryModel;
import com.car.voc.model.VocBusinessTagModel;
import com.car.voc.vo.InternationalVo;
import com.car.voc.vo.VocBusinessTagListVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.auth.RoleAuthTree;
import com.car.voc.vo.thirdVo.VocTagVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * @Date:   2021-03-30
 * @Version: V1.0
 */
public interface IVocBusinessTagService extends IService<VocBusinessTag> {

	/**根节点父ID的值*/
	public static final String ROOT_PID_VALUE = "0";

	/**树节点有子节点状态值*/
	public static final String HASCHILD = "1";

	/**树节点无子节点状态值*/
	public static final String NOCHILD = "0";

	/**新增节点*/
	void addVocBusinessTag(VocBusinessTagModel vocBusinessTagModel);

	/**修改节点*/
	void updateVocBusinessTag(VocBusinessTagModel vocBusinessTagModel) throws BootException;

	/**删除节点*/
	void deleteVocBusinessTag(String id) throws BootException;

    String getNameByCode(String zbCode);

    List<VocBusinessTagVo> queryTreeList(QueryWrapper<VocBusinessTag> queryWrapper);

	IPage<VocBusinessTagListVo> queryByPage(Page<VocBusinessTagListVo> page, VocBusinessTagListQueryModel vocBusinessTag, HttpServletRequest req);

    Result<?> batchImport(MultipartFile file);

	String queryIdByTagBrandAndCode(String brand, String parentTagCode);

	boolean addRecord(VocBusinessTag record);

	String getAllName(String labelCode);

	List<VocBusinessTagModel> findAll();

	VocBusinessTag selectByTagId(String tagId);

    IPage<VocTagVo> vocTags(Page<VocTagVo> page, VocBusinessTagListQueryModel vocBusinessTag, HttpServletRequest req);

    List<VocBusinessTagVo> getAllTags(List<String> tagCodes);

	public Set<TagCacheDto> businessTagAll();
}
