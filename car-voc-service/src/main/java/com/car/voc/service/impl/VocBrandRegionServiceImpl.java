package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.SysDictItem;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.mapper.VocBrandRegionMapper;
import com.car.voc.service.VocBrandRegionService;
import com.car.voc.vo.RegionalDictVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * (VocBrandRegion)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-20 17:03:47
 */
@Service
public class VocBrandRegionServiceImpl extends ServiceImpl<VocBrandRegionMapper, VocBrandRegion> implements VocBrandRegionService {
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<RegionalDictVo> queryBrandRegion(String brandCode, String id) {
        return baseMapper.queryBrandRegion(brandCode, id);
    }

    private void buildChildren(RegionalDictVo parent, List<RegionalDictVo> allNodes, int i, int a) {
        // 查找当前节点的子节点
        List<RegionalDictVo> children = allNodes.stream()
                .filter(node -> parent.getValue().equals(node.getPid()) && node.getChecked().equals(1))
                .collect(Collectors.toList());
        if (!children.isEmpty()) {
            parent.setChildes(children);
            // 递归处理每个子节点
            int finalI = i;
            children.forEach(child -> buildChildren(child, allNodes, finalI, a));
        }
    }

    @Override
    public List<RegionalDictVo> setChildes(Integer a, List<RegionalDictVo> regionalDictVos) {
        List<RegionalDictVo> retree = regionalDictVos.stream().filter(e -> "0".equals(e.getPid()) && e.getChecked().equals(1)).collect(Collectors.toList());
        // 递归构建树结构
        int i = 0;
        retree.forEach(node -> {
            buildChildren(node, regionalDictVos, i, a);
        });
        return retree;
    }

    @Override
    public List<SysDictItem> queryInternational() {
        return baseMapper.queryInternational();
    }

    @Override
    public Set<VocBrandRegion> brandRegionAll(String code) {
        String formatKey = String.format(CacheConstant.SYS_AREA_BRANDCODE_PROVINCE_ALL, code);
        if (redisUtil.hasKey(formatKey)) {
            Set<Object> seriesAll = redisUtil.sGet(formatKey);
            return seriesAll.stream().map(e -> (VocBrandRegion) e).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<VocBrandRegion> query = new QueryWrapper<VocBrandRegion>().lambda();
        query.select(VocBrandRegion::getProvinceCode, VocBrandRegion::getProvinceName,
                VocBrandRegion::getRegionalCode, VocBrandRegion::getRegionalName,
                VocBrandRegion::getCityCode, VocBrandRegion::getCityName,
                VocBrandRegion::getCommunityCode, VocBrandRegion::getCommunityName);
        query.eq(VocBrandRegion::getBrand, code);
        Set<VocBrandRegion> vocBrandRegions = new HashSet<>(this.list(query));
        redisUtil.sSetAllObject(formatKey, vocBrandRegions);
        return vocBrandRegions;
    }

    @Override
    public List<VocBrandRegion> cacheBrandRegionAll() {
        List<VocBrandRegion> brandRegionlist = (List<VocBrandRegion>) redisUtil.get(CacheConstant.SYS_AREA_PROVINCE_ALL);
        if (CollectionUtils.isEmpty(brandRegionlist)){
            brandRegionlist= this.list();
            redisUtil.set(CacheConstant.SYS_AREA_PROVINCE_ALL,brandRegionlist);
        }
        return brandRegionlist;
    }

}
