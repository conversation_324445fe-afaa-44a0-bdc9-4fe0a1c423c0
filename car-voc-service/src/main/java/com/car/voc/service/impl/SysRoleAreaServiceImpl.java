package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.SysRoleArea;
import com.car.voc.entity.SysRoleSeries;
import com.car.voc.mapper.SysRoleAreaMapper;
import com.car.voc.service.SysRoleAreaService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
*
*/
@Service
public class SysRoleAreaServiceImpl extends ServiceImpl<SysRoleAreaMapper, SysRoleArea> implements SysRoleAreaService{

    @Override
    public void saveOrUpdateData(String roleId, List<String> tagIdList,String brandCode){
        if(tagIdList == null) {
            return;
        }
        QueryWrapper<SysRoleArea> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRoleArea::getRoleId,roleId).eq(SysRoleArea::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
        List<SysRoleArea> list = this.list(queryWrapper);
        if(!CollectionUtils.isEmpty(list)){
            UpdateWrapper<SysRoleArea> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SysRoleArea::getRoleId,roleId);
            updateWrapper.lambda().eq(SysRoleArea::getBrandCode,brandCode).eq(SysRoleArea::getDelFlag,CommonConstant.DEL_FLAG_STR_0)
                    .set(SysRoleArea::getDelFlag, CommonConstant.DEL_FLAG_STR_1);
            this.update(updateWrapper);
        }
        List<SysRoleArea> arp=new ArrayList<>();
        tagIdList.stream().forEach(tagCode ->{
            SysRoleArea sysRoleBusinessTag = new SysRoleArea();
            sysRoleBusinessTag.setCode(tagCode);
            sysRoleBusinessTag.setRoleId(roleId);
            sysRoleBusinessTag.setBrandCode(brandCode);
            arp.add(sysRoleBusinessTag);
        });
        this.saveBatch(arp);
    }
}
