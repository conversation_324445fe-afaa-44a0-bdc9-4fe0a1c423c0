package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.entity.VocRiskWarningRecord;
import com.car.voc.mapper.VocRiskWarningRecordMapper;
import com.car.voc.service.IVocRiskWarningRecordService;
import com.car.voc.vo.risk.RiskAllTypesVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName IVocRiskWarningRecordImpl.java
 * @Description TODO
 * @createTime 2023年02月09日 15:34
 * @Copyright voc
 */
@Service
public class IVocRiskWarningRecordImpl extends ServiceImpl<VocRiskWarningRecordMapper, VocRiskWarningRecord> implements IVocRiskWarningRecordService {
    @Override
    public List<Object> riskWarningNum(RiskAllTypesVo e) {
        LambdaQueryWrapper<VocRiskWarningRecord> wrapper=new LambdaQueryWrapper<>();
        wrapper.select(VocRiskWarningRecord::getCreateTime);
        wrapper.eq(VocRiskWarningRecord::getRisk,e.getRisk());
        wrapper.orderByDesc(VocRiskWarningRecord::getCreateTime);
        return this.baseMapper.selectObjs(wrapper);
    }
}
