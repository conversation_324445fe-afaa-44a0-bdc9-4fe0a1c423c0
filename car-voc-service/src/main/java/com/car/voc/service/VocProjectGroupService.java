package com.car.voc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.VocProjectGroup;
import com.car.voc.entity.VocProjectGroupMember;
import com.car.voc.vo.DictVo;

import java.util.List;

/**
 * 项目组信息表(VocProjectGroup)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-02 14:31:10
 */
public interface VocProjectGroupService extends IService<VocProjectGroup> {

    void saveProjectGroup(VocProjectGroup projectGroup);

    Page<VocProjectGroup> pageList(Page<VocProjectGroup> page, String brand, String keyword);

    void updateProjectGroup(VocProjectGroup projectGroup);

    List<VocProjectGroup> projectGroupAndMember(String brandCode);

    List<VocProjectGroupMember> projectMembers(String projectId);

    List<DictVo> buildProjectTree(String brandCode);
}

