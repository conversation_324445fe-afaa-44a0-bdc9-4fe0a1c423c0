package com.car.voc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.entity.VocRiskHandlingRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName IVocRiskHandlingRecordService.java
 * @Description TODO
 * @createTime 2023年02月07日 11:35
 * @Copyright voc
 */
public interface IVocRiskHandlingRecordService extends IService<VocRiskHandlingRecord> {
    List<VocRiskHandlingRecord> listExtend(QueryWrapper<VocRiskHandlingRecord> wrapper);

    BigDecimal getToBeConfirmed(FilterCriteriaModel model);

    List<VocRiskHandlingRecord>  selectProcessingRecords(String id);

    List<VocRiskHandlingRecord> selectByWarningRiskId(String id);

    Integer deleteById(String id);

    void vocRiskProcessTimeoutRemindJob();
}
