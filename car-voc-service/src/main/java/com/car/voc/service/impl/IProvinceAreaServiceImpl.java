package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.ProvinceArea;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.mapper.ProvinceAreaMapper;
import com.car.voc.service.IProvinceAreaService;
import com.car.voc.service.ISysDictService;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.NewDictVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName ProvinceAreaServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月21日 16:54
 * @Copyright voc
 */
@Service("iProvinceAreaServiceImpl")
public class IProvinceAreaServiceImpl extends ServiceImpl<ProvinceAreaMapper, ProvinceArea> implements IProvinceAreaService {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ISysDictService sysDictService;
    @Override
    public List<DictVo> queryProvinceByAreaCode(String areaCode,String brandCode) {
        List<DictVo> re= (List<DictVo>) redisUtil.get(CacheConstant.sys_area_brand_province+areaCode+":"+brandCode);
        if (re!=null&&re.size()>0){
            return re;
        }else {
            re= baseMapper.queryProvinceByAreaCode(areaCode,brandCode);
            redisUtil.set(CacheConstant.sys_area_brand_province+areaCode+":"+brandCode,re,CacheConstant.expire);
        return re;
        }
    }

    @Override
    public String queryAreaByProvince(String province) {
        Object name=redisUtil.get(CacheConstant.sys_province_area+province);
        if (!StrUtil.isBlankIfStr(name)){
            return name+"";
        }else {
            List<ProvinceArea> areas=this.list();
            for (ProvinceArea provinceArea : areas) {
                redisUtil.set(CacheConstant.sys_province_area+provinceArea.getProvinceCode(),sysDictService.queryDictTextByKey("area",provinceArea.getAreaCode()),CacheConstant.expire);
            }
           return redisUtil.get(CacheConstant.sys_province_area+province)+"";
        }
    }

    @Override
    public List<NewDictVo> newQueryProvinceByAreaCode(String brandCode) {
        return baseMapper.newQueryProvinceByAreaCode(brandCode);
    }
    @Override
    public Map<String,String> queryAreaByBrandProvince(String brandCode) {
        List<NewDictVo> lists=baseMapper.newQueryProvinceByAreaCode(brandCode);
        Map<String,String> ar=new HashMap<>();
        for (NewDictVo area : lists) {
            if (area!=null&&area.getProvinceCodes()!=null){
                String[] prcode=area.getProvinceCodes().split(",");
                for (String brand : area.getBrandCode().split(",")) {
                    for (String provice : prcode) {
                        ar.put((brand+provice).replace(" ",""),area.getItemText());
                    }
                }
            }

        }

        return ar;
    }
}
