package com.car.voc.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.ModelGroupRelation;

import java.util.List;
import java.util.Set;

/**
 * @Description: 车型组关联
 *
 * @Date:   2021-04-09
 * @Version: V1.0
 */
public interface IModelGroupRelationService extends IService<ModelGroupRelation> {


    Set<String> queryCarNameByModelGroupId(String id);

    List<BrandProductManager> queryCarByModelGroupId(String id);
}
