package com.car.voc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.vo.VocChannelCategoryVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName IVocChannelCategoryService.java
 * @Description TODO
 * @createTime 2022年10月17日 22:31
 * @Copyright voc
 */
public interface IVocChannelCategoryService extends IService<VocChannelCategory> {
    Result<?> getAllChannels(String brandCode);

    VocChannelCategory getByIdCache(String id);

    IPage<VocChannelCategoryVo> queryByPage(Page<VocChannelCategoryVo> page, VocChannelCategory channelCategory, HttpServletRequest req);

    boolean add(VocChannelCategory model);

    boolean edit(VocChannelCategory model);

    boolean delete(String id);

    List<VocChannelCategory> cacheList();

    List<VocChannelCategory> findAll();
}
