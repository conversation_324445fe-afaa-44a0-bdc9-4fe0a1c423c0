package com.car.voc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.dto.TagCacheDto;
import com.car.voc.entity.FaultProblem;
import com.car.voc.model.FaultProblemAddModel;
import com.car.voc.model.FaultProblemListQueryModel;
import com.car.voc.vo.FaultProblemListVo;
import com.car.voc.vo.FaultProblemTreeVo;
import com.car.voc.vo.FaultProblemVo;
import com.car.voc.vo.InternationalVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName IFaultProblemService.java
 * @Description TODO
 * @createTime 2022年10月09日 13:50
 * @Copyright voc
 */
public interface IFaultProblemService extends IService<FaultProblem> {
    IPage<FaultProblemListVo> queryByPage(Page<FaultProblemListVo> page, FaultProblemListQueryModel queryModel, HttpServletRequest req);

    List<FaultProblemTreeVo> queryTreeList(QueryWrapper<FaultProblem> queryWrapper);

    void add(FaultProblemAddModel addModel);

    void edit(FaultProblemAddModel edit);

    void delete(String id);

    FaultProblemVo getItemById(String id);

    String getNameByCode(String topicCode);

    List<InternationalVo> internationalCnTags();

    List<InternationalVo> internationalEnTags();

    Result<?> batchImport(MultipartFile file);

    List<String> queryTopicCodesByTopicName(String topic);

    List<String> getTags2All();

    Set<TagCacheDto> faultProblemAll();
}
