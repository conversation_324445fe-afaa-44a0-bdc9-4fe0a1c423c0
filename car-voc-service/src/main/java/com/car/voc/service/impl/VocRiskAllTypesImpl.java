package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.mapper.DwsVocQualityUserDiMapper;
import com.car.stats.model.ComFilterCriteriaModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.*;
import com.car.stats.vo.ChannelVo;
import com.car.stats.vo.DateStiticVo;
import com.car.stats.vo.HighHotWordsVo;
import com.car.stats.vo.TrendChannelRiskVo;
import com.car.stats.vo.risk.DataDryingContrastVo;
import com.car.stats.vo.risk.EmotionIntentionVo;
import com.car.stats.vo.risk.RiskBriefingVo;
import com.car.stats.vo.risk.RiskDataDryingVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.DateUtils;
import com.car.voc.common.util.IDateUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.mapper.*;
import com.car.voc.model.LoginUser;
import com.car.voc.model.risk.RiskAlertReviewerModel;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.service.*;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.FaultProblemTreeVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.risk.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @version 1.0.0
 * @ClassName VocRiskWarningRecordImpl.java
 * @Description TODO
 * @createTime 2023年02月07日 11:31
 * @Copyright voc
 */

@Service
@Slf4j
@SuppressWarnings("all")
public class VocRiskAllTypesImpl extends ServiceImpl<VocRiskAllTypesMapper, VocRiskAllTypes> implements IVocRiskAllTypesService {
    @Autowired
    IVocRiskHandlingRecordService handlingRecordService;
    @Resource
    DwsVocEmotionUserDiMapper userDiMapper;
    @Resource
    DwsVocQualityUserDiMapper qualityUserDiMapper;
    @Autowired
    IVocRiskWarningRecordService warningRecordService;
    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    IDwdVocUserRiskService userRiskService;

    @Autowired
    IDwdVocDealerRiskService dealerRiskService;
    @Autowired
    IDwdVocQualityRiskService qualityRiskService;
    @Autowired
    IDimDateService dimDateService;

    @Autowired
    ISysUserService userService;

    @Autowired
    private IVocRiskAlertReviewerService riskAlertReviewerService;
    @Autowired
    IVocRiskAllTypesService riskAllTypesService;

    @Autowired
    IVocRiskProessRecipientService recipientService;

    @Autowired
    VocRiskProessRecipientMapper vocRiskProessRecipientMapper;

    @Autowired
    private IVocRiskProessRecipientService riskProessRecipientService;

    @Autowired
    private IBrandProductManagerService brandProductManagerService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private IFaultProblemService faultProblemService;

    @Autowired
    private IVocBusinessTagService vocBusinessTagService;

    @Autowired
    private IVocBusinessTagService businessTagService;

    @Autowired
    private SysRoleBusinessTagMapper sysRoleBusinessTagMapper;

    @Autowired
    private VocRiskAlertReviewerMapper reviewerMapper;

    @Autowired
    private VocProjectGroupService projectGroupService;


    @Autowired
    RedisUtil redisUtil;


    @Override
    public IPage<RiskAllTypesVo> taskProcessingList(RiskAllTypesModel model) {
        String userId = getUserInfo().getId();
        String brandCode = model.getBrandCode();
        SysRole role = sysRoleService.getRoleByUserId(userId);
        // 1为管理层人员；2为执行层人员
        String roleType = "";
        if (ObjectUtils.isNotEmpty(role)) {
            //1为管理层人员；2为执行层人员
            roleType = role.getRoleType();
        }
        if (ObjectUtils.isNotEmpty(model.getRiskState())) {
            if (model.getRiskState() == 0) {
                model.setRiskIdList(null);
                model.setRiskStateStr(Arrays.asList("0"));
            } else if (model.getRiskState() == 1) {
                model.setRiskStateStr(new ArrayList<>());
                model.setProcessStatus("0");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
            } else if (model.getRiskState() == 3) {
                model.setRiskStateStr(new ArrayList<>());
            } else if (model.getRiskState() == 4) {
                model.setRiskStateStr(new ArrayList<>());
                model.setProcessStatus("2");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
            } else if (model.getRiskState() == 5) {
                model.setRiskStateStr(new ArrayList<>());
                model.setIfRiskType("0");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
            }
        }
        //查询自己是否有审核的权限
        IPage<RiskAllTypesVo> riskAllTypesVos = null;
        Page<RiskAllTypesVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        model.setQualityBoolean(false);
        if (StringUtils.isNotEmpty(role.getQualityText()) && StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(role.getQualityText());
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                model.setQualityBoolean(o);
            }
        }
        model.setRoleId(role.getId());
        if (roleType.equals("1")) {
            riskAllTypesVos = this.baseMapper.riskListManage(page, model);
        } else {
            QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, userId)
                    .eq(VocRiskAlertReviewer::getDelFlag, CommonConstant.DEL_FLAG_0);
            boolean re = riskAlertReviewerService.count(wrapper) > 0;
            // 查询自己是否在处理记录里
            QueryWrapper<VocRiskHandlingRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(VocRiskHandlingRecord::getProcessUserId, userId);
            boolean pro = handlingRecordService.count(queryWrapper) > 0;
            if (!re && !pro) {
                return new Page<>();
            }
            // 处理人
            if ("2".equals(roleType) && !re) {
                model.setProcessUserId(userId);
                riskAllTypesVos = baseMapper.riskListProcessingPersonnel(page, model);
            } else {
                // 审核人
                RiskAlertReviewerVo riskAlert = baseMapper.getRiskAlert(userId);
                String brandCodes = riskAlert.getBrandCodes();
                model.setProcessUserId(userId);
                if (StringUtils.isBlank(brandCodes) || !brandCodes.contains(brandCode)) {
                    return new Page<>();
                }
                riskAllTypesVos = this.baseMapper.riskListReviewer(page, model);  //审核人
            }
        }
        if (riskAllTypesVos == null || riskAllTypesVos.getRecords().size() == 0) {
            return riskAllTypesVos;
        }
        this.comRiskListBatch(riskAllTypesVos);
        return riskAllTypesVos;
    }

    private void comRiskList(IPage<RiskAllTypesVo> relist) {
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd")
                .withZone(ZoneId.systemDefault());

        Map<String, List<SysUser>> list = userService.list().stream().collect(Collectors.groupingBy(SysUser::getId));
        Map<String, List<SysDepart>> departList = departService.list().stream().collect(Collectors.groupingBy(SysDepart::getId));
        Map<String, VocProjectGroup> projectList = projectGroupService.list().stream().collect(Collectors.toMap(VocProjectGroup::getId, Function.identity()));
        relist.getRecords().forEach(e -> {

            if (Objects.isNull(e.getAuditTime()) && Objects.nonNull(e.getCancelTime())) {
                e.setAuditTime(e.getCancelTime());
            }
            if (StringUtils.isBlank(e.getProcessingFrequency())) {
                e.setProcessingFrequency("0");
            }
            setRiskProcessRecords(e);
            setUserNum(e);
            List<WarningReviewList> warningReviewListList = new ArrayList<>();
            if (e.getRiskHandlingRecords() != null && e.getRiskHandlingRecords().size() > 0) {
                e.getRiskHandlingRecords().forEach(k -> {

                    if (k.getProcessUserId() != null) {
                        List<SysUser> us = list.get(k.getProcessUserId());
                        if (us != null && us.size() > 0) {
                            SysUser user = us.get(0);
                            k.setProcessUserName(user.getRealname());
                        }
                        List<SysDepart> departs = new ArrayList<>();
                        VocProjectGroup project = new VocProjectGroup();
                        if (k.getBindType() == 1) {
                            departs.addAll(departList.get(k.getProcessDepartId()));
                            if (departs != null && departs.size() > 0) {
                                SysDepart de = departs.get(0);
                                k.setProcessDepartName(de.getDepartName());
                            }
                        } else {
                            project = projectList.get(k.getProcessDepartId());
                            if (project != null) {
                                k.setProcessDepartName(project.getProjectName());
                            }
                        }

                        WarningReviewList warningReviewList = new WarningReviewList();
                        if (k.getProcessStatus() != 3) {
                            SysUser user = us.get(0);
                            if (k.getBindType() == 1) {
                                SysDepart de = departs.get(0);
                                warningReviewList.setProcessDepartName(de.getDepartName());
                            } else {
                                project = projectList.get(k.getProcessDepartId());
                                warningReviewList.setProcessDepartName(project.getProjectName());
                            }
                            warningReviewList.setProcessUserName(user.getRealname());
                            warningReviewList.setProcessUserId(k.getProcessUserId());
                            warningReviewListList.add(warningReviewList);
                        }
                    }

                });
                e.getRiskHandlingRecords()
                        .sort(Comparator.comparing(VocRiskHandlingRecord::getCreateTime, Comparator.nullsFirst(Date::compareTo))
                                .reversed());
            }

            e.setWarningReviewListList(warningReviewListList);
            //处理时长
            /**
             * 未完成时（有部门未完成），显示开始时间（最先执行部门的开始时间）；
             * 已完成时（所有部门已完成），显示天数及日期（最先执行部门的开始时间及最后执行部门的结束时间）
             * */
            String dateTime = "";
            String dateDay = "";
            if (e.getRiskState() == 1 || e.getRiskState() == 4 || e.getRiskState() == 7) {
                dateTime = dateFormat.format(e.getAuditTime().toInstant()) + "-";
            } else if (e.getRiskState() == 3 && Objects.nonNull(e.getAuditTime()) && Objects.nonNull(e.getConfirmationTime())) {

                dateTime = dateFormat.format(e.getAuditTime().toInstant()) + "-" + dateFormat.format(e.getConfirmationTime().toInstant());
                DateTime parse = DateUtil.parse(dateFormat.format(e.getAuditTime().toInstant()));
                DateTime parse1 = DateUtil.parse(dateFormat.format(e.getConfirmationTime().toInstant()));
                dateDay = parse.between(parse1, DateUnit.DAY) + 1 + "";
            } else {
                dateTime = "-";
            }
            e.setProcessingTime(dateTime);
            e.setProcessingDays(dateDay);
        });
    }


    @Autowired
    ISysRoleService sysRoleService;
    @Autowired
    ISysDepartService sysDepartService;

    @Override
    public IPage<RiskAllTypesVo> auditList(Page<RiskAllTypesVo> page, RiskAllTypesModel model) {
        SysRole role = sysRoleService.getRoleByUserId(getUserInfo().getId());
        if (role.getRoleType() != null && "1".equals(role.getRoleType())) {
            model.setUserId(null);
        } else {
            model.setUserId(getUserInfo().getId());
        }

        if (ObjectUtils.isNotEmpty(model.getColumn()) && !"statistic".equalsIgnoreCase(model.getColumn())) {
            model.setColumn(null);
        }
        if (ObjectUtils.isNotEmpty(model.getOrder()) && !"asc".equalsIgnoreCase(model.getOrder()) && !"desc".equalsIgnoreCase(model.getOrder())) {
            model.setOrder(null);
        }

        IPage<RiskAllTypesVo> relist = this.baseMapper.auditList(page, model);
        comRiskList(relist);
        return relist;
    }

    @Override
    public List<RiskAllTypesVo> taskListTop(FilterCriteriaModel model) {
        Page<RiskAllTypesVo> page = new Page<>(1, 10);

        RiskAllTypesModel riskAllTypesModel = new RiskAllTypesModel();
        BeanUtil.copyProperties(model, riskAllTypesModel);
        IPage<RiskAllTypesVo> relist = riskAllTypesService.riskList(page, riskAllTypesModel);
        comRiskList(relist);
        return relist.getRecords();
    }

    @Override
    public IPage<RiskAllTypesVo> riskList(Page<RiskAllTypesVo> page, RiskAllTypesModel model) {
        IPage<RiskAllTypesVo> relist = new Page<>();
        relist.setSize(model.getPageSize());
        relist.setCurrent(model.getPageNo());

        // 获取基础数据
        String userId = getUserInfo().getId();
        String brandCode = model.getBrandCode();

        // 异步获取用户角色信息
        CompletableFuture<String> roleTypeFuture = CompletableFuture.supplyAsync(() -> {
            SysRole role = sysRoleService.getRoleByUserId(userId);
            return ObjectUtils.isNotEmpty(role) ? role.getRoleType() : "";
        });

        // 异步检查审核权限
        CompletableFuture<Boolean> reviewerCheckFuture = CompletableFuture.supplyAsync(() -> {
            QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .eq(VocRiskAlertReviewer::getReviewerUserId, userId)
                    .eq(VocRiskAlertReviewer::getDelFlag, CommonConstant.DEL_FLAG_0);
            return riskAlertReviewerService.count(wrapper) > 0;
        });

        // 异步检查处理记录
        CompletableFuture<Boolean> processingCheckFuture = CompletableFuture.supplyAsync(() -> {
            QueryWrapper<VocRiskHandlingRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(VocRiskHandlingRecord::getProcessUserId, userId);
            return handlingRecordService.count(queryWrapper) > 0;
        });


        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(roleTypeFuture, reviewerCheckFuture, processingCheckFuture);
        try {
            voidCompletableFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        // 等待所有异步操作完成
        String roleType = roleTypeFuture.join();
        boolean re = reviewerCheckFuture.join();
        boolean pro = processingCheckFuture.join();

        // 处理风险状态
        processRiskState(model, userId);

        // 获取风险列表
        List<RiskAllTypesVo> riskAllTypesVos = getRiskListByRole(roleType, re, pro, model, userId);
        if (CollectionUtil.isEmpty(riskAllTypesVos)) {
            return relist;
        }

        // 并行处理权限过滤
        if (re) {
            riskAllTypesVos = processPermissions(riskAllTypesVos, userId, brandCode);
        }

        // 处理部门审核过滤
        if (ObjectUtils.isNotEmpty(model.getAuditDepartId())) {
            riskAllTypesVos = filterByAuditDepartment(riskAllTypesVos, model.getAuditDepartId());
        }

        // 处理处理频率过滤
        if (StringUtils.isNotBlank(model.getProcessingFrequency())) {
            riskAllTypesVos = riskAllTypesVos.parallelStream()
                    .filter(r -> r.getProcessingFrequency().equals(model.getProcessingFrequency()))
                    .collect(Collectors.toList());
        }

        // 分页处理
        return paginateResults(riskAllTypesVos, model, relist);
    }

    // 处理权限相关的过滤
    private List<RiskAllTypesVo> processPermissions(List<RiskAllTypesVo> riskAllTypesVos, String userId, String brandCode) {
        CompletableFuture<List<VocRiskProcessRecipientTag>> businessTagsFuture = CompletableFuture
                .supplyAsync(() -> riskProessRecipientService.queryPermissionByUserId(userId));

        CompletableFuture<List<VocRiskProcessRecipientBase>> qualityTagsFuture = CompletableFuture
                .supplyAsync(() -> riskProessRecipientService.queryPermissionQualityByUserId(userId));


        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(businessTagsFuture, qualityTagsFuture);
        try {
            voidCompletableFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }

        List<VocRiskProcessRecipientTag> recipientTags = businessTagsFuture.join();
        List<VocRiskProcessRecipientBase> recipientQualityTags = qualityTagsFuture.join();

        // 并行处理业务标签过滤
        if (CollectionUtil.isNotEmpty(recipientTags)) {
            riskAllTypesVos = processBusinessTags(riskAllTypesVos, recipientTags, brandCode);
        }

        // 并行处理质量标签过滤
        if (CollectionUtil.isNotEmpty(recipientQualityTags)) {
            riskAllTypesVos = processQualityTags(riskAllTypesVos, recipientQualityTags, brandCode);
        }

        return riskAllTypesVos;
    }

    // 分页处理
    private IPage<RiskAllTypesVo> paginateResults(List<RiskAllTypesVo> riskAllTypesVos,
                                                  RiskAllTypesModel model,
                                                  IPage<RiskAllTypesVo> relist) {
        int total = riskAllTypesVos.size();
        List<List<RiskAllTypesVo>> lists = Lists.partition(riskAllTypesVos, model.getPageSize());

        List<RiskAllTypesVo> pageData = new ArrayList<>();
        if (model.getPageNo() <= lists.size() && model.getPageNo() >= 1) {
            pageData = lists.get(model.getPageNo() - 1);
        }

        relist.setRecords(pageData);
        relist.setTotal(total);
        relist.setCurrent(model.getPageNo());
        return relist;
    }

    // 其他辅助方法...
    // 处理风险状态
    private void processRiskState(RiskAllTypesModel model, String userId) {
        if (ObjectUtils.isEmpty(model.getRiskState())) {
            return;
        }

        switch (model.getRiskState()) {
            case 0:
                model.setRiskIdList(null);
                model.setRiskStateStr(Arrays.asList("0"));
                break;
            case 1:
                model.setRiskStateStr(new ArrayList<>());
                model.setProcessStatus("0");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
                break;
            case 3:
                model.setRiskStateStr(new ArrayList<>());
                break;
            case 4:
                model.setRiskStateStr(new ArrayList<>());
                model.setProcessStatus("2");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
                break;
            case 5:
                model.setRiskStateStr(new ArrayList<>());
                model.setIfRiskType("0");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
                break;
        }
    }

    // 根据角色获取风险列表
    private List<RiskAllTypesVo> getRiskListByRole(String roleType, boolean hasReviewPermission,
                                                   boolean hasProcessRecord, RiskAllTypesModel model,
                                                   String userId) {
        // 管理层人员
        if ("1".equals(roleType)) {
            return this.baseMapper.riskList(model);
        }

        // 无权限返回空
        if (!hasReviewPermission && !hasProcessRecord) {
            return Collections.emptyList();
        }

        // 执行层人员且无审核权限
        if ("2".equals(roleType) && !hasReviewPermission) {
            model.setProcessUserId(userId);
            return baseMapper.riksListByStatus(model);
        }

        // 检查品牌权限
        RiskAlertReviewerVo riskAlert = baseMapper.getRiskAlert(userId);
        String brandCodes = riskAlert.getBrandCodes();
        if (StringUtils.isNotBlank(brandCodes) && brandCodes.contains(model.getBrandCode())) {
            return this.baseMapper.riskList(model);
        }

        return Collections.emptyList();
    }

    // 处理业务标签
    private List<RiskAllTypesVo> processBusinessTags(List<RiskAllTypesVo> riskAllTypesVos,
                                                     List<VocRiskProcessRecipientTag> recipientTags,
                                                     String brandCode) {
        List<VocRiskProcessRecipientTag> processRecipientTags = recipientTags.stream()
                .filter(r -> r.getBrandCode() != null && r.getBrandCode().equals(brandCode))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(processRecipientTags)) {
            return riskAllTypesVos.parallelStream()
                    .filter(r -> Objects.nonNull(r.getRiskType()) && r.getRiskType() != 1)
                    .collect(Collectors.toList());
        }

        // 并行处理非业务类型和业务类型的风险
        CompletableFuture<List<RiskAllTypesVo>> nonBusinessFuture = CompletableFuture.supplyAsync(() ->
                riskAllTypesVos.parallelStream()
                        .filter(r -> Objects.nonNull(r.getRiskType()) && r.getRiskType() != 1)
                        .collect(Collectors.toList())
        );

        CompletableFuture<List<RiskAllTypesVo>> businessFuture = CompletableFuture.supplyAsync(() -> {
            List<RiskAllTypesVo> businessTagVos = riskAllTypesVos.parallelStream()
                    .filter(r -> Objects.nonNull(r.getRiskType()) && r.getRiskType() == 1)
                    .collect(Collectors.toList());

            Map<String, RiskAllTypesVo> allTypesVoMap = businessTagVos.stream()
                    .collect(Collectors.toMap(RiskAllTypesVo::getRisk, Function.identity()));

            // 获取业务标签
            QueryWrapper<VocBusinessTag> tagQueryWrapper = new QueryWrapper<>();
            tagQueryWrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
            List<VocBusinessTag> tags = businessTagService.list(tagQueryWrapper);
            Map<String, VocBusinessTag> businessTagMap = tags.stream()
                    .collect(Collectors.toMap(VocBusinessTag::getId, Function.identity()));

            return processRecipientTags.parallelStream()
                    .map(tag -> {
                        VocBusinessTag businessTag = businessTagMap.get(tag.getTagId());
                        if (businessTag != null && allTypesVoMap.containsKey(businessTag.getTagCode())) {
                            return allTypesVoMap.get(businessTag.getTagCode());
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        });
        // 等待所有操作完成
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(nonBusinessFuture, businessFuture);
        try {
            voidCompletableFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
        List<RiskAllTypesVo> result = new ArrayList<>();
        result.addAll(nonBusinessFuture.join());
        result.addAll(businessFuture.join());
        return result;
    }

    // 处理质量标签
    private List<RiskAllTypesVo> processQualityTags(List<RiskAllTypesVo> riskAllTypesVos,
                                                    List<VocRiskProcessRecipientBase> recipientQualityTags,
                                                    String brandCode) {
        Optional<VocRiskProcessRecipientBase> qualityTag = recipientQualityTags.stream()
                .filter(r -> r.getBrandCode().equals(brandCode))
                .findFirst();
        // 有问题
        return qualityTag.map(tag -> {
            Stream<RiskAllTypesVo> stream = riskAllTypesVos.parallelStream();
            if (tag.getType() == 2) {
                stream = stream.filter(r -> r.getRiskType() != 2);
            } else {
                // 用户风险过滤
                stream = stream.filter(r -> r.getRiskType() != 3);
            }

            return stream.collect(Collectors.toList());
        }).orElseGet(() ->
                riskAllTypesVos.parallelStream()
                        .filter(r -> r.getRiskType() != 2 && r.getRiskType() != 3)
                        .collect(Collectors.toList())
        );
    }

    // 按部门过滤风险
    private List<RiskAllTypesVo> filterByAuditDepartment(List<RiskAllTypesVo> riskAllTypesVos,
                                                         String auditDepartId) {
        return riskAllTypesVos.parallelStream()
                .filter(risk -> {
                    if (CollectionUtil.isEmpty(risk.getRiskHandlingRecords())) {
                        return false;
                    }
                    return risk.getRiskHandlingRecords().stream()
                            .filter(r -> r.getProcessStatus() != 3)
                            .map(VocRiskHandlingRecord::getProcessDepartId)
                            .anyMatch(departId -> departId.equals(auditDepartId));
                })
                .collect(Collectors.toList());
    }

    // 处理风险状态过滤
    private List<RiskAllTypesVo> filterByRiskState(List<RiskAllTypesVo> riskAllTypesVos,
                                                   RiskAllTypesModel model,
                                                   String roleType,
                                                   boolean hasReviewPermission) {
        if (ObjectUtils.isEmpty(model.getRiskState())) {
            return riskAllTypesVos;
        }

        return riskAllTypesVos.parallelStream()
                .filter(risk -> {
                    if (model.getRiskState() == 4) {
                        if ("1".equals(roleType) || hasReviewPermission) {
                            return risk.getRiskState() == 4 || risk.getRiskState() == 7;
                        }
                        return risk.getRiskState() == 4;
                    }
                    return risk.getRiskState().equals(model.getRiskState());
                })
                .collect(Collectors.toList());
    }


    @Override
    public IPage<RiskAllTypesVo> riskList1(Page<RiskAllTypesVo> page, RiskAllTypesModel model) {
        IPage<RiskAllTypesVo> relist = new Page<>();
        relist.setSize(model.getPageSize());
        relist.setCurrent(model.getPageNo());
        String userId = getUserInfo().getId();
        String brandCode = model.getBrandCode();
        SysRole role = sysRoleService.getRoleByUserId(userId);
        // 1为管理层人员；2为执行层人员
        String roleType = "";
        if (ObjectUtils.isNotEmpty(role)) {
            //1为管理层人员；2为执行层人员
            roleType = role.getRoleType();
        }
        // 1为管理层人员；2为执行层人员
        String rilistType = roleType;
        if (ObjectUtils.isNotEmpty(model.getRiskState())) {
            if (model.getRiskState() == 0) {
                model.setRiskIdList(null);
                model.setRiskStateStr(Arrays.asList("0"));
            } else if (model.getRiskState() == 1) {
                model.setRiskStateStr(new ArrayList<>());
                model.setProcessStatus("0");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
            } else if (model.getRiskState() == 3) {
                model.setRiskStateStr(new ArrayList<>());
            } else if (model.getRiskState() == 4) {
                model.setRiskStateStr(new ArrayList<>());
                model.setProcessStatus("2");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
            } else if (model.getRiskState() == 5) {
                model.setRiskStateStr(new ArrayList<>());
                model.setIfRiskType("0");
                model.setProcessUserId(userId);
                model.setAuditUserId(userId);
            }
        }
        String tag = model.getTag();
        //查询自己是否有审核的权限
        QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, userId)
                .eq(VocRiskAlertReviewer::getDelFlag, CommonConstant.DEL_FLAG_0);

        boolean re = riskAlertReviewerService.count(wrapper) > 0;
        List<RiskAllTypesVo> riskAllTypesVos = null;
        if ("1".equals(roleType)) {
            riskAllTypesVos = this.baseMapper.riskList(model); // 管理层
        } else {
            //查询自己是否在处理记录里
            QueryWrapper<VocRiskHandlingRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(VocRiskHandlingRecord::getProcessUserId, userId);
            boolean pro = handlingRecordService.count(queryWrapper) > 0;
            if (!re && !pro) {
                return relist;
            }
            if ("2".equals(rilistType) && !re) {
                model.setProcessUserId(userId);
                riskAllTypesVos = baseMapper.riksListByStatus(model);//处理人
            } else {
                RiskAlertReviewerVo riskAlert = baseMapper.getRiskAlert(userId);
                String brandCodes = riskAlert.getBrandCodes();
                if (StringUtils.isNotBlank(brandCodes) && brandCodes.contains(model.getBrandCode())) {
                    riskAllTypesVos = this.baseMapper.riskList(model);  //审核人
                } else {
                    return relist;
                }
            }
        }
        if (CollectionUtil.isEmpty(riskAllTypesVos)) {
            return relist;
        }
        relist.setRecords(riskAllTypesVos);
        if (ObjectUtils.isNotEmpty(model.getGroupByName()) || "999999".equals(model.getPageSize())) {
            return relist;
        }

        // 过滤审核人数据
        if (re) {
            //业务
            List<VocRiskProcessRecipientTag> recipientTags = riskProessRecipientService.queryPermissionByUserId(userId);
            if (CollectionUtil.isNotEmpty(recipientTags)) {
                List<VocRiskProcessRecipientTag> processRecipientTags = recipientTags.stream().filter(r -> r.getBrandCode() != null && r.getBrandCode().equals(brandCode)).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(processRecipientTags)) {

                    List<RiskAllTypesVo> allTypesVos = riskAllTypesVos.stream().filter(r -> Objects.nonNull(r.getRiskType()) && r.getRiskType() != 1).collect(Collectors.toList());
                    List<RiskAllTypesVo> businessTagVos = riskAllTypesVos.stream().filter(r -> Objects.nonNull(r.getRiskType()) && r.getRiskType() == 1).collect(Collectors.toList());
                    Map<String, RiskAllTypesVo> allTypesVoMap = businessTagVos.stream().collect(Collectors.toMap(RiskAllTypesVo::getRisk, Function.identity()));
                    //业务标签树结构
                    List<RiskAllTypesVo> riskAllTypesVoList = new ArrayList<>();
                    QueryWrapper<VocBusinessTag> tagQueryWrapper = new QueryWrapper<>();
                    tagQueryWrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
                    List<VocBusinessTag> tags = businessTagService.list(tagQueryWrapper);
                    Map<String, VocBusinessTag> businessTagMap = tags.stream().collect(Collectors.toMap(VocBusinessTag::getId, Function.identity()));
                    for (VocRiskProcessRecipientTag vocRiskProcessRecipientTag : processRecipientTags) {
                        VocBusinessTag vocBusinessTag = businessTagMap.get(vocRiskProcessRecipientTag.getTagId());
                        if (vocBusinessTag != null && allTypesVoMap.containsKey(vocBusinessTag.getTagCode())) {
                            RiskAllTypesVo riskAllTypesVo = allTypesVoMap.get(vocBusinessTag.getTagCode());
                            riskAllTypesVoList.add(riskAllTypesVo);
                        }
                    }
                    allTypesVos.addAll(riskAllTypesVoList);
                    riskAllTypesVos = allTypesVos;

                } else {
                    riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskType() != 1).collect(Collectors.toList());
                }
            } else {
                riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskType() != 1).collect(Collectors.toList());
            }
            //质量、用户
            List<VocRiskProcessRecipientBase> recipientQualityTags = riskProessRecipientService.queryPermissionQualityByUserId(userId);
            if (CollectionUtil.isNotEmpty(recipientQualityTags)) {
                List<VocRiskProcessRecipientBase> first = recipientQualityTags.stream()
                        .filter(r -> r.getBrandCode().equals(brandCode))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(first)) {
                    for (VocRiskProcessRecipientBase recipientBase : first) {
                        // 有问题
                        if (recipientBase.getType() == 2) {
                            riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskType() != 2).collect(Collectors.toList());
                        } else if (recipientBase.getType() == 3) {
                            riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskType() != 3).collect(Collectors.toList());
                        }
                    }
                } else {
                    riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskType() != 2 && r.getRiskType() != 3).collect(Collectors.toList());
                }
            } else {
                riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskType() != 2 && r.getRiskType() != 3).collect(Collectors.toList());
            }
        }

        if (CollectionUtil.isEmpty(riskAllTypesVos)) {
            return relist;
        }
        relist.setRecords(riskAllTypesVos);
        this.comRiskList(relist);
        riskAllTypesVos = relist.getRecords();
        // 筛选条件过滤状态
        if (ObjectUtils.isNotEmpty(model.getRiskState())) {
            if (model.getRiskState() == 4) {
                if ("1".equals(roleType)) {
                    riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskState() == 4 || r.getRiskState() == 7).collect(Collectors.toList());
                } else if (re) {
                    riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskState() == 4 || r.getRiskState() == 7).collect(Collectors.toList());
                } else {
                    riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskState() == 4).collect(Collectors.toList());
                }
            } else {
                riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getRiskState().equals(model.getRiskState())).collect(Collectors.toList());
            }
        }
        if (CollectionUtil.isEmpty(riskAllTypesVos)) {
            relist.setRecords(new ArrayList<>());
            relist.setTotal(0);
            return relist;
        }
        // 部门筛、项目组选条件过滤
        List<RiskAllTypesVo> newRiskAllTypesVoList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(model.getAuditDepartId())) {

            for (RiskAllTypesVo riskAllTypesVo : riskAllTypesVos) {
                if (CollectionUtil.isEmpty(riskAllTypesVo.getRiskHandlingRecords())) {
                    continue;
                }
                List<VocRiskHandlingRecord> riskHandlingRecords = riskAllTypesVo.getRiskHandlingRecords();
                List<String> list = riskHandlingRecords.stream().filter(r -> r.getProcessStatus() != 3).map(VocRiskHandlingRecord::getProcessDepartId).collect(Collectors.toList());
                if (list.contains(model.getAuditDepartId())) {
                    newRiskAllTypesVoList.add(riskAllTypesVo);
                }
            }
            if (CollectionUtil.isEmpty(newRiskAllTypesVoList)) {
                relist.setRecords(new ArrayList<>());
                relist.setTotal(0);
                return relist;
            }
        }

        if (!CollectionUtil.isEmpty(newRiskAllTypesVoList)) {
            riskAllTypesVos = newRiskAllTypesVoList;
        }
        // 频次筛选条件过滤
        if (StringUtils.isNotBlank(model.getProcessingFrequency())) {
            riskAllTypesVos = riskAllTypesVos.stream().filter(r -> r.getProcessingFrequency().equals(model.getProcessingFrequency())).collect(Collectors.toList());
        }

        if (CollectionUtil.isEmpty(riskAllTypesVos)) {
            relist.setRecords(new ArrayList<>());
            relist.setTotal(0);
            return relist;
        }

        List<RiskAllTypesVo> riskAllTypesVoList = new ArrayList<>();
        int total = riskAllTypesVos.size();
        List<List<RiskAllTypesVo>> lists = Lists.partition(riskAllTypesVos, model.getPageSize());
        if (model.getPageNo() <= lists.size() && model.getPageNo() >= 1) {
            riskAllTypesVoList = lists.get(model.getPageNo() - 1);
        }
        relist.setRecords(riskAllTypesVoList);
        relist.setTotal(total);
        relist.setCurrent(model.getPageNo());
        return relist;
    }


    private void setUserNum(RiskAllTypesVo e) {


        if (e.getRiskType() == 1) {
            DwdVocRisk risk = vocRiskService.getById(e.getRiskId());
            RiskEventInsightModel model = new RiskEventInsightModel();
            if (risk == null) return;
            model.setBrandCode(risk.getBrandCode());
            model.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
            model.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(risk.getStatisticType()), risk.getPublishDate()));
            model.setRiskId(risk.getId());
            model.setTopicCode(risk.getTopicCode());
            e.setStartDate(model.getStartDate());
            e.setEndDate(model.getEndDate());
            e.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
            if (risk.getCreateTime() != null) {
                model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
            }
            Long un = userDiMapper.riskWarningUserNum(model, risk);
            e.setUserNum(un);
            e.setWarnPeriod(risk.getStatisticType());
            e.setRiskIndex(risk.getRiskIndex());

        } else if (e.getRiskType() == 2 || e.getRiskType() == 4) {
            DwdVocQualityRisk risk = qualityRiskService.getById(e.getRiskId());
            if (risk == null) return;
            RiskEventInsightModel model = new RiskEventInsightModel();
            model.setBrandCode(risk.getBrandCode());
            model.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
            model.setCreateDate(IDateUtils.format(risk.getCreateTime()));

            model.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(risk.getStatisticType()), risk.getPublishDate()));
            e.setStartDate(model.getStartDate());
            e.setEndDate(model.getEndDate());
            e.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
            model.setRiskId(risk.getId());
            model.setTopicCode(risk.getTopicCode());
            if (risk.getCreateTime() != null) {
                model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
            }
            if (e.getRiskType().equals(4)) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
            Long un = qualityUserDiMapper.riskWarningUserNum(model, risk);
            e.setUserNum(un);
            e.setWarnPeriod(risk.getStatisticType());
            e.setRiskIndex(risk.getRiskIndex());
        } else if (e.getRiskType() == 3) {
            DwdVocUserRisk risk = userRiskService.getById(e.getRiskId());
            if (risk == null) return;
            e.setWarnPeriod(risk.getStatisticType());
            e.setRiskIndex(risk.getRiskIndex());

            e.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
            e.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(risk.getStatisticType()), risk.getPublishDate()));
            e.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));

        } else if (e.getRiskType() == 5) {
            DwdVocDealerRisk risk = dealerRiskService.getById(e.getRiskId());
            RiskEventInsightModel model = new RiskEventInsightModel();
            if (risk == null) return;
            model.setBrandCode(risk.getBrandCode());
            model.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
            model.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(risk.getStatisticType()), risk.getPublishDate()));
            model.setRiskId(risk.getId());
            model.setDlrName(risk.getDlrName());
            e.setStartDate(model.getStartDate());
            e.setEndDate(model.getEndDate());
            e.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
            if (risk.getCreateTime() != null) {
                model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
            }
            model.setIntention("投诉");
            Long un = userDiMapper.riskBranchesWarningUserNum(model, risk);
            e.setUserNum(un);
            e.setWarnPeriod(risk.getStatisticType());
            e.setRiskIndex(risk.getRiskIndex());

        }
    }

    @Override
    public List<RiskAllTypesVo> riskRecordDetails(String ids) {

        QueryWrapper<VocRiskAllTypes> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(VocRiskAllTypes::getId, ids.split(","));
        List<VocRiskAllTypes> records = this.list(wrapper);
        List<RiskAllTypesVo> vos = BeanUtil.copyToList(records, RiskAllTypesVo.class);
        String userId = getUserInfo().getId();
        for (RiskAllTypesVo e : vos) {
            setProcessInfo(e);
            setRiskInfo(e);
            setRiskProcessRecords(e);

            if (e.getRiskType() == 1) {
                List<RiskAlertReviewerVo> vbtu = riskAlertReviewerService.selectByBrandCodeTagCodeUserId(getUserInfo().getId(), e.getBrandCode(), e.getRisk());
                if (ObjectUtils.isNotEmpty(vbtu)) {
                    if (ObjectUtils.isNotEmpty(vbtu.get(0))) {
                        e.setProcessUserId(StrUtil.isNotBlank(vbtu.get(0).getReviewerUserId()) ? vbtu.get(0).getReviewerUserId() : null);
                        e.setProcessDepartId(StrUtil.isNotBlank(vbtu.get(0).getReviewerDepartId()) ? vbtu.get(0).getReviewerDepartId() : null);
                    }
                }
            }
            //质量用户默认处理人添加
            if (e.getRiskType() == 2 || e.getRiskType() == 3) {
                List<VocRiskProcessRecipientBase> vocRiskProcessRecipientBases = vocRiskProessRecipientMapper.queryPermissionByUserIdAndBrandCode(getUserInfo().getId(), e.getBrandCode());
                for (VocRiskProcessRecipientBase vocRiskProcessRecipientBase : vocRiskProcessRecipientBases) {
                    if (ObjectUtils.isNotEmpty(vocRiskProcessRecipientBase)) {
                        if (e.getRiskType() == 2) {
                            e.setProcessUserId(vocRiskProcessRecipientBase.getRiskUserId());
                            e.setProcessDepartId(vocRiskProcessRecipientBase.getRiskUserGroupId());
                        }
                        if (e.getRiskType() == 3) {
                            e.setProcessUserId(vocRiskProcessRecipientBase.getRiskUserId());
                            e.setProcessDepartId(vocRiskProcessRecipientBase.getRiskUserGroupId());
                        }
                    }
                }
            }
        }
        for (RiskAllTypesVo riskAllTypesVo : vos) {
            List<VocRiskHandlingRecord> newRiskHandlingRecords = new ArrayList<>();
            List<VocRiskHandlingRecord> riskHandlingRecords = riskAllTypesVo.getRiskHandlingRecords();
            RiskAlertReviewerVo reviewerVo = riskAllTypesVo.getReviewerVo();
            if (CollectionUtil.isNotEmpty(riskHandlingRecords)) {
                for (VocRiskHandlingRecord vocRiskHandlingRecord : riskHandlingRecords) {
                    VocRiskHandlingRecord riskHandlingRecord = this.getHandlingRecord(vocRiskHandlingRecord, reviewerVo);
                    newRiskHandlingRecords.add(riskHandlingRecord);
                }

            }
            riskAllTypesVo.setRiskHandlingRecords(newRiskHandlingRecords);
            if (CollectionUtil.isEmpty(riskHandlingRecords)) {
                continue;
            }

            List<WarningReviewList> warningReviewListList = new ArrayList<>();
            for (VocRiskHandlingRecord vocRiskHandlingRecord : riskHandlingRecords) {
                if (vocRiskHandlingRecord.getProcessStatus() == 2) {
                    vocRiskHandlingRecord.setProcessTime(vocRiskHandlingRecord.getConfirmproTime());
                    vocRiskHandlingRecord.setSolutionDescription(vocRiskHandlingRecord.getConfirmSolutionDescription());
                    vocRiskHandlingRecord.setSolutionAttachment(vocRiskHandlingRecord.getConfirmSolutionAttachment());
                }
                if (vocRiskHandlingRecord.getProcessStatus() != 0 && vocRiskHandlingRecord.getProcessStatus() != 3) {
                    newRiskHandlingRecords.add(vocRiskHandlingRecord);
                }
                if (vocRiskHandlingRecord.getProcessStatus() == 1) {
                    VocRiskHandlingRecord riskHandlingRecord = new VocRiskHandlingRecord();
                    BeanUtil.copyProperties(vocRiskHandlingRecord, riskHandlingRecord);
                    riskHandlingRecord.setProcessStatus(2);
                    riskHandlingRecord.setProcessTime(riskHandlingRecord.getConfirmproTime());
                    riskHandlingRecord.setSolutionDescription(riskHandlingRecord.getConfirmSolutionDescription());
                    riskHandlingRecord.setSolutionAttachment(riskHandlingRecord.getConfirmSolutionAttachment());
                    newRiskHandlingRecords.add(riskHandlingRecord);
                }
                //处理记录添加撤回信息
                if (vocRiskHandlingRecord.getProcessStatus() == 3) {
                    VocRiskHandlingRecord riskHandlingRecord = new VocRiskHandlingRecord();
                    BeanUtil.copyProperties(vocRiskHandlingRecord, riskHandlingRecord);
                    riskHandlingRecord.setProcessStatus(3);
                    riskHandlingRecord.setProcessTime(riskHandlingRecord.getUpdateTime());
                    riskHandlingRecord.setSolutionDescription(riskHandlingRecord.getConfirmSolutionDescription());
                    riskHandlingRecord.setSolutionAttachment(riskHandlingRecord.getConfirmSolutionAttachment());
                    newRiskHandlingRecords.add(riskHandlingRecord);
                } else {
                    WarningReviewList warningReviewList = new WarningReviewList();
                    warningReviewList.setProcessDepartName(vocRiskHandlingRecord.getProcessDepartName());
                    warningReviewList.setProcessUserName(vocRiskHandlingRecord.getProcessUserName());
                    if (vocRiskHandlingRecord.getProcessStatus() == 1) {
                        warningReviewList.setProcessTime(vocRiskHandlingRecord.getProcessTime());
                    }
                    warningReviewList.setConfirmationTime(vocRiskHandlingRecord.getConfirmproTime());
                    warningReviewListList.add(warningReviewList);
                }
            }
            if (CollectionUtil.isNotEmpty(newRiskHandlingRecords)) {
                newRiskHandlingRecords.sort(Comparator.comparing(VocRiskHandlingRecord::getProcessTime, Comparator.nullsFirst(Date::compareTo)).reversed());
            }
            riskAllTypesVo.setRiskHandlingRecords(newRiskHandlingRecords);
            riskAllTypesVo.setWarningReviewListList(warningReviewListList);
        }
        return vos;
    }

    private VocRiskHandlingRecord getHandlingRecord(VocRiskHandlingRecord vocRiskHandlingRecord, RiskAlertReviewerVo reviewerVo) {
        VocRiskHandlingRecord handlingRecord = new VocRiskHandlingRecord();
        handlingRecord.setProcessStatus(-1);
        handlingRecord.setProcessTime(vocRiskHandlingRecord.getCreateTime());
        handlingRecord.setProcessDepartId(reviewerVo.getReviewerDepartId());
        handlingRecord.setProcessDepartName(reviewerVo.getReviewerDepartName());
        handlingRecord.setProcessUserId(reviewerVo.getReviewerUserId());
        handlingRecord.setProcessUserName(reviewerVo.getReviewerUserName());
        handlingRecord.setSolutionDescription(vocRiskHandlingRecord.getMailContent());
        return handlingRecord;
    }

    private void setRiskProcessRecords(RiskAllTypesVo e) {
        QueryWrapper<VocRiskHandlingRecord> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskHandlingRecord::getWarningRiskId, e.getId());
        wrapper.lambda().eq(VocRiskHandlingRecord::getDelFlag, 0);
//        wrapper.lambda().eq(VocRiskHandlingRecord::getProcessStatus,1);
        wrapper.lambda().orderByDesc(VocRiskHandlingRecord::getCreateTime);
        List<VocRiskHandlingRecord> records = handlingRecordService.listExtend(wrapper);

        if (CollUtil.isEmpty(records)) {
            return;
        }
        QueryWrapper<VocRiskAlertReviewer> wrapper1 = new QueryWrapper<>();
        wrapper1.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, getUserInfo().getId());
        boolean re = riskAlertReviewerService.count(wrapper1) > 0;
        List<VocRiskHandlingRecord> collect3 = records.stream().filter(s -> (s.getProcessUserId() != null && s.getProcessUserId().equals(getUserInfo().getId()))).collect(Collectors.toList());

        //当自己登录系统时，根据自己的处理记录来判断列表里的状态显示是否已处理
        if (CollectionUtil.isNotEmpty(collect3)) {
            VocRiskHandlingRecord myrec = collect3.get(0);
            log.info("自己当前的状态:{}", myrec.getWarningRiskId());
            if (myrec.getProcessStatus() == 0) {
                e.setRiskState(1);
                e.setIsDistribution(true);
                List<VocRiskHandlingRecord> v = records.stream().filter(s -> (s.getProcessUserId() != null && !s.getProcessUserId().equals(getUserInfo().getId())) && s.getProcessStatus() != 3 && s.getProcessStatus() != 0).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(v)) {
                    e.setIsDistribution(false);
                }
            } else if (myrec.getProcessStatus() == 2) {
                e.setRiskState(4);
            } else if (myrec.getProcessStatus() == 1) {
                e.setRiskState(7);
            } else if (myrec.getProcessStatus() == 3) {
                if (!re) {
                    e.setRiskState(6);
                } else {
                    List<VocRiskHandlingRecord> vocRiskHandlingRecords = records.stream().filter(s -> (s.getProcessUserId() != null && !s.getProcessUserId().equals(getUserInfo().getId())) && s.getProcessStatus() != 3).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(vocRiskHandlingRecords)) {
                        vocRiskHandlingRecords.sort(Comparator.comparing(VocRiskHandlingRecord::getCreateTime, Comparator.nullsFirst(Date::compareTo)).reversed());
                        VocRiskHandlingRecord vocRiskHandlingRecord = vocRiskHandlingRecords.get(0);
                        if (vocRiskHandlingRecord.getProcessStatus() == 0) {
                            e.setRiskState(1);
                            e.setIsDistribution(true);
                        } else if (vocRiskHandlingRecord.getProcessStatus() == 2) {
                            e.setRiskState(4);
                        } else if (vocRiskHandlingRecord.getProcessStatus() == 1) {
                            e.setRiskState(7);
                        }
                    } else {
                        e.setRiskState(6);
                    }

                }
            }
        } else {
            //问题审核人员授权 但处理人不是自己 0待处理，2确认处理，1完成处理，3撤回
            List<VocRiskHandlingRecord> collect = records.stream().filter(r -> r.getProcessStatus() == 0).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(collect)) {
                e.setRiskState(1);
                e.setIsDistribution(true);
            }
            List<VocRiskHandlingRecord> collect1 = records.stream().filter(r -> r.getProcessStatus() == 2).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(collect1)) {
                e.setRiskState(4);
            }
        }

        List<VocRiskHandlingRecord> processList = records.stream().filter(r -> r.getProcessStatus() != 3).collect(Collectors.toList());
        List<VocRiskHandlingRecord> collect = records.stream().filter(r -> r.getProcessStatus() == 3 && r.getProcessUserId().equals(getUserInfo().getId())).collect(Collectors.toList());
        List<VocRiskHandlingRecord> processStatus = records.stream().filter(r -> r.getProcessStatus() == 1).collect(Collectors.toList());

        if (ObjectUtils.isNotEmpty(processStatus)) {
            if (processList.size() == processStatus.size()) {
                e.setRiskState(3);
                e.setFinishTag(Boolean.TRUE);
            } else if (CollectionUtil.isNotEmpty(collect3)) {
                VocRiskHandlingRecord myrec = collect3.get(0);
                if (myrec.getProcessStatus() == 1) {
                    e.setRiskState(3);
                    e.setConfirmationTime(myrec.getProcessTime());
                    e.setFinishTag(Boolean.FALSE);
                }
            } else {
                e.setRiskState(7);
            }
        }
        if (CollectionUtil.isNotEmpty(collect) && !re) {
            e.setRiskState(6);
        }

        QueryWrapper<VocRiskHandlingRecord> wrapper2 = new QueryWrapper<>();
        wrapper2.lambda().eq(VocRiskHandlingRecord::getWarningRiskId, e.getId());
        wrapper2.lambda().eq(VocRiskHandlingRecord::getDelFlag, 0);
        wrapper2.lambda().orderByDesc(VocRiskHandlingRecord::getCreateTime);
        List<VocRiskHandlingRecord> recordList = handlingRecordService.listExtend(wrapper2);

        if (CollectionUtil.isNotEmpty(recordList)) {
            List<VocRiskHandlingRecord> handlingRecordList = records.stream().filter(s -> (s.getProcessUserId() != null && s.getProcessUserId().equals(getUserInfo().getId()))).collect(Collectors.toList());
            List<VocRiskHandlingRecord> handlingRecords = recordList.stream().filter(r -> r.getProcessStatus() == 3 && r.getProcessUserId().equals(getUserInfo().getId())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(handlingRecordList) && CollectionUtil.isNotEmpty(handlingRecords) && !re) {
                e.setRiskState(6);
            }
        }
        e.setRiskHandlingRecords(records);
    }

    @Autowired
    ISysUserDepartService userDepartService;
    @Autowired
    SendMessageService messageService;

    @Override
    public Result<?> confirmReview(RiskAllTypesModel model) {
        List<VocRiskHandlingRecord> processors = model.getProcessors();
        List<String> newProcessUserId = processors.stream().map(VocRiskHandlingRecord::getProcessUserId).collect(Collectors.toList());
        //是否存在处理人，存在则重新派发，之前派发人员本次没派发则显示已撤回
        List<VocRiskHandlingRecord> riskHandlingRecordList = handlingRecordService.selectByWarningRiskId(model.getId());
        if (ObjectUtils.isNotEmpty(riskHandlingRecordList)) {
            for (VocRiskHandlingRecord riskHandlingRecord : riskHandlingRecordList) {

                //已撤回
                UpdateWrapper<VocRiskHandlingRecord> handlingRecordWrapper = new UpdateWrapper<>();
                handlingRecordWrapper.lambda().eq(VocRiskHandlingRecord::getId, riskHandlingRecord.getId())
                        .eq(VocRiskHandlingRecord::getDelFlag, 0)
                        .set(VocRiskHandlingRecord::getProcessStatus, 3).set(VocRiskHandlingRecord::getUpdateTime, new Date());
                handlingRecordService.update(handlingRecordWrapper);


            }
        }


        RiskAlertReviewerModel sdf = new RiskAlertReviewerModel();
        sdf.setReviewerUserId(getUserInfo().getId());
        RiskAlertReviewerVo reviewerVo = riskAlertReviewerService.getByIdVo(sdf);
        UpdateWrapper<VocRiskAllTypes> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(VocRiskAllTypes::getId, model.getId());

        //查询原风险等级
        List<VocRiskAllTypes> list = this.list(wrapper);
        if (ObjectUtils.isNotEmpty(list)) {
            if (ObjectUtils.isNotEmpty(model.getNewRiskLevel())) {
                //查询新风险等级是否存在，存在将原数据赋值给riskLevel，前端传入值赋值给newRiskLevel
                //查询前端传入风险等级与newRiskLevel是否一致，一致则不更新
                wrapper.lambda().set(VocRiskAllTypes::getNewRiskLevel, model.getNewRiskLevel());

            }
        }
        wrapper.lambda().set(VocRiskAllTypes::isIfRisk, 1)
                .set(VocRiskAllTypes::getRiskState, 1)
                .set(VocRiskAllTypes::getAuditTime, new Date())
                .set(StrUtil.isNotBlank(getUserInfo().getId()), VocRiskAllTypes::getAuditUserId, getUserInfo().getId())
                .set(StrUtil.isNotBlank(reviewerVo.getReviewerDepartId()), VocRiskAllTypes::getAuditDepartId, reviewerVo.getReviewerDepartId())
        ;

        this.update(wrapper);
        model.getProcessors().forEach(e -> {
            e.setWarningRiskId(model.getId());
            e.setProcessStatus(0);
            e.setCreateTime(new Date());
            e.setDelFlag(0);
        });
        try {
            messageService.sendMessage(model);
        } catch (Exception e) {
            log.error("消息推送错误！");
            e.printStackTrace();
        }

        for (VocRiskHandlingRecord riskHandlingRecord : processors) {
            QueryWrapper<VocRiskHandlingRecord> handlingRecordSelectWrapper = new QueryWrapper<>();
            handlingRecordSelectWrapper.lambda().eq(VocRiskHandlingRecord::getWarningRiskId, model.getId())
                    .eq(VocRiskHandlingRecord::getProcessUserId, riskHandlingRecord.getProcessUserId());
            List<VocRiskHandlingRecord> handlingRecordList = handlingRecordService.list(handlingRecordSelectWrapper);
            for (VocRiskHandlingRecord v : handlingRecordList) {
                UpdateWrapper<VocRiskHandlingRecord> handlingRecordWrapper = new UpdateWrapper<>();
                handlingRecordWrapper.lambda().eq(VocRiskHandlingRecord::getWarningRiskId, model.getId())
                        .set(VocRiskHandlingRecord::getDelFlag, 1);
                handlingRecordService.update(handlingRecordWrapper);
            }
        }
        handlingRecordService.saveBatch(model.getProcessors());
        return Result.OK();
    }

    @Override
    public Result<?> confirmProcessing(VocRiskHandlingRecord model) {
        UpdateWrapper<VocRiskHandlingRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(VocRiskHandlingRecord::getWarningRiskId, model.getWarningRiskId())
                .eq(VocRiskHandlingRecord::getProcessUserId, getUserInfo().getId())
                .eq(VocRiskHandlingRecord::getDelFlag, 0)

                .set(StrUtil.isNotBlank(model.getSolutionAttachment()), VocRiskHandlingRecord::getConfirmSolutionAttachment, model.getSolutionAttachment())
                .set(StrUtil.isNotBlank(model.getSolutionDescription()), VocRiskHandlingRecord::getConfirmSolutionDescription, model.getSolutionDescription())
                .set(ObjectUtils.isNotEmpty(model.getPlanCompletionTime()), VocRiskHandlingRecord::getConfirmproTime, model.getPlanCompletionTime())
                .set(VocRiskHandlingRecord::getProcessStatus, 2).set(VocRiskHandlingRecord::getUpdateTime, new Date());
        //判断是否是管理员
//        SysRole role = sysRoleService.getRoleByUserId(getUserInfo().getId());
//        if ((role.getRoleType()!=null&&role.getRoleType().equals("1"))){//管理员角色
//            //无操作
//
//        }else {//执行人角色
//            updateWrapper.lambda().eq(VocRiskHandlingRecord::getConfirmproUserId,getUserInfo().getId());
//        }
        handlingRecordService.update(updateWrapper);
//        this.update(updateWrapper);

        return Result.OK();
    }

    @Override
    public Result<?> confirmCancel(VocRiskHandlingRecord model) {
        UpdateWrapper<VocRiskAllTypes> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(VocRiskAllTypes::getId, model.getWarningRiskId())
                .set(VocRiskAllTypes::getRiskState, 5)
                .set(VocRiskAllTypes::getUpdateTime, new Date())
                .set(VocRiskAllTypes::getCancelTime, new Date());
////                .set(StrUtil.isNotBlank(model.getMailContent()),VocRiskAllTypes::getMailContent,model.getMailContent())
//                .set(StrUtil.isNotBlank(model.getSolutionAttachment()),VocRiskAllTypes::getSolutionAttachment,model.getSolutionAttachment())
//                .set(StrUtil.isNotBlank(model.getSolutionDescription()),VocRiskAllTypes::getSolutionDescription,model.getSolutionDescription())
//                .set(ObjectUtils.isNotEmpty(model.getPlanCompletionTime()),VocRiskAllTypes::getConfirmproTime,model.getPlanCompletionTime())
//                .set(VocRiskAllTypes::getRiskState,5).set(VocRiskAllTypes::getUpdateTime,new Date());
        this.update(updateWrapper);
        return Result.OK();
    }


    @Override
    public Result<?> confirmHandle(VocRiskHandlingRecord model) {
        UpdateWrapper<VocRiskHandlingRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(VocRiskHandlingRecord::getProcessUserId, getUserInfo().getId())
                .eq(VocRiskHandlingRecord::getWarningRiskId, model.getWarningRiskId())
                .eq(VocRiskHandlingRecord::getDelFlag, 0)

                .set(StrUtil.isNotBlank(model.getMailContent()), VocRiskHandlingRecord::getMailContent, model.getMailContent())
                .set(StrUtil.isNotBlank(model.getSolutionAttachment()), VocRiskHandlingRecord::getSolutionAttachment, model.getSolutionAttachment())
                .set(StrUtil.isNotBlank(model.getSolutionDescription()), VocRiskHandlingRecord::getSolutionDescription, model.getSolutionDescription())
                .set(model.getPlanCompletionTime() != null, VocRiskHandlingRecord::getPlanCompletionTime, model.getPlanCompletionTime())
                .set(VocRiskHandlingRecord::getProcessStatus, 1)
                .set(VocRiskHandlingRecord::getProcessTime, new Date());

        //判断是否是管理员
//        SysRole  role = sysRoleService.getRoleByUserId(getUserInfo().getId());
//        if ((role.getRoleType()!=null&&role.getRoleType().equals("1"))){//管理员角色
        //无操作

//        }else {//执行人角色
//            updateWrapper.lambda().eq(VocRiskHandlingRecord::getProcessUserId,getUserInfo().getId());
//        }
        handlingRecordService.update(updateWrapper);
        //处理时间记第一个处理人处理时的时间
        UpdateWrapper<VocRiskAllTypes> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(VocRiskAllTypes::getId, model.getWarningRiskId())
                .isNull(VocRiskAllTypes::getConfirmationTime)
                .set(VocRiskAllTypes::getConfirmationTime, new Date());
//            this.update(wrapper);
        //只要第一个人处理了就算处理了当前风险 当另一个处理人没有处理时，他登录系统查询列表时再独立设置他自己的处理状态
        UpdateWrapper<VocRiskAllTypes> wrapper1 = new UpdateWrapper<>();

        UpdateWrapper<VocRiskHandlingRecord> selectWrapper = new UpdateWrapper<>();
        selectWrapper.lambda().eq(VocRiskHandlingRecord::getWarningRiskId, model.getWarningRiskId());
        selectWrapper.lambda().eq(VocRiskHandlingRecord::getDelFlag, 0);
        List<VocRiskHandlingRecord> list = handlingRecordService.list(selectWrapper);
        List<VocRiskHandlingRecord> collect = list.stream().filter(l -> l.getProcessStatus() == 0 && ObjectUtils.isEmpty(l.getProcessTime())).collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(collect)) {
            //未全部处理完成
            wrapper1.lambda().eq(VocRiskAllTypes::getId, model.getWarningRiskId())
                    .set(VocRiskAllTypes::getRiskState, 7).set(VocRiskAllTypes::getUpdateTime, new Date());
        } else {
            wrapper1.lambda().eq(VocRiskAllTypes::getId, model.getWarningRiskId())
                    .set(VocRiskAllTypes::getRiskState, 3)
                    .set(VocRiskAllTypes::getUpdateTime, new Date())
                    .set(VocRiskAllTypes::getConfirmationTime, new Date());
            ;
        }


        this.update(wrapper1);
        return Result.OK();
    }

    @Override
    public RiskDataDryingVo dataDrying(String id) {
        VocRiskAllTypes riskAllTypes = this.getById(id);

        RiskAllTypesVo e = BeanUtil.copyProperties(riskAllTypes, RiskAllTypesVo.class);
        setProcessInfo(e);
        setRiskInfo(e);
        setRiskProcessRecords(e);
        //任务处理完成时间
        List<VocRiskHandlingRecord> riskHandlingRecords = e.getRiskHandlingRecords();
        if (CollectionUtil.isNotEmpty(riskHandlingRecords)) {
            List<VocRiskHandlingRecord> handlingRecordList = riskHandlingRecords.stream().filter(r -> r.getProcessStatus() != 3).collect(Collectors.toList());
            List<VocRiskHandlingRecord> handlingRecords = riskHandlingRecords.stream().filter(r -> r.getProcessStatus() == 1).collect(Collectors.toList());
            if (handlingRecordList.size() == handlingRecords.size()) {
                handlingRecords.sort(Comparator.comparing(VocRiskHandlingRecord::getProcessTime, Comparator.nullsFirst(Date::compareTo)).reversed());
                e.setProcessTime(handlingRecords.get(0).getProcessTime());

            }
            List<VocRiskHandlingRecord> vocRiskHandlingRecords = riskHandlingRecords.stream().filter(r -> Objects.nonNull(r.getConfirmproTime())).collect(Collectors.toList());
            vocRiskHandlingRecords.sort(Comparator.comparing(VocRiskHandlingRecord::getConfirmproTime, Comparator.nullsFirst(Date::compareTo)));
            e.setConfirmationTime(vocRiskHandlingRecords.get(0).getConfirmproTime());
        }
        RiskDataDryingVo vo = BeanUtil.copyProperties(e, RiskDataDryingVo.class);
        vo.setRecipientVo(null);
        vo.setRiskInfoVo(null);

        return vo;
    }

    @Override
    public List<EmotionIntentionVo> changeEmotionIntention(String id) {
        VocRiskAllTypes riskall = this.getById(id);
        if ("1".equals(riskall.getRiskType())) {
            DwdVocRisk event = vocRiskService.getById(riskall.getRiskId());
            return eventEmotionIntention(event, riskall);
        } else if ("2".equals(riskall.getRiskType()) || "4".equals(riskall.getRiskType())) {
            DwdVocQualityRisk quality = qualityRiskService.getById(riskall.getRiskId());
            return qualityEmotionIntention(quality, riskall);
        } else if ("5".equals(riskall.getRiskType())) {
            DwdVocDealerRisk dealerRisk = dealerRiskService.getById(riskall.getRiskId());
            return dealerEmotionIntention(dealerRisk, riskall);
        } else {
            return new ArrayList<>();
        }

    }

    private List<EmotionIntentionVo> dealerEmotionIntention(DwdVocDealerRisk dealerRisk, VocRiskAllTypes riskall) {
        List<EmotionIntentionVo> vos;
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        setModelStEnUn(model, riskall, dealerRisk.getStatisticType());
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        model.setDlrName(dealerRisk.getDlrName());
        vos = userDiMapper.eventEmotionIntention(model);
        return vos;
    }

    private List<EmotionIntentionVo> qualityEmotionIntention(DwdVocQualityRisk quality, VocRiskAllTypes riskall) {
        List<EmotionIntentionVo> vos;
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        setModelStEnUn(model, riskall, quality.getStatisticType());
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        if (StrUtil.isNotBlank(quality.getChannelId())) {
            model.setChannelIds(Arrays.asList(quality.getChannelId().split(",")));
        }
       /* model.setStartDate(DateUtils.formatTime(DateUtils.addDay(riskall.getCreateTime(),-7)));
        model.setEndDate(DateUtils.formatTime(DateUtils.addDay(riskall.getConfirmationTime(),+7)));
        model.setDateUnit(-1);*/

        vos = qualityUserDiMapper.qualityEmotionIntention(model);
        return vos;
    }

    private void setModelStEnUn(RiskAllTypesModel model, VocRiskAllTypes riskall, String statisticType) {
        Date enddate;
        if ("d".equals(statisticType)) {
            model.setStartDate(DateUtils.formatTime(DateUtils.addDay(riskall.getCreateTime(), -7)));

            enddate = DateUtils.addDay(riskall.getConfirmationTime(), +7);
            if (enddate.after(new DateTime())) {
                enddate = new DateTime();
            }
            model.setEndDate(DateUtils.formatTime(enddate));
            model.setDateUnit(-1);
        } else if ("w".equals(statisticType)) {
            Date start = DateUtil.offsetWeek(riskall.getCreateTime(), -8);
            enddate = DateUtil.offsetWeek(riskall.getConfirmationTime(), 7);
            if (enddate.after(new DateTime())) {
                enddate = new DateTime();
            }
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(enddate));
        } else if ("m".equals(statisticType)) {
            Date start = DateUtil.offsetMonth(riskall.getCreateTime(), -7);
            enddate = DateUtil.offsetMonth(riskall.getConfirmationTime(), 7);
            if (enddate.after(new DateTime())) {
                enddate = new DateTime();
            }
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(enddate));
        } else if ("q".equals(statisticType)) {
            Date start = DateUtil.offsetMonth(riskall.getCreateTime(), -7 * 3);


            enddate = DateUtil.offsetMonth(riskall.getConfirmationTime(), 7 * 3);

            if (enddate.after(new DateTime())) {
                enddate = new DateTime();
            }
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(enddate));
        } else if (statisticType.equals("y")) {
            Date start = DateUtil.offsetMonth(riskall.getCreateTime(), -7 * 12);


            enddate = DateUtil.offsetMonth(riskall.getConfirmationTime(), 7 * 12);

            if (enddate.after(new DateTime())) {
                enddate = new DateTime();
            }
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(enddate));
        }
        model.setDateUnit(CalculatorUtils.periodStrToNum(statisticType));

    }

    private List<EmotionIntentionVo> eventEmotionIntention(DwdVocRisk event, VocRiskAllTypes riskall) {
        List<EmotionIntentionVo> vos;
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        setModelStEnUn(model, riskall, event.getStatisticType());
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        /*model.setStartDate(DateUtils.formatTime(DateUtils.addDay(riskall.getCreateTime(),-7)));
        model.setEndDate(DateUtils.formatTime(DateUtils.addDay(riskall.getConfirmationTime(),+7)));
        model.setDateUnit(-1);*/
        vos = userDiMapper.eventEmotionIntention(model);
        /*if (!event.getStatisticType().equals("d"))
        vos.stream().forEach(e-> setRangeDate(e,event.getStatisticType()));*/
        return vos;
    }

    private void setRangeDate(EmotionIntentionVo e, String statisticType) {
        e.setStatisticType(statisticType);
        QueryWrapper<DimDate> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DimDate::getDateYear, e.getYear())
                .eq("w".equals(e.getStatisticType()), DimDate::getDateWeek, e.getUni())
                .eq("m".equals(e.getStatisticType()), DimDate::getDateMonth, e.getUni())
                .eq("q".equals(e.getStatisticType()), DimDate::getDateQuarter, e.getUni())
                .orderByDesc(DimDate::getFormatDate)
        ;
        e.setPublishDate(dimDateService.list(wrapper).get(0).getFormatDate());
    }

    @Override
    public List<TrendChannelRiskVo> changeChannel(String id) {

        VocRiskAllTypes riskall = this.getById(id);
        if ("1".equals(riskall.getRiskType())) {
            DwdVocRisk event = vocRiskService.getById(riskall.getRiskId());
            return eventChangeChannel(event, riskall);
        } else if ("2".equals(riskall.getRiskType()) || "4".equals(riskall.getRiskType())) {
            DwdVocQualityRisk quality = qualityRiskService.getById(riskall.getRiskId());
            return qualityChangeChannel(quality, riskall);
        } else if ("5".equals(riskall.getRiskType())) {
            DwdVocDealerRisk dealerRisk = dealerRiskService.getById(riskall.getRiskId());
            return dealerChangeChannel(dealerRisk, riskall);
        } else {
            return null;
        }
    }

    private List<TrendChannelRiskVo> dealerChangeChannel(DwdVocDealerRisk dealerRisk, VocRiskAllTypes riskall) {
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        setModelStEnUn(model, riskall, dealerRisk.getStatisticType());
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        model.setDlrName(dealerRisk.getDlrName());
        List<ChannelVo> vos = userDiMapper.eventChangeChannel(model);
        List<TrendChannelRiskVo> channelVos = new ArrayList<>();
        Map<String, List<ChannelVo>> sfd = vos.stream()
                .collect(Collectors.groupingBy(
                        ChannelVo::getDateStr,
                        LinkedHashMap::new, // 使用LinkedHashMap维持插入顺序
                        Collectors.toList()   // 收集到List中
                ));
        for (Map.Entry<String, List<ChannelVo>> entry : sfd.entrySet()) {
            TrendChannelRiskVo vo = new TrendChannelRiskVo(entry);
            channelVos.add(vo);
        }
        return channelVos;

    }

    private List<TrendChannelRiskVo> qualityChangeChannel(DwdVocQualityRisk quality, VocRiskAllTypes riskall) {
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        setModelStEnUn(model, riskall, quality.getStatisticType());
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        if (StrUtil.isNotBlank(quality.getChannelId())) {
            model.setChannelIds(Arrays.asList(quality.getChannelId()));
        }
        List<ChannelVo> vos = qualityUserDiMapper.qualityChangeChannel(model);
        List<TrendChannelRiskVo> channelVos = new ArrayList<>();
        Map<String, List<ChannelVo>> sfd = vos.stream()
                .collect(Collectors.groupingBy(
                        ChannelVo::getDateStr,
                        LinkedHashMap::new, // 使用LinkedHashMap维持插入顺序
                        Collectors.toList()   // 收集到List中
                ));

        for (Map.Entry<String, List<ChannelVo>> entry : sfd.entrySet()) {
            TrendChannelRiskVo vo = new TrendChannelRiskVo(entry);
            channelVos.add(vo);
        }
//        channelVos.sort(Comparator.comparing(TrendChannelRiskVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        return channelVos;
    }

    private List<TrendChannelRiskVo> eventChangeChannel(DwdVocRisk event, VocRiskAllTypes riskall) {
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        setModelStEnUn(model, riskall, event.getStatisticType());
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        List<ChannelVo> vos = userDiMapper.eventChangeChannel(model);
        List<TrendChannelRiskVo> channelVos = new ArrayList<>();
//        Map<String, List<ChannelVo>> sfd = vos.stream().collect(Collectors.groupingBy(ChannelVo::getDateStr));
        Map<String, List<ChannelVo>> sfd = vos.stream()
                .collect(Collectors.groupingBy(
                        ChannelVo::getDateStr,
                        LinkedHashMap::new, // 使用LinkedHashMap维持插入顺序
                        Collectors.toList()   // 收集到List中
                ));
        for (Map.Entry<String, List<ChannelVo>> entry : sfd.entrySet()) {
            TrendChannelRiskVo vo = new TrendChannelRiskVo(entry);

            channelVos.add(vo);
        }
//        channelVos.sort(Comparator.comparing(TrendChannelRiskVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        return channelVos;

    }

    @Override
    public Map<String, Object> hotWords(String id) {
        Map<String, Object> ls = new HashMap<>();
        List<HighHotWordsVo> fro;
        List<HighHotWordsVo> after;
        VocRiskAllTypes riskall = this.getById(id);
        RiskAllTypesModel model = BeanUtil.copyProperties(riskall, RiskAllTypesModel.class);
        String systicType = "";
        DwdVocDealerRisk branchesRisk = new DwdVocDealerRisk();
        DwdVocQualityRisk quality = new DwdVocQualityRisk();
        if ("1".equals(riskall.getRiskType())) {
            DwdVocRisk event = vocRiskService.getById(riskall.getRiskId());
            systicType = event.getStatisticType();
        } else if ("2".equals(riskall.getRiskType()) || "4".equals(riskall.getRiskType())) {
            quality = qualityRiskService.getById(riskall.getRiskId());
            systicType = quality.getStatisticType();
        } else if ("5".equals(riskall.getRiskType())) {
            branchesRisk = dealerRiskService.getById(riskall.getRiskId());
            systicType = branchesRisk.getStatisticType();
        }
        setModelStEnUn(model, riskall, systicType);
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        if ("1".equals(riskall.getRiskType())) {
            fro = userDiMapper.eventHotWords(model);
            model.setEndDate(null);
            model.setStartDate(DateUtils.formatTime(riskall.getConfirmationTime()));
            after = userDiMapper.eventHotWords(model);
        } else if ("2".equals(riskall.getRiskType()) || "4".equals(riskall.getRiskType())) {
            if ("4".equals(riskall.getRiskType())) {
                model.setChannelIds(Arrays.asList(quality.getChannelId()));
            }
            fro = qualityUserDiMapper.qualityHotWords(model);
            model.setEndDate(null);
            model.setStartDate(DateUtils.formatTime(riskall.getConfirmationTime()));
            after = qualityUserDiMapper.qualityHotWords(model);
        } else if ("5".equals(riskall.getRiskType())) {
            model.setDlrName(branchesRisk.getDlrName());
            fro = userDiMapper.eventHotWords(model);
            model.setEndDate(null);
            model.setStartDate(DateUtils.formatTime(riskall.getConfirmationTime()));
            after = userDiMapper.eventHotWords(model);
        } else {
            return null;
        }
        ls.put("front", fro);
        ls.put("after", after);
        return ls;
    }

    @Override
    public BigDecimal getToBeReviewed(FilterCriteriaModel model) {
        QueryWrapper<VocRiskAllTypes> wrapper = new QueryWrapper<>();
//        wrapper.lambda().apply((model.getStartDate()!=null&&model.getEndDate()!=null),"(create_time BETWEEN {0} AND {1})", model.getStartDate(),model.getEndDate());
        wrapper.lambda().eq(VocRiskAllTypes::getRiskState, 0);
        return new BigDecimal(baseMapper.selectCount(wrapper));
    }

    @Override
    public Map<String, DataDryingContrastVo> dataDryingContrast(String id) {
        VocRiskAllTypes riskAllTypes = this.getById(id);
        RiskAllTypesVo e = BeanUtil.copyProperties(riskAllTypes, RiskAllTypesVo.class);
        setProcessInfo(e);
        setRiskInfo(e);
        setRiskProcessRecords(e);
        RiskDataDryingVo vo = BeanUtil.copyProperties(e, RiskDataDryingVo.class);
        Map<String, DataDryingContrastVo> map = new HashMap<>();
        DataDryingContrastVo vo1 = new DataDryingContrastVo();
        DataDryingContrastVo vo2 = new DataDryingContrastVo();
        DataDryingContrastVo vo3 = new DataDryingContrastVo();
        RiskAllTypesModel model = BeanUtil.copyProperties(riskAllTypes, RiskAllTypesModel.class);
        List<String> dats = new ArrayList<>();
        dats.add(CommonConstant.privateChannelId);
        model.setDataSources(dats);
        Integer frontDay = 1;
        Integer afterDay = 1;
        if ("1".equals(riskAllTypes.getRiskType())) {
            DwdVocRisk risk = vocRiskService.getById(riskAllTypes.getRiskId());
            vo.setStatisticType(risk.getStatisticType());
            //风险周期
            vo1 = userDiMapper.dataDryingContrast(model, "0");

            model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
            model.setEndDates(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            model.setEndDate(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            model.setStartDate(DateUtils.formatTime(risk.getPublishDate()));
            frontDay = DateUtils.getIntervalDays(model.getEndDate(), model.getStartDate());
            vo2 = userDiMapper.dataDryingContrast(model, "1");

            model.setEndDate(null);
            model.setStartDate(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            afterDay = DateUtils.getIntervalDays(DateUtil.offsetDay(new DateTime(), -2), riskAllTypes.getConfirmationTime());
            vo3 = userDiMapper.dataDryingContrast(model, "1");
        } else if ("2".equals(riskAllTypes.getRiskType()) || "4".equals(riskAllTypes.getRiskType())) {
            DwdVocQualityRisk qualityRiskF = qualityRiskService.getById(riskAllTypes.getRiskId());
            vo.setStatisticType(qualityRiskF.getStatisticType());
            if ("4".equals(riskAllTypes.getRiskType())) {
                model.setChannelIds(Arrays.asList(qualityRiskF.getChannelId()));
            }
            vo1 = qualityUserDiMapper.dataDryingContrast(model, "0");

            model.setDateUnit(CalculatorUtils.periodStrToNum(qualityRiskF.getStatisticType()));
            model.setEndDates(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            model.setEndDate(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            model.setStartDate(DateUtils.formatTime(qualityRiskF.getPublishDate()));

            frontDay = DateUtils.getIntervalDays(model.getEndDate(), model.getStartDate());

            vo2 = qualityUserDiMapper.dataDryingContrast(model, "1");
            model.setEndDate(null);
            model.setStartDate(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            afterDay = DateUtils.getIntervalDays(DateUtil.offsetDay(new DateTime(), -2), riskAllTypes.getConfirmationTime());
            vo3 = qualityUserDiMapper.dataDryingContrast(model, "1");

        } else if (riskAllTypes.getRiskType().equals("5")) {
            DwdVocDealerRisk risk = dealerRiskService.getById(riskAllTypes.getRiskId());
            vo.setStatisticType(risk.getStatisticType());
            model.setDlrName(risk.getDlrName());
            //风险周期
            vo1 = userDiMapper.dataDryingContrast(model, "0");

            model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
            model.setEndDates(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            model.setEndDate(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            model.setStartDate(DateUtils.formatTime(risk.getPublishDate()));
            frontDay = DateUtils.getIntervalDays(model.getEndDate(), model.getStartDate());
            vo2 = userDiMapper.dataDryingContrast(model, "1");

            model.setEndDate(null);
            model.setStartDate(DateUtils.formatTime(riskAllTypes.getConfirmationTime()));
            afterDay = DateUtils.getIntervalDays(DateUtil.offsetDay(new DateTime(), -2), riskAllTypes.getConfirmationTime());
            vo3 = userDiMapper.dataDryingContrast(model, "1");
        }
        /*vo3.setComplaintR(NumberUtil.sub(vo1.getComplaint(),vo2.getComplaint()));
        vo3.setGrumbleR(NumberUtil.sub(vo1.getGrumble(),vo2.getGrumble()));
        vo3.setNegativeR(NumberUtil.sub(vo1.getNegative(),vo2.getNegative()));
        vo3.setUserNumR(NumberUtil.sub(vo1.getUserNum(),vo2.getUserNum()));*/
        map.put("riskTotal", vo1);
        vo2.setDayAverage(frontDay);
        vo3.setDayAverage(afterDay);
        map.put("riskTotalFront", vo2);
        map.put("riskTotalAfter", vo3);
        vo.setContrasts(map);
        vo.setContrasts(map);
        return map;
    }

    @Override
    public List<DateStiticVo> trendDate(String id) {
        String statistype;
        VocRiskAllTypes riskall = this.getById(id);
        if ("1".equals(riskall.getRiskType())) {
            DwdVocRisk event = vocRiskService.getById(riskall.getRiskId());
            statistype = event.getStatisticType();
        } else if ("2".equals(riskall.getRiskType()) || "4".equals(riskall.getRiskType())) {
            DwdVocQualityRisk quality = qualityRiskService.getById(riskall.getRiskId());
            statistype = quality.getStatisticType();
        } else if ("5".equals(riskall.getRiskType())) {
            DwdVocDealerRisk dealerRisk = dealerRiskService.getById(riskall.getRiskId());
            statistype = dealerRisk.getStatisticType();
        } else {
            return null;
        }


        RiskAllTypesModel model = new RiskAllTypesModel();
        setModelStEnUn(model, riskall, statistype);
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parseDate(model.getStartDate().substring(0, 11)), DateUtil.parseDate(model.getEndDate().substring(0, 10)), DateField.DAY_OF_YEAR);
        List<DateStiticVo> strings = new ArrayList<>();
        dateTimes.forEach(e -> {
            DateStiticVo vo = new DateStiticVo(e.toDateStr());
            strings.add(vo);
        });
        return strings;
    }

    @Override
    public Object riskMailTextContent(String id) {
        return null;
    }

    @Override
    public List<Map<String, Integer>> riskLevelDis(FilterCriteriaModel model) {

        return baseMapper.riskLevelDis(model);
    }

    @Override
    public List<Map> riskDepsList(FilterCriteriaModel model) {
        return baseMapper.riskDepsList(model);
    }

    @Override
    public Map<String, Object> processingProgress(FilterCriteriaModel model) {

        RiskAllTypesModel riskAllTypesModel = new RiskAllTypesModel();

        BeanUtil.copyProperties(model, riskAllTypesModel);

        riskAllTypesModel.setPageNo(1);
        riskAllTypesModel.setPageSize(999999);
        riskAllTypesModel.setStartDate(model.getStartDate());
        riskAllTypesModel.setEndDate(model.getEndDate());
        Page<RiskAllTypesVo> page = new Page<>(riskAllTypesModel.getPageNo(), riskAllTypesModel.getPageSize());
        IPage<RiskAllTypesVo> riskAllTypeList = riskAllTypesService.riskList(page, riskAllTypesModel);

        Map<String, Object> map = new HashMap<>();
        map.put("complete", 0);
        map.put("total", 0);
        if (ObjectUtils.isNotEmpty(riskAllTypeList.getRecords())) {
            List<RiskAllTypesVo> records = riskAllTypeList.getRecords();
            map.put("total", records.size());
            List<RiskAllTypesVo> collect = records.stream().filter(r -> r.getRiskState() == 3).collect(Collectors.toList());
            map.put("complete", ObjectUtils.isNotEmpty(collect) ? collect.size() : 0);
        }


//        return baseMapper.processingProgress(model);
        return map;
    }


    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;

    private void setRiskInfo(RiskAllTypesVo e) {
        RiskInfoVo riskInfoVo = new RiskInfoVo();
        if (e.getRiskType() == 3) {
            DwdVocUserRisk risk = userRiskService.getById(e.getRiskId());

            ComFilterCriteriaModel model1 = new ComFilterCriteriaModel();
            model1.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
            model1.setStartDate(DateUtils.dateToStr(risk.getPublishDate()) + " 00:00:00");
            model1.setEndDate(CalculatorUtils.getRiskEndDate(model1.getDateUnit(), risk.getPublishDate()));
            model1.setBrandCode(risk.getBrandCode());

            model1.SetUpCycle();
            if (model1.getDateUnit() == -1) {
                model1.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
                model1.setEndDate(CalculatorUtils.getRiskEndDate(model1.getDateUnit(), risk.getPublishDate()));
            }
            model1.setUserId(e.getRisk());
            riskInfoVo.setCarSeries(emotionUserDiMapper.riskCarSeriesByUserId(model1));
            riskInfoVo.setAggProblem(emotionUserDiMapper.riskAggProblemByUserId(model1));
            riskInfoVo.setHotWords(emotionUserDiMapper.riskHotWordsByUserId(model1));
            e.setStatisticType(risk.getStatisticType());
            riskInfoVo.setWarnPeriod(risk.getStatisticType());
            riskInfoVo.setStartDate(model1.getStartDate());
            riskInfoVo.setEndDate(CalculatorUtils.getRiskEndDate(model1.getDateUnit(), risk.getPublishDate()));
            riskInfoVo.setStatistic(risk.getNegativeNum());

            riskInfoVo.setStatisticTotal(emotionUserDiMapper.userStatisticTotal(model1));
            riskInfoVo.setUsersNum(new BigDecimal(1));
            riskInfoVo.setUserTotalNum(new BigDecimal(1));
            e.setRiskInfoVo(riskInfoVo);

        } else if (e.getRiskType() == 1) {
            RiskEventInsightModel model = new RiskEventInsightModel();
            DwdVocRisk risk = vocRiskService.getById(e.getRiskId());
            if (risk == null) {
                e.setRiskInfoVo(new RiskInfoVo());
            } else {
                model.setTopicCode(risk.getTopicCode());
                model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
                model.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");
                model.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
                model.setBrandCode(risk.getBrandCode());
                Object result = vocRiskService.dataAnalysisBriefing(model, risk).getResult();
                RiskBriefingVo briefingVo = JSON.parseObject(JSON.toJSONString(result), RiskBriefingVo.class);
                briefingVo.setWarnPeriod(risk.getStatisticType());
                e.setStatisticType(risk.getStatisticType());
                briefingVo.setWarnPeriod(risk.getStatisticType());
                e.setRiskInfoVo(BeanUtil.copyProperties(briefingVo, RiskInfoVo.class));

            }
        } else if (e.getRiskType() == 2 || e.getRiskType() == 4) {
            RiskEventInsightModel model = new RiskEventInsightModel();
            DwdVocQualityRisk risk = qualityRiskService.getById(e.getRiskId());

            if (risk == null) {
                e.setRiskInfoVo(new RiskInfoVo());
            } else {
                model.setTopicCode(risk.getTopicCode());
                model.setBrandCode(risk.getBrandCode());
                model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
                model.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");
                model.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
                Object result = new Object();
                if (e.getRiskType().equals(4)) {
                    result = qualityRiskService.dataAnalysisBriefingRescue(model, risk).getResult();
                } else if (e.getRiskType().equals(2)) {
                    result = qualityRiskService.dataAnalysisBriefing(model, risk).getResult();
                }
                RiskBriefingVo briefingVo = JSON.parseObject(JSON.toJSONString(result), RiskBriefingVo.class);
                briefingVo.setWarnPeriod(risk.getStatisticType());
                e.setStatisticType(risk.getStatisticType());
                briefingVo.setWarnPeriod(risk.getStatisticType());
                e.setRiskInfoVo(BeanUtil.copyProperties(briefingVo, RiskInfoVo.class));
            }
        } else if (e.getRiskType() == 5) {
            RiskEventInsightModel model = new RiskEventInsightModel();
            DwdVocDealerRisk risk = dealerRiskService.getById(e.getRiskId());
            if (risk == null) {
                e.setRiskInfoVo(new RiskInfoVo());
            } else {
                model.setDlrName(risk.getDlrName());
                model.setBrandCode(risk.getBrandCode());
                model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
                model.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");
                model.setStartDate(DateUtil.formatDate(risk.getPublishDate()) + " 00:00:00");
                Object result = dealerRiskService.dataAnalysisBriefing(model, risk).getResult();
                RiskBriefingVo briefingVo = JSON.parseObject(JSON.toJSONString(result), RiskBriefingVo.class);
                briefingVo.setWarnPeriod(risk.getStatisticType());
                e.setStatisticType(risk.getStatisticType());
                briefingVo.setWarnPeriod(risk.getStatisticType());
                e.setRiskInfoVo(BeanUtil.copyProperties(briefingVo, RiskInfoVo.class));
            }
        }
        e.setRiskWarningNum(warningRecordService.riskWarningNum(e));

    }

    @Autowired
    ISysDepartService departService;

    private void setProcessInfo(RiskAllTypesVo e) {
        if (StrUtil.isNotBlank(e.getAuditUserId())) {//已经审核过了(这个风险点已经有审核人了)
            QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, e.getAuditUserId());
            RiskAlertReviewerModel model = new RiskAlertReviewerModel();
            model.setReviewerUserId(e.getAuditUserId());
            RiskAlertReviewerVo reviewerVo = riskAlertReviewerService.getByIdVo(model);
            e.setReviewerVo(reviewerVo);

            //如果自己已经是这个风险点的处理人则加载自己的信息
            QueryWrapper<VocRiskHandlingRecord> query1 = new QueryWrapper<>();
            query1.lambda()
                    .eq(VocRiskHandlingRecord::getWarningRiskId, e.getId());
            List<VocRiskHandlingRecord> records = handlingRecordService.list(query1);
            VocRiskHandlingRecord record = records.stream().filter(k -> k.getProcessUserId().equals(getUserInfo().getId())).findFirst().orElse(null);
            if (record != null) {
                RiskProcessRecipientVo vo = BeanUtil.copyProperties(record, RiskProcessRecipientVo.class);
                vo.setProcessUserName(getUserInfo().getRealname());
                if (record.getBindType() == 1) {
                    vo.setProcessDepartName(departService.getById(record.getProcessDepartId()).getDepartName());
                } else {
                    vo.setProcessDepartName(projectGroupService.getById(record.getProcessDepartId()).getProjectName());
                }
                e.setRecipientVo(vo);
            }

            //处理人处理时加载自己的信息


        } else {//第一次审核加载默认处理人
            RiskAlertReviewerModel model = new RiskAlertReviewerModel();
            model.setReviewerUserId(getUserInfo().getId());
            RiskAlertReviewerVo revo = riskAlertReviewerService.getByIdVo(model);
            QueryWrapper<VocRiskProcessRecipient> query = new QueryWrapper<>();
            e.setReviewerVo(revo);
        }

    }

    @Autowired
    ISysDictService dictService;

    @Autowired
    VocRiskProessRecipientMapper proessRecipientMapper;


    @Override
    public List<RiskBrandCodeVo> getRiskBrandCodes() {
        LoginUser userInfo = getUserInfo();
        String userId = userInfo.getId();
        List<RiskBrandCodeVo> riskBrandCodeVoList = new ArrayList<>();
        List<DictVo> questionType = dictService.queryDictItemsByCode("question_type");
        QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, userId)
                .eq(VocRiskAlertReviewer::getDelFlag, 0);
        VocRiskAlertReviewer vocRiskAlertReviewer = reviewerMapper.selectOne(wrapper);
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(userId);
        if (CollectionUtil.isEmpty(sysRoles) ||
                StringUtils.isBlank(sysRoles.get(0).getBrandCode()) ||
                CollectionUtil.isEmpty(Arrays.asList(sysRoles.get(0).getBrandCode().split(",")))) {
            return riskBrandCodeVoList;
        }
        SysRole sysRole = sysRoles.get(0);
        List<String> list = Arrays.asList(sysRoles.get(0).getBrandCode().split(","));
        List<VocRiskAllTypes> oth = proessRecipientMapper.queryPermissionByProcessUserId(userId);
        Map<String, List<VocRiskAllTypes>> othTag = new HashMap<>();
        Set<String> brandCodeList = new HashSet<>();
        if (CollectionUtil.isNotEmpty(oth)) {
            Map<String, List<VocRiskAllTypes>> vocRiskAllTypes = oth.stream().collect(Collectors.groupingBy(VocRiskAllTypes::getBrandCode));
            othTag.putAll(vocRiskAllTypes);
            brandCodeList.addAll(vocRiskAllTypes.keySet());
        }
        if (ObjectUtils.isEmpty(vocRiskAlertReviewer)) {
            for (String str : list) {
                RiskBrandCodeVo riskBrandCodeVo = new RiskBrandCodeVo();
                List<VocRiskAllTypes> vocRiskAllTypes2 = othTag.get(str);
                riskBrandCodeVo.setRiskBrandCode(str);
                if (sysRole.getRoleType().equals("1")) {
                    riskBrandCodeVo.setSelectPermissions(questionType);
                } else if (CollectionUtil.isNotEmpty(vocRiskAllTypes2)) {
                    List<String> vocRiskAllTypes1 = vocRiskAllTypes2.stream().map(VocRiskAllTypes::getRiskType).collect(Collectors.toList());
                    List<DictVo> collect = questionType.stream().filter(s -> vocRiskAllTypes1.contains(s.getValue())).collect(Collectors.toList());
                    riskBrandCodeVo.setSelectPermissions(collect);
                }
                List<SysRoleBusinessTag> sysRoleBusinessTags = sysRoleBusinessTagMapper.listByRoleId(sysRole.getId(), str);
                if (CollectionUtil.isEmpty(sysRoleBusinessTags)) {
                    List<DictVo> collect = riskBrandCodeVo.getSelectPermissions().stream().filter(s -> !s.getValue().equals("1")).collect(Collectors.toList());
                    riskBrandCodeVo.setSelectPermissions(collect);
                }
                boolean quality = getQuality(str, sysRole.getQualityText());
                if (!quality) {
                    List<DictVo> collect = riskBrandCodeVo.getSelectPermissions().stream().filter(s -> !s.getValue().equals("2")).collect(Collectors.toList());
                    riskBrandCodeVo.setSelectPermissions(collect);
                }
                riskBrandCodeVoList.add(riskBrandCodeVo);
            }
        }
        if (!sysRole.getRoleType().equals("1") && ObjectUtils.isNotEmpty(vocRiskAlertReviewer)) {
            List<String> brandCodes = new ArrayList<>();
            // 业务处理
            List<VocRiskProcessRecipientTag> recipientTags = riskProessRecipientService.queryPermissionByUserId(userId);
            Map<String, List<VocRiskProcessRecipientTag>> buss = new HashMap<>();
            if (CollectionUtil.isNotEmpty(recipientTags)) {
                Map<String, List<VocRiskProcessRecipientTag>> brandCode = recipientTags.stream().collect(Collectors.groupingBy(VocRiskProcessRecipientTag::getBrandCode));
                buss.putAll(brandCode);
                brandCodeList.addAll(brandCode.keySet());
            }
            // 质量、用户处理
            List<VocRiskProcessRecipientBase> recipientQualityTags = riskProessRecipientService.queryPermissionQualityByUserId(userId);
            Map<String, List<VocRiskProcessRecipientBase>> recipientBase = new HashMap<>();
            if (CollectionUtil.isNotEmpty(recipientQualityTags)) {
                Map<String, List<VocRiskProcessRecipientBase>> brandCode = recipientQualityTags.stream().collect(Collectors.groupingBy(VocRiskProcessRecipientBase::getBrandCode));
                recipientBase.putAll(brandCode);
                brandCodeList.addAll(brandCode.keySet());
            }

            if (CollectionUtil.isNotEmpty(brandCodeList)) {
                for (String brandCode : brandCodeList) {
                    Set<String> types = new HashSet<>();
                    List<VocRiskProcessRecipientTag> bussList = buss.get(brandCode);
                    if (CollectionUtil.isNotEmpty(bussList)) {
                        types.add("1");
                    }
                    List<VocRiskProcessRecipientBase> recipientBaseList = recipientBase.get(brandCode);
                    if (CollectionUtil.isNotEmpty(recipientBaseList)) {
                        Set<String> collect = recipientBaseList.stream().map(VocRiskProcessRecipientBase::getType)
                                .map(String::valueOf).collect(Collectors.toSet());
                        types.addAll(collect);
                    }
                    List<VocRiskAllTypes> othTagList = othTag.get(brandCode);
                    if (CollectionUtil.isNotEmpty(othTagList)) {
                        Set<String> collect = othTagList.stream().map(VocRiskAllTypes::getRiskType).collect(Collectors.toSet());
                        types.addAll(collect);
                    }
                    RiskBrandCodeVo riskBrandCodeVo = new RiskBrandCodeVo();
                    riskBrandCodeVo.setRiskBrandCode(brandCode);
                    List<DictVo> selectPermissions = questionType.stream().filter(s -> types.contains(s.getValue())).collect(Collectors.toList());
                    riskBrandCodeVo.setSelectPermissions(selectPermissions);
                    riskBrandCodeVoList.add(riskBrandCodeVo);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(riskBrandCodeVoList)) {
            List<String> codes = riskBrandCodeVoList.stream().map(RiskBrandCodeVo::getRiskBrandCode).collect(Collectors.toList());
            Map<String, RiskBrandCodeVo> brandCodeVoMap = riskBrandCodeVoList.stream().collect(Collectors.toMap(RiskBrandCodeVo::getRiskBrandCode, Function.identity()));
            QueryWrapper<BrandProductManager> queryBrandProduct = new QueryWrapper<>();
            queryBrandProduct.lambda().eq(BrandProductManager::getPId, 0);
            queryBrandProduct.lambda().orderByAsc(BrandProductManager::getSortNo);
            Page<BrandProductManager> page = new Page<>(0, 100);
            IPage<BrandProductManager> pageList = brandProductManagerService.page(page, queryBrandProduct);
            List<BrandProductManager> records = pageList.getRecords();
            log.info("品牌重新排序:{}", JSONUtil.toJsonStr(records));
            List<RiskBrandCodeVo> sortList = new ArrayList<>();
            for (BrandProductManager brandProductManager : records) {
                if (codes.contains(brandProductManager.getBrandCode())) {
                    sortList.add(brandCodeVoMap.get(brandProductManager.getBrandCode()));
                }
            }
            return sortList;
        }
        return riskBrandCodeVoList;
    }

    @Override
    public List<FaultProblemTreeVo> riskAllQuality(String brandCode) {
        List<FaultProblemTreeVo> ls = new ArrayList<>();
        LoginUser userInfo = getUserInfo();
        String userId = userInfo.getId();
        QueryWrapper<VocRiskAlertReviewer> reviewerQueryWrapper = new QueryWrapper<>();
        reviewerQueryWrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, userId)
                .eq(VocRiskAlertReviewer::getDelFlag, 0);
        VocRiskAlertReviewer vocRiskAlertReviewer = reviewerMapper.selectOne(reviewerQueryWrapper);
        boolean quality = Boolean.FALSE;
        if (ObjectUtils.isEmpty(vocRiskAlertReviewer)) {
            List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(userId);
            if (CollectionUtil.isNotEmpty(sysRoles)) {
                SysRole sysRole = sysRoles.get(0);
                quality = getQuality(brandCode, sysRole.getQualityText());
            }
        } else {
            quality = getQuality(brandCode);
        }
        if (!quality) {
            return ls;
        }
        QueryWrapper<FaultProblem> wrapper = new QueryWrapper<>();
        wrapper.last("where pid=0 ");
        List<FaultProblem> tags = faultProblemService.list(wrapper);
        ls = BeanUtil.copyToList(tags, FaultProblemTreeVo.class);
        ls.forEach(e -> {
            QueryWrapper<FaultProblem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(FaultProblem::getPid, e.getId());
            List<FaultProblem> tags2 = faultProblemService.list(queryWrapper);
            e.setChildes(BeanUtil.copyToList(tags2, FaultProblemTreeVo.class));
        });
        return ls;
    }

    @Override
    public List<VocBusinessTagVo> riskAllTopics(String brandCode) {
        List<VocBusinessTagVo> ls = new ArrayList<>();
        try {
            LoginUser userInfo = getUserInfo();
            String userId = userInfo.getId();
            QueryWrapper<VocRiskAlertReviewer> reviewerQueryWrapper = new QueryWrapper<>();
            reviewerQueryWrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, userId)
                    .eq(VocRiskAlertReviewer::getDelFlag, 0);
            VocRiskAlertReviewer vocRiskAlertReviewer = reviewerMapper.selectOne(reviewerQueryWrapper);
            List<String> roleTagCodes = new ArrayList<>();
            if (ObjectUtils.isEmpty(vocRiskAlertReviewer)) {
                Set<Object> permissionSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, brandCode, userId));
                roleTagCodes = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());
            } else {
                roleTagCodes = this.getRoleTagCodes(brandCode);
            }

            if (CollectionUtil.isEmpty(roleTagCodes)) {
                return ls;
            }
            QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(VocBusinessTag::getPid, "0");
            wrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
            List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
            ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
            List<VocBusinessTagVo> list = new ArrayList<>();
            for (VocBusinessTagVo l : ls) {
                if (roleTagCodes.contains(l.getId()) || roleTagCodes.contains(l.getTagCode())) {
                    list.add(l);
                }
            }
            ls = list;
            ls.sort(Comparator.comparing(VocBusinessTag::getOrderBy));

            for (VocBusinessTagVo e : ls) {
                QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(VocBusinessTag::getPid, e.getId());
                List<VocBusinessTag> tags2 = vocBusinessTagService.list(queryWrapper);
                List<VocBusinessTag> result = new ArrayList<>();
                for (VocBusinessTag s : tags2) {
                    if (roleTagCodes.contains(s.getId()) || roleTagCodes.contains(s.getTagCode())) {
                        result.add(s);
                    }
                }
                tags2 = result;
                List<VocBusinessTagVo> seouns = BeanUtil.copyToList(tags2, VocBusinessTagVo.class);
                seouns.sort(Comparator.comparing(VocBusinessTag::getOrderBy));
                e.setChildes(seouns);
            }
            return ls;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ls;
        }
    }


    /**
     * 角色权限
     *
     * @return
     */
    private List<String> getRoleTagCodes(String brandCode) {

        List<String> codes = new ArrayList<>();
        LoginUser userInfo = getUserInfo();
        List<VocRiskProcessRecipientTag> recipientTags = riskProessRecipientService.queryPermissionByUserId(userInfo.getId());
        if (CollectionUtil.isEmpty(recipientTags)) {
            return codes;
        }
        List<VocRiskProcessRecipientTag> processRecipientTags = recipientTags.stream().filter(r -> r.getBrandCode().equals(brandCode)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(processRecipientTags)) {
            return codes;
        }
        List<VocRiskProcessRecipientTag> tags = processRecipientTags.stream().filter(p -> "0".equals(p.getTagParentId())).collect(Collectors.toList());
        List<String> idList = tags.stream().map(VocRiskProcessRecipientTag::getTagId).collect(Collectors.toList());
        codes.addAll(idList);
        List<String> tagParentIdList = processRecipientTags.stream().filter(p -> !"0".equals(p.getTagParentId())).map(VocRiskProcessRecipientTag::getTagParentId).collect(Collectors.toList());
        codes.addAll(tagParentIdList);
        return codes;
    }


    private boolean getQuality(String brandCode, String qualityText) {
        if (StrUtil.isNotEmpty(qualityText) && StrUtil.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }


    private boolean getQuality(String brandCode) {
        //质量、用户
        Boolean flag = Boolean.FALSE;
        LoginUser userInfo = getUserInfo();
        List<VocRiskProcessRecipientBase> recipientQualityTags = riskProessRecipientService.queryPermissionQualityByUserId(userInfo.getId());
        List<VocRiskProcessRecipientBase> qualityTags = recipientQualityTags.stream()
                .filter(r -> r.getBrandCode().equals(brandCode) && r.getType() != null && r.getType() == 2)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(qualityTags)) {
            flag = Boolean.TRUE;
        }
        return flag;
    }


    LoginUser getUserInfo() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return sysUser;
    }

    /**
     * 优化后的风险列表处理方法
     */
    public void comRiskListBatch(IPage<RiskAllTypesVo> relist) {
        if (CollectionUtil.isEmpty(relist.getRecords())) {
            return;
        }

        // 1. 收集所有需要的ID
        Set<String> riskIds = new HashSet<>();

        riskIds.addAll(relist.getRecords().stream()
                .map(RiskAllTypesVo::getId)
                .collect(Collectors.toList()));

        // 2. 批量查询数据
        Map<String, SysUser> userMap = userService.list().stream()
                .collect(Collectors.toMap(SysUser::getId, Function.identity()));

        Map<String, SysDepart> departMap = departService.list().stream()
                .collect(Collectors.toMap(SysDepart::getId, Function.identity()));

        Map<String, VocProjectGroup> projectMap = projectGroupService.list().stream()
                .collect(Collectors.toMap(VocProjectGroup::getId, Function.identity()));
        // 获取当前用户ID
        String currentUserId = getUserInfo().getId();
        // 检查是否是审核人
        boolean isReviewer = riskAlertReviewerService.count(new LambdaQueryWrapper<VocRiskAlertReviewer>()
                .eq(VocRiskAlertReviewer::getReviewerUserId, currentUserId)) > 0;


        // 3. 批量查询处理记录
        Map<String, List<VocRiskHandlingRecord>> recordsMap = getHandlingRecordsMap(riskIds);

        // 4. 处理每条记录
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.systemDefault());
        relist.getRecords().forEach(e -> processRiskRecord(e, userMap, departMap, projectMap, recordsMap.get(e.getId()), dateFormat, isReviewer));
    }

    /**
     * 批量获取处理记录
     */
    private Map<String, List<VocRiskHandlingRecord>> getHandlingRecordsMap(Set<String> riskIds) {
        if (CollectionUtil.isEmpty(riskIds)) {
            return new HashMap<>();
        }
        // 批量查询处理记录
        LambdaQueryWrapper<VocRiskHandlingRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(VocRiskHandlingRecord::getWarningRiskId, riskIds)
                .eq(VocRiskHandlingRecord::getDelFlag, 0)
                .orderByDesc(VocRiskHandlingRecord::getCreateTime);
        return handlingRecordService.list(wrapper).stream()
                .collect(Collectors.groupingBy(VocRiskHandlingRecord::getWarningRiskId));
    }

    /**
     * 处理单条风险记录
     */
    private void processRiskRecord(RiskAllTypesVo risk,
                                   Map<String, SysUser> userMap,
                                   Map<String, SysDepart> departMap,
                                   Map<String, VocProjectGroup> projectMap,
                                   List<VocRiskHandlingRecord> records,
                                   DateTimeFormatter dateFormat, boolean isReviewer) {
        // 处理审核时间
        if (Objects.isNull(risk.getAuditTime()) && Objects.nonNull(risk.getCancelTime())) {
            risk.setAuditTime(risk.getCancelTime());
        }

        // 设置洞察周期时间
        setUserNum(risk);
        // 设置处理时间
        setProcessingTime(risk, dateFormat);

        // 处理记录为空的情况
        if (CollectionUtil.isEmpty(records)) {
            return;
        }

        // 设置处理记录
        List<WarningReviewList> reviewList = new ArrayList<>();
        records.forEach(record -> {
            if (record.getProcessUserId() != null) {
                SysUser user = userMap.get(record.getProcessUserId());
                if (user != null) {
                    record.setProcessUserName(user.getRealname());

                    // 设置部门/项目组信息
                    if (record.getBindType() == 1) {
                        SysDepart depart = departMap.get(record.getProcessDepartId());
                        if (depart != null) {
                            record.setProcessDepartName(depart.getDepartName());
                        }
                    } else {
                        VocProjectGroup project = projectMap.get(record.getProcessDepartId());
                        if (project != null) {
                            record.setProcessDepartName(project.getProjectName());
                        }
                    }

                    // 添加到审核列表
                    if (record.getProcessStatus() != 3) {
                        WarningReviewList review = new WarningReviewList();
                        review.setProcessUserName(user.getRealname());
                        review.setProcessUserId(record.getProcessUserId());
                        review.setProcessDepartName(record.getProcessDepartName());
                        reviewList.add(review);
                    }
                }
            }
        });

        // 设置审核列表
        risk.setWarningReviewListList(reviewList);

        // 设置风险状态
        setRiskState(risk, records, isReviewer);
    }

    /**
     * 设置处理时间
     */
    private void setProcessingTime(RiskAllTypesVo risk, DateTimeFormatter dateFormat) {
        String dateTime = "";
        String dateDay = "";
        if (risk.getRiskState() == 1 || risk.getRiskState() == 4 || risk.getRiskState() == 7) {
            dateTime = dateFormat.format(risk.getAuditTime().toInstant()) + "-";
        } else if (risk.getRiskState() == 3 &&
                Objects.nonNull(risk.getAuditTime()) &&
                Objects.nonNull(risk.getConfirmationTime())) {
            dateTime = dateFormat.format(risk.getAuditTime().toInstant()) + "-" +
                    dateFormat.format(risk.getConfirmationTime().toInstant());

            DateTime startDate = DateUtil.parse(dateFormat.format(risk.getAuditTime().toInstant()));
            DateTime endDate = DateUtil.parse(dateFormat.format(risk.getConfirmationTime().toInstant()));
            dateDay = String.valueOf(startDate.between(endDate, DateUnit.DAY) + 1);
        } else {
            dateTime = "-";
        }
        risk.setProcessingTime(dateTime);
        risk.setProcessingDays(dateDay);
    }

    /**
     * 设置风险状态
     */
    private void setRiskState(RiskAllTypesVo risk, List<VocRiskHandlingRecord> records, boolean isReviewer) {
        // 获取当前用户ID
        String currentUserId = getUserInfo().getId();

        // 获取当前用户的处理记录
        List<VocRiskHandlingRecord> userRecords = records.stream()
                .filter(r -> r.getProcessUserId() != null &&
                        r.getProcessUserId().equals(currentUserId))
                .collect(Collectors.toList());

        // 处理状态逻辑
        if (CollectionUtil.isNotEmpty(userRecords)) {
            // 获取当前用户最新的处理记录
            VocRiskHandlingRecord latestRecord = userRecords.get(0);

            switch (latestRecord.getProcessStatus()) {
                case 0: // 待处理
                    risk.setRiskState(1);
                    risk.setIsDistribution(true);
                    // 检查其他用户是否有处理中的记录
                    boolean hasOthersProcessing = records.stream()
                            .anyMatch(r -> !r.getProcessUserId().equals(currentUserId) &&
                                    r.getProcessStatus() != 3 &&
                                    r.getProcessStatus() != 0);
                    if (hasOthersProcessing) {
                        risk.setIsDistribution(false);
                    }
                    break;

                case 2: // 确认处理
                    risk.setRiskState(4);
                    break;

                case 1: // 完成处理
                    risk.setRiskState(7);
                    break;

                case 3: // 撤回
                    if (!isReviewer) {
                        risk.setRiskState(6);
                    } else {
                        // 获取其他用户的非撤回记录
                        List<VocRiskHandlingRecord> othersRecords = records.stream()
                                .filter(r -> !r.getProcessUserId().equals(currentUserId) &&
                                        r.getProcessStatus() != 3)
                                .sorted(Comparator.comparing(VocRiskHandlingRecord::getCreateTime)
                                        .reversed())
                                .collect(Collectors.toList());

                        if (CollectionUtil.isNotEmpty(othersRecords)) {
                            VocRiskHandlingRecord latestOther = othersRecords.get(0);
                            switch (latestOther.getProcessStatus()) {
                                case 0:
                                    risk.setRiskState(1);
                                    risk.setIsDistribution(true);
                                    break;
                                case 2:
                                    risk.setRiskState(4);
                                    break;
                                case 1:
                                    risk.setRiskState(7);
                                    break;
                            }
                        } else {
                            risk.setRiskState(6);
                        }
                    }
                    break;
            }
        } else {
            // 非当前用户处理的记录
            boolean hasPending = records.stream()
                    .anyMatch(r -> r.getProcessStatus() == 0);
            if (hasPending) {
                risk.setRiskState(1);
                risk.setIsDistribution(true);
            }

            boolean hasProcessing = records.stream()
                    .anyMatch(r -> r.getProcessStatus() == 2);
            if (hasProcessing) {
                risk.setRiskState(4);
            }
        }

        // 处理完成状态
        List<VocRiskHandlingRecord> activeRecords = records.stream()
                .filter(r -> r.getProcessStatus() != 3)
                .collect(Collectors.toList());

        List<VocRiskHandlingRecord> completedRecords = records.stream()
                .filter(r -> r.getProcessStatus() == 1)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(completedRecords)) {
            if (activeRecords.size() == completedRecords.size()) {
                // 所有记录都完成
                risk.setRiskState(3);
                risk.setFinishTag(Boolean.TRUE);
            } else if (CollectionUtil.isNotEmpty(userRecords)) {
                // 当前用户完成
                VocRiskHandlingRecord userRecord = userRecords.get(0);
                if (userRecord.getProcessStatus() == 1) {
                    risk.setRiskState(3);
                    risk.setConfirmationTime(userRecord.getProcessTime());
                    risk.setFinishTag(Boolean.FALSE);
                }
            } else {
                risk.setRiskState(7);
            }
        }

        // 处理撤回状态
        boolean userRevoked = records.stream()
                .anyMatch(r -> r.getProcessStatus() == 3 &&
                        r.getProcessUserId().equals(currentUserId));
        if (userRevoked && !isReviewer) {
            risk.setRiskState(6);
        }

        // 设置处理记录
        risk.setRiskHandlingRecords(records);
    }

    @Override
    public List<RiskAllTypesVo> problemOverviewLevel(FilterCriteriaModel model) {
        List<RiskAllTypesVo> riskAllTypesVos = riskAlertReviewerService.problemOverviewLevel(model);
        return riskAllTypesVos;
    }

    @Override
    public List<RiskAllTypesVo> problemOverviewDepart(FilterCriteriaModel model) {
        List<RiskAllTypesVo> riskAllTypesVos = riskAlertReviewerService.problemOverviewDepart(model);
        return riskAllTypesVos;
    }

    @Override
    public RiskAllTypesVo processingProgress2(FilterCriteriaModel model) {
        return riskAlertReviewerService.processingProgress(model);
    }
}
