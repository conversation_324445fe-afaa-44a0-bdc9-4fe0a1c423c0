package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.SysRoleBusinessTag;
import com.car.voc.entity.SysRoleChannel;
import com.car.voc.mapper.SysRoleBusinessTagMapper;
import com.car.voc.service.SysRoleBusinessTagService;
import com.car.voc.vo.risk.TagRoleVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
*
*/
@Service
public class SysRoleBusinessTagServiceImpl extends ServiceImpl<SysRoleBusinessTagMapper, SysRoleBusinessTag>
            implements SysRoleBusinessTagService {

    @Override
    public void saveOrUpdateData(String roleId, List<String> tagIdList,String brandCode){
        if(tagIdList == null) {
            return;
        }
        QueryWrapper<SysRoleBusinessTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRoleBusinessTag::getRoleId,roleId).eq(SysRoleBusinessTag::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
        List<SysRoleBusinessTag> list = this.list(queryWrapper);
        if(!CollectionUtils.isEmpty(list)){
            UpdateWrapper<SysRoleBusinessTag> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SysRoleBusinessTag::getRoleId,roleId);
            updateWrapper.lambda().eq(SysRoleBusinessTag::getBrandCode,brandCode).eq(SysRoleBusinessTag::getDelFlag, CommonConstant.DEL_FLAG_STR_0)
                    .set(SysRoleBusinessTag::getDelFlag, CommonConstant.DEL_FLAG_STR_1);
            this.update(updateWrapper);
        }
        List<SysRoleBusinessTag> tags=new ArrayList<>();
        tagIdList.stream().forEach(tagCode ->{
            SysRoleBusinessTag sysRoleBusinessTag = new SysRoleBusinessTag();
            sysRoleBusinessTag.setTagCode(tagCode);
            sysRoleBusinessTag.setRoleId(roleId);
            sysRoleBusinessTag.setBrandCode(brandCode);
            tags.add(sysRoleBusinessTag);
        });
        this.saveBatch(tags);
    }

    @Override
    public List<TagRoleVo> queryRoleNameByTagCode(String[] strings) {
        return baseMapper.queryRoleNameByTagCode(strings);
    }
}
