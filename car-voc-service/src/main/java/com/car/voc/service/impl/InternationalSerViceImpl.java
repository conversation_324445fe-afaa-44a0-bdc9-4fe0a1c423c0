package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.service.*;
import com.car.voc.vo.InternationalBaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * @version 1.0.0
 * @ClassName InternationalSerViceImpl.java
 * @Description TODO
 * @createTime 2023年01月09日 16:24
 * @Copyright voc
 */
@Service
public class InternationalSerViceImpl implements InternationalSerVice {

    @Autowired
    InternationalSerVice internationalSerVice;

    @Autowired
    IVocBusinessTagService tagService;
    @Autowired
    IFaultProblemService qualityService;
    @Autowired
    VocBrandRegionService brandRegionService;
    @Autowired
    RedisUtil redisUtil;
    @Override
    public Map internationalList() {


        Map<String,Object> inters=null;
//        inters= (Map<String, Object>) redisUtil.get(CacheConstant.internationalList);
        if (inters==null){
            inters=new HashMap<>();
            Map<String,Object> chinese=new HashMap<>();
            Map<String,Object> english=new HashMap<>();
            setBusinessTags(inters,chinese,english);
            setQualityTags(inters,chinese,english);
            setChannelLanguage(inters,chinese,english);
            setCarsLanguage(inters,chinese,english);
            setEmotionIntention(inters,chinese,english);
            setEnergyType(inters,chinese,english);
            setProvincesRegions(inters,chinese,english);
            setCarType(inters,chinese,english);
            setUserType(inters,chinese,english);
            setRiskType(inters,chinese,english);
            inters.put("chinese",chinese);
            inters.put("english",english);
            redisUtil.set(CacheConstant.internationalList,inters,60*60*12);
        }
        return inters;
    }

    private void setRiskType(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        QueryWrapper<SysDictItem> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(SysDictItem::getItemText,SysDictItem::getItemKey,SysDictItem::getItemValue,SysDictItem::getItemTextEn);
        wrapper.lambda().eq(SysDictItem::getDictId,CommonConstant.sys_risk_type_id);
        List<SysDictItem> tagsCn=iSysDictItemService.list(wrapper);
        Map<String,String> typeCn=new HashMap<>();
        Map<String,String> typeEn=new HashMap<>();
        tagsCn.forEach(e->typeCn.put(e.getItemValue(),e.getItemText()));
        tagsCn.forEach(e->typeEn.put(e.getItemValue(),e.getItemTextEn()));

        /*tagsCn.forEach(e-> typeCn.put(e.getKey(),e.getText()));
        tagsCn.forEach(k->typeEn.put(k.getKey(),k.getText()));*/
        chinese.put("riskType",typeCn);
        english.put("riskType",typeEn);
    }


    private void setCarType(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
    }


    private void setEnergyType(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        Map<String,String> energyTypeCn=new HashMap<>();
        Map<String,String> energyTypeEn=new HashMap<>();
        energyTypeCn.put("id_pure_electricity","ID.纯电");
        energyTypeEn.put("id_pure_electricity","ID.EV");
        energyTypeCn.put("fuel_truck","燃油车");
        energyTypeEn.put("fuel_truck","Fuel truck");
        energyTypeCn.put("new_energy","新能源");
        energyTypeEn.put("new_energy","New energy");
        chinese.put("energyType",energyTypeCn);
        english.put("energyType",energyTypeEn);
    }

    private void setEmotionIntention(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        Map<String,String> eICn=new HashMap<>();
        Map<String,String> eIEn=new HashMap<>();

    }
    @Autowired
    ISysDictItemService iSysDictItemService;
    private void setUserType(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        QueryWrapper<SysDictItem> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(SysDictItem::getItemText,SysDictItem::getItemKey,SysDictItem::getItemTextEn);
        wrapper.lambda().eq(SysDictItem::getDictId,CommonConstant.sys_user_type_id);
        List<SysDictItem> tagsCn=iSysDictItemService.list(wrapper);
        sysDictItemKeyLanageue(tagsCn,"userType",chinese,english);

    }
    private void setProvincesRegions(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {

        QueryWrapper<SysDictItem> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(SysDictItem::getItemText,SysDictItem::getItemValue,SysDictItem::getItemTextEn,SysDictItem::getDictId);
        wrapper.lambda().in(SysDictItem::getDictId,new String[]{CommonConstant.sys_province_id,CommonConstant.sys_region_id,CommonConstant.sys_car_type_id});
        List<SysDictItem> tagsCn=iSysDictItemService.list(wrapper);
        Map<String,List<SysDictItem>> proviregiosyscar=tagsCn.stream().collect(Collectors.groupingBy(SysDictItem::getDictId));
        tagsCn=new ArrayList<>();
        tagsCn.addAll(proviregiosyscar.get(CommonConstant.sys_province_id));
//        tagsCn.addAll(proviregiosyscar.get(CommonConstant.sys_region_id));
        List<SysDictItem> carTypes=proviregiosyscar.get(CommonConstant.sys_car_type_id);
        sysDictItemLanageue(carTypes,"carType",chinese,english);
        List<SysDictItem> regions= brandRegionService.queryInternational();
        SysDictItem re1=new SysDictItem();
        re1.setItemText("销售区域");re1.setItemTextEn("Sales territory");re1.setItemValue("Sales_territory");
        regions.add(0,re1);
        SysDictItem re2=new SysDictItem();
        re2.setItemText("售后区域");re2.setItemTextEn("Aftermarket area");re2.setItemValue("Aftermarket_area");
        regions.add(0,re2);
        tagsCn.addAll(regions);
        Map<String, Object> chineseCars =(Map<String, Object>)chinese.get("cars");
        Map<String, Object> chineseCarsEn =(Map<String, Object>)english.get("cars");
        chineseCars.putAll((Map<String, Object>)chinese.get("carType"));
        chineseCarsEn.putAll((Map<String, Object>)english.get("carType"));
        chinese.put("cars",chineseCars);
        english.put("cars",chineseCarsEn);


        sysDictItemLanageue(tagsCn,"provincesRegion",chinese,english);
    }

    private void sysDictItemLanageue(List<SysDictItem> carTypes, String carType, Map<String, Object> chinese, Map<String, Object> english) {
        HashMap<String,String> map=new HashMap<>();
        map.put("itemValue","key");map.put("itemText","text");
        CopyOptions copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagcn= BeanUtil.copyToList(carTypes,InternationalBaseVo.class,copyOptions);
        map.remove("itemText"); map.put("itemTextEn","text");
        copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagen= BeanUtil.copyToList(carTypes,InternationalBaseVo.class,copyOptions);
        Map<String,String> provincesRegionCn=new HashMap<>();
        Map<String,String> provincesRegionEn=new HashMap<>();
        tagcn.forEach(e-> provincesRegionCn.put(e.getKey(),e.getText()));
        tagen.forEach(k->provincesRegionEn.put(k.getKey(),k.getText()));
        chinese.put(carType,provincesRegionCn);
        english.put(carType,provincesRegionEn);
    }

    private void sysDictItemKeyLanageue(List<SysDictItem> carTypes, String carType, Map<String, Object> chinese, Map<String, Object> english) {
        HashMap<String,String> map=new HashMap<>();
        map.put("itemKey","key");map.put("itemText","text");
        CopyOptions copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagcn= BeanUtil.copyToList(carTypes,InternationalBaseVo.class,copyOptions);
        map.remove("itemText"); map.put("itemTextEn","text");
        copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagen= BeanUtil.copyToList(carTypes,InternationalBaseVo.class,copyOptions);
        Map<String,String> typeCn=new HashMap<>();
        Map<String,String> typeEn=new HashMap<>();
        tagcn.forEach(e-> typeCn.put(e.getKey(),e.getText()));
        tagen.forEach(k->typeEn.put(k.getKey(),k.getText()));
        typeCn.put("CDP_user","CDP用户");
        typeCn.put("not_CDP_user","非CDP用户");
        typeEn.put("CDP_user","CDP users");
        typeEn.put("not_CDP_user","Non-CDP users");
        chinese.put(carType,typeCn);
        english.put(carType,typeEn);
    }

    @Autowired
    IBrandProductManagerService carService;
    private void setCarsLanguage(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        QueryWrapper<BrandProductManager> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(BrandProductManager::getCode,BrandProductManager::getName,BrandProductManager::getEnglishName);
        List<BrandProductManager> tagsCn=carService.list(wrapper);
        HashMap<String,String> map=new HashMap<>();
        map.put("code","key");map.put("name","text");
        CopyOptions copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagcn= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        map.remove("name"); map.put("englishName","text");
        copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagen= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        Map<String,String> carsCn=new HashMap<>();
        Map<String,String> carsEn=new HashMap<>();
        tagcn.forEach(e-> carsCn.put(e.getKey(),e.getText()));
        tagen.forEach(k->carsEn.put(k.getKey(),k.getText()));
        carsCn.put("all-vehicle-series","全部车系");
        carsEn.put("all-vehicle-series","All Cars");
        chinese.put("cars",carsCn);
        english.put("cars",carsEn);
    }

    @Autowired
    IVocChannelCategoryService categoryService;
    private void setChannelLanguage(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        QueryWrapper<VocChannelCategory> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(VocChannelCategory::getId,VocChannelCategory::getName,VocChannelCategory::getNameEn);
        List<VocChannelCategory> tagsCn=categoryService.list(wrapper);
        HashMap<String,String> map=new HashMap<>();
        map.put("id","key");map.put("name","text");
        CopyOptions copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagcn= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        map.remove("name"); map.put("nameEn","text");
        copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagen= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        Map<String,String> channelCn=new HashMap<>();
        Map<String,String> channelEn=new HashMap<>();
        tagcn.forEach(e-> channelCn.put(e.getKey(),e.getText()));
        tagen.forEach(k->channelEn.put(k.getKey(),k.getText()));
        chinese.put("channel",channelCn);
        english.put("channel",channelEn);
    }

    private void setBusinessTags(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        QueryWrapper<VocBusinessTag> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(VocBusinessTag::getTagCode,VocBusinessTag::getName,VocBusinessTag::getNameEn);
        List<VocBusinessTag> tagsCn=tagService.list(wrapper);
        HashMap<String,String> map=new HashMap<>();
        map.put("tagCode","key");map.put("name","text");
        CopyOptions copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagcn= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        map.remove("name"); map.put("nameEn","text");
        copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagen= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        Map<String,String> businessTagCn=new HashMap<>();
        Map<String,String> businessTagEn=new HashMap<>();
        tagcn.forEach(e-> businessTagCn.put(e.getKey(),e.getText()));
        tagen.forEach(k->businessTagEn.put(k.getKey(),k.getText()));
        chinese.put("businessTag",businessTagCn);
        english.put("businessTag",businessTagEn);
    }

    private void setQualityTags(Map<String, Object> inters, Map<String, Object> chinese, Map<String, Object> english) {
        QueryWrapper<FaultProblem> wrapper=new QueryWrapper<>();
        wrapper.lambda().select(FaultProblem::getCode,FaultProblem::getName,FaultProblem::getNameEn);
        List<FaultProblem> tagsCn=qualityService.list(wrapper);
        HashMap<String,String> map=new HashMap<>();
        map.put("code","key");map.put("name","text");
        CopyOptions copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagcn= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        map.remove("name"); map.put("nameEn","text");
        copyOptions=CopyOptions.create().setFieldMapping(map);
        List<InternationalBaseVo> tagen= BeanUtil.copyToList(tagsCn,InternationalBaseVo.class,copyOptions);
        Map<String,String> qualityTagCn=new HashMap<>();
        Map<String,String> qualityTagEn=new HashMap<>();
        qualityTagCn.put("Q0001","质量故障");
        tagcn.forEach(e-> qualityTagCn.put(e.getKey(),e.getText()));
        tagen.forEach(k->qualityTagEn.put(k.getKey(),k.getText()));
        chinese.put("qualityTag",qualityTagCn);
        english.put("qualityTag",qualityTagEn);
    }
}
