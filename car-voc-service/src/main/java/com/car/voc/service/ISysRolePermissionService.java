package com.car.voc.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.SysRolePermission;

import java.util.List;

/**
 * <p>
 * 角色权限表 服务类
 * </p>
 */
public interface ISysRolePermissionService extends IService<SysRolePermission> {

	/**
	 * 保存授权/先删后增
	 * @param roleId
	 * @param permissionIds
	 */
	public void saveRolePermission(String roleId,String permissionIds);


	void saveOrUpdateData(String roleId, List<String> permissionIds);

}
