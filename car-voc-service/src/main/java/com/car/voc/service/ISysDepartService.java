package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.SysDepart;
import com.car.voc.model.SysDepartModel;
import com.car.voc.vo.DictVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 部门表 服务实现类
 * <p>
 */
public interface ISysDepartService extends IService<SysDepart> {

    void saveDepartData(SysDepart sysDepart, String username);

    Result<SysDepart> add(SysDepartModel sysDepart, HttpServletRequest request);

    boolean delete(String id);

    SysDepart getDepartByUserId(String userId);

    boolean updateDepartDataById(SysDepart sysDepart, String username);

    List<DictVo> buildDepartTree(String brandCode);
}
