package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.sync.GetCCApiTokenService;
import com.car.voc.common.util.PasswordUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.mapper.SysRoleMapper;
import com.car.voc.mapper.SysUserMapper;
import com.car.voc.mapper.VocChannelCategoryMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.model.SysRoleModel;
import com.car.voc.model.TreeModel;
import com.car.voc.service.*;
import com.car.voc.vo.ChannelCategoryVo;
import com.car.voc.vo.auth.RoleAuthTree;
import com.car.voc.vo.auth.RoleAuthVo;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.ibatis.annotations.Update;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import wiremock.org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * @since 2018-12-19
 */
@Log4j2
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {
    @Autowired
    SysRoleMapper sysRoleMapper;
    @Autowired
    SysUserMapper sysUserMapper;
    @Autowired
    ISysDepartService departService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ISysRoleService sysRoleService;
    @Autowired
    SysRoleChannelService sysRoleChannelService;
    @Autowired
    SysRoleBusinessTagService sysRoleBusinessTagService;
    @Autowired
    SysRoleSeriesService sysRoleSeriesService;
    @Autowired
    ISysPermissionService sysPermissionService;
    @Autowired
    ISysRolePermissionService sysRolePermissionService;
    @Autowired
    SysRoleAreaService sysRoleAreaService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    GetCCApiTokenService ccApiTokenService;
    @Autowired
    ISysUserDepartService sysUserDepartService;
    @Autowired
    ISysUserRoleService sysUserRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(String roleid) throws Exception {
        SysUserRole sysUserRole = sysRoleMapper.getUserRoleByRoleId(roleid);
        if (sysUserRole != null) {
            throw new Exception("该角色已绑定账号不可禁用或删除，请解除后再操作");
        }
        //1.删除角色和用户关系
        sysRoleMapper.deleteRoleUserRelation(roleid);
        //2.删除角色和权限关系
        sysRoleMapper.deleteRolePermissionRelation(roleid);
        //3.删除角色
        UpdateWrapper<SysRole> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysRole::getId, roleid).set(SysRole::getDelFlag, "1");
        sysRoleMapper.update(null, updateWrapper);
        //清除接口权限缓存
        redisUtil.removeAll(CacheConstant.SYS_USER_API_PERMISSION);
        return true;
    }

    @Override
    public Result<Map<String, Object>> queryTreeList(SysRoleModel roleModel, HttpServletRequest request) {
        Result<Map<String, Object>> result = new Result<>();
        //全部权限ids
        List<String> ids = new ArrayList<>();
        try {
            LambdaQueryWrapper<SysPermission> query = new LambdaQueryWrapper<SysPermission>();
            query.eq(SysPermission::getDelFlag, CommonConstant.DEL_FLAG_0);
            query.orderByAsc(SysPermission::getSortNo);
            List<SysPermission> list = sysPermissionService.list(query);
            for (SysPermission sysPer : list) {
                ids.add(sysPer.getId());
            }
            List<TreeModel> treeList = new ArrayList<>();
            getTreeModelList(treeList, list, null);
            Map<String, Object> resMap = new HashMap<String, Object>();
            List<SysRolePermission> checklist = sysRolePermissionService.list(new QueryWrapper<SysRolePermission>().lambda().eq(SysRolePermission::getRoleId, roleModel.getId()).eq(SysRolePermission::getDelFlag, CommonConstant.DEL_FLAG_STR_0));
            //要选中的ids
            resMap.put("checkIds", checklist.stream().map(SysRolePermission -> String.valueOf(SysRolePermission.getPermissionId())).collect(Collectors.toList()));
            resMap.put("roleInfo", sysRoleMapper.selectById(roleModel.getId()));
            resMap.put("allTreeList", treeList); //全部树节点数据
            resMap.put("allIds", ids);//全部树ids
            result.setResult(resMap);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public Result<SysRole> saveOrUpdate(SysRoleModel roleModel) {
        SysRole role = new SysRole();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        BeanUtil.copyProperties(roleModel, role);
        role.setCreateBy(loginUser.getId());
        role.setBrandCode(String.join(",", roleModel.getBrandCode()));
        Result<SysRole> result = new Result<SysRole>();
        try {
            if (StrUtil.isNotBlank(role.getId())) {
                role.setUpdateTime(new Date());
            } else {
                role.setCreateTime(new Date());
            }
            if (StrUtil.isNotBlank(roleModel.getId())) {
                SysUserRole sysUserRole = sysRoleMapper.getUserRoleByRoleId(roleModel.getId());
                SysRole sysRole = sysRoleMapper.getRoleByRoleId(roleModel.getId());
                if (sysUserRole != null && sysRole.isRoleStatus() && !roleModel.isRoleStatus()) {
                    return result.error500("该角色已绑定账号不可禁用或删除，请解除后再操作");
                }
            }
            role.setAll(roleModel.isAllPermission());
            QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysRole::getRoleName, roleModel.getRoleName());
            List<SysRole> roles = this.list(queryWrapper);
            if (roles != null && roles.size() > 0 && roleModel.getId() == null) {
                return result.error500("角色名已存在");
            }

            if (!(roleModel.getTagIds() != null && roleModel.getTagIds().size() > 0)) {
                return result.error500("至少选择一个业务标签");
            }
            this.saveOrUpdate(role);
            //清除接口权限缓存
            redisUtil.removeAll(CacheConstant.SYS_USER_API_PERMISSION);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 新增更新新接口
     *
     * @param roleModelList
     * @return
     */
    @Override
    public Result<SysRole> newSaveOrUpdate(List<SysRoleModel> roleModelList) {

        SysRoleModel roleModel = roleModelList.get(0);
        List<String> brandCodeList = roleModelList.stream().map(SysRoleModel::getBrandCode).collect(Collectors.toList());
        Map<String, Boolean> booleanMap = roleModelList.stream().collect(Collectors.toMap(SysRoleModel::getBrandCode, SysRoleModel::isQuality));
        SysRole role = new SysRole();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        BeanUtil.copyProperties(roleModel, role);
        role.setCreateBy(loginUser.getId());
        role.setBrandCode(String.join(",", brandCodeList));
        role.setQualityText(JSON.toJSONString(booleanMap));
        Result<SysRole> result = new Result<SysRole>();
        try {
            if (StrUtil.isNotBlank(role.getId())) {
                role.setUpdateTime(new Date());
            } else {
                role.setCreateTime(new Date());
            }
            if (StrUtil.isNotBlank(roleModel.getId())) {
                SysUserRole sysUserRole = sysRoleMapper.getUserRoleByRoleId(roleModel.getId());
                SysRole sysRole = sysRoleMapper.getRoleByRoleId(roleModel.getId());
                if (sysUserRole != null && sysRole.isRoleStatus() && !roleModel.isRoleStatus()) {
                    return result.error500("该角色已绑定账号不可禁用或删除，请解除后再操作");
                }
            }
            role.setAll(roleModel.isAllPermission());
            QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysRole::getRoleName, roleModel.getRoleName());
            queryWrapper.lambda().eq(SysRole::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
            List<SysRole> roles = this.list(queryWrapper);
            if (roles != null && roles.size() > 0 && roleModel.getId() == null) {
                return result.error500("角色名已存在");
            }
            this.saveOrUpdate(role);
            sysRolePermissionService.saveOrUpdateData(role.getId(), roleModel.getPermissionIds());
            roleModelList.parallelStream().forEach(sysRoleModel -> {
                String roleId = role.getId();
                String brandCode = sysRoleModel.getBrandCode();
                CompletableFuture.allOf(
                        CompletableFuture.runAsync(() ->
                                sysRoleChannelService.saveOrUpdateData(roleId, sysRoleModel.getChannelIds(), brandCode)),
                        CompletableFuture.runAsync(() ->
                                sysRoleBusinessTagService.saveOrUpdateData(roleId, sysRoleModel.getTagIds(), brandCode)),
                        CompletableFuture.runAsync(() ->
                                sysRoleSeriesService.saveOrUpdateData(roleId, sysRoleModel.getSeriesIds(), brandCode)),
                        CompletableFuture.runAsync(() ->
                                sysRoleAreaService.saveOrUpdateData(roleId, sysRoleModel.getAreaIds(), brandCode))
                ).join();
            });
            //清除接口权限缓存
            redisUtil.removeAll(CacheConstant.SYS_USER_API_PERMISSION);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @Override
    public Result<List<RoleAuthVo>> queryList() {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRole::isRoleStatus, true).eq(SysRole::getDelFlag, "0");
        List<SysRole> roles = this.list(queryWrapper);
        List<RoleAuthVo> list = new ArrayList<>();
        roles.stream().forEach(record -> {
            RoleAuthVo roleAuthVo = queryById(record.getId(), null);
            roleAuthVo.setRoleName(record.getRoleName());
            roleAuthVo.setRoleId(record.getId());
            roleAuthVo.setDesc(record.getDescription());
            roleAuthVo.setAllPermission(record.isAll());
            roleAuthVo.setStatus(record.isRoleStatus());
            list.add(roleAuthVo);
        });
        return Result.OK(list);
    }

    @Override
    public Result<IPage<RoleAuthVo>> queryPageLit(SysRoleModel role, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        Result<IPage<RoleAuthVo>> result = new Result<IPage<RoleAuthVo>>();
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(role.getSearchKeyword()) && StrUtil.isNotBlank(role.getBrandCode())) {
            queryWrapper.lambda().like(SysRole::getBrandCode, role.getBrandCode()).and(wrapper -> wrapper.like(SysRole::getRoleName, role.getSearchKeyword()).or().like(SysRole::getDescription, role.getSearchKeyword()));
        } else if (StrUtil.isNotBlank(role.getSearchKeyword())) {
            queryWrapper.lambda().and(wrapper -> wrapper.like(SysRole::getRoleName, role.getSearchKeyword()).or().like(SysRole::getDescription, role.getSearchKeyword()));
        } else if (StrUtil.isNotBlank(role.getBrandCode())) {
            queryWrapper.lambda().like(SysRole::getBrandCode, role.getBrandCode());
        }

        queryWrapper.lambda().eq(SysRole::getDelFlag, CommonConstant.DEL_FLAG_STR_0)
                .orderByDesc(SysRole::getCreateTime);
        if (role.getQueryType() != null && role.getQueryType() == 1) {
            pageSize = 1000;
        }
        Page<SysRole> page = new Page<>(pageNo, pageSize);
//        queryWrapper.lambda().ne(SysRole::getRoleName,"超级管理员");
//        queryWrapper.lambda().eq(SysRole::isRoleStatus,1);
        IPage<SysRole> pageList = page(page, queryWrapper);
        List<RoleAuthVo> list = new ArrayList<>();
        if (role.getQueryType() != null && role.getQueryType() == 1) {
            IPage<RoleAuthVo> recordPage = new Page<>();
            pageList.getRecords().stream().forEach(e -> {
                RoleAuthVo roleAuthVo = new RoleAuthVo(e);
                list.add(roleAuthVo);
            });
            recordPage.setRecords(list);
            result.setSuccess(true);
            result.setResult(recordPage);
            return result;
        }


        pageList.getRecords().stream().forEach(record -> {

            String code = null;
            if (StringUtils.isNotEmpty(record.getBrandCode())) {
                List<String> brandList = Arrays.asList(record.getBrandCode().split(","));
                code = brandList.get(0);
            }
            RoleAuthVo roleAuthVo = queryById(record.getId(), code);
            roleAuthVo.setRoleName(record.getRoleName());
            roleAuthVo.setRoleId(record.getId());
            roleAuthVo.setDesc(record.getDescription());
            roleAuthVo.setAllPermission(record.isAll());
            roleAuthVo.setStatus(record.isRoleStatus());
            roleAuthVo.setRoleType(record.getRoleType());
            if (StringUtils.isNotEmpty(record.getBrandCode())) {
                roleAuthVo.setBrandCodeList(Arrays.asList(record.getBrandCode().split(",")));
            }
            list.add(roleAuthVo);
        });
        IPage<RoleAuthVo> recordPage = new Page<>();
        BeanUtil.copyProperties(pageList, recordPage, true);
        recordPage.setRecords(list);
        result.setSuccess(true);
        result.setResult(recordPage);
        return result;
    }

    @Autowired
    IVocChannelCategoryService channelService;
    @Autowired
    IVocBusinessTagService businessTagService;

    @Autowired
    IBrandProductManagerService brandProductManagerService;

    @Autowired
    VocChannelCategoryMapper vocChannelCategoryMapper;

    @Override
    public RoleAuthVo queryById(String id, String brandCode) {
        RoleAuthVo authVo = new RoleAuthVo();

        //标签
        List<RoleAuthTree> butags = baseMapper.relationBusinessTag(brandCode, id);
        authVo.setRelationBuTag(dealBusinessTag(authVo, butags, false));
        //渠道
        List<RoleAuthTree> channls = baseMapper.brandCodeDataChannel(brandCode, id);
        setRoleChannel(authVo, channls);
        //地域
        List<RoleAuthTree> areas = baseMapper.brandCodeDataArea(brandCode, id);
//        authVo.setArea(areas);
        authVo.setArea(regionCategory(authVo, areas, false));
        //车系
        List<RoleAuthTree> series = baseMapper.brandCodeDataSeries(brandCode, id);
        authVo.setRelationCar(dealCategory(authVo, series, false));

        List<RoleAuthTree> permission = baseMapper.roleDataPermission(id);
        authVo.setAppKanban(dealCategory(authVo, permission, true));
        SysRole role = this.getById(id);
        if (role == null) {
            return authVo;
        }
        SysUserRole sysUserRole = sysRoleMapper.getUserRoleByRoleId(id);
        if (sysUserRole == null) {
            authVo.setUse(false);
        } else {
            authVo.setUse(true);
        }
        authVo.setQuality(false);
        String qualityText = role.getQualityText();
        if (StringUtils.isNotEmpty(qualityText) && StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                authVo.setQuality(o);
            }
        }
        authVo.setDownload(role.isDownload());
        authVo.setDesensitization(role.getDesensitization());
        authVo.setDesensitizationVin(role.getDesensitizationVin());
        authVo.setExport(role.isExport());
        authVo.setBrandCode(role.getBrandCode());
        return authVo;
    }


    @Override
    public List<RoleAuthVo> getDataByBrandCode(String brandCode) {

        List<RoleAuthVo> roleAuthVoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(brandCode)) {
            RoleAuthVo roleAuthVo = queryById("999", brandCode);
            roleAuthVo.setBrandCode(brandCode);
            roleAuthVoList.add(roleAuthVo);
        } else {
            QueryWrapper<BrandProductManager> query = new QueryWrapper<>();
            query.lambda().eq(BrandProductManager::getPId, 0);
            query.lambda().orderByAsc(BrandProductManager::getSortNo);
            Page<BrandProductManager> page = new Page<>(0, 100);
            IPage<BrandProductManager> pageList = brandProductManagerService.page(page, query);
            List<BrandProductManager> records = pageList.getRecords();
            for (BrandProductManager brandProductManager : records) {
                RoleAuthVo roleAuthVo = queryById("999", brandProductManager.getCode());
                roleAuthVo.setBrandCode(brandProductManager.getCode());
                roleAuthVoList.add(roleAuthVo);

            }
        }
        return roleAuthVoList;
    }

    @Override
    public List<RoleAuthVo> getListByRoleId(String roleId) {

        SysRole sysRole = this.getById(roleId);
        String brandCode = sysRole.getBrandCode();
        List<RoleAuthVo> roleAuthVoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(brandCode)) {
            List<String> brandCodeList = Arrays.asList(brandCode.split(","));
            for (String code : brandCodeList) {
                RoleAuthVo roleAuthVo = queryById(roleId, code);
                roleAuthVo.setRoleName(sysRole.getRoleName());
                roleAuthVo.setRoleId(sysRole.getId());
                roleAuthVo.setDesc(sysRole.getDescription());
                roleAuthVo.setAllPermission(sysRole.isAll());
                roleAuthVo.setStatus(sysRole.isRoleStatus());
                roleAuthVo.setRoleType(sysRole.getRoleType());
                roleAuthVo.setBrandCode(code);
                if (StringUtils.isNotEmpty(sysRole.getBrandCode())) {
                    roleAuthVo.setBrandCodeList(Arrays.asList(sysRole.getBrandCode().split(",")));
                }
                roleAuthVoList.add(roleAuthVo);
            }
        } else {
            QueryWrapper<BrandProductManager> query = new QueryWrapper<>();
            query.lambda().eq(BrandProductManager::getPId, 0);
            query.lambda().orderByAsc(BrandProductManager::getSortNo);
            Page<BrandProductManager> page = new Page<>(0, 1);
            IPage<BrandProductManager> pageList = brandProductManagerService.page(page, query);
            List<BrandProductManager> records = pageList.getRecords();
            for (BrandProductManager brandProductManager : records) {
                RoleAuthVo roleAuthVo = queryById("999", brandProductManager.getCode());
                roleAuthVo.setBrandCode(brandProductManager.getCode());
                roleAuthVo.setRoleName(sysRole.getRoleName());
                roleAuthVo.setRoleId(sysRole.getId());
                roleAuthVo.setDesc(sysRole.getDescription());
                roleAuthVo.setAllPermission(sysRole.isAll());
                roleAuthVo.setStatus(sysRole.isRoleStatus());
                roleAuthVo.setRoleType(sysRole.getRoleType());
                roleAuthVoList.add(roleAuthVo);
            }
            log.error("brandCode为空之前老数据");
        }
        return roleAuthVoList;
    }

    @Override
    public List<ChannelCategoryVo> allChannel() {

        QueryWrapper<VocChannelCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByAsc(VocChannelCategory::getOrderBy);
        queryWrapper.lambda().eq(VocChannelCategory::isEnable, true);
        List<VocChannelCategory> categoryVos = vocChannelCategoryMapper.selectList(queryWrapper);
        List<ChannelCategoryVo> relsis = BeanUtil.copyToList(categoryVos, ChannelCategoryVo.class);

        List<ChannelCategoryVo> categoryVos1 = relsis.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
        List<ChannelCategoryVo> finalRelsis = relsis;
        categoryVos1.forEach(e -> {
            List<ChannelCategoryVo> seoslist = finalRelsis.stream().filter(a -> a.getPid().equals(e.getId())).collect(Collectors.toList());
            seoslist.forEach(s -> {
                List<ChannelCategoryVo> threes = finalRelsis.stream().filter(k -> k.getPid().equals(s.getId())).collect(Collectors.toList());
                s.setChildes(threes);
            });
            e.setChildes(seoslist);
        });
        return categoryVos1;
    }

    @Override
    public SysRole getRoleByUserId(String userId) {
        return baseMapper.getRoleByUserId(userId);
    }

    private static final String VOC_KEYWORD = "voc";

    @Override
    public void roleUserSyncJob() {
        try {
            // 1. 获取外部系统角色列表
            JsonNode roleList =ccApiTokenService.fromCCGetRoles();
            if(roleList.isEmpty()) {
                log.error("获取外部系统角色列表为空");
                return;
            }
            // 2. 查询本地数据并转换为高效集合
            Set<String> allUserNames = queryLocalUserNames();
            Set<String> allRoleNames = queryLocalRoleNames();
            Set<String> allDepartNames = queryLocalDepartNames();

            // 3. 遍历处理角色
            processRoles(roleList, allUserNames, allRoleNames, allDepartNames);
        } catch (Exception e) {
            log.error("角色用户同步任务异常: {}", e.getMessage(), e);
        }
    }

    // 查询本地用户名集合
    private Set<String> queryLocalUserNames() {
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(SysUser::getUsername);
        return sysUserMapper.selectList(wrapper).stream()
                .map(user -> user.getUsername().toLowerCase())
                .collect(Collectors.toSet());
    }

    // 查询本地角色名集合
    private Set<String> queryLocalRoleNames() {
        QueryWrapper<SysRole> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(SysRole::getRoleName)
                .eq(SysRole::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
        return sysRoleMapper.selectList(wrapper).stream()
                .map(role -> role.getRoleName().toLowerCase())
                .collect(Collectors.toSet());
    }

    // 查询本地部门名集合
    private Set<String> queryLocalDepartNames() {
        QueryWrapper<SysDepart> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(SysDepart::getDepartName)
                .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
        return departService.list(wrapper).stream()
                .map(depart->depart.getDepartName().toLowerCase())
                .collect(Collectors.toSet());
    }

    // 处理角色逻辑
    private void processRoles(JsonNode roleList, Set<String> allUserNames,
                              Set<String> allRoleNames, Set<String> allDepartNames) {
        List<SysUser> userListSave = new ArrayList<>();
        List<SysRole> roleListSave = new ArrayList<>();
        List<SysDepart> departListSave = new ArrayList<>();
        List<Triple<String, String, String>> userDepartRoles = new ArrayList<>();

        roleList.forEach(role -> {
            if (isVocRole(role)) {
                String roleId = getRoleId(role);
                String roleName = role.get("name").asText();
                log.info("匹配到角色: {},{}", roleId,roleName);

                // 同步角色
                syncRoleIfAbsent(roleName, allRoleNames, role,roleListSave);

                // 处理用户
                processUsers(roleId,roleName, allUserNames, allDepartNames,userListSave,departListSave,userDepartRoles);

            }
        });
        sysRoleService.saveOrUpdateBatch(roleListSave);
        log.info("开始保存角色：{}条", roleListSave.size());
        sysUserService.saveOrUpdateBatch(userListSave);
        log.info("开始保存用户：{}条", userListSave.size());
        departService.saveOrUpdateBatch(departListSave);
        log.info("开始保存部门：{}条", departListSave.size());
        log.info("开始保存用户角色关联：{}条", userDepartRoles.size());
        if (!userDepartRoles.isEmpty()) {
            log.info("开始保存用户角色关联");
            saveUserDepartRoles(userDepartRoles);
        }
        log.info("角色用户同步任务完成");
    }

    private void saveUserDepartRoles(List<Triple<String, String, String>> userDepartRoles) {
        Set<String> deps = userDepartRoles.stream().map(dep->dep.getMiddle()).collect(Collectors.toSet());
        Set<String> roes = userDepartRoles.stream().map(dep->dep.getRight()).collect(Collectors.toSet());
        QueryWrapper<SysDepart>  depsQuery = new QueryWrapper<>();
        depsQuery.lambda().in(SysDepart::getDepartName,deps).eq(SysDepart::getDelFlag,CommonConstant.DEL_FLAG_STR_0).select(SysDepart::getId,SysDepart::getDepartName);
        List<SysDepart> depsList = departService.list(depsQuery);
        QueryWrapper<SysRole> rolesQuery = new QueryWrapper<>();
        rolesQuery.lambda().in(SysRole::getRoleName,roes).eq(SysRole::getDelFlag,CommonConstant.DEL_FLAG_STR_0).select(SysRole::getId,SysRole::getRoleName);
        List<SysRole> rolesList = sysRoleService.list(rolesQuery);
        // 转换为 Map<String, String> (name -> id)
        Map<String, String> departMap = depsList.stream()
                .collect(Collectors.toMap(SysDepart::getDepartName, SysDepart::getId, (oldVal, newVal) -> oldVal));
        Map<String, String> roleMap = rolesList.stream()
                .collect(Collectors.toMap(SysRole::getRoleName, SysRole::getId, (oldVal, newVal) -> oldVal));
        List<SysUserRole> userRoleList = new ArrayList<>();
        List<SysUserDepart> userDepartList = new ArrayList<>();
        for (Triple<String, String, String> userDepartRole : userDepartRoles) {
            if (departMap.get(userDepartRole.getMiddle())!=null){
                SysUserDepart userDepart = new SysUserDepart(userDepartRole.getLeft(), departMap.get(userDepartRole.getMiddle()));
                userDepartList.add(userDepart);
            }if (roleMap.get(userDepartRole.getRight())!=null){
                SysUserRole userRole = new SysUserRole(userDepartRole.getLeft(), roleMap.get(userDepartRole.getRight()));
                userRoleList.add(userRole);
            }
        }
        sysUserDepartService.saveOrUpdateBatch(userDepartList);
        sysUserRoleService.saveOrUpdateBatch(userRoleList);
    }

    // 判断是否为 VOC 角色
    private boolean isVocRole(JsonNode role) {
        return role.has("name") &&
                role.get("name").asText().toLowerCase().contains(VOC_KEYWORD);
    }

    // 获取角色ID（安全方式）
    private String getRoleId(JsonNode role) {
        return Optional.ofNullable(role.get("id"))
                .map(JsonNode::asText)
                .orElse("未知ID");
    }

    // 同步缺失的角色
    private void syncRoleIfAbsent(String roleName, Set<String> allRoleNames, JsonNode role, List<SysRole> roleListSave) {
        if (!allRoleNames.contains(roleName.toLowerCase())) {
            SysRole newRole = new SysRole();
            newRole.setRoleName(roleName);
            newRole.setDescription(role.get("remark").asText());
            newRole.setCreateTime(new Date());
            newRole.setDelFlag(CommonConstant.DEL_FLAG_STR_0);
            newRole.setRoleStatus(false);
            roleListSave.add(newRole);
            allRoleNames.add(roleName.toLowerCase());
            log.info("新增角色: {}", roleName);
        }
    }

    // 处理用户数据
    private void processUsers( String roleId,String roleName,
                              Set<String> allUserNames, Set<String> allDepartNames,List<SysUser> userListSave,List<SysDepart> departListSave, List<Triple<String, String, String>> userDepartRoles) {
        JsonNode userList = ccApiTokenService.queryUsersByRoleId(roleId);
        if(userList.isEmpty()) {
            log.error("获取外部系统用户列表为空");
            return;
        }
        userList.forEach(user -> {
            String account = user.get("account").asText();
            log.info("处理用户: {}", account);
            Triple<String, String, String> triple =null;

            // 同步用户
            if (!allUserNames.contains(account.toLowerCase())) {
                SysUser newUser=userSyncSave(user.get("id").asText(), account, user.get("name").asText(), user.get("email").asText(), user.get("phone").asText());
                userListSave.add(newUser);
                allUserNames.add(newUser.getUsername().toLowerCase());
                triple=Triple.of(newUser.getId(), null, null);
            }

            // 同步部门
            JsonNode org = user.get("organization");
            String departName = org.get("orgName").asText();
            if (!allDepartNames.contains(departName.toLowerCase())) {
               SysDepart newDepart= syncDepartmentIfAbsent(org);
                departListSave.add(newDepart);
                allDepartNames.add(departName.toLowerCase());
            }
            if (triple!=null){
                triple=Triple.of(triple.getLeft(),departName,roleName);
                userDepartRoles.add(triple);
            }
        });
    }

    // 同步缺失的部门
    private SysDepart syncDepartmentIfAbsent(JsonNode org) {
        String departName = org.get("orgName").asText();
            SysDepart depart = new SysDepart();
            depart.setDepartName(departName);
            depart.setOrgCode(org.get("orgCode").asText());
            depart.setDescription(org.get("description").asText());
            depart.setDelFlag(CommonConstant.DEL_FLAG_STR_0);
            depart.setOrgType("1");
            depart.setDepartNameAbbr(departName);
            depart.setCreateTime(new Date());
            depart.setStatus(false);
            log.info("新增部门: {}", departName);
            return depart;
    }
    private SysUser userSyncSave(String userId, String account, String name, String email, String phone) {
        SysUser sysUser=new SysUser();
        sysUser.setId(userId);
        sysUser.setUsername(account);
        sysUser.setRealname(name);
        sysUser.setEmail(StrUtil.isNotBlank(email) && !("null".equals(email))? email :null);
        sysUser.setPhone(StrUtil.isNotBlank(phone) && !("null".equals(phone))? phone:null);
        sysUser.setWorkNo(account);
        sysUser.setCreateTime(new Date());//设置创建时间
        String salt = RandomUtil.randomString(8);
        sysUser.setSalt(salt);
        String passwordEncode= PasswordUtil.encrypt(sysUser.getUsername(), sysUser.getUsername(), salt);
        sysUser.setPassword(passwordEncode);
        sysUser.setDelFlag(CommonConstant.DEL_FLAG_0);
        sysUser.setStatus(CommonConstant.USER_FREEZE);
        sysUser.setType("1");
        return sysUser;
    }


    private void buildChildren(RoleAuthTree parent, List<RoleAuthTree> allNodes, boolean isNull) {
        // 查找当前节点的子节点
        List<RoleAuthTree> children = allNodes.stream()
                .filter(node -> parent.getId().equals(node.getPid()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parent.setChildren(children);
            // 递归处理每个子节点
            children.forEach(child -> buildChildren(child, allNodes, isNull));
        }
    }

    private List<RoleAuthTree> regionCategory(RoleAuthVo authVo, List<RoleAuthTree> region, boolean isNull) {
        Map<String, List<RoleAuthTree>> cateType = region.stream().collect(Collectors.groupingBy(RoleAuthTree::getType));
        List<RoleAuthTree> result = new ArrayList<RoleAuthTree>();
        if (cateType.size() == 1) {
            List<RoleAuthTree> businesstag = region.stream().filter(e -> isNull ? StrUtil.isBlank(e.getPid()) : "0".equals(e.getPid())).collect(Collectors.toList());
            // 递归构建树结构
            businesstag.forEach(node -> {
                buildChildren(node, region, isNull);
            });
            result.addAll(businesstag);
        } else {
            for (String s : cateType.keySet()) {
                RoleAuthTree type = new RoleAuthTree();
                if ("1".equals(s)) {
                    type.setId("1");
                    type.setCode("sale");
                    type.setName("销售区域");
                    type.setPid("0");
                    List<RoleAuthTree> businesstag = cateType.get(s).stream().filter(e -> isNull ? StrUtil.isBlank(e.getPid()) : "0".equals(e.getPid())).collect(Collectors.toList());
                    // 递归构建树结构
                    businesstag.forEach(node -> {
                        buildChildren(node, region, isNull);
                    });
                    type.setChildren(businesstag);
                } else if ("2".equals(s)) {
                    type.setId("2");
                    type.setCode("after-sales");
                    type.setName("售后区域");
                    type.setPid("0");
                    List<RoleAuthTree> butags = cateType.get(s).stream().filter(e -> isNull ? StrUtil.isBlank(e.getPid()) : "0".equals(e.getPid())).collect(Collectors.toList());
                    // 递归构建树结构
                    butags.forEach(node -> {
                        buildChildren(node, region, isNull);
                    });
                    type.setChildren(butags);
                }
                result.add(type);
            }
        }
        return result;
    }

    private List<RoleAuthTree> dealCategory(RoleAuthVo authVo, List<RoleAuthTree> butags, boolean isNull) {
        List<RoleAuthTree> businesstag = butags.stream().filter(e -> isNull ? StrUtil.isBlank(e.getPid()) : "0".equals(e.getPid())).collect(Collectors.toList());
        // 递归构建树结构
        businesstag.forEach(node -> {
            buildChildren(node, butags, isNull);
        });
//        businesstag.forEach(k -> {
//            k.setChildren(butags.stream().filter(o->!(isNull?StrUtil.isBlank(o.getPid()): "0".equals(o.getPid()))).filter(o -> o.getPid().equals(k.getId())).collect(Collectors.toList()));
//        });
        if (true) {
            authVo.setSystemAdmin(businesstag.stream().filter(o -> "d7d6e2e4e2934f2c9385a623fd98c600".equals(o.getId())).collect(Collectors.toList()));
            businesstag = businesstag.stream().filter(o -> !"d7d6e2e4e2934f2c9385a623fd98c600".equals(o.getId())).collect(Collectors.toList());
        }
        return businesstag;
    }

    private List<RoleAuthTree> dealBusinessTag(RoleAuthVo authVo, List<RoleAuthTree> butags, boolean isNull) {
        List<RoleAuthTree> businesstag = butags.stream().filter(e -> isNull ? StrUtil.isBlank(e.getPid()) : "0".equals(e.getPid())).collect(Collectors.toList());
        businesstag.sort(Comparator.comparing(RoleAuthTree::getOrderBy));
        businesstag.forEach(k -> {
            List<RoleAuthTree> childrens = butags.stream().filter(o -> !(isNull ? StrUtil.isBlank(o.getPid()) : "0".equals(o.getPid()))).filter(o -> o.getPid().equals(k.getId())).collect(Collectors.toList());

            childrens.sort(Comparator.comparing(RoleAuthTree::getOrderBy));
            k.setChildren(childrens);
        });
        return businesstag;
    }

    private void setRoleChannel(RoleAuthVo authVo, List<RoleAuthTree> channls) {
        RoleAuthTree shisj = channls.stream().filter(d -> "私域数据".equals(d.getName())).collect(Collectors.toList()).stream().findFirst().orElse(null);
        List<RoleAuthTree> syData = new ArrayList<>();
        LinkedHashMap<String, List<RoleAuthTree>> syDataMap = new LinkedHashMap<>();
        if (Objects.nonNull(shisj)) {
            syData.addAll(channls.stream().filter(k -> k.getPid().equals(shisj.getId())).collect(Collectors.toList()));
            syData.stream().forEach(e -> e.setChildren(channls.stream().filter(j -> e.getId().equals(j.getPid())).collect(Collectors.toList())));
        }
        RoleAuthTree gysj = channls.stream().filter(d -> "公域数据".equals(d.getName())).collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (Objects.nonNull(gysj)) {
            List<RoleAuthTree> otherData = channls.stream().filter(i -> i.getPid().equals(gysj.getId())).collect(Collectors.toList());
            otherData.stream().forEach(e -> e.setChildren(channls.stream().filter(j -> e.getId().equals(j.getPid())).collect(Collectors.toList())));
            syDataMap.put("公域数据", otherData);
        } else {
            syDataMap.put("公域数据", new ArrayList<>());
        }
        syDataMap.put("私域数据", syData);
        authVo.setDataChannel(syDataMap);
    }


    private void getTreeModelList(List<TreeModel> treeList, List<SysPermission> metaList, TreeModel temp) {
        for (SysPermission permission : metaList) {
            String tempPid = permission.getParentId();
            TreeModel tree = new TreeModel(permission.getId(), tempPid, permission.getName(), permission.getRuleFlag(), permission.isLeaf());
            if (temp == null && StrUtil.isEmpty(tempPid)) {
                treeList.add(tree);
                if (!tree.IsLeaf()) {
                    getTreeModelList(treeList, metaList, tree);
                }
            } else if (temp != null && tempPid != null && tempPid.equals(temp.getKey())) {
                temp.getChildren().add(tree);
                if (!tree.IsLeaf()) {
                    getTreeModelList(treeList, metaList, tree);
                }
            }

        }
    }
}
