package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.car.voc.common.constant.FillRuleConstant;
import com.car.voc.common.util.FillRuleUtil;
import com.car.voc.dto.BusinessTagImportDto;
import com.car.voc.dto.FaultProblemImportDto;
import com.car.voc.entity.FaultProblem;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.service.IVocBusinessTagService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Date:   2021-03-30
 * @Version: V1.0
 */
public class VocFaultProblemImportListener extends AnalysisEventListener<FaultProblemImportDto> {
	private static final Logger LOGGER = LoggerFactory.getLogger(VocFaultProblemImportListener.class);

	@Autowired
	private IVocBusinessTagService businessTagService;
	@Autowired
	private IFaultProblemService faultProblemService;

	/**
	 * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
	 */
	private static final int BATCH_COUNT = 1000;
	List<FaultProblemImportDto> list = new ArrayList<>();

	public VocFaultProblemImportListener(IFaultProblemService faultProblemService) {
		this.faultProblemService=faultProblemService;
	}

	@Override
	public void invoke(FaultProblemImportDto data, AnalysisContext context) {
		LOGGER.info("解析到一条数据:{}", JSON.toJSONString(data));
		list.add(data);
	}


	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		saveData();
		LOGGER.info("所有数据解析完成！");
	}

	private static void setTagScope(FaultProblemImportDto dto, VocBusinessTag t4) {
		if (StrUtil.isNotBlank(dto.getApplicationSide())
				&& dto.getApplicationSide().equals("工单")) {
			t4.setTagScope("2");
		} else {
			t4.setTagScope("1");
		}
	}

	private void saveData() {
		LOGGER.info("{}条数据，开始存储数据库！", list.size());
		//第一级
		Map<String, List<FaultProblemImportDto>> tree1 = list.stream()
				.collect(Collectors.groupingBy(
						FaultProblemImportDto::getTag1,
						TreeMap::new,  // 使用 TreeMap
						Collectors.toList()
				));

		for (Map.Entry<String, List<FaultProblemImportDto>> tag1 : tree1.entrySet()) {
			FaultProblem t1 = new FaultProblem();
			t1.setName(tag1.getKey());
			//一级存在时
			QueryWrapper<FaultProblem> queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(FaultProblem::getName, tag1.getKey());
			queryWrapper.lambda().eq(FaultProblem::getPid, "0");
			t1 = faultProblemService.getOne(queryWrapper);
			if (t1 == null) {//保存一级
				t1 = new FaultProblem();
				t1.setTag1Info(tag1.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag1.getKey(), t1, 1, "0", 1);
			}
			//新增一级时 （有一级的情况下）
			if (StrUtil.isBlankIfStr(t1.getCode())) {
				t1.setTag1Info(tag1.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag1.getKey(), t1, 1, "0", 1);
			}

			//第二级
			Map<String, List<FaultProblemImportDto>> tree2 = tag1.getValue().stream()
					.collect(Collectors.groupingBy(
							FaultProblemImportDto::getTag2,
							TreeMap::new,  // 使用 TreeMap
							Collectors.toList()
					));
			for (Map.Entry<String, List<FaultProblemImportDto>> tag2 : tree2.entrySet()) {
				FaultProblem t2 = new FaultProblem();
				t2.setTag2Info(tag2.getValue().get(0));
				t2.setTagScope("0");
				codeWarehousing(tag2.getKey(), t2, 2, t1.getId(), 1);

				//第三级
				Map<String, List<FaultProblemImportDto>> tree3 = tag2.getValue().stream()
						.collect(Collectors.groupingBy(
								FaultProblemImportDto::getTag3,
								TreeMap::new,  // 使用 TreeMap
								Collectors.toList()
						));
				for (Map.Entry<String, List<FaultProblemImportDto>> tag3 : tree3.entrySet()) {
					FaultProblem t3 = new FaultProblem();
					t3.setTag3Info(tag3.getValue().get(0));
					t3.setTagScope("0");
					codeWarehousing(tag3.getKey(), t3, 3, t2.getId(), 1);

					//第四级
					for (FaultProblemImportDto dto : tag3.getValue()) {
						FaultProblem t4 = new FaultProblem();
						t4.setTag4Info(dto);
						t4.setTagScope("0");
						codeWarehousing(dto.getTag4(), t4, 4, t3.getId(), 0);
					}
				}
			}
		}

		LOGGER.info("存储数据库成功！");
	}
	/**
	 * 加上存储数据库
	 */
	private void saveData1() {
		LOGGER.info("{}条数据，开始存储数据库！", list.size());
		//第一级
		Map<String, List<FaultProblemImportDto>> tree1 = list.stream().collect(Collectors.groupingBy(FaultProblemImportDto::getTag1));

		for (Map.Entry<String, List<FaultProblemImportDto>> tag1 : tree1.entrySet()) {
			FaultProblem t1=new FaultProblem();
			t1.setName(tag1.getKey());
			//一级存在时
			QueryWrapper<FaultProblem> queryWrapper=new QueryWrapper<>();
			queryWrapper.lambda().eq(FaultProblem::getName,tag1.getKey());
			queryWrapper.lambda().eq(FaultProblem::getPid,"0");
			t1=faultProblemService.getOne(queryWrapper);
			if (t1==null){//保存一级
				t1=new FaultProblem();
				t1.setTag1Info(tag1.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag1.getKey(),t1,1,"0",1);
			}
			//新增一级时 （有一级的情况下）
			if (StrUtil.isBlankIfStr(t1.getCode())){
				t1.setTag1Info(tag1.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag1.getKey(),t1,1,"0",1);
			}

			//第二级
			Map<String, List<FaultProblemImportDto>> tree2 = tag1.getValue().stream().collect(Collectors.groupingBy(FaultProblemImportDto::getTag2));
			for (Map.Entry<String, List<FaultProblemImportDto>> tag2 : tree2.entrySet()) {
				FaultProblem t2 = new FaultProblem();
				t2.setTag2Info(tag2.getValue().get(0));
				t2.setTagScope("0");

				codeWarehousing(tag2.getKey(),t2,2,t1.getId(),1);
				//第三级
				Map<String, List<FaultProblemImportDto>> tree3 = tag2.getValue().stream().collect(Collectors.groupingBy(FaultProblemImportDto::getTag3));
					for (Map.Entry<String, List<FaultProblemImportDto>> tag3 : tree3.entrySet()) {
						FaultProblem t3 = new FaultProblem();
						t3.setTag3Info(tag3.getValue().get(0));
						t3.setTagScope("0");
						codeWarehousing(tag3.getKey(),t3,3,t2.getId(),1);
						//第四级
						for (FaultProblemImportDto dto : tag3.getValue()) {
							FaultProblem t4 = new FaultProblem();
							t4.setTag4Info(dto);
							t4.setTagScope("0");
							codeWarehousing(dto.getTag4(),t4,4,t3.getId(),0);
						}

					}


			}


		}

		LOGGER.info("存储数据库成功！");
	}
	private void codeWarehousing(String name, FaultProblem vo, int level, String pid, int hasChild) {
		vo.setName(name);
//		vo.setLevel(level);
		vo.setPid(pid);
		vo.setEnable(true);
		vo.setDelFlag(0);
		vo.setCreateTime(new Date());
		vo.setHasChild(hasChild);
		JSONObject formData2 = new JSONObject();
		formData2.putOpt("pid",pid);
		String code2 = (String) FillRuleUtil.executeRule(FillRuleConstant.FAULT_PROBLEM,formData2);
		vo.setCode(code2);
		faultProblemService.save(vo);
	}
}
