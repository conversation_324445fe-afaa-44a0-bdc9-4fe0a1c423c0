package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.CaseLibrary;
import com.car.stats.mapper.CaseLibraryMapper;
import com.car.voc.common.Result;
import com.car.voc.entity.*;
import com.car.voc.mapper.*;
import com.car.voc.model.SysCaseClassifyModel;
import com.car.voc.service.*;
import com.car.voc.vo.SysCaseClassifyListVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * @Description 案例分类 服务实现类
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
@Service
@Slf4j
public class SysCaseClassifyServiceImpl extends ServiceImpl<SysCaseClassifyMapper, SysCaseClassify> implements ISysCaseClassifyService {

    @Autowired
    private SysCaseClassifyMapper sysCaseClassifyMapper;



    @Override
    public Result<IPage<SysCaseClassifyListVo>> queryPageList(SysCaseClassifyModel caseClassifyModel, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        SysCaseClassify caseClassify = new SysCaseClassify();
        BeanUtils.copyProperties(caseClassifyModel,caseClassify);

        Result<IPage<SysCaseClassifyListVo>> result = new Result<IPage<SysCaseClassifyListVo>>();

        QueryWrapper<SysCaseClassify> queryWrapper =new QueryWrapper<>();

        Page<SysCaseClassify> page = new Page<SysCaseClassify>(pageNo, pageSize);

//        queryWrapper.lambda().orderByDesc(SysCaseClassify::getCreateTime);

        queryWrapper.lambda().orderByAsc(SysCaseClassify::getSort);
        queryWrapper.lambda().orderByDesc(SysCaseClassify::getUpdateTime);
        IPage<SysCaseClassify> pageList = this.page(page, queryWrapper);

        IPage<SysCaseClassifyListVo> listVoIPage = new Page<>();
        List<SysCaseClassifyListVo> listVos;

        listVos= BeanUtil.copyToList(pageList.getRecords(),SysCaseClassifyListVo.class);

        for (SysCaseClassifyListVo vo:listVos) {
            SysCaseClassifyListVo cVo = sysCaseClassifyMapper.getCaseBrowseNum(vo.getId(),null);
            vo.setCaseNum(ObjectUtils.isEmpty(cVo.getCaseNum()) ? 0 : cVo.getCaseNum());
            vo.setBrowseNum(ObjectUtils.isEmpty(cVo.getBrowseNum()) ? 0 : cVo.getBrowseNum());
        }

        listVoIPage.setRecords(listVos);
        listVoIPage.setTotal(pageList.getTotal());
        listVoIPage.setCurrent(pageList.getCurrent());
        listVoIPage.setPages(pageList.getPages());
        listVoIPage.setSize(pageList.getSize());
        listVoIPage.setCurrent(pageList.getCurrent());
        result.setSuccess(true);
        result.setResult(listVoIPage);
        log.info("案例分类列表返回：{}",listVoIPage.toString());
        return result;
    }

    @Override
    public Result<SysCaseClassify> add(SysCaseClassifyModel caseClassifyModel, HttpServletRequest req) {
        Result<SysCaseClassify> result = new Result<SysCaseClassify>();
        SysCaseClassify sysCaseClassify = new SysCaseClassify();
        BeanUtils.copyProperties(caseClassifyModel,sysCaseClassify);
        try {

            //设置创建时间
            Date date = new Date();
            sysCaseClassify.setCreateTime(date);
            //从token中获取登录用户名
            String username = CommonService.getUserNameByToken(req);
            sysCaseClassify.setCreateBy(username);
            sysCaseClassify.setUpdateTime(date);
            sysCaseClassify.setUpdateBy(username);
            //添加时根据数据条数赋值排序
            QueryWrapper<SysCaseClassify> wrapper=new QueryWrapper<>();
            wrapper.lambda().orderByDesc(SysCaseClassify::getSort);
            List<SysCaseClassify> sysCaseClassifies = sysCaseClassifyMapper.selectList(wrapper);
            //分类名称不能重复
            List<SysCaseClassify> collect = sysCaseClassifies.stream().filter(s -> s.getName().equals(caseClassifyModel.getName())).collect(Collectors.toList());
            if(ObjectUtils.isNotEmpty(collect)){
                return result.error500("分类名称不能重复！");
            }
            Integer caseClassifyCount = sysCaseClassifyMapper.selectCount(wrapper);
            if(ObjectUtils.isNotEmpty(sysCaseClassifies)){
                sysCaseClassify.setSort(ObjectUtils.isNotEmpty(sysCaseClassifies.get(0).getSort()) ? (sysCaseClassifies.get(0).getSort() + 1) : (caseClassifyCount + 1));
            }else{
                sysCaseClassify.setSort(caseClassifyCount + 1);
            }

            this.save(sysCaseClassify);
            result.success("案例分类添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("案例分类操作失败");
        }
        return result;
    }

    @Override
    public Result<SysCaseClassify> edit(SysCaseClassifyModel caseClassifyModel, HttpServletRequest req) {
        Result<SysCaseClassify> result = new Result<SysCaseClassify>();
        SysCaseClassify sysCaseClassify = new SysCaseClassify();
        BeanUtils.copyProperties(caseClassifyModel,sysCaseClassify);
        try {
            sysCaseClassify = getById(sysCaseClassify.getId());
            if(ObjectUtils.isEmpty(sysCaseClassify)) {
                result.error500("案例分类编辑未找到对应实体");
            }else {
                //分类名称不能重复
                QueryWrapper<SysCaseClassify> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(SysCaseClassify::getName,caseClassifyModel.getName());
                SysCaseClassify oneName = getOne(wrapper);
                if(ObjectUtils.isNotEmpty(oneName) && !oneName.getId().equals(caseClassifyModel.getId())){
                    return result.error500("分类名称不能重复！");
                }

                SysCaseClassify caseClassify = new SysCaseClassify();
                BeanUtils.copyProperties(caseClassifyModel,caseClassify);
                caseClassify.setUpdateTime(new Date());
                String username = CommonService.getUserNameByToken(req);
                caseClassify.setUpdateBy(username);
                caseClassify.setSort(caseClassifyModel.getSort());
                this.updateById(caseClassify);
                result.success("案例分类编辑修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("案例分类编辑操作失败");
        }
        return result;
    }

    @Override
    public Boolean deleteCaseClassify(String id, HttpServletRequest req) {
        return this.removeById(id);
    }

}
