package com.car.voc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.SysCaseClassify;
import com.car.voc.model.SysCaseClassifyModel;
import com.car.voc.vo.SysCaseClassifyListVo;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * TODO
 *
 * @Description 案例分类 服务实现类
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
public interface ISysCaseClassifyService extends IService<SysCaseClassify> {

    Result<IPage<SysCaseClassifyListVo>> queryPageList(SysCaseClassifyModel caseClassify, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Result<SysCaseClassify> add(SysCaseClassifyModel caseClassifyModel, HttpServletRequest req);

    Result<SysCaseClassify> edit(SysCaseClassifyModel caseClassifyModel, HttpServletRequest req);

    Boolean deleteCaseClassify(String id, HttpServletRequest req);

}
