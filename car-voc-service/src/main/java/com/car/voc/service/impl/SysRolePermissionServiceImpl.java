package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.entity.SysRolePermission;
import com.car.voc.mapper.SysRolePermissionMapper;
import com.car.voc.service.ISysRolePermissionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 角色权限表 服务实现类
 * </p>
 *
 *
 * @since 2018-12-21
 */
@Service
public class SysRolePermissionServiceImpl extends ServiceImpl<SysRolePermissionMapper, SysRolePermission> implements ISysRolePermissionService {

	@Override
	public void saveRolePermission(String roleId, String permissionIds) {
		String ip = "";
		try {
			//获取request
			HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
			//获取IP地址
			ip = ServletUtil.getClientIP(request);

		} catch (Exception e) {
			ip = "127.0.0.1";
		}
		LambdaQueryWrapper<SysRolePermission> query = new QueryWrapper<SysRolePermission>().lambda().eq(SysRolePermission::getRoleId, roleId);
		this.remove(query);
		List<SysRolePermission> list = new ArrayList<SysRolePermission>();
        String[] arr = permissionIds.split(",");
		for (String p : arr) {
			if(StrUtil.isNotEmpty(p)) {
				SysRolePermission rolepms = new SysRolePermission(roleId, p);
				rolepms.setOperateDate(new Date());
				rolepms.setOperateIp(ip);
				list.add(rolepms);
			}
		}
		this.saveBatch(list);
	}

	@Override
	public void saveOrUpdateData(String roleId, List<String> permissionList){
		if(permissionList == null) {
            return;
        }
		QueryWrapper<SysRolePermission> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(SysRolePermission::getRoleId,roleId);
		List<SysRolePermission> list = this.list(queryWrapper);
		if(!CollectionUtils.isEmpty(list)){
			UpdateWrapper<SysRolePermission> updateWrapper = new UpdateWrapper<>();
			updateWrapper.lambda().eq(SysRolePermission::getRoleId,roleId).eq(SysRolePermission::getDelFlag, CommonConstant.DEL_FLAG_STR_0)
					.set(SysRolePermission::getDelFlag, CommonConstant.DEL_FLAG_STR_1);
			this.update(updateWrapper);
		}
		List<SysRolePermission> syp=new ArrayList<>();
		permissionList.stream().forEach(permissionId ->{
			SysRolePermission sysRoleSeries = new SysRolePermission();
			sysRoleSeries.setPermissionId(permissionId);
			sysRoleSeries.setRoleId(roleId);
			sysRoleSeries.setOperateDate(new Date());
			syp.add(sysRoleSeries);
		});
		this.saveBatch(syp);
	}

}
