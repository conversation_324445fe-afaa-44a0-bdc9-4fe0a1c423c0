package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.druid.sql.visitor.functions.Length;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.FillRuleConstant;
import com.car.voc.common.util.FillRuleUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.dto.FaultProblemImportDto;
import com.car.voc.dto.TagCacheDto;
import com.car.voc.entity.FaultProblem;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.FaultProblemMapper;
import com.car.voc.model.FaultProblemAddModel;
import com.car.voc.model.FaultProblemListQueryModel;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.vo.FaultProblemListVo;
import com.car.voc.vo.FaultProblemTreeVo;
import com.car.voc.vo.FaultProblemVo;
import com.car.voc.vo.InternationalVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @version 1.0.0
 * @ClassName FaultProblemServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月09日 13:51
 * @Copyright voc
 */
@Service
public class FaultProblemServiceImpl extends ServiceImpl<FaultProblemMapper, FaultProblem> implements IFaultProblemService {
   @Resource
   FaultProblemMapper faultProblemMapper;
   @Autowired
   IFaultProblemService faultProblemService;
    @Autowired
    private SysDepartServiceImpl sysDepartServiceImpl;

    @Override
    public IPage<FaultProblemListVo> queryByPage(Page<FaultProblemListVo> page, FaultProblemListQueryModel queryModel, HttpServletRequest req) {
        String parentId = queryModel.getPid();
        if (StrUtil.isBlank(parentId)) {
            parentId = "0";
            queryModel.setPid("0");
        }
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.select("id","depart_name");
        List<SysDepart> sysDeparts=sysDepartServiceImpl.list(queryWrapper);
        Map<String, String> departs = sysDeparts.stream()
                .collect(Collectors.toMap(SysDepart::getId, SysDepart::getDepartName));
        IPage<FaultProblemListVo> relist=faultProblemMapper.queryByPage(page,queryModel);
        relist.getRecords().stream().forEach(e->{
            e.setRelatedDepartmentsName(departs.get(e.getRelatedDepartments()));
        });
        return relist;
    }

    @Override
    public List<FaultProblemTreeVo> queryTreeList(QueryWrapper<FaultProblem> queryWrapper) {
        QueryWrapper<FaultProblem> wrapper=new QueryWrapper<>();
        wrapper.select("name","CODE","pid","id");
        List<FaultProblem> alls=baseMapper.selectList(wrapper);
        List<FaultProblemTreeVo> list= BeanUtil.copyToList(alls, FaultProblemTreeVo.class);
        List<FaultProblemTreeVo> tree1=list.stream().filter(e-> "0".equals(e.getPid())).collect(Collectors.toList());
        Collections.sort(tree1,(FaultProblemTreeVo o1, FaultProblemTreeVo o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.getName(),o2.getName()));
        tree1.forEach(e1->{
            List<FaultProblemTreeVo> tree2=list.stream().filter(i->e1.getId().equals(i.getPid())).collect(Collectors.toList());
            tree2.forEach(e2->{
                List<FaultProblemTreeVo> tree3=list.stream().filter(i->e2.getId().equals(i.getPid())).collect(Collectors.toList());
                tree3.forEach(e3->{
                    List<FaultProblemTreeVo> tree4=list.stream().filter(i->e3.getId().equals(i.getPid())).collect(Collectors.toList());
                    Collections.sort(tree4,(FaultProblemTreeVo o1, FaultProblemTreeVo o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.getName(),o2.getName()));
                    e3.setChildes(tree4);
                });
                Collections.sort(tree3,(FaultProblemTreeVo o1, FaultProblemTreeVo o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.getName(),o2.getName()));
                e2.setChildes(tree3);
            });
            Collections.sort(tree2,(FaultProblemTreeVo o1, FaultProblemTreeVo o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.getName(),o2.getName()));
            e1.setChildes(tree2);
        });
        return tree1;
    }

    @Override
    public void add(FaultProblemAddModel addModel) {
        FaultProblem faultProblem =new FaultProblem();
        QueryWrapper<FaultProblem> wrapper=new QueryWrapper<>();
        if (!StrUtil.isNotBlank(addModel.getProblemCode3())){
            throw  new BootException("请选择问题部件！");
        }
        wrapper.lambda().eq(FaultProblem::getCode,addModel.getProblemCode3());
        FaultProblem faldb =this.getOne(wrapper);
        faultProblem.setPid(faldb.getId());

        BeanUtil.copyProperties(addModel,faultProblem);
        faultProblem.setPid(faldb.getId());

        String n_tagCode = "";
        addModel.setPid(faldb.getId());
        if (StrUtil.isBlankIfStr(addModel.getPid())){
            faultProblem.setPid("0");
        }else {
            //如果当前节点父ID不为空 则设置父节点的hasChildren 为1
            FaultProblem parent = baseMapper.selectById(addModel.getPid());
            if(parent!=null && !"1".equals(parent.getHasChild())){
                parent.setHasChild(1);
                baseMapper.updateById(parent);
            }
        }
        if (StrUtil.isBlankIfStr(addModel.getCode())){
            JSONObject formData = new JSONObject();
            formData.putOpt("pid",faultProblem.getPid());
            n_tagCode = (String) FillRuleUtil.executeRule(FillRuleConstant.FAULT_PROBLEM,formData);
            //update-end- for：分类字典编码规则生成器做成公用配置
            faultProblem.setCode(n_tagCode);
        }

        this.saveOrUpdate(faultProblem);

        Set<String> nCarCodes= (Set<String>) redisUtil.get(CacheConstant.sys_disable_quality_code);
        if (nCarCodes==null){
            nCarCodes=new HashSet<>();
        }
        if (!faultProblem.isEnable()) {
            nCarCodes.add(faultProblem.getCode());
        }else if (faultProblem.isEnable()){
            nCarCodes.remove(faultProblem.getCode());
        }
        redisUtil.set(CacheConstant.sys_disable_quality_code,nCarCodes);

    }

    @Override
    public void edit(FaultProblemAddModel edit) {
        FaultProblem faultProblem =new FaultProblem();
        BeanUtil.copyProperties(edit,faultProblem);
        FaultProblem entity = this.getById(edit.getId());
        if(entity==null) {
            throw new BootException("未找到对应实体");
        }
        String old_pid = entity.getPid();
        String new_pid = edit.getPid();
        if(!old_pid.equals(new_pid)) {
            updateOldParentNode(old_pid);
            if(StrUtil.isEmpty(new_pid)){
                faultProblem.setPid("0");
            }
            if(!"0".equals(faultProblem.getPid())) {
                baseMapper.updateTreeNodeStatus(faultProblem.getPid(), 1);
            }
        }
        baseMapper.updateById(faultProblem);

        Set<String> nCarCodes= (Set<String>) redisUtil.get(CacheConstant.sys_disable_quality_code);
        if (nCarCodes==null){
            nCarCodes=new HashSet<>();
        }
        if (!faultProblem.isEnable()) {
            nCarCodes.add(faultProblem.getCode());
        }else if (faultProblem.isEnable()){
            nCarCodes.remove(faultProblem.getCode());
        }
        redisUtil.set(CacheConstant.sys_disable_quality_code,nCarCodes);


    }

    @Override
    public void delete(String id) {
        FaultProblem faultProblem = this.getById(id);
        if(faultProblem==null) {
            throw new BootException("未找到对应实体");
        }
        updateOldParentNode(faultProblem.getPid());
        baseMapper.deleteById(id);
    }

    @Override
    public FaultProblemVo getItemById(String id) {
        return baseMapper.getItemById(id);
    }

    /**
     * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
     * @param pid
     */
    private void updateOldParentNode(String pid) {
        if(!"0".equals(pid)) {
            Integer count = baseMapper.selectCount(new QueryWrapper<FaultProblem>().eq("pid", pid));
            if(count==null || count<=1) {
                baseMapper.updateTreeNodeStatus(pid, 0);
            }
        }
    }
    @Autowired
    RedisUtil redisUtil;
    @Override
    public String getNameByCode(String zbCode) {
        Object name= redisUtil.get(CacheConstant.SYS_FAULT_PROBLEM_CACHE+zbCode);
        if (!StrUtil.isBlankIfStr(name)){
            return name+"";
        }else {
            QueryWrapper<FaultProblem> wrapper=new QueryWrapper<>();
            wrapper.select("name");
            wrapper.lambda().eq(FaultProblem::getCode,zbCode);
            name= baseMapper.getNameByCode(wrapper);
            if (StrUtil.isBlankIfStr(name)) {
                name="";
            }
            redisUtil.set(CacheConstant.SYS_FAULT_PROBLEM_CACHE+zbCode,name, CacheConstant.expire*100);
            return name+"";
        }

    }

    @Override
    public List<InternationalVo> internationalCnTags() {
        return baseMapper.internationalCnTags();
    }
    @Override
    public List<InternationalVo> internationalEnTags() {
        return baseMapper.internationalEnTags();
    }

    @Override
    public Result<?> batchImport(MultipartFile file) {
        try{
            //获取文件流
            InputStream inputStream = file.getInputStream();
            //easyexcel导入文件
            EasyExcel.read(inputStream, FaultProblemImportDto.class,new VocFaultProblemImportListener(faultProblemService)).sheet().headRowNumber(1).doRead();
            return Result.OK();
        }catch (IOException e){
            e.printStackTrace();
            return Result.error("数据异常无法导入文件!");
        }
    }

    @Override
    public List<String> queryTopicCodesByTopicName(String topic) {
        return baseMapper.queryTopicCodesByTopicName(topic);
    }

    @Override
    public List<String> getTags2All() {
        List<String> qtag2s= (List<String>) redisUtil.get(CacheConstant.SYS_FAULT_PROBLEM2_CACHES);
        if (CollectionUtil.isEmpty(qtag2s)){
            qtag2s=baseMapper.getTags2All();
            redisUtil.set(CacheConstant.SYS_FAULT_PROBLEM2_CACHES,qtag2s,CacheConstant.expire);
        }
        return qtag2s;
    }

    @Override
    public Set<TagCacheDto> faultProblemAll() {
        String formatKey = String.format(CacheConstant.SYS_FAULT_PROBLEM_ALL);
        if (redisUtil.hasKey(formatKey)) {
            Set<Object> seriesAll = redisUtil.sGet(formatKey);
            return seriesAll.stream().map(e -> (TagCacheDto) e).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<FaultProblem> query = new QueryWrapper<FaultProblem>().lambda();
        query.select(FaultProblem::getId, FaultProblem::getCode,
                FaultProblem::getName, FaultProblem::getNameEn,
                FaultProblem::getPid);
        Set<TagCacheDto> res = this.list(query).stream()
                .map(e -> {
                            TagCacheDto model = new TagCacheDto();
                            BeanUtil.copyProperties(e, model);
                            return model;
                        }
                ).collect(Collectors.toSet());
        redisUtil.sSetAllObject(formatKey, res);
        return res;
    }
}

