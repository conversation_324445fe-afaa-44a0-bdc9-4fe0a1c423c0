package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.SysDictItem;
import com.car.voc.mapper.SysDictItemMapper;
import com.car.voc.service.ISysDictItemService;
import com.car.voc.service.ISysDictService;
import com.car.voc.vo.EnergyClassificationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


@Service
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements ISysDictItemService {
    @Resource
    RedisUtil redisUtil;
    @Resource
    private SysDictItemMapper sysDictItemMapper;

    @Autowired
    ISysDictService sysDictService;

    @Override
    public List<SysDictItem> selectItemsByMainId(String mainId) {
        return sysDictItemMapper.selectItemsByMainId(mainId);
    }

    @Override
    public List<SysDictItem> getSysDictItemByItemValueAndDictId(String key, String dictId) {
        if(StrUtil.isBlank(key) && StrUtil.isBlank(dictId)){
            return null;
        }
        QueryWrapper<SysDictItem> sysDictItemQueryWrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(key)){
            sysDictItemQueryWrapper.lambda().eq(SysDictItem::getItemValue,key);
        }
        if(StrUtil.isNotBlank(dictId)){
            sysDictItemQueryWrapper.lambda().eq(SysDictItem::getDictId,dictId);
        }

        List<SysDictItem> sysDictItems = sysDictItemMapper.selectList(sysDictItemQueryWrapper);
        if(null != sysDictItems && sysDictItems.size()>0){
            return sysDictItems;
        }

        return null;
    }

    @Override
    public String getSysDictItemTextByItemValueAndDictId(String itemValue, String dictId) {
        if(StrUtil.isBlank(itemValue) && StrUtil.isBlank(dictId)){
            return null;
        }
        QueryWrapper<SysDictItem> sysDictItemQueryWrapper = new QueryWrapper<>();
        if(StrUtil.isNotBlank(itemValue)){
            sysDictItemQueryWrapper.lambda().eq(SysDictItem::getItemValue,itemValue);
        }
        if(StrUtil.isNotBlank(dictId)){
            sysDictItemQueryWrapper.lambda().eq(SysDictItem::getDictId,dictId);
        }

        List<SysDictItem> sysDictItems = sysDictItemMapper.selectList(sysDictItemQueryWrapper);
        if(null != sysDictItems && sysDictItems.size()>0){
            return sysDictItems.get(0).getItemText();
        }

        return "";
    }

    @Override
    public Result<JSONObject> sysAllDictItems() {
        JSONObject obj = new JSONObject();
        obj.putOpt("sysAllDictItems", sysDictService.queryAllDictItems());
        return Result.OK(obj);
    }

    @Override
    public List<EnergyClassificationVo> energyClassification() {
        return sysDictItemMapper.energyClassification();
    }


}
