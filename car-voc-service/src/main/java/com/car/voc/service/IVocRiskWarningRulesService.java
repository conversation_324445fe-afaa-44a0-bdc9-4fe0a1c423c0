package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.entity.VocRiskWarningRulesDetailed;
import com.car.voc.vo.risk.RiskRuleVo;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName IVocRiskWarningRulesService.java
 * @Description TODO
 * @createTime 2023年02月06日 10:27
 * @Copyright voc
 */
public interface IVocRiskWarningRulesService extends IService<VocRiskWarningRules> {
    Result<?> getWarningRules(VocRiskWarningRules warningRules);

    Result<?> saveOrUpdate1(VocRiskWarningRules warningRules);

    RiskRuleVo getRiskEmotionRule(String id);

    String getWarnRuleDetailListByIdBrandCode(String riskType, String brandCode, BigDecimal riskIndex);

    List<VocRiskWarningRules> getRiskType(String riskType);

    VocRiskWarningRules getByBrandCode(String brandCode, String 风险事件洞察);
}
