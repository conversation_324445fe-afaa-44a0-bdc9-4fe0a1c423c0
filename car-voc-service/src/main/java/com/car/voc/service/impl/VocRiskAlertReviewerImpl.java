package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.cach.BaseDataCache;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.RiskStateEnum;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.mapper.SysRoleBusinessTagMapper;
import com.car.voc.mapper.SysRoleMapper;
import com.car.voc.mapper.VocRiskAlertReviewerMapper;
import com.car.voc.model.risk.RiskAlertReviewerBrandTagModel;
import com.car.voc.model.risk.RiskAlertReviewerModel;
import com.car.voc.service.*;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.risk.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.beans.Transient;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName VocRiskAlertReviewerImpl.java
 * @Description TODO
 * @createTime 2023年02月06日 10:33
 * @Copyright voc
 */
@Slf4j
@Service
public class VocRiskAlertReviewerImpl extends ServiceImpl<VocRiskAlertReviewerMapper, VocRiskAlertReviewer> implements IVocRiskAlertReviewerService {

    @Autowired
    IVocRiskProessRecipientService recipientService;
    @Resource
    VocRiskAlertReviewerMapper reviewerMapper;
    @Autowired
    IVocRiskAllTypesService allTypesService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    private IBrandProductManagerService brandProductManagerService;
    @Autowired
    private IVocBusinessTagService businessTagService;
    @Autowired
    private IVocRiskProessRecipientService riskProessRecipientService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISendMessageService iSendMessageService;
    @Autowired
    ISysDepartService departService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysRoleBusinessTagMapper sysRoleBusinessTagMapper;

    @Autowired
    private BaseDataCache baseDataCache;

    @Override
    public Result<IPage<RiskAlertReviewerVo>> queryPageList(VocRiskAlertReviewer reviewer, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        Page<RiskAlertReviewerVo> page = new Page<>(pageNo, pageSize);

        QueryWrapper<BrandProductManager> bpm = new QueryWrapper<>();
        bpm.lambda().eq(BrandProductManager::getPId, "0");
        Map<String, String> bpmCount = brandProductManagerService.list(bpm).stream()
                .collect(Collectors.toMap(BrandProductManager::getCode, BrandProductManager::getName));
        IPage<RiskAlertReviewerVo> relist = reviewerMapper.queryByPage(page, reviewer);

        relist.getRecords().forEach(e -> {
            QueryWrapper<VocRiskAllTypes> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(VocRiskAllTypes::getAuditUserId, e.getReviewerUserId()).ge(VocRiskAllTypes::getRiskState, 0);
            e.setAuditsNum(allTypesService.count(wrapper));
            //对应接收人员
            List<String> recipientUser = new ArrayList<>();
            //查询业务标签
            List<VocRiskProcessRecipientTag> recipientTags = recipientService.selectByProessRecipientTag(e.getId());
            for (VocRiskProcessRecipientTag recipientTag : recipientTags) {
                String recipient = recipientTag.getBrandCodeName();
                if (StringUtils.isNotBlank(recipientTag.getTagName())) {
                    recipient = recipient + "/" + "风险主题洞察-" + recipientTag.getTagName();
                }
                if (StringUtils.isNotBlank(recipientTag.getProcessUserName())) {
                    recipient = recipient + "/" + recipientTag.getProcessUserName();
                }
                if (StringUtils.isNotBlank(recipientTag.getProcessDepartName())) {
                    recipient = recipient + "-" + recipientTag.getProcessDepartName();
                }
                recipientUser.add(recipient);
            }


            //质量、用户
            List<VocRiskProcessRecipientBase> qualityTags = recipientService.selectByProcessRecipientBase(e.getId());
            for (VocRiskProcessRecipientBase qualityTag : qualityTags) {
                String riskTypeStr = "";
                if (qualityTag.getType() == 2) {
                    riskTypeStr = qualityTag.getBrandCodeName() + "/" + "质量问题风险" + "/";
                } else if (qualityTag.getType() == 3) {
                    riskTypeStr = qualityTag.getBrandCodeName() + "/" + "投诉用户排行" + "/";
                } else if (qualityTag.getType() == 4) {
                    riskTypeStr = qualityTag.getBrandCodeName() + "/" + "救援故障" + "/";
                } else if (qualityTag.getType() == 5) {
                    riskTypeStr = qualityTag.getBrandCodeName() + "/" + "网点风险" + "/";
                }
                if (StringUtils.isNotBlank(qualityTag.getRiskUserName())) {
                    riskTypeStr = riskTypeStr + qualityTag.getRiskUserName() + "-" + qualityTag.getRiskUserGroupName();
                    ;
                }
                recipientUser.add(riskTypeStr);
            }
            String[] brandCodes = e.getBrandCodes().split(",");
            if (ObjectUtils.isNotEmpty(brandCodes)) {
                if (brandCodes.length == bpmCount.size()) {
                    e.setBrandCodes("全部");
                } else {
                    List<String> brandName = new ArrayList<>();
                    for (String brandCode : brandCodes) {
                        brandName.add(bpmCount.get(brandCode));
                    }
                    e.setBrandCodes(String.join("、", brandName));
                }
            }

            if (ObjectUtils.isNotEmpty(recipientUser)) {
                e.setRecipientUser(String.join(",", recipientUser));
            }
        });


        return Result.OK(relist);
    }


    @Transient
    @Override
    public Result<?> saveOrUpdate2(RiskAlertReviewerModel reviewerModel) {
        // 检查重复账号
        if (ObjectUtils.isEmpty(reviewerModel.getId()) && isReviewerExists(reviewerModel.getReviewerUserId())) {
            return Result.error("此账号已绑定过审核人");
        }

        try {

            // 保存审核人信息
            VocRiskAlertReviewer reviewer = saveReviewer(reviewerModel);

            // 删除旧的关联数据
            cleanupOldData(reviewer.getId());

            // 处理品牌标签
            if (ObjectUtils.isNotEmpty(reviewerModel.getBrandTagList())) {
                processBrandTagsBatch(reviewer, reviewerModel.getBrandTagList());
            }

            // 清除缓存
            clearCache(reviewerModel.getReviewerUserId());

            return Result.OK(BeanUtil.copyProperties(reviewer, RiskAlertReviewerVo.class));
        } catch (Exception e) {
            log.error("保存审核人员信息异常", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    // 新增的辅助方法
    private boolean isReviewerExists(String reviewerUserId) {
        QueryWrapper<VocRiskAlertReviewer> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(VocRiskAlertReviewer::getReviewerUserId, reviewerUserId)
                .eq(VocRiskAlertReviewer::getDelFlag, CommonConstant.DEL_FLAG_0);
        return baseMapper.selectCount(queryWrapper) > 0;
    }

    private VocRiskAlertReviewer saveReviewer(RiskAlertReviewerModel reviewerModel) {
        VocRiskAlertReviewer reviewer = BeanUtil.copyProperties(reviewerModel, VocRiskAlertReviewer.class);
        if (ObjectUtils.isEmpty(reviewer.getId())) {
            reviewer.setCreateTime(new Date());
        }
        List<String> brandCodes = reviewerModel.getBrandTagList().stream()
                .map(RiskAlertReviewerBrandTagModel::getBrandCode)
                .collect(Collectors.toList());
        reviewer.setBrandCodes(String.join(",", brandCodes));
        super.saveOrUpdate(reviewer);
        return reviewer;
    }

    private void cleanupOldData(String reviewerId) {
        recipientService.delByProessRecipientTag(reviewerId);
        recipientService.delByProessRecipientQualityTag(reviewerId);
    }

    private void collectIds(List<RiskAlertReviewerBrandTagModel> brandTagList,
                            Set<String> departIds,
                            Set<String> userIds,
                            Set<String> brandCodes,
                            Set<String> tagIds,
                            Set<String> projectIds) {
        for (RiskAlertReviewerBrandTagModel brandTag : brandTagList) {
            this.recipientBase(brandTag.getRecipientQualityTag(), departIds, userIds, brandCodes, projectIds);
            this.recipientBase(brandTag.getRecipientTopUser(), departIds, userIds, brandCodes, projectIds);
            this.recipientBase(brandTag.getRecipientBranches(), departIds, userIds, brandCodes, projectIds);
            this.recipientBase(brandTag.getRecipientRescue(), departIds, userIds, brandCodes, projectIds);

            // 收集接收者标签相关ID
            if (ObjectUtils.isNotEmpty(brandTag.getRecipientTagList())) {
                for (VocRiskProcessRecipientTag recipientTag : brandTag.getRecipientTagList()) {
                    if (recipientTag == null) {
                        continue;
                    }
                    if (recipientTag.getBindType() != null && recipientTag.getBindType() == 2 && StrUtil.isNotBlank(recipientTag.getProcessDepartId())) {
                        projectIds.add(recipientTag.getProcessDepartId());
                    } else if (recipientTag.getBindType() != null && recipientTag.getBindType() == 1 && StrUtil.isNotBlank(recipientTag.getProcessDepartId())) {
                        departIds.add(recipientTag.getProcessDepartId());
                    }
                    userIds.add(recipientTag.getProcessUserId());
                    brandCodes.add(recipientTag.getBrandCode());
                    tagIds.add(recipientTag.getTagId());
                }
            }
        }
    }

    private void recipientBase(VocRiskProcessRecipientBase recipientBase, Set<String> departIds, Set<String> userIds, Set<String> brandCodes, Set<String> projectIds) {
        if (ObjectUtils.isNotEmpty(recipientBase)) {
            // 收集部门ID或者项目组id
            if (recipientBase.getBindType() != null && recipientBase.getBindType() == 2 && StrUtil.isNotBlank(recipientBase.getRiskUserGroupId())) {
                projectIds.add(recipientBase.getRiskUserGroupId());
            } else if (recipientBase.getBindType() != null && recipientBase.getBindType() == 1 && StrUtil.isNotBlank(recipientBase.getRiskUserGroupId())) {
                departIds.add(recipientBase.getRiskUserGroupId());
            }
            brandCodes.add(recipientBase.getBrandCode());
            userIds.add(recipientBase.getRiskUserId());
        }
    }

    private void processBrandTagsBatch(VocRiskAlertReviewer reviewer, List<RiskAlertReviewerBrandTagModel> brandTagList) {
        // 收集所有需要查询的ID
        Set<String> departIds = new HashSet<>();
        Set<String> userIds = new HashSet<>();
        Set<String> brandCodes = new HashSet<>();
        Set<String> tagIds = new HashSet<>();
        Set<String> projectIds = new HashSet<>();

        collectIds(brandTagList, departIds, userIds, brandCodes, tagIds, projectIds);

        // 批量查询数据
        Map<String, SysDepart> departMap = batchGetDepartments(departIds);
        Map<String, SysUser> userMap = batchGetUsers(userIds);
        Map<String, String> brandNameMap = batchGetBrandNames(brandCodes);
        Map<String, VocBusinessTag> businessTagMap = batchGetBusinessTags(tagIds);
        Map<String, VocProjectGroup> projectMap = batchGetProjects(projectIds);

        // 构建质量标签列表
        List<VocRiskProcessRecipientBase> qualityTags = new ArrayList<>();
        List<VocRiskProcessRecipientTag> recipientTags = new ArrayList<>();

        for (RiskAlertReviewerBrandTagModel brandTag : brandTagList) {
            // 构建质量标签
            if (ObjectUtils.isNotEmpty(brandTag.getRecipientQualityTag())) {
                VocRiskProcessRecipientBase qualityTag = createQualityTagBatch(
                        reviewer.getId(),
                        brandTag.getRecipientQualityTag(),
                        departMap,
                        userMap,
                        brandNameMap,
                        projectMap,
                        2
                );
                qualityTags.add(qualityTag);
            }
            if (ObjectUtils.isNotEmpty(brandTag.getRecipientTopUser())) {
                VocRiskProcessRecipientBase topUser = createQualityTagBatch(
                        reviewer.getId(),
                        brandTag.getRecipientTopUser(),
                        departMap,
                        userMap,
                        brandNameMap,
                        projectMap,
                        3
                );
                qualityTags.add(topUser);
            }
            if (ObjectUtils.isNotEmpty(brandTag.getRecipientRescue())) {
                VocRiskProcessRecipientBase topUser = createQualityTagBatch(
                        reviewer.getId(),
                        brandTag.getRecipientRescue(),
                        departMap,
                        userMap,
                        brandNameMap,
                        projectMap,
                        4
                );
                qualityTags.add(topUser);
            }
            if (ObjectUtils.isNotEmpty(brandTag.getRecipientBranches())) {
                VocRiskProcessRecipientBase topUser = createQualityTagBatch(
                        reviewer.getId(),
                        brandTag.getRecipientBranches(),
                        departMap,
                        userMap,
                        brandNameMap,
                        projectMap,
                        5
                );
                qualityTags.add(topUser);
            }
            // 构建接收者标签
            if (ObjectUtils.isNotEmpty(brandTag.getRecipientTagList())) {
                recipientTags.addAll(
                        createRecipientTagsBatch(
                                reviewer.getId(),
                                brandTag.getRecipientTagList(),
                                departMap,
                                userMap,
                                brandNameMap,
                                businessTagMap
                        )
                );
            }
        }

        // 批量保存
        if (CollUtil.isNotEmpty(qualityTags)) {
            recipientService.saveBatchQualityTags(qualityTags);
        }
        if (CollUtil.isNotEmpty(recipientTags)) {
            recipientService.saveBatchRecipientTags(recipientTags);
        }
    }


    private List<VocRiskProcessRecipientTag> createRecipientTagsBatch(
            String reviewerId,
            List<VocRiskProcessRecipientTag> sourceTags,
            Map<String, SysDepart> departMap,
            Map<String, SysUser> userMap,
            Map<String, String> brandNameMap,
            Map<String, VocBusinessTag> businessTagMap) {

        return sourceTags.stream()
                .map(sourceTag -> {
                    VocRiskProcessRecipientTag processTag = new VocRiskProcessRecipientTag();
                    BeanUtils.copyProperties(sourceTag, processTag);
                    processTag.setId(IdWorker.getIdStr());
                    processTag.setRecipientId(reviewerId);

                    // 设置部门和用户信息
                    Optional.ofNullable(departMap.get(sourceTag.getProcessDepartId()))
                            .ifPresent(dept -> processTag.setProcessDepartName(dept.getDepartName()));
                    Optional.ofNullable(userMap.get(sourceTag.getProcessUserId()))
                            .ifPresent(user -> processTag.setProcessUserName(user.getRealname()));

                    processTag.setBrandCodeName(brandNameMap.get(sourceTag.getBrandCode()));

                    // 设置业务标签信息
                    setBusinessTagInfoBatch(processTag, sourceTag.getTagId(), businessTagMap);

                    return processTag;
                })
                .collect(Collectors.toList());
    }

    private void setBusinessTagInfoBatch(
            VocRiskProcessRecipientTag processTag,
            String tagId, Map<String, VocBusinessTag> businessTagMap) {
        Optional.ofNullable(businessTagMap.get(tagId))
                .ifPresent(businessTag -> {
                    processTag.setTagCode(businessTag.getTagCode());
                    processTag.setTagName(businessTag.getName());
                    processTag.setTagParentId(businessTag.getPid());

                    if ("0".equals(businessTag.getPid())) {
                        processTag.setTagParentName("风险主题洞察");
                    } else {
                        Optional.ofNullable(businessTagMap.get(businessTag.getPid()))
                                .ifPresent(parentTag -> processTag.setTagParentName(parentTag.getName()));
                    }
                });
    }

    private VocRiskProcessRecipientBase createQualityTagBatch(
            String reviewerId,
            VocRiskProcessRecipientBase brandTag,
            Map<String, SysDepart> departMap,
            Map<String, SysUser> userMap,
            Map<String, String> brandNameMap,
            Map<String, VocProjectGroup> projectMap,
            int type) {

        VocRiskProcessRecipientBase recipientBase = new VocRiskProcessRecipientBase();
        BeanUtils.copyProperties(brandTag, recipientBase);
        recipientBase.setId(IdWorker.getIdStr());
        recipientBase.setRecipientId(reviewerId);
        recipientBase.setType(type);
        // 设置部门或项目组 和用户信息
        if (brandTag.getBindType() != null && brandTag.getBindType() == 2) {
            Optional.ofNullable(projectMap.get(brandTag.getRiskUserGroupId()))
                    .ifPresent(dept -> recipientBase.setRiskUserGroupName(dept.getProjectName()));
        } else {
            Optional.ofNullable(departMap.get(brandTag.getRiskUserGroupId()))
                    .ifPresent(dept -> recipientBase.setRiskUserGroupName(dept.getDepartName()));
        }
        Optional.ofNullable(userMap.get(brandTag.getRiskUserId()))
                .ifPresent(user -> recipientBase.setRiskUserName(user.getRealname()));
        recipientBase.setBrandCodeName(brandNameMap.get(brandTag.getBrandCode()));

        return recipientBase;
    }

    private void clearCache(String reviewerUserId) {
        redisUtil.del(CacheConstant.SYS_REVIEWER_PROCESS + reviewerUserId);
        redisUtil.del(CacheConstant.SYS_HANDLE_PROCESS + reviewerUserId);
    }

    @Override
    public void deleteReviewer(String id) {
        UpdateWrapper<VocRiskProcessRecipient> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(VocRiskProcessRecipient::getAuditId, id)
                .set(VocRiskProcessRecipient::getDelFlag, CommonConstant.DEL_FLAG_1);
        recipientService.update(updateWrapper);
        UpdateWrapper<VocRiskAlertReviewer> updateWrapper1 = new UpdateWrapper<>();
        updateWrapper1.lambda().eq(VocRiskAlertReviewer::getId, id)
                .set(VocRiskAlertReviewer::getDelFlag, CommonConstant.DEL_FLAG_1);
        this.update(updateWrapper1);
    }

    @Override
    public Result<?> queryById(String id) {

        //业务标签树结构
        QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByAsc(VocBusinessTag::getOrderBy);
        List<VocBusinessTag> tags = businessTagService.list(wrapper);
        //所有品牌
        QueryWrapper<BrandProductManager> bpmWrapper = new QueryWrapper<>();
        bpmWrapper.lambda().eq(BrandProductManager::getPId, "0");
        List<BrandProductManager> bpmList = brandProductManagerService.list(bpmWrapper);
        bpmList.sort(Comparator.comparing(BrandProductManager::getSortNo));

        List<WarningReviewBrandTagVo> brandTagList = new ArrayList<>();
        VocRiskAlertReviewer reviewer = this.getById(id);
        //查询该风险审核人员id下的品牌对应标签
        List<VocRiskProcessRecipientTag> selectedRecipientTags = riskProessRecipientService.selectByProessRecipientTag(id);
        //查询风险审核人员id下的品牌对应质量与用户
        List<VocRiskProcessRecipientBase> selectedQualityTags = riskProessRecipientService.selectByProcessRecipientBase(id);

        SysRole roleByUserId = sysRoleMapper.getRoleByUserId(reviewer.getReviewerUserId());
        List<String> brandCodeList = Arrays.asList(roleByUserId.getBrandCode().split(","));

        for (BrandProductManager bpm : bpmList) {
            WarningReviewBrandTagVo warningReviewBrandTagVo = new WarningReviewBrandTagVo();
            //业务标签
            List<VocRiskProcessRecipientTag> brandCodeTagCollect = selectedRecipientTags.stream().filter(s -> s.getBrandCode().equals(bpm.getCode())).collect(Collectors.toList());

            if (ObjectUtils.isNotEmpty(brandCodeTagCollect)) {
                warningReviewBrandTagVo.setSelected(true);
            } else {
                if (!brandCodeList.contains(bpm.getBrandCode())) {
                    log.info("没有该品牌的权限");
                    continue;
                }
            }
            List<SysRoleBusinessTag> sysRoleBusinessTags = sysRoleBusinessTagMapper.listByRoleId(roleByUserId.getId(), bpm.getBrandCode());
            List<String> tagIdList = sysRoleBusinessTags.stream().map(SysRoleBusinessTag::getTagCode).collect(Collectors.toList());
            //循环品牌下选中的标签
            List<VocBusinessTagVo> resultTag = new ArrayList<>();
            List<VocBusinessTagVo> ls1 = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
            //1. 构建一级节点
            for (VocBusinessTagVo tag : ls1) {
                if ("0".equals(tag.getPid())) {
                    VocBusinessTagVo vocBusinessTagVo = new VocBusinessTagVo();
                    //树节点选中
                    List<VocRiskProcessRecipientTag> selectParentCollect = brandCodeTagCollect.stream().filter(b -> b.getTagId().equals(tag.getId())).collect(Collectors.toList());
                    if (ObjectUtils.isNotEmpty(selectParentCollect)) {
                        VocRiskProcessRecipientTag vocRiskProcessRecipientTag = selectParentCollect.get(0);
                        tag.setSelected(true);
                        tag.setProcessDepartId(vocRiskProcessRecipientTag.getProcessDepartId());
                        tag.setProcessDepartName(vocRiskProcessRecipientTag.getProcessDepartName());
                        tag.setProcessUserId(vocRiskProcessRecipientTag.getProcessUserId());
                        tag.setProcessUserName(vocRiskProcessRecipientTag.getProcessUserName());
                        tag.setBindType(vocRiskProcessRecipientTag.getBindType());
                        vocBusinessTagVo = BeanUtil.copyProperties(tag, VocBusinessTagVo.class);

                    } else {
                        tag.setProcessDepartId(null);
                        tag.setProcessDepartName(null);
                        tag.setProcessUserId(null);
                        tag.setProcessUserName(null);
                        tag.setSelected(false);
                    }
                    if (ObjectUtils.isNotEmpty(vocBusinessTagVo.getProcessUserId())) {
                        resultTag.add(vocBusinessTagVo);
                    } else {
                        if (tagIdList.contains(tag.getTagCode())) {
                            resultTag.add(tag);
                        }
                    }
                }
            }
            // 2、递归获取子节点
            for (VocBusinessTagVo parent : resultTag) {
                recursiveTree(parent, ls1, brandCodeTagCollect, tagIdList);
            }
            warningReviewBrandTagVo.setBrandCode(bpm.getCode());
            //业务标签
            warningReviewBrandTagVo.setBusinessTagList(resultTag);
            warningReviewBrandTagVo.setUserPermission(Boolean.TRUE);
            warningReviewBrandTagVo.setQualityPermission(getQuality(bpm.getCode(), roleByUserId.getQualityText()));
            //质量标签
            List<VocRiskProcessRecipientBase> qualityTagCollect = selectedQualityTags.stream().filter(s -> s.getBrandCode().equals(bpm.getCode())).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(qualityTagCollect)) {
                for (VocRiskProcessRecipientBase qualityTag : qualityTagCollect) {
                    if (qualityTag.getType() == 2) {
                        //质量
                        warningReviewBrandTagVo.setQualityTag(qualityTag);
                        warningReviewBrandTagVo.setSelected(true);
                    } else if (qualityTag.getType() == 3) {
                        //用户
                        warningReviewBrandTagVo.setTopUser(qualityTag);
                        warningReviewBrandTagVo.setSelected(true);
                    } else if (qualityTag.getType() == 4) {
                        warningReviewBrandTagVo.setRecipientRescue(qualityTag);
                        warningReviewBrandTagVo.setSelected(true);
                    } else if (qualityTag.getType() == 5) {
                        warningReviewBrandTagVo.setRecipientBranches(qualityTag);
                        warningReviewBrandTagVo.setSelected(true);
                    }
                }
            }
            brandTagList.add(warningReviewBrandTagVo);
        }

        WarningReviewVo warningReviewVo = new WarningReviewVo();
        warningReviewVo.setBrandTagList(brandTagList);
        RiskAlertReviewerVo vo = BeanUtil.copyProperties(reviewer, RiskAlertReviewerVo.class);
        warningReviewVo.setId(vo.getId());
        warningReviewVo.setReviewerUserId(vo.getReviewerUserId());
        warningReviewVo.setReviewerUserName(vo.getReviewerUserName());
        warningReviewVo.setReviewerDepartId(vo.getReviewerDepartId());
        warningReviewVo.setReviewerDepartName(vo.getReviewerDepartName());
        warningReviewVo.setRemark(vo.getRemark());
        warningReviewVo.setSendDingtalk(vo.isSendDingtalk());
        return Result.OK(warningReviewVo);
    }


    private boolean getQuality(String brandCode, String qualityText) {
        if (StringUtils.isNotEmpty(qualityText) && StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 递归
     *
     * @return
     */
    private VocBusinessTagVo recursiveTree(VocBusinessTagVo parent, List<VocBusinessTagVo> list, List<VocRiskProcessRecipientTag> brandCodeTagCollect, List<String> codes) {
        List<VocBusinessTagVo> seouns = new ArrayList<>();
        Boolean selected = parent.getSelected();
        List<VocBusinessTagVo> collect1 = list.stream().filter(e -> parent.getId().equals(e.getPid())).collect(Collectors.toList());
        List<String> tagCodes = list.stream().map(VocBusinessTagVo::getTagCode).collect(Collectors.toList());
        for (VocBusinessTagVo zcprojectFieldDto : collect1) {
            if (codes.contains(zcprojectFieldDto.getTagCode())) {
                zcprojectFieldDto = recursiveTree(zcprojectFieldDto, list, brandCodeTagCollect, tagCodes);
                VocBusinessTagVo finalZcprojectFieldDto = zcprojectFieldDto;
                List<VocRiskProcessRecipientTag> collect = brandCodeTagCollect.stream().filter(b -> b.getTagId().equals(finalZcprojectFieldDto.getId())).collect(Collectors.toList());
                if (ObjectUtils.isNotEmpty(collect)) {
                    VocRiskProcessRecipientTag vocRiskProcessRecipientTag = collect.get(0);
                    zcprojectFieldDto.setSelected(true);
                    zcprojectFieldDto.setProcessDepartId(vocRiskProcessRecipientTag.getProcessDepartId());
                    zcprojectFieldDto.setProcessDepartName(vocRiskProcessRecipientTag.getProcessDepartName());
                    zcprojectFieldDto.setProcessUserId(vocRiskProcessRecipientTag.getProcessUserId());
                    zcprojectFieldDto.setProcessUserName(vocRiskProcessRecipientTag.getProcessUserName());
                    zcprojectFieldDto.setBindType(vocRiskProcessRecipientTag.getBindType());
                } else if (selected.equals(Boolean.FALSE)) {
                    zcprojectFieldDto.setSelected(false);
                    zcprojectFieldDto.setProcessDepartId(null);
                    zcprojectFieldDto.setProcessDepartName(null);
                    zcprojectFieldDto.setProcessUserId(null);
                    zcprojectFieldDto.setProcessUserName(null);
                }
                seouns.add(zcprojectFieldDto);

                List<VocBusinessTagVo> vocBusinessTagVos = BeanUtil.copyToList(seouns, VocBusinessTagVo.class);

                parent.setChildes(vocBusinessTagVos);
            }
        }
        return parent;
    }

    @Override
    public RiskAlertReviewerVo getByIdVo(RiskAlertReviewerModel model) {
        return this.baseMapper.getByIdVo(model);
    }

    @Override
    @Cacheable(value = CacheConstant.SYS_REVIEWER_PROCESS, key = "#userId")
    public ReviewerPermissionVo queryPermissionByUserId(String userId) {
        ReviewerPermissionVo reviewerPermissionVo = new ReviewerPermissionVo();
        List<String> brandCodes = new ArrayList<>();

        //业务
        List<VocRiskProcessRecipientTag> recipientTags = riskProessRecipientService.queryPermissionByUserId(userId);
        //业务下所有品牌
        List<String> brandCodeCollect = recipientTags.stream().map(VocRiskProcessRecipientTag::getBrandCode).collect(Collectors.toList());

        //质量、用户
        List<VocRiskProcessRecipientBase> recipientQualityTags = riskProessRecipientService.queryPermissionQualityByUserId(userId);
        List<String> qualityBrandCodecollect = recipientQualityTags.stream().map(VocRiskProcessRecipientBase::getBrandCode).collect(Collectors.toList());


        //品牌去重
        brandCodes.addAll(brandCodeCollect);
        brandCodes.addAll(qualityBrandCodecollect);
        brandCodes = brandCodes.stream().distinct().collect(Collectors.toList());

        List<ReviewerPermissionBrandCodeVo> reviewerPermissionList = new ArrayList<>();
        for (String brandCode : brandCodes) {
            List<String> riskTypeStatus = new ArrayList<>();
            ReviewerPermissionBrandCodeVo reviewerPermissionBrandCodeVo = new ReviewerPermissionBrandCodeVo();
            reviewerPermissionBrandCodeVo.setBrandCode(brandCode);
            //业务品牌标签
            List<String> tagCollect = recipientTags.stream().filter(r -> ObjectUtils.isNotEmpty(r.getBrandCode()) && r.getBrandCode().equals(brandCode))
                    .map(VocRiskProcessRecipientTag::getTagCode).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(tagCollect)) {
                reviewerPermissionBrandCodeVo.setTagCodes(tagCollect);
                reviewerPermissionBrandCodeVo.setEmotionStatus(1);
                riskTypeStatus.add("1");
            }

            //质量、用户权限
            List<VocRiskProcessRecipientBase> qualityTagCollect = recipientQualityTags.stream().filter(r -> ObjectUtils.isNotEmpty(r.getBrandCode()) && r.getBrandCode().equals(brandCode)).collect(Collectors.toList());
            qualityTagCollect.forEach(s -> {
                if (s.getType() != null && s.getType() == 2) {
                    reviewerPermissionBrandCodeVo.setQualityStatus(1);
                }
                if (s.getType() != null && s.getType() == 3) {
                    reviewerPermissionBrandCodeVo.setUserStatus(1);
                }
            });
            if (ObjectUtils.isNotEmpty(reviewerPermissionBrandCodeVo.getQualityStatus()) && reviewerPermissionBrandCodeVo.getQualityStatus() == 1) {
                riskTypeStatus.add("2");
            }
            if (ObjectUtils.isNotEmpty(reviewerPermissionBrandCodeVo.getUserStatus()) && reviewerPermissionBrandCodeVo.getUserStatus() == 1) {
                riskTypeStatus.add("3");
            }
            reviewerPermissionBrandCodeVo.setRiskTypeStatus(riskTypeStatus);
            reviewerPermissionList.add(reviewerPermissionBrandCodeVo);
        }
        reviewerPermissionVo.setUserId(userId);
        reviewerPermissionVo.setReviewerPermission(reviewerPermissionList);
        return reviewerPermissionVo;
    }

    @Override
    @Cacheable(value = CacheConstant.SYS_HANDLE_PROCESS, key = "#userId")
    public ReviewerPermissionVo queryHandlePermissionByUserId(String userId) {
        ReviewerPermissionVo reviewerPermissionVo = new ReviewerPermissionVo();
        List<String> brandCodes = new ArrayList<>();

        //业务
        List<VocRiskProcessRecipientTag> recipientTags = riskProessRecipientService.queryHandlePermissionByUserId(userId);
        //业务下所有品牌
        List<String> brandCodeCollect = recipientTags.stream().map(VocRiskProcessRecipientTag::getBrandCode).collect(Collectors.toList());

        //质量、用户
        List<VocRiskProcessRecipientBase> recipientQualityTags = riskProessRecipientService.queryHandlePermissionQualityByUserId(userId);
        List<String> qualityBrandCodecollect = recipientQualityTags.stream().map(VocRiskProcessRecipientBase::getBrandCode).collect(Collectors.toList());


        //品牌去重
        brandCodes.addAll(brandCodeCollect);
        brandCodes.addAll(qualityBrandCodecollect);
        brandCodes = brandCodes.stream().distinct().collect(Collectors.toList());

        List<ReviewerPermissionBrandCodeVo> reviewerPermissionList = new ArrayList<>();
        for (String brandCode : brandCodes) {
            ReviewerPermissionBrandCodeVo reviewerPermissionBrandCodeVo = new ReviewerPermissionBrandCodeVo();
            reviewerPermissionBrandCodeVo.setBrandCode(brandCode);
            //业务品牌标签
            List<String> tagCollect = recipientTags.stream().filter(r -> r.getBrandCode().equals(brandCode))
                    .map(VocRiskProcessRecipientTag::getTagCode).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(tagCollect)) {
                reviewerPermissionBrandCodeVo.setTagCodes(tagCollect);
                reviewerPermissionBrandCodeVo.setEmotionStatus(1);
            }

            //质量用户权限
            List<VocRiskProcessRecipientBase> qualityTagCollect = recipientQualityTags.stream().filter(r -> r.getBrandCode().equals(brandCode)).collect(Collectors.toList());
            qualityTagCollect.forEach(s -> {
                if (s.getType() == 2) {
                    reviewerPermissionBrandCodeVo.setQualityStatus(1);
                }
                if (s.getType() == 3) {
                    reviewerPermissionBrandCodeVo.setUserStatus(1);
                }
            });
            reviewerPermissionList.add(reviewerPermissionBrandCodeVo);
        }
        reviewerPermissionVo.setUserId(userId);
        reviewerPermissionVo.setReviewerPermission(reviewerPermissionList);
        return reviewerPermissionVo;
    }

    @Override
    public List<RiskAlertReviewerVo> selectByBrandCodeTagCodeUserId(String id, String brandCode, String riskNameSubstring) {
        return baseMapper.selectByBrandCodeTagCodeUserId(id, brandCode, riskNameSubstring);
    }

    @Override
    public void vocRiskReviewerSendDingtalkJob() {
        QueryWrapper<VocRiskAllTypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(VocRiskAllTypes::getRiskState, RiskStateEnum.RiskState_0)
                .eq(VocRiskAllTypes::getDelFlag, 0);
        List<VocRiskAllTypes> rewer = allTypesService.list(queryWrapper);
        if (rewer.isEmpty()) {
            return;
        }
        QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskAlertReviewer::isSendDingtalk, true);
        List<VocRiskAlertReviewer> revie = reviewerMapper.selectList(wrapper);
        for (VocRiskAlertReviewer aRer : revie) {
            Map<String, Map<String, long[]>> res = new HashMap<>();
            //业务
            List<VocRiskProcessRecipientTag> brtag = recipientService.queryBrandTagByReId(aRer.getId());
            //质量、用户
            List<VocRiskProcessRecipientBase> qualityTags = recipientService.selectByProcessRecipientBase(aRer.getId());
            Map<String, List<VocRiskProcessRecipientBase>> qub = qualityTags.stream().collect(Collectors.groupingBy(VocRiskProcessRecipientBase::getBrandCode));
            Map<String, List<VocRiskProcessRecipientTag>> arbrands = brtag.stream().collect(Collectors.groupingBy(VocRiskProcessRecipientTag::getBrandCode));

            for (String br : arbrands.keySet()) {
                List<String> tas = arbrands.get(br).stream().map(VocRiskProcessRecipientTag::getTagCode).collect(Collectors.toList());
                //累计
                List<VocRiskAllTypes> ar = rewer.stream().filter(e -> e.getBrandCode().equals(br) && tas.contains(e.getRiskName()) && "1".equals(e.getRiskType())).collect(Collectors.toList());
//                List<VocRiskAllTypes> ar = rewer.stream().filter(e -> e.getBrandCode().equals(br) && e.getRiskType().equals("1")).collect(Collectors.toList());
                //今天的
                Long buCount = ar.stream().filter(e -> e.getCreateTime().compareTo(DateUtil.beginOfDay(new Date())) > 0).count();
                Map<String, long[]> sre = new HashMap<>();
                sre.put("业务问题", new long[]{ar.size(), buCount});
                if (ar.size() > 0) {
                    res.put(br, sre);
                }
            }
            for (String qubg : qub.keySet()) {
                VocRiskProcessRecipientBase oneq = qub.get(qubg).stream().findFirst().orElse(null);
                if (oneq != null) {
                    //累计
                    List<VocRiskAllTypes> ar = rewer.stream().filter(e -> e.getBrandCode().equals(qubg) && "2".equals(e.getRiskType())).collect(Collectors.toList());
                    //今天的
                    Long buCount = ar.stream().filter(e -> e.getCreateTime().compareTo(DateUtil.beginOfDay(new Date())) > 0).count();
                    Map<String, long[]> sre = new HashMap<>();
                    sre.put("质量问题", new long[]{ar.size(), buCount});
                    Map<String, long[]> qbs = res.get(qubg);
                    if (ar.size() > 0) {
                        if (qbs == null) {
                            res.put(qubg, sre);
                        } else {
                            qbs.putAll(sre);
                        }
                    }
                }
            }

            log.error("-----------------------------------");
            log.error("用户：" + aRer.getReviewerUserId());
            for (String s : res.keySet()) {
                Map<String, long[]> sfsd = res.get(s);
                log.error("品牌" + s + ":");
                if (sfsd.get("业务问题") != null && sfsd.get("质量问题") != null) {
                    log.error("1、业务问题-累计" + sfsd.get("业务问题")[0] + "条未审核，今天" + sfsd.get("业务问题")[1] + "条未审核；");
                    log.error("2、质量问题-累计" + sfsd.get("质量问题")[0] + "条未审核，今天" + sfsd.get("质量问题")[1] + "条未审核；");
                } else if (sfsd.get("业务问题") != null) {
                    log.error("1、业务问题-累计" + sfsd.get("业务问题")[0] + "条未审核，今天" + sfsd.get("业务问题")[1] + "条未审核；");
                } else {
                    log.error("1、质量问题-累计" + sfsd.get("质量问题")[0] + "条未审核，今天" + sfsd.get("质量问题")[1] + "条未审核；");

                }

            }
            log.error("-----------------------------------end");
            SysUser pu = sysUserService.getById(aRer.getReviewerUserId());
            SysDepart pp = departService.getById(aRer.getReviewerDepartId());
            String useinfo = pp.getDepartName() + "-" + pu.getRealname();
            String t3 = "VOC管理平台风险预警需要您及时审核处理，涉及：";
            String title = "\n" +
                    "标题：VOC风险预警审核处理提醒  \n" +
                    "内容：**" + useinfo + "**：您好！  \n" + t3 +
                    "\n";
            String last = "\n登录VOC管理平台-任务处理派发-待审核，请及时进行处理。";
            StringBuilder table = new StringBuilder();
            if (res.isEmpty()) {
                continue;
            }
            for (String brandcode : res.keySet()) {
                Map<String, long[]> sfsd = res.get(brandcode);
                String brandName = brandProductManagerService.getCarNameByCarCode(brandcode);

                table.append("  \n" + "-----------------------------------------------------------\n" + "品牌：**").append(brandName).append("** \n").append("\n");
                String days = "", dayq = "";
                if (sfsd.get("业务问题") != null && sfsd.get("业务问题")[1] > 0) {
                    days = "，今日**" + sfsd.get("业务问题")[1] + "**条未审核";
                }
                if (sfsd.get("质量问题") != null && sfsd.get("质量问题")[1] > 0) {
                    dayq = "，今日**" + sfsd.get("质量问题")[1] + "**条未审核";
                }
                if (sfsd.get("业务问题") != null && sfsd.get("质量问题") != null) {
                    table.append("-----------------------------------------------------------\n" + "1、业务问题：累计**").append(sfsd.get("业务问题")[0]).append("**条未审核").append(days).append("； \n").append("\n").append("-----------------------------------------------------------\n").append("2、质量问题：累计**").append(sfsd.get("质量问题")[0]).append("**条未审核").append(dayq).append("； \n").append("\n");

                    log.error("1、业务问题-累计" + sfsd.get("业务问题")[0] + "条未审核" + days + "；");
                    log.error("2、质量问题-累计" + sfsd.get("质量问题")[0] + "条未审核" + dayq + "；");
                } else if (sfsd.get("业务问题") != null) {
                    table.append("-----------------------------------------------------------\n" + "1、业务问题：累计**").append(sfsd.get("业务问题")[0]).append("**条未审核").append(days).append("；").append(" \n").append("\n");
                    log.error("1、业务问题-累计" + sfsd.get("业务问题")[0] + "条未审核" + days + "；");
                } else {
                    table.append("-----------------------------------------------------------\n" + "1、质量问题：累计**").append(sfsd.get("质量问题")[0]).append("**条未审核").append(dayq).append("；").append(" \n").append("\n");
                    log.error("1、质量问题-累计" + sfsd.get("质量问题")[0] + "条未审核" + dayq + "；");
                }
            }
            iSendMessageService.sendAuditDingtalk(pu.getUsername(), title, table.toString(), last);
//             iSendMessageService.sendAuditDingtalk("1100011000",title,table,last);
        }
    }

    @Override
    public List<String> getBrandCodeList(String userId) {
        SysRole roleByUserId = sysRoleMapper.getRoleByUserId(userId);
        List<String> brandCodeList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(roleByUserId)) {
            String code = roleByUserId.getBrandCode();
            if (StringUtils.isNotBlank(code)) {
                brandCodeList = Arrays.asList(code.split(","));
            }
        }
        if (CollectionUtil.isEmpty(brandCodeList)) {
            return new ArrayList<>();
        }
        QueryWrapper<BrandProductManager> queryBrandProduct = new QueryWrapper<>();
        queryBrandProduct.lambda().eq(BrandProductManager::getPId, 0);
        queryBrandProduct.lambda().orderByAsc(BrandProductManager::getSortNo);
        List<BrandProductManager> records = brandProductManagerService.list(queryBrandProduct);
        log.info("品牌重新排序:{}", JSONUtil.toJsonStr(records));
        List<String> sortList = new ArrayList<>();
        for (BrandProductManager brandProductManager : records) {
            if (brandCodeList.contains(brandProductManager.getBrandCode())) {
                sortList.add(brandProductManager.getBrandCode());
            }
        }
        return sortList;
    }

    private Map<String, SysDepart> batchGetDepartments(Set<String> departIds) {
        if (departIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return sysDepartService.listByIds(departIds).stream()
                .collect(Collectors.toMap(SysDepart::getId, Function.identity(), (a, b) -> a));
    }

    private Map<String, SysUser> batchGetUsers(Set<String> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return sysUserService.listByIds(userIds).stream()
                .collect(Collectors.toMap(SysUser::getId, Function.identity(), (a, b) -> a));
    }

    private Map<String, String> batchGetBrandNames(Set<String> brandCodes) {
        if (brandCodes.isEmpty()) {
            return Collections.emptyMap();
        }
        QueryWrapper<BrandProductManager> queryBrandProduct = new QueryWrapper<>();
        queryBrandProduct.lambda().in(BrandProductManager::getCode, brandCodes);
        return brandProductManagerService.list(queryBrandProduct).stream()
                .collect(Collectors.toMap(BrandProductManager::getCode, BrandProductManager::getName, (a, b) -> a));
    }

    private Map<String, VocBusinessTag> batchGetBusinessTags(Set<String> tagIds) {
        if (tagIds.isEmpty()) {
            return Collections.emptyMap();
        }
        QueryWrapper<VocBusinessTag> queryBrandProduct = new QueryWrapper<>();
        queryBrandProduct.lambda().in(VocBusinessTag::getId, tagIds);
        return businessTagService.list(queryBrandProduct).stream()
                .collect(Collectors.toMap(VocBusinessTag::getId, Function.identity(), (a, b) -> a));
    }

    @Autowired
    VocProjectGroupService projectGroupService;

    private Map<String, VocProjectGroup> batchGetProjects(Set<String> projectIds) {
        if (projectIds.isEmpty()) {
            return Collections.emptyMap();
        }
        QueryWrapper<VocProjectGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(VocProjectGroup::getId, projectIds);
        List<VocProjectGroup> list = projectGroupService.list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream()
                .collect(Collectors.toMap(VocProjectGroup::getId, Function.identity(), (a, b) -> a));
    }

    @Override
    public List<RiskAllTypesVo> problemOverviewLevel(FilterCriteriaModel model) {
        return this.baseMapper.problemOverviewLevel(model);
    }

    @Override
    public List<RiskAllTypesVo> problemOverviewDepart(FilterCriteriaModel model) {
        return this.baseMapper.problemOverviewDepart(model).stream()
                .filter(s -> s.getDepartName() != null).collect(Collectors.toList());
    }

    @Override
    public RiskAllTypesVo processingProgress(FilterCriteriaModel model) {
        return this.baseMapper.processingProgress(model);
    }
}

