package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.SysCategory;
import com.car.voc.exception.BootException;
import com.car.voc.vo.TreeSelectVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 分类字典
 *
 * @Date:   2019-05-29
 * @Version: V1.0
 */
public interface ISysCategoryService extends IService<SysCategory> {

	/**根节点父ID的值*/
	public static final String ROOT_PID_VALUE = "0";

	void addSysCategory(SysCategory sysCategory);

	void updateSysCategory(SysCategory sysCategory);

	/**
	  * 根据父级编码加载分类字典的数据
	 * @param pcode
	 * @return
	 */
	public List<TreeSelectVo> queryListByCode(String pcode) throws BootException;

	/**
	  * 根据pid查询子节点集合
	 * @param pid
	 * @return
	 */
	public List<TreeSelectVo> queryListByPid(String pid);

	/**
	 * 根据pid查询子节点集合,支持查询条件
	 * @param pid
	 * @param condition
	 * @return
	 */
	public List<TreeSelectVo> queryListByPid(String pid, Map<String,String> condition);

	/**
	 * 根据code查询id
	 * @param code
	 * @return
	 */
	public String queryIdByCode(String code);

	public List<SysCategory> queryCategoryListByCode(String pcode);

	List<SysCategory> queryListByCodeList(ArrayList<String> strings);

	List<SysCategory> queryByBrandCode(String brand);
}
