package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.SysRoleBusinessTag;
import com.car.voc.entity.SysRoleSeries;
import com.car.voc.mapper.SysRoleSeriesMapper;
import com.car.voc.service.SysRoleSeriesService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
*
*/
@Service
public class SysRoleSeriesServiceImpl extends ServiceImpl<SysRoleSeriesMapper, SysRoleSeries>
implements SysRoleSeriesService {

    @Override
    public void saveOrUpdateData(String roleId, List<String> seriesIdList,String brandCode){
        if(seriesIdList == null) {
            return;
        }
        QueryWrapper<SysRoleSeries> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRoleSeries::getRoleId,roleId).eq(SysRoleSeries::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
        List<SysRoleSeries> list = this.list(queryWrapper);
        if(!CollectionUtils.isEmpty(list)){
            UpdateWrapper<SysRoleSeries> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SysRoleSeries::getRoleId,roleId);
            updateWrapper.lambda().eq(SysRoleSeries::getBrandCode,brandCode).eq(SysRoleSeries::getDelFlag,CommonConstant.DEL_FLAG_STR_0)
                    .set(SysRoleSeries::getDelFlag,CommonConstant.DEL_FLAG_STR_1);
            this.update(updateWrapper);
        }
        List<SysRoleSeries> cars=new ArrayList<>();
        seriesIdList.stream().forEach(seriesId ->{
            SysRoleSeries sysRoleSeries = new SysRoleSeries();
            sysRoleSeries.setCarCode(seriesId);
            sysRoleSeries.setRoleId(roleId);
            sysRoleSeries.setBrandCode(brandCode);
            cars.add(sysRoleSeries);});
        this.saveBatch(cars);
    }
}
