package com.car.voc.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.entity.VocProjectGroup;
import com.car.voc.entity.VocProjectGroupMember;
import com.car.voc.entity.VocProjectGroupSeries;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.SysUserDepartMapper;
import com.car.voc.mapper.VocProjectGroupMapper;
import com.car.voc.service.VocProjectGroupMemberService;
import com.car.voc.service.VocProjectGroupSeriesService;
import com.car.voc.service.VocProjectGroupService;
import com.car.voc.vo.DictVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目组信息表(VocProjectGroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:31:11
 */
@Service
@AllArgsConstructor
public class VocProjectGroupServiceImpl extends ServiceImpl<VocProjectGroupMapper, VocProjectGroup> implements VocProjectGroupService {

    private VocProjectGroupMemberService vocProjectGroupMemberService;

    private VocProjectGroupSeriesService vocProjectGroupSeriesService;

    private SysUserDepartMapper sysUserDepartMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProjectGroup(VocProjectGroup projectGroup) {
        String leaderId = projectGroup.getLeaderId();
        int check = this.count(new QueryWrapper<VocProjectGroup>().lambda().eq(VocProjectGroup::getLeaderId, leaderId));
        if (check > 0) {
            throw new BootException("项目组负责人已存在");
        }
        Date now = new Date();
        projectGroup.setUpdateTime(now);
        projectGroup.setCreateTime(now);
        projectGroup.setCreateBy(projectGroup.getCreateBy());
        List<VocProjectGroupSeries> seriesList = projectGroup.getSeriesList();
        Set<String> brandCodes = seriesList.stream().map(VocProjectGroupSeries::getBrandCode).collect(Collectors.toSet());
        projectGroup.setBrandCode(String.join(",", brandCodes));
        this.save(projectGroup);
        String groupId = projectGroup.getId();
        // 保存新的关联数据
        saveNewRelatedData(groupId, projectGroup.getMemberIds(), projectGroup.getSeriesList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectGroup(VocProjectGroup projectGroup) {
        String groupId = projectGroup.getId();
        String leaderId = projectGroup.getLeaderId();
        int check = this.count(new QueryWrapper<VocProjectGroup>()
                .lambda()
                .eq(VocProjectGroup::getLeaderId, leaderId)
                .ne(VocProjectGroup::getId, groupId));
        if (check > 0) {
            throw new BootException("项目组负责人已存在");
        }
        Date now = new Date();
        // 更新操作
        projectGroup.setUpdateTime(now);
        List<VocProjectGroupSeries> seriesList = projectGroup.getSeriesList();
        Set<String> brandCodes = seriesList.stream().map(VocProjectGroupSeries::getBrandCode).collect(Collectors.toSet());
        projectGroup.setBrandCode(String.join(",", brandCodes));
        if (!this.updateById(projectGroup)) {
            throw new BootException("更新项目组信息失败!");
        }
        // 更新关联数据的逻辑删除标记
        updateRelatedData(groupId, projectGroup.getMemberIds(), seriesList);
    }

    @Override
    public List<VocProjectGroup> projectGroupAndMember(String brandCode) {
        List<VocProjectGroup> records = this.baseMapper.pageList(brandCode, null);
        if (CollUtil.isEmpty(records)) {
            return records;
        }
        List<String> ids = records.stream().map(VocProjectGroup::getId).collect(Collectors.toList());
        List<VocProjectGroupMember> memberByIds = this.baseMapper.getMemberByIds(ids);
        Map<String, List<VocProjectGroupMember>> memberByMap = memberByIds.stream()
                .collect(Collectors.groupingBy(VocProjectGroupMember::getGroupId, Collectors.toList()));
        List<String> userIds = memberByIds.stream().map(VocProjectGroupMember::getUserId).collect(Collectors.toList());
        Map<Object, Object> userDepart = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            // 3. 批量查询部门信息
            List<Map> userDepartMap = sysUserDepartMapper.getDepartInfoByUserIds(userIds);
            userDepart.putAll(userDepartMap.stream()
                    .collect(Collectors.toMap(map -> map.get("userId"), map -> map.get("departId"))));
        }
        records.forEach(group -> {
            group.setMemberIds(memberByMap.get(group.getId()));
            if (userDepart.containsKey(group.getLeaderId())) {
                group.setDeptId((String) userDepart.get(group.getLeaderId()));
            }
            group.getMemberIds().forEach(member -> {
                if (userDepart.containsKey(member.getUserId())) {
                    member.setDepartId((String) userDepart.get(member.getUserId()));
                }
            });
        });
        return records;
    }

    @Override
    public List<VocProjectGroupMember> projectMembers(String projectId) {
        return this.baseMapper.getMemberByIds(Collections.singletonList(projectId));
    }

    @Override
    public Page<VocProjectGroup> pageList(Page<VocProjectGroup> page, String brandCode, String keyword) {
        // 1. 查询项目组列表
        Page<VocProjectGroup> vocProjectGroupPage = this.baseMapper.pageList(page, brandCode, keyword);
        List<VocProjectGroup> records = vocProjectGroupPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return vocProjectGroupPage;
        }
        List<String> ids = records.stream().map(VocProjectGroup::getId).collect(Collectors.toList());
        List<VocProjectGroupSeries> seriesByIds = this.baseMapper.getSeriesByIds(ids);
        Map<String, List<VocProjectGroupSeries>> seriesByMap = seriesByIds.stream()
                .collect(Collectors.groupingBy(VocProjectGroupSeries::getGroupId, Collectors.toList()));
        List<VocProjectGroupMember> memberByIds = this.baseMapper.getMemberByIds(ids);
        Map<String, List<VocProjectGroupMember>> memberByMap = memberByIds.stream()
                .collect(Collectors.groupingBy(VocProjectGroupMember::getGroupId, Collectors.toList()));

        // 2. 收集所有成员ID
        List<String> userIds = memberByIds.stream().map(VocProjectGroupMember::getUserId).collect(Collectors.toList());
        userIds.addAll(records.stream().map(VocProjectGroup::getLeaderId).collect(Collectors.toList()));
        Map<Object, Object> userDepart = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            // 3. 批量查询部门信息
            List<Map> userDepartMap = sysUserDepartMapper.getDepartInfoByUserIds(userIds);
            userDepart.putAll(userDepartMap.stream()
                    .collect(Collectors.toMap(map -> map.get("userId"), map -> map.get("departId"))));
        }
        records.forEach(group -> {
            List<VocProjectGroupSeries> vocProjectGroupSeries = seriesByMap.get(group.getId());
            group.setSeriesList(CollUtil.isNotEmpty(vocProjectGroupSeries) ? vocProjectGroupSeries : new ArrayList<>());
            List<VocProjectGroupMember> vocProjectGroupMembers = memberByMap.get(group.getId());
            group.setMemberIds(CollUtil.isNotEmpty(vocProjectGroupMembers) ? vocProjectGroupMembers : new ArrayList<>());
            if (userDepart.containsKey(group.getLeaderId())) {
                group.setDeptId((String) userDepart.get(group.getLeaderId()));
            }
            if (CollUtil.isNotEmpty(group.getMemberIds())) {
                group.getMemberIds().forEach(member -> {
                    if (userDepart.containsKey(member.getUserId())) {
                        member.setDepartId((String) userDepart.get(member.getUserId()));
                    }
                });
            }
        });
        return vocProjectGroupPage;
    }

    private void updateRelatedData(String groupId,
                                   List<VocProjectGroupMember> newMembers,
                                   List<VocProjectGroupSeries> newSeries) {
        // 1. 获取现有数据
        List<VocProjectGroupMember> existingMembers = vocProjectGroupMemberService
                .list(new LambdaQueryWrapper<VocProjectGroupMember>()
                        .eq(VocProjectGroupMember::getGroupId, groupId)
                        .eq(VocProjectGroupMember::getDelFlag, 0));

        List<VocProjectGroupSeries> existingSeries = vocProjectGroupSeriesService
                .list(new LambdaQueryWrapper<VocProjectGroupSeries>()
                        .eq(VocProjectGroupSeries::getGroupId, groupId)
                        .eq(VocProjectGroupSeries::getDelFlag, 0));

        // 2. 找出需要删除的记录
        Set<String> newMemberIds = newMembers.stream()
                .map(VocProjectGroupMember::getUserId)
                .collect(Collectors.toSet());

        Set<String> newSeriesIds = newSeries.stream()
                .map(VocProjectGroupSeries::getSeriesCode)
                .collect(Collectors.toSet());

        // 3. 逻辑删除不再需要的记录
        List<String> userIdsDel = existingMembers.stream()
                .filter(m -> !newMemberIds.contains(m.getUserId()))
                .map(VocProjectGroupMember::getId)
                .collect(Collectors.toList());
        vocProjectGroupMemberService.removeByIds(userIdsDel);

        List<String> seriesIdsDel = existingSeries.stream()
                .filter(s -> !newSeriesIds.contains(s.getSeriesCode()))
                .map(VocProjectGroupSeries::getId)
                .collect(Collectors.toList());
        vocProjectGroupSeriesService.removeByIds(seriesIdsDel);

        // 4. 添加新记录
        Set<String> existingMemberIds = existingMembers.stream()
                .map(VocProjectGroupMember::getUserId)
                .collect(Collectors.toSet());

        Set<String> existingSeriesIds = existingSeries.stream()
                .map(VocProjectGroupSeries::getSeriesCode)
                .collect(Collectors.toSet());

        List<VocProjectGroupMember> membersToAdd = newMembers.stream()
                .filter(m -> !existingMemberIds.contains(m.getUserId()))
                .peek(m -> m.setGroupId(groupId))
                .peek(s -> s.setId(null))
                .collect(Collectors.toList());

        List<VocProjectGroupSeries> seriesToAdd = newSeries.stream()
                .filter(s -> !existingSeriesIds.contains(s.getSeriesCode()))
                .peek(s -> s.setGroupId(groupId))
                .peek(s -> s.setId(null))
                .collect(Collectors.toList());

        // 5. 批量保存新记录
        if (!membersToAdd.isEmpty()) {
            vocProjectGroupMemberService.saveBatch(membersToAdd);
        }
        if (!seriesToAdd.isEmpty()) {
            vocProjectGroupSeriesService.saveBatch(seriesToAdd);
        }
    }

    private void saveNewRelatedData(String groupId,
                                    List<VocProjectGroupMember> members,
                                    List<VocProjectGroupSeries> series) {
        // 设置关联ID
        members.forEach(m -> m.setGroupId(groupId));
        series.forEach(s -> s.setGroupId(groupId));

        // 批量保存
        if (!members.isEmpty()) {
            vocProjectGroupMemberService.saveBatch(members);
        }
        if (!series.isEmpty()) {
            vocProjectGroupSeriesService.saveBatch(series);
        }
    }

    /**
     * 构建项目组树
     */
    @Override
    public List<DictVo> buildProjectTree(String brandCode) {
        List<Map<String, Object>> rawData = this.baseMapper.getProjectWithUsers(brandCode);
        Map<String, DictVo> projectMap = new HashMap<>();

        // 构建项目组节点
        for (Map<String, Object> row : rawData) {
            String projectId = row.get("project_id").toString();
            if (!projectMap.containsKey(projectId)) {
                DictVo project = new DictVo();
                project.setValue(projectId);
                project.setText(row.get("project_name").toString());
                project.setChildes(new ArrayList<>());
                projectMap.put(projectId, project);
            }

            // 添加用户节点
            if (row.get("user_id") != null) {
                DictVo user = new DictVo();
                user.setValue(row.get("user_id").toString());
                user.setText(row.get("user_name").toString());
                List<DictVo> childes = (List<DictVo>) projectMap.get(projectId).getChildes();
                childes.add(user);
            }
        }
        return new ArrayList<>(projectMap.values());
    }
}
