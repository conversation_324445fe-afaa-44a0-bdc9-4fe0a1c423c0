package com.car.voc.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.SysRole;
import com.car.voc.model.SysRoleModel;
import com.car.voc.vo.ChannelCategoryVo;
import com.car.voc.vo.auth.RoleAuthVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * @since 2018-12-19
 */
public interface ISysRoleService extends IService<SysRole> {


    /**
     * 删除角色
     *
     * @param roleid
     * @return
     */
    boolean deleteRole(String roleid) throws Exception;

    Result<Map<String, Object>> queryTreeList(SysRoleModel roleModel, HttpServletRequest request);

    Result<SysRole> saveOrUpdate(SysRoleModel roleModel);

    Result<SysRole> newSaveOrUpdate(List<SysRoleModel> roleModelList);

    Result<IPage<RoleAuthVo>> queryPageLit(SysRoleModel role, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Result<List<RoleAuthVo>> queryList();

    RoleAuthVo queryById(String id, String brandCode);

    List<RoleAuthVo> getDataByBrandCode(String brandCode);


    List<RoleAuthVo> getListByRoleId(String roleId);

    List<ChannelCategoryVo> allChannel();

    SysRole getRoleByUserId(String id);

    void roleUserSyncJob();
}
