package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.entity.ModelGroup;
import com.car.voc.entity.ModelGroupRelation;
import com.car.voc.mapper.ModelGroupMapper;
import com.car.voc.model.ModelGroupModel;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IModelGroupRelationService;
import com.car.voc.service.IModelGroupService;
import com.car.voc.vo.ModelGroupVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName ModelGroupServiceImpl.java
 * @Description TODO
 * @createTime 2022年09月26日 11:14
 * @Copyright voc
 */
@Service
public class ModelGroupServiceImpl extends ServiceImpl<ModelGroupMapper, ModelGroup> implements IModelGroupService {
    @Autowired
    IModelGroupRelationService modelGroupRelationService;
    @Autowired
    IBrandProductManagerService brandProductManagerService;
    @Override
    public Result<IPage<ModelGroup>> queryPageList(ModelGroupModel modelGroupModel, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        Result<IPage<ModelGroup>> result = new Result<>();

        Page<ModelGroup> page = new Page<>(pageNo, pageSize);
        QueryWrapper<ModelGroup> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(ModelGroup::getDelFlag,0);
        queryWrapper.lambda().like(StrUtil.isNotBlank(modelGroupModel.getSearchKeyword()),ModelGroup::getName,modelGroupModel.getSearchKeyword());
        page=this.page(page,queryWrapper);
        page.getRecords().forEach(e->{
            Set<String> names=modelGroupRelationService.queryCarNameByModelGroupId(e.getId());
            e.setVehicleSeries(names);
            e.setCarSeries(names.size());
        });
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    @Override
    public Result<ModelGroupVo> add(ModelGroupModel modelGroupModel) {
        ModelGroup modelGroup=new ModelGroup();
        ModelGroupVo modelGroupvo=new ModelGroupVo();
        BeanUtils.copyProperties(modelGroupModel,modelGroup);
        if (StrUtil.isBlankIfStr(modelGroup.getId())){
            modelGroup.setCreateTime(new Date());
            modelGroup.setDelFlag(0);
        }else {
            modelGroup.setUpdateTime(new Date());
        }
        Result<ModelGroupVo> result = new Result<ModelGroupVo>();
        this.saveOrUpdate(modelGroup);
        List<ModelGroupRelation> relations=BeanUtil.copyToList(modelGroupModel.getModels(),ModelGroupRelation.class);
        relations.forEach(e-> e.setModelGroupId(modelGroup.getId()));
        relations.forEach(e->{
            if (StrUtil.isBlankIfStr(e.getId())){
                e.setCreateTime(new Date());
            }
        });
        modelGroupRelationService.saveOrUpdateBatch(relations);
        BeanUtils.copyProperties(modelGroup,modelGroupvo);
        result.setResult(modelGroupvo);
        return result;
    }

    @Override
    public Result<ModelGroupVo> edit(ModelGroupModel modelGroupModel) {
        return this.add(modelGroupModel);
    }

    @Override
    public void deleteModelGroup(String id) {
        this.removeById(id);
    }

    @Override
    public Result<List<ModelGroupVo>> listall(ModelGroupModel modelGroupModel, HttpServletRequest req) {
        QueryWrapper<ModelGroup> wrapper=new QueryWrapper<>();
//        wrapper.lambda().eq(ModelGroup::getDelFlag,0);
        List<ModelGroup> list=this.list(wrapper);
        List<ModelGroupVo> listre=BeanUtil.copyToList(list,ModelGroupVo.class);
        return Result.OK(listre);
    }
}
