package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.FillRuleConstant;
import com.car.voc.common.util.FillRuleUtil;
import com.car.voc.entity.SysCategory;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.SysCategoryMapper;
import com.car.voc.service.ISysCategoryService;
import com.car.voc.vo.TreeSelectVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 分类字典
 *
 * @Date:   2019-05-29
 * @Version: V1.0
 */
@Service
public class SysCategoryServiceImpl extends ServiceImpl<SysCategoryMapper, SysCategory> implements ISysCategoryService {

	@Override
	public void addSysCategory(SysCategory sysCategory) {
		String categoryCode = "";
		String categoryPid = ISysCategoryService.ROOT_PID_VALUE;
		String parentCode = null;
		if(StrUtil.isNotEmpty(sysCategory.getPid())){
			categoryPid = sysCategory.getPid();

			//PID 不是根节点 说明需要设置父节点 hasChild 为1
			if(!ISysCategoryService.ROOT_PID_VALUE.equals(categoryPid)){
				SysCategory parent = baseMapper.selectById(categoryPid);
				parentCode = parent.getCode();
				if(parent!=null && !"1".equals(parent.getHasChild())){
					parent.setHasChild("1");
					baseMapper.updateById(parent);
				}
			}
		}
		//update-begin- for：分类字典编码规则生成器做成公用配置
		JSONObject formData = new JSONObject();
		formData.putOpt("pid",categoryPid);
		categoryCode = (String) FillRuleUtil.executeRule(FillRuleConstant.CATEGORY,formData);
		//update-end- for：分类字典编码规则生成器做成公用配置
		sysCategory.setCode(categoryCode);
		sysCategory.setPid(categoryPid);
		baseMapper.insert(sysCategory);
	}

	@Override
	public void updateSysCategory(SysCategory sysCategory) {
		if(StrUtil.isEmpty(sysCategory.getPid())){
			sysCategory.setPid(ISysCategoryService.ROOT_PID_VALUE);
		}else{
			//如果当前节点父ID不为空 则设置父节点的hasChild 为1
			SysCategory parent = baseMapper.selectById(sysCategory.getPid());
			if(parent!=null && !"1".equals(parent.getHasChild())){
				parent.setHasChild("1");
				baseMapper.updateById(parent);
			}
		}
		baseMapper.updateById(sysCategory);
	}

	@Override
	public List<SysCategory> queryCategoryListByCode(String pcode) {
		String pid = ROOT_PID_VALUE;
		if(StrUtil.isNotEmpty(pcode)) {
			List<SysCategory> list = baseMapper.selectList(new LambdaQueryWrapper<SysCategory>().likeRight(SysCategory::getCode, pcode));
			return list;
		}
		return new ArrayList<>();
	}

	@Override
	public List<SysCategory> queryListByCodeList(ArrayList<String> codeList) {
		if(null != codeList && codeList.size()>0){
			QueryWrapper<SysCategory> queryMapper = new QueryWrapper();
			queryMapper.lambda().in(SysCategory::getCode,codeList);
			List<SysCategory> sysCategoryList = baseMapper.selectList(queryMapper);
			return sysCategoryList;
		}
		return new ArrayList<>();
	}

	@Override
	public List<SysCategory> queryByBrandCode(String brand) {
		return this.baseMapper.queryByBrandCode(brand);
	}

	@Override
	public List<TreeSelectVo> queryListByCode(String pcode) throws BootException {
		String pid = ROOT_PID_VALUE;
		if(StrUtil.isNotEmpty(pcode)) {
			List<SysCategory> list = baseMapper.selectList(new LambdaQueryWrapper<SysCategory>().eq(SysCategory::getCode, pcode));
			if(list==null || list.size() ==0) {
				throw new BootException("该编码【"+pcode+"】不存在，请核实!");
			}
			if(list.size()>1) {
				throw new BootException("该编码【"+pcode+"】存在多个，请核实!");
			}
			pid = list.get(0).getId();
		}
		return baseMapper.queryListByPid(pid,null);
	}

	@Override
	public List<TreeSelectVo> queryListByPid(String pid) {
		if(StrUtil.isEmpty(pid)) {
			pid = ROOT_PID_VALUE;
		}
		return baseMapper.queryListByPid(pid,null);
	}

	@Override
	public List<TreeSelectVo> queryListByPid(String pid,Map<String, String> condition) {
		if(StrUtil.isEmpty(pid)) {
			pid = ROOT_PID_VALUE;
		}
		return baseMapper.queryListByPid(pid,condition);
	}




	@Override
	public String queryIdByCode(String code) {
		return baseMapper.queryIdByCode(code);
	}

}
