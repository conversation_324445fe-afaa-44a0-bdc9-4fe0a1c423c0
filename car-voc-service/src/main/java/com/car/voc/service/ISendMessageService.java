package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.SysSendMessageLog;

/**
 * <AUTHOR>
 * @date ：Created in 2023/12/20
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
public interface ISendMessageService extends IService<SysSendMessageLog> {
    boolean sendDingtalk(String userName,String title,String table,String last);
    boolean sendAuditDingtalk(String userName,String title,String table,String last);

    boolean sendMessage(String phone, String msg);
}
