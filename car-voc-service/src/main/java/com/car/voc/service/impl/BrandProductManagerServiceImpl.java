package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.FillRuleConstant;
import com.car.voc.common.util.FillRuleUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.dto.BrandProductManagerDto;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.ModelGroupRelation;
import com.car.voc.entity.SysCategory;
import com.car.voc.entity.SysDictItem;
import com.car.voc.mapper.BrandProductManagerMapper;
import com.car.voc.model.BrandProductManagerModel;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IModelGroupRelationService;
import com.car.voc.service.ISysCategoryService;
import com.car.voc.service.ISysDictItemService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 品牌产品管理
 * @Date: 2021-04-09
 * @Version: V1.0
 */
@Service
public class BrandProductManagerServiceImpl extends ServiceImpl<BrandProductManagerMapper, BrandProductManager> implements IBrandProductManagerService {

    @Resource
    BrandProductManagerMapper brandProductManagerMapper;

    @Autowired
    ISysCategoryService sysCategoryService;
    @Autowired
    IModelGroupRelationService groupRelationService;
    @Autowired
    ISysDictItemService iSysDictItemService;

    @Override
    public void addBrandProduct(BrandProductManager brandProductManager) {
        String categoryCode = "";
        String categoryPid = IBrandProductManagerService.ROOT_PID_VALUE;
        String parentCode = null;
        if (StrUtil.isNotEmpty(brandProductManager.getPId())) {
            categoryPid = brandProductManager.getPId();

            //PID 不是根节点 说明需要设 为置父节点 hasChild1
            if (!IBrandProductManagerService.ROOT_PID_VALUE.equals(categoryPid)) {
                BrandProductManager parent = baseMapper.queryById(categoryPid);
                parentCode = parent.getCode();
                parentCode = parent.getCode();
                categoryPid = parent.getId();
                if (parent != null && !"1".equals(parent.getHasChild())) {
                    parent.setHasChild("1");
                    baseMapper.updateById(parent);
                }
            }
        }
        if (StrUtil.isNotBlank(brandProductManager.getPId())) {
            JSONObject formData = new JSONObject();
            formData.putOpt("pid", brandProductManager.getPId());
            categoryCode = (String) FillRuleUtil.executeRule(FillRuleConstant.BRAND_PRODUCT_MANAGER, formData);
            brandProductManager.setCode(categoryCode);
        }
        if (!"0".equals(brandProductManager.getPId()) && StrUtil.isNotBlank(brandProductManager.getModelGroupId())) {
            QueryWrapper<ModelGroupRelation> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ModelGroupRelation::getModelGroupId, brandProductManager.getModelGroupId());
            wrapper.lambda().eq(ModelGroupRelation::getCarSeriesCode, brandProductManager.getCode());
            ModelGroupRelation relation = modelGroupRelationService.getOne(wrapper);
            if (relation == null) {
                relation = new ModelGroupRelation();
                relation.setBrandCode("A12");
                relation.setCreateTime(new DateTime());
                relation.setCarSeriesCode(categoryCode);
                relation.setModelGroupId(brandProductManager.getModelGroupId());
                modelGroupRelationService.saveOrUpdate(relation);
            }
        }
        if (StrUtil.isBlankIfStr(brandProductManager.getId())) {
            brandProductManager.setCreateTime(new Date());
        }
        baseMapper.insert(brandProductManager);

    }

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IModelGroupRelationService modelGroupRelationService;

    @Override
    public void editBrandProduct(BrandProductManagerModel managerModel) {
        BrandProductManager brandProductManager = new BrandProductManager();
        if (StrUtil.isNotBlank(managerModel.getCarType())) {
            QueryWrapper<SysDictItem> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(SysDictItem::getDictId, CommonConstant.sys_car_type_id)
                    .eq(SysDictItem::getItemValue, managerModel.getCarType());
            SysDictItem item = iSysDictItemService.getOne(wrapper);
            if (item != null && item.getItemText() != null) {
                managerModel.setCarType(item.getItemText());
            }
        }
        BeanUtils.copyProperties(managerModel, brandProductManager);
        BrandProductManager code = baseMapper.queryById(managerModel.getId());
        brandProductManager.setCode(code.getCode());
        brandProductManager.setUpdateTime(new Date());
        updateById(brandProductManager);
        Set<String> nCarCodes = (Set<String>) redisUtil.get(CacheConstant.sys_disable_car_code);
        if (nCarCodes == null) {
            nCarCodes = new HashSet<>();
        }
        if (brandProductManager.getEnable() != null && brandProductManager.getEnable().equals(0)) {
            nCarCodes.add(code.getCode());
        } else if (brandProductManager.getEnable() != null && brandProductManager.getEnable().equals(1)) {
            nCarCodes.remove(code.getCode());
        }
        redisUtil.set(CacheConstant.sys_disable_car_code, nCarCodes);
        /**
         * 修改当前品牌车系权限
         */
        String userSeries = String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES_ALL, brandProductManager.getBrandCode());
        redisUtil.del(userSeries);
        this.brandSeriesAll(brandProductManager.getBrandCode());

        /*if (StrUtil.isNotBlank(managerModel.getModelGroupId())){
            QueryWrapper<ModelGroupRelation>  rea=new QueryWrapper<>();
            rea.lambda().eq(ModelGroupRelation::getCarSeriesCode,code.getCode());
            modelGroupRelationService.remove(rea);
            ModelGroupRelation relation=new ModelGroupRelation();
            relation.setBrandCode(CommonConstant.DEFAULT_BRAND);relation.setCarSeriesCode(code.getCode());
            relation.setModelGroupId(managerModel.getModelGroupId());
            modelGroupRelationService.saveOrUpdate(relation);
        }*/
    }

    @Override
    public IPage<BrandProductManager> getChildListBatch(String parentIds) {
        QueryWrapper<BrandProductManager> queryWrapper = new QueryWrapper<>();
        List<String> parentIdList = Arrays.asList(parentIds.split(","));
        queryWrapper.in("p_id", parentIdList);
        queryWrapper.lambda().eq(BrandProductManager::getEnable, 1);
        List<BrandProductManager> list = brandProductManagerMapper.selectList(queryWrapper);
        IPage<BrandProductManager> pageList = new Page<>(1, 1000, list.size());
        pageList.setRecords(list);
        return pageList;
    }

    @Override
    public String getCarNameByCarCode(String carCode) {
        if (carCode == null) {
            return "";
        }
        Object name = redisUtil.hmget(CacheConstant.SYS_CAR_CODE_NAME).get(carCode);
        if (name == null) {
            QueryWrapper<BrandProductManager> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().select(BrandProductManager::getCode, BrandProductManager::getName);
            List<BrandProductManager> lsi = this.list(queryWrapper);
            Map<String, Object> caral = new HashMap<>();
            lsi.stream().forEach(e -> caral.put(e.getCode(), e.getName()));
            redisUtil.hmset(CacheConstant.SYS_CAR_CODE_NAME, caral, CacheConstant.expire);
            name = caral.get(carCode);
        }
        return name + "";
    }

    @Override
    public Result<?> add(BrandProductManagerModel managerModel) {
        BrandProductManager brandProductManager = new BrandProductManager();
        BeanUtils.copyProperties(managerModel, brandProductManager);
        String categoryCode = "";
        Result<?> result = new Result<>();
        LambdaQueryWrapper<BrandProductManager> query = new LambdaQueryWrapper<BrandProductManager>().eq(BrandProductManager::getName, managerModel.getName());
        query.eq(BrandProductManager::getDelFlag, 0);
        List<BrandProductManager> brandProductManagers = baseMapper.selectList(query);
        if (CollectionUtil.isNotEmpty(brandProductManagers)) {
            result.error500("车系名称不能重复");
            return Result.error("车系名称不能重复");
        }
        brandProductManager.setSortNo(0);
        if (brandProductManager != null) {
            brandProductManager.setDelFlag(0);
            if (brandProductManager.getType() == 1) {//品牌
                brandProductManager.setPId("0");
                SysCategory sysCategory = sysCategoryService.getById(managerModel.getpId());
                brandProductManager.setCode(sysCategory.getCode());
                brandProductManager.setName(sysCategory.getName());
                brandProductManager.setRelationId(sysCategory.getId());
                brandProductManager.setHasChild("1");
            } else if (brandProductManager.getType() == 2 || brandProductManager.getType() == 3) {//车系
                if (brandProductManager.getType() == 2) {
                    JSONObject formData = new JSONObject();
                    formData.putOpt("pid", managerModel.getpId());
                    categoryCode = (String) FillRuleUtil.executeRule(FillRuleConstant.BRAND_PRODUCT_MANAGER, formData);
                    brandProductManager.setCode(categoryCode);
                }
            }
        }
        if (StrUtil.isNotBlank(brandProductManager.getCarType())) {
            QueryWrapper<SysDictItem> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(SysDictItem::getDictId, CommonConstant.sys_car_type_id)
                    .eq(SysDictItem::getItemValue, brandProductManager.getCarType());
            SysDictItem item = iSysDictItemService.getOne(wrapper);
            if (item != null && item.getItemText() != null) {
                brandProductManager.setCarType(item.getItemText());
            }
        }
        try {
            brandProductManager.setPId(managerModel.getpId());
            this.addBrandProduct(brandProductManager);
            Set<String> nCarCodes = (Set<String>) redisUtil.get(CacheConstant.sys_disable_car_code);
            if (nCarCodes == null) {
                nCarCodes = new HashSet<>();
            }
            if (brandProductManager.getEnable() != null && brandProductManager.getEnable().equals(0)) {
                nCarCodes.add(brandProductManager.getCode());
            } else if (brandProductManager.getEnable() != null && brandProductManager.getEnable().equals(1)) {
                nCarCodes.remove(brandProductManager.getCode());
            }
            redisUtil.set(CacheConstant.sys_disable_car_code, nCarCodes);
            /**
             * 修改当前品牌车系权限
             */
            String userSeries = String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES_ALL, brandProductManager.getBrandCode());
            redisUtil.del(userSeries);
            this.brandSeriesAll(brandProductManager.getBrandCode());

            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return Result.error("操作失败");
        }


        return Result.OK("添加成功！");
    }

    @Override
    public String selectByBrandCode(String brandCode) {
        return brandProductManagerMapper.selectByBrandCode(brandCode);
    }

    @Override
    public Page<BrandProductManager> queryByPage(Page<BrandProductManager> page, BrandProductManager brandProductManager) {
        brandProductManager.setDelFlag(0);

        List<BrandProductManager> reulsts = brandProductManagerMapper.queryByPage(page, brandProductManager);
        List<ModelGroupRelation> relations = modelGroupRelationService.list();
        reulsts.forEach(e -> {
            ModelGroupRelation ve = relations.stream().filter(c -> e.getCode().equals(c.getCarSeriesCode())).findFirst().orElse(null);
            if (ve != null) {
                e.setModelGroupId(ve.getModelGroupId());
            }
            e.setOrderBy(e.getSortNo());
            e.setChildren(getChildListBatch(e.getId()).getRecords());
            if (e.getCode().contains("A12")) {
                e.setRelationship("本品");
            } else {
                e.setRelationship("竞品");
            }
        });
        return page.setRecords(reulsts);
    }

    @Override
    public List<BrandProductManagerModel> findAll() {
        List<BrandProductManagerModel> list = baseMapper.selectList(new QueryWrapper<>())
                .stream().map(e -> {
                            BrandProductManagerModel model = new BrandProductManagerModel();
                            BeanUtil.copyProperties(e, model);
                            return model;
                        }
                )
                .collect(Collectors.toList());
        return list;
    }

    @Override
    public Set<BrandProductManagerDto> brandSeriesAll(String brandCode) {
        String userSeries = String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES_ALL, brandCode);
        if (redisUtil.hasKey(userSeries)) {
            Set<Object> seriesAll = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES_ALL, brandCode));
            return seriesAll.stream().map(e -> (BrandProductManagerDto) e).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<BrandProductManager> objectQueryWrapper = new QueryWrapper<BrandProductManager>().lambda();
        objectQueryWrapper.select(BrandProductManager::getId, BrandProductManager::getCode,
                BrandProductManager::getName, BrandProductManager::getAlias,
                BrandProductManager::getPId, BrandProductManager::getEnglishName);
        objectQueryWrapper.likeRight(BrandProductManager::getCode, brandCode);
        Set<BrandProductManagerDto> list = this.list(objectQueryWrapper)
                .stream()
                .map(e -> {
                            BrandProductManagerDto model = new BrandProductManagerDto();
                            BeanUtil.copyProperties(e, model);
                            return model;
                        }
                )
                .collect(Collectors.toSet());
        redisUtil.sSetAllObject(userSeries, list);
        return list;
    }
}
