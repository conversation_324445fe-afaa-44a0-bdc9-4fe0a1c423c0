package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.SysUser;
import com.car.voc.entity.SysUserDepart;
import com.car.voc.entity.SysUserRole;
import com.car.voc.mapper.SysUserDepartMapper;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.DepartIdModel;
import com.car.voc.service.ISysDepartService;
import com.car.voc.service.ISysUserDepartService;
import com.car.voc.service.ISysUserRoleService;
import com.car.voc.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <P>
 * 用户部门表实现类
 * <p/>
 *
 *@since 2019-02-22
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleService {

}
