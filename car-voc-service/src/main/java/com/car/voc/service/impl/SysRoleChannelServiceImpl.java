package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.SysRoleChannel;
import com.car.voc.entity.SysRolePermission;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.mapper.SysRoleChannelMapper;
import com.car.voc.mapper.VocChannelCategoryMapper;
import com.car.voc.service.SysRoleChannelService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
*
*/
@Service
public class SysRoleChannelServiceImpl extends ServiceImpl<SysRoleChannelMapper, SysRoleChannel>
implements SysRoleChannelService {

    @Autowired
    VocChannelCategoryMapper vocChannelCategoryMapper;

    @Override
    public void saveOrUpdateData(String roleId, List<String> channelIdList,String brandCode){
        if (CollectionUtils.isEmpty(channelIdList)){
            return;
        }
        channelIdList.addAll(getFirstChannel(channelIdList));
        if(channelIdList == null) {
            return;
        }
        List<String> channelIds = new ArrayList<>();
        channelIds.addAll(channelIdList);
        QueryWrapper<SysRoleChannel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysRoleChannel::getRoleId,roleId).eq(SysRoleChannel::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
        List<SysRoleChannel> list = this.list(queryWrapper);
        if(!CollectionUtils.isEmpty(list)){
            UpdateWrapper<SysRoleChannel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SysRoleChannel::getRoleId,roleId);
            updateWrapper.lambda().eq(SysRoleChannel::getBrandCode,brandCode).eq(SysRoleChannel::getDelFlag, CommonConstant.DEL_FLAG_STR_0)
                    .set(SysRoleChannel::getDelFlag, CommonConstant.DEL_FLAG_STR_1);
            this.update(updateWrapper);
        }
        List<SysRoleChannel> channels =new ArrayList<>();
        channelIds.stream().forEach(channelId ->{
            SysRoleChannel sysRoleChannel = new SysRoleChannel();
            sysRoleChannel.setChannelId(channelId);
            sysRoleChannel.setRoleId(roleId);
            sysRoleChannel.setBrandCode(brandCode);
            channels.add(sysRoleChannel);
        });
        this.saveBatch(channels);
    }

    public List<String> getFirstChannel(List<String> channelIds){
        if(CollectionUtils.isEmpty(channelIds)){
            return new ArrayList<String>();
        }
        List<VocChannelCategory> list = vocChannelCategoryMapper.selectBatchIds(channelIds);
        return list.stream().filter(s->!"0".equals(s.getPid())).map(VocChannelCategory::getPid).collect(Collectors.toList());
    }

}
