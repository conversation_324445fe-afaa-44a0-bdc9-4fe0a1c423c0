package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.FillRuleConstant;
import com.car.voc.common.util.FillRuleUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.dto.BusinessTagImportDto;
import com.car.voc.dto.TagCacheDto;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.TagDepartRelation;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.VocBusinessTagMapper;
import com.car.voc.model.VocBusinessTagListQueryModel;
import com.car.voc.model.VocBusinessTagModel;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.service.ITagDepartRelationService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.SysRoleBusinessTagService;
import com.car.voc.vo.VocBusinessTagListVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.risk.TagRoleVo;
import com.car.voc.vo.thirdVo.VocTagVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Date: 2021-03-30
 * @Version: V1.0
 */
@Service
public class VocBusinessTagServiceImpl extends ServiceImpl<VocBusinessTagMapper, VocBusinessTag> implements IVocBusinessTagService {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ITagDepartRelationService tagDepartRelationService;
    @Resource
    VocBusinessTagMapper vocBusinessTagMapper;
    @Autowired
    IVocBusinessTagService vocBusinessTagService;
    @Autowired
    private SysDepartServiceImpl sysDepartServiceImpl;
    @Autowired
    IFaultProblemService faultProblemService;
    @Override
    public void addVocBusinessTag(VocBusinessTagModel vocBusinessTagModel) {

        VocBusinessTag vocBusinessTag = new VocBusinessTag();
        vocBusinessTag.setBrand("B");
        vocBusinessTag.setEnable(true);
        vocBusinessTag.setOther(0);
        BeanUtil.copyProperties(vocBusinessTagModel, vocBusinessTag);
        String n_tagCode = "";
        if (StrUtil.isEmpty(vocBusinessTag.getPid())) {
            vocBusinessTag.setPid(IVocBusinessTagService.ROOT_PID_VALUE);
        } else {
            //如果当前节点父ID不为空 则设置父节点的hasChildren 为1
            VocBusinessTag parent = baseMapper.selectById(vocBusinessTag.getPid());
            if (parent != null && !"1".equals(parent.getHasChild())) {
                parent.setHasChild("1");
                baseMapper.updateById(parent);
            }
        }

        JSONObject formData = new JSONObject();
        formData.putOpt("pid", vocBusinessTag.getPid());
        n_tagCode = (String) FillRuleUtil.executeRule(FillRuleConstant.BUSINESS_TAG, formData);
        //update-end- for：分类字典编码规则生成器做成公用配置
        vocBusinessTag.setTagCode(n_tagCode);
        vocBusinessTag.setOrderBy(7);
        baseMapper.insert(vocBusinessTag);

        Set<String> nCarCodes = (Set<String>) redisUtil.get(CacheConstant.sys_disable_tag_code);
        if (nCarCodes == null) {
            nCarCodes = new HashSet<>();
        }
        if (!vocBusinessTag.isEnable()) {
            nCarCodes.add(vocBusinessTag.getTagCode());
        } else if (vocBusinessTag.isEnable()) {
            nCarCodes.remove(vocBusinessTag.getTagCode());
        }
        redisUtil.set(CacheConstant.sys_disable_tag_code, nCarCodes);


        vocBusinessTagModel.setTagCode(vocBusinessTag.getTagCode());

    }

    private void saveOrupdateTagDepart(VocBusinessTagModel vocBusinessTagModel) {
        QueryWrapper<TagDepartRelation> wrapper = new QueryWrapper();
        wrapper.lambda().eq(TagDepartRelation::getTagCode, vocBusinessTagModel.getTagCode());
        tagDepartRelationService.remove(wrapper);
        if (vocBusinessTagModel.getDeparts() != null && vocBusinessTagModel.getDeparts().size() > 0) {
            vocBusinessTagModel.getDeparts().forEach(e -> {
                e.setTagCode(vocBusinessTagModel.getTagCode());
                e.setCreateTime(new Date());
                e.setDelFlag(0);
            });
            tagDepartRelationService.saveOrUpdateBatch(vocBusinessTagModel.getDeparts());
        }
    }

    @Override
    public void updateVocBusinessTag(VocBusinessTagModel vocBusinessTagModel) {
        VocBusinessTag vocBusinessTag = new VocBusinessTag();
        BeanUtil.copyProperties(vocBusinessTagModel, vocBusinessTag);
        VocBusinessTag entity = this.getById(vocBusinessTag.getId());
        if (entity == null) {
            throw new BootException("未找到对应实体");
        }
        String old_pid = entity.getPid();
        String new_pid = vocBusinessTag.getPid();
        if (!old_pid.equals(new_pid)) {
            updateOldParentNode(old_pid);
            if (StrUtil.isEmpty(new_pid)) {
                vocBusinessTag.setPid(IVocBusinessTagService.ROOT_PID_VALUE);
            }
            if (!IVocBusinessTagService.ROOT_PID_VALUE.equals(vocBusinessTag.getPid())) {
                baseMapper.updateTreeNodeStatus(vocBusinessTag.getPid(), IVocBusinessTagService.HASCHILD);
            }
        }
        vocBusinessTag.setOrderBy(entity.getOrderBy());
        baseMapper.updateById(vocBusinessTag);

        Set<String> nCarCodes = (Set<String>) redisUtil.get(CacheConstant.sys_disable_tag_code);
        if (nCarCodes == null) {
            nCarCodes = new HashSet<>();
        }
        if (!vocBusinessTag.isEnable()) {
            nCarCodes.add(vocBusinessTag.getTagCode());
        } else if (vocBusinessTag.isEnable()) {
            nCarCodes.remove(vocBusinessTag.getTagCode());
        }
        redisUtil.set(CacheConstant.sys_disable_tag_code, nCarCodes);


        vocBusinessTagModel.setTagCode(vocBusinessTag.getTagCode());

//		saveOrupdateTagDepart(vocBusinessTagModel);
    }

    @Override
    public void deleteVocBusinessTag(String id) throws BootException {
        VocBusinessTag vocBusinessTag = this.getById(id);
        if (vocBusinessTag == null) {
            throw new BootException("未找到对应实体");
        }
        updateOldParentNode(vocBusinessTag.getPid());
        UpdateWrapper<VocBusinessTag> update = new UpdateWrapper<>();
        update.lambda().ge(VocBusinessTag::getId, id).set(VocBusinessTag::getYndel, 1);
//		baseMapper.deleteById(id);
        QueryWrapper<TagDepartRelation> wrapper = new QueryWrapper();
        wrapper.lambda().eq(TagDepartRelation::getTagCode, vocBusinessTag.getTagCode());
//		tagDepartRelationService.remove(wrapper);
    }

    @Override
    public String getNameByCode(String zbCode) {
        if (StrUtil.isBlankIfStr(zbCode)){
            return null;
        }
        // 从缓存获取
        String cacheKey = CacheConstant.SYS_BUSINESS_TAG_CACHE + zbCode;
        String name = (String) redisUtil.get(cacheKey);
        if (StrUtil.isNotBlank(name)) {
            return name;
        }
        // 构建查询条件
        QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
        wrapper.select("name");
        wrapper.lambda().eq(VocBusinessTag::getTagCode, zbCode);
        name = baseMapper.getNameByCode(wrapper);
        if (name == null){
            name= faultProblemService.getNameByCode(zbCode);
        }
        // 处理结果并缓存
        name = StrUtil.blankToDefault(name, "");
        redisUtil.set(cacheKey, name, CacheConstant.expire*100);
        return name;
    }
    @Override
    public List<VocBusinessTagVo> queryTreeList(QueryWrapper<VocBusinessTag> wrapper) {
        wrapper.select("name", "TAG_CODE", "pid", "id");
        wrapper.lambda().eq(VocBusinessTag::getBrand, "B");
        List<VocBusinessTag> alls = baseMapper.selectList(wrapper);
        List<VocBusinessTagVo> list = BeanUtil.copyToList(alls, VocBusinessTagVo.class);
        List<VocBusinessTagVo> tree1 = list.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
        Collections.sort(tree1, (VocBusinessTagVo o1, VocBusinessTagVo o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getName(), o2.getName()));
        tree1.forEach(e1 -> {
            List<VocBusinessTagVo> tree2 = list.stream().filter(i -> e1.getId().equals(i.getPid())).collect(Collectors.toList());
            tree2.forEach(e2 -> {
                List<VocBusinessTagVo> tree3 = list.stream().filter(i -> e2.getId().equals(i.getPid())).collect(Collectors.toList());
                tree3.forEach(e3 -> {
                    List<VocBusinessTagVo> tree4 = list.stream().filter(i -> e3.getId().equals(i.getPid())).collect(Collectors.toList());
                    Collections.sort(tree4, (VocBusinessTagVo o1, VocBusinessTagVo o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getName(), o2.getName()));

                    e3.setChildes(tree4);
                });
                Collections.sort(tree3, (VocBusinessTagVo o1, VocBusinessTagVo o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getName(), o2.getName()));

                e2.setChildes(tree3);
            });
            Collections.sort(tree2, (VocBusinessTagVo o1, VocBusinessTagVo o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getName(), o2.getName()));

            e1.setChildes(tree2);
        });
        return tree1;
    }

    @Autowired
    SysRoleBusinessTagService roleBusinessTagService;

    @Override
    public IPage<VocBusinessTagListVo> queryByPage(Page<VocBusinessTagListVo> page, VocBusinessTagListQueryModel vocBusinessTag, HttpServletRequest req) {

        String parentId = vocBusinessTag.getPid();
        if (StrUtil.isBlank(parentId)) {
            parentId = "0";
            vocBusinessTag.setPid("0");
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.select("id", "depart_name");
        List<SysDepart> sysDeparts = sysDepartServiceImpl.list(queryWrapper);
        Map<String, String> departs = sysDeparts.stream()
                .collect(Collectors.toMap(SysDepart::getId, SysDepart::getDepartName));
        IPage<VocBusinessTagListVo> relist = vocBusinessTagMapper.queryByPage(page, vocBusinessTag);
        relist.getRecords().forEach(e -> {
            List<TagRoleVo> list = roleBusinessTagService.queryRoleNameByTagCode(new String[]{e.getSecondDimensionCode()});
            if (list != null && list.size() > 0) {
                e.setRoles(list.stream().map(TagRoleVo::getRoleName).collect(Collectors.toSet()));
                e.setRolesIds(list.stream().map(TagRoleVo::getRoleId).collect(Collectors.toSet()));
            }
            e.setRelatedDepartmentsName(departs.get(e.getRelatedDepartments()));
        });
        return relist;
    }

    @Override
    public Result<?> batchImport(MultipartFile file) {
        try {
            //获取文件流
            InputStream inputStream = file.getInputStream();
            //easyexcel导入文件
            EasyExcel.read(inputStream, BusinessTagImportDto.class, new BusinessTagNoCodeImportListener(vocBusinessTagService)).sheet().headRowNumber(1).doRead();
            return Result.OK();
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("数据异常无法导入文件!");
        }
    }

    @Override
    public String queryIdByTagBrandAndCode(String brand, String parentTagCode) {
        return this.baseMapper.queryIdByTagBrandAndCode(brand, parentTagCode);
    }

    @Override
    public boolean addRecord(VocBusinessTag record) {


        try {
            return baseMapper.addRecord(record) > -1;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String getAllName(String labelCode) {
        String level = labelCode.substring(4);
        int a = level.length() / 3;
        List<String> set = new ArrayList<>();
        for (int i = 0; i < a; i++) {
            String s = getNameByCode("B1" + level.substring(0, (i + 1) * 3));
            set.add(s);
        }
        return String.join("#", set);
    }

    @Override
    public VocBusinessTag selectByTagId(String tagId) {
        return baseMapper.selectById(tagId);
    }

    @Override
    public IPage<VocTagVo> vocTags(Page<VocTagVo> page, VocBusinessTagListQueryModel vocBusinessTag, HttpServletRequest req) {
        IPage<VocTagVo> relist = vocBusinessTagMapper.vocTags(page, vocBusinessTag);
        return relist;
    }

    @Override
    public List<VocBusinessTagVo> getAllTags(List<String> tags) {
        return vocBusinessTagMapper.getAllTags(tags);
    }


    @Override
    public List<VocBusinessTagModel> findAll() {
        List<VocBusinessTag> list = vocBusinessTagMapper.selectList(new QueryWrapper<>());
        List<VocBusinessTagModel> modelList = new ArrayList<>();
        list.stream().forEach(e -> {
            VocBusinessTagModel model = new VocBusinessTagModel();
            BeanUtil.copyProperties(e, model);
            modelList.add(model);
        });
        return modelList;
    }


    /**
     * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
     *
     * @param pid
     */
    private void updateOldParentNode(String pid) {
        if (!IVocBusinessTagService.ROOT_PID_VALUE.equals(pid)) {
            Integer count = baseMapper.selectCount(new QueryWrapper<VocBusinessTag>().eq("pid", pid));
            if (count == null || count <= 1) {
                baseMapper.updateTreeNodeStatus(pid, IVocBusinessTagService.NOCHILD);
            }
        }
    }

    @Override
    public Set<TagCacheDto> businessTagAll() {
        String formatKey = String.format(CacheConstant.SYS_BUSINESS_TAG_ALL);
        if (redisUtil.hasKey(formatKey)) {
            Set<Object> seriesAll = redisUtil.sGet(formatKey);
            return seriesAll.stream().map(e -> (TagCacheDto) e).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<VocBusinessTag> query = new QueryWrapper<VocBusinessTag>().lambda();
        query.select(VocBusinessTag::getId, VocBusinessTag::getTagCode,
                VocBusinessTag::getName, VocBusinessTag::getNameEn,
                VocBusinessTag::getPid);
        Set<TagCacheDto> res = this.list(query).stream()
                .map(e -> {
                            TagCacheDto model = new TagCacheDto();
                            BeanUtil.copyProperties(e, model);
                            model.setCode(e.getTagCode());
                            return model;
                        }
                ).collect(Collectors.toSet());
        redisUtil.sSetAllObject(formatKey, res);
        return res;
    }

}
