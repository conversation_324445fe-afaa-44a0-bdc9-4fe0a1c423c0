package com.car.voc.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.dto.BrandProductManagerDto;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.model.BrandProductManagerModel;

import java.util.List;
import java.util.Set;

/**
 * @Description: 品牌产品管理
 *
 * @Date:   2021-04-09
 * @Version: V1.0
 */
public interface IBrandProductManagerService extends IService<BrandProductManager> {

    /**根节点父ID的值*/
    public static final String ROOT_PID_VALUE = "0";

    void addBrandProduct(BrandProductManager brandProductManager);

    Page<BrandProductManager> queryByPage(Page<BrandProductManager> page, BrandProductManager brandProductManager);

    void editBrandProduct(BrandProductManagerModel brandProductManager);

    IPage<BrandProductManager> getChildListBatch(String parentIds);

    String getCarNameByCarCode(String carCode);

    Result<?> add(BrandProductManagerModel brandProductManager);

    String selectByBrandCode(String brandCode);

    List<BrandProductManagerModel> findAll();

    Set<BrandProductManagerDto> brandSeriesAll(String brandCode);
}
