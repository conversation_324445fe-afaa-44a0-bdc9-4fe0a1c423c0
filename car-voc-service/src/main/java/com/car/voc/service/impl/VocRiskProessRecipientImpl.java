package com.car.voc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.entity.VocRiskProcessRecipient;
import com.car.voc.entity.VocRiskProcessRecipientBase;
import com.car.voc.entity.VocRiskProcessRecipientTag;
import com.car.voc.mapper.VocRiskProessRecipientMapper;
import com.car.voc.service.IVocRiskProessRecipientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @version 1.0.0
 * @ClassName VocRiskProessRecipientImp.java
 * @Description TODO
 * @createTime 2023年02月06日 11:18
 * @Copyright voc
 */
@Service
public class VocRiskProessRecipientImpl extends ServiceImpl<VocRiskProessRecipientMapper, VocRiskProcessRecipient> implements IVocRiskProessRecipientService {

    @Autowired
    private VocRiskProessRecipientMapper proessRecipientMapper;


    @Override
    public List<VocRiskProcessRecipientTag> selectByProessRecipientTag(String id) {
        return baseMapper.selectByProessRecipientTag(id);
    }

    @Override
    public List<VocRiskProcessRecipientBase> selectByProcessRecipientBase(String id) {
        return baseMapper.selectByProcessRecipientBase(id);
    }

    @Override
    public Integer delByProessRecipientTag(String id) {
        return baseMapper.delByProessRecipientTag(id);
    }

    @Override
    public Integer delByProessRecipientQualityTag(String id) {
        return baseMapper.delByProessRecipientQualityTag(id);
    }

    @Override
    public List<VocRiskProcessRecipientTag> queryPermissionByUserId(String userId) {
        return baseMapper.queryPermissionByUserId(userId);
    }

    @Override
    public List<VocRiskProcessRecipientBase> queryPermissionQualityByUserId(String userId) {
        return baseMapper.queryPermissionQualityByUserId(userId);
    }

    @Override
    public List<VocRiskProcessRecipientTag> queryHandlePermissionByUserId(String userId) {
        return baseMapper.queryHandlePermissionByUserId(userId);
    }

    @Override
    public List<VocRiskProcessRecipientBase> queryHandlePermissionQualityByUserId(String userId) {
        return baseMapper.queryHandlePermissionQualityByUserId(userId);
    }

    @Override
    public List<VocRiskProcessRecipientTag> queryBrandTagByReId(String id) {
        return baseMapper.queryBrandTagByReId(id);
    }

    @Override
    public void saveBatchQualityTags(List<VocRiskProcessRecipientBase> qualityTags) {
        if (CollectionUtils.isEmpty(qualityTags)) {
            return;
        }
        proessRecipientMapper.saveBatchQualityTags(qualityTags);
    }

    @Override
    public void saveBatchRecipientTags(List<VocRiskProcessRecipientTag> recipientTags) {
        if (CollectionUtils.isEmpty(recipientTags)) {
            return;
        }
        proessRecipientMapper.saveBatchRecipientTags(recipientTags);
    }
}
