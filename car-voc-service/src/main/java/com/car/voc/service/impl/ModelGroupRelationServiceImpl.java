package com.car.voc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.ModelGroupRelation;
import com.car.voc.mapper.ModelGroupRelationMapper;
import com.car.voc.service.IModelGroupRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName ModelGroupServiceImpl.java
 * @Description TODO
 * @createTime 2022年09月26日 11:14
 * @Copyright voc
 */
@Service
public class ModelGroupRelationServiceImpl extends ServiceImpl<ModelGroupRelationMapper, ModelGroupRelation> implements IModelGroupRelationService {
    @Resource
    ModelGroupRelationMapper modelGroupRelationMapper;

    @Override
    public Set<String> queryCarNameByModelGroupId(String id) {
        Set<String> names=modelGroupRelationMapper.queryCarNameByModelGroupId(id);
        return names;
    }

    @Override
    public List<BrandProductManager> queryCarByModelGroupId(String id) {
        return modelGroupRelationMapper.queryCarByModelGroupId(id);
    }
}
