package com.car.voc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.entity.TagDepartRelation;
import com.car.voc.mapper.TagDepartRelationMapper;
import com.car.voc.service.ITagDepartRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName TagDepartRelationServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月08日 20:13
 * @Copyright voc
 */
@Service
public class TagDepartRelationServiceImpl extends ServiceImpl<TagDepartRelationMapper,TagDepartRelation> implements ITagDepartRelationService {
    @Resource
    TagDepartRelationMapper tagDepartRelationMapper;
    @Override
    public Set<String> queryDepartNameByTagCode(String[] strings) {
        return tagDepartRelationMapper.queryDepartNameByTagCode(strings);
    }
}
