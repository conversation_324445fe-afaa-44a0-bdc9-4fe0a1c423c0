package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.service.IVocBusinessTagService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * @Date:   2021-03-30
 * @Version: V1.0
 */
public class VocBusinessTagImportListener extends AnalysisEventListener<Map<Integer, String>> {
	private static final Logger LOGGER = LoggerFactory.getLogger(VocBusinessTagImportListener.class);

	@Autowired
	private IVocBusinessTagService businessTagService;
	/**
	 * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
	 */
	private static final int BATCH_COUNT = 1000;
	List<Map<Integer, String>> list = new ArrayList<Map<Integer, String>>();

	private Map<String,Integer> headMap=new HashMap<>();
	Set<String> tagCodes =new HashSet<>();
	public VocBusinessTagImportListener(IVocBusinessTagService vocBusinessTagService) {
		this.businessTagService=vocBusinessTagService;
	}

	@Override
	public void invoke(Map<Integer, String> data, AnalysisContext context) {
		LOGGER.info("解析到一条数据:{}", JSON.toJSONString(data));
		list.add(data);
		if (list.size() >= BATCH_COUNT) {
			saveData();
			list.clear();
		}
	}
	@Override
	public void invokeHeadMap(Map<Integer, String> head, AnalysisContext context) {
		LOGGER.info("解析到一条数据:{}", JSON.toJSONString(head));
		head.forEach((index,key)->{
			headMap.put(key,index);
		});

	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		saveData();
		LOGGER.info("所有数据解析完成！");
	}

	private boolean addRecordBy(Map<Integer,String> map,String tagName,String childTag,String parentTag){
		boolean result=false;
		if(headMap.containsKey(tagName)&&StrUtil.isNotBlank(map.get(headMap.get(tagName+"编码")))){
			VocBusinessTag record=new VocBusinessTag();
			record.setBrand("B");
			record.setIndustryId("11");
			record.setName(map.get(headMap.get(tagName)));
			record.setTagCode(map.get(headMap.get(tagName+"编码")));
			record.setNameEn(map.get(headMap.get(tagName+"英文")));
			record.setTagType("0");
			record.setEnable(true);

			if (record.getTagCode().length()<14){
				if (map.get(headMap.get("是否兜底"))!=null){
					Integer s= Integer.valueOf(map.get(headMap.get("是否兜底")));
					record.setOther(s);
				}

			}

			if(StrUtil.isNotBlank(childTag)&&StrUtil.isNotBlank(map.get(headMap.get(childTag)))){
				record.setHasChild("1");
			}else{
				record.setHasChild("0");
			}
			if(StrUtil.isEmpty(parentTag)){
				record.setPid("0");
			}else {
				record.setPid(businessTagService.queryIdByTagBrandAndCode(record.getBrand(),map.get(headMap.get(parentTag+"编码"))));
			}
			record.setId(IdWorker.getIdStr());
			try{
				if (!tagCodes.contains(record.getTagCode())){
					result=businessTagService.addRecord(record);
				}
				tagCodes.add(record.getTagCode());
			}catch (Exception e){
				LOGGER.error("",e);
			}
		}
		return result;
	}


	/**
	 * 加上存储数据库
	 */
	private void saveData() {
		LOGGER.info("{}条数据，开始存储数据库！", list.size());
		list.forEach(map->{
			addRecordBy(map,"一级指标","二级指标",null);
			addRecordBy(map,"二级指标","三级指标","一级指标");
			addRecordBy(map,"三级指标","四级指标","二级指标");
			addRecordBy(map,"四级指标",null,"三级指标");

		});

		LOGGER.info("存储数据库成功！");
	}
}
