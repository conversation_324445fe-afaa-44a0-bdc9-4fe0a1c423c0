package com.car.voc.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.Sm4Util;
import com.car.voc.exception.BootException;
import com.car.voc.model.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletRequest;

/**
 * @version 1.0.0
 * @ClassName CommonService.java
 * @Description TODO
 * @createTime 2022年09月13日 16:53
 * @Copyright voc
 */
public class CommonService {


    /**
     * 根据request中的token获取用户账号
     *
     * @param request
     * @return
     */
    public static String getUserNameByToken(HttpServletRequest request) {
        String accessToken = request.getHeader("X-Access-Token");
        String username = getUsername(accessToken);
        if (StrUtil.isEmpty(username)) {
            throw new BootException("未获取到用户");
        }
        return username;
    }

    public static String getUsername(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            String username = jwt.getClaim("username").asString();
            username = Sm4Util.decryptEcb(CommonConstant.aes_key, username);
            return username;
        } catch (Exception e) {
            return null;
        }
    }

    public static String getUserId() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        return loginUser.getId();
    }
    @Value("${configuration.tokenCheckToken.clientSecret}")
    private String clientSecret;
    public  String GetCCToken() {
        String url = "https://cc.dfes.com.cn/api/oauth2/oauth/token";
        String requestBody = "grant_type=client_credentials&client_id=client_voc&client_secret="+clientSecret;

        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/x-www-form-urlencoded");
        request.body(requestBody);
        HttpResponse response = request.execute();
        System.out.println(JSONUtil.parseObj(response.body()));
        return JSONUtil.parseObj(response.body()).getStr("access_token");
    }

}
