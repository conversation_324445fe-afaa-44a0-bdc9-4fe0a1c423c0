package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.SysDictItem;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.RegionalDictVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Set;

/**
 * (VocBrandRegion)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-20 17:03:43
 */
public interface VocBrandRegionService extends IService<VocBrandRegion> {


    List<RegionalDictVo> queryBrandRegion(String brandCode, String id);

    List<RegionalDictVo> setChildes(Integer i, List<RegionalDictVo> regionalDictVos);

    List<SysDictItem> queryInternational();

    Set<VocBrandRegion> brandRegionAll(String code);
    List<VocBrandRegion> cacheBrandRegionAll();
}
