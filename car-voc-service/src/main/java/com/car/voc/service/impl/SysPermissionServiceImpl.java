package com.car.voc.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.RoleIndexConfigEnum;
import com.car.voc.common.util.MD5Util;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.dto.BrandProductManagerDto;
import com.car.voc.entity.*;
import com.car.voc.mapper.*;
import com.car.voc.model.LoginUser;
import com.car.voc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName SysPermissionServiceImpl.java
 * @Description TODO
 * @createTime 2022年09月13日 15:11
 * @Copyright voc
 */
@Service
@Slf4j
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements ISysPermissionService {

    @Autowired
    ISysUserService sysUserService;
    @Autowired
    SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    ISysUserDepartService sysUserDepartService;
    @Autowired
    SysRoleSeriesMapper sysRoleSeriesMapper;
    @Autowired
    SysRoleChannelMapper sysRoleChannelMapper;
    @Autowired
    SysRoleBusinessTagMapper sysRoleBusinessTagMapper;
    @Autowired
    SysRolePermissionMapper sysRolePermissionMapper;

    @Autowired
    IVocRiskAlertReviewerService riskAlertReviewerService;
    @Autowired
    IVocRiskProessRecipientService recipientService;
    @Autowired
    VocBusinessTagMapper vocBusinessTagMapper;
    @Autowired
    SysRoleAreaMapper sysRoleAreaMapper;

    @Autowired
    SysRoleMapper sysRoleMapper;

    @Autowired
    IBrandProductManagerService brandProductManagerService;
    @Autowired
    IFaultProblemService faultProblemService;
    @Autowired
    VocBrandRegionService vocBrandRegionService;

    /**
     * 判断是否授权首页
     *
     * @param metaList
     * @return
     */
    public static boolean hasIndexPage(List<SysPermission> metaList) {
        boolean hasIndexMenu = false;
        for (SysPermission sysPermission : metaList) {
            if ("首页".equals(sysPermission.getName())) {
                hasIndexMenu = true;
                break;
            }
        }
        return hasIndexMenu;
    }

    @Override
    public Result<?> getUserPermissionByToken() {
        Result<JSONObject> result = new Result<JSONObject>();
        long totalStartTime = System.currentTimeMillis();
        try {
            long startTime = System.currentTimeMillis();
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            long endTime = System.currentTimeMillis();
            log.info("获取当前用户耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            if (StrUtil.isEmptyIfStr(loginUser)) {
                return Result.error("请登录系统！");
            }

            startTime = System.currentTimeMillis();
            cachePermission(loginUser.getId());
            endTime = System.currentTimeMillis();
            log.info("缓存权限耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            startTime = System.currentTimeMillis();
            Set<Object> permissionSet = redisUtil.sGet(CacheConstant.SYS_USER_PERMISSION + loginUser.getId());
            endTime = System.currentTimeMillis();
            log.info("Redis获取权限耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            List<String> permissionIds = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());

            startTime = System.currentTimeMillis();
            List<SysPermission> metaList = queryByUser(loginUser.getUsername());
            endTime = System.currentTimeMillis();
            log.info("查询用户权限耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            startTime = System.currentTimeMillis();
            List<String> roles = sysUserService.getRole(loginUser.getUsername());
            endTime = System.currentTimeMillis();
            log.info("获取用户角色耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            String compUrl = RoleIndexConfigEnum.getIndexByRoles(roles);
            if (StrUtil.isNotBlank(compUrl)) {
                List<SysPermission> menus = metaList.stream().filter(sysPermission -> "首页".equals(sysPermission.getName())).collect(Collectors.toList());
                if (permissionIds.contains(menus.get(0).getId())) {
                    menus.get(0).setComponent(compUrl);
                }
            }

            JSONObject json = new JSONObject();
            JSONArray menujsonArray = new JSONArray();

            startTime = System.currentTimeMillis();
            this.getPermissionJsonArray(menujsonArray, metaList, null, permissionIds, loginUser.getId());
            endTime = System.currentTimeMillis();
            log.info("获取权限JSON数组耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            startTime = System.currentTimeMillis();
            this.handleFirstLevelMenuHidden(menujsonArray);
            endTime = System.currentTimeMillis();
            log.info("处理一级菜单耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            JSONArray authjsonArray = new JSONArray();
            this.getAuthJsonArray(authjsonArray, metaList);

            startTime = System.currentTimeMillis();
            LambdaQueryWrapper<SysPermission> query = new LambdaQueryWrapper<SysPermission>();
            query.eq(SysPermission::getDelFlag, CommonConstant.DEL_FLAG_0);
            query.eq(SysPermission::getMenuType, CommonConstant.MENU_TYPE_2);
            List<SysPermission> allAuthList = list(query);
            endTime = System.currentTimeMillis();
            log.info("查询所有权限耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            JSONArray allauthjsonArray = new JSONArray();
            this.getAllAuthJsonArray(allauthjsonArray, allAuthList);

            json.putOpt("menu", menujsonArray);
            json.putOpt("auth", authjsonArray);
            json.putOpt("allAuth", allauthjsonArray);

            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            sysUser.setUserPermission(null);
            JSONObject object = JSONUtil.parseObj(sysUser);
            object.remove("password");

            startTime = System.currentTimeMillis();
            List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(loginUser.getId());
            endTime = System.currentTimeMillis();
            log.info("获取角色信息耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            boolean isExport = false;
            boolean isDownload = false;
            boolean desensitization = false;
            boolean desensitizationVin = false;
            boolean isQuality = false;
            for (SysRole role : sysRoles) {
                if (role.isDownload()) {
                    isDownload = true;
                }
                if (role.isExport()) {
                    isExport = true;
                }
                if (role.getDesensitization()) {
                    desensitization = true;
                }
                if (role.getDesensitizationVin()) {
                    desensitizationVin = true;
                }
            }
            object.putOpt("isExport", isExport);
            object.putOpt("isDownload", isDownload);
            object.putOpt("desensitization", desensitization);
            object.putOpt("desensitizationVin", desensitizationVin);
            object.putOpt("roleName", sysRoles.get(0).getRoleName());
            object.putOpt("roleId", sysRoles.get(0).getId());
            String roleType = sysRoles.get(0).getRoleType();

            if (Objects.nonNull(sysRoles.get(0).getRoleType())) {
                object.putOpt("roleType", sysRoles.get(0).getRoleType());
            } else {
                object.putOpt("roleType", "");
            }

            if (roleType != null && "1".equals(roleType)) {
                object.putOpt("examine", true);
                object.putOpt("handle", true);
            } else {
                startTime = System.currentTimeMillis();
                QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, loginUser.getId());
                object.putOpt("examine", riskAlertReviewerService.count(wrapper) > 0);

                QueryWrapper<VocRiskProcessRecipient> wrappe = new QueryWrapper<>();
                object.putOpt("handle", recipientService.count(wrappe) > 0);
                endTime = System.currentTimeMillis();
                log.info("查询审核处理权限耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
            }

            object.putOpt("isReset", sysUser.getPassResetTime() == null);

            startTime = System.currentTimeMillis();
            object.putOpt("organize", sysDepartService.getDepartByUserId(loginUser.getId()));
            endTime = System.currentTimeMillis();
            log.info("获取部门信息耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
            redisUtil.set(CacheConstant.SYS_USERS_CACHE_INFO +":" + loginUser.getId(),object);
            json.putOpt("userInfo", object);

            startTime = System.currentTimeMillis();
            List<String> pee = sysRolePermissionMapper.listKanBan(loginUser.getId());
            endTime = System.currentTimeMillis();
            log.info("获取看板权限耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

            json.putOpt("permission", pee);
            json.putOpt("brandCodeAuth", new ArrayList<>());

            SysRole sysRole = sysRoles.get(0);
            if (!StringUtils.isEmpty(sysRole.getBrandCode())) {
                List<String> list = Arrays.asList(sysRole.getBrandCode().split(","));
                json.putOpt("brandCodeAuth", list);
                if (list.size() > 1) {
                    startTime = System.currentTimeMillis();
                    QueryWrapper<BrandProductManager> queryBrandProduct = new QueryWrapper<>();
                    queryBrandProduct.lambda().eq(BrandProductManager::getPId, 0);
                    queryBrandProduct.lambda().orderByAsc(BrandProductManager::getSortNo);
                    Page<BrandProductManager> page = new Page<>(0, 100);
                    IPage<BrandProductManager> pageList = brandProductManagerService.page(page, queryBrandProduct);
                    endTime = System.currentTimeMillis();
                    log.info("查询品牌产品耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);

                    List<BrandProductManager> records = pageList.getRecords();
                    log.info("品牌重新排序:{}", JSONUtil.toJsonStr(records));
                    List<String> sortList = new ArrayList<>();
                    for (BrandProductManager brandProductManager : records) {
                        if (list.contains(brandProductManager.getBrandCode())) {
                            sortList.add(brandProductManager.getBrandCode());
                        }
                    }
                    json.putOpt("brandCodeAuth", sortList);
                }
            }
            result.setResult(json);

            long totalEndTime = System.currentTimeMillis();
            log.info("获取用户权限总耗时：{}秒{}毫秒", (totalEndTime - totalStartTime) / 1000, (totalEndTime - totalStartTime) % 1000);

        } catch (Exception e) {
            result.error500("查询失败:" + e.getMessage());
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public Result<?> getUserPermissionByToken1() {
        Result<JSONObject> result = new Result<JSONObject>();
        try {
            //直接获取当前用户不适用前端token
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (StrUtil.isEmptyIfStr(loginUser)) {
                return Result.error("请登录系统！");
            }
            //缓存用户的权限！！！！！！
            cachePermission(loginUser.getId());
            Set<Object> permissionSet = redisUtil.sGet(CacheConstant.SYS_USER_PERMISSION + loginUser.getId());
            List<String> permissionIds = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());

            List<SysPermission> metaList = queryByUser(loginUser.getUsername());
            //添加首页路由
            //update-begin for: TASK #3368 【路由缓存】首页的缓存设置有问题，需要根据后台的路由配置来实现是否缓存
//            if(!hasIndexPage(metaList)){
//                SysPermission indexMenu = this.list(new LambdaQueryWrapper<SysPermission>().eq(SysPermission::getName,"首页")).get(0);
//                metaList.add(0,indexMenu);
//            }
            //update-end for: TASK #3368 【路由缓存】首页的缓存设置有问题，需要根据后台的路由配置来实现是否缓存

            //update-begin- for:自定义首页地址LOWCOD-1578
            List<String> roles = sysUserService.getRole(loginUser.getUsername());
            String compUrl = RoleIndexConfigEnum.getIndexByRoles(roles);
            if (StrUtil.isNotBlank(compUrl)) {
                List<SysPermission> menus = metaList.stream().filter(sysPermission -> "首页".equals(sysPermission.getName())).collect(Collectors.toList());
                if (permissionIds.contains(menus.get(0).getId())) {
                    menus.get(0).setComponent(compUrl);
                }
            }
            //update-end- for：自定义首页地址LOWCOD-1578
            JSONObject json = new JSONObject();
            JSONArray menujsonArray = new JSONArray();
            this.getPermissionJsonArray(menujsonArray, metaList, null, permissionIds, loginUser.getId());

            //一级菜单下的子菜单全部是隐藏路由，则一级菜单不显示
            this.handleFirstLevelMenuHidden(menujsonArray);

            JSONArray authjsonArray = new JSONArray();
            this.getAuthJsonArray(authjsonArray, metaList);
            //查询所有的权限
            LambdaQueryWrapper<SysPermission> query = new LambdaQueryWrapper<SysPermission>();
            query.eq(SysPermission::getDelFlag, CommonConstant.DEL_FLAG_0);
            query.eq(SysPermission::getMenuType, CommonConstant.MENU_TYPE_2);
            //query.eq(SysPermission::getStatus, "1");
            List<SysPermission> allAuthList = list(query);
            JSONArray allauthjsonArray = new JSONArray();
            this.getAllAuthJsonArray(allauthjsonArray, allAuthList);
            //路由菜单
            json.putOpt("menu", menujsonArray);
            //按钮权限（用户拥有的权限集合）
            json.putOpt("auth", authjsonArray);
            //全部权限配置集合（按钮权限，访问权限）
            json.putOpt("allAuth", allauthjsonArray);

            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            sysUser.setUserPermission(null);
            JSONObject object = JSONUtil.parseObj(sysUser);
            object.remove("password");
            List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(loginUser.getId());
            boolean isExport = false;
            boolean isDownload = false;
            boolean isQuality = false;
            for (SysRole role : sysRoles) {
                if (role.isDownload()) {
                    isDownload = true;
                }
                if (role.isExport()) {
                    isExport = true;
                }
            }
            object.putOpt("isExport", isExport);
            object.putOpt("isDownload", isDownload);
            object.putOpt("roleName", sysRoles.get(0).getRoleName());
            object.putOpt("roleId", sysRoles.get(0).getId());
            String roleType = sysRoles.get(0).getRoleType();

            if (Objects.nonNull(sysRoles.get(0).getRoleType())) {
                object.putOpt("roleType", sysRoles.get(0).getRoleType());
            } else {
                object.putOpt("roleType", "");
            }
            //roleType 1为管理层人员；2为执行层人员
            if (roleType != null && "1".equals(roleType)) {
                object.putOpt("examine", true);
                object.putOpt("handle", true);
            } else {
                QueryWrapper<VocRiskAlertReviewer> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(VocRiskAlertReviewer::getReviewerUserId, loginUser.getId());
                object.putOpt("examine", riskAlertReviewerService.count(wrapper) > 0 ? true : false);
                QueryWrapper<VocRiskProcessRecipient> wrappe = new QueryWrapper<>();
                //后期修改
//                wrappe.lambda().eq(VocRiskProcessRecipient::getProcessUserId,loginUser.getId());
                object.putOpt("handle", recipientService.count(wrappe) > 0 ? true : false);
            }

            object.putOpt("isReset", sysUser.getPassResetTime() == null ? true : false);

            // 获取用户部门信息
            object.putOpt("organize", sysDepartService.getDepartByUserId(loginUser.getId()));
            redisUtil.set(CacheConstant.SYS_USERS_CACHE_INFO +":"+ loginUser.getId(), object);
            json.putOpt("userInfo", object);

            List<String> pee = sysRolePermissionMapper.listKanBan(loginUser.getId());
            json.putOpt("permission", pee);
            json.putOpt("brandCodeAuth", new ArrayList<>());
            SysRole sysRole = sysRoles.get(0);
            if (!StringUtils.isEmpty(sysRole.getBrandCode())) {
                List<String> list = Arrays.asList(sysRole.getBrandCode().split(","));
                json.putOpt("brandCodeAuth", list);
                if (list.size() > 1) {
                    QueryWrapper<BrandProductManager> queryBrandProduct = new QueryWrapper<>();
                    queryBrandProduct.lambda().eq(BrandProductManager::getPId, 0);
                    queryBrandProduct.lambda().orderByAsc(BrandProductManager::getSortNo);
                    Page<BrandProductManager> page = new Page<>(0, 100);
                    IPage<BrandProductManager> pageList = brandProductManagerService.page(page, queryBrandProduct);
                    List<BrandProductManager> records = pageList.getRecords();
                    log.info("品牌重新排序:{}", JSONUtil.toJsonStr(records));
                    List<String> sortList = new ArrayList<>();
                    for (BrandProductManager brandProductManager : records) {
                        if (list.contains(brandProductManager.getBrandCode())) {
                            sortList.add(brandProductManager.getBrandCode());
                        }
                    }
                    json.putOpt("brandCodeAuth", sortList);
                }
            }
            result.setResult(json);
        } catch (Exception e) {
            result.error500("查询失败:" + e.getMessage());
            log.error(e.getMessage(), e);
        }
        return result;
    }


    @Autowired
    VocChannelCategoryServiceImpl vocChannelCategoryService;
    @Autowired
    RedisUtil redisUtil;

    private void cachePermission(String userId) {
        ExecutorService executorService = Executors.newWorkStealingPool(2);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 处理业务标签缓存
            CompletableFuture<Void> tagFuture = CompletableFuture.runAsync(() -> {
                long tagStartTime = System.currentTimeMillis();
                QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(VocBusinessTag::getOther, 1);
                List<VocBusinessTag> list = vocBusinessTagMapper.selectList(queryWrapper);

                if (!list.isEmpty()) {
                    redisUtil.del(CacheConstant.SYS_USER_TAG_OTHER);
                    List<String> tagCodes = list.stream()
                            .map(VocBusinessTag::getTagCode)
                            .collect(Collectors.toList());
                    redisUtil.sSetAll2(CacheConstant.SYS_USER_TAG_OTHER, tagCodes);
                }
                long tagEndTime = System.currentTimeMillis();
                log.info("处理业务标签缓存耗时：{}秒{}毫秒", (tagEndTime - tagStartTime) / 1000, (tagEndTime - tagStartTime) % 1000);
            }, executorService);


            // 2. 获取用户角色信息
            long roleStartTime = System.currentTimeMillis();
            List<String> roleIdList = sysUserRoleMapper.getRoleIdByUserId(userId);
            long roleEndTime = System.currentTimeMillis();
            log.info("获取用户角色信息耗时：{}秒{}毫秒", (roleEndTime - roleStartTime) / 1000, (roleEndTime - roleStartTime) % 1000);

            if (roleIdList.isEmpty()) {
                log.warn("用户{}没有关联的角色", userId);
                return;
            }

            // 3. 批量获取角色权限
            Set<SysRolePermission> rolePermissionList = new ConcurrentHashSet<>();
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (String roleId : roleIdList) {
                CompletableFuture<Void> roleFuture = CompletableFuture.runAsync(() -> {
                    long permStartTime = System.currentTimeMillis();

                    // 获取角色权限
                    List<SysRolePermission> permissions = sysRolePermissionMapper.listByRoleId(roleId);
                    rolePermissionList.addAll(permissions);

                    // 获取角色信息
                    SysRole roleByRoleId = sysRoleMapper.getRoleByRoleId(roleId);
                    if (roleByRoleId == null) {
                        log.error("未找到角色信息，roleId: {}", roleId);
                        return;
                    }

                    String brandCode = roleByRoleId.getBrandCode();
                    if (StringUtils.isEmpty(brandCode)) {
                        log.info("角色{}没有关联的品牌代码", roleId);
                        return;
                    }

                    // 处理品牌代码
                    String[] brandCodes = brandCode.split(",");
                    List<CompletableFuture<Void>> brandFutures = new ArrayList<>();
                    String formatKey = String.format(CacheConstant.SYS_USER_BRANDCODE, userId);
                    redisUtil.sSetAllObject(formatKey, Arrays.asList(brandCodes));

                    for (String code : brandCodes) {
                        // 批量处理各类数据
                        CompletableFuture<Void> channelFuture = processChannelData(roleId, code, userId);
                        CompletableFuture<Void> tagFuture1 = processBusinessTagData(roleByRoleId, roleId, code, userId);
                        CompletableFuture<Void> seriesFuture = processSeriesData(roleId, code, userId);
//                        CompletableFuture<Void> areaFuture = processAreaData(roleId, code, userId);
                        CompletableFuture<Void> provinceFuture = processProvinceData(roleId, code, userId);
                        brandFutures.add(channelFuture);
                        brandFutures.add(tagFuture1);
                        brandFutures.add(seriesFuture);
//                        brandFutures.add(areaFuture);
                        brandFutures.add(provinceFuture);

                    }

                    CompletableFuture.allOf(brandFutures.toArray(new CompletableFuture[0])).join();

                    long permEndTime = System.currentTimeMillis();
                    log.info("处理角色{}权限数据耗时：{}秒{}毫秒", roleId, (permEndTime - permStartTime) / 1000, (permEndTime - permStartTime) % 1000);
                }, executorService);

                futures.add(roleFuture);
            }

            // 等待所有异步任务完成
            CompletableFuture.allOf(
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])),
                    tagFuture
            ).join();

            // 4. 更新用户权限缓存
            long cacheStartTime = System.currentTimeMillis();
            if (!rolePermissionList.isEmpty()) {
                String permissionKey = CacheConstant.SYS_USER_PERMISSION + userId;
                redisUtil.del(permissionKey);
                List<String> permissionIds = rolePermissionList.stream()
                        .map(SysRolePermission::getPermissionId)
                        .collect(Collectors.toList());
                redisUtil.sSetAll2(permissionKey, permissionIds);
            }
            long cacheEndTime = System.currentTimeMillis();
            log.info("更新用户权限缓存耗时：{}秒{}毫秒", (cacheEndTime - cacheStartTime) / 1000, (cacheEndTime - cacheStartTime) % 1000);

        } catch (Exception e) {
            log.error("缓存权限数据异常，userId: {}", userId, e);
            throw new RuntimeException("缓存权限数据失败", e);
        } finally {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            long endTime = System.currentTimeMillis();
            log.info("缓存权限总耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
        }
    }

    private CompletableFuture<Void> processChannelData(String roleId, String code, String userId) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<SysRoleChannel> channelList = sysRoleChannelMapper.listByRoleId(roleId, code);
                if (!channelList.isEmpty()) {
                    String userChannel = String.format(CacheConstant.SYS_USER_BRANDCODE_CHANNEL, code, userId);
                    redisUtil.del(userChannel);
                    List<String> channelIds = channelList.stream()
                            .map(SysRoleChannel::getChannelId)
                            .collect(Collectors.toList());
                    redisUtil.sSetAll2(userChannel, channelIds);
                }
            } catch (Exception e) {
                log.error("处理渠道数据异常，roleId: {}, code: {}", roleId, code, e);
                throw e;
            }
            long endTime = System.currentTimeMillis();
            log.info("处理渠道数据耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
        });
    }

    private CompletableFuture<Void> processBusinessTagData(SysRole role, String roleId, String code, String userId) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<SysRoleBusinessTag> tagList = sysRoleBusinessTagMapper.listByRoleId(roleId, code);
                JSONObject jsonObject = JSONUtil.parseObj(role.getQualityText());
                List<String> qTags = new ArrayList<String>();
                if (jsonObject.getBool(code)) {
                    qTags = faultProblemService.getTags2All();
                }
                String userTag = String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, code, userId);
                redisUtil.del(userTag);
                List<String> tagCodes = new ArrayList<>();
                if (!tagList.isEmpty()) {
                    tagCodes = tagList.stream()
                            .map(SysRoleBusinessTag::getTagCode)
                            .collect(Collectors.toList());
//                    redisUtil.sSetAll2(userTag, tagCodes);
                }
                tagCodes.addAll(qTags);
                redisUtil.sSetAll2(userTag, tagCodes);
            } catch (Exception e) {
                log.error("处理业务标签数据异常，roleId: {}, code: {}", roleId, code, e);
                throw e;
            }
            long endTime = System.currentTimeMillis();
            log.info("处理业务标签数据耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
        });
    }

    private CompletableFuture<Void> processSeriesData(String roleId, String code, String userId) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<SysRoleSeries> seriesList = sysRoleSeriesMapper.listByRoleId(roleId, code);
                if (!seriesList.isEmpty()) {
                    String userSeries = String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES, code, userId);
                    redisUtil.del(userSeries);
                    List<String> seriesCodes = seriesList.stream()
                            .map(SysRoleSeries::getCarCode)
                            .collect(Collectors.toList());
                    redisUtil.sSetAll2(userSeries, seriesCodes);
                }
            } catch (Exception e) {
                log.error("处理系列数据异常，roleId: {}, code: {}", roleId, code, e);
                throw e;
            }
            long endTime = System.currentTimeMillis();
            log.info("处理系列数据耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
        });
    }


    private CompletableFuture<Void> processAreaData(String roleId, String code, String userId) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<SysRoleArea> areaList = sysRoleAreaMapper.areaListByRoleId(roleId, code);
                if (!areaList.isEmpty()) {
                    String userArea = String.format(CacheConstant.SYS_USER_BRANDCODE_AREA, code, userId);
                    redisUtil.del(userArea);
                    List<String> areaCodes = areaList.stream()
                            .map(SysRoleArea::getCode)
                            .collect(Collectors.toList());
                    redisUtil.sSetAll2(userArea, areaCodes);
                }
            } catch (Exception e) {
                log.error("处理区域数据异常，roleId: {}, code: {}", roleId, code, e);
                throw e;
            }
            long endTime = System.currentTimeMillis();
            log.info("处理区域数据耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
        });
    }

    private CompletableFuture<Void> processProvinceData(String roleId, String code, String userId) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<SysRoleArea> areaList = sysRoleAreaMapper.provinceListByRoleId(roleId, code);
                if (!areaList.isEmpty()) {
                    String userProvince = String.format(CacheConstant.SYS_USER_BRANDCODE_PROVINCE, code, userId);
                    redisUtil.del(userProvince);
                    List<String> areaProvinces = areaList.stream()
                            .map(SysRoleArea::getCode)
                            .collect(Collectors.toList());
                    redisUtil.sSetAll2(userProvince, areaProvinces);
                }
            } catch (Exception e) {
                log.error("处理省份数据异常，roleId: {}, code: {}", roleId, code, e);
                throw e;
            }
            long endTime = System.currentTimeMillis();
            log.info("处理省份数据耗时：{}秒{}毫秒", (endTime - startTime) / 1000, (endTime - startTime) % 1000);
        });
    }

    private void getAllAuthJsonArray(JSONArray jsonArray, List<SysPermission> allList) {
        JSONObject json = null;
        for (SysPermission permission : allList) {
            json = new JSONObject();
            json.putOpt("action", permission.getPerms());
            json.putOpt("status", permission.getStatus());
            //1显示2禁用
            json.putOpt("type", permission.getPermsType());
            json.putOpt("describe", permission.getName());
            jsonArray.add(json);
        }
    }

    private void getAuthJsonArray(JSONArray jsonArray, List<SysPermission> metaList) {
        for (SysPermission permission : metaList) {
            if (permission.getMenuType() == null) {
                continue;
            }
            JSONObject json;
            if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_2) && CommonConstant.STATUS_1.equals(permission.getStatus())) {
                json = new JSONObject();
                json.putOpt("action", permission.getPerms());
                json.putOpt("type", permission.getPermsType());
                json.putOpt("describe", permission.getName());
                jsonArray.add(json);
            }
        }
    }


    private void handleFirstLevelMenuHidden(JSONArray jsonArray) {
        jsonArray = jsonArray.stream().map(obj -> {
            JSONObject returnObj = new JSONObject();
            JSONObject jsonObj = (JSONObject) obj;
            if (jsonObj.containsKey("children")) {
                JSONArray childrens = jsonObj.getJSONArray("children");
                childrens = childrens.stream().filter(arrObj -> !"true".equals(((JSONObject) arrObj).getStr("hidden"))).collect(Collectors.toCollection(JSONArray::new));
                if (childrens == null || childrens.size() == 0) {
                    jsonObj.putOpt("hidden", true);

                    //vue3版本兼容代码
                    JSONObject meta = new JSONObject();
                    meta.putOpt("hideMenu", true);
                    jsonObj.putOpt("meta", meta);
                }
            }
            return returnObj;
        }).collect(Collectors.toCollection(JSONArray::new));
    }


    private void getPermissionJsonArray(JSONArray jsonArray, List<SysPermission> metaList, JSONObject parentJson, List<String> permissionIds, String userId) {
        for (SysPermission permission : metaList) {
            if (permission.getMenuType() == null) {
                continue;
            }
            if ("1622894412432433154".equals(userId)) {
                permission.setHidden(false);
            }
            else if ("c7d6e2e4e2934f2c9385a623fd98c701".equals(permission.getId())) {
                permission.setHidden(false);
            } else if (!permissionIds.contains(permission.getId())) {
                permission.setHidden(true);
            }

            String tempPid = permission.getParentId();
            JSONObject json = getPermissionJsonObject(permission);
            if (json == null) {
                continue;
            }
            if (parentJson == null && StrUtil.isEmpty(tempPid)) {
                jsonArray.add(json);
                if (!permission.isLeaf()) {
                    getPermissionJsonArray(jsonArray, metaList, json, permissionIds, userId);
                }
            } else if (parentJson != null && StrUtil.isNotEmpty(tempPid) && tempPid.equals(parentJson.getStr("id"))) {
                // 类型( 0：一级菜单 1：子菜单 2：按钮 )
                if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_2)) {
                    JSONObject metaJson = parentJson.getJSONObject("meta");
                    if (metaJson.containsKey("permissionList")) {
                        metaJson.getJSONArray("permissionList").add(json);
                    } else {
                        JSONArray permissionList = new JSONArray();
                        permissionList.add(json);
                        metaJson.putOpt("permissionList", permissionList);
                    }
                    // 类型( 0：一级菜单 1：子菜单 2：按钮 )
                } else if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_1) || permission.getMenuType().equals(CommonConstant.MENU_TYPE_0)) {
                    if (parentJson.containsKey("children")) {
                        parentJson.getJSONArray("children").add(json);
                    } else {
                        JSONArray children = new JSONArray();
                        children.add(json);
                        parentJson.putOpt("children", children);
                    }

                    if (!permission.isLeaf()) {
                        getPermissionJsonArray(jsonArray, metaList, json, permissionIds, userId);
                    }
                }
            }

        }
    }

    private JSONObject getPermissionJsonObject(SysPermission permission) {
        JSONObject json = new JSONObject();
        // 类型(0：一级菜单 1：子菜单 2：按钮)
        if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_2)) {
            //json.put("action", permission.getPerms());
            //json.put("type", permission.getPermsType());
            //json.put("describe", permission.getName());
            return null;
        } else if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_0) || permission.getMenuType().equals(CommonConstant.MENU_TYPE_1)) {
            json.putOpt("id", permission.getId());
            if (permission.isRoute()) {
                json.putOpt("route", "1");// 表示生成路由
            } else {
                json.putOpt("route", "0");// 表示不生成路由
            }

            if (isWWWHttpUrl(permission.getUrl())) {
                json.putOpt("path", MD5Util.MD5Encode(permission.getUrl(), "utf-8"));
            } else if (!permission.isHidden()) {
                json.putOpt("path", permission.getUrl());
            }
            json.putOpt("menuI18n", permission.getMenuI18n());
            // 重要规则：路由name (通过URL生成路由name,路由name供前端开发，页面跳转使用)
            if (StrUtil.isNotEmpty(permission.getComponentName())) {
                json.putOpt("name", permission.getComponentName());
            } else {
                json.putOpt("name", urlToRouteName(permission.getUrl()));
            }

            JSONObject meta = new JSONObject();
            // 是否隐藏路由，默认都是显示的
            if (permission.isHidden()) {
                json.putOpt("hidden", true);
                //vue3版本兼容代码
                json.putOpt("hideMenu", true);
            }
            // 聚合路由
            if (permission.isAlwaysShow()) {
                json.putOpt("alwaysShow", true);
            }
            json.putOpt("component", permission.getComponent());
            // 由用户设置是否缓存页面 用布尔值
            if (permission.isKeepAlive()) {
                json.putOpt("keepAlive", true);
            } else {
                json.putOpt("keepAlive", false);
            }

            /*update_begin  for:往菜单信息里添加外链菜单打开方式 */
            //外链菜单打开方式
            if (permission.isInternalOrExternal()) {
                json.putOpt("internalOrExternal", true);
            } else {
                json.putOpt("internalOrExternal", false);
            }
            /* update_end author: date:20190908 for: 往菜单信息里添加外链菜单打开方式*/

            json.putOpt("title", permission.getName());

            //update-begin- for：路由缓存问题，关闭了tab页时再打开就不刷新 #842
            String component = permission.getComponent();
            if (StrUtil.isNotEmpty(permission.getComponentName()) || StrUtil.isNotEmpty(component)) {
                json.putOpt("componentName", getString(permission.getComponentName(), component.substring(component.lastIndexOf("/") + 1)));
            }
            //update-end- for：路由缓存问题，关闭了tab页时再打开就不刷新 #842
            json.putOpt("redirect", permission.getRedirect());
            json.putOpt("icon", permission.getIcon());
/*            if (StrUtil.isEmpty(permission.getParentId())) {
                // 一级菜单跳转地址
                json.putOpt("redirect", permission.getRedirect());
                if (StrUtil.isNotEmpty(permission.getIcon())) {
                    json.putOpt("icon", permission.getIcon());
                }
            } else {
                if (StrUtil.isNotEmpty(permission.getIcon())) {
                    json.putOpt("icon", permission.getIcon());
                }
            }*/
            if (isWWWHttpUrl(permission.getUrl())) {
                json.putOpt("url", permission.getUrl());
            }
            // update-begin- for：新增适配vue3项目的隐藏tab功能
            if (permission.isHideTab()) {
                json.putOpt("hideTab", true);
            }
            // update-end- for：新增适配vue3项目的隐藏tab功能
            json.putOpt("meta", meta);
        }

        return json;
    }

    private boolean isWWWHttpUrl(String url) {
        if (url != null && (url.startsWith("http://") || url.startsWith("https://") || url.startsWith("{{"))) {
            return true;
        }
        return false;
    }

    private String urlToRouteName(String url) {
        if (StrUtil.isNotEmpty(url)) {
            if (url.startsWith("/")) {
                url = url.substring(1);
            }
            url = url.replace("/", "-");

            // 特殊标记
            url = url.replace(":", "@");
            return url;
        } else {
            return null;
        }
    }

    public String getString(String s, String defval) {
        if (StrUtil.isEmpty(s)) {
            return (defval);
        }
        return (s.trim());
    }

    @Override
    public List<SysPermission> queryByUser(String username) {
        return baseMapper.queryByUser(username);
    }
}
