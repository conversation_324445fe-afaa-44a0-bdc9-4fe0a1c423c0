package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.*;
import com.car.voc.mapper.*;
import com.car.voc.model.SysDepartModel;
import com.car.voc.service.CommonService;
import com.car.voc.service.ISysDepartService;
import com.car.voc.vo.DictVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 部门表 服务实现类
 * <p>
 */
@Service
public class SysDepartServiceImpl extends ServiceImpl<SysDepartMapper, SysDepart> implements ISysDepartService {

    @Autowired
    private SysDepartRoleMapper sysDepartRoleMapper;
    @Autowired
    private SysUserDepartMapper userDepartMapper;
    @Autowired
    private SysDepartPermissionMapper departPermissionMapper;
    @Autowired
    private SysDepartRolePermissionMapper departRolePermissionMapper;
    @Autowired
    private SysDepartRoleUserMapper departRoleUserMapper;


    @Override
    public Result<SysDepart> add(SysDepartModel sysDepartModel, HttpServletRequest request) {
        Result<SysDepart> result = new Result<SysDepart>();
        String username = CommonService.getUserNameByToken(request);
        SysDepart sysDepart = new SysDepart();
        BeanUtil.copyProperties(sysDepartModel, sysDepart);
        if (CollectionUtils.isNotEmpty(sysDepartModel.getBrandCodeList())) {
            String join = String.join(",", sysDepartModel.getBrandCodeList());
            sysDepart.setBrandCode(join);
        }
        try {
            sysDepart.setCreateBy(username);
            saveDepartData(sysDepart, username);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @Override
    public boolean delete(String id) {
        List<String> idList = new ArrayList<>();
        idList.add(id);
        this.checkChildrenExists(id, idList);
        //清空部门树内存
        //FindsDepartsChildrenUtil.clearDepartIdModel();
        boolean ok = this.removeByIds(idList);
        //根据部门id获取部门角色id
        List<String> roleIdList = new ArrayList<>();
        LambdaQueryWrapper<SysDepartRole> query = new LambdaQueryWrapper<>();
        query.select(SysDepartRole::getId).in(SysDepartRole::getDepartId, idList);
        List<SysDepartRole> depRoleList = sysDepartRoleMapper.selectList(query);
        for (SysDepartRole deptRole : depRoleList) {
            roleIdList.add(deptRole.getId());
        }
        //根据部门id删除用户与部门关系
        userDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getDepId, idList));
        //根据部门id删除部门授权
        departPermissionMapper.delete(new LambdaQueryWrapper<SysDepartPermission>().in(SysDepartPermission::getDepartId, idList));
        //根据部门id删除部门角色
        sysDepartRoleMapper.delete(new LambdaQueryWrapper<SysDepartRole>().in(SysDepartRole::getDepartId, idList));
        if (roleIdList != null && roleIdList.size() > 0) {
            //根据角色id删除部门角色授权
            departRolePermissionMapper.delete(new LambdaQueryWrapper<SysDepartRolePermission>().in(SysDepartRolePermission::getRoleId, roleIdList));
            //根据角色id删除部门角色用户信息
            departRoleUserMapper.delete(new LambdaQueryWrapper<SysDepartRoleUser>().in(SysDepartRoleUser::getDroleId, roleIdList));
        }
        return ok;
    }

    @Override
    public SysDepart getDepartByUserId(String userId) {
        return baseMapper.getDepartByUserId(userId);
    }

    /**
     * delete 方法调用
     *
     * @param id
     * @param idList
     */
    private void checkChildrenExists(String id, List<String> idList) {
        LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
        query.eq(SysDepart::getParentId, id);
        List<SysDepart> departList = this.list(query);
        if (departList != null && departList.size() > 0) {
            for (SysDepart depart : departList) {
                idList.add(depart.getId());
                this.checkChildrenExists(depart.getId(), idList);
            }
        }
    }

    /**
     * saveDepartData 对应 add 保存用户在页面添加的新的部门对象数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDepartData(SysDepart sysDepart, String username) {
        if (sysDepart != null && username != null) {
            if (sysDepart.getParentId() == null) {
                sysDepart.setParentId("");
            }
            String s = UUID.randomUUID().toString().replace("-", "");
            sysDepart.setId(s);
            // 先判断该对象有无父级ID,有则意味着不是最高级,否则意味着是最高级
            // 获取父级ID
            String parentId = sysDepart.getParentId();
            //update-begin- for：部门编码规则生成器做成公用配置
            JSONObject formData = new JSONObject();
            formData.putOpt("parentId", parentId);
//			String[] codeArray = (String[]) FillRuleUtil.executeRule(FillRuleConstant.DEPART,formData);
            //update-end- for：部门编码规则生成器做成公用配置
//			sysDepart.setOrgCode(codeArray[0]);
//			String orgType = codeArray[1];
            sysDepart.setOrgType("1");
//			sysDepart.setStatus(true);
            sysDepart.setCreateTime(new Date());
            sysDepart.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
            this.save(sysDepart);

        }

    }

    @Override
    @Transactional
    public boolean updateDepartDataById(SysDepart sysDepart, String username) {
        if (sysDepart != null && username != null) {
            sysDepart.setUpdateTime(new Date());
            sysDepart.setUpdateBy(username);
            this.updateById(sysDepart);
            return true;
        } else {
            return false;
        }

    }

    /**
     * 构建部门树
     */
    @Override
    public List<DictVo> buildDepartTree(String brandCode) {
        List<Map<String, Object>> rawData = this.baseMapper.getDepartWithUsers(brandCode);
        Map<String, DictVo> departMap = new HashMap<>();

        // 构建部门节点
        for (Map<String, Object> row : rawData) {
            String departId = row.get("depart_id").toString();
            if (!departMap.containsKey(departId)) {
                DictVo depart = new DictVo();
                depart.setValue(departId);
                depart.setText(row.get("depart_name").toString());
                depart.setChildes(new ArrayList<>());
                departMap.put(departId, depart);
            }

            // 添加用户节点
            if (row.get("user_id") != null) {
                DictVo user = new DictVo();
                user.setValue(row.get("user_id").toString());
                user.setText(row.get("user_name").toString());
                List<DictVo> childes = (List<DictVo>) departMap.get(departId).getChildes();
                childes.add(user);
            }
        }
        return new ArrayList<>(departMap.values());
    }


}
