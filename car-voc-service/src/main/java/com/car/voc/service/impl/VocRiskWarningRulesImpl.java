package com.car.voc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.mapper.VocRiskWarningRulesDetailedMapper;
import com.car.voc.mapper.VocRiskWarningRulesMapper;
import com.car.voc.service.IVocRiskWarningRulesDetailedService;
import com.car.voc.service.IVocRiskWarningRulesService;
import com.car.voc.vo.risk.RiskRuleVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName VocRiskWarningRulesImpl.java
 * @Description TODO
 * @createTime 2023年02月06日 10:34
 * @Copyright voc
 */
@Service
public class VocRiskWarningRulesImpl extends ServiceImpl<VocRiskWarningRulesMapper, VocRiskWarningRules> implements IVocRiskWarningRulesService {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IVocRiskWarningRulesDetailedService detailedService;

    @Autowired
    private VocRiskWarningRulesMapper warningRulesMapper;

    @Autowired
    private VocRiskWarningRulesDetailedMapper warningRulesDetailedMapper;

    @Override
    public Result<?> getWarningRules(VocRiskWarningRules warningRules) {
        List<VocRiskWarningRulesNew> rules = warningRulesMapper.selectRuleList(warningRules.getBrandCode());
        List<String> riskWarningRulesId = rules.stream().map(VocRiskWarningRulesNew::getId).collect(Collectors.toList());
        QueryWrapper<VocRiskWarningRulesDetailed> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(VocRiskWarningRulesDetailed::getWarnRuleId, riskWarningRulesId)
                .orderByAsc(VocRiskWarningRulesDetailed::getSort);
        List<VocRiskWarningRulesDetailed> rulsAllList = detailedService.list(wrapper);
        Map<String, List<VocRiskWarningRulesDetailed>> detailMap = rulsAllList.stream().collect(Collectors.groupingBy(VocRiskWarningRulesDetailed::getWarnRuleId));
        rules.forEach(e -> {
            List<VocRiskWarningRulesDetailed> rulsList = detailMap.get(e.getId());
            rulsList.sort(Comparator.comparing(VocRiskWarningRulesDetailed::getType, Comparator.nullsFirst(Integer::compareTo)).reversed());
            List<VocRiskWarningRulesDetailedInsightCycle> insightCycleList = new ArrayList<>();
            List<VocRiskWarningRulesDetailedRiskLevel> riskLevelList = new ArrayList<>();
            for (VocRiskWarningRulesDetailed rule : rulsList) {
                if (rule.getType() == 1) {
                    VocRiskWarningRulesDetailedInsightCycle insightCycle = new VocRiskWarningRulesDetailedInsightCycle();
                    BeanUtils.copyProperties(rule, insightCycle);
                    insightCycleList.add(insightCycle);
                } else {
                    VocRiskWarningRulesDetailedRiskLevel riskLevel = new VocRiskWarningRulesDetailedRiskLevel();
                    BeanUtils.copyProperties(rule, riskLevel);
                    riskLevelList.add(riskLevel);
                }
            }
            e.setInsightCycle(insightCycleList);
            e.setRiskLevel(riskLevelList);
        });
        rules.sort(Comparator.comparing(VocRiskWarningRulesNew::getOrderSort, Comparator.nullsFirst(Integer::compareTo)));
        return Result.OK(rules);
    }

    @Override
    public Result<?> saveOrUpdate1(VocRiskWarningRules warningRules) {
        VocRiskWarningRules warningRulesOld = new VocRiskWarningRules();
        warningRulesOld = warningRules;
        this.saveOrUpdate(warningRules);

        List<VocRiskWarningRulesDetailed> rulesDetailedsList = new ArrayList<>();

        List<VocRiskWarningRulesDetailedInsightCycle> insightCycleList = warningRules.getInsightCycle();
        for (VocRiskWarningRulesDetailedInsightCycle insightCycle : insightCycleList) {
            VocRiskWarningRulesDetailed riskWarningRulesDetailed = new VocRiskWarningRulesDetailed();
            BeanUtils.copyProperties(insightCycle, riskWarningRulesDetailed);
            rulesDetailedsList.add(riskWarningRulesDetailed);
        }

        List<VocRiskWarningRulesDetailedRiskLevel> riskLevelList = warningRules.getRiskLevel();
        for (VocRiskWarningRulesDetailedRiskLevel riskLevel : riskLevelList) {
            VocRiskWarningRulesDetailed riskWarningRulesDetailed = new VocRiskWarningRulesDetailed();
            BeanUtils.copyProperties(riskLevel, riskWarningRulesDetailed);
            rulesDetailedsList.add(riskWarningRulesDetailed);
        }


        warningRules.setRulesDetaileds(rulesDetailedsList);


        warningRules.getRulesDetaileds().forEach(e -> e.setWarnRuleId(warningRules.getId()));
        RiskRuleVo rulevo = new RiskRuleVo();
        for (VocRiskWarningRulesDetailed rd : warningRules.getRulesDetaileds()) {
            if (ObjectUtils.isNotEmpty(rd.getInsightCycle())) {
                rulevo.setNumAll(rd);
            }
        }
        redisUtil.set(CacheConstant.SYS_RISK_RULE + warningRules.getId(), rulevo);
        detailedService.saveOrUpdateBatch(warningRules.getRulesDetaileds());
        warningRulesOld.setRulesDetaileds(null);
        return Result.OK(warningRulesOld);
    }

    @Override
    public RiskRuleVo getRiskEmotionRule(String id) {
        RiskRuleVo vo = (RiskRuleVo) redisUtil.get(CacheConstant.SYS_RISK_RULE + id);
        if (vo == null) {
            QueryWrapper<VocRiskWarningRulesDetailed> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(VocRiskWarningRulesDetailed::getWarnRuleId, id)
                    .eq(VocRiskWarningRulesDetailed::getType, 1);
            List<VocRiskWarningRulesDetailed> rsk = detailedService.list(wrapper);
            RiskRuleVo rulevo = new RiskRuleVo();
            for (VocRiskWarningRulesDetailed rd : rsk) {
                if (Objects.nonNull(rd)) {
                    rulevo.setNumAll(rd);
                }
            }
            redisUtil.set(CacheConstant.SYS_RISK_RULE + id, rulevo);
            return rulevo;
        } else {
            return vo;
        }
    }

    @Override
    public String getWarnRuleDetailListByIdBrandCode(String riskType, String brandCode, BigDecimal riskIndex) {
//        riskType = "风险事件洞察";
//        brandCode = "A12";
//        riskIndex = BigDecimal.valueOf(30);
        String riskLevel = warningRulesMapper.selectRuleDetailList(riskType, brandCode, riskIndex);
        return riskLevel;
    }

    @Override
    public List<VocRiskWarningRules> getRiskType(String riskType) {
        return baseMapper.getRiskType(riskType);
    }

    @Override
    public VocRiskWarningRules getByBrandCode(String brandCode, String riskType) {
        QueryWrapper<VocRiskWarningRules> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskWarningRules::getBrandCode, brandCode)
                .eq(VocRiskWarningRules::getRiskType, riskType);

        return baseMapper.selectOne(wrapper);
    }


}
