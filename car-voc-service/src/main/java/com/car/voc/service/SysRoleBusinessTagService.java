package com.car.voc.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.SysRoleBusinessTag;
import com.car.voc.vo.risk.TagRoleVo;

import java.util.List;

/**
*
*/
public interface SysRoleBusinessTagService extends IService<SysRoleBusinessTag> {

    void saveOrUpdateData(String roleId, List<String> tagIds,String brandCode);

    List<TagRoleVo> queryRoleNameByTagCode(String[] strings);
}
