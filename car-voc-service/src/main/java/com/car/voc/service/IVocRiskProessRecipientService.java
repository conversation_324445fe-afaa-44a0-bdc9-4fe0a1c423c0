package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.VocRiskProcessRecipient;
import com.car.voc.entity.VocRiskProcessRecipientBase;
import com.car.voc.entity.VocRiskProcessRecipientTag;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName IVocRiskProessRecipientService.java
 * @Description TODO
 * @createTime 2023年02月06日 11:17
 * @Copyright voc
 */
public interface IVocRiskProessRecipientService extends IService<VocRiskProcessRecipient> {

    List<VocRiskProcessRecipientTag> selectByProessRecipientTag(String id);

    List<VocRiskProcessRecipientBase> selectByProcessRecipientBase(String id);

    Integer delByProessRecipientTag(String id);

    Integer delByProessRecipientQualityTag(String id);

    List<VocRiskProcessRecipientTag> queryPermissionByUserId(String userId);

    List<VocRiskProcessRecipientBase> queryPermissionQualityByUserId(String userId);

    List<VocRiskProcessRecipientTag> queryHandlePermissionByUserId(String userId);

    List<VocRiskProcessRecipientBase> queryHandlePermissionQualityByUserId(String userId);

    List<VocRiskProcessRecipientTag> queryBrandTagByReId(String id);

    void saveBatchQualityTags(List<VocRiskProcessRecipientBase> qualityTags);

    void saveBatchRecipientTags(List<VocRiskProcessRecipientTag> recipientTags);
}
