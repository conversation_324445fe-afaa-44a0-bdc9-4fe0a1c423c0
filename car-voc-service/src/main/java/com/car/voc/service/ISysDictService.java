package com.car.voc.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.SysDict;
import com.car.voc.entity.SysDictCc;
import com.car.voc.entity.SysDictItem;
import com.car.voc.model.TreeSelectModel;
import com.car.voc.vo.DictVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 *
 * @since 2018-12-28
 */
public interface ISysDictService extends IService<SysDict> {

    public List<DictVo> queryDictItemsByCode(String code);

    public Map<String,List<DictVo>> queryAllDictItems();

    @Deprecated
    List<DictVo> queryTableDictItemsByCode(String table, String text, String code);

    @Deprecated
	public List<DictVo> queryTableDictItemsByCodeAndFilter(String table, String text, String code, String filterSql);

    public String queryDictTextByKey(String code, String key);
    public String queryDictValueByKey(String code, String key);

    @Deprecated
	String queryTableDictTextByKey(String table, String text, String code, String key);

	@Deprecated
	List<String> queryTableDictByKeys(String table, String text, String code, String keys);

    /**
     * 根据字典类型删除关联表中其对应的数据
     *
     * @param sysDict
     * @return
     */
    boolean deleteByDictId(SysDict sysDict);

    /**
     * 添加一对多
     */
    public Integer saveMain(SysDict sysDict, List<SysDictItem> sysDictItemList);

    /**
	 * 查询所有部门 作为字典信息 id -->value,departName -->text
	 * @return
	 */
	public List<DictVo> queryAllDepartBackDictModel();

	/**
	 * 查询所有用户  作为字典信息 username -->value,realname -->text
	 * @return
	 */
	public List<DictVo> queryAllUserBackDictModel();

	/**
	 * 通过关键字查询字典表
	 * @param table
	 * @param text
	 * @param code
	 * @param keyword
	 * @return
	 */
	@Deprecated
	public List<DictVo> queryTableDictItems(String table, String text, String code, String keyword);

	/**
	  * 根据表名、显示字段名、存储字段名 查询树
	 * @param table
	 * @param text
	 * @param code
	 * @param pidField
	 * @param pid
	 * @param hasChildField
	 * @return
	 */
	@Deprecated
	List<TreeSelectModel> queryTreeList(Map<String, String> query, String table, String text, String flag, String code, String pidField, String pid, String hasChildField);

	/**
	 * 真实删除
	 * @param id
	 */
	public void deleteOneDictPhysically(String id);

	/**
	 * 修改delFlag
	 * @param delFlag
	 * @param id
	 */
	public void updateDictDelFlag(int delFlag,String id);

	/**
	 * 查询被逻辑删除的数据
	 * @return
	 */
	public List<SysDict> queryDeleteList();


	List<DictVo> queryDictItemsLanguageByCode(String all_users);

	List<SysDictItem> findAll();

	List<SysDictCc> getCcList(List<String> groupIds);
}
