package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.entity.ProvinceArea;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.NewDictVo;

import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName ProvinceAreaService.java
 * @Description TODO
 * @createTime 2022年10月21日 16:52
 * @Copyright voc
 */
public interface IProvinceAreaService extends IService<ProvinceArea> {
    List<DictVo> queryProvinceByAreaCode(String value,String brandCode);
    String queryAreaByProvince(String province);

    List<NewDictVo> newQueryProvinceByAreaCode(String brandCode);

    Map<String, String> queryAreaByBrandProvince(String brand);
}
