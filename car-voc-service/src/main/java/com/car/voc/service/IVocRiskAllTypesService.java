package com.car.voc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.DateStiticVo;
import com.car.stats.vo.TrendChannelRiskVo;
import com.car.stats.vo.TrendChannelVo;
import com.car.stats.vo.risk.DataDryingContrastVo;
import com.car.stats.vo.risk.EmotionIntentionVo;
import com.car.stats.vo.risk.RiskDataDryingVo;
import com.car.voc.common.Result;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.entity.VocRiskHandlingRecord;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.vo.FaultProblemTreeVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.risk.RiskAllTypesVo;
import com.car.voc.vo.risk.RiskBrandCodeVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @ClassName IVocRiskWarningRecordService.java
 * @Description TODO
 * @createTime 2023年02月07日 11:29
 * @Copyright voc
 */
public interface IVocRiskAllTypesService extends IService<VocRiskAllTypes> {
    IPage<RiskAllTypesVo> auditList(Page<RiskAllTypesVo> page, RiskAllTypesModel model);

    IPage<RiskAllTypesVo> riskList(Page<RiskAllTypesVo> page, RiskAllTypesModel model);
    IPage<RiskAllTypesVo> riskList1(Page<RiskAllTypesVo> page, RiskAllTypesModel model);

    List<RiskAllTypesVo> riskRecordDetails(String id);

    Result<?> confirmReview(RiskAllTypesModel model);

    Result<?> confirmHandle(VocRiskHandlingRecord model);

    RiskDataDryingVo dataDrying(String id);

    List<EmotionIntentionVo> changeEmotionIntention(String id);

    List<TrendChannelRiskVo> changeChannel(String id);

    Map<String, Object> hotWords(String id);

    BigDecimal getToBeReviewed(FilterCriteriaModel model);

    Map<String, DataDryingContrastVo> dataDryingContrast(String id);

    List<DateStiticVo> trendDate(String id);

    Object riskMailTextContent(String id);

    List<Map<String, Integer>> riskLevelDis(FilterCriteriaModel model);

    List<Map> riskDepsList(FilterCriteriaModel model);

    Map<String, Object> processingProgress(FilterCriteriaModel model);

    List<RiskAllTypesVo> taskListTop(FilterCriteriaModel model);

    Result<?> confirmProcessing(VocRiskHandlingRecord model);

    Result<?> confirmCancel(VocRiskHandlingRecord model);

    List<RiskBrandCodeVo> getRiskBrandCodes();

    List<FaultProblemTreeVo> riskAllQuality(String brandCode);

    List<VocBusinessTagVo> riskAllTopics(String brandCode);

    IPage<RiskAllTypesVo> taskProcessingList(RiskAllTypesModel model);

    List<RiskAllTypesVo> problemOverviewLevel(FilterCriteriaModel model);

    List<RiskAllTypesVo> problemOverviewDepart(FilterCriteriaModel model);

    RiskAllTypesVo processingProgress2(FilterCriteriaModel model);
}
