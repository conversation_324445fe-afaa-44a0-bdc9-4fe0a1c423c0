package com.car.voc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.SysPermission;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName ISysPermissionService.java
 * @createTime 2022年09月13日 11:53
 * @Copyright voc
 */
public interface ISysPermissionService extends IService<SysPermission> {
    Result<?> getUserPermissionByToken();
    public List<SysPermission> queryByUser(String username);

}
