package com.car.voc.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.util.HmacSignUtil;
import com.car.voc.entity.SysSendMessageLog;
import com.car.voc.mapper.SendMessageLogMapper;
import com.car.voc.service.ISendMessageService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.MalformedURLException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date ：Created in 2023/12/20
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@Slf4j
@Service
public class SendMessageServiceImpl extends ServiceImpl<SendMessageLogMapper, SysSendMessageLog> implements ISendMessageService {




    @Value("${configuration.dingTalk.appKey}")
    private String appKey;
    @Value("${configuration.dingTalk.appSecret}")
    private String appSecret;
    @Value("${configuration.dingTalk.workNoticesUrl}")
    private String workNoticesUrl;



    //短信账号
    @Value("${configuration.msg.loginName}")
    private   String  loginName ;
    //短信账号
    @Value("${configuration.msg.spCode}")
    private   String  spCode ;
    //短信账号密码
    @Value("${configuration.msg.password}")
    private  String password ;
    //短信Url
    @Value("${configuration.msg.msgUrl}")
    private  String msgUrl ;





    @Override
    public boolean sendDingtalk(String userName, String title, String table, String last) {

        // 利用开放平台工具包获取签名
        Map<String, String> headMaps = null;
        try {
            headMaps = HmacSignUtil.createSignHeader(appKey, appSecret, workNoticesUrl, "POST");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        //创建okhttp客户端
        OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(5L, TimeUnit.SECONDS)
                .readTimeout(3L, TimeUnit.SECONDS).build();
        //设置请求头,http传输方式为json
        MediaType mediaType = MediaType.parse("application/json");
        JSONObject mass=new JSONObject();
        mass.putOpt("userList", ArrayUtil.addAll(new String[]{userName}));
//        mass.putOpt("userList", ArrayUtil.addAll(new String[]{"1100011000","1100011001","1100011002"}));

        mass.putOpt("messagetype",0);
        mass.putOpt("msgtype",6);
        mass.putOpt("title","VOC 风险警示－1个风险点");
        mass.putOpt("text",title+table+last);
        RequestBody body = RequestBody.create(mediaType, mass.toString());
        SysSendMessageLog massageLog=new SysSendMessageLog(1,1,userName,title+table+last,mass.toString(),workNoticesUrl);
        this.save(massageLog);
        //添加请求头参数
        Request request = new Request.Builder()
                //请求地址
                .url(workNoticesUrl)
                //请求方法
                .method("POST", body)
                //添加开放平台应用key
                .addHeader("X-HMAC-ACCESS-KEY", headMaps.get("X-HMAC-ACCESS-KEY"))
                //添加开放签名算法
                .addHeader("X-HMAC-ALGORITHM", headMaps.get("X-HMAC-ALGORITHM"))
                //添加开放请求时间
                .addHeader("Date", headMaps.get("Date"))
                //添加开放签名
                .addHeader("X-HMAC-SIGNATURE", headMaps.get("X-HMAC-SIGNATURE"))
                .addHeader("Content-Type", "application/json")
                .build();
        //执行请求
        Response response = null;
        UpdateWrapper<SysSendMessageLog> updateWrapper=new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysSendMessageLog::getId,massageLog.getId());
        try {
            response = client.newCall(request).execute();
        } catch (Exception e) {
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,e.getMessage());
            this.update(updateWrapper);
            e.printStackTrace();
        }


        log.debug("钉钉信息：response===" + response);
        String returnBody = null;
        try {
            returnBody = response.body().string();
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,returnBody);
            this.update(updateWrapper);
        } catch (IOException e) {
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,e.getMessage());
            this.update(updateWrapper);
            e.printStackTrace();
        }
        log.debug("returnBody===" + returnBody);
        return true;
    }

    @Override
    public boolean sendAuditDingtalk(String userName, String title, String table, String last) {
        // 利用开放平台工具包获取签名
        Map<String, String> headMaps = null;
        try {
            headMaps = HmacSignUtil.createSignHeader(appKey, appSecret, workNoticesUrl, "POST");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        //创建okhttp客户端
        OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(5L, TimeUnit.SECONDS)
                .readTimeout(3L, TimeUnit.SECONDS).build();
        //设置请求头,http传输方式为json
        MediaType mediaType = MediaType.parse("application/json");
        JSONObject mass=new JSONObject();
        mass.putOpt("userList", ArrayUtil.addAll(new String[]{userName}));
//        mass.putOpt("userList", ArrayUtil.addAll(new String[]{"1100011000","1100011001","1100011002"}));
        mass.putOpt("messagetype",0);
        mass.putOpt("msgtype",6);
        mass.putOpt("title","VOC 风险警示－审核提醒");
        mass.putOpt("text",title+table+last);
        RequestBody body = RequestBody.create(mediaType, mass.toString());
        SysSendMessageLog massageLog=new SysSendMessageLog(1,3,userName,title+table+last,mass.toString(),workNoticesUrl);
        this.save(massageLog);
        //添加请求头参数
        Request request = new Request.Builder()
                //请求地址
                .url(workNoticesUrl)
                //请求方法
                .method("POST", body)
                //添加开放平台应用key
                .addHeader("X-HMAC-ACCESS-KEY", headMaps.get("X-HMAC-ACCESS-KEY"))
                //添加开放签名算法
                .addHeader("X-HMAC-ALGORITHM", headMaps.get("X-HMAC-ALGORITHM"))
                //添加开放请求时间
                .addHeader("Date", headMaps.get("Date"))
                //添加开放签名
                .addHeader("X-HMAC-SIGNATURE", headMaps.get("X-HMAC-SIGNATURE"))
                .addHeader("Content-Type", "application/json")
                .build();
        //执行请求
        Response response = null;
        UpdateWrapper<SysSendMessageLog> updateWrapper=new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysSendMessageLog::getId,massageLog.getId());
        try {
            response = client.newCall(request).execute();
        } catch (Exception e) {
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,e.getMessage());
            this.update(updateWrapper);
            e.printStackTrace();
        }

        log.debug("钉钉信息：response===" + response);
        String returnBody = null;
        try {
            returnBody = response.body().string();
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,returnBody);
            this.update(updateWrapper);
        } catch (IOException e) {
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,e.getMessage());
            this.update(updateWrapper);
            e.printStackTrace();
        }
        log.debug("returnBody===" + returnBody);
        return false;
    }

    @Override
    public boolean sendMessage(String phones,String content){
        SysSendMessageLog massageLog=new SysSendMessageLog();
        UpdateWrapper<SysSendMessageLog> updateWrapper=new UpdateWrapper<>();
        String re;
        try {
            JSONObject text=new JSONObject();
            text.putOpt("account",loginName);
            text.putOpt("password",password);
            text.putOpt("phones",phones);
            text.putOpt("content",content);
            // 将内容转换为GBK编码
            String gbkContent = new String(content.getBytes("GBK"), "GBK");
            // 构建请求参数
//            String requestBody = String.format("SpCode=%s&LoginName=%s&Password=%s&MessageContent=%s&UserNumber=%s&templateId=1",
            String requestBody = String.format("SpCode=%s&LoginName=%s&Password=%s&MessageContent=%s&UserNumber=%s",
                    spCode, loginName, password, gbkContent, phones);
            text.putOpt("urlParm",requestBody);
            massageLog=new SysSendMessageLog(2,2,phones,content,text.toString(),msgUrl);
            this.save(massageLog);
            re = PostToMsgRequest(requestBody, updateWrapper, massageLog);
            if (re == null) return false;
        }catch (Exception e){
            updateWrapper.lambda().eq(SysSendMessageLog::getId,massageLog.getId());
            updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,e.getMessage());
            this.update(updateWrapper);
            log.error("短信发送失败:");
                e.printStackTrace();
                return false;
            }
        updateWrapper.lambda().eq(SysSendMessageLog::getId,massageLog.getId());
        updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,re);
            this.update(updateWrapper);
        return true;
    }

    private @Nullable String PostToMsgRequest(String requestBody, UpdateWrapper<SysSendMessageLog> updateWrapper, SysSendMessageLog massageLog) throws IOException {
        String re;
        // 创建OkHttp客户端
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url(msgUrl)
                .post(RequestBody.create(
                        MediaType.parse("application/x-www-form-urlencoded;charset=GBK"),
                        requestBody))
                .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=GBK")
                .build();

        // 执行请求
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("发送短信失败: " + response);
                updateWrapper.lambda().eq(SysSendMessageLog::getId, massageLog.getId());
                updateWrapper.lambda().set(SysSendMessageLog::getResponseParam,response.message());
                this.update(updateWrapper);
                log.error("短信发送失败:");
                return null;
            }
            String responseBody = new String(response.body().bytes(), "GBK");
            re=responseBody;
            log.debug("短信发送响应: {}", responseBody);
        }
        return re;
    }

}
