package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.VocChannelCategoryMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.IVocChannelCategoryService;
import com.car.voc.vo.ChannelCategoryVo;
import com.car.voc.vo.VocChannelCategoryVo;
import com.car.voc.vo.auth.RoleAuthTree;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import wiremock.org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @version 1.0.0
 * @ClassName VocChannelCategoryServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月17日 22:31
 * @Copyright voc
 */
@Service
public class VocChannelCategoryServiceImpl extends ServiceImpl<VocChannelCategoryMapper, VocChannelCategory> implements IVocChannelCategoryService {
    @Autowired
    RedisUtil redisUtil;

   /* @Override
    public Result<?> getAllChannels() {
        QueryWrapper<VocChannelCategory> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(VocChannelCategory::getOrderBy);
        List<VocChannelCategory> categoryVos=this.list(queryWrapper);
        List<ChannelCategoryVo> relsis= BeanUtil.copyToList(categoryVos,ChannelCategoryVo.class);
        relsis = relsis.stream().filter(k->!k.getId().equals("1356178730703224007")&&!k.getId().equals("1356178730703224006")).collect(Collectors.toList());
        List<ChannelCategoryVo> categoryVos1=relsis.stream().filter(e->e.getPid().equals("0")).collect(Collectors.toList());
        List<ChannelCategoryVo> cp=new ArrayList<>();
        List<ChannelCategoryVo> finalRelsis = relsis;
        categoryVos1.forEach(e->{
            List<ChannelCategoryVo> seoslist= finalRelsis.stream().filter(a->a.getPid().equals(e.getId())).collect(Collectors.toList());
//            seoslist.sort(Comparator.comparing(ChannelCategoryVo::getOrderBy).reversed());

            seoslist.forEach(s->{
                List<ChannelCategoryVo> threes= finalRelsis.stream().filter(k->k.getPid().equals(s.getId()) ).collect(Collectors.toList());
                Collections.sort(threes,(ChannelCategoryVo o1, ChannelCategoryVo o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.getName(),o2.getName()));
                s.setChildes(threes);
            });
//            Collections.sort(seoslist,(ChannelCategoryVo o1, ChannelCategoryVo o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.getName(),o2.getName()));
            e.setChildes(seoslist);
            if (!e.getName().equals("公域数据")){
                cp.add(e);
            }
        });
        List<ChannelCategoryVo> rel=new ArrayList<>();
        ChannelCategoryVo ctsj=new ChannelCategoryVo();ctsj.setName("私域数据");ctsj.setId("Customer-data");ctsj.setChildes(cp);
//        ChannelCategoryVo b2csj=categoryVos1.stream().filter(d->d.getName().equals("公域数据")).collect(Collectors.toList()).stream().findFirst().orElse(null);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<Object> permissionSet = redisUtil.sGet(CacheConstant.SYS_USER_CHANNEL+loginUser.getId());
        List<String> roleChannelIds = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());
        ctsj.setChildes(ctsj.getChildes().stream().filter(s->roleChannelIds.contains(s.getId())).collect(Collectors.toList()));
        ctsj.getChildes().stream().forEach(s->{
            if(s.getId().equals(CommonConstant.privateChannelId)){
                s.setChildes(s.getChildes().stream().filter(y->roleChannelIds.contains(y.getId())).collect(Collectors.toList()));
            }
        });
//        b2csj.setChildes(b2csj.getChildes().stream().filter(s->roleChannelIds.contains(s.getId())).collect(Collectors.toList()));

//        List<>
//        rel.add(b2csj);
        rel.add(0,ctsj);
        return Result.OK(rel);
    }
    */

    @Override
    public Result<?> getAllChannels(String brandCode) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<Object> channelSet =redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_CHANNEL,brandCode,loginUser.getId()));
        Set<String> stringSet = channelSet.stream()
                .map(Object::toString) // 将对象转换为字符串
                .collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(stringSet)){
            return Result.OK(new ArrayList<>());
        }
        QueryWrapper<VocChannelCategory> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().orderByAsc(VocChannelCategory::getOrderBy);
        queryWrapper.lambda().eq(VocChannelCategory::isEnable,true);
        queryWrapper.lambda().in(VocChannelCategory::getId,stringSet);
        List<VocChannelCategory> categoryVos=this.list(queryWrapper);

        List<ChannelCategoryVo> relsis= BeanUtil.copyToList(categoryVos,ChannelCategoryVo.class);

        List<ChannelCategoryVo> categoryVos1=relsis.stream().filter(e-> "0".equals(e.getPid())).collect(Collectors.toList());
        List<ChannelCategoryVo> finalRelsis = relsis;
        categoryVos1.forEach(e->{
            List<ChannelCategoryVo> seoslist= finalRelsis.stream().filter(a->a.getPid().equals(e.getId())).collect(Collectors.toList());
            seoslist=seoslist.stream().filter(s->stringSet.contains(s.getId())).collect(Collectors.toList());
            seoslist.forEach(s->{
                List<ChannelCategoryVo> threes= finalRelsis.stream().filter(k->k.getPid().equals(s.getId()) ).collect(Collectors.toList());
                threes=threes.stream().filter(k->stringSet.contains(k.getId())).collect(Collectors.toList());
                s.setChildes(threes);
            });
            e.setChildes(seoslist);
        });
        return Result.OK(categoryVos1);
    }

    @Override
    public VocChannelCategory getByIdCache(String id) {
        VocChannelCategory vo= (VocChannelCategory) redisUtil.get(CacheConstant.SYS_CHANNEL_Id+id);
        if (vo==null){
            vo=this.getById(id);
            redisUtil.set(CacheConstant.SYS_CHANNEL_Id+id,vo,10000);
        }
        return vo;
    }

    @Override
    public IPage<VocChannelCategoryVo> queryByPage(Page<VocChannelCategoryVo> page, VocChannelCategory model, HttpServletRequest req) {


        return baseMapper.queryByPage(page,model);
    }

    @Override
    public boolean add(VocChannelCategory model) {

        VocChannelCategory vo=new VocChannelCategory();
        LambdaQueryWrapper<VocChannelCategory> query = new LambdaQueryWrapper<VocChannelCategory>().eq(VocChannelCategory::getName, model.getName());
        List<VocChannelCategory> brandProductManagers = baseMapper.selectList(query);
        if(CollectionUtil.isNotEmpty(brandProductManagers)){
            return false;
        }
        this.brandCode(model.getPid(),model.getBrandCodeList());
        BeanUtil.copyProperties(model,vo);
        vo.setOrderBy(Short.valueOf("0"));
        if (CollectionUtil.isNotEmpty(model.getBrandCodeList())) {
            vo.setBrandCode(String.join(",", model.getBrandCodeList()));
        }
        if (StrUtil.isEmpty(model.getPid())){
            vo.setPid("0");
        }
        vo.setCreateTime(new Date());
        return this.saveOrUpdate(vo);
    }

    public void  brandCode(String id,List<String> brandCodeList){

        if(StringUtils.isNotEmpty(id)&&!"0".equals(id)&&CollectionUtil.isNotEmpty(brandCodeList)){
            VocChannelCategory vocChannelCategory = baseMapper.selectById(id);
            if(Objects.nonNull(vocChannelCategory)){
                String brandCode = vocChannelCategory.getBrandCode();
                List<String> brandCodes = new ArrayList<>();
                if (StringUtils.isNotEmpty(brandCode)){
                    brandCodes.addAll(Arrays.asList(brandCode.split(",")));
                    List<String> list = brandCodes.stream().filter(b -> !brandCodeList.contains(b)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(list)){
                        brandCodes.addAll(list);
                        String join = String.join(",", brandCodes);
                        vocChannelCategory.setBrandCode(join);
                        this.updateById(vocChannelCategory);
                    }else {
                        String join = String.join(",", brandCodeList);
                        vocChannelCategory.setBrandCode(join);
                        this.updateById(vocChannelCategory);
                    }
                }else {
                    String join = String.join(",", brandCodeList);
                    vocChannelCategory.setBrandCode(join);
                    this.updateById(vocChannelCategory);
                }
                if(StringUtils.isNotEmpty(vocChannelCategory.getPid())&&!"0".equals(vocChannelCategory.getPid())&&CollectionUtil.isNotEmpty(brandCodes)){
                    this.brandCode(vocChannelCategory.getPid(),brandCodes);
                }else {
                    return;
                }
            }
        }
    }

    @Override
    public boolean edit(VocChannelCategory model) {
        VocChannelCategory vo =new VocChannelCategory();
        LambdaQueryWrapper<VocChannelCategory> query = new LambdaQueryWrapper<VocChannelCategory>().eq(VocChannelCategory::getName, model.getName());
        query.ne(VocChannelCategory::getId,model.getId());
        List<VocChannelCategory> brandProductManagers = baseMapper.selectList(query);
        if(CollectionUtil.isNotEmpty(brandProductManagers)){
            return false;
        }
        this.brandCode(model.getPid(),model.getBrandCodeList());
        BeanUtil.copyProperties(model,vo);
        vo.setBrandCode(String.join(",",model.getBrandCodeList()));
        VocChannelCategory entity = this.getById(model.getId());
        if(entity==null) {
            throw new BootException("未找到对应实体");
        }
        vo.setUpdateTime(new Date());
        return this.updateById(vo);
    }

    @Override
    public boolean delete(String id) {
        VocChannelCategory vo = this.getById(id);
        if(vo==null) {
            throw new BootException("未找到对应实体");
        }
        return this.removeById(id);
    }

    @Override
    public List<VocChannelCategory> cacheList() {
        List<VocChannelCategory> chall= (List<VocChannelCategory>) redisUtil.get(CacheConstant.SYS_CHANNEL_ALL);
        if (chall!=null&&chall.size()>0){
            return chall;
        }else {
            chall=this.list();
            redisUtil.set(CacheConstant.SYS_CHANNEL_ALL,chall,CacheConstant.expire);
            return chall;
        }
    }
    @Override
    public List<VocChannelCategory> findAll(){
        return this.cacheList();
    }


}
