package com.car.voc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.common.Result;
import com.car.voc.entity.VocRiskAlertReviewer;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.model.risk.RiskAlertReviewerModel;
import com.car.voc.vo.risk.ReviewerPermissionVo;
import com.car.voc.vo.risk.RiskAlertReviewerVo;
import com.car.voc.vo.risk.RiskAllTypesVo;

import javax.servlet.http.HttpServletRequest;
import java.beans.Transient;
import java.util.List;

/**
 * @version 1.0.0
 * @ClassName IVocAlertReviewerService.java
 * @Description TODO
 * @createTime 2023年02月03日 16:08
 * @Copyright voc
 */
public interface IVocRiskAlertReviewerService extends IService<VocRiskAlertReviewer> {
    Result<IPage<RiskAlertReviewerVo>> queryPageList(VocRiskAlertReviewer user, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Result<?> saveOrUpdate2(RiskAlertReviewerModel reviewerModel);

    void deleteReviewer(String id);

    Result<?> queryById(String id);

    RiskAlertReviewerVo getByIdVo(RiskAlertReviewerModel model);

    ReviewerPermissionVo queryPermissionByUserId(String userId);

    ReviewerPermissionVo queryHandlePermissionByUserId(String userId);

    List<RiskAlertReviewerVo> selectByBrandCodeTagCodeUserId(String id, String brandCode, String riskNameSubstring);

    void vocRiskReviewerSendDingtalkJob();

    List<String> getBrandCodeList(String userId);

    List<RiskAllTypesVo> problemOverviewLevel(FilterCriteriaModel model);

    List<RiskAllTypesVo> problemOverviewDepart(FilterCriteriaModel model);

    RiskAllTypesVo processingProgress(FilterCriteriaModel model);
}
