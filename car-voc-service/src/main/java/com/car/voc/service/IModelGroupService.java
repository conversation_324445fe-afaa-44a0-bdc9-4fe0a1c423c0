package com.car.voc.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.ModelGroup;
import com.car.voc.model.ModelGroupModel;
import com.car.voc.vo.ModelGroupVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 车型组管理
 *
 * @Date:   2021-04-09
 * @Version: V1.0
 */
public interface IModelGroupService extends IService<ModelGroup> {


    Result<IPage<ModelGroup>> queryPageList(ModelGroupModel modelGroupModel, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Result<ModelGroupVo> add(ModelGroupModel modelGroupModel);

    Result<ModelGroupVo> edit(ModelGroupModel sysUserModel);

    void deleteModelGroup(String id);

    Result<List<ModelGroupVo>> listall(ModelGroupModel modelGroupModel, HttpServletRequest req);
}
