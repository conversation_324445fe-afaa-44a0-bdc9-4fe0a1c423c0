package com.car.voc.service;


import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.SysDictItem;
import com.car.voc.vo.EnergyClassificationVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 *
 * @since 2018-12-28
 */
public interface ISysDictItemService extends IService<SysDictItem> {
    public List<SysDictItem> selectItemsByMainId(String mainId);

    List<SysDictItem> getSysDictItemByItemValueAndDictId(String key, String dictId);
    String  getSysDictItemTextByItemValueAndDictId(String itemValue, String dictId);

    Result<JSONObject> sysAllDictItems();

    List<EnergyClassificationVo> energyClassification();
}
