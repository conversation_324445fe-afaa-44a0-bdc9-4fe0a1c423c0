package com.car.voc.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.common.enums.RiskProcessStatusEnum;
import com.car.voc.common.enums.RiskStateEnum;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.mapper.VocRiskAlertReviewerMapper;
import com.car.voc.mapper.VocRiskHandlingRecordMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.*;
import com.car.voc.vo.risk.RiskAllTypesVo;
import com.car.voc.vo.risk.RiskProcessTimeoutVo;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * @version 1.0.0
 * @ClassName VocRiskHandlingRecordImpl.java
 * @Description TODO
 * @createTime 2023年02月07日 11:36
 * @Copyright voc
 */
@Service
public class VocRiskHandlingRecordImpl extends ServiceImpl<VocRiskHandlingRecordMapper, VocRiskHandlingRecord> implements IVocRiskHandlingRecordService {

    @Autowired
    ISysUserService userService;
    @Autowired
    ISysDepartService departService;

    @Autowired
    IVocRiskProessRecipientService recipientService;
    @Resource
    VocRiskAlertReviewerMapper reviewerMapper;
    @Autowired
    IVocRiskAllTypesService allTypesService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    private IBrandProductManagerService brandProductManagerService;
    @Autowired
    private IVocBusinessTagService businessTagService;
    @Autowired
    private IVocRiskProessRecipientService riskProessRecipientService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISendMessageService iSendMessageService;







    @Override
    public List<VocRiskHandlingRecord> listExtend(QueryWrapper<VocRiskHandlingRecord> wrapper) {
        return this.baseMapper.listExtend(wrapper);
    }
    LoginUser getUserInfo(){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return sysUser;
    }
    @Override
    public BigDecimal getToBeConfirmed(FilterCriteriaModel model) {
        QueryWrapper<VocRiskHandlingRecord> wrapper=new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskHandlingRecord::getProcessUserId, getUserInfo().getId());
        wrapper.lambda().eq(VocRiskHandlingRecord::getProcessStatus,0);
//        wrapper.lambda().apply((model.getStartDate()!=null&&model.getEndDate()!=null),"(create_time BETWEEN {0} AND {1})", model.getStartDate(),model.getEndDate());
        return new BigDecimal(baseMapper.selectCount(wrapper));
    }

    @Override
    public List<VocRiskHandlingRecord> selectProcessingRecords(String id) {
        QueryWrapper<VocRiskHandlingRecord> wrapper=new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskHandlingRecord::getWarningRiskId, id);
        wrapper.lambda().eq(VocRiskHandlingRecord::getProcessStatus,1);
        wrapper.lambda().orderByDesc(VocRiskHandlingRecord::getProcessTime);
        List<VocRiskHandlingRecord> vocRiskHandlingRecords = baseMapper.selectList(wrapper);

        for (VocRiskHandlingRecord riskHandlingRecord:vocRiskHandlingRecords) {
            SysUser processUser = userService.getById(riskHandlingRecord.getProcessUserId());
            if(ObjectUtils.isNotEmpty(processUser)){
                riskHandlingRecord.setProcessUserName(processUser.getRealname());
            }

            SysDepart processDepart = departService.getById(riskHandlingRecord.getProcessDepartId());
            if(ObjectUtils.isNotEmpty(processDepart)){
                riskHandlingRecord.setProcessDepartName(processDepart.getDepartName());
            }
        }

        return vocRiskHandlingRecords;
    }

    @Override
    public List<VocRiskHandlingRecord> selectByWarningRiskId(String id) {
        QueryWrapper<VocRiskHandlingRecord> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocRiskHandlingRecord::getWarningRiskId,id);
        wrapper.lambda().eq(VocRiskHandlingRecord::getDelFlag,0);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Integer deleteById(String id) {
        return baseMapper.deleteById(id);
    }



    @Override
    public void vocRiskProcessTimeoutRemindJob() {
        QueryWrapper<VocRiskHandlingRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(VocRiskHandlingRecord::getProcessStatus, new int[]{RiskProcessStatusEnum.RiskState_0.getValue(),RiskProcessStatusEnum.RiskState_2.getValue()})
                .eq(VocRiskHandlingRecord::getDelFlag, 0);

        List<RiskProcessTimeoutVo> rewer = baseMapper.processTimeoutRemind(queryWrapper);
        if (rewer.isEmpty()) {
            return;
        }
        Map<String, List<RiskProcessTimeoutVo>> grUserId = rewer.stream().collect(Collectors.groupingBy(RiskProcessTimeoutVo::getProcessUserId));
        for (Map.Entry<String, List<RiskProcessTimeoutVo>> user : grUserId.entrySet()) {
            Map<String, List<RiskProcessTimeoutVo>> grBrand = user.getValue().stream().collect(Collectors.groupingBy(RiskProcessTimeoutVo::getBrandName));
            Map<String,Map<String,String>> brands=new HashMap<>();

            SysUser pu = sysUserService.getById(user.getKey());
            SysDepart pp = departService.getById(user.getValue().get(0).getProcessDepartId());
            String useinfo = pp.getDepartName() + "-" + pu.getRealname();
            String t3 = "VOC管理平台分派的问题需要您及时处理，涉及：";
            String title = "\n" +
                    "标题：VOC问题处理提醒  \n" +
                    "内容：**" + useinfo + "**：您好！  \n" + t3 +
                    "\n";
            String last = "\n登录VOC管理平台-任务处理派发，请及时进行处理。";
            StringBuilder table = new StringBuilder();


            for (Map.Entry<String, List<RiskProcessTimeoutVo>> brand : grBrand.entrySet()) {
                Map<String,String> tags=new HashMap<>();
                Map<String, List<RiskProcessTimeoutVo>> grRisk=brand.getValue().stream().collect(Collectors.groupingBy(RiskProcessTimeoutVo::getRiskType));
                for (Map.Entry<String, List<RiskProcessTimeoutVo>> tagtype : grRisk.entrySet()) {
                    if (tagtype.getValue()!=null&&tagtype.getValue().size()>0){
                        List<RiskProcessTimeoutVo> tag7Update=tagtype.getValue().stream().filter(e->(e.getDaysDifference()<10&&e.getReminderFrequency()==null)).collect(Collectors.toList());
                        List<RiskProcessTimeoutVo> tag10Update=tagtype.getValue().stream().filter(e->((e.getDaysDifference()==10)&&e.getReminderFrequency()==1)).collect(Collectors.toList());
                        List<RiskProcessTimeoutVo> tag10Gt7Update=tagtype.getValue().stream().filter(e->((e.getDaysDifference()>10&&e.getDaysDi7()!=null&&e.getDaysDi7()==7))).collect(Collectors.toList());
                        List<String> tag7=tag7Update.stream().map(RiskProcessTimeoutVo::getRiskName).collect(Collectors.toList());
                        List<String> tag10=tag10Update.stream().map(RiskProcessTimeoutVo::getRiskName).collect(Collectors.toList());
                        List<String> tag10Gt7=tag10Gt7Update.stream().map(RiskProcessTimeoutVo::getRiskName).collect(Collectors.toList());

                        if (tag10==null) {
                            tag10=new ArrayList<>();
                        }

                        if (tag10Gt7!=null&&tag10Gt7.size()>0){
                            tag10.addAll(tag10Gt7);
                        }
                        String tagStr=null;
                        if (tag7!=null&&tag7.size()>0){
                            tagStr=String.join("、",tag7)+"共计"+tag7.size()+"条即将逾期";
                        }
                        if (tag10!=null&&tag10.size()>0){
                            tagStr=String.join("、",tag10)+"共计"+tag10.size()+"条已经逾期";
                        }
                        if (tagStr!=null){
                            tags.put(tagtype.getKey(),tagStr);
                        }

                        List<RiskProcessTimeoutVo> allUpdate=new ArrayList<>();
                        if (tag7Update!=null&&tag7Update.size()>0) {
                            allUpdate.addAll(tag7Update);
                        }
                        if (tag10Update!=null&&tag10Update.size()>0) {
                            allUpdate.addAll(tag10Update);
                        }
                        if (tag10Gt7Update!=null&&tag10Gt7Update.size()>0) {
                            allUpdate.addAll(tag10Gt7Update);
                        }
                        for (RiskProcessTimeoutVo riskProcessTimeoutVo : allUpdate) {
                            VocRiskHandlingRecord onerhlr=this.getById(riskProcessTimeoutVo.getId());
                            UpdateWrapper<VocRiskHandlingRecord> update=new UpdateWrapper<>();
                            update.lambda().eq(VocRiskHandlingRecord::getId,riskProcessTimeoutVo.getId())
                                    .set(VocRiskHandlingRecord::getLastReminderTime,new Date())
                                    .set(VocRiskHandlingRecord::getReminderFrequency,onerhlr.getReminderFrequency()==null?1:onerhlr.getReminderFrequency()+1);
                            this.update(update);
                        }
                    }
                }
                if (tags.size()==0){
                    continue;
                }
                brands.put(brand.getKey(),tags);
                table.append("  \n" + "-----------------------------------------------------------\n" + "品牌：**").append(brand.getKey()).append("** \n").append("\n");
                if (tags.get("业务问题") != null && tags.get("质量问题") != null) {
                    table.append("-----------------------------------------------------------\n" + "1、业务问题：").append(tags.get("业务问题")).append("； \n").append("\n")
                         .append("-----------------------------------------------------------\n").append("2、质量问题：").append(tags.get("质量问题")).append("； \n").append("\n");
                    log.debug("1、业务问题：" + tags.get("业务问题")  +"；");
                    log.debug("2、质量问题：" + tags.get("业务问题") + "；");
                } else if (tags.get("业务问题") != null) {
                    table.append("-----------------------------------------------------------\n" + "1、业务问题：").append(tags.get("业务问题")).append("；").append(" \n").append("\n");
                    log.debug("1、业务问题：" + tags.get("业务问题")+ "；");
                } else {
                    table.append("-----------------------------------------------------------\n" + "1、质量问题：").append(tags.get("质量问题")).append("；").append(" \n").append("\n");
                    log.debug("1、质量问题：" + tags.get("质量问题") + "；");
                }


            }
            if (brands.size()>0){
                iSendMessageService.sendAuditDingtalk(pu.getUsername(), title, table.toString(), last);
//                iSendMessageService.sendAuditDingtalk("1100011000", title, table.toString(), last);
            }
        }
    }

}
