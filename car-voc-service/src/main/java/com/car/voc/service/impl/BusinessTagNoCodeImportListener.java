package com.car.voc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.car.voc.common.constant.FillRuleConstant;
import com.car.voc.common.util.FillRuleUtil;
import com.car.voc.dto.BusinessTagImportDto;
import com.car.voc.dto.BusinessTagImportDto;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocBusinessTagService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Date:   2021-03-30
 * @Version: V1.0
 */
public class BusinessTagNoCodeImportListener extends AnalysisEventListener<BusinessTagImportDto> {
	private static final Logger LOGGER = LoggerFactory.getLogger(BusinessTagNoCodeImportListener.class);

	@Resource
	private IVocBusinessTagService businessTagService;

	/**
	 * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
	 */
	private static final int BATCH_COUNT = 1000;
	List<BusinessTagImportDto> list = new ArrayList<>();

	public BusinessTagNoCodeImportListener(IVocBusinessTagService businessTagService) {
		this.businessTagService=businessTagService;
	}

	@Override
	public void invoke(BusinessTagImportDto data, AnalysisContext context) {
		LOGGER.info("解析到一条数据:{}", JSON.toJSONString(data));
		list.add(data);
	}


	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		saveData();
		LOGGER.info("所有数据解析完成！");
	}

	private void saveData() {
		LOGGER.info("{}条数据，开始存储数据库！", list.size());

		//第一级 - 使用 TreeMap
		Map<String, List<BusinessTagImportDto>> tree1 = list.stream()
				.collect(Collectors.groupingBy(
						BusinessTagImportDto::getTag1,
						TreeMap::new,  // 指定使用 TreeMap
						Collectors.toList()
				));

		for (Map.Entry<String, List<BusinessTagImportDto>> tag1 : tree1.entrySet()) {
			VocBusinessTag t1 = new VocBusinessTag();
			t1.setName(tag1.getKey());
			//一级存在时
			QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(VocBusinessTag::getName, tag1.getKey());
			queryWrapper.lambda().eq(VocBusinessTag::getPid, "0");
			t1 = businessTagService.getOne(queryWrapper);

			if (t1 == null) {//保存一级
				t1 = new VocBusinessTag();
				t1.setTag1Info(tag1.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag1.getKey(), t1, 1, "0", 1);
			}
			//新增一级时 （有一级的情况下）
			if (StrUtil.isBlankIfStr(t1.getTagCode())) {
				t1.setTag1Info(tag1.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag1.getKey(), t1, 1, "0", 1);
			}

			//第二级 - 使用 TreeMap
			Map<String, List<BusinessTagImportDto>> tree2 = tag1.getValue().stream()
					.collect(Collectors.groupingBy(
							BusinessTagImportDto::getTag2,
							TreeMap::new,  // 指定使用 TreeMap
							Collectors.toList()
					));

			for (Map.Entry<String, List<BusinessTagImportDto>> tag2 : tree2.entrySet()) {
				VocBusinessTag t2 = new VocBusinessTag();
				t2.setTag2Info(tag2.getValue().get(0));
				t1.setTagScope("0");
				codeWarehousing(tag2.getKey(), t2, 2, t1.getId(), 1);

				//第三级 - 使用 TreeMap
				Map<String, List<BusinessTagImportDto>> tree3 = tag2.getValue().stream()
						.collect(Collectors.groupingBy(
								BusinessTagImportDto::getTag3,
								TreeMap::new,  // 指定使用 TreeMap
								Collectors.toList()
						));

				for (Map.Entry<String, List<BusinessTagImportDto>> tag3 : tree3.entrySet()) {
					VocBusinessTag t3 = new VocBusinessTag();
					t3.setTag3Info(tag3.getValue().get(0));
					setTagScope(tag3.getValue().get(0), t3);
					codeWarehousing(tag3.getKey(), t3, 3, t2.getId(), 1);

					//第四级
					for (BusinessTagImportDto dto : tag3.getValue()) {
						VocBusinessTag t4 = new VocBusinessTag();
						t4.setTag4Info(dto);
						t4.setRelatedDepartments(dto.getRelatedDepartments());
						setTagScope(dto, t4);
						codeWarehousing(dto.getTag4(), t4, 4, t3.getId(), 0);
					}
				}
			}
		}

		LOGGER.info("存储数据库成功！");
	}

	private static void setTagScope(BusinessTagImportDto dto, VocBusinessTag t4) {
		if (StrUtil.isNotBlank(dto.getApplicationSide())
				&& dto.getApplicationSide().equals("工单")) {
			t4.setTagScope("2");
		} else {
			t4.setTagScope("0");
		}
	}

	private void codeWarehousing(String name, VocBusinessTag vo, int level, String pid, int hasChild) {
		vo.setName(name);
//		vo.setLevel(level);
		vo.setPid(pid);
		vo.setEnable(true);
		vo.setCreateTime(new Date());
		vo.setHasChild(hasChild+"");
		vo.setOther(0);
		JSONObject formData2 = new JSONObject();
		formData2.putOpt("pid",pid);
		String code2 = (String) FillRuleUtil.executeRule(FillRuleConstant.BUSINESS_TAG,formData2);
		vo.setTagCode(code2);
		businessTagService.save(vo);
	}
}
