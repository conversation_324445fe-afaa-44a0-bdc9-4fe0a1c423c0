package com.car.voc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.*;
import com.car.voc.dto.RequestDictDto;
import com.car.voc.entity.*;
import com.car.voc.mapper.*;
import com.car.voc.model.*;
import com.car.voc.service.*;
import com.car.voc.vo.SysUserDepVo;
import com.car.voc.vo.SysUserListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * @Date: 2018-12-20
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    SysDepartRoleMapper sysDepartRoleMapper;
    @Autowired
    private SysDepartRoleUserMapper departRoleUserMapper;
    @Autowired
    ISysUserDepartService sysUserDepartService;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    ISysRoleService sysRoleService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    ISysDictService sysDictService;
    @Autowired
    SysLogMapper sysLogMapper;
    @Autowired
    private ISysLogService sysLogService;

    @Value("${configuration.tokenCheckToken.baseUrl}")
    String baseUrl;
    @Value("${configuration.tokenCheckToken.checkTokenValidPath}")
    String checkTokenValidPath;
    @Value("${configuration.tokenCheckToken.getUserInfoPath}")
    String getUserInfoPath;
    @Value("${configuration.tokenCheckToken.dictPath}")
    String dictPath;
    @Value("${configuration.tokenCheckToken.logoutPath}")
    String logoutPath;
    @Value("${configuration.tokenCheckToken.clientId}")
    String clientId;
    @Value("${configuration.tokenCheckToken.clientSecret}")
    String clientSecret;
    @Value("${configuration.tokenCheckToken.roleId}")
    String tokenCheckTokenRoleId;
    @Value("${configuration.tokenCheckToken.departId}")
    String tokenCheckTokenDepartId;

    /**
     * 用户信息
     *
     * @param sysUser
     * @param result
     * @return
     */
    @Override
    public Result<JSONObject> userInfo(SysUser sysUser, Result<JSONObject> result) {
        String syspassword = sysUser.getPassword();
        String username = sysUser.getUsername();
        // 生成token
        String token = JwtUtil.sign(username, syspassword);
        // 设置token缓存有效时间 7天
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token, JwtUtil.EXPIRE_TIME + 24, TimeUnit.HOURS);
        //2小时
//        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME*2 / 1000);


        // 获取用户部门信息
        JSONObject obj = new JSONObject();

        obj.putOpt("token", token);
        obj.putOpt("tokenExpire", redisUtil.getExpire(CommonConstant.PREFIX_USER_TOKEN + token));
//        obj.putOpt("userInfo", sysUser);
//        obj.putOpt("sysAllDictItems", sysDictService.queryAllDictItems());

        result.setResult(obj);
        result.success("登录成功");
        return result;
    }

    @Override
    public Result<JSONObject> login(SysLoginModel sysLoginModel) {
        Result<JSONObject> result = new Result<JSONObject>();


        String username = PasswordUtil.decrypt(sysLoginModel.getUsername());
        String password = PasswordUtil.decrypt(sysLoginModel.getPassword());
        //前端密码加密，后端进行密码解密
        String type = sysLoginModel.getType();

        String realKey = "";
        if (StrUtil.isNotBlank(type) && "1".equals(type)) {//1:其他页面 不用验证

        } else {//0:自有页面  要验证
            String captcha = sysLoginModel.getCaptcha();
            if (captcha == null) {
                result.error500("验证码无效");
                return result;
            }

            if (!"9527".equals(captcha)) {
                String lowerCaseCaptcha = captcha.toLowerCase(Locale.ROOT);
                realKey = MD5Util.MD5Encode(lowerCaseCaptcha + sysLoginModel.getCheckKey(), "utf-8");
                Object checkCode = redisUtil.get(realKey);
                //当进入登录页时，有一定几率出现验证码错误 #1714
                if (checkCode == null || !checkCode.toString().equals(lowerCaseCaptcha)) {
                    redisUtil.del(realKey);
                    result.error500("验证码错误");
                    return result;
                }
            }
        }

        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        SysUser sysUser = this.getOne(queryWrapper);
        //update-end for: 登录代码验证用户是否注销bug，if条件永远为false
        result = this.checkUserIsEffective(sysUser);
        if (!result.isSuccess()) {
            if (StrUtil.isBlankIfStr(type)) {
                redisUtil.del(realKey);
            }
            return result;
        }
        //密码输入次数超过5次
        Integer logn = ((Integer) redisUtil.get(CommonConstant.SHIRO_IS_LOCK + username));
        if (logn != null && logn >= 5) {
            redisUtil.set(CommonConstant.SHIRO_IS_LOCK + username, logn, 60, TimeUnit.SECONDS);
            result.error500("你已经被冻结，密码输入错误次数超过5次，请10分钟后再来登录");
            return result;
        }


        //2. 校验用户名或密码是否正确
        String userpassword = PasswordUtil.encrypt(username, password, sysUser.getSalt());
        String syspassword = sysUser.getPassword();

        if (!syspassword.equals(userpassword)) {
            result.error500("用户名或密码错误");

            if (StrUtil.isBlankIfStr(type)) {
                redisUtil.del(realKey);
            }
            Integer n = ((Integer) redisUtil.get(CommonConstant.SHIRO_IS_LOCK + username));
            n = n == null ? 0 : n;
            if (n < 5) {
                redisUtil.set(CommonConstant.SHIRO_IS_LOCK + username, n + 1, 60, TimeUnit.SECONDS);
            }
            Integer logi = ((Integer) redisUtil.get(CommonConstant.SHIRO_IS_LOCK + username));
            if (logi != null && logi >= 5) {
                result.error500("你已经被冻结，密码输入错误次数超过5次，请10分钟后再来登录");
            }
            return result;
        }

        redisUtil.del(CommonConstant.SHIRO_IS_LOCK + username);
        //用户登录信息
        userInfo(sysUser, result);

        LoginUser loginUser = new LoginUser();
        BeanUtils.copyProperties(sysUser, loginUser);
        if (StrUtil.isBlankIfStr(type)) {
            redisUtil.del(realKey);
        }
        return result;
    }


    @Override
    public Result checkUserIsEffective(SysUser sysUser) {
        Result<?> result = new Result<Object>();
        //情况1：根据用户信息查询，该用户不存在
        if (sysUser == null) {
//			result.error500("该用户不存在，请注册");
            result.error500("用户名或密码错误");
            return result;
        }
        //情况2：根据用户信息查询，该用户已注销
        //update-begin-- for：if条件永远为falsebug------------
        if (CommonConstant.DEL_FLAG_1.equals(sysUser.getDelFlag())) {
            //update-end-- for：if条件永远为falsebug------------
//			result.error500("该用户已注销");
            result.error500("用户名或密码错误");
            return result;
        }
        //情况3：根据用户信息查询，该用户已冻结
        if (CommonConstant.USER_FREEZE.equals(sysUser.getStatus())) {
//			result.error500("该用户已冻结");
            result.error500("您无权访问此系统");
            return result;
        }
        return result;
    }

    @Override
    @Cacheable(cacheNames = CacheConstant.SYS_USERS_CACHE, key = "#username")
    public LoginUser getUserByName(String username) {
        if (StrUtil.isEmpty(username)) {
            return null;
        }
        LoginUser loginUser = new LoginUser();
        SysUser sysUser = baseMapper.getUserByName(username);
        if (sysUser == null) {
            return null;
        }
        BeanUtils.copyProperties(sysUser, loginUser);
        return loginUser;
    }


    @Override
    public List<String> getRole(String username) {
        return sysUserRoleMapper.getRoleByUserName(username);
    }

    @Override
    public Map<String, String> getDepNamesByUserIds(List<String> userIds) {
        List<SysUserDepVo> list = this.baseMapper.getDepNamesByUserIds(userIds);

        Map<String, String> res = new HashMap<String, String>();
        list.forEach(item -> {
                    if (res.get(item.getUserId()) == null) {
                        res.put(item.getUserId(), item.getDepartName());
                    } else {
                        res.put(item.getUserId(), item.getDepartName());
                    }
                }
        );
        return res;
    }


    @Override
    public Result<SysUser> add(SysUserModel sysUserModel) {
        Result<SysUser> result = new Result<SysUser>();

        if (sysUserModel.getUsername() != null) {
            QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(SysUser::getUsername, sysUserModel.getUsername());
            wrapper.lambda().eq(SysUser::getDelFlag, 0);
            if (this.count(wrapper) > 0) {
                result.error500("账号名重复!");
                return result;
            }
        }
        sysUserModel.setWorkNo(sysUserModel.getUsername());
        if (sysUserModel.getStatus() == null) {
            result.error500("请设置状态!");
            return result;
        }
        if (sysUserModel.getStatus().equals(1) && (StrUtil.isBlankIfStr(sysUserModel.getRoleId()) || StrUtil.isBlankIfStr(sysUserModel.getDepartId()))) {
            result.error500("状态启用时，需设置角色与部门!");
            return result;
        }
        SysUser user = new SysUser();
        BeanUtils.copyProperties(sysUserModel, user);
        try {
            user.setCreateTime(new Date());//设置创建时间
            String salt = RandomUtil.randomString(8);
            user.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getUsername(), salt);
            user.setPassword(passwordEncode);
            user.setStatus(sysUserModel.getStatus());
            user.setDelFlag(CommonConstant.DEL_FLAG_0);
            user.setType("0");
            this.save(user);

            addUserWithRole(user, sysUserModel.getRoleId());
            addUserWithDepart(user, sysUserModel.getDepartId());
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @Override
    @Transactional
    public void addUserWithRole(SysUser user, String roleId) {
        SysUserRole userRole = new SysUserRole(user.getId(), roleId);
        sysUserRoleMapper.insert(userRole);
    }


    @Override
    @Transactional
    public void addUserWithDepart(SysUser user, String departId) {
        SysUserDepart userDeaprt = new SysUserDepart(user.getId(), departId);
        sysUserDepartMapper.insert(userDeaprt);
    }

    @Override
    public Result<SysUser> edit(SysUserModel sysUserModel) {
        Result<SysUser> result = new Result<SysUser>();
        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(sysUserModel, sysUser);
        try {
            sysUser = getById(sysUser.getId());
            if (sysUser == null) {
                result.error500("未找到对应实体");
            } else {
                SysUser user = new SysUser();
                BeanUtils.copyProperties(sysUserModel, user);
                if (sysUserModel.getStatus().equals(1) && (StrUtil.isBlankIfStr(sysUserModel.getRoleId()) || StrUtil.isBlankIfStr(sysUserModel.getDepartId()))) {
                    result.error500("状态启用时，需设置角色与部门!");
                    return result;
                }
               if (sysUserModel.getStatus().equals(1)){
                   SysDepart depart = sysDepartService.getById(sysUserModel.getDepartId());
                   SysRole role = sysRoleService.getById(sysUserModel.getRoleId());
                   if (depart == null || role == null) {
                       result.error500("部门或角色不存在!");
                       return result;
                   }
                   if (!depart.getStatus()){
                       result.error500("部门未启用!");
                       return result;
                   }
                   if (!role.isRoleStatus()){
                       result.error500("角色未启用!");
                       return result;
                   }
               }
                user.setUpdateTime(new Date());
                user.setPassword(sysUser.getPassword());
                editUserWithRole(user, sysUserModel.getRoleId());
                editUserWithDepart(user, sysUserModel.getDepartId());
                this.updateById(user);
                result.success("修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional
    public void editUserWithRole(SysUser user, String roleId) {
        //先删后加
        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
        SysUserRole userRole = new SysUserRole(user.getId(), roleId);
        if (roleId != null) {
            sysUserRoleMapper.insert(userRole);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public void editUserWithDepart(SysUser user, String departId) {
        //先删后加
        sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
        if (departId != null) {
            sysUserDepartMapper.insert(userDepart);
        }
    }


    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(String userId) {
        //1.删除用户
        this.removeById(userId);
        UpdateWrapper<SysUser> update = new UpdateWrapper<SysUser>();
        update.lambda().set(SysUser::getDelFlag, CommonConstant.DEL_FLAG_1)
                .eq(SysUser::getId, userId);
        this.update(update);
        return true;
    }

    @Override
    public Result<List<String>> queryUserRole(String userid) {
        Result<List<String>> result = new Result<>();
        List<String> list = new ArrayList<String>();
        List<SysUserRole> userRole = sysUserRoleMapper.selectList(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, userid));
        if (userRole == null || userRole.size() <= 0) {
            result.error500("未找到用户相关角色信息");
        } else {
            for (SysUserRole sysUserRole : userRole) {
                list.add(sysUserRole.getRoleId());
            }
            result.setSuccess(true);
            result.setResult(list);
        }
        return result;
    }

    @Override
    public Result<Boolean> checkOnlyUser(SysUser sysUser) {
        Result<Boolean> result = new Result<>();
        //如果此参数为false则程序发生异常
        result.setResult(true);
        try {
            //通过传入信息查询新的用户信息
            SysUser user = this.getOne(new QueryWrapper<SysUser>(sysUser));
            if (user != null) {
                result.setSuccess(false);
                result.setMessage("用户账号已存在");
                return result;
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            return result;
        }
        result.setSuccess(true);
        return result;
    }

    //    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> changePassword1(SysUser sysUser) {
        String salt = RandomUtil.randomString(8);
        sysUser.setSalt(salt);
        String password = sysUser.getPassword();
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
        sysUser.setPassword(passwordEncode);
        UpdateWrapper<SysUser> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(SysUser::getUsername, sysUser.getUsername());
        wrapper.lambda().set(SysUser::getPassword, sysUser.getPassword());
        wrapper.lambda().set(SysUser::getSalt, salt);
        this.update(wrapper);
        return Result.OK("密码修改成功!");
    }


    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> changePassword(SysUser sysUser) {
        String salt = RandomUtil.randomString(8);
        sysUser.setSalt(salt);
        String password = sysUser.getPassword();
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
        sysUser.setPassword(passwordEncode);
        UpdateWrapper<SysUser> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(SysUser::getUsername, sysUser.getUsername());
        wrapper.lambda().set(SysUser::getPassword, sysUser.getPassword());
        wrapper.lambda().set(SysUser::getSalt, salt);
        this.update(wrapper);
        return Result.OK("密码修改成功!");
    }


    @Override
    public Result<List<DepartIdModel>> getUserDepartsList(String userId) {
        Result<List<DepartIdModel>> result = new Result<>();
        try {
            List<DepartIdModel> depIdModelList = this.sysUserDepartService.queryDepartIdsOfUser(userId);
            if (depIdModelList != null && depIdModelList.size() > 0) {
                result.setSuccess(true);
                result.setMessage("查找成功");
                result.setResult(depIdModelList);
            } else {
                result.setSuccess(false);
                result.setMessage("查找失败");
            }
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.setSuccess(false);
            result.setMessage("查找过程中出现了异常: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Result<List<SysUser>> queryUserByDepId(String id, String realname) {

        Result<List<SysUser>> result = new Result<>();
        List<SysUser> userList = sysUserDepartService.queryUsersByDepId(id);
        userList.stream().forEach(e -> {
            //查询角色信息
            e.setPassword(null);
            SysRole role = sysUserRoleMapper.getRoleInfoByUserId(e.getId());
            if (role != null) {
                e.setRoleType(role.getRoleType());
            }
        });
        try {
            result.setSuccess(true);
            result.setResult(userList);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.setSuccess(false);
            return result;
        }
    }

    @Override
    public Result<?> resetPassword(UserUpdetePasModel userUpdetePasModel) {
        SysUser user = baseMapper.getUserByName(userUpdetePasModel.getUsername());
        String passwordEncode = PasswordUtil.encrypt(user.getUsername(), userUpdetePasModel.getOldpassword(), user.getSalt());
        if (!user.getPassword().equals(passwordEncode)) {
            return Result.error("旧密码输入错误!");
        }
        if (StrUtil.isEmpty(userUpdetePasModel.getPassword())) {
            return Result.error("新密码不允许为空!");
        }
        if (!userUpdetePasModel.getPassword().equals(userUpdetePasModel.getConfirmpassword())) {
            return Result.error("两次输入密码不一致!");
        }
        if (!PasswordUtil.isComplexPassword(userUpdetePasModel.getPassword(), user.getUsername())) {
            return Result.error("密码不符合复杂性要求!");
        }
        String password = PasswordUtil.encrypt(user.getUsername(), userUpdetePasModel.getPassword(), user.getSalt());

        this.baseMapper.update(new SysUser().setPassword(password).setPassResetTime(new DateTime()), new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, user.getId()));
        return Result.OK("密码重置成功!");
    }

    @Override
    public Result<?> adminUpdatePassword(UserUpdetePasModel userUpdetePasModel) {
        SysUser user = baseMapper.getUserByName(userUpdetePasModel.getUsername());

        if (StrUtil.isEmpty(userUpdetePasModel.getPassword())) {
            return Result.error("新密码不允许为空!");
        }
        if (!userUpdetePasModel.getPassword().equals(userUpdetePasModel.getConfirmpassword())) {
            return Result.error("两次输入密码不一致!");
        }
        String password = PasswordUtil.encrypt(user.getUsername(), userUpdetePasModel.getPassword(), user.getSalt());
        this.baseMapper.update(new SysUser().setPassword(password), new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, user.getId()));
        return Result.OK("密码重置成功!");
    }

    @Override
    public Result<IPage<SysUserListVo>> queryPageList(SysUserModel userModel, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userModel, user);

        Result<IPage<SysUserListVo>> result = new Result<IPage<SysUserListVo>>();
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        //TODO 外部模拟登陆临时账号，列表不显示
//        queryWrapper.ne("username","_reserve_user_external");
        if (StrUtil.isNotBlank(userModel.getSearchKeyword())) {
            queryWrapper.lambda().like(SysUser::getRealname, userModel.getSearchKeyword()).or().like(SysUser::getWorkNo, userModel.getSearchKeyword()).or().like(SysUser::getUsername, userModel.getSearchKeyword());
        }
        Page<SysUser> page = new Page<SysUser>(pageNo, pageSize);
        queryWrapper.lambda().ne(SysUser::getUsername, "root");
        queryWrapper.lambda().orderByDesc(SysUser::getCreateTime);
        queryWrapper.lambda().eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        IPage<SysUser> pageList = this.page(page, queryWrapper);
        IPage<SysUserListVo> listVoIPage = new Page<>();
        List<SysUserListVo> listVos;
        listVos = BeanUtil.copyToList(pageList.getRecords(), SysUserListVo.class);
        listVoIPage.setRecords(listVos);
        listVoIPage.setTotal(pageList.getTotal());
        listVoIPage.setCurrent(pageList.getCurrent());
        listVoIPage.setPages(pageList.getPages());
        listVoIPage.setSize(pageSize);
        listVoIPage.getRecords().forEach(item -> {
            //查询角色信息
            SysRole role = sysUserRoleMapper.getRoleInfoByUserId(item.getId());
            if (role != null) {
                item.setRoleName(role.getRoleName());
                item.setRoleId(role.getId());
                item.setRoleType(role.getRoleType());
            }

            //查询部门信息
            SysDepart depart = sysUserDepartMapper.getDepartInfoByUserId(item.getId());
            if (depart != null) {
                item.setDepartName(depart.getDepartName());
                item.setDepartId(depart.getId());
            }


            item.setLoginCount(sysLogMapper.selectLoginCountByUserId(item.getId()));
            item.setLastLoginTime(sysLogMapper.selectLastLoginTime(item.getId()));
        });

        result.setSuccess(true);
        result.setResult(listVoIPage);
        log.info(listVoIPage.toString());
        return result;
    }

    @Override
    public Result<JSONObject> userInfoPus() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 获取用户部门信息
        JSONObject obj = new JSONObject();
        obj.putOpt("userInfo", sysUser);

        return Result.OK(obj);
    }

    @Override
    public Result<?> ticketByEmployeeId(ThirdLoginModel model) {
        String appKey = "6wjo8y15f33tndpk4v1tsmj6l870760j";//固定提供
        if (!model.getAppKey().equals(appKey)) {
            return Result.error(5001, "appKey不正确！");
        }
        long timeStamp;//时间戳
        timeStamp = model.getTimestamp();//时间戳
        String employeeId = model.getEmployeeId();//工号
        char[] chars = appKey.toCharArray();
        Arrays.sort(chars);
        String signature = SecureUtil.md5(StrUtil.join(StrUtil.AT, new String(chars), timeStamp, employeeId));
        if (!signature.equals(model.getSignature())) {
            return Result.error(5002, "签名不合法！");
        }

        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getWorkNo, model.getEmployeeId());
        SysUser sysUser = this.getOne(queryWrapper);
        if (sysUser == null) {
            return Result.error(5003, "工号不存在！");
        }
        Result<JSONObject> result = new Result<>();
        return userInfo(sysUser, result);
    }

    @Override
    public List<String> listUserPermission(String id) {
        List<String> apis = (List<String>) redisUtil.get(CacheConstant.SYS_USER_API_PERMISSION + id);
        if (apis != null && apis.size() > 0) {
            return apis;
        } else {
            apis = sysUserRoleMapper.listUserPermission(id);
            redisUtil.set(CacheConstant.SYS_USER_API_PERMISSION + id, apis, CacheConstant.expire);
            return apis;
        }
    }

    @Override
    public Result<?> checkTokenVaild(TokenCheckTokenVaildModel checkTokenVaild, HttpServletRequest request) {
        if (StrUtil.isBlankIfStr(checkTokenVaild.getAccessToken())) {
            return Result.error(5001, "accessToken不能为空！");
        }
        return introspect(checkTokenVaild.getAccessToken(),request);
    }

    Result introspect(String accessToken, HttpServletRequest request) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        header.put("Authorization", "Basic " + Base64.encode(clientId + ":" + clientSecret));
        // 创建请求头
        String result = null;
        try {
            result = HttpUtil.createPost(checkTokenValidPath).addHeaders(header).body("&token=" + accessToken).execute().body();
        } catch (HttpException e) {
            throw new RuntimeException(e);
        }
        log.info("result:{}", result);
        if (JSONUtil.isJson(result)) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Result<JSONObject> result1 = new Result<>();
            if (jsonObject.get("authenticated") != null && jsonObject.get("authenticated").equals(true)) {
                QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("id", jsonObject.getStr("sub"));
                SysUser sysUser = this.getOne(queryWrapper);
                if (sysUser != null && sysUser.getStatus().equals(1)) {
                    saveSysLog(sysUser, request);
                    return userInfo(sysUser, result1);
                }else{
                    return Result.error("您无权访问此系统！");
                }
            } else {
                return Result.error("验证失败（统一平台）！");
            }
        } else {
            return Result.error("验证失败！");
        }
    }
    private void saveSysLog(SysUser user, HttpServletRequest request) {
        SysLog sysLog = new SysLog();
            sysLog.setLogContent("登录");
            sysLog.setLogType(CommonConstant.LOG_TYPE_1);
        String className = this.getClass().getName();
        sysLog.setMethod(className);
        //设置IP地址
        sysLog.setIp(IpUtil.getIpAddr(request));
        //获取登录用户信息
        if (user != null) {
            sysLog.setUserid(user.getId());
            sysLog.setUsername(user.getRealname());

        }else {
            return;
        }
        sysLog.setCreateTime(new Date());
        //保存系统日志
        sysLogService.save(sysLog);
    }

    public void toCCgetUserInfo(String accessToken, Map<String, String> header, SysUser user, SysDepart depart, SysRole role) {
        header.put("Authorization", "Bearer " + accessToken);
        String resul = HttpUtil.createPost(getUserInfoPath).addHeaders(header).body("{ \"query\": \"query MyQuery { " +
                "getMe { account birthName ctiCode email id name nickName phone " +
                "organization {     orgName      id      orgCode  description  }  " +
                "roleObjects {      createTime      editable      id      isDefault     name      enabled      remark    } " +
                "  } }\", \"operationName\": \"MyQuery\" }").execute().body();
        if (JSONUtil.isJson(resul)) {
            JSONObject jsonObject1 = JSONUtil.parseObj(resul);
            JSONObject data = jsonObject1.getJSONObject("data");
            JSONObject getMe = data.getJSONObject("getMe");
            user.setUsername(getMe.getStr("account"));
            user.setRealname(getMe.getStr("name"));
            user.setEmail(getMe.getStr("email"));
            user.setPhone(getMe.getStr("phone"));
            user.setWorkNo(getMe.getStr("account"));
            JSONObject organization = getMe.getJSONObject("organization");
            if (organization != null) {
                QueryWrapper<SysDepart> query = new QueryWrapper<>();
                query.lambda()
                        .eq(SysDepart::getDepartName, organization.getStr("orgName"))
                        .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
                depart = sysDepartService.getOne(query);
                if (depart == null) {
                    depart = new SysDepart();
                    depart.setDelFlag(CommonConstant.DEL_FLAG_0 + "");
                    depart.setOrgType("1");
                    depart.setDepartNameAbbr(organization.getStr("orgName"));
                    depart.setCreateTime(new Date());
                    depart.setDepartName(organization.getStr("orgName"));
                    depart.setDescription(organization.getStr("description"));
                    depart.setOrgCode(organization.getStr("orgCode"));
                    depart.setStatus(false);
                    sysDepartService.save(depart);
                } else {
                    depart = null;
                }
            }
            JSONArray roleObjects = getMe.getJSONArray("roleObjects");
            if (roleObjects != null && roleObjects.size() > 0) {
                JSONObject roleObject = roleObjects.getJSONObject(0);
                QueryWrapper<SysRole> query1 = new QueryWrapper<>();
                query1.lambda()
                        .eq(SysRole::getRoleName, roleObject.getStr("name"))
                        .eq(SysRole::getDelFlag, CommonConstant.DEL_FLAG_STR_0);
                role = sysRoleService.getOne(query1);
                if (role == null) {
                    role = new SysRole();
                    role.setCreateTime(new Date());
                    role.setDelFlag(CommonConstant.DEL_FLAG_STR_0);
                    role.setRoleName(roleObject.getStr("name"));
                    role.setDescription(roleObject.getStr("remark"));
                    role.setRoleStatus(false);
                    user.setStatus(CommonConstant.USER_FREEZE);
                    sysRoleService.save(role);
                } else {
                    role = null;
                }
            }
            /**if (depart == null && role == null) {
                user.setStatus(CommonConstant.USER_UNFREEZE);
            } else {
                user.setStatus(CommonConstant.USER_FREEZE);
            }**/
        }
    }

    public void toCCgetMe(String accessToken, Map<String, String> header, SysUser user) {
        header.put("Authorization", "Bearer " + accessToken);
        String resul = HttpUtil.createPost(getUserInfoPath).addHeaders(header).body("{\"query\":\"query MyQuery {\\n  getMe {\\n    roles\\n    account\\n    createTime\\n    ctiCode\\n    deleted\\n    phone\\n    name\\n    email\\n    enabled\\n    id\\n    mobile\\n    organization {\\n      orgName\\n      id\\n      orgCode\\n      parent {\\n        id\\n        orgName\\n        parent {\\n          id\\n          orgName\\n        }\\n      }\\n    }\\n    organizationId\\n    principal\\n    roleObjects {\\n      createTime\\n      editable\\n      id\\n      isDefault\\n      name\\n      enabled\\n      remark\\n    }\\n  }\\n}\",\"operationName\":\"MyQuery\"}").execute().body();
        if (JSONUtil.isTypeJSON(resul)) {
            JSONObject jsonObject1 = JSONUtil.parseObj(resul);
            JSONObject data = jsonObject1.getJSONObject("data");
            JSONObject getMe = data.getJSONObject("getMe");
            user.setUsername(getMe.getStr("account"));
            user.setRealname(getMe.getStr("name"));
            user.setWorkNo(getMe.getStr("id"));
            user.setEmail(getMe.getStr("email"));
        }
    }

    @Override
    public Result<?> dataSources(TokenCheckTokenVaildModel checkTokenValid, List<String> groupIds) {
        RequestDictDto dto = new RequestDictDto();
        dto.setGroupIds(groupIds);
        return getCCDict(checkTokenValid.getAccessToken(), dto);
    }

    public Result<?> getCCDict(String accessToken, RequestDictDto dto) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        header.put("Authorization", "Bearer " + accessToken);
        String jsonStr = JSONUtil.toJsonStr(dto);
        String resul = HttpUtil.createPost(dictPath).addHeaders(header).body(jsonStr).execute().body();
        if (StrUtil.isBlank(resul)) {
            return Result.OK();
        }
        // 判断返回的结果是否是 JSON 格式
        if (JSONUtil.isTypeJSON(resul)) {
            if (JSONUtil.isTypeJSONArray(resul)) {
                // 如果是 JSON 数组
                try {
                    List<Map> dictItems = JSONUtil.toList(resul, Map.class);
                    return Result.OK(dictItems);
                } catch (Exception e) {
                    return Result.error("JSON数组解析失败：" + e.getMessage());
                }
            } else if (JSONUtil.isTypeJSONObject(resul)) {
                // 如果是 JSON 对象
                try {
                    Map<String, Object> responseMap = JSONUtil.parseObj(resul);
                    return Result.OK(responseMap);
                } catch (Exception e) {
                    return Result.error("JSON对象解析失败：" + e.getMessage());
                }
            }
        }
        return Result.OK(resul);
    }


}
