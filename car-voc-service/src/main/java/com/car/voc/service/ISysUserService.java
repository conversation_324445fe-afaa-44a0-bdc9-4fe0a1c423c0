package com.car.voc.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.voc.common.Result;
import com.car.voc.entity.SysUser;
import com.car.voc.model.*;
import com.car.voc.vo.SysUserListVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName ISysUserService.java
 * @Description TODO
 * @createTime 2022年09月06日 11:29
 * @Copyright voc
 */
public interface ISysUserService extends IService<SysUser> {

    Result<JSONObject> userInfo(SysUser sysUser, Result<JSONObject> result);

    Result<JSONObject> login(SysLoginModel sysLoginModel);

    /**
     * 校验用户是否有效
     *
     * @param sysUser
     * @return
     */
    Result checkUserIsEffective(SysUser sysUser);

    LoginUser getUserByName(String username);



    /**
     * 获取用户的授权角色
     * @param username
     * @return
     */
    public List<String> getRole(String username);

   public Map<String, String> getDepNamesByUserIds(List<String> userIds);

   public Result<SysUser> add(SysUserModel sysUserModel);




    /**
     * 添加用户和用户角色关系
     * @param user
     * @param roles
     */
    public void addUserWithRole(SysUser user,String roles);
    /**
     * 添加用户和用户部门关系
     * @param user
     * @param selectedParts
     */
    void addUserWithDepart(SysUser user, String selectedParts);

   public Result<SysUser> edit(SysUserModel sysUserModel);


    /**
     * 修改用户和用户角色关系
     * @param user
     * @param roles
     */
    public void editUserWithRole(SysUser user,String roles);
    /**
     * 编辑用户和用户部门关系
     * @param user
     * @param departs
     */
    void editUserWithDepart(SysUser user, String departs);


    /**
     * 删除用户
     * @param userId
     * @return
     */
    public boolean deleteUser(String userId);


    public Result<List<String>> queryUserRole(String userid);

   public Result<Boolean> checkOnlyUser(SysUser sysUser);
    /**
     * 修改密码
     *
     * @param sysUser
     * @return
     */
    public Result<?> changePassword(SysUser sysUser);

   public Result<List<DepartIdModel>> getUserDepartsList(String userId);

   public Result<List<SysUser>> queryUserByDepId(String id, String realname);

   public Result<?> resetPassword(UserUpdetePasModel userUpdetePasModel);
   public Result<?> adminUpdatePassword(UserUpdetePasModel userUpdetePasModel);

   public Result<IPage<SysUserListVo>> queryPageList(SysUserModel user, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Result<JSONObject> userInfoPus();

    Result<?> ticketByEmployeeId(ThirdLoginModel sysLoginModel);

    List<String> listUserPermission(String id);

    Result<?> checkTokenVaild(TokenCheckTokenVaildModel checkTokenVaild, HttpServletRequest request);

    Result<?> dataSources(TokenCheckTokenVaildModel checkTokenValid,List<String> groupIds);
}
