package com.car.voc.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 第三方登录
 */
@Data
public class ThirdLoginModel implements Serializable {
    private static final long serialVersionUID = 4098628709290780891L;


    /**
     * appKey
     */
    @ApiModelProperty(value="appKey",required = true)
    private String appKey;

    /**
     * 第三方登录 employeeId
     */
    @ApiModelProperty(value="employeeId",required = true)
    private String employeeId;

    /**
     * 请求时间戳-毫秒
     */
    @ApiModelProperty(value="请求时间戳-毫秒",required = true)
    private Long timestamp;

    /**
     *  签名
     */
    @ApiModelProperty(value="签名",required = true)
    private String signature;




}
