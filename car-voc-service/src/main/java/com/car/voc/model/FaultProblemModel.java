package com.car.voc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 *
 * @version 1.0.0
 * @ClassName FaultProblem.java
 * @Description TODO
 * @createTime 2022年10月09日 11:50
 * @Copyright voc
 */
@Data
@ApiModel(value="故障问题对象", description="VOC_FAULT_PROBLEM")
public class FaultProblemModel {


    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**name*/

    @ApiModelProperty(value = "name")
    private String name;
    /**code*/

    @ApiModelProperty(value = "code")
    private String code;

    /**pId*/

    @ApiModelProperty(value = "pid")
    private String pid;
    /**级别*/

    @ApiModelProperty(value = "级别")
    private Integer level;
    /**是否有子节点*/

    @ApiModelProperty(value = "是否有子节点")
    private Integer hasChild;
    /**orderBy*/

    @ApiModelProperty(value = "orderBy")
    private Integer orderBy;
    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
    /**delFlag*/

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;

    @ApiModelProperty(value = "是否应用")
    private boolean enable;

    @ApiModelProperty(value = "严重性(dictCode=seriousness_type)")
    @Dict(dicCode = "seriousness_type")
    private Integer seriousness;


    @ApiModelProperty(value = "最后一级指标")
    private String lastTagCode;
}
