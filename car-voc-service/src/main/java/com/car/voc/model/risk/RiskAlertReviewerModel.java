package com.car.voc.model.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName RiskAlertReviewerModel.java
 * @Description TODO
 * @createTime 2023年02月06日 10:46
 * @Copyright voc
 */
@Data
public class RiskAlertReviewerModel {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "审核人员用户id")
    private String reviewerUserId;
    @ApiModelProperty(value = "审核人员组织id")
    private String reviewerDepartId;

//    @ApiModelProperty(value = "接收人员")
//    private List<RiskProcessRecipientModel>  receivingUsers;

    @ApiModelProperty(value = "品牌标签")
    private List<RiskAlertReviewerBrandTagModel> brandTagList;

    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value="是否钉钉推送")
    private boolean sendDingtalk;
}
