package com.car.voc.model;

import com.car.voc.entity.TagDepartRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
@ApiModel(value="业务指标对象", description="业务指标对象")
public class VocBusinessTagListQueryModel implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "id编辑时传入")
	private String id;
	@ApiModelProperty(value = "标签名称")
	private String name;
	@ApiModelProperty(value = "英文名称")
	private String nameEn;
	@ApiModelProperty(value = "编码")
	private String tagCode;
	@ApiModelProperty(value = "备注")
	private String remark;
	@ApiModelProperty(value = "父级节点")
	private String pid;
	@ApiModelProperty(value = "能源类型: 0为全部，1为燃油车，2为新能源")
	private String associatedEnergy;
	@ApiModelProperty(value = "是否应用,1为是")
	private Integer enable;
	@ApiModelProperty(value = "咨询指标:标签类型:0:通用  1:咨询")
	private String tagType;
	@ApiModelProperty(value = "相关描述")
	private String relatedDescription;

	@ApiModelProperty(value = "关联部门Ids")
	private Set<TagDepartRelation> departIds;

	@ApiModelProperty(value = "搜索关键词")
	String searchKeyword;


}
