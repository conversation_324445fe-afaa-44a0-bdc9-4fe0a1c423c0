package com.car.voc.model;

import com.car.stats.model.ComFilterCriteriaModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * @version 1.0.0
 * @ClassName NPSFilterCriteriaModel.java
 * @Description TODO
 * @createTime 2023年07月31日 13:22
 * @Copyright voc
 */
@Data
@Builder
@ApiModel(value = "NPS分析筛选条件入参")
public class NPSFilterCriteriaModel extends ComFilterCriteriaModel {
    public static final Integer TOPN_DEFAULT_VALUE = 50;
    public static final String CARSERIESDISTRIBUTIONTYPE_DEFAULT_VALUE = "1";
    static final List<String> EMOTION_VALUES = Arrays.asList("正面", "中性", "负面");
    String startChianDate;
    String endChianDate;
    @ApiModelProperty(value = "一级标签")
    String firstDimensionCode;
    Set<String> firstDimensionCodes;
    @ApiModelProperty(value = "二级标签")
    String secondDimensionCode;
    Set<String> secondDimensionCodes;
    @ApiModelProperty(value = "三级标签")
    String thirdDimensionCode;

    String dimensionRsLevel;
    /**
     * 品牌
     */

    @ApiModelProperty(value = "日期单位(0为周，1为月，2为季，3为年度，-1为自定义)", example = "1")
    private Integer dateUnit;
    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    private List<String> carSeriesCode;
    /**
     * 拥车期：默认全部，分为半年内、0.5~1 年、1~2 年、2~3 年、3~4 年、4~5 年
     */
    @ApiModelProperty(value = "拥车期")
    private String carSpecificUYears;
    /**
     * 渠道：即对应调研数据的来源，如电话、线上；
     */
    @ApiModelProperty(value = "渠道")
    private List<String> channel;
    /**
     * 阶段：默认提车，维保;
     */
    @ApiModelProperty(value = "阶段")
    private String stage;
    /**
     * 能源类型：默认全部：燃油车、新能源；
     */
    @ApiModelProperty(value = "能源类型")
    private String energyType;
    /**
     * 分类：默认全部：分为门店、品牌；
     */
    @ApiModelProperty(value = "分类")
    private String classify;
    /**
     * 默认全部，分为男、女。
     */
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 性感
     */
    @ApiModelProperty(value = "性感")
    private String emotion;
    /**
     * 高频热词默认返回数量
     */
    @ApiModelProperty(value = "数据数量（默认50）")
    private Integer topn;
    @ApiModelProperty(value = "车系分布分析类型：1：NPS得分  2：情感")
    @Builder.Default
    private String carSeriesDistributionType = "1";


    @ApiModelProperty(value = "仅针对姓名、内容模糊查询")
    private String keyword;



}
