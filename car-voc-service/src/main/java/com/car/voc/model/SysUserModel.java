package com.car.voc.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户对象", description = "角色对象")
@Accessors(chain = true)
public class SysUserModel implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value="ID:编辑时传入")
    private String id;


    @ApiModelProperty(value="登录账号")
    private String username;

    @ApiModelProperty(value="角色id")
    private String roleId;

    @ApiModelProperty(value="姓名")
    private String realname;

    @ApiModelProperty(value="工号，唯一键")
    private String workNo;

    @ApiModelProperty(value="密码")
    private String password;

    @ApiModelProperty(value="电子邮件")
    private String email;


    @ApiModelProperty(value="联系电话")
    private String phone;

    @ApiModelProperty(value="部门Id")
    String departId;

    @ApiModelProperty(value="职位")
    String position;

    @ApiModelProperty(value="备注")
    String remark;


    @ApiModelProperty(value="状态(1：启用  2：禁用 ）")
    private Integer status;
    @ApiModelProperty(value = "搜索关键词")
    String searchKeyword;



}
