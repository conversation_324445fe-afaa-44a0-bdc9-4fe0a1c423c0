package com.car.voc.model.risk;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.car.stats.model.DateModel;
import com.car.voc.entity.VocRiskHandlingRecord;
import com.car.voc.vo.risk.ReviewerPermissionBrandCodeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * VOC_RISK_WARNING_RECORD
 *
 */
@ApiModel(value="风险警示记录表")
@Data
public class RiskAllTypesModel extends DateModel {
    private String mailServer;  //邮件服务器地址
    private String user;//登录用户名
    private String password;//登录密码
    private String stroepassword;
    private String path;
    private String picturePath;
    private String id;
    @ApiModelProperty(value="邮件图")
    private String mailChart;

    /**
     * 风险类型
     */
    @ApiModelProperty(value="风险类型")
    private String riskType;



    /**
     * 警示时间
     */
    @ApiModelProperty(value="警示时间")
    private Date createTime;

    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 风险名称
     */
    @ApiModelProperty(value="风险名称")
    private String riskName;
    private String dlrName;
    private String dlrCode;

    /**
     * 相关提及量
     */
    @ApiModelProperty(value="相关提及量")
    private BigDecimal statistic;

    /**
     * 风险点Id
     */
    @ApiModelProperty(value="风险点Id")
    private String riskId;

    /**
     * 风险点Id
     */
    @ApiModelProperty(value="风险点Id")
    private List<String> riskIdList;

    /**
     * 发声用户
     */
    @ApiModelProperty(value="发声用户")
    private Long userNum;
    @ApiModelProperty(value="1为审核，2为处理")
    private Integer tagType;
    /**
     * 状态(0:待审核,1:已审核,2:待处理,3:已处理,4:处理中,5:已取消,6已撤回，7已完成未全部完成)
     */
    @ApiModelProperty(value="状态(0:待审核,1:已审核,2:待处理,3:已处理,4:处理中,5:已取消,6已撤回，7已完成未全部完成)")
    private Integer riskState;

    /**
     * 状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)
     */
    @ApiModelProperty(value="状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)")
    private List<String> riskStateStr;

    /**
     * 审核时间
     */
    @ApiModelProperty(value="审核时间")
    private Date auditTime;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核员userId")
    private String auditUserId;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核员组织Id")
    private String auditDepartId;
    private String userId;




    /**
     * 确认时间
     */
    @ApiModelProperty(value="确认时间")
    private Date confirmationTime;

    /**
     * 是否构成风险
     */
    @ApiModelProperty(value="是否构成风险")
    private boolean ifRisk;

    /**
     * 是否构成风险
     */
    @ApiModelProperty(value="是否构成风险,0不是风险，1是风险")
    private String ifRiskType = "1";

    @ApiModelProperty(value = "pageNo")
    Integer pageNo;
    @ApiModelProperty(value = "pageSize")
    Integer pageSize;
    @ApiModelProperty(value="执行处理信息")
    List<VocRiskHandlingRecord> processors;
    public RiskAllTypesModel() {
        this.pageNo = 1;
        this.pageSize = 10;
    }


    @ApiModelProperty(value="调整后风险等级")
    private String riskLevel;

    @ApiModelProperty(value="调整后风险等级")
    private String newRiskLevel;

    @ApiModelProperty(value="标签筛选条件")
    private String tag;

    @ApiModelProperty(value="类型筛选条件")
    private String questionType;

    @ApiModelProperty(value="风险等级筛选条件")
    private String riskLevelKey;

    @ApiModelProperty(value="问题进度筛选条件")
    private String progressLabel;

    @ApiModelProperty(value="部门筛选条件")
    private String aduitDepartId;

    @ApiModelProperty(value="频次筛选条件")
    private String processingFrequency;

    @ApiModelProperty(value="洞察周期筛选条件")
    private String warnPeriod;
    
    @ApiModelProperty(value = "排序字段")
    private String column;

    @ApiModelProperty(value = "排序类型asc,desc")
    private String order;

    @ApiModelProperty(value = "权限")
    @Builder.Default
    private List<ReviewerPermissionBrandCodeVo> reviewerPermission = new ArrayList<>();

    @ApiModelProperty(value = "审核状态（0，1），处理状态（2，3，4）")
    private List<String> state;

    private String riskTypeStatus;

    private String processStatus;

    private String processUserId;

    private String groupByName;

    private String brandCode;

    private List<String> riskTypeList;

    private List<String> channelIds;

    private List<String> dataSources;
    @ApiModelProperty(value = "渠道权限", hidden = true)
    Set<Object> channelIdPowers;

    private boolean qualityBoolean;

    private String roleId;
}
