package com.car.voc.model.risk;

import com.car.voc.entity.VocRiskProcessRecipientBase;
import com.car.voc.entity.VocRiskProcessRecipientTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @version 1.0.0
 * @ClassName RiskAlertReviewerModel.java
 * @Description TODO
 * @createTime 2023年02月06日 10:46
 * @Copyright voc
 */
@Data
public class RiskAlertReviewerBrandTagModel {
    /**
     * 风险审核人员关联品牌id
     */
    @ApiModelProperty(value = "风险审核人员关联品牌id")
    private String recipientId;

    //品牌code
    private String brandCode;

    //品牌下的业务标签选择
    private List<VocRiskProcessRecipientTag> recipientTagList;

    @ApiModelProperty(value = "质量标签")
    private VocRiskProcessRecipientBase recipientQualityTag;

    @ApiModelProperty(value = "top用户")
    private VocRiskProcessRecipientBase recipientTopUser;

    @ApiModelProperty(value = "救援故障")
    private VocRiskProcessRecipientBase recipientRescue;
    @ApiModelProperty(value = "网点风险")
    private VocRiskProcessRecipientBase recipientBranches;

}
