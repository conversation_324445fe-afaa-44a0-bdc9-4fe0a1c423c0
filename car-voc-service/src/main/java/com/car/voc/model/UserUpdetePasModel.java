package com.car.voc.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "用户修改密码对象")
@Accessors(chain = true)
public class UserUpdetePasModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="登录账号")
    private String username;

    @ApiModelProperty(value="旧密码")
    private String oldpassword;


    @ApiModelProperty(value="新密码")
    private String password;

    @ApiModelProperty(value="确认密码")
    private String confirmpassword;



}
