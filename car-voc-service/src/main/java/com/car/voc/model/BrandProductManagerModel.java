package com.car.voc.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(value="添加车型请求对象", description="添加车型请求对象")
public class BrandProductManagerModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;
	/**车辆类型*/
    @ApiModelProperty(value = "车辆类型(dictCode=car_type)存中文")
    private String carType;
	/**车辆空间属性*/
	//@Excel(name = "车辆空间属性", width = 15)
    @ApiModelProperty(value = "车辆空间属性(dictCode=car_space_type)存中文")
    private String carSpaceType;
	/**类型，1：品牌 2：车系 3：车型*/
	//@Excel(name = "类型，1：品牌 2：车系 3：车型", width = 15)
    @ApiModelProperty(value = "类型，1：品牌 2：车系 ")
    private Integer type;
	/**中文名称*/
	//@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "中文名称")
    private String name;
	/**英文名*/
	//@Excel(name = "英文名", width = 15)
    @ApiModelProperty(value = "英文名")
    private String englishName;
	/**描述*/
	//@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String remark;
	/**展示图片*/
	//@Excel(name = "展示图片", width = 15)
    @ApiModelProperty(value = "展示图片")
    private String showImg;
    @ApiModelProperty(value = "父级节点")
	private String pId;
    @ApiModelProperty(value = "是否应用,1为是")
    private Integer enable;

    private String code;
    @ApiModelProperty(value = "能源类型(dictCode=energy_type)存中文")
    private String energyType;
    @ApiModelProperty(value = "供能类型(dictCode=energy_supply_type)存中文")
    private String energySupplyType;

    @ApiModelProperty(value = "别名(多个以(,)分隔")
    private String alias;
    @ApiModelProperty(value = "所属车型组Id")
    private String modelGroupId;

    private String orderBy;

   /* public void setPId(String pId) {
        System.out.println("setPId:"+pId);
        this.pId = pId;
    }
*/
    public String getpId() {
        System.out.println("getpId:"+pId);
        return this.pId;
    }

}
