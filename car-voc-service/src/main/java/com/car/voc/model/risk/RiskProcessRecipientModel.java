package com.car.voc.model.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName RiskProcessRecipientModel.java
 * @Description TODO
 * @createTime 2023年02月06日 10:50
 * @Copyright voc
 */
@Data
public class RiskProcessRecipientModel {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "审核id")
    private String auditId;

    @ApiModelProperty(value = "接收人员用户id")
    private String processUserId;
    @ApiModelProperty(value = "接收人员组织id")
    private String processDepartId;

    @ApiModelProperty(value = "品牌范围")
    private String brandCodes;
}
