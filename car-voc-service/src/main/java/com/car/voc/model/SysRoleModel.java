package com.car.voc.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "角色对象", description = "角色对象")
public class SysRoleModel implements Serializable {

    @ApiModelProperty(value="查询来源类型1为查询全量角色信息")
    Integer queryType;

    @ApiModelProperty(value="ID:编辑时传入")
    private String id;

    @ApiModelProperty(value="角色名称")
    private String roleName;

    @ApiModelProperty(value="备注")
    private String description;

    @ApiModelProperty(value="关联部门；部门ID")
    private String departId;

    @ApiModelProperty(value="角色类型")
    private String roleType;

    @ApiModelProperty(value="关联车系， 传code以后英文逗号(,)分隔")
    private List<String> seriesIds;

    @ApiModelProperty(value="关联渠道ID，以后英文逗号(,)分隔")
    private List<String> channelIds;

    @ApiModelProperty(value="关联业务标签，传code以后英文逗号(,)分隔")
    private List<String> tagIds;

    @ApiModelProperty(value="关联业务标签，传code以后英文逗号(,)分隔")
    private List<String> areaIds;


    @ApiModelProperty(value="功能权限：是否可以导出 true为是")
    private boolean isExport;

    @ApiModelProperty(value="功能权限：是否可以下载 true为是")
    private boolean isDownload;
    @ApiModelProperty(value="功能权限是否脱敏：是否可以下载 true为是")
    private boolean desensitization;
    @ApiModelProperty(value="功能权限是否脱敏Vin：是否可以下载 true为是")
    private boolean desensitizationVin;

    @ApiModelProperty(value="质量标签 true为是")
    private boolean isQuality;

    @ApiModelProperty(value="选中的菜单权限id： 多个以后英文逗号(,)分隔")
    private List<String> permissionIds;
    @ApiModelProperty(value="状态 true:启用 false：禁用")
    private boolean roleStatus;

    @ApiModelProperty(value="关联业务标签，传code以后英文逗号(,)分隔")
    private String searchKeyword;

    @ApiModelProperty(value="是否拥有所有权限 true:是")
    private boolean isAllPermission;

    @ApiModelProperty(value="品牌code")
    private String brandCode;

    public boolean isExport() {
        return isExport;
    }

    public void setExport(boolean export) {
        isExport = export;
    }

    public boolean isDownload() {
        return isDownload;
    }

    public void setDownload(boolean download) {
        isDownload = download;
    }



}
