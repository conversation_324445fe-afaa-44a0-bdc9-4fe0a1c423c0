package com.car.voc.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "部门对象", description = "部门对象")
public class SysDepartModel implements Serializable {
	@ApiModelProperty(value="ID:编辑时传入")
	private String id;

	@ApiModelProperty(value="部门名称")
	private String departName;

	@ApiModelProperty(value="简称")
	private String departNameAbbr;

	@ApiModelProperty(value="码框关联：1全部；0部门关联")
	String tagRelation;

	@ApiModelProperty(value="车型关联：1全部；")
	String carModelRelation;

	@ApiModelProperty(value="负责人名")
	String personInCharge;

	@ApiModelProperty(value="联系电话")
	private String mobile;

	@ApiModelProperty(value="邮箱地址")
	String mailbox;

	@ApiModelProperty(value="备注")
	private String memo;

	@ApiModelProperty(value="状态 ture:启用")
	boolean status;

	private String searchKeyword;

	@ApiModelProperty("品牌code")
	private List<String> brandCodeList;

}
