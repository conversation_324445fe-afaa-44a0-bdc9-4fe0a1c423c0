package com.car.voc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="车型组关联", description="车型组关联")
public class ModelGroupRelationModel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "车型组id")
    private String modelGroupId;
    @ApiModelProperty(value = "品牌code")
    private String brandCode;
    @ApiModelProperty(value = "车系code")
    private String carSeriesCode;
}
