package com.car.voc.model;


import com.car.voc.entity.SysPermission;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 树形列表用到
 */
@Data
public class TreeModel implements Serializable {

	private static final long serialVersionUID = 4013193970046502756L;

	private String key;

	private String title;

	private String slotTitle;

	private boolean isLeaf;

	private String icon;

	private Integer ruleFlag;

	private Map<String,String> scopedSlots;

	private List<TreeModel> children;



	public TreeModel() {

	}

	public TreeModel(SysPermission permission) {
		this.key = permission.getId();
		this.icon = permission.getIcon();
		this.parentId = permission.getParentId();
		this.title = permission.getName();
		this.slotTitle =  permission.getName();
		this.value = permission.getId();
		this.isLeaf = permission.isLeaf();
		this.label = permission.getName();
		if(!permission.isLeaf()) {
			this.children = new ArrayList<>();
		}
	}

	public TreeModel(String key,String parentId,String slotTitle,Integer ruleFlag,boolean isLeaf) {
		this.key = key;
		this.parentId = parentId;
		this.ruleFlag=ruleFlag;
		this.slotTitle =  slotTitle;
		Map<String,String> map = new HashMap<String,String>();
		map.put("title", "hasDatarule");
		this.scopedSlots = map;
		this.isLeaf = isLeaf;
		this.value = key;
		if(!isLeaf) {
			this.children = new ArrayList<>();
		}
	}
	private String parentId;

	private String label;

	private String value;

	public boolean IsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(boolean leaf) {
		isLeaf = leaf;
	}
}
