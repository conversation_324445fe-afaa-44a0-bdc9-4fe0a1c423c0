package com.car.voc.shiro.filters;

import cn.hutool.json.JSONUtil;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.model.LoginUser;
import com.car.voc.shiro.JwtToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.web.filter.authc.BasicHttpAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * @Description: 鉴权登录拦截器
 *
 * @Date: 2018/10/7
 **/
@Slf4j
public class JwtFilter extends BasicHttpAuthenticationFilter {

    private boolean allowOrigin = true;

    public JwtFilter() {
    }

    public JwtFilter(boolean allowOrigin) {
        this.allowOrigin = allowOrigin;
    }

    /**
     * 执行登录认证
     *
     * @param request
     * @param response
     * @param mappedValue
     * @return
     */
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        try {
             executeLogin(request, response);
            return true;
        } catch (Exception e) {
            log.error("",e);
        }
        return false;
    }

    @Override
    protected boolean sendChallenge(ServletRequest request, ServletResponse response) {
        try {
            HttpServletResponse httpResponse = WebUtils.toHttp(response);
            httpResponse.setStatus(401);
            httpResponse.setContentType("application/json;charset=utf-8");
            httpResponse.getWriter().write(JSONUtil.toJsonStr(Result.error(401,"Token失效，请重新登录")));
        } catch (IOException e) {
            log.error("sendChallenge()方法异常:",e);
        }
        return false;
    }


    /**
     *
     */
    @Override
    protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse= (HttpServletResponse) response;

        String token = httpServletRequest.getHeader(CommonConstant.X_ACCESS_TOKEN);
        // update-begin- for：JT-355 OA聊天添加token验证，获取token参数
        if (token == null) {
            token = httpServletRequest.getParameter("token");
        }
        // update-end- for：JT-355 OA聊天添加token验证，获取token参数

        JwtToken jwtToken = new JwtToken(token);

        // 提交给realm进行登入，如果错误他会抛出异常并被捕获
        getSubject(request, response).login(jwtToken);
        // 如果没有抛出异常则代表登入成功，返回true

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser==null){
            return true;
        }else {
            List<String> permissionSet = loginUser.getUserPermission();
            String uri=httpServletRequest.getRequestURI();
            boolean auth=false;
            permissionSet.add("/sys/permission/getUserPermissionByToken");
            permissionSet.add("/stats/labelDetails");
            permissionSet.add("/stats/filterCriteria");
            permissionSet.add("/voc/correction");
            permissionSet.add("/sys/dict");
            permissionSet.add("/sys/log");
            permissionSet.add("/sys/dict");
            permissionSet.add("/sys/common");
            permissionSet.add("/sys/parameters");
            permissionSet.add("/sys/ticketByEmployeeId");
            permissionSet.add("/stats/home");
            permissionSet.add("/api/stats/filterCriteria/getTime");
            permissionSet.add("/api/sys/oss/getStsToken");
            permissionSet.add("/api/sys/oss/getStsToken");
            permissionSet.add("/sys/user/updatePassword");
            permissionSet.add("/voc/brandProductManager");
            permissionSet.add("/cc/dataSources");

            for (String s : permissionSet) {
                if(s.contains(",")){
                    for (String s1 : s.split(",")) {
                        if (uri.contains(s1)){
                            auth= true;
                        }
                    }
                }else {
                    if (uri.contains(s)){
                        auth= true;
                    }
                }
            }
            if (auth){
                return true;
            }else {
                httpServletResponse.sendError(403,"拒绝访问!");
                return false;

            }
        }

//        return true;
    }

    /**
     * 对跨域提供支持
     */
    @Override
    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        if (allowOrigin) {
            httpServletResponse.setHeader("Access-control-Allow-Origin", httpServletRequest.getHeader("Origin"));
            httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS,PUT,DELETE");
            httpServletResponse.setHeader("Access-Control-Allow-Headers", httpServletRequest.getHeader("Access-Control-Request-Headers"));
            //update-begin for:issues/I1TAAP 前后端分离，shiro过滤器配置引起的跨域问题
            // 是否允许发送Cookie，默认Cookie不包括在CORS请求之中。设为true时，表示服务器允许Cookie包含在请求中。
            httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
            //update-end for:issues/I1TAAP 前后端分离，shiro过滤器配置引起的跨域问题
        }
        // 跨域时会首先发送一个option请求，这里我们给option请求直接返回正常状态
        if (httpServletRequest.getMethod().equals(RequestMethod.OPTIONS.name())) {
            httpServletResponse.setStatus(HttpStatus.OK.value());
            return false;
        }
        return super.preHandle(request, response);
    }
}
