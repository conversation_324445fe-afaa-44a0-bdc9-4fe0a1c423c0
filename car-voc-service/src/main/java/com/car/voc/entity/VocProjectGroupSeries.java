package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;


/**
 * 项目组品牌车系关联表(VocProjectGroupSeries)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:33:50
 */
@Data
@TableName("voc_project_group_series")
public class VocProjectGroupSeries extends Model<VocProjectGroupSeries> {
    /**
     * 主键ID
     */
    @TableId
    private String id;
    /**
     * 项目组ID
     */
    @TableField(value = "group_id")
    private String groupId;
    /**
     * 品牌编码
     */
    @TableField(value = "brand_code")
    private String brandCode;
    /**
     * 车系编码
     */
    @TableField(value = "series_code")
    private String seriesCode;

    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField(value = "del_flag")
    private Integer delFlag;
}

