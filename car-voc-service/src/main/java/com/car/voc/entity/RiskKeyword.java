package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TC_RISK_KEYWORD
 *
 */
@ApiModel(value="风险关键词")
@Data
@TableName("TC_RISK_KEYWORD")
public class RiskKeyword implements Serializable {
    private String id;

    private String riskKeyword;
    @TableField(exist = false)
    private String searchKeyword;
    private String classify;
    private String extendedWord;

    private Double weight;
    private Integer delFlag;

    private Date createTime;

    private static final long serialVersionUID = 1L;

    private String brandCode;

    @TableField(exist = false)
    private List<String> brandCodes;

}
