package com.car.voc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * VOC_RISK_HANDLING_RECORD
 *
 */
@ApiModel(value="风险处理记录表")
@Data
public class VocRiskHandlingRecord implements Serializable {
    private String id;
    private String mailChart;

    /**
     * 审核id
     */
    @ApiModelProperty(value="警示风险id")
    private String warningRiskId;

    /**
     * 处理接收人员id
     */
    @ApiModelProperty(value="处理接收人员id")
    private String processUserId;
    @TableField(exist = false)
    private String processUserName;

    /**
     * 邮件内容
     */
    @ApiModelProperty(value="邮件内容")
    private String mailContent;

    /**
     * 邮件地址
     */
    @ApiModelProperty(value="邮件地址")
    private String mailAddress;

//    @ApiModelProperty(value="邮件图")
//    private String mailChart;

    /**
     * 是否发送邮件
     */
    @ApiModelProperty(value="是否发送邮件")
    private boolean sendMail;
    @ApiModelProperty(value="是否钉钉推送")
    private boolean sendDingtalk;
    @ApiModelProperty(value="是否发送短信")
    private boolean sendMessage;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastReminderTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 处理人员的组织id
     */
    @ApiModelProperty(value="处理人员的组织id")
    private String processDepartId;
    @TableField(exist = false)
    private String processDepartName;
    @ApiModelProperty(value = "标签绑定人员类型：1.部门，2.项目组")
    private Integer bindType;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value="计划完成时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date planCompletionTime;
    @ApiModelProperty(value="处理时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /**
     * 解决方案与说明
     */
    @ApiModelProperty(value="解决方案与说明")
    private String solutionDescription;

    /**
     * 解决方案附件
     */
    @ApiModelProperty(value="解决方案附件")
    private String solutionAttachment;

    @ApiModelProperty(value="处理状态0:未处理，1：已处理")
    private Integer processStatus;


    /**
     * 确认处理时间
     */
    @ApiModelProperty(value="确认(处理)时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date confirmproTime;

    /**
     * 确认处理时间
     */
    @ApiModelProperty(value="确认(处理)时间人员id")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date confirmproUserId;

    /**
     * 解决方案与说明
     */
    @ApiModelProperty(value="确认(处理)解决方案与说明")
    private String confirmSolutionDescription;

    private Integer reminderFrequency;
    /**
     * 解决方案附件
     */
    @ApiModelProperty(value="确认(处理)解决方案附件")
    private String confirmSolutionAttachment;

    private static final long serialVersionUID = 1L;
}
