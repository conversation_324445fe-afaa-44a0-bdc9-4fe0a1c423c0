package com.car.voc.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName TF_DWD_VOC_SENTENCE_REPAIR
 */
@Data
public class TfDwdVocSentenceRepair implements Serializable {
    /**
     *
     */
    private String id;

    /**
     *
     */
    private String balanceNo;

    /**
     *
     */
    private String serviceCode;

    /**
     *
     */
    private String roNo;

    /**
     *
     */
    private String oneId;

    /**
     *
     */
    private Long userType;

    /**
     *
     */
    private Long userLevel;

    /**
     *
     */
    private String isOneId;

    /**
     *
     */
    private String displayName;

    /**
     *
     */
    private String brandCode;

    /**
     *
     */
    private String carSeriesCode;

    /**
     *
     */
    private String carGroup;

    /**
     *
     */
    private String carType;

    /**
     *
     */
    private String firstDimensionCode;

    /**
     *
     */
    private String secondDimensionCode;

    /**
     *
     */
    private String threeDimensionCode;

    /**
     *
     */
    private String topicCode;

    /**
     *
     */
    private String province;

    /**
     *
     */
    private String areaCode;

    /**
     *
     */
    private Date publishDate;

    /**
     *
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}
