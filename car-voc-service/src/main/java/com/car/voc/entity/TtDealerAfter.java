package com.car.voc.entity;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName TT_DEALER_AFTER
 */
@Data
public class TtDealerAfter implements Serializable {
    /**
     *
     */
    private String brand;

    /**
     *
     */
    private String afterSalesCode;

    /**
     *
     */
    private String dealerName;

    /**
     *
     */
    private String dealerShortname;

    /**
     *
     */
    private String lastAfterSaleCode;

    /**
     *
     */
    private String parentCode;

    /**
     *
     */
    private String areaCode;

    /**
     *
     */
    private String agencyCode;

    /**
     *
     */
    private String provinceCode;

    /**
     *
     */
    private String cityCode;

    /**
     *
     */
    private String countyCode;

    /**
     *
     */
    private String address;

    private static final long serialVersionUID = 1L;
}
