package com.car.voc.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * VOC_RISK_WARNING_RULES_DETAILED
 *
 */
@ApiModel(value="警示规则明细表")
@Data
public class VocRiskWarningRulesDetailedNew {

    @ApiModelProperty(value="洞察周期")
    private List<VocRiskWarningRulesDetailedInsightCycle> insightCycle;

    @ApiModelProperty(value="风险等级")
    private List<VocRiskWarningRulesDetailedRiskLevel> riskLevel;

}
