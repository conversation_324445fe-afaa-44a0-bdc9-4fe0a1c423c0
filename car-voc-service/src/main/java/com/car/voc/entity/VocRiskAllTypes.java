package com.car.voc.entity;

import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocQualityRiskF;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.voc.common.enums.RiskTypeEnum;
import com.car.voc.common.util.CalculatorUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * VOC_RISK_WARNING_RECORD
 *
 */
@ApiModel(value="generate.VocRiskWarningRecord风险警示记录表")
@Data
public class VocRiskAllTypes implements Serializable {
    private String id;

    /**
     * 风险类型
     */
    @ApiModelProperty(value="风险类型")
    private String riskType;

    /**
     * 警示时间
     */
    @ApiModelProperty(value="警示时间")
    private Date createTime;

    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 风险名称
     */
    @ApiModelProperty(value="风险名称")
    private String riskName;

    /**
     * 相关提及量
     */
    @ApiModelProperty(value="相关提及量")
    private BigDecimal statistic;

    @ApiModelProperty(value="警示次数")
    private Integer warningNum;

    /**
     * 风险点Id
     */
    @ApiModelProperty(value="风险点Id")
    private String riskId;

    /**
     * 发声用户
     */
    @ApiModelProperty(value="发声用户")
    private Integer userNum;

    /**
     * 状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)
     */
    @ApiModelProperty(value="状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)")
    private Integer riskState;

    /**
     * 审核时间
     */
    @ApiModelProperty(value="审核时间")
    private Date auditTime;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核员userId")
    private String auditUserId;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核员组织Id")
    private String auditDepartId;

    @ApiModelProperty(value="风险code或用户Id")
    private String risk;

    /**
     * 确认时间(处理完成)
     */
    @ApiModelProperty(value="处理完成时间")
    private Date confirmationTime;

    /**
     * 是否构成风险
     */
    @ApiModelProperty(value="是否构成风险")
    private boolean ifRisk;

    @ApiModelProperty(value="当前风险等级")
    private String riskLevel;

    @ApiModelProperty(value="新风险等级")
    private String newRiskLevel;

    @ApiModelProperty(value="品牌code")
    private String brandCode;

    @ApiModelProperty(value="洞察周期")
    private String statisticType;

    @ApiModelProperty(value="取消时间")
    private String cancelTime;

    private static final long serialVersionUID = 1L;

    public VocRiskAllTypes(DwdVocUserRisk userRisk) {
        this.riskType="3";
        this.delFlag=0;
        this.createTime=new Date();
        this.riskName=userRisk.getDisplayName();
        this.statistic=userRisk.getNegativeNum();
        this.riskId=userRisk.getId();
        this.risk=userRisk.getUserId();
        this.userNum= 1;
        this.warningNum=1;
        this.riskState=0;
        this.ifRisk=true;
        this.brandCode = userRisk.getBrandCode();
        this.statisticType = userRisk.getStatisticType();
    }

    public VocRiskAllTypes() {
    }

    public VocRiskAllTypes(DwdVocEmotionRisk eventRisk) {
        this.riskType="1";
        this.delFlag=0;
        this.createTime=new Date();
        this.riskName=eventRisk.getTopicCode();
        this.statistic=eventRisk.getNegativeNum();
        this.riskId=eventRisk.getId();
        this.risk=eventRisk.getTopicCode();
        this.userNum= 1;
        this.warningNum=1;
        this.riskState=0;
        this.ifRisk=true;
        this.brandCode = eventRisk.getBrandCode();
        this.statisticType = eventRisk.getStatisticType();
    }

    public VocRiskAllTypes(DwdVocQualityRiskF qualityRisk, RiskTypeEnum riskTypeEnum) {
        this.riskType=riskTypeEnum.getType();
        this.delFlag=0;
        this.createTime=new Date();
        this.riskName=qualityRisk.getTopicCode();
        this.statistic=qualityRisk.getTotalNum();
        this.riskId=qualityRisk.getId();
        this.risk=qualityRisk.getTopicCode();
        this.userNum= 1;
        this.warningNum=1;
        this.riskState=0;
        this.ifRisk=true;
        this.brandCode = qualityRisk.getBrandCode();
        this.statisticType = qualityRisk.getStatisticType();
    }

    public VocRiskAllTypes(DwdVocDealerRisk branchesRisk) {
        this.riskType=RiskTypeEnum.VocRiskBranches.getType();
        this.delFlag=0;
        this.createTime=new Date();
        this.riskName=branchesRisk.getDlrName();
        this.statistic=branchesRisk.getComplainNum();
        this.riskId=branchesRisk.getId();
        this.risk=branchesRisk.getDlrName();
        this.userNum= 1;
        this.warningNum=1;
        this.riskState=0;
        this.ifRisk=true;
        this.brandCode = branchesRisk.getBrandCode();
        this.statisticType = branchesRisk.getStatisticType();
    }
}
