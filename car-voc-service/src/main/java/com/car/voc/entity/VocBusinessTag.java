package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.common.aspect.annotation.Dict;
import com.car.voc.dto.BusinessTagImportDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: voc_business_tag
 *
 * @Date:   2021-03-30
 * @Version: V1.0
 */
@Data
@TableName("voc_business_tag")
@ApiModel(value="voc_business_tag对象", description="voc_business_tag")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VocBusinessTag implements Serializable {
    private static final long serialVersionUID = 1L;

	/**编号*/
	@TableId(type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "编号")
	private String id;
	/**名称*/
	//@Excel(name = "名称", width = 15)
	@ApiModelProperty(value = "名称")
	private String name;
	/**英文名称*/
	//@Excel(name = "英文名称", width = 15)
	@ApiModelProperty(value = "英文名称")
	private String nameEn;
	/**编码*/
	//@Excel(name = "编码", width = 15)
	@ApiModelProperty(value = "编码")
	private String tagCode;
	@TableField(exist = false)
	private String code;
	/**排序*/
	//@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
	private Integer orderBy;
	/**层级*/
	//@Excel(name = "层级", width = 15)
	@ApiModelProperty(value = "层级")
	private String hierarchy;
	/**行业*/
	//@Excel(name = "行业", width = 15, dicCode = "industry")
	//@Dict(dicCode = "industry")
	@ApiModelProperty(value = "行业")
	private String industryId;
	/**关联部门*/
	//@Excel(name = "关联部门", width = 15, dicCode = "department")
	//@Dict(dicCode = "department")
	@ApiModelProperty(value = "关联部门")
//	@TableField(exist = false)
	private String relatedDepartments;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "createTime")
	private Date createTime;
	/**标签数*/
	//@Excel(name = "标签数", width = 15)
	@ApiModelProperty(value = "标签数")
	private Integer tagData;
	/**备注*/
	//@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "相关描述")
	private String relatedDescription;
	/**yndel*/
	//@Excel(name = "yndel", width = 15)
	@ApiModelProperty(value = "yndel")
	private String yndel;
	/**是否有子节点*/
	//@Excel(name = "是否有子节点", width = 15)
	@ApiModelProperty(value = "是否有子节点")
	private String hasChild;
	/**父级节点*/
	//@Excel(name = "父级节点", width = 15)
	@ApiModelProperty(value = "父级节点")
	private String pid;
	private String brand;
	private String tagType;
	@ApiModelProperty(value = "能源类型")
	private String associatedEnergy;
	@ApiModelProperty(value = "是否应用")
	private boolean enable;
	@Dict(dicCode = "tag_order_type")
	private String orderType;
	@ApiModelProperty(value = "是否兜底")
	private Integer other;
//	标签作用域：0:全部 1:VOC 2:CC

	@Dict(dicCode = "tag_application_type")
	private String tagScope;

	@ApiModelProperty(value = "是否选中")
	@TableField(exist = false)
	@Builder.Default
	private Boolean selected = false;

	@ApiModelProperty(value = "处理部门id")
	@TableField(exist = false)
	private String processDepartId;

	@ApiModelProperty(value = "处理部门名称")
	@TableField(exist = false)
	private String processDepartName;

	@ApiModelProperty(value = "处理用户id")
	@TableField(exist = false)
	private String processUserId;

	@ApiModelProperty(value = "处理用户名称")
	@TableField(exist = false)
	private String processUserName;
	private String remark;

	public void setTag1Info(BusinessTagImportDto tag1) {
		this.nameEn=tag1.getTage1();
		this.orderBy=tag1.getOrderBy();
	}

	public void setTag2Info(BusinessTagImportDto tag2) {
		this.nameEn=tag2.getTage1();
		this.orderBy=tag2.getOrderBy();

	}
	public void setTag3Info(BusinessTagImportDto tag3) {
		this.nameEn=tag3.getTage1();
		this.orderBy=tag3.getOrderBy();

	}
	public void setTag4Info(BusinessTagImportDto tag4) {
		this.nameEn=tag4.getTage1();
		this.orderBy=tag4.getOrderBy();
		this.remark=tag4.getDescribe();
	}
}
