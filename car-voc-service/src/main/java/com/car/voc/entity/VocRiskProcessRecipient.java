package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * VOC_RISK_ALERT_REVIEWER
 *
 */
@ApiModel(value="generate.voc_risk_process_recipient警示审核人员")
@Data
@TableName(autoResultMap = true)
public class VocRiskProcessRecipient implements Serializable {
    private String id;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核id")
    private String auditId;

    @TableField(exist = false)
    private String processUserName;
    @TableField(exist = false)
    private String processUserNo;


    private Date createTime;

    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value = "品牌范围")
//    @TableField(typeHandler = FastjsonTypeHandler.class)
    private String brandCodes;

    private static final long serialVersionUID = 1L;

    public String getBrandCodes() {
        return brandCodes==null?"":brandCodes;
    }


}
