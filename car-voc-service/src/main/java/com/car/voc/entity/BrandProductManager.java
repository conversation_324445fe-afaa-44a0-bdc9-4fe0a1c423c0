package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 品牌产品管理
 * @Date: 2021-04-09
 * @Version: V1.0
 */
@Data
@TableName("voc_brand_product_manager")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "voc_brand_product_manager对象", description = "品牌产品管理")
public class BrandProductManager implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 删除状态(0-正常,1-已删除)
     */
    //@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常,1-已删除)")
    private Integer delFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 所属行业ID
     */
    //@Excel(name = "所属行业ID", width = 15)
    @ApiModelProperty(value = "所属行业ID")
    private String industry;
    /**
     * 车辆类型
     */
    //@Excel(name = "车辆类型", width = 15)
    @ApiModelProperty(value = "车辆类型")
    @Dict(dicCode = "car_type")
    private String carType;
    /**
     * 车辆空间属性
     */
    //@Excel(name = "车辆空间属性", width = 15)
    @ApiModelProperty(value = "车辆空间属性")
    @Dict(dicCode = "car_space_type")
    private String carSpaceType;
    /**
     * 类型，1：品牌 2：车系 3：车型
     */
    //@Excel(name = "类型，1：品牌 2：车系 3：车型", width = 15)
    @ApiModelProperty(value = "类型，1：品牌 2：车系 3：车型")
    private Integer type;
    /**
     * 关联品牌/车型/车系 ID
     */
    //@Excel(name = "关联品牌/车型/车系 ID", width = 15)
    @ApiModelProperty(value = "关联品牌/车型/车系 ID")
    private String relationId;
    /**
     * 中文名称
     */
    //@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "中文名称")
    private String name;
    /**
     * 英文名
     */
    //@Excel(name = "英文名", width = 15)
    @ApiModelProperty(value = "英文名")
    private String englishName;
    /**
     * 国家
     */
    //@Excel(name = "国家", width = 15)
    @ApiModelProperty(value = "国家")
    private String country;
    /**
     * 描述
     */
    //@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String remark;
    /**
     * 展示图片
     */
    //@Excel(name = "展示图片", width = 15)
    @ApiModelProperty(value = "展示图片")
    private String showImg;
    /**
     * 展示图片
     */
    //@Excel(name = "大屏图片", width = 15)
    @ApiModelProperty(value = "大屏图片")
    private String screenImg;
    @ApiModelProperty(value = "父级节点")
    private String pId;
    @ApiModelProperty(value = "所属行业文字")
    private String industryText;
    private Integer enable;


    private String code;
    private String hasChild;
    @TableField(exist = false)
    private String brandType;

    Integer sortNo;


    @ApiModelProperty(value = "能源类型(dictCode=energy_type)")
//    @Dict(dicCode = "energy_type")
    private String energyType;
    @ApiModelProperty(value = "供能类型(dictCode=energy_supply_type)")
//    @Dict(dicCode = "energy_supply_type")
    private String energySupplyType;

    @ApiModelProperty(value = "别名(多个以(,)分隔")
    private String alias;
    @ApiModelProperty(value = "所属车型组Id")
    private String modelGroupId;

    @TableField(exist = false)
    List<BrandProductManager> children;
    @TableField(exist = false)
    String relationship;
    @TableField(exist = false)
    String brandCode;
    @TableField(exist = false)
    String searchKeyword;

    /**
     * 排序
     */
    @TableField(exist = false)
    private Integer orderBy;

    public String getBrandCode() {
        if (code != null) {
            return this.code.substring(0, 3);
        }
        return this.brandCode;
    }
}
