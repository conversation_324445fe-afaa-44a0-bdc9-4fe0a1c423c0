package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.vo.DictVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dict_item")
public class SysDictItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 字典id
     */
    private String dictId;

    /**
     * 字典项文本
     */
    //@Excel(name = "字典项文本", width = 20)
    private String itemText;
    @TableField(exist = false)
    private List<DictVo> provinces;
    private String itemTextEn;
    private String itemKey;

    /**
     * 字典项值
     */
    //@Excel(name = "字典项值", width = 30)
    private String itemValue;

    /**
     * 描述
     */
    //@Excel(name = "描述", width = 40)
    private String description;

    /**
     * 排序
     */
    //@Excel(name = "排序", width = 15,type=4)
    private Integer sortOrder;


    /**
     * 状态（1启用 0不启用）
     */
    //@Dict(dicCode = "dict_item_status")
    private Integer status;

    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    private String brandCode;

    @ApiModelProperty("品牌code")
    @TableField(exist = false)
    private List<String> brandCodeList;


}
