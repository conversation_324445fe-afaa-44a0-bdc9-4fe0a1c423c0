package com.car.voc.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * VOC_RISK_WARNING_RULES_DETAILED
 */
@ApiModel(value = "警示规则明细表")
@Data
public class VocRiskWarningRulesDetailed {
    private String id;

    /**
     * 警示规则id
     */
    @ApiModelProperty(value = "警示规则id")
    private String warnRuleId;

    private Date createTime;

    private Date updateTime;
    private Date lastWarningTime;

    private BigDecimal delFlag;

    /**
     * 洞察周期(d,w,m,q,y)
     */
    @ApiModelProperty(value = "洞察周期(d,w,m,q,y)")
    private String insightCycle;

    /**
     * 提及量
     */
    @ApiModelProperty(value = "提及量")
    private BigDecimal statistic;

    /**
     * 负面提及量
     */
    @ApiModelProperty(value = "负面提及量")
    private BigDecimal negativeNum;

    /**
     * 投诉量
     */
    @ApiModelProperty(value = "投诉量")
    private BigDecimal complaintNum;

    /**
     * 发声渠道
     */
    @ApiModelProperty(value = "发声渠道")
    private BigDecimal channelNum;

    /**
     * 净情感值
     */
    @ApiModelProperty(value = "净情感值")
    private BigDecimal emotionNum;

    /**
     * 发声用户
     */
    @ApiModelProperty(value = "发声用户")
    private BigDecimal userNum;
    /**
     * 风险词提及量
     */
    @ApiModelProperty(value = "风险词提及量")
    private BigDecimal riskWordsNum;

    /**
     * 售后
     */
    @ApiModelProperty(value = "售后")
    private BigDecimal afterSalesNum;
    /**
     * 售前
     */
    @ApiModelProperty(value = "售前")
    private BigDecimal preSalesNum;

    /**
     * 推送条件(改为风险等级)
     */
    @ApiModelProperty(value = "推送条件")
    private String pushCondition;

    /**
     * 严重性(高,较高,中,较低,低)
     */
    @ApiModelProperty(value = "严重性(高,较高,中,较低,低)")
    private String seriousness;

    /**
     * 风险等级名称
     */
    @ApiModelProperty(value = "风险等级名称")
    private String riskLevel;

    /**
     * 风险等级最大值
     */
    @ApiModelProperty(value = "风险等级最大值")
    private BigDecimal riskLevelMax;

    /**
     * 风险等级最小值
     */
    @ApiModelProperty(value = "风险等级最小值")
    private BigDecimal riskLevelMin;

    /**
     * 风险等级颜色
     */
    @ApiModelProperty(value = "风险等级颜色")
    private String riskLevelColor;

    /**
     * 1为洞察周期，2为风险等级
     */
    @ApiModelProperty(value = "1为洞察周期，2为风险等级")
    private Integer type;

    private Integer sort;
}
