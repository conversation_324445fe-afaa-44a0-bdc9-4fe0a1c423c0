package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Description: 车型组管理
 * @Date:   2021-04-09
 * @Version: V1.0
 */
@Data
@TableName("VOC_MODEL_GROUP")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="车型组管理", description="车型组管理")
public class ModelGroup implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**删除状态(0-正常,1-已删除)*/
	//@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常,1-已删除)")
    private Integer delFlag;

	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

	/**中文名称*/
	//@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "车型组名称")
    private String name;


    @ApiModelProperty(value = "是否应用,1为是")
    private Integer enable;
    /**中文名称*/
    //@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "车系名")

    @TableField(exist = false)
    private Set<String> vehicleSeries;
    /**中文名称*/
    //@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "车系名数")
    @TableField(exist = false)
    private Integer carSeries;

    private Integer sortNo;

    @TableField(exist = false)
    List<BrandProductManager> childes;
}
