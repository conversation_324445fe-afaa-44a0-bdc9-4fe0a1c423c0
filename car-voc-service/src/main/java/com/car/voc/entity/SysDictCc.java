package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

// 字典实体类
@Data
@TableName("sys_dict_cc")
public class SysDictCc implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private String id;
    private String uuid;
    private Boolean enabled;
    private String text;
    private String groupId;
    private String externalId;
    private Integer index;
    private Boolean editable;
    private Integer depth;
    private String parentId;
    private Boolean isLeaf;
    private String icon;

    @TableField(exist = false)
    private List<SysDictCc> children;

    @TableField(exist = false)
    private DictRelation relation;

    public DictRelation getRelation() {
        if (relation == null) {
            relation = new DictRelation(this.index, this.parentId, this.id);
        }
        return relation;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DictRelation implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer index;
        private String parentId;
        private String id;
    }

}
