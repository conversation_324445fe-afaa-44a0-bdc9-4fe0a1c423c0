package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 角色表
 * </p>
 *
 * @since 2018-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SysRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 角色名称
     */
    //@Excel(name="角色名",width=15)
    private String roleName;

    /**
     * 角色编码
     */
    //@Excel(name="角色编码",width=15)

    private String roleCode;

    /**
     * 描述
     */
    //@Excel(name="描述",width=60)
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    @ApiModelProperty(value = "角色类型")
    private String roleType;
    /**
     * 删除状态（0，正常，1已删除）
     */
    //@Dict(dicCode = "del_flag")
    private String delFlag;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @ApiModelProperty(value = "关联部门；部门ID")
    private String departId;

    @ApiModelProperty(value = "功能权限：true为是")
    private boolean isExport;
    @ApiModelProperty(value = "功能权限：true为是")
    private boolean isDownload;
    @ApiModelProperty(value = "质量标签：true为是")
    private boolean isQuality;

    @ApiModelProperty(value = "功能权限：脱敏，true为是")
    private boolean desensitization;
    @ApiModelProperty(value = "功能权限Vin：脱敏，true为是")
    private boolean desensitizationVin;

    @ApiModelProperty(value = "数据渠道：社媒；传code 以后英文逗号(,)分隔")
    private String publicSphere;
    @ApiModelProperty(value = "数据渠道：私域；传code 以后英文逗号(,)分隔")
    private String privateSphere;
    @ApiModelProperty(value = "类型权限：传code 以后英文逗号(,)分隔")
    private String contentType;

    @ApiModelProperty(value = "状态 true:启用 false：禁用")
    private boolean roleStatus;

    @ApiModelProperty(value = "是否拥有所有权限 true:是")
    private boolean isAll;

    @ApiModelProperty(value = "品牌code")
    private String brandCode;

    private String qualityText;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public boolean isExport() {
        return isExport;
    }

    public void setExport(boolean export) {
        isExport = export;
    }

    public boolean isDownload() {
        return isDownload;
    }

    public void setDownload(boolean download) {
        isDownload = download;
    }

    public boolean isRoleStatus() {
        return roleStatus;
    }

    public void setRoleStatus(boolean roleStatus) {
        this.roleStatus = roleStatus;
    }

    public boolean isAll() {
        return isAll;
    }

    public void setAll(boolean all) {
        isAll = all;
    }

    public boolean getDesensitization() {
        return desensitization;
    }

    public void setDesensitization(boolean desensitization) {
        this.desensitization = desensitization;
    }

    public boolean getDesensitizationVin() {
        return desensitizationVin;
    }

    public void setDesensitizationVin(boolean desensitizationVin) {
        this.desensitizationVin = desensitizationVin;
    }
}
