package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 角色车系
 * @TableName SYS_ROLE_SERIES
 */
@Data
public class SysRoleSeries implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     *
     */
    private String roleId;
    private String delFlag;

    /**
     *
     */
    private String carCode;

    private String brandCode;

    private static final long serialVersionUID = 1L;
}
