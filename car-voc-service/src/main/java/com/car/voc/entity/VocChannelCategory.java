package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 */
@Data
@TableName("voc_channel_category")
public class

VocChannelCategory implements Serializable {
    private String id;

    /**
     * 父级节点
     */
    private String pid;

    /**
     * 类型名称
     */
    private String name;
    private String nameEn;

    /**
     * 类型编码
     */
    private String code;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新日期
     */
    private Date updateTime;

    /**
     * 所属部门
     */
    private String sysOrgCode;

    /**
     * 是否有子节点
     */
    private String hasChild;

    /**
     * 公域数据类型：dictCode：site_content_type
     */
    private String flag;
    private String esIndex;
    private String remark;

    @ApiModelProperty(value = "搜索关键词")
    @TableField(exist = false)
    String searchKeyword;

    @ApiModelProperty(value = "品牌ID搜索")
    @TableField(exist = false)
    String sBrandCode;

    @ApiModelProperty(value = "品牌ID搜索")
    @TableField(exist = false)
    List<String> brandCodeList;

    @ApiModelProperty(value = "品牌ID")
    String brandCode;

    /**
     * 排序
     */
    private Short orderBy;
    @ApiModelProperty(value = "是否应用")
    private boolean enable;
    private static final long serialVersionUID = 1L;
}
