package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * @TableName TT_BALANCE_LABOUR
 */
@Data
public class TtBalanceLabour implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     *
     */
    private String dealerCode;

    /**
     *
     */
    private String serviceCode;

    /**
     *
     */
    private String oldId;

    /**
     *
     */
    private String roNo;

    /**
     *
     */
    private String labourId;

    /**
     *
     */
    private Long roItemId;

    /**
     *
     */
    private String manageSortCode;

    /**
     *
     */
    private String troubleDesc;

    /**
     *
     */
    private String activityNo;

    /**
     *
     */
    private String troubleCause;

    /**
     *
     */
    private String labourCode;

    /**
     *
     */
    private String labourName;

    /**
     *
     */
    private BigDecimal labourPrice;

    /**
     *
     */
    private BigDecimal stdLabourHour;

    /**
     *
     */
    private BigDecimal assignLabourHour;

    /**
     *
     */
    private BigDecimal labourAmount;

    /**
     *
     */
    private String workerTypeCode;

    /**
     *
     */
    private BigDecimal realReceiveAmount;

    /**
     *
     */
    private BigDecimal discount;

    /**
     *
     */
    private BigDecimal discountAmount;

    /**
     *
     */
    private String chargePartitionCode;

    /**
     *
     */
    private Long interReturn;

    /**
     *
     */
    private Long preCheck;

    /**
     *
     */
    private Long consignExterior;

    /**
     *
     */
    private String packageCode;

    /**
     *
     */
    private String repairTypeCode;

    /**
     *
     */
    private String repairTechnician;

    /**
     *
     */
    private String rpTechnicianName;

    /**
     *
     */
    private Long labourType;

    /**
     *
     */
    private Long isNeedChoose;

    /**
     *
     */
    private Long isDiscount;

    /**
     *
     */
    private Long repairItemType;

    /**
     *
     */
    private Long activityType;

    /**
     *
     */
    private String obligatedNo;

    /**
     *
     */
    private BigDecimal discountStdLabourHour;

    /**
     *
     */
    private String itemSeq;

    /**
     *
     */
    private String applyNo;

    /**
     *
     */
    private Long isCreditsActivity;

    /**
     *
     */
    private String activityCreditsPackCode;

    /**
     *
     */
    private Long actItemId;

    /**
     *
     */
    private String remark;

    /**
     *
     */
    private Long manageLabourType;

    /**
     *
     */
    private Long isStaffVehicle;

    /**
     *
     */
    private Long dKey;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private String createDate;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    private String updateDate;

    /**
     *
     */
    private Long ver;

    /**
     *
     */
    private BigDecimal stdLabourPrice;

    /**
     *
     */
    private BigDecimal viewStdLabourHour;

    /**
     *
     */
    private String entityCode;

    /**
     *
     */
    private String balanceNo;

    /**
     *
     */
    private String dbName;

    /**
     *
     */
    private Date syncTime;

    private static final long serialVersionUID = 1L;
}
