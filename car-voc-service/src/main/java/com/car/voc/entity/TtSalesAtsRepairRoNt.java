package com.car.voc.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 委托书表
 * @TableName TT_SALES_ATS_REPAIR_RO_NT
 */
@Data
public class TtSalesAtsRepairRoNt implements Serializable {
    /**
     * 经销商代码SST_CODE
     */
    private String serviceCode;

    /**
     * 维修日期
     */
    private String roCreateDate;

    /**
     * 车辆性质代码
     */
    private String useKind;

    /**
     * 车辆性质
     */
    private String useKindDesc;

    /**
     * 营运性质
     */
    private String businessKind;

    /**
     * 当前里程
     */
    private BigDecimal inMileage;

    /**
     * 送修症状
     */
    private String roTroubleDesc;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车型代码
     */
    private String modelCode;

    /**
     * 购车日期
     */
    private String salesDate;

    /**
     * 车辆牌照
     */
    private String license;

    /**
     * 送修人
     */
    private String deliverer;

    /**
     * 三位码
     */
    private String seriesCode;

    /**
     * 服务专员ID
     */
    private String serviceAdvisor;

    /**
     * 服务专员姓名
     */
    private String serviceAdvisorAss;

    /**
     * 预约单号
     */
    private String bookingOrderNo;

    /**
     * 结算单号
     */
    private String roNo;

    /**
     * 创建日期CREATE_DATE_RO
     */
    private String createDate;

    /**
     * 更新日期UPDATE_DATE_RO
     */
    private String updateDate;

    /**
     *
     */
    private Date syncTime;

    private static final long serialVersionUID = 1L;
}
