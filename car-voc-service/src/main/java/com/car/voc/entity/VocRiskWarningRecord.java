package com.car.voc.entity;

import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocQualityRiskF;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.voc.common.enums.RiskTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * VOC_RISK_WARNING_RECORD
 *
 */
@ApiModel(value="风险警示记录表")
@Data
public class VocRiskWarningRecord implements Serializable {
    private String id;

    /**
     * 风险类型1为业务,2为质量,3为用户
     */
    @ApiModelProperty(value="风险类型1为业务,2为质量,3为用户")
    private String riskType;

    /**
     * 警示时间
     */
    @ApiModelProperty(value="警示时间")
    private Date createTime;

    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 风险名称
     */
    @ApiModelProperty(value="风险UserId或者code")
    private String risk;

    /**
     * 相关提及量(累加)
     */
    @ApiModelProperty(value="相关提及量")
    private BigDecimal statistic;

    /**
     * 风险点Id
     */
    @ApiModelProperty(value="风险点Id")
    private String riskId;

    /**
     * 发声用户(累加)
     */
    @ApiModelProperty(value="发声用户")
    private Integer userNum;

    private static final long serialVersionUID = 1L;


    public VocRiskWarningRecord(DwdVocUserRisk userRisk) {
        this.riskType="3";
        this.createTime=new Date();
        this.delFlag=0;
        this.risk =userRisk.getUserId();
        this.statistic=userRisk.getComplainNum();
        this.riskId=userRisk.getId();
        this.userNum=1;
    }

    public VocRiskWarningRecord(DwdVocEmotionRisk eventRisk) {
        this.riskType="1";
        this.createTime=new Date();
        this.delFlag=0;
        this.risk =eventRisk.getTopicCode();
        this.statistic=eventRisk.getTotalNum();
        this.riskId=eventRisk.getId();
    }

    public VocRiskWarningRecord(DwdVocQualityRiskF qualityRisk, RiskTypeEnum riskTypeEnum) {
        this.riskType=riskTypeEnum.getType();
        this.createTime=new Date();
        this.delFlag=0;
        this.risk =qualityRisk.getTopicCode();
        this.statistic=qualityRisk.getTotalNum();
        this.riskId=qualityRisk.getId();
    }

    public VocRiskWarningRecord(DwdVocDealerRisk eventRisk) {
        this.riskType=RiskTypeEnum.VocRiskBranches.getType();
        this.createTime=new Date();
        this.delFlag=0;
        this.risk =eventRisk.getDlrName();
        this.statistic=eventRisk.getTotalNum();
        this.riskId=eventRisk.getId();
    }
}
