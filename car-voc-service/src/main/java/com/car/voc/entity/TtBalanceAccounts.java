package com.car.voc.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * @TableName TT_BALANCE_ACCOUNTS
 */
@Data
public class TtBalanceAccounts implements Serializable {
    /**
     *
     */
    private String id;

    /**
     *
     */
    private String entityCode;

    /**
     *
     */
    private String dealerCode;

    /**
     *
     */
    private String serviceCode;

    /**
     *
     */
    private String balanceNo;

    /**
     *
     */
    private String branchCode;

    /**
     *
     */
    private String lastBalanceNo;

    /**
     *
     */
    private Long cardId;

    /**
     *
     */
    private Integer balanceBusinessType;

    /**
     *
     */
    private String balanceBusinessNo;

    /**
     *
     */
    private Integer balanceBusinessStatus;

    /**
     *
     */
    private String balanceModeCode;

    /**
     *
     */
    private String discountModeCode;

    /**
     *
     */
    private String contractNo;

    /**
     *
     */
    private String contractCard;

    /**
     *
     */
    private String invoiceNo;

    /**
     *
     */
    private String invoiceTypeCode;

    /**
     *
     */
    private String payTypeCode;

    /**
     *
     */
    private BigDecimal sumAmount;

    /**
     *
     */
    private BigDecimal totalAmount;

    /**
     *
     */
    private BigDecimal tax;

    /**
     *
     */
    private BigDecimal taxAmount;

    /**
     *
     */
    private String netAmouccnt;

    /**
     *
     */
    private BigDecimal receiveAmount;

    /**
     *
     */
    private BigDecimal subObbAmount;

    /**
     *
     */
    private BigDecimal derateAmount;

    /**
     *
     */
    private Integer payOff;

    /**
     *
     */
    private String estimateNo;

    /**
     *
     */
    private String balanceHandler;

    /**
     *
     */
    private Integer isRed;

    /**
     *
     */
    private Long balanceTime;

    /**
     *
     */
    private Long squareDate;

    /**
     *
     */
    private Integer balanceClose;

    /**
     *
     */
    private Long balanceCloseTime;

    /**
     *
     */
    private String obligatedOperator;

    /**
     *
     */
    private String cancelCloseReason;

    /**
     *
     */
    private String memberNo;

    /**
     *
     */
    private BigDecimal activityCredit;

    /**
     *
     */
    private BigDecimal occurCredit;

    /**
     *
     */
    private BigDecimal totalCredit;

    /**
     *
     */
    private Integer arrBalance;

    /**
     *
     */
    private String insurationCode;

    /**
     *
     */
    private String insurationNo;

    /**
     *
     */
    private Long printBalanceTime;

    /**
     *
     */
    private String remark;

    /**
     *
     */
    private Long shieldingDate;

    /**
     *
     */
    private Integer isShielding;

    /**
     *
     */
    private String lockUser;

    /**
     *
     */
    private String cancelBalanceReason;

    /**
     *
     */
    private Integer cancelBalanceExplain;

    /**
     *
     */
    private String cancelBalanceApplicant;

    /**
     *
     */
    private Long cancelBalApplicantTime;

    /**
     *
     */
    private BigDecimal couponTotalAmount;

    /**
     *
     */
    private BigDecimal materialDeductAmount;

    /**
     *
     */
    private BigDecimal labourDeductAmount;

    /**
     *
     */
    private BigDecimal labourAmount;

    /**
     *
     */
    private BigDecimal repairPartAmount;

    /**
     *
     */
    private BigDecimal receiveLabourAmount;

    /**
     *
     */
    private BigDecimal receiveRepairPartAmount;

    /**
     *
     */
    private BigDecimal receiveAdditionalAmount;

    /**
     *
     */
    private Integer auditingStatus;

    /**
     *
     */
    private Long auditingTime;

    /**
     *
     */
    private String auditingRemark;

    /**
     *
     */
    private BigDecimal dragonCardAmount;

    /**
     *
     */
    private BigDecimal svwLabourDeductCredit;

    /**
     *
     */
    private BigDecimal svwDeductCredit;

    /**
     *
     */
    private BigDecimal svwCurrentUsedCredit;

    /**
     *
     */
    private Integer svwCreditAmountFactor;

    /**
     *
     */
    private BigDecimal svwLabrDeductCrdtAmt;

    /**
     *
     */
    private BigDecimal svwDeductCrdtAmt;

    /**
     *
     */
    private BigDecimal svwCrrntUsedCrdtAmt;

    /**
     *
     */
    private BigDecimal skdHqtrsCrdtAmt;

    /**
     *
     */
    private BigDecimal skdDealerCrdtAmt;

    /**
     *
     */
    private String crmExchangeNo;

    /**
     *
     */
    private Integer recActStatus;

    /**
     *
     */
    private BigDecimal theoryTotalAmount;

    /**
     *
     */
    private Long submitTime;

    /**
     *
     */
    private Long downStamp;

    /**
     *
     */
    private Integer isUpload;

    /**
     *
     */
    private String reasonTechNojoin;

    /**
     *
     */
    private String posAccount;

    /**
     *
     */
    private BigDecimal dragonUseIntegral;

    /**
     *
     */
    private Integer repairType;

    /**
     *
     */
    private BigDecimal rmbAmount;

    /**
     *
     */
    private Integer balanceType;

    /**
     *
     */
    private String repairOrderTypeCode;

    /**
     *
     */
    private String discountOperator;

    /**
     *
     */
    private BigDecimal claimPartDiscount;

    /**
     *
     */
    private BigDecimal claimItemDiscount;

    /**
     *
     */
    private Integer techNorepairReason;

    /**
     *
     */
    private String dxRemark;

    /**
     *
     */
    private Integer vwEmployeeTag;

    /**
     *
     */
    private BigDecimal hqVwUsedCredit;

    /**
     *
     */
    private BigDecimal hqVwUsedCreditAmount;

    /**
     *
     */
    private BigDecimal hqVwCreateCredit;

    /**
     *
     */
    private BigDecimal hqVwPreDeductCredit;

    /**
     *
     */
    private BigDecimal hqVwBalanceHqCredit;

    /**
     *
     */
    private BigDecimal hqVwBalanceDealerCredit;

    /**
     *
     */
    private BigDecimal hqVwBalanceCreditFactor;

    /**
     *
     */
    private BigDecimal hqVwPreDeductCreditAmount;

    /**
     *
     */
    private String hqVwCardId;

    /**
     *
     */
    private String hqVwErrorCode;

    /**
     *
     */
    private Integer isUploadHqVw;

    /**
     *
     */
    private String hqVwCardType;

    /**
     *
     */
    private Integer isCreditsSwap;

    /**
     *
     */
    private BigDecimal hqVwLabourHqCredit;

    /**
     *
     */
    private BigDecimal hqVwLabourDealerCredit;

    /**
     *
     */
    private BigDecimal hqVwMultHqCredit;

    /**
     *
     */
    private BigDecimal hqVwMultDealerCredit;

    /**
     *
     */
    private BigDecimal hqVwStdHqCredit;

    /**
     *
     */
    private BigDecimal hqVwStdDealerCredit;

    /**
     *
     */
    private String hqSynCode;

    /**
     *
     */
    private BigDecimal hqVwActDealerCredit;

    /**
     *
     */
    private BigDecimal hqVwActHqCredit;

    /**
     *
     */
    private Integer honestBusiness;

    /**
     *
     */
    private Integer hqVwEticketPoint;

    /**
     *
     */
    private BigDecimal hqVwEticketPointValue;

    /**
     *
     */
    private BigDecimal labourRate;

    /**
     *
     */
    private BigDecimal partRate;

    /**
     *
     */
    private BigDecimal labourPrice;

    /**
     *
     */
    private Integer isApp;

    /**
     *
     */
    private BigDecimal preServiceUseAmount;

    /**
     *
     */
    private Integer wechatGiveCredit;

    /**
     *
     */
    private String hqVwPropertyCode;

    /**
     *
     */
    private Integer isUpdate;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private Long createDate;

    /**
     *
     */
    private Integer dKey;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    private Long updateDate;

    /**
     *
     */
    private Integer ver;

    /**
     *
     */
    private BigDecimal additionalAmount;

    /**
     *
     */
    private Integer containInsurance;

    /**
     *
     */
    private String relateInsurationName;

    /**
     *
     */
    private String maintainSuggest;

    /**
     *
     */
    private Integer warrantyDays;

    /**
     *
     */
    private Integer warrantyMileage;

    /**
     *
     */
    private Integer isDeclare;

    /**
     *
     */
    private BigDecimal realReceiveLabourAmount;

    /**
     *
     */
    private BigDecimal realReceiveRepairPartAmount;

    /**
     *
     */
    private BigDecimal orderPriceAmount;

    /**
     *
     */
    private BigDecimal nextMitainMileage;

    /**
     *
     */
    private Long nextMitainDate;

    /**
     *
     */
    private String preServiceAmount;

    /**
     *
     */
    private String rightAmount;

    /**
     *
     */
    private String rightRmbAmount;

    /**
     *
     */
    private Long firstPrintDate;

    /**
     *
     */
    private String vin;

    /**
     *
     */
    private String ownerName;

    /**
     *
     */
    private String ownerMobile;

    /**
     *
     */
    private String ownerAppIdpid;

    /**
     *
     */
    private String deliverer;

    /**
     *
     */
    private String delivererMobile;

    /**
     *
     */
    private String delivererAppIdpid;

    /**
     *
     */
    private Date syncTime;

    private static final long serialVersionUID = 1L;
}
