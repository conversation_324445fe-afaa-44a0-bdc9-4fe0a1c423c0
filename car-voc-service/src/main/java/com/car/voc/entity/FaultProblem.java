package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.dto.FaultProblemImportDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * @version 1.0.0
 * @ClassName FaultProblem.java
 * @Description TODO
 * @createTime 2022年10月09日 11:50
 * @Copyright voc
 */
@Data
@TableName("VOC_FAULT_PROBLEM")
@ApiModel(value="故障问题对象", description="VOC_FAULT_PROBLEM")
public class FaultProblem {

    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**name*/

    @ApiModelProperty(value = "name")
    private String name;
    private String nameEn;
    /**code*/

    @ApiModelProperty(value = "code")
    private String code;

    /**pId*/

    @ApiModelProperty(value = "pid")
    private String pid;
    /**级别*/


    /**是否有子节点*/

    @ApiModelProperty(value = "是否有子节点")
    private Integer hasChild;
    /**orderBy*/

    @ApiModelProperty(value = "orderBy")
    private Integer orderBy;
    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
    /**delFlag*/

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;

    @ApiModelProperty(value = "是否应用")
    private boolean enable;

    @ApiModelProperty(value = "严重性(dictCode=seriousness_type)")
//    @Dict(dicCode = "seriousness_type")
    private String seriousness;
    private String relatedDepartments;

    //	标签作用域：0:全部 1:VOC 2:CC
    @Dict(dicCode = "tag_application_type")
    private String tagScope;
    @Dict(dicCode = "tag_order_type")
    private String orderType;

    @ApiModelProperty(value = "最后一级指标")
    private String lastTagCode;
    @ApiModelProperty(value = "描述")
    @TableField("`describe`")
    private String describe;

    public FaultProblem() {
    }

    public FaultProblem(FaultProblemImportDto dto) {

    }

    public void setTag1Info(FaultProblemImportDto tag1) {
        this.nameEn=tag1.getTage1();
        this.orderBy=tag1.getOrderBy();
    }

    public void setTag2Info(FaultProblemImportDto tag2) {
        this.nameEn=tag2.getTage2();
        this.orderBy=tag2.getOrderBy();
    }

    public void setTag3Info(FaultProblemImportDto tag3) {
        this.nameEn=tag3.getTage3();
        this.orderBy=tag3.getOrderBy();

    }

    public void setTag4Info(FaultProblemImportDto tag4) {
        this.nameEn=tag4.getTage4();
        this.orderBy=tag4.getOrderBy();
        this.describe=tag4.getDescribe();
        this.seriousness=tag4.getSeriousness();


    }
}
