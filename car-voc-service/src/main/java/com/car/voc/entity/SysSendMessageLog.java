package com.car.voc.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 信息发送记录日志表
 * </p>
 *
 * @since 2018-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ColumnWidth(20)
public class SysSendMessageLog implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(type = IdType.ID_WORKER_STR)
	@ExcelIgnore
	private String id;

	/**
	 * 创建人
	 */
	@ExcelIgnore
	private String createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "操作时间")
	private Date createTime;

	@ApiModelProperty(value = "开始日期",example = "2022-06-01 00:00:00")
	@TableField(exist = false)
	@ExcelIgnore
	String startDate;
	@ApiModelProperty(value = "结束日期",example = "2022-10-01 00:00:00")
	@TableField(exist = false)
	@ExcelIgnore
	String endDate;

	@TableField(exist = false)
	@ExcelProperty(value = "登陆账户名称")
	private String reviewerUserName;
	@TableField(exist = false)
	@ExcelProperty(value = "登陆账号")
	private String reviewerUserNo;
	@TableField(exist = false)
	@ExcelIgnore
	private String reviewerDepartId;
	@TableField(exist = false)
	@ExcelProperty(value = "部门名称")
	private String reviewerDepartName;

	@TableField(exist = false)
	@ExcelIgnore
	private String searchKeyword;

	/**
	 * 更新人
	 */
	@ExcelIgnore
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ExcelIgnore
	private Date updateTime;

	/**
	 * 耗时
	 */
	@ExcelIgnore
	private Long costTime;

	/**
	 * 请求参数
	 */
	@ExcelIgnore
	private String requestParam;
	private String responseParam;

	/**
	 * 请求类型
	 */
	@ExcelProperty(value = "请求类型")
	@ExcelIgnore
	private String requestType;

	/**
	 * 请求路径
	 */
	@ExcelProperty(value = "请求路径")
	@ExcelIgnore
	private String requestUrl;
	/**
	 * 请求方法
	 */
	@ExcelIgnore
	private String method;

	/**
	 * 操作人用户名称
	 */
	@ExcelProperty(value = "操作人")
	private String username;
	/**
	 * 操作人用户账户
	 */
	@ExcelIgnore
	private String userid;

	@TableField(exist = false)
	@ExcelIgnore
	private String departid;
	/**
	 * 操作详细日志
	 */
	@ExcelProperty(value = "操作详细")
	private String logContent;

	/**
	 * 日志类型（1登录日志，2操作日志）
	 */
	@Dict(dicCode = "log_type")
	@ExcelIgnore
	private Integer logType;

	@ExcelProperty(value = "日志类型")
	@TableField(exist = false)
	private String logTypeStr;

	/**
	 * 操作类型（1查询，2添加，3修改，4删除,5导入，6导出）
	 */
	@Dict(dicCode = "operate_type")
	@ExcelIgnore
	private Integer operateType;

	public SysSendMessageLog() {
	}

	public SysSendMessageLog(Integer logType, Integer operateType, String username, String logContent, String requestParam, String workNoticesUrl) {
		this.createTime=new Date();
		this.logType=logType;
		this.logContent=logContent;
		this.operateType=operateType;
		this.setRequestParam(requestParam);
		this.setRequestUrl(workNoticesUrl);
		this.username=username;
	}
}
