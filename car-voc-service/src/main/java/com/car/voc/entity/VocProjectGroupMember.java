package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;


/**
 * 项目组成员关联表(VocProjectGroupMember)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:33:23
 */
@Data
@TableName("voc_project_group_member")
public class VocProjectGroupMember extends Model<VocProjectGroupMember> {
    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;
    /**
     * 项目组ID
     */
    @TableField(value = "group_id")
    private String groupId;
    /**
     * 成员用户ID
     */
    @TableField(value = "user_id")
    private String userId;
    /**
     * 成员用户名称
     */
    @TableField(exist = false)
    private String userName;
    /**
     * 成员用户部门id
     */
    @TableField(exist = false)
    private String departId;

    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField(value = "del_flag")
    private Integer delFlag;
}

