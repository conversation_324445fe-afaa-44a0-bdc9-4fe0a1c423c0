package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * VOC_SOUND_CORRECTION
 *
 */
@ApiModel(value="声音纠错")
@Data
@TableName("VOC_SOUND_CORRECTION")
public class VocSoundCorrection implements Serializable {
    private String id;

    /**
     * 操作用户账号
     */
    @ApiModelProperty(value="操作用户账号")
    private String userid;

    /**
     * 操作用户名称
     */
    @ApiModelProperty(value="操作用户名称")
    private String username;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 声音id
     */
    @ApiModelProperty(value="声音id")
    private String soundId;

    private static final long serialVersionUID = 1L;
}
