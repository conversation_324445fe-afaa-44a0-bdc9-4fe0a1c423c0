package com.car.voc.entity;

import com.car.voc.common.aspect.annotation.Dict;
import lombok.Data;

import java.io.Serializable;

/**
 * (VocBrandRegion)实体类
 * voc_brand_region
 * <AUTHOR>
 * @since 2024-11-20 17:03:36
 */
@Data
public class VocBrandRegion implements Serializable {
    private static final long serialVersionUID = -80864999324814562L;
/**
     * 品牌
     */
    private String brand;
/**
     * 大区名称
     */
    private String regionalName;
/**
     * 大区编码
     */
    private String regionalCode;
    /**
     * 小区编码
     */
    private String communityCode;
    /**
     * 小区名称
     */
    private String communityName;
/**
     * 省份编码
     */
    private String provinceCode;
/**
     * 省份名称
     */
    private String provinceName;
/**
     * 城市编码
     */
    private String cityCode;
/**
     * 城市名称
     */
    private String cityName;
    /**
     * 对应的标签code
     */
    private String tagCodes;
/**
     * 应用类型：0全部，1销售，2售后
     */
    @Dict(dicCode = "area_application_type")
    private String applicationType;




}

