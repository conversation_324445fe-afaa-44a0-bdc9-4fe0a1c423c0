package com.car.voc.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TF_DIM_DATE
 *
 */
@ApiModel(value="generate.TfDimDate时间维度表")
@Data
public class DimDate implements Serializable {
    private Long id;

    /**
     * 时间yyyy-MM-dd
     */
    @ApiModelProperty(value="时间yyyy-MM-dd")
    private Date formatDate;

    private String dateYear;

    private String dateMonth;

    private String dateWeek;

    private String dateQuarter;

    private static final long serialVersionUID = 1L;
}
