package com.car.voc.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * VOC_RISK_WARNING_RULES_DETAILED
 *
 */
@ApiModel(value="警示规则明细表")
@Data
public class VocRiskWarningRulesDetailedRiskLevel {
    private String id;

    /**
     * 警示规则id
     */
    @ApiModelProperty(value="警示规则id")
    private String warnRuleId;

    private Date createTime;

    private Date updateTime;
    private Date lastWarningTime;

    private BigDecimal delFlag;

    /**
     * 风险等级名称
     */
    @ApiModelProperty(value="风险等级名称")
    private String riskLevel;

    /**
     * 风险等级最大值
     */
    @ApiModelProperty(value="风险等级最大值")
    private BigDecimal riskLevelMax;

    /**
     * 风险等级最小值
     */
    @ApiModelProperty(value="风险等级最小值")
    private BigDecimal riskLevelMin;

    /**
     * 风险等级颜色
     */
    @ApiModelProperty(value="风险等级颜色")
    private String riskLevelColor;

    /**
     * 1为洞察周期，2为风险等级
     */
    @ApiModelProperty(value="1为洞察周期，2为风险等级")
    private Integer type;
}
