package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 项目组信息表(VocProjectGroup)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:31:10
 */
@Data
@TableName("voc_project_group")
public class VocProjectGroup extends Model<VocProjectGroup> {
    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;
    /**
     * 项目组名称
     */
    @TableField(value = "project_name")
    private String projectName;
    /**
     * 主责人ID
     */
    @TableField(value = "leader_id")
    private String leaderId;
    /**
     * 品牌
     */
    @TableField(value = "brand_code")
    private String brandCode;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 状态(0-禁用,1-启用)
     */
    @TableField(value = "status")
    private Boolean status;
    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private String deptId;

    @TableField(exist = false)
    private String leaderName;

    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField(value = "del_flag")
    private Integer delFlag;

    @TableField(exist = false)
    private List<VocProjectGroupMember> memberIds;

    @TableField(exist = false)
    private List<VocProjectGroupSeries> seriesList;
}

