package com.car.voc.entity;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName TT_DEALER_DCC
 */
@Data
public class TtDealerDcc implements Serializable {
    /**
     *
     */
    private String dealerCode;

    /**
     *
     */
    private String dealerName;

    /**
     *
     */
    private String dealerShortName;

    /**
     *
     */
    private String dealerEnglishName;

    /**
     *
     */
    private String dealerBillCode;

    /**
     *
     */
    private String brand;

    /**
     *
     */
    private String saleType;

    /**
     *
     */
    private String dealerTypeCode;

    /**
     *
     */
    private String dealerTypeName;

    /**
     *
     */
    private String dealerTypeDetailCode;

    /**
     *
     */
    private String dealerTypeDetailName;

    /**
     *
     */
    private String areaCode;

    /**
     *
     */
    private String agencyCode;

    /**
     *
     */
    private String smallAreaCode;

    /**
     *
     */
    private String provinceCode;

    private static final long serialVersionUID = 1L;
}
