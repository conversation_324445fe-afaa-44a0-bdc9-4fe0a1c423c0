package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * VOC_RISK_ALERT_REVIEWER
 *
 */
@ApiModel(value="generate.VocRiskAlertReviewer警示审核人员")
@Data
public class VocRiskAlertReviewer implements Serializable {
    private String id;

    /**
     * 审核人id
     */
    @ApiModelProperty(value="审核人id")
    private String reviewerUserId;
    @TableField(exist = false)
    private String searchKeyword;

   /* *//**
     * 处理接收人员id多个用逗号分隔
     *//*
    @ApiModelProperty(value="处理接收人员id多个用逗号分隔")
    private String processUserId;

    *//**
     * 风险事件
     *//*
    @ApiModelProperty(value="风险事件")
    private Short riskEvents;

    *//**
     * 质量风险
     *//*
    @ApiModelProperty(value="质量风险")
    private Short qualityRisk;

    *//**
     * top用户
     *//*
    @ApiModelProperty(value="top用户")
    private Short topUser;

    *//**
     * 发送操作:1为人工审核,2为自动审核
     *//*
    @ApiModelProperty(value="发送操作:1为人工审核,2为自动审核")
    private BigDecimal sendOperation;
*/
    private Date createTime;

    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;
    @ApiModelProperty(value="是否钉钉推送")
    private boolean sendDingtalk;
    /**
     * 审核人员的组织id
     */
    @ApiModelProperty(value="审核人员的组织id")
    private String reviewerDepartId;

    private static final long serialVersionUID = 1L;

    private String brandCodes;



}
