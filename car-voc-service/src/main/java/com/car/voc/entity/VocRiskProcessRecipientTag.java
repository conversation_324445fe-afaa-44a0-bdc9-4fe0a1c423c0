package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * VOC_RISK_Process_Recipient_tag
 * 根据recipientId and brandCode进行关联
 *
 */
@ApiModel(value="警示审核人员对应业务标签接收人")
@Data
@TableName(autoResultMap = true)
public class VocRiskProcessRecipientTag implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 风险审核人员关联品牌id
     */
    @ApiModelProperty(value="风险审核人员关联品牌id")
    private String recipientId;

    /**
     * 品牌code
     */
    @ApiModelProperty(value="品牌code")
    private String brandCode;

    /**
     * 品牌code名称
     */
    @ApiModelProperty(value="品牌code名称")
    private String brandCodeName;

    /**
     * 标签id
     */
    @ApiModelProperty(value="标签id")
    private String tagId;

    /**
     * 标签id
     */
    @ApiModelProperty(value="标签Code")
    private String tagCode;

    /**
     * 标签名称
     */
    @ApiModelProperty(value="标签名称")
    private String tagName;

    /**
     * 标签父级id
     */
    @ApiModelProperty(value="标签父级id")
    private String tagParentId;

    /**
     * 标签父级名称
     */
    @ApiModelProperty(value="标签父级名称")
    private String tagParentName;

    /**
     * 处理部门id
     */
    @ApiModelProperty(value="处理部门id")
    private String processDepartId;

    /**
     * 处理部门名称
     */
    @ApiModelProperty(value="处理部门名称")
    private String processDepartName;

    /**
     * 处理人员id
     */
    @ApiModelProperty(value="处理人员id")
    private String processUserId;

    /**
     * 处理人员名称
     */
    @ApiModelProperty(value="处理人员名称")
    private String processUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private String createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private String updateTime;

    /**
     * 标签绑定人员类型：1.部门2.项目组
     */
    @ApiModelProperty(value="标签绑定人员类型：1.部门2.项目组")
    private Integer bindType;
}
