package com.car.voc.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * @TableName TT_BALANCE_REPAIR_PART
 */
@Data
public class TtBalanceRepairPart implements Serializable {
    /**
     *
     */
    private String id;

    /**
     *
     */
    private String dealerCode;

    /**
     *
     */
    private String serviceCode;

    /**
     *
     */
    private Long roItemId;

    /**
     *
     */
    private String roNo;

    /**
     *
     */
    private String obligatedNo;

    /**
     *
     */
    private String chargePartitionCode;

    /**
     *
     */
    private String manageSortCode;

    /**
     *
     */
    private String storagePositionCode;

    /**
     *
     */
    private Integer isMainPart;

    /**
     *
     */
    private String partNo;

    /**
     *
     */
    private String partName;

    /**
     *
     */
    private BigDecimal partQuantity;

    /**
     *
     */
    private String unitCode;

    /**
     *
     */
    private Integer priceType;

    /**
     *
     */
    private BigDecimal priceRate;

    /**
     *
     */
    private String storageCode;

    /**
     *
     */
    private String activityNo;

    /**
     *
     */
    private BigDecimal highLimitPrice;

    /**
     *
     */
    private BigDecimal partCostPrice;

    /**
     *
     */
    private BigDecimal partSalesPrice;

    /**
     *
     */
    private BigDecimal partCostAmount;

    /**
     *
     */
    private BigDecimal partSalesAmount;

    /**
     *
     */
    private BigDecimal discountAmount;

    /**
     *
     */
    private BigDecimal realReceiveAmount;

    /**
     *
     */
    private String sender;

    /**
     *
     */
    private String receiver;

    /**
     *
     */
    private String sendTime;

    /**
     *
     */
    private Integer isExported;

    /**
     *
     */
    private Integer batchNo;

    /**
     *
     */
    private Integer preCheck;

    /**
     *
     */
    private BigDecimal discount;

    /**
     *
     */
    private Integer isAllowDiscount;

    /**
     *
     */
    private Integer isObligated;

    /**
     *
     */
    private String labourCode;

    /**
     *
     */
    private String packageCode;

    /**
     *
     */
    private Integer isFinished;

    /**
     *
     */
    private String repairTypeCode;

    /**
     *
     */
    private Integer isDiscount;

    /**
     *
     */
    private Integer isNeedChoose;

    /**
     *
     */
    private Integer isPrint;

    /**
     *
     */
    private Integer activityType;

    /**
     *
     */
    private String realStoragePosition;

    /**
     *
     */
    private String diffPartNo;

    /**
     *
     */
    private String diffPartName;

    /**
     *
     */
    private BigDecimal diffPartPrice;

    /**
     *
     */
    private BigDecimal diffPartQuantity;

    /**
     *
     */
    private BigDecimal diffAmount;

    /**
     *
     */
    private Integer partNature;

    /**
     *
     */
    private Integer partGroup;

    /**
     *
     */
    private Integer partSort;

    /**
     *
     */
    private String spCode;

    /**
     *
     */
    private String spApplyCode;

    /**
     *
     */
    private Integer spSn;

    /**
     *
     */
    private String applyNo;

    /**
     *
     */
    private String activityCreditsPackCode;

    /**
     *
     */
    private BigDecimal hqVwDealerAmount;

    /**
     *
     */
    private BigDecimal hqVwHqAmount;

    /**
     *
     */
    private Integer promotionType;

    /**
     *
     */
    private Integer isCreditsActivity;

    /**
     *
     */
    private Long actItemId;

    /**
     *
     */
    private Integer actCreditsSource;

    /**
     *
     */
    private Integer isStaffVehicle;

    /**
     *
     */
    private Integer dKey;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private String createDate;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    private String updateDate;

    /**
     *
     */
    private Integer ver;

    /**
     *
     */
    private BigDecimal orderPrice;

    /**
     *
     */
    private String entityCode;

    /**
     *
     */
    private String balanceNo;

    /**
     *
     */
    private String dbName;

    /**
     *
     */
    private Date syncTime;

    private static final long serialVersionUID = 1L;
}
