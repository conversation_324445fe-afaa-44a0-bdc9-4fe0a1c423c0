package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * VOC_RISK_WARNING_RULES
 */
@Data
public class VocRiskWarningRules {
    private String id;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    private String riskType;

    private Date createTime;

    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 推送条件(风险指数范围0-100)
     * 改为
     * 推送条件风险等级
     */
    @ApiModelProperty(value = "推送条件(风险等级)")
    private String pushCondition;

    /**
     * 推送条件(风险指数范围0-100)
     * 改为
     * 推送条件风险等级
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "推送条件(风险等级)")
    private List<String> pushConditionList;

    private String remark1;

    private String lastWarningTime;

    /**
     * 品牌code
     */
    @ApiModelProperty(value = "品牌code")
    private String brandCode;

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    @ApiModelProperty(value = "规则明细")
    List<VocRiskWarningRulesDetailed> rulesDetaileds;

    @TableField(exist = false)
    @ApiModelProperty(value = "洞察周期")
    private List<VocRiskWarningRulesDetailedInsightCycle> insightCycle;

    @TableField(exist = false)
    @ApiModelProperty(value = "风险等级")
    private List<VocRiskWarningRulesDetailedRiskLevel> riskLevel;

    @TableField(exist = false)
    @ApiModelProperty(value = "sql使用")
    private String riskRuleType;

    private Integer orderSort;
}
