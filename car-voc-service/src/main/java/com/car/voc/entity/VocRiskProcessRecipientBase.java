package com.car.voc.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * VOC_RISK_Process_Recipient_tag
 * voc_risk_process_recipient_quality_tag
 * voc_risk_process_recipient_base
 * 根据recipientId and brandCode进行关联
 */
@Data
@ApiModel(value = "警示审核人员对应质量、用户接收人")
@TableName(value = "", autoResultMap = true)
public class VocRiskProcessRecipientBase implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId
    private String id;

    /**
     * 风险审核人员关联品牌id
     */
    @ApiModelProperty(value = "风险审核人员关联品牌id")
    private String recipientId;

    /**
     * 品牌code
     */
    @ApiModelProperty(value = "品牌code")
    private String brandCode;

    /**
     * 品牌code名称
     */
    @ApiModelProperty(value = "品牌code名称")
    private String brandCodeName;

    /**
     * 类型：1.质量quality，2.投诉用户top_user,4救援，5网点
     */
    @ApiModelProperty(value = "类型：2.质量quality，3.投诉用户top_user,4救援，5网点")
    private Integer type;
    /**
     * 标签绑定人员类型：1.部门2.项目组
     */
    @ApiModelProperty(value = "标签绑定人员类型：1.部门2.项目组")
    private Integer bindType;

    @ApiModelProperty(value = "质量风险处理部门id")
    private String riskUserGroupId;
    /**
     * 质量风险处理部门名称
     */
    @ApiModelProperty(value = "质量风险处理部门名称")
    private String riskUserGroupName;

    @ApiModelProperty(value = "质量风险处理人员id")
    private String riskUserId;
    /**
     * 质量风险处理人员名称
     */
    @ApiModelProperty(value = "质量风险处理人员名称")
    private String riskUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private String updateTime;

}
