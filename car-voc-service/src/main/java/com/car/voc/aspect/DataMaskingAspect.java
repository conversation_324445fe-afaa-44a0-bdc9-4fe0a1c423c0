package com.car.voc.aspect;

import cn.hutool.json.JSONObject;
import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.model.LoginUser;
import com.car.voc.util.DataMaskingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 数据脱敏切面
 * 用于在方法执行后对返回结果中标记了@DataDesensitization注解的字段进行脱敏处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Aspect
@Component
@Slf4j
@Order(100) // 确保在其他切面之后执行
public class DataMaskingAspect {

    /**
     * 定义切点：拦截所有Controller方法
     */
    @Pointcut("execution(public * com.car..*.*.*(..))")
    public void controllerMethods() {
    }

    /**
     * 环绕通知：在方法执行后对返回结果进行数据脱敏处理
     */
    @Around("controllerMethods()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = pjp.getSignature().toShortString();

        // 执行原方法
        Object result = pjp.proceed();
        // 获取方法对象
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        // 判断方法是否被 @DataDesensitization 注解标记
        if (method.isAnnotationPresent(DataDesensitization.class)) {
            try {
                DataDesensitization annotation = method.getAnnotation(DataDesensitization.class);
                log.debug("开始处理数据脱敏，方法: {}", methodName);

                // 对返回结果进行数据脱敏处理
                if (result instanceof Map) {
                    result = applyDesensitization(annotation.type(), (Map) result);
                }
                long endTime = System.currentTimeMillis();
                log.debug("数据脱敏处理完成，方法: {}, 耗时: {}ms", methodName, endTime - startTime);
                return result;
            } catch (Exception e) {
                log.error("数据脱敏处理异常，方法: {}", methodName, e);
                // 异常时返回原结果，不影响正常流程
                return result;
            }
        }
        // 如果没有注解标记，则直接执行方法
        return result;
    }

    static List<String> desensitizationKey = Arrays.asList("phone", "customerNumber", "questionDescription", "customerPhone", "woContent", "sendUserPhone", "sendUserNick", "messageContentAll", "remark", "body", "customerName","visitorName");

    static List<String> desensitizationKeyName = Arrays.asList("customerName", "sendUserNick","visitorName");

    /**
     * 根据脱敏类型应用相应的脱敏规则
     */
    public static Map applyDesensitization(DataDesensitization.DesensitizationType[] type, Map<String, Object> value) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        JSONObject json = (JSONObject) redisUtil.get(CacheConstant.SYS_USERS_CACHE_INFO + ":" + loginUser.getId());
        Boolean desensitizationBol = json.getBool("desensitization");
        Boolean desensitizationBolVin = json.getBool("desensitizationVin");
        if (type == null || (desensitizationBol && desensitizationBolVin)) {
            return value;
        }
        for (DataDesensitization.DesensitizationType desensitizationType : type) {
            if ((desensitizationType.equals(DataDesensitization.DesensitizationType.NAME) ||
                    desensitizationType.equals(DataDesensitization.DesensitizationType.PHONE))
                    && desensitizationBol) {
            } else if (desensitizationType.equals(DataDesensitization.DesensitizationType.VIN) && desensitizationBolVin) {
            } else {
                value = applyDesensitizationFind(desensitizationType, value);
            }
        }
        return value;
    }

    public static Map<String, Object> applyDesensitizationFind(DataDesensitization.DesensitizationType type, Map<String, Object> value) {
        Map<String, Object> desensitization = new LinkedHashMap<>();
        value.forEach((key, val) -> {
            if (val instanceof Map) {
                desensitization.put(key, applyDesensitizationFind(type, (Map) val));
            } else if (val instanceof List) {
                List<Map> re = new ArrayList<>();
                for (Map a : (List<Map>) val) {
                    Map map = applyDesensitizationFind(type, a);
                    re.add(map);
                }
                desensitization.put(key, re);
            } else if (val instanceof String && desensitizationKey.contains(key)) {
                if (!type.equals(DataDesensitization.DesensitizationType.NAME)) {
                    String desensitizedValue = DataMaskingUtil.applyDesensitization(type, (String) val);
                    desensitization.put(key, desensitizedValue);
                } else if (desensitizationKeyName.contains(key)) {
                    String desensitizedValue = DataMaskingUtil.applyDesensitization(type, (String) val);
                    desensitization.put(key, desensitizedValue);
                } else {
                    desensitization.put(key, val);
                }
            } else {
                desensitization.put(key, val);
            }
        });
        return desensitization;
    }
}
