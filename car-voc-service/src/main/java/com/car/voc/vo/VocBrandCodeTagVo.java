package com.car.voc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: voc_business_tag
 * @Date: 2021-03-30
 * @Version: V1.0
 */
@Data
@ApiModel("新的审核返回对象")
public class VocBrandCodeTagVo implements Serializable {

    @ApiModelProperty("业务标签")
    private List<VocBusinessTagVo> vocBusinessTagVoList;

    @ApiModelProperty("品牌Code")
    private String brandCode;

    @ApiModelProperty("是否有质量标签权限")
    private Boolean qualityPermission;

    @ApiModelProperty("是否有用户投诉权限")
    private Boolean userPermission;
}
