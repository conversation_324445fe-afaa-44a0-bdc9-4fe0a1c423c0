package com.car.voc.vo.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName RiskAlertReviewerVo.java
 * @Description TODO
 * @createTime 2023年02月06日 10:41
 * @Copyright voc
 */
@Data
@TableName(autoResultMap = true)
public class WarningReviewVo implements Serializable {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "审核人员用户id")
    private String reviewerUserId;
    private String reviewerUserName;

    @ApiModelProperty(value = "审核人员组织id")
    private String reviewerDepartId;
    private String reviewerDepartName;

    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value="是否钉钉推送")
    private boolean sendDingtalk;
    private List<WarningReviewBrandTagVo> brandTagList;


}
