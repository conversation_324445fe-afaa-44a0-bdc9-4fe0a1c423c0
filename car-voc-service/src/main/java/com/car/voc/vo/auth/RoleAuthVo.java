package com.car.voc.vo.auth;

import cn.hutool.json.JSONObject;
import com.car.voc.entity.SysRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName RoleAuthVo.java
 * @Description TODO
 * @createTime 2023年01月04日 15:05
 * @Copyright voc
 */
@Data
public class RoleAuthVo implements Serializable {
    LinkedHashMap<String,List<RoleAuthTree>> dataChannel;
    List<RoleAuthTree> relationBuTag;
    boolean quality ;
    List<RoleAuthTree> relationCar;
    List<RoleAuthTree> appKanban;
    List<RoleAuthTree> systemAdmin;
    List<RoleAuthTree> area;
    JSONObject downloadAuth;
    private String roleId;
    private String roleName;
    private boolean status;
    @ApiModelProperty(value="角色类型")
    private String roleType;
    @ApiModelProperty(value="品牌code")
    private String brandCode;
    @ApiModelProperty(value="品牌code")
    private List<String> brandCodeList;
    private Integer orderBy;


    public boolean isUse() {
        return isUse;
    }

    public void setUse(boolean use) {
        isUse = use;
    }

    private boolean isUse;

    public RoleAuthVo(SysRole e) {
        this.roleId=e.getId();
        this.roleName=e.getRoleName();
        this.status=e.isRoleStatus();
    }

    public RoleAuthVo() {
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    private String desc;
    boolean isExport;

    boolean isDownload;
    /**
     * 功能权限：脱敏，true为是
     */
    @ApiModelProperty(value="功能权限：脱敏，true为是")
    private Boolean desensitization;
    @ApiModelProperty(value="功能权限Vin：脱敏，true为是")
    private Boolean desensitizationVin;

    boolean isAllPermission;

    public Map<String, List<RoleAuthTree>> getDataChannel() {
        return dataChannel;
    }

    public void setDataChannel(LinkedHashMap<String, List<RoleAuthTree>> dataChannel) {
        this.dataChannel = dataChannel;
    }

    public boolean isQuality() {
        return quality;
    }

    public void setQuality(boolean quality) {
        this.quality = quality;
    }

    public boolean isExport() {
        return isExport;
    }

    public void setExport(boolean export) {
        isExport = export;
    }

    public boolean isDownload() {
        return isDownload;
    }

    public void setDownload(boolean download) {
        isDownload = download;
    }

    public boolean isAllPermission() {
        return isAllPermission;
    }

    public void setAllPermission(boolean allPermission) {
        this.isAllPermission = allPermission;
    }
}
