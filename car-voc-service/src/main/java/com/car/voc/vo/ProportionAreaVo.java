package com.car.voc.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ProportionCarSeriesVo.java
 * @Description TODO
 * @createTime 2022年10月14日 09:55
 * @Copyright svw
 */
@Data
public class ProportionAreaVo {
    String areaCode;
    String areaStr;
    BigDecimal proportion;
    BigDecimal statistic;
    BigDecimal statisticR;
    BigDecimal statisticP;
    String dateTime;

    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.statistic= CalculatorUtils.avgePerDayNum(this.statistic,new BigDecimal(day));
    }

}
