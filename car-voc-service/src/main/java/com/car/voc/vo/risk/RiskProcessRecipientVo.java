package com.car.voc.vo.risk;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * VOC_RISK_ALERT_REVIEWER
 *
 */
@ApiModel(value="generate.VocRiskProcessRecipient警示审核人员")
@Data
public class RiskProcessRecipientVo implements Serializable {
    private String id;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核id")
    private String auditId;

    /**
     * 处理接收人员id多个用逗号分隔
     */
    @ApiModelProperty(value="处理接收人员id多个用逗号分隔")
    private String processUserId;
    private String processUserName;

    /**
     * 风险事件
     */
    @ApiModelProperty(value="风险事件")
    private boolean riskEvents;

    /**
     * 质量风险
     */
    @ApiModelProperty(value="质量风险")
    private boolean qualityRisk;

    /**
     * top用户
     */
    @ApiModelProperty(value="top用户")
    private boolean topUser;


    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 审核人员的组织id
     */
    @ApiModelProperty(value="接收人员的组织id")
    private String processDepartId;
    private String processDepartName;

    private static final long serialVersionUID = 1L;



}
