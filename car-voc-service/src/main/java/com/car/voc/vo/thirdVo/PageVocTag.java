package com.car.voc.vo.thirdVo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
@Data
public class PageVocTag {
    protected List<VocTagVo> records;
    protected long total;
    protected long size;
    protected long pages;
    protected long current;

    public PageVocTag(IPage<VocTagVo> page) {
        this.records = page.getRecords();
        this.total = page.getTotal();
        this.size = page.getSize();
        this.pages = page.getPages();
        this.current = page.getCurrent();
    }
}
