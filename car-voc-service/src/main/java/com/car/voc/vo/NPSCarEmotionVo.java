package com.car.voc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSCarEmotionVo.java
 * @Description TODO
 * @createTime 2023年07月31日 10:24
 * @Copyright voc
 */
@Data
public class NPSCarEmotionVo {
    @ApiModelProperty(value = "车型")
    String carSeries;
    /**
     * 正面
     */
    @ApiModelProperty(value = "正面")
    String positive;
    /**
     * 负面
     */
    @ApiModelProperty(value = "负面")
    String negative;
    /**
     * 负面
     */
    @ApiModelProperty(value = "中性")
    String neutral;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "正面比例")
    String positiveP;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "负面比例")
    String negativeP;
    /**
     * 中性比例
     */
    @ApiModelProperty(value = "中性比例")
    String neutralP;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "正面环比")
    String positiveC;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "负面环比")
    String negativeC;
    /**
     * 中性比例
     */
    @ApiModelProperty(value = "中性环比")
    String neutralC;
    /**
     * 参与人数
     */
    @ApiModelProperty(value = "参与人数")
    String total;

    /**
     * 净推荐占比：推荐者的人数/总人数
     */
    @ApiModelProperty(value = "净推荐占比")
    String netPromoter;
}
