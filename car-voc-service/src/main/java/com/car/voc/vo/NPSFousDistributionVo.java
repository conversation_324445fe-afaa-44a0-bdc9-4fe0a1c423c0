package com.car.voc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSFousDistributionVo.java
 * @Description TODO
 * @createTime 2023年07月31日 10:24
 * @Copyright voc
 */
@Data
public class NPSFousDistributionVo {
    /**
     * 一级标签
     */
    @ApiModelProperty(value="一级标签")
    private String firstDimensionCode;

    /**
     * 二级标签
     */
    @ApiModelProperty(value="二级标签")
    private String secondDimensionCode;

    /**
     * 三级标签
     */
    @ApiModelProperty(value="三级标签")
    private String threeDimensionCode;

    /**
     *  默认返回一级，   选了一级是返回二级code
     */
    @ApiModelProperty(value="标签编码")
    private String labelCode;

    /**
     * 四级标签/话题
     */
    @ApiModelProperty(value="四级标签/话题")
    private String topicCode;
    /**
     * 正面
     */
    @ApiModelProperty(value = "正面")
    String positive;
    /**
     * 负面
     */
    @ApiModelProperty(value = "负面")
    String negative;
    /**
     * 负面
     */
    @ApiModelProperty(value = "中性")
    String neutral;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "正面比例")
    String positiveP;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "负面比例")
    String negativeP;
    /**
     * 中性比例
     */
    @ApiModelProperty(value = "中性比例")
    String neutralP;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "正面环例")
    String positiveC;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "负面环例")
    String negativeC;
    /**
     * 中性比例
     */
    @ApiModelProperty(value = "中性环例")
    String neutralC;

}
