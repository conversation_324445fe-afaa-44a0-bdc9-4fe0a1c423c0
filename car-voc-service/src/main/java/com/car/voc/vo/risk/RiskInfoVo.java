package com.car.voc.vo.risk;

import com.alibaba.fastjson.annotation.JSONField;
import com.car.stats.vo.risk.RiskBriefingVo;
import lombok.Data;

import java.util.Date;

/**
 *
 * @version 1.0.0
 * @ClassName RiskInfoVo.java
 * @Description TODO
 * @createTime 2023年02月10日 09:58
 * @Copyright voc
 */
@Data
public class RiskInfoVo extends RiskBriefingVo {
    String phone;
    String carNo;
    String region;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    Date warningTime;
    Long warningNum;

}
