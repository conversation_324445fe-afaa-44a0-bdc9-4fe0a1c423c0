package com.car.voc.vo;

import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @version 1.0.0
 * @ClassName TrendLabelVo.java
 * @Description TODO
 * @createTime 2022年10月17日 09:52
 * @Copyright voc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"dateStr"})
public class NPSAnalysisLabelVo {
    String dateStr;
    @Builder.Default
    List<NPSAnalysisTrendVo> list = new ArrayList<>();
}
