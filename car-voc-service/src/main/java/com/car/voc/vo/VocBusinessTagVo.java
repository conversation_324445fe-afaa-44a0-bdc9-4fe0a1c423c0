package com.car.voc.vo;

import com.car.voc.entity.VocBusinessTag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: voc_business_tag
 *
 * @Date:   2021-03-30
 * @Version: V1.0
 */
@Data
@ApiModel(value="voc_business_tag对象", description="voc_business_tag")
public class VocBusinessTagVo extends VocBusinessTag {
   /**
    * 标签绑定人员类型：1.部门2.项目组
    */
   @ApiModelProperty(value = "标签绑定人员类型：1.部门2.项目组")
   private Integer bindType;
   List<VocBusinessTagVo> childes;
}
