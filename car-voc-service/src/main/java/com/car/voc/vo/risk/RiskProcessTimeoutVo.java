package com.car.voc.vo.risk;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/31
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@Data
public class RiskProcessTimeoutVo {
    String id;
    String warningRiskId;
    String processUserId;
    String processDepartId;
    String createTime;
    String lastReminderTime;
    String realname;
    String departDame;
    String riskType;
    String riskName;
    String brandCode;
    String brandName;
    Integer daysDifference;
    Integer reminderFrequency;
    Integer daysDi7;

}
