package com.car.voc.vo.auth;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName RoleAuthTree.java
 * @Description TODO
 * @createTime 2023年01月04日 15:06
 * @Copyright voc
 */
@Data
public class RoleAuthTree implements Serializable {
    String id;
    String code;
    String pid;
    String name;
    String type;
    boolean isChecked;
    BigDecimal orderBy;
    List<RoleAuthTree> children;

    public boolean isChecked() {
        return isChecked;
    }
}
