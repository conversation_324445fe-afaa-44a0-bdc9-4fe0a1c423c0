package com.car.voc.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.entity.ModelGroupRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Description: 车型组管理
 * @Date:   2021-04-09
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="车型组对象", description="车型组对象")
public class ModelGroupVo  {

	/**主键id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**删除状态(0-正常,1-已删除)*/
	//@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常,1-已删除)")
    private Integer delFlag;

	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

	/**中文名称*/
	//@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "车型组名称")
    private String name;


	/**中文名称*/
	//@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "车系名")
    private Set<String> vehicleSeries;
	/**中文名称*/
	//@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "车系名数")
    private Integer carSeries;

    List<ModelGroupRelation> relations;


    @ApiModelProperty(value = "是否应用,1为是")
    private Integer enable;

}
