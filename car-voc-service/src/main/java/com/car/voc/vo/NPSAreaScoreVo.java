package com.car.voc.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0.0
 * @ClassName NPSOverviewVo.java
 * @Description TODO
 * @createTime 31 09:52
 * @Copyright voc
 */
@Data
//@EqualsAndHashCode(of ={"dateStr","dayAvgParentScores","topCode",})
@ApiModel(value = "NPS分析对象", description = "NPS调研人数统计")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NPSAreaScoreVo implements Serializable {
    @ApiModelProperty(value = "全国排名总数")
    String nationalRanking;
    @ApiModelProperty(value = "全国排名总数")
    String nationalRankingC;
    @ApiModelProperty( value = "区域编码")
    String areaCode;
    @ApiModelProperty(value = "区域平均值")
    String areaScores;
    @ApiModelProperty( value = "区域环比值")
    String areaChianScores;
    @JsonIgnore
    String level;

}
