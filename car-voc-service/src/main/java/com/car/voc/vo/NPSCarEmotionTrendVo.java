package com.car.voc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSCarEmotionTrendVo.java
 * @Description TODO
 * @createTime 2023年07月31日 10:24
 * @Copyright voc
 */
@Data
public class NPSCarEmotionTrendVo {

    @ApiModelProperty(value = "日期")
    String dateStr;
    /**
     * 正面
     */
    @ApiModelProperty(value = "正面")
    String positive;

    /**
     * 负面
     */
    @ApiModelProperty(value = "负面")
    String negative;
    /**
     * 中立
     */
    @ApiModelProperty(value = "中立")
    String neutral;

    /**
     * 正面比例
     */
    @ApiModelProperty(value = "正面比例")
    String positiveP;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "负面比例")
    String negativeP;
    /**
     * 中立比例
     */
    @ApiModelProperty(value = "中立比例")
    String neutralP;

    /**
     * 正面环例
     */
    @ApiModelProperty(value = "正面环例")
    String positiveC;
    /**
     * 负面环例
     */
    @ApiModelProperty(value = "负面环例")
    String negativeC;
    /**
     * 中立环例
     */
    @ApiModelProperty(value = "中立环例")
    String neutralC;
}
