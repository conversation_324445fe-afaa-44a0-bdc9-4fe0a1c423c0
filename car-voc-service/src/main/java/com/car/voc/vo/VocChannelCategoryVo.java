package com.car.voc.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName VocChannelCategoryVo.java
 * @Description TODO
 * @createTime 2023年05月10日 14:24
 * @Copyright voc
 */
@Data
public class VocChannelCategoryVo {
    private String id;

    /**
     * 父级节点
     */
    private String pid;

    /**
     * 类型名称
     */
    private String name;
    private String namep;
    private Object pids;
    private String nameEn;

    /**
     * 类型编码
     */
    private String code;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新日期
     */
    private Date updateTime;

    /**
     * 所属部门
     */
    private String sysOrgCode;

    /**
     * 是否有子节点
     */
    private String hasChild;

    /**
     * 公域数据类型：dictCode：site_content_type
     */
    private String flag;
    private String esIndex;
    private String remark;

    /**
     * 排序
     */
    private Short orderBy;

    private boolean enable;

    @ApiModelProperty(value = "品牌ID搜索")
    List<String> brandCodeList;

    @ApiModelProperty(value = "品牌ID")
    String brandCode;

    public Object getPids() {
        if ("".equals(pids)){
            return null;
        }
        String[] s=pids.toString().split(",");
        return s;
    }
}
