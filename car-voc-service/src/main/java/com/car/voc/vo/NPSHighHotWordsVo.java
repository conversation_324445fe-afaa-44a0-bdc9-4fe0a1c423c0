package com.car.voc.vo;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSHighHotWordsVo.java
 * @Description TODO
 * @createTime 2023年07月31日 09:52
 * @Copyright voc
 */
@Data
public class NPSHighHotWordsVo {
    @ApiModelProperty(value = "高频热词")
    String keyword;
    @ApiModelProperty(value = "正面")
    String positive;
    @ApiModelProperty(value = "负面")
    String negative;
    @ApiModelProperty(value = "中性")
    String neutral;
    @ApiModelProperty(value = "出现频次")
    String statistic;

    Integer weight;

    public Integer getWeight() {
        if (StrUtil.isNotBlank(positive) && StrUtil.isNotBlank(negative)  ) {
            try{
                return Integer.valueOf(positive).compareTo(Integer.valueOf(negative));
            }catch (Exception e){
                e.printStackTrace();
            }
            return 0;
        } else if (StrUtil.isNotBlank(positive)) {
            return 1;
        } else if (StrUtil.isNotBlank(negative)) {
            return -1;
        } else {
            return 0;
        }
    }

}
