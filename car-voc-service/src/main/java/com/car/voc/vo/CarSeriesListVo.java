package com.car.voc.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * @version 1.0.0
 * @ClassName CarSeriesListVo.java
 * @Description TODO
 * @createTime 2023年06月07日 19:07
 * @Copyright voc
 */
@Data
public class CarSeriesListVo {



    /**主键id*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;

    /**中文名称*/
    //@Excel(name = "中文名称", width = 15)
    @ApiModelProperty(value = "中文名称")
    private String name;
    /**英文名*/
    //@Excel(name = "英文名", width = 15)
    @ApiModelProperty(value = "英文名")
    private String englishName;
    /**国家*/

    @ApiModelProperty(value = "父级节点")
    private String pId;


    private String code;

    @TableField(exist = false)
    String brandCode;


    public String getBrandCode() {

        return this.code!=null?this.code.substring(0,3):null;
    }
}
