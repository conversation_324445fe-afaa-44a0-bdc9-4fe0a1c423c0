package com.car.voc.vo.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName RiskAlertReviewerVo.java
 * @Description TODO
 * @createTime 2023年02月06日 10:41
 * @Copyright voc
 */
@Data
@TableName(autoResultMap = true)
public class ReviewerPermissionVo implements Serializable {

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "权限")
    private List<ReviewerPermissionBrandCodeVo> reviewerPermission;

}
