package com.car.voc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: voc_business_tag
 *
 * @Date:   2021-03-30
 * @Version: V1.0
 */
@Data
@ApiModel(value="voc_business_tag对象", description="voc_business_tag")
public class FaultProblemTreeVo implements Serializable {
    private static final long serialVersionUID = 1L;



	/**id*/
	@ApiModelProperty(value = "id")
	private String id;
	/**name*/

	@ApiModelProperty(value = "name")
	private String name;
	/**code*/

	@ApiModelProperty(value = "code")
	private String code;

	/**pId*/

	@ApiModelProperty(value = "pid")
	private String pid;
	/**级别*/

	@ApiModelProperty(value = "级别")
	private Integer level;
	/**是否有子节点*/

	@ApiModelProperty(value = "是否有子节点")
	private Integer hasChild;
	/**orderBy*/

	@ApiModelProperty(value = "orderBy")
	private Integer orderBy;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "createTime")
	private java.util.Date createTime;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "updateTime")
	private java.util.Date updateTime;
	/**delFlag*/

	@ApiModelProperty(value = "delFlag")
	private Integer delFlag;

	@ApiModelProperty(value = "是否应用")
	private boolean enable;

	@ApiModelProperty(value = "严重性(dictCode=seriousness_type)")
	@Dict(dicCode = "seriousness_type")
	private Integer seriousness;


	@ApiModelProperty(value = "最后一级指标")
	private String lastTagCode;

	@ApiModelProperty(value = "孩子")
	private List<FaultProblemTreeVo> childes;

}
