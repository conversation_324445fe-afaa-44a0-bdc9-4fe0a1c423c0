package com.car.voc.vo.risk;

import com.car.voc.entity.VocRiskWarningRulesDetailed;
import lombok.Data;

import java.math.BigDecimal;
/**
 * 
 * @version 1.0.0
 * @ClassName RiskRuleVo.java
 * @Description TODO
 * @createTime 2023年03月09日 16:58
 * @Copyright voc
 */
@Data
public class RiskRuleVo {
    BigDecimal negativeNumD;
    BigDecimal complaintNumD;
    BigDecimal riskWordsNumD;
    BigDecimal channelNumD;
    BigDecimal emotionNumD;
    BigDecimal statisticD;

    BigDecimal negativeNumW;
    BigDecimal complaintNumW;
    BigDecimal riskWordsNumW;
    BigDecimal channelNumW;
    BigDecimal emotionNumW;
    BigDecimal statisticW;



    BigDecimal negativeNumM;
    BigDecimal complaintNumM;
    BigDecimal riskWordsNumM;
    BigDecimal channelNumM;
    BigDecimal emotionNumM;
    BigDecimal statisticM;



    BigDecimal negativeNumQ;
    BigDecimal complaintNumQ;
    BigDecimal riskWordsNumQ;
    BigDecimal channelNumQ;
    BigDecimal emotionNumQ;
    BigDecimal statisticQ;

    BigDecimal negativeNumY;
    BigDecimal complaintNumY;
    BigDecimal riskWordsNumY;
    BigDecimal channelNumY;
    BigDecimal emotionNumY;
    BigDecimal statisticY;



    public void setNumAll(VocRiskWarningRulesDetailed rd) {
        if ("d".equals(rd.getInsightCycle())){
            this.channelNumD=rd.getChannelNum();
            this.negativeNumD=rd.getNegativeNum();
            this.riskWordsNumD=rd.getRiskWordsNum();
            this.complaintNumD=rd.getComplaintNum();
            this.emotionNumD=rd.getEmotionNum();
            this.statisticD=rd.getStatistic();

        }else if ("w".equals(rd.getInsightCycle())){
            this.channelNumW=rd.getChannelNum();
            this.negativeNumW=rd.getNegativeNum();
            this.riskWordsNumW=rd.getRiskWordsNum();
            this.complaintNumW=rd.getComplaintNum();
            this.emotionNumW=rd.getEmotionNum();
            this.statisticW=rd.getStatistic();

        }else if ("m".equals(rd.getInsightCycle())){
            this.channelNumM=rd.getChannelNum();
            this.negativeNumM=rd.getNegativeNum();
            this.riskWordsNumM=rd.getRiskWordsNum();
            this.complaintNumM=rd.getComplaintNum();
            this.emotionNumM=rd.getEmotionNum();
            this.statisticM=rd.getStatistic();

        }else if ("q".equals(rd.getInsightCycle())){
            this.channelNumQ=rd.getChannelNum();
            this.negativeNumQ=rd.getNegativeNum();
            this.riskWordsNumQ=rd.getRiskWordsNum();
            this.complaintNumQ=rd.getComplaintNum();
            this.emotionNumQ=rd.getEmotionNum();
            this.statisticQ=rd.getStatistic();

        }else if("y".equals(rd.getInsightCycle())){
            this.channelNumY=rd.getChannelNum();
            this.negativeNumY=rd.getNegativeNum();
            this.riskWordsNumY=rd.getRiskWordsNum();
            this.complaintNumY=rd.getComplaintNum();
            this.emotionNumY=rd.getEmotionNum();
            this.statisticY=rd.getStatistic();
        }
    }

    public RiskRuleVo() {
    }

    public BigDecimal getRiskWordsNumD() {
        return riskWordsNumD.equals(BigDecimal.ZERO)?BigDecimal.ZERO:riskWordsNumD;
    }
    public BigDecimal getRiskWordsNumW() {
        return riskWordsNumW.equals(BigDecimal.ZERO)?BigDecimal.ZERO:riskWordsNumW;
    }
    public BigDecimal getRiskWordsNumM() { return riskWordsNumM.equals(BigDecimal.ZERO)?BigDecimal.ZERO:riskWordsNumM; }
    public BigDecimal getRiskWordsNumQ() {return riskWordsNumQ.equals(BigDecimal.ZERO)?BigDecimal.ZERO:riskWordsNumQ; }
    public BigDecimal getRiskWordsNumY() { return riskWordsNumY.equals(BigDecimal.ZERO)?BigDecimal.ZERO:riskWordsNumY; }
}
