package com.car.voc.vo;

import cn.hutool.core.util.StrUtil;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.entity.VocRiskHandlingRecord;
import com.car.voc.vo.risk.RiskAlertReviewerVo;
import com.car.voc.vo.risk.RiskInfoVo;
import com.car.voc.vo.risk.RiskProcessRecipientVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@ApiModel(value="风险处理跟进列表、风险详情、返回对象")
@Data
public class ProblemOverviewVo implements Serializable {

    private String departName;

    private Integer bindType;

    private String departId;

    private Integer total;

}
