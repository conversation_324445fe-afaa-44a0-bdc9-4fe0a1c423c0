package com.car.voc.vo.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.entity.VocRiskProcessRecipient;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName RiskAlertReviewerVo.java
 * @Description TODO
 * @createTime 2023年02月06日 10:41
 * @Copyright voc
 */
@Data
@TableName(autoResultMap = true)
public class RiskAlertReviewerVo implements Serializable {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "审核人员用户id")
    private String reviewerUserId;
    private String reviewerUserName;
    private String reviewerUserNo;
    @ApiModelProperty(value = "审核人员组织id")
    private String reviewerDepartId;
    private String reviewerDepartName;
    private Integer auditsNum;

//    @ApiModelProperty(value = "接收人员")
//    private List<VocRiskProcessRecipient> receivingUsers;



    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "对应接收人员")
    private String recipientUser;

    @ApiModelProperty(value = "品牌")
    private String brandCodes;
    @ApiModelProperty(value="是否钉钉推送")
    private boolean sendDingtalk;
}
