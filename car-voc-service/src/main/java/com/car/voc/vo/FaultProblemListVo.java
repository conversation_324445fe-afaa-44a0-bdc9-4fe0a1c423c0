package com.car.voc.vo;

import com.car.voc.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.util.CalculatorUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: voc_business_tag
 *
 * @Date:   2021-03-30
 * @Version: V1.0
 */
@Data
@ApiModel(value="voc_business_tag对象", description="voc_business_tag")
public class FaultProblemListVo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@ApiModelProperty(value = "id")
	private String id;
	/**name*/

	@ApiModelProperty(value = "name")
	private String name;
	/**code*/

	@ApiModelProperty(value = "code")
	private String code;

	/**pId*/

	@ApiModelProperty(value = "pid")
	private String pid;
	/**级别*/

	@ApiModelProperty(value = "级别")
	private Integer level;
	/**是否有子节点*/

	@ApiModelProperty(value = "是否有子节点")
	private Integer hasChild;
	/**orderBy*/
	private String orderType;
	@ApiModelProperty(value = "orderBy")
	private Integer orderBy;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "createTime")
	private Date createTime;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "updateTime")
	private Date updateTime;
	/**delFlag*/
	@ApiModelProperty(value = "delFlag")
	private Integer delFlag;

	@ApiModelProperty(value = "是否应用")
	private Integer enable;

	@ApiModelProperty(value = "严重性")
	private String seriousness;

	@ApiModelProperty(value = "标签")
	private String problemName1;

	@ApiModelProperty(value = "标签")
	private String problemCode1;

	@ApiModelProperty(value = "标签")
	private String problemName2;

	@ApiModelProperty(value = "标签")
	private String problemCode2;

	@ApiModelProperty(value = "标签")
	private String problemName3;

	@ApiModelProperty(value = "标签")
	private String problemCode3;

	@ApiModelProperty(value = "标签")
	private String problemName4;

	@ApiModelProperty(value = "标签")
	private String problemCode4;

	private String relatedDepartments;
	private String relatedDepartmentsName;
	@ApiModelProperty(value = "指标Code")
	private String lastTagCode;
	@ApiModelProperty(value = "指标名称")
	private String lastTagName;
	private String tagCode3;
	private String tagCode4;
	private String describe;
	@Dict(dicCode = "tag_application_type")
	private String tagScope;

}
