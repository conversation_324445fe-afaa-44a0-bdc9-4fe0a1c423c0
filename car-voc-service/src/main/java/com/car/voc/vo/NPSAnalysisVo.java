package com.car.voc.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @version 1.0.0
 * @ClassName NPSAnalysisVo.java
 * @Description TODO
 * @createTime 31 09:52
 * @Copyright voc
 */
@Data
//@EqualsAndHashCode(of ={"dateStr","dayAvgParentScores","topCode",})
@ApiModel(value = "NPS分析对象", description = "NPS分析对象")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NPSAnalysisVo implements Serializable {
    @JsonIgnore
    String dateUnit;

    @ApiModelProperty(value = "平均值")
    String avgScores;
    @ApiModelProperty(value = "平均值环比")
    String avgScoresC;

    @ApiModelProperty(value = "诋毁者")
    String detractor;

    String nationalRanking;
    /**
     * 中立者
     */
    @ApiModelProperty(value = "中立者")
    String neutral;
    /**
     * 推荐者
     */
    @ApiModelProperty(value = "推荐者")
    String recommend;

    @ApiModelProperty(value = "1：全国，2：区域  3：省份")
    String level;

    @ApiModelProperty(value = "编码")
    String code;
}
