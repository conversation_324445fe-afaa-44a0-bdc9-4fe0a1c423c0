package com.car.voc.vo.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName RiskAlertReviewerVo.java
 * @Description TODO
 * @createTime 2023年02月06日 10:41
 * @Copyright voc
 */
@Data
@TableName(autoResultMap = true)
public class ReviewerPermissionBrandCodeVo implements Serializable {


    @ApiModelProperty(value = "brandCodes")
    private String brandCode;

    @ApiModelProperty(value = "tagCodes")
    private List<String> tagCodes;

    @ApiModelProperty(value = "emotionStatus")
    private Integer emotionStatus;

    @ApiModelProperty(value = "qualityStatus")
    private Integer qualityStatus;

    @ApiModelProperty(value = "userStatus")
    private Integer userStatus;

    private List<String> riskTypeStatus;
}
