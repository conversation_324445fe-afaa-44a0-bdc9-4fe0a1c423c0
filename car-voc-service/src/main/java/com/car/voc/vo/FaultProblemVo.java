package com.car.voc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 *
 * @version 1.0.0
 * @ClassName FaultProblem.java
 * @Description TODO
 * @createTime 2022年10月09日 11:50
 * @Copyright voc
 */
@Data
@ApiModel(value="故障问题对象", description="VOC_FAULT_PROBLEM")
public class FaultProblemVo {


    /**id*/
    @ApiModelProperty(value = "id")
    private String id;
    /**name*/

    @ApiModelProperty(value = "name")
    private String name;
    /**code*/

    @ApiModelProperty(value = "code")
    private String code;

    /**pId*/

    @ApiModelProperty(value = "pid")
    private String pid;
    /**级别*/

    @ApiModelProperty(value = "级别")
    private Integer level;
    /**是否有子节点*/

    @ApiModelProperty(value = "是否有子节点")
    private Integer hasChild;
    /**orderBy*/

    @ApiModelProperty(value = "orderBy")
    private Integer orderBy;
    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
    /**delFlag*/

    @ApiModelProperty(value = "delFlag")
    private Integer delFlag;

    @ApiModelProperty(value = "是否应用")
    private boolean enable;

    @ApiModelProperty(value = "严重性(dictCode=seriousness_type)")
    @Dict(dicCode = "seriousness_type")
    private Integer seriousness;


    @ApiModelProperty(value = "最后一级指标")
    private String lastTagCode;



    @ApiModelProperty(value = "标签")
    private String firstDimensionName;

    @ApiModelProperty(value = "标签")
    private String firstDimensionCode;

    @ApiModelProperty(value = "标签")
    private String secondDimensionName;

    @ApiModelProperty(value = "标签")
    private String secondDimensionCode;

    @ApiModelProperty(value = "标签")
    private String thirdDimensionName;

    @ApiModelProperty(value = "标签")
    private String thirdDimensionCode;

    @ApiModelProperty(value = "标签")
    private String topicName;

    @ApiModelProperty(value = "标签")
    private String topicCode;

    @ApiModelProperty(value = "标签")
    private String problemName1;

    @ApiModelProperty(value = "标签")
    private String problemCode1;

    @ApiModelProperty(value = "标签")
    private String problemName2;

    @ApiModelProperty(value = "标签")
    private String problemCode2;

    @ApiModelProperty(value = "标签")
    private String problemName3;

    @ApiModelProperty(value = "标签")
    private String problemCode3;

    @ApiModelProperty(value = "标签")
    private String problemName4;

    @ApiModelProperty(value = "标签")
    private String problemCode4;

    String describe;



}
