package com.car.voc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSIntentionAnalysisProportionVo.java
 * @Description TODO
 * @createTime 2023年07月31日 10:24
 * @Copyright voc
 */
@Data
public class NPSIntentionAnalysisProportionVo {
    @ApiModelProperty(value = "日期")
    String dateStr;

    /**
     * 咨询
     */
    @ApiModelProperty(value = "咨询")
    String consult;
    /**
     * 负面
     */
    @ApiModelProperty(value = "投诉")
    String complaint;
    /**
     * 负面
     */
    @ApiModelProperty(value = "表扬")
    String praise;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "建议")
    String suggest;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "其他")
    String other;

    /**
     * 咨询
     */
    @ApiModelProperty(value = "咨询比例")
    String consultP;
    /**
     * 负面
     */
    @ApiModelProperty(value = "投诉比例")
    String complaintP;
    /**
     * 负面
     */
    @ApiModelProperty(value = "抱怨比例")
    String praiseP;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "建议比例")
    String suggestP;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "其他比例")
    String otherP;

    /**
     * 咨询
     */
    @ApiModelProperty(value = "咨询环比")
    String consultC;
    /**
     * 负面
     */
    @ApiModelProperty(value = "投诉环比")
    String complaintC;
    /**
     * 负面
     */
    @ApiModelProperty(value = "抱怨环比")
    String complainC;
    /**
     * 正面比例
     */
    @ApiModelProperty(value = "建议环比")
    String suggestC;
    /**
     * 负面比例
     */
    @ApiModelProperty(value = "其他环比")
    String otherC;
}
