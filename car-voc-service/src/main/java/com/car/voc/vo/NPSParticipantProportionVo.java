package com.car.voc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0.0
 * @ClassName NPSOverviewVo.java
 * @Description TODO
 * @createTime 31 09:52
 * @Copyright voc
 */
@Data
//@EqualsAndHashCode(of ={"dateStr","dayAvgParentScores","topCode",})
@ApiModel(value = "NPS分析对象", description = "NPS调研人数统计")
public class NPSParticipantProportionVo implements Serializable {

    @ApiModelProperty(value = "诋毁者")
    String detractor;
    @ApiModelProperty(value = "中立者")
    String neutral;
    @ApiModelProperty(value = "推荐者")
    String recommend;
    @ApiModelProperty(value = "诋毁者比例")
    String detractorP;
    @ApiModelProperty(value = "中立者比例")
    String neutralP;
    @ApiModelProperty(value = "推荐者比例")
    String recommendP;
    @ApiModelProperty(value = "诋毁者环比")
    String detractorC;
    @ApiModelProperty(value = "中立者环比")
    String neutralC;
    @ApiModelProperty(value = "推荐者环比")
    String recommendC;

    @ApiModelProperty(value = "调研参与者中总人数")
    String total;
    @ApiModelProperty(value = "净推荐占比")
    String totalRecommendP;
}
