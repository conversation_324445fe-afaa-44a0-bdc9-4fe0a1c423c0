package com.car.voc.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSDataVo.java
 * @Description TODO
 * @createTime 2023年07月31日 10:24
 * @Copyright voc
 */
@Data
public class NPSDataVo {
    @ExcelIgnore
    String id;
    @ExcelProperty(value = "问卷编号")
    @ApiModelProperty(value = "问卷编号")
    String questionCode;
    @ExcelProperty(value = "问卷名称")
    @ApiModelProperty(value = "问卷名称")
    String questionName;
    @ExcelProperty(value = "答卷编号")
    @ApiModelProperty(value = "答卷编号")
    String answerCode;
    @ExcelIgnore
    @ApiModelProperty(value = "用户类型")
    String userType;
    @ExcelProperty(value = "渠道")
    @ApiModelProperty(value = "渠道")
    String contentType;

    @ExcelIgnore
    String channelId;
    @ExcelProperty(value = "阶段")
    @ApiModelProperty(value = "阶段")
    String stage;
    @ExcelProperty(value = "状态")
    @ApiModelProperty(value = "状态")
    String status;
    @ExcelProperty(value = "开始时间")
    @ApiModelProperty(value = "开始时间")
    String startTime;
    @ExcelProperty(value = "结束时间")
    @ApiModelProperty(value = "结束时间")
    String endTime;
    @ExcelProperty(value = "省份")
    @ApiModelProperty(value = "省份")
    String province;
    @ExcelIgnore
    @ApiModelProperty(value = "省份编码")
    String provinceCode;
    @ExcelProperty(value = "城市")
    @ApiModelProperty(value = "城市")
    String cityCode;
    @ExcelProperty(value = "服务站号")
    @ApiModelProperty(value = "服务站号")
    String serviceStationNum;
    @ExcelProperty(value = "分厂号")
    @ApiModelProperty(value = "分厂号")
    String affiliatedFactoryNum;
    @ExcelProperty(value = "购车时间")
    @ApiModelProperty(value = "购车时间")
    String carSpecificTime;
    @ExcelProperty(value = "品牌")
    @ApiModelProperty(value = "品牌")
    String brandCode;
    @ExcelProperty(value = "车型")
    @ApiModelProperty(value = "车型")
    String carSeriesCode;
    @ExcelIgnore
    @ApiModelProperty(value = "车分类")
    String carType;
    @ExcelProperty(value = "底盘号")
    @ApiModelProperty(value = "底盘号")
    String chassisNumber;
    @ExcelProperty(value = "客户姓名")
    @ApiModelProperty(value = "客户姓名")
    String clientName;
    @ExcelProperty(value = "性别")
    @ApiModelProperty(value = "性别")
    String clientSex;
    @ExcelProperty(value = "电话")
    @ApiModelProperty(value = "电话")
    String clientPhone;
    @ExcelProperty(value = "分值")
    @ApiModelProperty(value = "分值")
    String scores;
    @ExcelProperty(value = "分类")
    @ApiModelProperty(value = "分类")
    String classify;
    @ExcelProperty(value = "问题")
    @ApiModelProperty(value = "问题")
    String question;
    @ExcelProperty(value = "问题分类")
    @ApiModelProperty(value = "问题分类")
    String problemClassify;
    @ExcelProperty(value = "追问-备注")
    @ApiModelProperty(value = "追问-备注")
    String questionCloselyNotes;
    @JsonProperty("soundsContent")
    @ExcelProperty(value = "声音片段内容")
    @ApiModelProperty(value = "声音片段内容")
    String sentence;

    @ExcelIgnore
    @ApiModelProperty(value = "用户标识")
    String userId;
    @ExcelProperty(value = "能源类型")
    @ApiModelProperty(value = "能源类型")
    String energyType;

    @ExcelProperty(value = "情感")
    @ApiModelProperty(value = "情感")
    String dimensionEmotion;
    @ApiModelProperty(value = "意图")
    @ExcelProperty(value = "意图")
    String intentionType;
    @ExcelProperty(value = "一级标签")
    @ApiModelProperty(value = "一级标签")
    String firstDimensionCode;
    @ExcelProperty(value = "二级标签")
    @ApiModelProperty(value = "二级标签")
    String secondDimensionCode;
    @ExcelProperty(value = "三级标签")
    @ApiModelProperty(value = "三级标签")
    String thirdDimensionCode;
    @ExcelProperty(value = "四级标签")
    @ApiModelProperty(value = "四级标签")
    String topicCode;
    @ExcelIgnore
    @ApiModelProperty(value = "数据类型")
    String dataType;
}
