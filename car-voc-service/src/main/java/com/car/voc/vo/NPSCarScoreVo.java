package com.car.voc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName NPSCarScoreVo.java
 * @Description TODO
 * @createTime 2023年07月31日 10:24
 * @Copyright voc
 */
@Data
public class NPSCarScoreVo {

    @ApiModelProperty(value = "车型")
    String carSeries;
    /**
     * 诋毁者
     */
    @ApiModelProperty(value = "诋毁者")
    String detractor;

    /**
     * 中立者
     */
    @ApiModelProperty(value = "中立者")
    String neutral;
    /**
     * 推荐者
     */
    @ApiModelProperty(value = "推荐者")
    String recommend;

    /**
     * 诋毁者比例
     */
    @ApiModelProperty(value = "诋毁者比例")
    String detractorP;
    /**
     * 中立者比例
     */
    @ApiModelProperty(value = "中立者比例")
    String neutralP;
    /**
     * 推荐者比例
     */
    @ApiModelProperty(value = "推荐者比例")
    String recommendP;

    @ApiModelProperty(value = "诋毁者环比")
    String detractorC;
    /**
     * 中立者比例
     */
    @ApiModelProperty(value = "中立者环比")
    String neutralC;
    /**
     * 推荐者比例
     */
    @ApiModelProperty(value = "推荐者环比")
    String recommendC;
    /**
     * 参与人数
     */
    @ApiModelProperty(value = "参与人数")
    String total;

    /**
     * 净推荐占比：推荐者的人数/总人数
     */
    @ApiModelProperty(value = "净推荐占比")
    String netPromoter;
}
