package com.car.voc.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewDictVo implements Serializable{
	private static final long serialVersionUID = 1L;

	private String brandCode;

	private String areaCode;

	private String itemText;

	private String provinceTexts;

	private String provinceCodes;
}
