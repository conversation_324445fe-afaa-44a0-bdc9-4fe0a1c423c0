package com.car.voc.vo;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class WoTypeVo {
    private String woType;
    private String status;
    private String area;

    private BigDecimal statistic;
    private BigDecimal satisfactionNum;
    private BigDecimal unSatisfactionNum;
    private BigDecimal satisfactionNumP;
    private BigDecimal unSatisfactionNumP;
    private BigDecimal userNum;
    private BigDecimal closeCount;
    private BigDecimal openCount;
    private BigDecimal statisticP;
    private BigDecimal statisticR;

    private String dateStr;
    private String satisfaction;

}
