package com.car.voc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @version 1.0.0
 * @ClassName NPSOverviewVo.java
 * @Description TODO
 * @createTime 31 09:52
 * @Copyright voc
 */
@Data
//@EqualsAndHashCode(of ={"dateStr","dayAvgParentScores","topCode",})
@ApiModel(value = "NPS分析对象", description = "NPS调研人数统计")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NPSParticipantScoreVo implements Serializable {

    @ApiModelProperty( value = "区域编码")
    String areaCode;
    @ApiModelProperty(value = "区域平均值")
    String areaScores;
    @ApiModelProperty( value = "区域环比值")
    String areaChianScores;

    @ApiModelProperty(value = "全国得分")
    String nationalScores;
    @ApiModelProperty(value = "全国得分环比")
    String nationalScoresChian;

    @ApiModelProperty(value = "全国排名总数")
    String nationalRanking;
    @ApiModelProperty( value = "全国排名")
    String nationalAreaCount;
    @ApiModelProperty( value = "全国排名环比")
    String nationalRankingChian;

    @ApiModelProperty( value = "全国所有区域得分")
    List<NPSAreaScoreVo> areaList = new ArrayList<>();
}
