package com.car.voc.vo.risk;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.entity.VocRiskHandlingRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * VOC_RISK_WARNING_RECORD
 *
 */
@ApiModel(value="风险处理跟进列表、风险详情、返回对象")
@Data
public class RiskAllTypesVo implements Serializable {
    private String id;
    @ApiModelProperty(value="统计周期:d,w,m,q,y")
    private String statisticType;
    private String warnPeriod;
    /**
     * 风险类型
     */
    @ApiModelProperty(value="风险类型:1为业务,2为质量,3为用户")
    private Integer riskType;

    /**
     * 警示时间
     */
    @ApiModelProperty(value="警示时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer delFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 风险名称
     */
    @ApiModelProperty(value="风险名称")
    private String riskName;

    /**
     * 相关提及量
     */
    @ApiModelProperty(value="相关提及量")
    private BigDecimal statistic;

    @ApiModelProperty(value="风险程度值")
    private BigDecimal riskIndex;
    @ApiModelProperty(value="风险等级")
    private String riskLevel;
    private Integer riskLevelNum;
    private Integer riskLevelNum1;
    @ApiModelProperty(value="新风险等级")
    private String newRiskLevel;
    @ApiModelProperty(value="品牌code")
    private String brandCode;
    /**
     * 风险点Id
     */
    @ApiModelProperty(value="风险点Id")
    private String riskId;

    /**
     * 发声用户
     */
    @ApiModelProperty(value="发声用户")
    private Long userNum;
    private Long warningNum;

    /**
     * 状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)
     */
    @ApiModelProperty(value="状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)")
    private Integer riskState;

    private Integer getAuditStatus(){
        return riskState==0?riskState:1;
    }

    /**
     * 审核时间
     */
    @ApiModelProperty(value="审核时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty("取消时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /**
     * 审核id
     */
    @ApiModelProperty(value="审核员userId")
    private String auditUserId;
    private String auditUserName;
    private Integer bindType;
    /**
     * 审核id
     */
    @ApiModelProperty(value="审核员组织Id")
    private String auditDepartId;
    private String auditDepartName;

    @ApiModelProperty(value="用户id或code")
    private String risk;

    /**
     * 确认时间
     */
    @ApiModelProperty(value="确认时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date confirmationTime;

    /**
     * 是否构成风险 是否风险
     */
    @ApiModelProperty(value="是否构成风险")
    private boolean ifRisk;

    private List<WarningReviewList> warningReviewListList;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="风险审核人信息")
    RiskAlertReviewerVo reviewerVo;
    @ApiModelProperty(value="风险默认处理人信息")
    RiskProcessRecipientVo recipientVo;
    @ApiModelProperty(value="风险相关数据信息")
    RiskInfoVo riskInfoVo;

    @ApiModelProperty(value="风险所有处理人记录信息")
    List<VocRiskHandlingRecord> riskHandlingRecords;
    @ApiModelProperty(value="风险警示次数")
    List<Object> riskWarningNum;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    @ApiModelProperty(value="处理时长")
    private String processingTime;

    @ApiModelProperty(value="处理天数")
    private String processingDays;

    @ApiModelProperty(value="处理频次")
    private String processingFrequency;

    @ApiModelProperty(value="回显部门id")
    private String processDepartId;

    @ApiModelProperty(value="回显用户id")
    private String processUserId;

//    public String getRiskLevel() {
//        return CalculatorUtils.getRiskLevel(this.riskIndex);
//        return "";
//    }

    @ApiModelProperty(value = "开始日期",example = "2022-06-01 00:00:00")
    String startDate;
    @ApiModelProperty(value = "结束日期",example = "2022-10-01 00:00:00")
    String endDate;
    @ApiModelProperty(value = "日期单位(0为周，1为月，2为季，3为年度，-1为自定义)",example = "1")
    Integer dateUnit;


    private String departName;

    private String departId;

    private Integer total;

    private Integer complete;

    private Boolean finishTag;

    @ApiModelProperty("是否有重新派发按钮")
    private Boolean isDistribution;

    public String getProcessingFrequency() {
        return StrUtil.isBlankIfStr(processingFrequency)?"0":processingFrequency;
    }

//表内新增risk_level字段
//    public String getRiskLevel() {
////        IVocRiskWarningRulesService riskWarningRulesService = (IVocRiskWarningRulesService) SpringContextUtils.getBean("IVocRiskWarningRulesService");
////        1为业务,2为质量,3为用户
////        String riksTypeName = "";
////        if(this.riskType == 1){
////            riksTypeName = "风险事件洞察";
////        }else if(this.riskType == 2){
////            riksTypeName = "质量问题风险";
////        }else if(this.riskType == 3){
////            riksTypeName = "高频投诉用户";
////        }
////        String riskLevel = riskWarningRulesService.getWarnRuleDetailListByIdBrandCode(riksTypeName, this.brandCode, this.riskIndex);
//        return VocCalculatorUtils.getRiskLevel(this.riskIndex);
////        return riskLevel;
////        return "";
//    }

    public String getRiskName() {
        if (StrUtil.isNotBlank(this.riskName)&&this.riskType!=3){
            if (this.riskName.length()>20){
                return this.riskName;
            }else {
                return CalculatorUtils.getAllTagCode(this.riskName);
            }
        }else {
            return riskName;
        }
    }
    Integer isOneId=1;
}
