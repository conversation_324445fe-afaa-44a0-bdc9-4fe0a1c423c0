package com.car.voc.vo.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.entity.VocRiskProcessRecipientBase;
import com.car.voc.vo.VocBusinessTagVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName RiskAlertReviewerVo.java
 * @Description TODO
 * @createTime 2023年02月06日 10:41
 * @Copyright voc
 */
@Data
@TableName(autoResultMap = true)
public class WarningReviewBrandTagVo implements Serializable {

    @ApiModelProperty(value = "brandCode")
    private String brandCode;

    @ApiModelProperty(value = "标签树")
    private List<VocBusinessTagVo> businessTagList;

    @ApiModelProperty(value = "质量标签")
    private VocRiskProcessRecipientBase qualityTag;

    @ApiModelProperty(value = "投诉用户")
    private VocRiskProcessRecipientBase topUser;

    @ApiModelProperty(value = "救援故障")
    private VocRiskProcessRecipientBase recipientRescue;

    @ApiModelProperty(value = "网点风险")
    private VocRiskProcessRecipientBase recipientBranches;


    @ApiModelProperty(value = "标签是否选中")
    private Boolean selected = false;

    @ApiModelProperty("是否有质量标签权限")
    private Boolean qualityPermission;

    @ApiModelProperty("是否有用户投诉权限")
    private Boolean userPermission;
}
