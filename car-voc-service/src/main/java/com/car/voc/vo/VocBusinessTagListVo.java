package com.car.voc.vo;

import com.car.voc.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.json.JSONArray;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Description: voc_business_tag
 *
 * @Date:   2021-03-30
 * @Version: V1.0
 */
@Data
@ApiModel(value="voc_business_tag对象", description="voc_business_tag")
public class VocBusinessTagListVo implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "一级名称")
	private String firstDimensionName;

	@ApiModelProperty(value = "一级Code")
	private String firstDimensionCode;

	@ApiModelProperty(value = "二级名称")
	private String secondDimensionName;
	@ApiModelProperty(value = "二级Code")
	private String secondDimensionCode;
	@ApiModelProperty(value = "三级名称")
	private String thirdDimensionName;
	@ApiModelProperty(value = "三级Code")
	private String thirdDimensionCode;
	@ApiModelProperty(value = "四级名称")
	private String topicName;
	@ApiModelProperty(value = "四级Code")
	private String topicCode;
	private String relatedDepartments;
	private String relatedDepartmentsName;

	@ApiModelProperty(value = "是否应用")
	private Integer enable;

	@ApiModelProperty(value = "部门")
	private Set<String> departments;
	@ApiModelProperty(value = "角色")
	private Set<String> roles;
	@ApiModelProperty(value = "角色Ids")
	private Set<String> rolesIds;
	private String orderType;

	/**编号*/
	@ApiModelProperty(value = "编号")
	private String id;
	/**名称*/
	//@Excel(name = "名称", width = 15)
	@ApiModelProperty(value = "名称")
	private String name;
	/**英文名称*/
	//@Excel(name = "英文名称", width = 15)
	@ApiModelProperty(value = "英文名称")
	private String nameEn;
	/**编码*/
	//@Excel(name = "编码", width = 15)
	@ApiModelProperty(value = "编码")
	private String tagCode;
	/**排序*/
	//@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
	private Integer orderBy;
	/**层级*/
	//@Excel(name = "层级", width = 15)
	@ApiModelProperty(value = "层级")
	private String hierarchy;
	private String industryId;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "createTime")
	private Date createTime;
	/**标签数*/
	//@Excel(name = "标签数", width = 15)
	@ApiModelProperty(value = "标签数")
	private Integer tagData;
	/**备注*/
	//@Excel(name = "备注", width = 15)
	@ApiModelProperty(value = "相关描述")
	private String relatedDescription;
	/**yndel*/
	//@Excel(name = "yndel", width = 15)
	@ApiModelProperty(value = "yndel")
	private String yndel;
	/**是否有子节点*/
	//@Excel(name = "是否有子节点", width = 15)
	@ApiModelProperty(value = "是否有子节点")
	private String hasChild;
	/**父级节点*/
	//@Excel(name = "父级节点", width = 15)
	@ApiModelProperty(value = "父级节点")
	private String pid;
	@Dict(dicCode = "tag_application_type")
	private String tagScope;

}
