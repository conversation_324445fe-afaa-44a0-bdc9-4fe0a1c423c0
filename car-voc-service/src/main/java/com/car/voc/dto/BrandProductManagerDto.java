package com.car.voc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BrandProductManagerDto implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "别名(多个以(,)分隔")
    private String alias;

    /**
     * 车辆类型
     */
    @ApiModelProperty(value = "车辆类型(dictCode=car_type)存中文")
    private String carType;

    @ApiModelProperty(value = "中文名称")
    private String name;

    /**
     * 英文名
     */
    //@Excel(name = "英文名", width = 15)
    @ApiModelProperty(value = "英文名")
    private String englishName;

    @ApiModelProperty(value = "父级节点")
    private String pId;
}
