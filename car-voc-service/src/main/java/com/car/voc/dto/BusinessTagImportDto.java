package com.car.voc.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 *
 * @version 1.0.0
 * @ClassName FaultProblemImportDto.java
 * @Description TODO
 * @createTime 2023年03月13日 13:44
 * @Copyright voc
 */
@Data
public class BusinessTagImportDto {

    @ExcelProperty("一级标签分类")
    String tag1;
    @ExcelProperty("First-level Indicators(Category)")
    String tage1;
    @ExcelProperty("二级标签分类")
    String tag2;
    @ExcelProperty("Second-level Indicators(Classification)")
    String tage2;
    @ExcelProperty("三级标签分类")
    String tag3;
    @ExcelProperty("Third-level Indicators(Items)")
    String tage3;
    @ExcelProperty("四级标签分类")
    String tag4;
    @ExcelProperty("Fourth-level Indicators(Fault phenomenon/Subitems)")
    String tage4;
    @ExcelProperty("问题及描述")
    String describe;
    @ExcelProperty("应用端")
    String applicationSide;
    @ExcelProperty("主责部门")
    String relatedDepartments;
    @ExcelProperty("排序")
    Integer orderBy;
}
