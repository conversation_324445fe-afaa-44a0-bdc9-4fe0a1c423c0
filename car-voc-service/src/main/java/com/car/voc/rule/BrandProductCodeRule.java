package com.car.voc.rule;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.car.voc.common.handler.IFillRuleHandler;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.common.util.YouBianCodeUtil;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.mapper.BrandProductManagerMapper;

import java.util.List;

/**
 *
 * @Date 2019/12/9 11:32
 * @Description: 分类字典编码生成规则
 */
public class BrandProductCodeRule implements IFillRuleHandler {

    public static final String ROOT_PID_VALUE = "0";

    @Override
    public Object execute(JSONObject params, JSONObject formData) {

        String categoryPid = ROOT_PID_VALUE;
        String categoryCode = null;

        if (formData != null && formData.size() > 0) {
            Object obj = formData.get("pid");
            if (!StrUtil.isBlankIfStr(obj)) {
                categoryPid = obj.toString();
            }
        } else {
            if (params != null) {
                Object obj = params.get("pid");
                if (!StrUtil.isBlankIfStr(obj)) {
                    categoryPid = obj.toString();
                }
            }
        }

        /*
         * 分成三种情况
         * 1.数据库无数据 调用YouBianCodeUtil.getNextYouBianCode(null);
         * 2.添加子节点，无兄弟元素 YouBianCodeUtil.getSubYouBianCode(parentCode,null);
         * 3.添加子节点有兄弟元素 YouBianCodeUtil.getNextYouBianCode(lastCode);
         * */
        //找同类 确定上一个最大的code值
        LambdaQueryWrapper<BrandProductManager> query = new LambdaQueryWrapper<BrandProductManager>().eq(BrandProductManager::getPId, categoryPid).isNotNull(BrandProductManager::getCode).orderByDesc(BrandProductManager::getCode);
        query.eq(BrandProductManager::getDelFlag,0);
        BrandProductManagerMapper baseMapper = (BrandProductManagerMapper) SpringContextUtils.getBean("brandProductManagerMapper");
        List<BrandProductManager> list = baseMapper.selectList(query);
        if (list == null || list.size() == 0) {
            if (ROOT_PID_VALUE.equals(categoryPid)) {
                //情况1
                categoryCode = YouBianCodeUtil.getNextYouBianCode(null);
            } else {
                //情况2
                BrandProductManager parent = (BrandProductManager) baseMapper.selectById(categoryPid);
                categoryCode = YouBianCodeUtil.getSubYouBianCode(parent.getCode(), null);
            }
        } else {
            //情况3
            categoryCode = YouBianCodeUtil.getNextYouBianCode(list.get(0).getCode());
        }
        return categoryCode;
    }


}
