package com.car.voc.cach;

import cn.hutool.core.bean.BeanUtil;
import com.car.stats.model.DateModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.voc.common.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MyCacheKeyGenerator implements KeyGenerator {
    @Override
    public Object generate(Object o, Method method, Object... objects) {
        String s = "";
        if(objects[0] instanceof FilterCriteriaModel || objects[0] instanceof DateModel){
            Map<String,Object> map = BeanUtil.beanToMap(objects[0]);
            if(map.containsKey("userId")){
                map.remove("userId");
            }if(map.containsKey("accessToken")){
                map.remove("accessToken");
            }
            StringBuilder sb = new StringBuilder();
            s = sortSign(map, sb);
        }else {
            s = Arrays.asList(objects).stream().map(String::valueOf).collect(Collectors.joining());
        }
        String key=MD5Util.MD5Encode(s,null);
        log.info("key:{}",key);
        return key;
    }
    public static String sortSign(Object obj, StringBuilder result) {
        if(obj == null) {
            return null;
        }
        if (obj instanceof Map) {
            Map<String, Object> map = (Map) obj;
            String[] keys = map.keySet().toArray(new String[0]);
            Arrays.sort(keys);
            StringBuilder rs = new StringBuilder();
            for (String mapKey : keys) {
                Object value = map.get(mapKey);
                if(value == null) {
                    continue;
                }
                if (StringUtils.isNotBlank(mapKey)) {
                    String s = sortSign(value, rs);
                    if (!StringUtils.isBlank(s)) {
                        rs.append(mapKey).append(s);
                    }
                }
            }
            return rs.toString();
        } else if (obj instanceof List) {
            List<Object> list = (List) obj;
            list.stream().map(String::valueOf).collect(Collectors.toList())
                    .sort(Comparator.comparing(String::valueOf, Comparator.nullsFirst(String::compareTo)).reversed());
            StringBuilder rs = new StringBuilder();
            for (Object o : list) {
                rs.append(sortSign(o, rs));
            }
            return rs.toString();
        } else if (obj instanceof Set) {
            Set<Object> list = (Set) obj;
            StringBuilder rs = new StringBuilder();
            for (Object o : list) {
                rs.append(sortSign(o, rs));
            }
            return rs.toString();
        } else if (isBasicType(obj.getClass())) {
            return valueOf(obj);
        }
        return "";
    }
    public static String valueOf(Object obj) {
        if (obj == null) {
            return "";
        }
        return String.valueOf(obj);
    }
    public static boolean isBasicType(Class clazz) {
        return isNumberType(clazz) || clazz == String.class || clazz.isEnum()
                || Boolean.class == clazz || boolean.class == clazz || byte.class == clazz
                || Byte.class == clazz || byte[].class == clazz || Byte[].class == clazz || BigDecimal.class == clazz
                || Date.class == clazz || clazz.isEnum();
    }

    public static boolean isNumberType(Class clazz) {
        return clazz.getSuperclass() == Number.class || clazz == int.class || clazz == long.class
                || clazz == double.class || clazz == float.class || clazz == short.class;
    }
}
