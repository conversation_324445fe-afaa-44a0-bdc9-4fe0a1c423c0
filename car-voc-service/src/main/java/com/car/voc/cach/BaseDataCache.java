package com.car.voc.cach;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.dto.BrandProductManagerDto;
import com.car.voc.dto.TagCacheDto;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.VocBrandRegionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 初始化缓存数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service("baseDataCache")
@AllArgsConstructor
public class BaseDataCache {

    private final IBrandProductManagerService brandProductManagerService;

    private final RedisUtil redisUtil;

    private final VocBrandRegionService vocBrandRegionService;

    private final IVocBusinessTagService businessTagService;

    private final IFaultProblemService faultProblemService;


    @PostConstruct
    public void init() {
        // 品牌系列产品缓存
        LambdaQueryWrapper<BrandProductManager> objectQueryWrapper = new QueryWrapper<BrandProductManager>().lambda();
        objectQueryWrapper.select(BrandProductManager::getCode);
        objectQueryWrapper.eq(BrandProductManager::getPId, "0");
        List<BrandProductManager> list = brandProductManagerService.list(objectQueryWrapper);
        list.forEach(brandProductManager -> {
            this.brandSeriesAll(brandProductManager.getCode());
        });
        // 品牌系列产品地区缓存
        LambdaQueryWrapper<VocBrandRegion> query = new QueryWrapper<VocBrandRegion>().lambda();
        query.select(VocBrandRegion::getBrand, VocBrandRegion::getProvinceCode, VocBrandRegion::getProvinceName,
                VocBrandRegion::getRegionalCode, VocBrandRegion::getRegionalName,
                VocBrandRegion::getCityCode, VocBrandRegion::getCityName,
                VocBrandRegion::getCommunityCode, VocBrandRegion::getCommunityName);
        List<VocBrandRegion> brandRegionlist = vocBrandRegionService.list(query);
        Map<String, List<VocBrandRegion>> regionMap = brandRegionlist.stream()
                .collect(Collectors.groupingBy(VocBrandRegion::getBrand));
        regionMap.forEach((brandCode, v) -> {
            String formatKey = String.format(CacheConstant.SYS_AREA_BRANDCODE_PROVINCE_ALL, brandCode);
            if (!redisUtil.hasKey(formatKey)) {
                redisUtil.sSetAllObject(formatKey, v);
            }
        });
        log.info("初始化品牌区域缓存:{}",regionMap.size());

        // 标签缓存
        Set<TagCacheDto> tagCacheDtos = this.tagAll();
        log.info("初始化标签缓存:{}",tagCacheDtos.size());
    }

    public Set<BrandProductManagerDto> brandSeriesAll(String code) {
        return brandProductManagerService.brandSeriesAll(code);
    }

    public Set<TagCacheDto> faultProblemAll(String code) {
        return faultProblemService.faultProblemAll();
    }

    public Set<TagCacheDto> businessTagAll(String code) {
        return businessTagService.businessTagAll();
    }

    public Set<TagCacheDto> tagAll() {
        // 标签缓存
        Set<TagCacheDto> tagCacheDtos = this.businessTagAll("0");
        tagCacheDtos.addAll( this.faultProblemAll("0"));
        return tagCacheDtos;
    }

}
