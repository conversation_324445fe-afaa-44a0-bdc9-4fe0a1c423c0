package com.car.voc.task;

import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskComplaintUserJob.java
 * @Description 风险事件警示
 * @createTime 2023年02月07日 13:51
 * @Copyright voc
 */
//@Configuration
//@EnableS
// cheduling
@Slf4j
@Component
public class VocRiskEventJob {
    @Resource
    VocRiskJobService eventJobService;
    @Resource
    VocRiskJobService userJobService;
    private static Logger logger = LoggerFactory.getLogger(VocRiskEventJob.class);

    //    每天凌晨1点执行一次：0 0 1 * * ?
//    @Scheduled(cron = "0 0 1 * * ?")
    private final Lock lock = new ReentrantLock();

    @XxlJob("vocRiskEventJob")
    public void vocRiskEventJob() throws Exception {
        lock.lock(); // 获取锁
        try {
            log.info("=====================开始执行风险的预警=======================");
            logger.info("=====================开始执行风险的预警=======================");
            eventJobService.vocRiskEventJob();
            eventJobService.vocRiskQualityJob();
            eventJobService.vocRiskRescueJob();
            eventJobService.vocRiskBranchesJob();
            userJobService.vocRiskUserJob();
            logger.info("=====================业务风险的预警结束==========================");
            log.info("=====================业务风险的预警结束==========================");
        } finally {
            lock.unlock(); // 释放锁
        }

    }
}
