package com.car.voc.task;

import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskComplaintUserJob.java
 * @Description 高频用户投诉警示
 * @createTime 2023年02月07日 13:51
 * @Copyright voc
 */
@Slf4j
@Component
public class VocRiskUserJob {
    @Resource
    VocRiskJobService userJobService;
    private static Logger logger = LoggerFactory.getLogger(VocRiskUserJob.class);

    //    每天凌晨1点执行一次：0 0 1 * * ?
    //    @Scheduled(cron = "0 0 1 * * ?")
    @XxlJob("vocRiskUserJob")
    public void vocRiskUserJob(){
        log.info("=====================开始执行用户风险的预警=======================");
        logger.info("=====================开始执行用户风险的预警=======================");
        userJobService.vocRiskUserJob();
        logger.info("=====================执行用户风险的预警结束=======================");
        log.info("=====================执行用户风险的预警结束=======================");
    }

}
