package com.car.voc.task;

import cn.hutool.core.util.ObjectUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * @version 1.0.0
 * @ClassName
 * @Description 缓存清除
 * @Copyright voc
 */
@Slf4j
@Component
public class VocRedisClearKeyJob {
    @Resource
    RedisUtil redisUtil;
    private static Logger logger = LoggerFactory.getLogger(VocRedisClearKeyJob.class);


    @XxlJob("vocRedisClearJob")
    public void vocRedisClearJob(){
        String keyPrefix= XxlJobHelper.getJobParam();
        if (ObjectUtil.isNotEmpty(keyPrefix)){
            log.info("=====================开始执行删除指定前缀{}的一系列key=======================",keyPrefix);
            logger.info("=====================开始执行删除指定前缀{}的一系列key=======================",keyPrefix);
            redisUtil.removeAll(keyPrefix);
            logger.info("=====================执行删除指定前缀{}的一系列key结束=======================",keyPrefix);
            log.info("=====================执行删除指定前缀{}的一系列key结束=======================",keyPrefix);
        }

    }

}
