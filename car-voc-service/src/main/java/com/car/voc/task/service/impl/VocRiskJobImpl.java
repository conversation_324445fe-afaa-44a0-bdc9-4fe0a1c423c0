package com.car.voc.task.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.car.stats.entity.risk.*;
import com.car.stats.serivce.IDwdVocDealerRiskService;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.IDwdVocRiskService;
import com.car.stats.serivce.IDwdVocUserRiskService;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.RiskTypeEnum;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.entity.VocRiskWarningRecord;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.service.IVocRiskAllTypesService;
import com.car.voc.service.IVocRiskWarningRecordService;
import com.car.voc.service.IVocRiskWarningRulesDetailedService;
import com.car.voc.service.IVocRiskWarningRulesService;
import com.car.voc.task.service.VocRiskJobService;
import com.car.voc.vo.risk.RiskRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskUserJobImpl.java
 * @Description TODO
 * @createTime 2023年02月07日 14:01
 * @Copyright voc
 */
@Slf4j
@Service
public class VocRiskJobImpl implements VocRiskJobService {
    @Autowired
    IVocRiskWarningRulesService rulesService;
    @Autowired
    IVocRiskWarningRulesDetailedService rulesDetailedService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IDwdVocUserRiskService userRiskService;

    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    IDwdVocQualityRiskService qualityRiskService;
    @Autowired
    IDwdVocDealerRiskService vocDealerRiskService;


    @Override
    public void vocRiskUserJob() {
        riskUserFilteringNew();
    }

    @Override
    public void vocRiskEventJob() {
        riskEventFilteringNew();
    }

    @Override
    public void vocRiskQualityJob() {
        riskQualiytFilteringNew();
    }

    @Override
    public void vocRiskRescueJob() {
        riskRescueFilteringNew();
    }

    @Override
    public void vocRiskBranchesJob() {
        riskBranchesFiltering();
    }

    private void riskBranchesFiltering() {
        List<VocRiskWarningRules> ruleList = rulesService.getRiskType(RiskTypeEnum.VocRiskBranches.getName());
        for (VocRiskWarningRules riskWarningRules:ruleList) {
            if(ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                riskWarningRules.setPushConditionList(Arrays.asList(riskWarningRules.getPushCondition().split(",")));
            }
            String lasttime= (String) redisUtil.get(CacheConstant.SYS_RISK_RULE + "lastTime:" + riskWarningRules.getId());
            if (StrUtil.isNotBlank(lasttime)){
                riskWarningRules.setLastWarningTime((String) redisUtil.get(CacheConstant.SYS_RISK_RULE+"lastTime:" + riskWarningRules.getId()));
//                riskWarningRules.setLastWarningTime("2024-02-20 16:36:16");
            }
        }
        riskBranchesFilteringAdd(ruleList);
    }

    private void riskBranchesFilteringAdd(List<VocRiskWarningRules> rule) {
        List<DwdVocDealerRisk> emotionRiskList = new ArrayList<>();
        for (VocRiskWarningRules riskWarningRules:rule) {
            RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(riskWarningRules.getId());
            extracted(riskWarningRules, vo);
            List<DwdVocDealerRisk> list = vocDealerRiskService.riskBranchesFiltering(riskWarningRules,vo);

            //更新最后一查询时间
            redisUtil.set(CacheConstant.SYS_RISK_RULE+"lastTime:"+riskWarningRules.getId(),new DateTime().toString());
            UpdateWrapper<VocRiskWarningRules> riskRuleUpdate = new UpdateWrapper<>();
            riskRuleUpdate.lambda().eq(VocRiskWarningRules::getId,riskWarningRules.getId())
                    .set(VocRiskWarningRules::getLastWarningTime,new DateTime());
            rulesService.update(riskRuleUpdate);
            if (ObjectUtils.isNotEmpty(list)&&ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                emotionRiskList.addAll(list);
            }
        }
        //添加到风险审核列表及添加到记录表
        if(ObjectUtils.isNotEmpty(emotionRiskList)){
            addToRiskBranchesReviewList(emotionRiskList);
        }

        log.info("=====================网点风险预警结束==========================");
    }

    private void addToRiskBranchesReviewList(List<DwdVocDealerRisk> emotionRiskList) {
        List<VocRiskWarningRecord> records=new ArrayList<>();
        List<VocRiskAllTypes> riskAllTypes=new ArrayList<>();
        Set<String> set=new HashSet<>();
        for (DwdVocDealerRisk branchesRisk : emotionRiskList) {
            if (set.contains(branchesRisk.getBrandCode()+":"+branchesRisk.getDlrName())){
                continue;
            }
            set.add(branchesRisk.getBrandCode()+":"+branchesRisk.getDlrName());
            //==================1.警示风险记录==========start=================
            records.add(new VocRiskWarningRecord(branchesRisk));


            QueryWrapper<VocRiskAllTypes> wrapper=new QueryWrapper<>();
            wrapper.lambda()
                    .eq(VocRiskAllTypes::getRisk,branchesRisk.getDlrName())
                    .eq(VocRiskAllTypes::getBrandCode,branchesRisk.getBrandCode())
                    .eq(VocRiskAllTypes::getDelFlag,0)
                    .eq(VocRiskAllTypes::getRiskType,RiskTypeEnum.VocRiskBranches.getType())//风险类型(1:业务，2：质量，3：用户)
            ;
            VocRiskAllTypes onerisk=riskAllTypesService.getOne(wrapper);
            UpdateWrapper<VocRiskAllTypes> updateWrapper=new UpdateWrapper<>();


            //==================2.查询已经存在的风险点【待审核】则更新数据==========start=================

            if (onerisk!=null&&onerisk.getRiskState().equals(0)){//已经存在风险点(待审核)则更新数据 //等于0 状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理,4:部分处理)

                updateWrapper.lambda()
                        .eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getCreateTime,new DateTime())
                        .set(VocRiskAllTypes::getStatistic,branchesRisk.getNegativeNum())
                        .set(VocRiskAllTypes::getRiskId,branchesRisk.getId())
                        .set(VocRiskAllTypes::getWarningNum,onerisk.getWarningNum()+1);
                //判断是否更新风险等级risk_index
                //更改后的风险等级不存在，判断新risk_index与旧风险等级是否相同，相同则不更新，不相同则更新new_risk_level
                //20231012修改读取风险数据库
//                String eventRiskRiskLevel = CalculatorUtils.getRiskLevel(branchesRisk.getRiskIndex());
                String eventRiskRiskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(RiskTypeEnum.VocRiskBranches.getName(), branchesRisk.getBrandCode(),branchesRisk.getRiskIndex());
                if(ObjectUtils.isEmpty(onerisk.getNewRiskLevel())){
                    if(!onerisk.getRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda().set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }else{
                    //新风险等级存在，判断当前新风险等级newRiskLevel与eventRiskRiskLevel是否一致，一致则不更新，不一致则把newRiskLevel变为riskLevel,eventRiskRiskLevel变为newRiskLevel
                    if(!onerisk.getNewRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda()
                                .set(VocRiskAllTypes::getRiskLevel,onerisk.getNewRiskLevel())
                                .set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }

                riskAllTypesService.update(updateWrapper);
                continue;
            }
            //==================2.判断已经存在的风险点【待审核】则更新数据==========end=================


            //==================3.判断已经存在的风险点【已经处理完】则更新数据==========start=================

            //20231012修改读取风险数据库
            String riskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(RiskTypeEnum.VocRiskBranches.getName(),branchesRisk.getBrandCode(),branchesRisk.getRiskIndex());
            if (onerisk!=null&&onerisk.getRiskState().equals(3)){
                VocRiskAllTypes newrisk=new VocRiskAllTypes(branchesRisk);//新添加风险
                newrisk.setRiskLevel(riskLevel);
                newrisk.setWarningNum(onerisk.getWarningNum()+1);

                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda().eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getDelFlag,1);
                riskAllTypesService.update(updateWrapper);//软删除已经处理过的风险

                riskAllTypesService.save(newrisk);
                //==================3.判断已经存在的风险点【已经处理完】则更新数据==========end=================
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========start=================

            if (onerisk==null){//新的风险点
                VocRiskAllTypes  userrisk=new VocRiskAllTypes(branchesRisk);
                userrisk.setRiskLevel(riskLevel);
                riskAllTypes.add(userrisk);
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========end=================

        }
        if (records.size()>0){
            warningRecordService.saveBatch(records);
        }if (riskAllTypes.size()>0){
            riskAllTypesService.saveBatch(riskAllTypes);
        }
    }

    private void riskRescueFilteringNew() {
        List<VocRiskWarningRules> ruleList = rulesService.getRiskType(CommonConstant.sys_risk_rescue_name);
        for (VocRiskWarningRules riskWarningRules:ruleList) {
            if(ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                riskWarningRules.setPushConditionList(Arrays.asList(riskWarningRules.getPushCondition().split(",")));
            }
            String lasttime= (String) redisUtil.get(CacheConstant.SYS_RISK_RULE + "lastTime:" + riskWarningRules.getId());
            if (StrUtil.isNotBlank(lasttime)){
                riskWarningRules.setLastWarningTime((String) redisUtil.get(CacheConstant.SYS_RISK_RULE+"lastTime:" + riskWarningRules.getId()));
//                riskWarningRules.setLastWarningTime("2024-02-20 16:36:16");
            }
        }
        riskRescueFiltering(ruleList);
    }

    private void riskUserFilteringNew() {
        List<VocRiskWarningRules> ruleList = rulesService.getRiskType(CommonConstant.sys_risk_user_name);
        for (VocRiskWarningRules riskWarningRules:ruleList) {
            if(ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                riskWarningRules.setPushConditionList(Arrays.asList(riskWarningRules.getPushCondition().split(",")));
            }
            String lasttime= (String) redisUtil.get(CacheConstant.SYS_RISK_RULE + "lastTime:" + riskWarningRules.getId());
            if (StrUtil.isNotBlank(lasttime)){
                riskWarningRules.setLastWarningTime((String) redisUtil.get(CacheConstant.SYS_RISK_RULE+"lastTime:" + riskWarningRules.getId()));
            }
        }
        riskUserFiltering_d_new(ruleList);
    }

    private void riskEventFilteringNew() {
        List<VocRiskWarningRules> ruleList = rulesService.getRiskType(CommonConstant.sys_risk_emotion_name);


        for (VocRiskWarningRules riskWarningRules:ruleList) {
            if(ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                riskWarningRules.setPushConditionList(Arrays.asList(riskWarningRules.getPushCondition().split(",")));
            }
            String lasttime= (String) redisUtil.get(CacheConstant.SYS_RISK_RULE + "lastTime:" + riskWarningRules.getId());
            if (StrUtil.isNotBlank(lasttime)){
                riskWarningRules.setLastWarningTime((String) redisUtil.get(CacheConstant.SYS_RISK_RULE+"lastTime:" + riskWarningRules.getId()));
            }
        }
        riskEventFiltering_d_new(ruleList);
    }



    private void riskQualiytFilteringNew() {
        List<VocRiskWarningRules> ruleList = rulesService.getRiskType(CommonConstant.sys_risk_quality_name);
        for (VocRiskWarningRules riskWarningRules:ruleList) {
            if(ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                riskWarningRules.setPushConditionList(Arrays.asList(riskWarningRules.getPushCondition().split(",")));
            }
            String lasttime= (String) redisUtil.get(CacheConstant.SYS_RISK_RULE + "lastTime:" + riskWarningRules.getId());
            if (StrUtil.isNotBlank(lasttime)){
                riskWarningRules.setLastWarningTime((String) redisUtil.get(CacheConstant.SYS_RISK_RULE+"lastTime:" + riskWarningRules.getId()));
            }
        }
        riskQualiytFiltering_d_new(ruleList);
    }



    private void riskUserFiltering_d_new(List<VocRiskWarningRules> rule) {
        List<DwdVocUserRisk> emotionRiskList = new ArrayList<>();
        for (VocRiskWarningRules riskWarningRules:rule) {
            RiskRuleVo vo = (RiskRuleVo) redisUtil.get(CacheConstant.SYS_RISK_RULE + riskWarningRules.getId());
            extracted(riskWarningRules, vo);
            List<DwdVocUserRisk> list = userRiskService.riskUserFilteringNew(riskWarningRules,vo);
            //更新最后一查询时间
            redisUtil.set(CacheConstant.SYS_RISK_RULE+"lastTime:"+riskWarningRules.getId(),new DateTime().toString());
            UpdateWrapper<VocRiskWarningRules> riskRuleUpdate = new UpdateWrapper<>();
            riskRuleUpdate.lambda().eq(VocRiskWarningRules::getId,riskWarningRules.getId())
                    .set(VocRiskWarningRules::getLastWarningTime,new DateTime());
            rulesService.update(riskRuleUpdate);
            if (ObjectUtils.isNotEmpty(list)&&ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                emotionRiskList.addAll(list);
            }
        }
        //添加到风险审核列表及添加到记录表
        if(ObjectUtils.isNotEmpty(emotionRiskList)){
            addToRiskReviewListNew(emotionRiskList);
        }
        log.info("=====================用户风险预警结束==========================");
    }


    @Autowired
    IVocRiskWarningRulesService vocRiskWarningRulesService;

    private void riskEventFiltering_d_new(List<VocRiskWarningRules> rule) {
        List<DwdVocEmotionRisk> emotionRiskList = new ArrayList<>();
        for (VocRiskWarningRules riskWarningRules:rule) {
            RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(riskWarningRules.getId());
            extracted(riskWarningRules, vo);
            List<DwdVocEmotionRisk> list = vocRiskService.riskEventFilteringNew(riskWarningRules,vo);

            //更新最后一查询时间
            redisUtil.set(CacheConstant.SYS_RISK_RULE+"lastTime:"+riskWarningRules.getId(),new DateTime().toString());
            UpdateWrapper<VocRiskWarningRules> riskRuleUpdate = new UpdateWrapper<>();
            riskRuleUpdate.lambda().eq(VocRiskWarningRules::getId,riskWarningRules.getId())
                    .set(VocRiskWarningRules::getLastWarningTime,new DateTime());
            rulesService.update(riskRuleUpdate);
            if (ObjectUtils.isNotEmpty(list)&&ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                emotionRiskList.addAll(list);
            }
        }
        //添加到风险审核列表及添加到记录表
        if(ObjectUtils.isNotEmpty(emotionRiskList)){
            addToRiskEventReviewListNew(emotionRiskList);
        }

        log.info("=====================业务风险预警结束==========================");

    }

    private void riskQualiytFiltering_d_new(List<VocRiskWarningRules> rule) {
        List<DwdVocQualityRiskF> emotionRiskList = new ArrayList<>();
        for (VocRiskWarningRules riskWarningRules:rule) {
            RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(riskWarningRules.getId());
            extracted(riskWarningRules, vo);
            List<DwdVocQualityRiskF> list = qualityRiskService.riskQualiytFilteringNew(riskWarningRules,vo);
            //更新最后一查询时间
            redisUtil.set(CacheConstant.SYS_RISK_RULE+"lastTime:"+riskWarningRules.getId(),new DateTime().toString());
            UpdateWrapper<VocRiskWarningRules> riskRuleUpdate = new UpdateWrapper<>();
            riskRuleUpdate.lambda().eq(VocRiskWarningRules::getId,riskWarningRules.getId())
                    .set(VocRiskWarningRules::getLastWarningTime,new DateTime());
            rulesService.update(riskRuleUpdate);
            if (ObjectUtils.isNotEmpty(list)&&ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                emotionRiskList.addAll(list);
            }
        }
        //添加到风险审核列表及添加到记录表
        if(ObjectUtils.isNotEmpty(emotionRiskList)){
            addToRiskQualityReviewListNew(emotionRiskList);
        }
        log.info("=====================质量风险预警结束==========================");
    }

    private void riskRescueFiltering(List<VocRiskWarningRules> rule) {
        List<DwdVocQualityRiskF> riskList = new ArrayList<>();
        for (VocRiskWarningRules riskWarningRules:rule) {
            RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(riskWarningRules.getId());
            extracted(riskWarningRules, vo);
            List<DwdVocQualityRiskF> list = qualityRiskService.riskRescueFiltering(riskWarningRules,vo);
            //更新最后一查询时间
            redisUtil.set(CacheConstant.SYS_RISK_RULE+"lastTime:"+riskWarningRules.getId(),new DateTime().toString());
            UpdateWrapper<VocRiskWarningRules> riskRuleUpdate = new UpdateWrapper<>();
            riskRuleUpdate.lambda().eq(VocRiskWarningRules::getId,riskWarningRules.getId())
                    .set(VocRiskWarningRules::getLastWarningTime,new DateTime());
            rulesService.update(riskRuleUpdate);
            if (ObjectUtils.isNotEmpty(list)&&ObjectUtils.isNotEmpty(riskWarningRules.getPushCondition())){
                riskList.addAll(list);
            }
        }
        //添加到风险审核列表及添加到记录表
        if(ObjectUtils.isNotEmpty(riskList)){
            addToRiskRescueReviewList(riskList);
        }
        log.info("=====================救援故障预警结束==========================");
    }

    private void addToRiskRescueReviewList(List<DwdVocQualityRiskF> list) {
        List<VocRiskWarningRecord> records=new ArrayList<>();
        List<VocRiskAllTypes> riskAllTypes=new ArrayList<>();
        Set<String> set=new HashSet<>();

        for (DwdVocQualityRiskF qualityRisk : list) {
            if (set.contains(qualityRisk.getBrandCode()+":"+qualityRisk.getTopicCode())){
                continue;
            }
            set.add(qualityRisk.getBrandCode()+":"+qualityRisk.getTopicCode());

            //==================1.警示风险记录==========start=================
            records.add(new VocRiskWarningRecord(qualityRisk, RiskTypeEnum.VocRiskRescue));

            QueryWrapper<VocRiskAllTypes> wrapper=new QueryWrapper<>();
            wrapper.lambda()
                    .eq(VocRiskAllTypes::getRisk,qualityRisk.getTopicCode())
                    .eq(VocRiskAllTypes::getBrandCode,qualityRisk.getBrandCode())
                    .eq(VocRiskAllTypes::getDelFlag,0)
                    .eq(VocRiskAllTypes::getRiskType,RiskTypeEnum.VocRiskRescue.getType());//风险类型(1:业务，2：质量，3：用户,4:救援 ,5:网点)
            VocRiskAllTypes onerisk=riskAllTypesService.getOne(wrapper);


            //==================2.判断已经存在的风险点【待审核】则更新数据==========start=================

            UpdateWrapper<VocRiskAllTypes> updateWrapper;
            if (onerisk!=null&&onerisk.getRiskState().equals(0)){//已经存在风险点(未处理完)则更新数据
                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda()
                        .eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getWarningNum,onerisk.getWarningNum()+1)
                        .set(VocRiskAllTypes::getCreateTime,new DateTime())
                        .set(VocRiskAllTypes::getRiskId,qualityRisk.getId())
                ;
                String eventRiskRiskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(RiskTypeEnum.VocRiskRescue.getName(), qualityRisk.getBrandCode(),qualityRisk.getRiskIndex());

                if(ObjectUtils.isEmpty(onerisk.getNewRiskLevel())){
                    if(!onerisk.getRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda().set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }else{
                    //新风险等级存在，判断当前新风险等级newRiskLevel与eventRiskRiskLevel是否一致，一致则不更新，不一致则把newRiskLevel变为riskLevel,eventRiskRiskLevel变为newRiskLevel
                    if(!onerisk.getNewRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda()
                                .set(VocRiskAllTypes::getRiskLevel,onerisk.getNewRiskLevel())
                                .set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }

                riskAllTypesService.update(updateWrapper);
                continue;
            }
            //==================2.判断已经存在的风险点【待审核】则更新数据==========end=================

            //==================3.判断已经存在的风险点【已经处理完】则更新数据==========start=================

            //20231012修改读取风险数据库
            String riskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(RiskTypeEnum.VocRiskRescue.getName(), qualityRisk.getBrandCode(),qualityRisk.getRiskIndex());
            if (onerisk!=null&&onerisk.getRiskState().equals(3)){
                VocRiskAllTypes newrisk=new VocRiskAllTypes(qualityRisk,RiskTypeEnum.VocRiskRescue);//新添加风险
                newrisk.setRiskLevel(riskLevel);
                newrisk.setWarningNum(onerisk.getWarningNum()+1);

                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda().eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getDelFlag,1);
                riskAllTypesService.update(updateWrapper);//软删除已经处理过的风险

                riskAllTypesService.save(newrisk);
                //==================3.判断已经存在的风险点【已经处理完】则更新数据==========end=================
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========start=================

            if (onerisk==null){//新的风险点
                VocRiskAllTypes  userrisk=new VocRiskAllTypes(qualityRisk,RiskTypeEnum.VocRiskRescue);
                userrisk.setRiskLevel(riskLevel);
                riskAllTypes.add(userrisk);
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========end=================

        }
        if (records.size()>0){
            warningRecordService.saveBatch(records);
        }if (riskAllTypes.size()>0){
            riskAllTypesService.saveBatch(riskAllTypes);
        }
    }

    private static void extracted(VocRiskWarningRules riskWarningRules, RiskRuleVo vo) {
        if(ObjectUtils.isNotEmpty(vo)){
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumD()) && !vo.getRiskWordsNumD().equals(BigDecimal.ZERO)){
                riskWarningRules.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumW()) && !vo.getRiskWordsNumW().equals(BigDecimal.ZERO)){
                riskWarningRules.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumM()) && !vo.getRiskWordsNumM().equals(BigDecimal.ZERO)){
                riskWarningRules.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumQ()) && !vo.getRiskWordsNumQ().equals(BigDecimal.ZERO)){
                riskWarningRules.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumY()) && !vo.getRiskWordsNumY().equals(BigDecimal.ZERO)){
                riskWarningRules.setRiskRuleType("1");
            }
        }
    }

    @Resource
    IVocRiskAllTypesService riskAllTypesService;
    @Resource
    IVocRiskWarningRecordService warningRecordService;

    private void addToRiskReviewListNew(List<DwdVocUserRisk> list) {
        List<VocRiskWarningRecord> records=new ArrayList<>();
        List<VocRiskAllTypes> riskAllTypes=new ArrayList<>();
        Set<String> set=new HashSet<>();

        for (DwdVocUserRisk userRisk : list) {
            if (set.contains(userRisk.getBrandCode()+":"+userRisk.getUserId())){
                continue;
            }
            set.add(userRisk.getBrandCode()+":"+userRisk.getUserId());

            //==================1.警示风险记录==========start=================
            records.add(new VocRiskWarningRecord(userRisk));




            QueryWrapper<VocRiskAllTypes> wrapper=new QueryWrapper<>();
            wrapper.lambda()
                    .eq(VocRiskAllTypes::getRisk,userRisk.getUserId())
                    .eq(VocRiskAllTypes::getBrandCode,userRisk.getBrandCode())
                    .eq(VocRiskAllTypes::getDelFlag,0)
                    .eq(VocRiskAllTypes::getRiskType,3)//风险类型(1:业务，2：质量，3：用户)
            ;//不等于3 状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理)
            VocRiskAllTypes onerisk=riskAllTypesService.getOne(wrapper);


            //==================2.判断已经存在的风险点【待审核】则更新数据==========start=================

            UpdateWrapper<VocRiskAllTypes> updateWrapper=new UpdateWrapper<>();
            if (onerisk!=null&&onerisk.getRiskState().equals(0)){//已经存在风险点(未处理完)则更新数据
                updateWrapper.lambda()
                        .eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getStatistic,userRisk.getComplainNum())
                        .set(VocRiskAllTypes::getCreateTime,new DateTime())
                        .set(VocRiskAllTypes::getRiskId,userRisk.getId())
                        .set(VocRiskAllTypes::getWarningNum,onerisk.getWarningNum()+1);
                String riskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(CommonConstant.sys_risk_user_name,userRisk.getBrandCode(),userRisk.getRiskIndex());

                if(ObjectUtils.isEmpty(onerisk.getNewRiskLevel())){
                    if(!onerisk.getRiskLevel().equals(riskLevel)){
                        updateWrapper.lambda().set(VocRiskAllTypes::getNewRiskLevel,riskLevel);
                    }
                }else{
                    //新风险等级存在，判断当前新风险等级newRiskLevel与eventRiskRiskLevel是否一致，一致则不更新，不一致则把newRiskLevel变为riskLevel,eventRiskRiskLevel变为newRiskLevel
                    if(!onerisk.getNewRiskLevel().equals(riskLevel)){
                        updateWrapper.lambda()
                                .set(VocRiskAllTypes::getRiskLevel,onerisk.getNewRiskLevel())
                                .set(VocRiskAllTypes::getNewRiskLevel,riskLevel);
                    }
                }



                riskAllTypesService.update(updateWrapper);
                continue;
            }
            //==================2.判断已经存在的风险点【待审核】则更新数据==========end=================

            //==================3.判断已经存在的风险点【已经处理完】则更新数据==========start=================

            //20231012修改读取风险数据库
            String riskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(CommonConstant.sys_risk_user_name,userRisk.getBrandCode(),userRisk.getRiskIndex());
            if (onerisk!=null&&onerisk.getRiskState().equals(3)){

                VocRiskAllTypes newrisk=new VocRiskAllTypes(userRisk);//新添加风险 软删除已经处理过的风险
                newrisk.setRiskLevel(riskLevel);
                newrisk.setStatistic(userRisk.getNegativeNum());
                newrisk.setWarningNum(onerisk.getWarningNum()+1);

                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda().eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getDelFlag,1);
                riskAllTypesService.update(updateWrapper);//软删除已经处理过的风险



                riskAllTypesService.save(newrisk);
                //==================3.判断已经存在的风险点【已经处理完】则更新数据==========end=================
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========start=================
            if (onerisk==null){//新的风险点
                VocRiskAllTypes  userrisk=new VocRiskAllTypes(userRisk);
                userrisk.setRiskLevel(riskLevel);
                riskAllTypes.add(userrisk);
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========end=================

        }
        if (records.size()>0){
            warningRecordService.saveBatch(records);
        }if (riskAllTypes.size()>0){
            riskAllTypesService.saveBatch(riskAllTypes);
        }
        //==================1.警示风险记录==========end=================

    }


    private void addToRiskEventReviewListNew(List<DwdVocEmotionRisk> list) {
        List<VocRiskWarningRecord> records=new ArrayList<>();
        List<VocRiskAllTypes> riskAllTypes=new ArrayList<>();
        Set<String> set=new HashSet<>();
        for (DwdVocEmotionRisk eventRisk : list) {
            if (set.contains(eventRisk.getBrandCode()+":"+eventRisk.getTopicCode())){
                continue;
            }
            set.add(eventRisk.getBrandCode()+":"+eventRisk.getTopicCode());
            //==================1.警示风险记录==========start=================
            records.add(new VocRiskWarningRecord(eventRisk));



            QueryWrapper<VocRiskAllTypes> wrapper=new QueryWrapper<>();
            wrapper.lambda()
                    .eq(VocRiskAllTypes::getRisk,eventRisk.getTopicCode())
                    .eq(VocRiskAllTypes::getBrandCode,eventRisk.getBrandCode())
                    .eq(VocRiskAllTypes::getDelFlag,0)
                    .eq(VocRiskAllTypes::getRiskType,1)//风险类型(1:业务，2：质量，3：用户)
                    ;
            VocRiskAllTypes onerisk=riskAllTypesService.getOne(wrapper);
            UpdateWrapper<VocRiskAllTypes> updateWrapper=new UpdateWrapper<>();


            //==================2.查询已经存在的风险点【待审核】则更新数据==========start=================

            if (onerisk!=null&&onerisk.getRiskState().equals(0)){//已经存在风险点(待审核)则更新数据 //等于0 状态(0:待审核,1:已审核(风险审核),待处理(风险处理),3:已处理,4:部分处理)

                updateWrapper.lambda()
                        .eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getCreateTime,new DateTime())
                        .set(VocRiskAllTypes::getStatistic,eventRisk.getNegativeNum())
                        .set(VocRiskAllTypes::getRiskId,eventRisk.getId())
                        .set(VocRiskAllTypes::getWarningNum,onerisk.getWarningNum()+1);
                //判断是否更新风险等级risk_index
                //更改后的风险等级不存在，判断新risk_index与旧风险等级是否相同，相同则不更新，不相同则更新new_risk_level
                //20231012修改读取风险数据库
//                String eventRiskRiskLevel = CalculatorUtils.getRiskLevel(eventRisk.getRiskIndex());
                String eventRiskRiskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(CommonConstant.sys_risk_emotion_name,eventRisk.getBrandCode(),eventRisk.getRiskIndex());

                if(ObjectUtils.isEmpty(onerisk.getNewRiskLevel())){
                    if(!onerisk.getRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda().set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }else{
                    //新风险等级存在，判断当前新风险等级newRiskLevel与eventRiskRiskLevel是否一致，一致则不更新，不一致则把newRiskLevel变为riskLevel,eventRiskRiskLevel变为newRiskLevel
                    if(!onerisk.getNewRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda()
                                .set(VocRiskAllTypes::getRiskLevel,onerisk.getNewRiskLevel())
                                .set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }

//                        .set(VocRiskAllTypes::getRiskLevel,onerisk.getRiskLevel().equals(riskLevel) ? onerisk.getRiskLevel() : )
                riskAllTypesService.update(updateWrapper);
                continue;
            }
            //==================2.判断已经存在的风险点【待审核】则更新数据==========end=================


            //==================3.判断已经存在的风险点【已经处理完】则更新数据==========start=================

            //20231012修改读取风险数据库
            String riskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(CommonConstant.sys_risk_emotion_name,eventRisk.getBrandCode(),eventRisk.getRiskIndex());
            if (onerisk!=null&&onerisk.getRiskState().equals(3)){
                VocRiskAllTypes newrisk=new VocRiskAllTypes(eventRisk);//新添加风险
                newrisk.setRiskLevel(riskLevel);
                newrisk.setWarningNum(onerisk.getWarningNum()+1);

                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda().eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getDelFlag,1);
                riskAllTypesService.update(updateWrapper);//软删除已经处理过的风险

                riskAllTypesService.save(newrisk);
                //==================3.判断已经存在的风险点【已经处理完】则更新数据==========end=================
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========start=================

            if (onerisk==null){//新的风险点
                VocRiskAllTypes  userrisk=new VocRiskAllTypes(eventRisk);
                userrisk.setRiskLevel(riskLevel);
                riskAllTypes.add(userrisk);
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========end=================

        }
        if (records.size()>0){
            warningRecordService.saveBatch(records);
        }if (riskAllTypes.size()>0){
            riskAllTypesService.saveBatch(riskAllTypes);
        }
    }


    private void addToRiskQualityReviewListNew(List<DwdVocQualityRiskF> list) {
        List<VocRiskWarningRecord> records=new ArrayList<>();
        List<VocRiskAllTypes> riskAllTypes=new ArrayList<>();
        Set<String> set=new HashSet<>();

        for (DwdVocQualityRiskF qualityRisk : list) {
            if (set.contains(qualityRisk.getBrandCode()+":"+qualityRisk.getTopicCode())){
                continue;
            }
            set.add(qualityRisk.getBrandCode()+":"+qualityRisk.getTopicCode());

            //==================1.警示风险记录==========start=================
            records.add(new VocRiskWarningRecord(qualityRisk,RiskTypeEnum.VocRiskQuality));

            QueryWrapper<VocRiskAllTypes> wrapper=new QueryWrapper<>();
            wrapper.lambda()
                    .eq(VocRiskAllTypes::getRisk,qualityRisk.getTopicCode())
                    .eq(VocRiskAllTypes::getBrandCode,qualityRisk.getBrandCode())
                    .eq(VocRiskAllTypes::getDelFlag,0)
                    .eq(VocRiskAllTypes::getRiskType,2);//风险类型(1:业务，2：质量，3：用户)
            VocRiskAllTypes onerisk=riskAllTypesService.getOne(wrapper);


            //==================2.判断已经存在的风险点【待审核】则更新数据==========start=================

            UpdateWrapper<VocRiskAllTypes> updateWrapper=new UpdateWrapper<>();
            if (onerisk!=null&&onerisk.getRiskState().equals(0)){//已经存在风险点(未处理完)则更新数据
                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda()
                        .eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getWarningNum,onerisk.getWarningNum()+1)
                        .set(VocRiskAllTypes::getCreateTime,new DateTime())
                        .set(VocRiskAllTypes::getRiskId,qualityRisk.getId())
                ;
                String eventRiskRiskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(RiskTypeEnum.VocRiskQuality.getName(), qualityRisk.getBrandCode(),qualityRisk.getRiskIndex());

                if(ObjectUtils.isEmpty(onerisk.getNewRiskLevel())){
                    if(!onerisk.getRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda().set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }else{
                    //新风险等级存在，判断当前新风险等级newRiskLevel与eventRiskRiskLevel是否一致，一致则不更新，不一致则把newRiskLevel变为riskLevel,eventRiskRiskLevel变为newRiskLevel
                    if(!onerisk.getNewRiskLevel().equals(eventRiskRiskLevel)){
                        updateWrapper.lambda()
                                .set(VocRiskAllTypes::getRiskLevel,onerisk.getNewRiskLevel())
                                .set(VocRiskAllTypes::getNewRiskLevel,eventRiskRiskLevel);
                    }
                }

                riskAllTypesService.update(updateWrapper);
                continue;
            }
            //==================2.判断已经存在的风险点【待审核】则更新数据==========end=================

            //==================3.判断已经存在的风险点【已经处理完】则更新数据==========start=================

            //20231012修改读取风险数据库
            String riskLevel = rulesService.getWarnRuleDetailListByIdBrandCode(RiskTypeEnum.VocRiskQuality.getName(),qualityRisk.getBrandCode(),qualityRisk.getRiskIndex());
            if (onerisk!=null&&onerisk.getRiskState().equals(3)){
                VocRiskAllTypes newrisk=new VocRiskAllTypes(qualityRisk,RiskTypeEnum.VocRiskQuality);//新添加风险
                newrisk.setRiskLevel(riskLevel);
                newrisk.setWarningNum(onerisk.getWarningNum()+1);

                updateWrapper=new UpdateWrapper<>();
                updateWrapper.lambda().eq(VocRiskAllTypes::getId,onerisk.getId())
                        .set(VocRiskAllTypes::getDelFlag,1);
                riskAllTypesService.update(updateWrapper);//软删除已经处理过的风险

                riskAllTypesService.save(newrisk);
                //==================3.判断已经存在的风险点【已经处理完】则更新数据==========end=================
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========start=================

            if (onerisk==null){//新的风险点
                VocRiskAllTypes  userrisk=new VocRiskAllTypes(qualityRisk,RiskTypeEnum.VocRiskQuality);
                userrisk.setRiskLevel(riskLevel);
                riskAllTypes.add(userrisk);
            }
            //==================4.判断是否已经存在处理中的风险，没有则添加风险==========end=================

        }
        if (records.size()>0){
            warningRecordService.saveBatch(records);
        }if (riskAllTypes.size()>0){
            riskAllTypesService.saveBatch(riskAllTypes);
        }
    }


}
