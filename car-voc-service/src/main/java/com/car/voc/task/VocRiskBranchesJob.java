package com.car.voc.task;

import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @version 1.0.0
 * @ClassName .java
 * @Description 网点风险预警
 * @createTime 2023年02月07日 13:51
 * @Copyright voc
 */
//@Configuration
//@EnableScheduling
@Component
@Log4j2
public class VocRiskBranchesJob {

    @Resource
    VocRiskJobService eventJobService;
    private static Logger logger = LoggerFactory.getLogger(VocRiskBranchesJob.class);


    //    每天凌晨1点30执行一次：0 30 1 * * ?
//    @Scheduled(cron = "0 30 1 * * ?")
    @XxlJob("VocRiskBranchesJob")
    public void VocRiskBranchesJob() {
        log.info("=====================开始执行网点风险的预警=======================");
        logger.info("=====================开始执行网点风险的预警=======================");
        eventJobService.vocRiskBranchesJob();
        logger.debug("=====================网点风险的预警结束==========================");
        log.info("=====================网点风险的预警结束==========================");
    }

}
