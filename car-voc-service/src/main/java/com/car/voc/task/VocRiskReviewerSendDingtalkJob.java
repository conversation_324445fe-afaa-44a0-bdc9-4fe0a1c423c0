package com.car.voc.task;

import com.car.voc.service.IVocRiskAlertReviewerService;
import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * @version 1.0.0
 * @Description 审核人员发送钉钉
 * @createTime 2023年02月07日 13:51
 * @Copyright voc
 */
@Slf4j
@Component
public class VocRiskReviewerSendDingtalkJob {
    @Resource
    IVocRiskAlertReviewerService riskAlertReviewerService;

    private static Logger logger = LoggerFactory.getLogger(VocRiskReviewerSendDingtalkJob.class);

    //    每天凌晨1点执行一次：0 0 1 * * ?
    //    @Scheduled(cron = "0 0 1 * * ?")
    @XxlJob("vocRiskReviewerSendDingtalkJob")
    public void vocRiskReviewerSendDingtalkJob(){
        log.info("=====================开始执行审核人员发送钉钉=======================");
        logger.info("=====================开始执行审核人员发送钉钉=======================");
        riskAlertReviewerService.vocRiskReviewerSendDingtalkJob();
        log.info("=====================执行审核人员发送钉钉结束=======================");
        logger.info("=====================执行审核人员发送钉钉结束=======================");
    }

}
