package com.car.voc.task;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.VocRiskAllTypes;
import com.car.voc.service.IVocRiskAllTypesService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * @version 1.0.0
 * @ClassName
 * @Description 软删除所有风险
 * @Copyright voc
 */
@Slf4j
@Component
public class VocClearAllRiskJob {
    @Autowired
    IVocRiskAllTypesService riskAllTypesService;
    private static Logger logger = LoggerFactory.getLogger(VocClearAllRiskJob.class);


    @XxlJob("vocClearAllRiskJob")
    public void vocClearAllRiskJob(){

            log.info("=====================开始执行软删除所有风险=======================");
            logger.info("=====================开始执行软删除所有风险=======================");
            UpdateWrapper<VocRiskAllTypes> update=new  UpdateWrapper<>();
            update.lambda().eq(VocRiskAllTypes::getDelFlag,0)
                            .set(VocRiskAllTypes::getDelFlag,1);
            riskAllTypesService.update(update);
            logger.info("=====================执行软删除所有风险结束=======================");
            log.info("=====================执行软删除所有风险结束=======================");

    }

}
