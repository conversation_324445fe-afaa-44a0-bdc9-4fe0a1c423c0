package com.car.voc.task;

import com.car.voc.service.IVocRiskAlertReviewerService;
import com.car.voc.service.IVocRiskHandlingRecordService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/30
 * @description： 问题处理超时提醒
 * @modified By：
 * @version: $version$
 */

@Slf4j
@Component
public class VocRiskProcessTimeoutRemindJob {

    @Resource
    IVocRiskHandlingRecordService handlingRecordService;

    private static Logger logger = LoggerFactory.getLogger(com.car.voc.task.VocRiskProcessTimeoutRemindJob.class);
    @XxlJob("vocRiskProcessTimeoutRemindJob")
    public void vocRiskProcessTimeoutRemindJob() {
        log.info("=====================开始执行【问题处理超时提醒】发送钉钉=======================");
        logger.info("=====================开始执行【问题处理超时提醒】发送钉钉=======================");
        handlingRecordService.vocRiskProcessTimeoutRemindJob();
        log.info("=====================执行【问题处理超时提醒】发送钉钉结束=======================");
        logger.info("=====================执行【问题处理超时提醒】发送钉钉结束=======================");
    }
}