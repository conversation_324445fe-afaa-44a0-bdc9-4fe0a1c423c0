package com.car.voc.task;

import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @version 1.0.0
 * @ClassName .java
 * @Description 质量风险警示
 * @createTime 2023年02月07日 13:51
 * @Copyright voc
 */
//@Configuration
//@EnableScheduling
@Component
@Log4j2
public class VocRiskQualityJob {

    @Resource
    VocRiskJobService eventJobService;
    private static Logger logger = LoggerFactory.getLogger(VocRiskQualityJob.class);


    //    每天凌晨1点30执行一次：0 30 1 * * ?
//    @Scheduled(cron = "0 30 1 * * ?")
    @XxlJob("vocRiskQualityJob")
    public void vocRiskQualityJob() {
        log.info("=====================开始执行质量风险的预警=======================");
        logger.info("=====================开始执行质量风险的预警=======================");
        eventJobService.vocRiskQualityJob();
        logger.debug("=====================质量风险的预警结束==========================");
        log.info("=====================质量风险的预警结束==========================");
    }

}
