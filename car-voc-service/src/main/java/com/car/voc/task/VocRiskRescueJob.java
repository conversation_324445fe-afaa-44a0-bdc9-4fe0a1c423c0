package com.car.voc.task;

import com.car.voc.task.service.VocRiskJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @version 1.0.0
 * @ClassName .java
 * @Description 救援故障预警
 * @createTime 2023年02月07日 13:51
 * @Copyright voc
 */
//@Configuration
//@EnableScheduling
@Component
@Log4j2
public class VocRiskRescueJob {

    @Resource
    VocRiskJobService eventJobService;
    private static Logger logger = LoggerFactory.getLogger(VocRiskRescueJob.class);


    //    每天凌晨1点30执行一次：0 30 1 * * ?
//    @Scheduled(cron = "0 30 1 * * ?")
//    @XxlJob("vocRiskRescueJob")
    public void vocRiskRescueJob() {
        log.info("=====================开始执行救援故障的预警=======================");
        logger.info("=====================开始执行救援故障的预警=======================");
        eventJobService.vocRiskRescueJob();
        logger.debug("=====================救援故障的预警结束==========================");
        log.info("=====================救援故障的预警结束==========================");
    }

}
