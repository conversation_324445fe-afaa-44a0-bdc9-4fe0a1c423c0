package com.car.voc.task;

import com.car.voc.service.ISysRoleService;
import com.car.voc.service.IVocRiskAlertReviewerService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * 角色用户同步任务
 */
@Slf4j
@Component
public class RoleUserSyncJob {
    @Resource
    ISysRoleService sysRoleService;
    private static Logger logger = LoggerFactory.getLogger(RoleUserSyncJob.class);

    //    每天凌晨1点执行一次：0 0 1 * * ?
//    @Scheduled(cron = "0 0 1 * * ?")
    @XxlJob("roleUserSyncJob")
    public void roleUserSyncJob(){
        log.info("=====================开始执行角色用户同步任务=======================");
        logger.info("=====================开始执行角色用户同步任务=======================");
        sysRoleService.roleUserSyncJob();
        log.info("=====================执行角色用户同步任务结束=======================");
        logger.info("=====================执行角色用户同步任务结束=======================");
    }

}
