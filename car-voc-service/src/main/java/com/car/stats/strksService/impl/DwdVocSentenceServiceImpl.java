package com.car.stats.strksService.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwdVocSentence;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.strksMapper.DwdVocSentenceMapper;
import com.car.stats.strksService.IDwdVocSentenceService;
import com.car.stats.vo.ChannelVo;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@DS("starrocks1")
public class DwdVocSentenceServiceImpl extends ServiceImpl<DwdVocSentenceMapper, DwdVocSentence> implements IDwdVocSentenceService {
   @Autowired
   DwdVocSentenceMapper mapper;
    @Override
    public Result<?> channelTrend(FilterCriteriaModel model) {
        List<ChannelVo> objects = new ArrayList<>();
        objects=setChannelAllList(model);
        return Result.ok(objects);
    }

    @Override
    public Result<?> homeIntentionTrend(FilterCriteriaModel model) {
        List<HomePurposeTrendVo> objectLists = mapper.intentionNumAndUserNumList(model);

        return null;
    }

    public List<ChannelVo> setChannelAllList(FilterCriteriaModel model) {

        List<ChannelVo> objects = new ArrayList<>();
        if (model.isCPoint()){//查询私域二级
            model.setChannelId(CommonConstant.privateChannelId);
            model.setChannelIds(null);
            model.setDataSources(Arrays.asList(CommonConstant.privateChannelId));
            objects.addAll(mapper.channel2Distribution(model));
        }else if (model.getChannelId()!=null && !"".equals(model.getChannelId())&&model.getChannelId().equals(CommonConstant.publicSphereId)&&model.getChannelIds().size()==0){
            //公域二级
//            objects.addAll(dwsVocEmotionDiMapper.channel2Distribution(model));
        }else if (model.getChannelIds().size()>0&& StrUtil.isBlankIfStr(model.getChannelId())){
            //公域三级
            model.setDataSources(Arrays.asList(CommonConstant.publicSphereId));
//            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));
        }else if (model.getChannelId()!=null && !"".equals(model.getChannelId())){
            model.setDataSources(null);
//            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));

        }else {
            model.setDataSources(null);model.setChannelId(null);
//            objects.addAll(dwsVocEmotionDiMapper.dataSourceDistribution(model));
        }

        return objects;
    }
}
