package com.car.stats.model;

import com.car.voc.common.enums.SoundTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @version 1.0.0
 * @ClassName FilterCriteriaModel.java
 * @Description TODO
 * @createTime 2022年10月10日 18:08
 * @Copyright voc
 */
@Data
@ApiModel(value = "筛选条件入参")
@ToString(callSuper = true)
public class FilterCriteriaModel extends ComFilterCriteriaModel {


    @ApiModelProperty(value = "一级标签", example = "B1001")
    String firstDimensionCode;
    @ApiModelProperty(value = "二级标签", example = "B1003002")
    String secondDimensionCode;
    @ApiModelProperty(value = "三级标签", example = "B1003002011")
    String threeDimensionCode;
    @ApiModelProperty(value = "三级标签", example = "B1003002011")
    String thirdDimensionCode;
    @ApiModelProperty(value = "四级标签")
    String topicCode;


    @ApiModelProperty(value = "一级标签list", example = "[]")
    List<String> firstDimensionCodes = new ArrayList<>();
    @ApiModelProperty(value = "二级标签list", example = "[]")
    List<String> secondDimensionCodes = new ArrayList<>();
    @ApiModelProperty(value = "三级标签list", hidden = true)
    Set<String> threeDimensionCodes;
    @ApiModelProperty(value = "四级标签list", hidden = true)
    Set<String> topicCodes;

    @ApiModelProperty(hidden = true)
    Set<String> carGroups;
    @ApiModelProperty(hidden = true)
    Set<String> hotWords;

    @ApiModelProperty(hidden = true)
    String userLevel;
    @ApiModelProperty(value = "意图", example = "投诉")
    String intention;
    @ApiModelProperty(value = "热词")
    String emotionKeyword;
    @ApiModelProperty(value = "情感", example = "正面")
    String emotion;
    @ApiModelProperty(value = "情感类型", example = "正面")
    List<String> emotions;
    @ApiModelProperty(value = "声音类型", example = "emotion")
    SoundTypeEnum soundType;

    @ApiModelProperty(value = "严重性")
    String problemLevel;


    /* =====================     工单特殊筛选条件   ================================= */
    String woFlow;
    @ApiModelProperty(value = "状态")
    String status;
    @ApiModelProperty(value = "网点名称")
    String dlrName;
    @ApiModelProperty(value = "网点code")
    String dlrCode;
    @ApiModelProperty(value = "满意类型")
    String satisfactionStatus;
    @ApiModelProperty(value = "救援状态")
    String rescueStatus;
    @ApiModelProperty(value = "工单来源")
    String woDataSource;
    @ApiModelProperty(value = "工单来源")
    List<String> woDataSources;
    @ApiModelProperty(value = "舆情分类（全部/客诉类/媒体类）")
    String publicOpinionType;
    @ApiModelProperty(value = "舆情等级（全部/一级/二级）")
    String publicOpinionGrade;
    @ApiModelProperty(value = "工单特殊时间筛选")
    String woTime;

    @ApiModelProperty(value = "私域数据", hidden = true)
    Set<String> dataSource;

    public FilterCriteriaModel() {
        this.pageNo = 1;
        this.pageSize = 10;
    }

    public FilterCriteriaModel(String firstDimensionCode, String region, Set<String> carSeries, List<String> userTypes, String startDate, String endDate, String startDateUp, String endDateUp, Set<String> dataSource) {
        this.firstDimensionCode = firstDimensionCode;
        this.region = region;
        this.carSeries = carSeries;
        this.userTypes = userTypes;
        this.startDate = startDate;
        this.endDate = endDate;
        this.startDateUp = startDateUp;
        this.endDateUp = endDateUp;
        this.dataSource = dataSource;
    }

    public FilterCriteriaModel(String thisStartTime, String thisEndTime, String upStartTime, String upEndTime) {
        this.startDate = thisStartTime;
        this.endDate = thisEndTime;
        this.startDateUp = upStartTime;
        this.endDateUp = upEndTime;
    }


    @Override
    public String getAccessToken() {
        return super.getAccessToken();
    }

}
