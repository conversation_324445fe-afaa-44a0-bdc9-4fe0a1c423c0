package com.car.stats.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel(value = "筛选条件入参")
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class LargeScreenDataModel implements Serializable {


    @ApiModelProperty(value = "开始日期", example = "2023-06-01 00:00:00")
    String startDate;
    @ApiModelProperty(value = "结束日期", example = "2024-10-01 00:00:00")
    String endDate;
    @ApiModelProperty(value = "1提及 2飙升")
    String type;
    @ApiModelProperty(value = "品牌Code", example = "A11")
    @NotBlank(message = "品牌Code不能为空")
    String brandCode;

}
