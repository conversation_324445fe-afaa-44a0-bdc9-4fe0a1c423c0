package com.car.stats.model;

import cn.hutool.core.util.StrUtil;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * @version 1.0.0
 * @ClassName LabelDetailFilterModel.java
 * @Description TODO
 * @createTime 2022年10月30日 20:30
 * @Copyright voc
 */
@Data
public class LabelDetailFilterModel extends FilterCriteriaModel{

    @ApiModelProperty(value = "数据数据状态",example = "有结果",notes = "有结果,去噪,无结果")
    String tag;
    @ApiModelProperty(value = "原声id")
    String sourceId;
    String carType;

    @ApiModelProperty(value = "一级标签")
    String firstDimensionCode;
    @ApiModelProperty(value = "二级标签")
    String secondDimensionCode;
    @ApiModelProperty(value = "三级标签")
    String thirdDimensionCode;
    @ApiModelProperty(value = "四级标签")
    String topicCode;
    @ApiModelProperty(value = "共性问题专用")
    String topic;

    @ApiModelProperty(value = "一级标签list",example = "[]")
    List<String> firstDimensionCodes;
    @ApiModelProperty(value = "二级标签list",example = "[]")
    List<String> secondDimensionCodes;
    @ApiModelProperty(value = "三级标签list",hidden = true)
    Set<String> threeDimensionCodes;
    @ApiModelProperty(value = "四级标签list",hidden = true)
    Set<String> topicCodes;

    @ApiModelProperty(value = "感情")
    String emotion;
    List<String> emotions;
    @ApiModelProperty(value = "意图",example = "投诉")
    String intention;
    @ApiModelProperty(value = "热词")
    String emotionKeyword;

    @ApiModelProperty(hidden = true)
    Set<String> carGroups;
    @ApiModelProperty(hidden = true)
    Set<String> hotWords;

    @ApiModelProperty(value = "严重性")
    String problemLevel;
    @ApiModelProperty(value = "媒体工单专用(媒体一级)",example = "公众网站")
    String mediaName1;
    @ApiModelProperty(value = "媒体工单专用(媒体二级)",example = "车质网")
    String mediaName2;

    //==================nps使用的入参key=====begin=======
    /**
     * 拥车期：默认全部，分为半年内、0.5~1 年、1~2 年、2~3 年、3~4 年、4~5 年
     */
    @ApiModelProperty(value = "拥车期")
    private String carSpecificUYears;
    /**
     * 渠道：即对应调研数据的来源，如电话、线上；
     */
    @ApiModelProperty(value = "渠道")
    private List<String> channel;
    /**
     * 阶段：默认提车，维保;
     */
    @ApiModelProperty(value = "阶段")
    private String stage;
    /**
     * 能源类型：默认全部：燃油车、新能源；
     */
    @ApiModelProperty(value = "能源类型")
    private String energyType;
    /**
     * 分类：默认全部：分为门店、品牌；
     */
    @ApiModelProperty(value = "分类")
    private String classify;
    /**
     * 默认全部，分为男、女。
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    //==================nps使用的入参key======end=============

    @Override
    public String getAccessToken(){
        return super.getAccessToken();
    }
    public String getCode() {
        if (StrUtil.isNotBlank(topicCode)){
            return topicCode;
        }else if (StrUtil.isNotBlank(thirdDimensionCode)){
            return thirdDimensionCode;
        }else if (StrUtil.isNotBlank(secondDimensionCode)){
            return secondDimensionCode;
        }else if (StrUtil.isNotBlank(firstDimensionCode)){
            return firstDimensionCode;
        }else {
            return null;
        }
    }


    public List<String> getEmotions() {
        if(StrUtil.isNotBlank(this.emotion) && this.emotion.contains(",")){
            this.emotions = Arrays.asList(emotion.split(","));
        }
        return emotions;
    }

    @Override
    public List<String> getDataSources() {
        return dataSources;
    }

    public List<String> getSecondDimensionCodes(){
        if (tagType!=null&&tagType==2){
            return secondDimensionCodes;
        }if (StrUtil.isNotBlank(menuName)&& "feedbackAnalysis".equals(menuName)){
            return secondDimensionCodes;
        }if (this.isRiskPire()){
            return secondDimensionCodes;
        }
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        String path = null;
        if (ObjectUtils.isEmpty(request)) {
            path = SpringContextUtils.getRequestURI();
        } else {
            path = request.getRequestURI();
        }
        if (StrUtil.isBlank(path)){
            path = SpringContextUtils.getRequestURI();
        }
        if (path.contains("lastTagHotWords")||path.contains("productQuality")||path.contains("qualityTagDetail")||path.contains("qualityTagUserList")){
            return this.secondDimensionCodes;
        }
        if(CollectionUtils.isEmpty(this.secondDimensionCodes)){
            RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
            Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG,this.getBrandCode(),getUserId()));

            this.secondDimensionCodes = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
        }
        return secondDimensionCodes;
    }


}
