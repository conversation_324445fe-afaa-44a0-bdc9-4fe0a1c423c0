package com.car.stats.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * @version 1.0.0
 * @ClassName RiskWarningModel.java
 * @Description TODO
 * @createTime 2022年11月23日 13:46
 * @Copyright voc
 */
@Data
@ApiModel(value="高频用户投诉")
public class ComplaintUserTopModel extends ComFilterCriteriaModel{
    String id;

    @ApiModelProperty(value = "风险警示等级",example = "1")
    @Dict(dicCode = "risk_warning_level")
    String riskWarningLevel;

    @TableField(exist = false)
    @ApiModelProperty(value="sql使用")
    private String riskRuleType;

    @ApiModelProperty(value="sql使用")
    private String riskLevel;

    @Override
    public String getAccessToken(){
        return super.getAccessToken();
    }
}
