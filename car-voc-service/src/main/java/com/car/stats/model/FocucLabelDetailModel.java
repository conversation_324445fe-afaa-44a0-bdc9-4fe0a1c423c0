package com.car.stats.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName FocucLabelDetailModel.java
 * @Description TODO
 * @createTime 2022年10月16日 11:40
 * @Copyright voc
 */
@Data
public class FocucLabelDetailModel extends FilterCriteriaModel{
    @ApiModelProperty(value = "二级标签",required = true)
    @NotBlank(message = "不能为空！")
    String secondDimensionCode;
    @ApiModelProperty(value = "二级标签list",example = "[]")
    List<String> secondDimensionCodes;

    List<String> channelIds;

    @Override
    public String getThirdDimensionCode() {
        return null;
    }

}
