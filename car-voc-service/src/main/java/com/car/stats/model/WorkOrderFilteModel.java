package com.car.stats.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName WorkOrderFilteModel.java
 * @Description TODO
 * @createTime 2022年11月07日 14:56
 * @Copyright voc
 */
@Data
public class WorkOrderFilteModel extends FilterCriteriaModel{
    @ApiModelProperty(value = "选择媒体分类【全部时不传】",example = "公司内部")
    String workOrderType1;
    @ApiModelProperty(value = "默认选中第一个媒体传名称",example = "12345市民热线")
    String mediaName;
    @ApiModelProperty(hidden = true)
    Set<String> mediaNames;
    @ApiModelProperty(hidden = true)
    Set<String> labelCodes;
}
