package com.car.stats.model;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.car.voc.cach.BaseDataCache;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.dto.BrandProductManagerDto;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.entity.VocChannelCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共筛选条件带权限入参《带标签权限、其他标签、渠道权限、区域权限、省市权限、车系权限》
 *
 * @version 1.0.0
 * @ClassName FilterCriteriaModel.java
 * @Description 公共筛选条件带权限入参《带标签权限、其他标签、渠道权限、区域权限、省市权限、车系权限》
 * @createTime 2022年10月10日 18:08
 * @Copyright voc
 */
@Data
@ApiModel(value = "公共筛选条件入参")
@AllArgsConstructor
@Slf4j
public class ComFilterCriteriaModel extends DateModel {
    static TimedCache<String, Set<Object>> brandCodeCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Set<Object>> tagSetCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Set<Object>> channelSetCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Set<Object>> areaSetCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Set<Object>> carSeriesSetCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Set<Object>> carProvincesSetCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Set<VocBrandRegion>> provincesRegionCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, Map<String, String>> maps = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    protected String accessToken;
    @ApiModelProperty(value = "风险Id")
    String riskId;
    @ApiModelProperty(value = "菜单名")
    String menuName;
    @ApiModelProperty(value = "数据类型", example = "numMention")
    DataEnum dataType;
    @ApiModelProperty(value = "工单类型：1.救援2.后处理工单3.商机线索", hidden = true)
    String woType;

    @ApiModelProperty(value = "工单类型：1.救援2.后处理工单3.商机线索")
    List<String> woTypeList;

    @ApiModelProperty(value = "标签类型")
    Integer tagType;

    @ApiModelProperty(value = "单品牌")
    String brand;
    @ApiModelProperty(value = "单品牌")
    Set<String> brandNames;
    @ApiModelProperty(value = "多品牌")
    String brandCode;
    @ApiModelProperty(value = "品牌多个code")
    List<String> brands;
    @ApiModelProperty(value = "品牌权限", hidden = true)
    List<String> brandCodePowers;

    String sentence;


    @ApiModelProperty(value = "共用ids查询")
    List<String> ids;
    List<String> userIds;
    @ApiModelProperty(value = "用户类型", example = "vip")
    List<String> userTypes;
    String isOneId;

    Integer numCycles = 7;

    @ApiModelProperty(value = "一级渠道id多个")
    List<String> dataSources;
    List<String> dataSourceList;
    List<String> channelList;
    @ApiModelProperty(value = "一级渠道一个")
    String channelId;
    @ApiModelProperty(value = "二级渠道id多个")
    List<String> channelIds;
    @ApiModelProperty(value = "渠道权限", hidden = true)
    Set<Object> channelIdPowers;


    @ApiModelProperty(value = "区域类型")
    String regionType;
    @ApiModelProperty(value = "大区")
    List<String> dealerRegion1s;
    @ApiModelProperty(value = "小区")
    List<String> dealerRegion2s;

    @ApiModelProperty(value = "区域")
    List<String> areas;
    @ApiModelProperty(value = "区域权限", hidden = true)
    List<String> areasPowers;
    @ApiModelProperty(value = "地区")
    String region;
    @ApiModelProperty(value = "省份")
    List<String> province;
    @ApiModelProperty(value = "省份名称")
    Set<String> provinceNames;
    @ApiModelProperty(value = "NPS兼容字段——省份")
    List<String> provinceCodes;
    @ApiModelProperty(value = "省份权限", hidden = true)
    List<String> provinceCodesPowers;


    @ApiModelProperty(value = "多个车系(当选择车型组时，传车型组下面的所有车系code,当选择车型分类时，传车型分类下面的所有车系code)", example = "[\"A12A14\",\"A12A14\",\"A12A14\",\"A12A14\"]")
    Set<String> carSeries;
    @ApiModelProperty(value = "车系权限", hidden = true)
    Set<String> carSeriesPowers;
    @ApiModelProperty(hidden = true)
    Set<String> carSeriesDisables;
    @ApiModelProperty(value = "多个车系通过carSeries字段获取中文（过滤没有权限车系、禁用车系）")
    Set<String> carSeriesNames;
    @ApiModelProperty(value = "单个车系名称，返回车系carSeries的code")
    String carSeriesName;


    @ApiModelProperty(hidden = true, value = "禁用业务tag")
    Set<String> tagTopicCodesDisables;
    @ApiModelProperty(hidden = true, value = "禁用质量tag")
    Set<String> qualityTopicCodesDisables;
    @ApiModelProperty(value = "兜底标签")
    List<String> otherTag;

    @ApiModelProperty(value = "标签权限", hidden = true)
    List<String> secondDimensionCodesPowers;
    List<String> unionTag2;

    public List<String> getChannelIds() {
        if (this.isRiskPire()) {
            if (!StrUtil.isBlankIfStr(channelId) && channelId.equals(CommonConstant.publicSphereId) && channelIds.size() == 0) {
                this.dataSources = Arrays.asList(CommonConstant.publicSphereId);
            }
            if (ObjectUtils.isNotEmpty(this.channelIds)) {
                this.channelIds = this.channelIds.stream().sorted().collect(Collectors.toList());
            }
            return this.channelIds;
        }
        if (!StrUtil.isBlankIfStr(channelId) && channelId.equals(CommonConstant.publicSphereId) && channelIds.size() == 0) {
            this.dataSources = Arrays.asList(CommonConstant.publicSphereId);
            return Collections.EMPTY_LIST;
        }

        if (!StrUtil.isBlankIfStr(channelId) && channelId.equals(CommonConstant.publicSphereId) && this.channelIds.size() > 0) {
            RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
            List<VocChannelCategory> chall = (List<VocChannelCategory>) redisUtil.get(CacheConstant.SYS_CHANNEL_ALL);
            List<String> strings = new ArrayList<>();
            for (String id : channelIds) {
                List<String> s = chall.stream().filter(e -> e.getPid().equals(id)).collect(Collectors.toList()).stream().map(VocChannelCategory::getId).collect(Collectors.toList());
                strings.addAll(s);
            }
            this.channelId = null;
            this.channelIds = strings;
            if (ObjectUtils.isNotEmpty(this.channelIds)) {
                this.channelIds = this.channelIds.stream().sorted().collect(Collectors.toList());
            }
            return this.channelIds;
        }
        if (ObjectUtils.isNotEmpty(this.channelIds)) {
            this.channelIds = this.channelIds.stream().sorted().collect(Collectors.toList());
        }
        return channelIds;
    }


    public List<String> getProvince() {
        if (CollectionUtil.isNotEmpty(this.areas)) {
            Set<VocBrandRegion> vocreginos = getVocBrandRegions();
            Set<String> proCodes = vocreginos.stream()
                    .filter(vocBrandRegion -> this.areas.contains(vocBrandRegion.getCommunityCode()))
                    .map(VocBrandRegion::getProvinceCode)
                    .collect(Collectors.toSet());
            return new ArrayList<>(proCodes);
        }
        return provinceCodes;
    }

    public Set<String> getProvinceNames() {
        if (StrUtil.isBlank(this.getBrandCode()) || this.isRiskPire()) {
            return this.provinceNames;
        }
        List<Object> currentUserProvincesSet = this.getCurrentUserProvincesSet();
        if (CollectionUtil.isEmpty(currentUserProvincesSet)) {
            return this.provinceNames;
        }
        List<String> currentProvince = this.getProvince();
        if (CollectionUtil.isNotEmpty(currentProvince)) {
            currentUserProvincesSet.retainAll(currentProvince);
        }
        Set<VocBrandRegion> vocAllRegions = this.getVocBrandRegions();
        Set<String> filterData = vocAllRegions.stream()
                .filter(e -> currentUserProvincesSet.contains(e.getProvinceCode()))
                .map(VocBrandRegion::getProvinceName).collect(Collectors.toSet());
        Set<String> allProvinceName = vocAllRegions.stream()
                .map(VocBrandRegion::getProvinceName)
                .collect(Collectors.toSet());
        if (filterData.size() == allProvinceName.size()) {
            return this.provinceNames;
        }
        this.provinceNames = filterData;
        return this.provinceNames;
    }

    public Set<Object> getChannelIdPowers() {
        if (this.isRiskPire()) {
            return null;
        }
        final Set<Object> channelSet = this.getCurrentUserChannelSet();

        if (channelSet == null) {
            return Collections.EMPTY_SET;
        } else {
            Set<Object> collect = channelSet.stream().sorted().collect(Collectors.toSet());
            return collect;
        }
    }

    public String getAccessToken() {
        if (ObjectUtil.isNotNull(this.accessToken)) {
            return this.accessToken;
        }
        return SpringContextUtils.getToken();
    }

    private Set<Object> getCurrentUserChannelSet() {
        Set<Object> channelSet = new HashSet<>();
        final String accessToken = getAccessToken();
        if (ObjectUtil.isNotNull(accessToken) && ObjectUtil.isNotNull(this.getBrandCode())) {
            final String key = accessToken.concat("_").concat(this.getBrandCode());
            if (channelSetCache.containsKey(key)) {
                channelSet = channelSetCache.get(key);
            } else {

                RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
                final Set<Object> newChannelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_CHANNEL, this.getBrandCode(), getUserId()));
                if (CollUtil.isNotEmpty(newChannelSet)) {
                    channelSetCache.put(key, newChannelSet);
                    channelSet = newChannelSet;
                }
            }
        }
        if (ObjectUtils.isNotEmpty(channelSet)) {
            channelSet = channelSet.stream().sorted().collect(Collectors.toSet());
        }
        return channelSet;
    }

    private Set<Object> getCurrentUserBrandCodeSet() {
        Set<Object> res = new HashSet<>();
        final String accessToken = getAccessToken();
        if (ObjectUtil.isNotNull(accessToken)) {
            final String key = accessToken.concat("_");
            if (brandCodeCache.containsKey(key)) {
                res = brandCodeCache.get(key);
            } else {
                RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
                final Set<Object> re = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE, getUserId()));
                if (CollUtil.isNotEmpty(re)) {
                    brandCodeCache.put(key, re);
                    res = re;
                }
            }
        }
        if (ObjectUtils.isNotEmpty(res)) {
            res = res.stream().sorted().collect(Collectors.toSet());
        }
        return res;
    }

    private Set<Object> getCurrentUserBuztagSet() {
        Set<Object> channelSet = new HashSet<>();
        final String accessToken = getAccessToken();
        if (ObjectUtil.isNotNull(accessToken) && ObjectUtil.isNotNull(this.getBrandCode())) {
            final String key = accessToken.concat("_").concat(this.getBrandCode());
            if (tagSetCache.containsKey(key)) {
                channelSet = tagSetCache.get(key);
            } else {

                RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
                final Set<Object> newChannelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, this.getBrandCode(), getUserId()));
                if (CollUtil.isNotEmpty(newChannelSet)) {
                    tagSetCache.put(key, newChannelSet);
                    channelSet = newChannelSet;
                }
            }
        }
        if (ObjectUtils.isNotEmpty(channelSet)) {
            channelSet = channelSet.stream().sorted().collect(Collectors.toSet());
        }
        return channelSet;
    }


    private List<Object> getCurrentUserProvincesSet() {
        Set<Object> provinces = new HashSet<>();
        final String accessToken = this.getAccessToken();
        if (ObjectUtil.isNotNull(accessToken) && ObjectUtil.isNotNull(this.getBrandCode())) {
            final String key = accessToken.concat("_").concat(this.getBrandCode());
            if (carProvincesSetCache.containsKey(key)) {
                provinces = carProvincesSetCache.get(key);
            } else {
                RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
                provinces = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_PROVINCE, this.getBrandCode(), getUserId()));
                if (CollUtil.isNotEmpty(provinces)) {
                    carProvincesSetCache.put(key, provinces);
                    provinces = provinces.stream().sorted().collect(Collectors.toSet());
                    return new ArrayList<>(provinces);
                }
            }
        } else {
            log.error("accessToken is null ");
        }
        if (ObjectUtils.isNotEmpty(provinces)) {
            provinces = provinces.stream().sorted().collect(Collectors.toSet());
        }
        return new ArrayList<>(provinces);
    }

    private Set<Object> getCurrentUserCarSeriesSet() {
        Set<Object> channelSet = new HashSet<>();
        final String accessToken = getAccessToken();
        if (ObjectUtil.isNotNull(accessToken) && ObjectUtil.isNotNull(this.getBrandCode())) {
            final String key = accessToken.concat("_").concat(this.getBrandCode());
            if (carSeriesSetCache.containsKey(key)) {
                channelSet = carSeriesSetCache.get(key);
            } else {

                RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
                final Set<Object> newChannelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES, this.getBrandCode(), getUserId()));
                if (CollUtil.isNotEmpty(newChannelSet)) {
                    carSeriesSetCache.put(key, newChannelSet);
                    channelSet = newChannelSet;
                }
            }
        }
        if (ObjectUtils.isNotEmpty(channelSet)) {
            channelSet = channelSet.stream().sorted().collect(Collectors.toSet());
        }
        return channelSet;
    }

    public Set<String> getBrandNames() {
        Set<String> brandSeries = new HashSet<>();
        BaseDataCache redisUtil = (BaseDataCache) SpringContextUtils.getBean("baseDataCache");
        Set<BrandProductManagerDto> seriesAllDto = redisUtil.brandSeriesAll(this.getBrandCode());
        if (StrUtil.isBlank(this.getBrandCode())) {
            Set<Object> currentUserBrandCodeSet = this.getCurrentUserBrandCodeSet();
            seriesAllDto.stream().filter(e -> currentUserBrandCodeSet.contains(e.getCode())).forEach(item -> {
                String[] split = item.getAlias().split("\\\\");
                Set<String> collect = Arrays.stream(split).collect(Collectors.toSet());
                collect.add(item.getName());
                brandSeries.addAll(collect);
            });
            this.brandNames = brandSeries;
            return this.brandNames;
        }
        seriesAllDto.stream().filter(e -> this.getBrandCode().equals(e.getCode())).forEach(item -> {
            String[] split = item.getAlias().split("\\\\");
            Set<String> collect = Arrays.stream(split).collect(Collectors.toSet());
            collect.add(item.getName());
            brandSeries.addAll(collect);
        });
        this.brandNames = brandSeries;
        return this.brandNames;
    }

    public Set<String> getCarSeriesNames() {
        if (StrUtil.isNotBlank(this.carSeriesName)) {
            if (CollUtil.isEmpty(this.carSeriesNames)) {
                this.carSeriesNames = new HashSet<>();
            }
            this.carSeriesNames.add(this.carSeriesName);
            return this.carSeriesNames;
        }
        if (StrUtil.isBlank(this.getBrandCode()) || this.isRiskPire()) {
            return this.carSeriesNames;
        }
        Set<String> carSeriesPowersCode = this.getCarSeriesPowers();
        if (CollUtil.isEmpty(carSeriesPowersCode)) {
            return this.carSeriesNames;
        }
        BaseDataCache redisUtil = (BaseDataCache) SpringContextUtils.getBean("baseDataCache");
        Set<BrandProductManagerDto> seriesAllDto = redisUtil.brandSeriesAll(this.getBrandCode());
        List<BrandProductManagerDto> carSeriesFilter = seriesAllDto.stream()
                .filter(e -> carSeriesPowersCode.contains(e.getCode()))
                .collect(Collectors.toList());
        if (carSeriesFilter.size() == (seriesAllDto.size() - 1)) {
            return this.carSeriesNames;
        }
        this.carSeriesNames = new HashSet<>();
        carSeriesFilter.forEach(item -> {
            String[] split = item.getAlias().split("\\\\");
            List<String> collect = Arrays.stream(split).collect(Collectors.toList());
            collect.add(item.getName());
            carSeriesNames.addAll(collect);
        });
        return this.carSeriesNames;
    }

    public Set<String> getCarSeries() {
        if (StrUtil.isBlank(this.getBrandCode()) || this.isRiskPire()) {
            return this.carSeries;
        }
        String currentCarSeriesName = this.getCarSeriesName();
        if (CollUtil.isNotEmpty(this.carSeries) || StrUtil.isBlank(currentCarSeriesName)) {
            return this.carSeries;
        }
        BaseDataCache redisUtil = (BaseDataCache) SpringContextUtils.getBean("baseDataCache");
        Set<BrandProductManagerDto> seriesAllDto = redisUtil.brandSeriesAll(this.getBrandCode());
        List<BrandProductManagerDto> carSeriesCodeBySeriesName = seriesAllDto.stream().filter(e -> e.getAlias().contains(currentCarSeriesName) ||
                e.getName().equals(currentCarSeriesName)).collect(Collectors.toList());
        this.carSeries = carSeriesCodeBySeriesName.stream().map(BrandProductManagerDto::getCode).collect(Collectors.toSet());
        return this.carSeries;
    }

    boolean isCPoint;

    public boolean isCPoint() {

        if (CollectionUtils.isNotEmpty(this.dataSources) && this.dataSources.contains("Customer-data")) {
            this.isCPoint = true;
            return true;
        } else {
            return isCPoint;
        }
    }

    public DataEnum getDataType() {
        if (this.dataType == null) {
            return DataEnum.numMention;
        } else {
            return dataType;

        }
    }

    public ComFilterCriteriaModel() {
        this.pageNo = 1;
        this.pageSize = 10;
    }


    public synchronized Set<String> getCarSeriesPowers() {
        if (this.isRiskPire()) {
            if (ObjectUtils.isNotEmpty(this.carSeriesPowers)) {
                this.carSeriesPowers = this.carSeriesPowers.stream().sorted().collect(Collectors.toSet());
            }
            return this.carSeriesPowers;
        }
        try {
            RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
            Set<Object> seriesSet = this.getCurrentUserCarSeriesSet();
            Set<Object> disableCar = (Set<Object>) redisUtil.get(CacheConstant.sys_disable_car_code);
            if (seriesSet != null) {
                if (disableCar != null) {
                    seriesSet.removeAll(disableCar);
                }
                if (CollectionUtils.isNotEmpty(this.getCarSeries())) {
                    seriesSet.retainAll(this.getCarSeries());
                }
                this.carSeriesPowers = seriesSet.stream().map(String::valueOf).collect(Collectors.toSet());
            }
        } catch (Exception e) {
            if (ObjectUtils.isNotEmpty(this.carSeriesPowers)) {
                this.carSeriesPowers = this.carSeriesPowers.stream().sorted().collect(Collectors.toSet());
            }
            return carSeriesPowers;
        }
        if (ObjectUtils.isNotEmpty(this.carSeriesPowers)) {
            this.carSeriesPowers = this.carSeriesPowers.stream().sorted().collect(Collectors.toSet());
        }
        return this.carSeriesPowers;
    }


    public List<Object> getProvinceCodesPowers() {
        if (this.isRiskPire()) {
            Set<String> proCodes = new HashSet<String>();
            if (ObjectUtils.isNotEmpty(this.areas)) {
                Set<VocBrandRegion> vocreginos = this.getVocBrandRegions();
                proCodes = vocreginos.stream()
                        .filter(vocBrandRegion -> areas.contains(vocBrandRegion.getCommunityCode()))
                        .map(VocBrandRegion::getProvinceCode)
                        .collect(Collectors.toSet());
            }
            return new ArrayList<>(proCodes);
        }
        List<Object> provinces = this.getCurrentUserProvincesSet();
        return provinces;
    }

    private Set<VocBrandRegion> getVocBrandRegions() {
        Set<VocBrandRegion> regions = new HashSet<>();
        String key = String.format(CacheConstant.SYS_AREA_BRANDCODE_PROVINCE_ALL, this.getBrandCode());
        if (provincesRegionCache.containsKey(key)) {
            regions = provincesRegionCache.get(key);
        } else {
            RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
            Set<Object> newRegions = redisUtil.sGet(key);
            Set<VocBrandRegion> collect = newRegions.stream().map(e -> (VocBrandRegion) e).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(collect)) {
                provincesRegionCache.put(key, collect);
                regions = collect;
            }
        }
        return regions;
    }


    //判断是否为风险的下钻
    boolean riskPire = false;

    public boolean isRiskPire() {
        boolean riskIs = false;
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        String requestURI = null;
        if (ObjectUtils.isEmpty(request)) {
            requestURI = SpringContextUtils.getRequestURI();
        } else {
            requestURI = request.getRequestURI();
        }
        if (StrUtil.isBlank(requestURI)){
            requestURI = SpringContextUtils.getRequestURI();
        }
        if (StrUtil.isNotBlank(this.getRiskId()) && requestURI.contains("stats/labelDetails")) {
            riskIs = true;
        } else if (StrUtil.isNotBlank(this.getRiskId()) || (this.getMenuName() != null && ("riskEvent,qualityRisk,followUp").contains(this.getMenuName()))) {
            riskIs = true;
            //所有风险只查私域数据
            if (ObjectUtil.isAllEmpty(this.getDataSources()) && ObjectUtil.isAllEmpty(this.getChannelId())) {
                List<String> das = new ArrayList<>();
                das.add(CommonConstant.privateChannelId);
                this.setDataSources(das);
            }

        } else if ((this.getMenuName() != null && ("allDataQuery").contains(this.getMenuName()))) {
            riskIs = true;
        } else if (requestURI.contains("risk")) {
            riskIs = true;
        }
        return riskIs;
    }


    public List<String> getSecondDimensionCodesPowers() {
        try {
            if (this.isRiskPire()) {
                return null;
            }

            if (SpringContextUtils.getHttpServletRequest() != null) {
                HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
                String path = null;
                if (ObjectUtils.isEmpty(request)) {
                    path = SpringContextUtils.getRequestURI();
                } else {
                    path = request.getRequestURI();
                }
                if (StrUtil.isBlank(path)){
                    path = SpringContextUtils.getRequestURI();
                }
                if (path.contains("overviewProblemDistribution") || path.contains("overviewProblemTrends") || path.contains("overviewQualityProblemTypical") || path.contains("productQuality") || path.contains("quality")) {//总览质量问题
                    return null;
                }

                if (this.getMenuName() != null && ("qualityRisk,productQuality").contains(this.getMenuName())) {
                    return null;
                }

                if (this.getMenuName() != null && ("dashBoard").contains(this.getMenuName()) && path.contains("lastTagHotWords")) {
                    return null;
                }
            }
        } catch (Exception e) {
            if (ObjectUtils.isNotEmpty(this.secondDimensionCodesPowers)) {
                this.secondDimensionCodesPowers = this.secondDimensionCodesPowers.stream().sorted().collect(Collectors.toList());
            }
            return secondDimensionCodesPowers;
        }

        final Set<Object> channelSet = this.getCurrentUserBuztagSet();
        if (CollUtil.isNotEmpty(channelSet)) {
            this.secondDimensionCodesPowers = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
        } else {
            List<String> scodes = new ArrayList<>();
            scodes.add("getCurrentUserBuztagSet is null");
            this.setSecondDimensionCodesPowers(scodes);
        }
        if (ObjectUtils.isNotEmpty(this.secondDimensionCodesPowers)) {
            this.secondDimensionCodesPowers = this.secondDimensionCodesPowers.stream().sorted().collect(Collectors.toList());
        }
        return secondDimensionCodesPowers;
    }

    public String getRegionType() {
        if (StrUtil.isNotBlank(this.regionType) && "Sales_territory".equals(this.regionType)) {
            return "1";
        } else if (StrUtil.isNotBlank(this.regionType) && "Aftermarket_area".equals(this.regionType)) {
            return "2";
        } else return null;
    }
}
