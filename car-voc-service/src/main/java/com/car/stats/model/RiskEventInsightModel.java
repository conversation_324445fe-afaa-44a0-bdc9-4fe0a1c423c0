package com.car.stats.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.car.voc.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName RiskWarningModel.java
 * @Description TODO
 * @createTime 2022年11月23日 13:46
 * @Copyright voc
 */
@Data
@ApiModel(value="风险事件洞察")
@NoArgsConstructor
@AllArgsConstructor
public class RiskEventInsightModel extends FilterCriteriaModel{
    @ApiModelProperty(value = "风险点Id",example = "B1001002003001404697794",required = true)
    String riskId;

    @ApiModelProperty(hidden = true)
    String riskUserId;

    @ApiModelProperty(value = "一级标签",example = "B1001")
    String firstDimensionCode;
    @ApiModelProperty(value = "问题分类(二级)")
    String secondDimensionCode;
    @ApiModelProperty(value = "问题部件(三级)")
    String thirdDimensionCode;
    @ApiModelProperty(value = "风险点Code(四级)",example = "B1004003005003")
    String topicCode;

    @ApiModelProperty(value = "一级标签list",example = "[]")
    List<String> firstDimensionCodes;
    @ApiModelProperty(value = "二级标签list",example = "[]")
    List<String> secondDimensionCodes;
    @ApiModelProperty(value = "三级标签list",hidden = true)
    Set<String> threeDimensionCodes;
    @ApiModelProperty(value = "四级标签list",hidden = true)
    Set<String> topicCodes;

    @ApiModelProperty(value = "风险警示等级",example = "1")
    @Dict(dicCode = "risk_warning_level")
    String riskWarningLevel;
    @ApiModelProperty(value = "风险点",example = "销售服务#交付新车整整洁度#")
    String riskPointStr;

    @ApiModelProperty(hidden = true)
    String emotion;
    @ApiModelProperty(hidden = true)
    String intention;

    @ApiModelProperty(value = "严重性")
    String problemLevel;

    @ApiModelProperty(value = "排序类型 index:负面浓度  num:负面提及量")
    String orderByType;

    private Integer riskType;

    @TableField(exist = false)
    @ApiModelProperty(value="sql使用")
    private String riskRuleType;
    @ApiModelProperty(value="sql使用")
    private String tag;
    @ApiModelProperty(value="sql使用")
    private String riskLevel;
    /*public List<String> getSecondDimensionCodes(){
        String path=   SpringContextUtils.getHttpServletRequest().getRequestURI();
        if (path.contains("risk")){//
            return this.secondDimensionCodes;
        }
        if(CollectionUtils.isEmpty(this.secondDimensionCodes)){
            RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
            Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG,this.getBrandCode(),getUserId()));

            this.secondDimensionCodes = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
        }
        return secondDimensionCodes;
    }*/

    @Override
    public String getAccessToken(){
        return super.getAccessToken();
    }

}
