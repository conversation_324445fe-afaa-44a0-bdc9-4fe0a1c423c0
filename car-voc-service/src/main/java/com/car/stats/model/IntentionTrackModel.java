package com.car.stats.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionTrackModel.java
 * @Description TODO
 * @createTime 2022年11月02日 18:55
 * @Copyright voc
 */
@Data
public class IntentionTrackModel extends ComFilterCriteriaModel{
    @ApiModelProperty(value = "userId",example = "6600000001337564")
    String userId;
    @ApiModelProperty(value = "渠道Id(为空全部)",example = "1356178730703224801")
    String channelId;
    String intention;
    String emotion;

    public IntentionTrackModel() {
        this.pageNo=1;
        this.pageSize=10;
    }
}
