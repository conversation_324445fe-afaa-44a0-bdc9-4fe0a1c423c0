package com.car.stats.model;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName FilterCriteriaModel.java
 * @Description TODO
 * @createTime 2022年10月10日 18:08
 * @Copyright voc
 */
@Data
@ApiModel(value="产品质量筛选条件入参")
public class ProductQualityFilterCriteriaModel extends FilterCriteriaModel{

    @ApiModelProperty(value = "一级标签",hidden = true)
    String firstDimensionCode;
    @ApiModelProperty(value = "二级标签")
    String secondDimensionCode;
    @ApiModelProperty(value = "三级标签")
    String thirdDimensionCode;


    @ApiModelProperty(value = "二级标签list",hidden = true)
    List<String> secondDimensionCodes;
    @ApiModelProperty(value = "三级标签list",hidden = true)
    Set<String> threeDimensionCodes;
    @ApiModelProperty(value = "四级标签list",hidden = true)
    Set<String> topicCodes;
//    List<String> secondDimensionCodesPowers;

    @ApiModelProperty(value = "私域数据",hidden = true)
    Set<String> dataSource;

    @ApiModelProperty(hidden = true)
    Set<String> carGroups;
    @ApiModelProperty(hidden = true)
    Set<String> hotWords;



    @ApiModelProperty(hidden = true)
    String userLevel;
    @ApiModelProperty(value = "意图",example = "投诉",hidden = true)
    String intention;
    @ApiModelProperty(value = "情感",example = "正面")
    String emotion;
    @ApiModelProperty(value = "情感类型",example = "正面")
    List<String> emotions;

    @ApiModelProperty(value = "严重性")
    String problemLevel;

    @ApiModelProperty(value = "问题分布类型（提及量:mention，维修量:maintenance）")
    String problemDistributionType;

    public ProductQualityFilterCriteriaModel() {
    }
    public List<String> getEmotions() {
        if(StrUtil.isNotBlank(this.emotion) && this.emotion.contains(",")){
            this.emotions = Arrays.asList(emotion.split(","));
        }
        return emotions;
    }


}
