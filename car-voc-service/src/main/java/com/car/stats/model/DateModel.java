package com.car.stats.model;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.car.voc.common.aspect.annotation.SensitiveField;
import com.car.voc.common.enums.DateStyle;
import com.car.voc.common.enums.SensitiveFieldTypeEnum;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.DateUtils;
import com.car.voc.common.util.IDateUtils;
import com.car.voc.common.util.SvwDate;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.shiro.SecurityUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0.0
 * @ClassName DateModel.java
 * @Description TODO
 * @createTime 2022年11月23日 13:47
 * @Copyright voc
 */
@Data
@ApiModel(value = "时间公共筛选条件入参")
public class DateModel implements Serializable {

    @ApiModelProperty(value = "是否下载")
    boolean excel;

    Integer downloadNum;
    //渠道
    Integer downloadChannelNum = 100;
    //热词
    Integer downloadHotWordsNum = 500;
    //标签
    Integer downloadTagNum = 500;
    //用户
    Integer downLoadUserTop = 1000;

    String statisticType;

    String userId;

    //    @JsonIgnore
    @ApiModelProperty(value = "排序字段")
    @SensitiveField(type = SensitiveFieldTypeEnum.FIELD)
    String column;
    @ApiModelProperty(value = "排序类型asc,desc")
    String order;
    @ApiModelProperty(value = "pageNo")
    Integer pageNo;
    @ApiModelProperty(value = "pageSize")
    Integer pageSize;
    @ApiModelProperty(hidden = true)
    Integer rownum;

    @ApiModelProperty(value = "开始日期", example = "2023-06-01 00:00:00")
    String startDate;
    @ApiModelProperty(value = "结束日期", example = "2024-10-01 00:00:00")
    String endDate;
    String createDate;
    String endDates;
    @ApiModelProperty(hidden = true)
    DateTime newDate;
    @ApiModelProperty(hidden = true)
    String dateS;
    @ApiModelProperty(hidden = true)
    String dateE;
    @ApiModelProperty(value = "日期单位(0为周，1为月，2为季，3为年度，-1为自定义)", example = "1")
    Integer dateUnit;
    @ApiModelProperty(value = "上一个开始日期", hidden = true)
    String startDateUp;
    @ApiModelProperty(value = "上一个结束日期", hidden = true)
    String endDateUp;
    @ApiModelProperty(hidden = true)
    List<SvwDate> dateList;

    String month;
    String week;
    String season;
    String year;
    Integer dayNum;
    Integer timeUnit;


    /**
     * @Description 获取参数里当前时间 周，月，季的数值
     * @createTime 2021年07月06日
     * @Copyright voc
     */

    public Integer getTimeUnit() {
        //日期单位(0为周，1为月，2为季，3为年，-1为自定义)
        if (this.dateUnit != null) {
            if (this.dateUnit.equals(0)) {
                return DateUtils.getWeekOfYear(DateUtil.parseDateTime(this.getStartDate()));
            } else if (this.dateUnit.equals(1)) {
                return DateUtils.getMonth(this.getStartDate());
            } else if (this.dateUnit.equals(2)) {
                return ((DateUtils.getMonth(this.getStartDate())) / 3) + 1;
            }
        }

        return timeUnit;
    }

    public String getWeek() {
        if (StrUtil.isNotBlank(this.getStartDate())) {
            Integer a = DateUtil.weekOfYear(DateUtil.parseDateTime(this.getStartDate()));
            return a + "";
        }
        return week;
    }

    public String getMonth() {
        if (StrUtil.isNotBlank(this.getStartDate())) {
            Integer a = (DateUtil.parseDateTime(this.getStartDate())).month() + 1;
            String month = a < 10 ? ("0" + a) : (a + "");
            return month + "";
        }
        return month;
    }

    public String getSeason() {
        if (StrUtil.isNotBlank(this.getStartDate())) {
            Integer a = DateUtil.quarter(DateUtil.parseDateTime(this.getStartDate()));
            return a + "";
        }
        return season;
    }

    public String getYear() {
        if (StrUtil.isNotBlank(this.getStartDate())) {
            Integer a = DateUtil.year(DateUtil.parseDateTime(this.getStartDate()));
            return a + "";
        }
        return year;
    }

    public void setUpYearMonthWeek() {
        if (this.getDateUnit() == 0) {
            if ("1".equals(week)) {
                this.week = String.valueOf(DateUtils.getMaxWeekNumOfYear(Integer.parseInt(this.year) - 1));
                this.year = String.valueOf(Integer.parseInt(this.year) - 1);
            } else {
                this.week = String.valueOf(Integer.parseInt(this.week) - 1);
            }
        } else if (this.getDateUnit() == 1) {
            if ("1".equals(this.month) || "01".equals(this.month)) {
                month = "12";
                this.year = String.valueOf(Integer.parseInt(this.year) - 1);
            } else {
                this.month = String.valueOf(Integer.parseInt(this.month) - 1);
            }
        } else if (this.getDateUnit() == 2) {
            if ("1".equals(this.season)) {
                month = "4";
                this.year = String.valueOf(Integer.parseInt(this.year) - 1);
            } else {
                this.season = String.valueOf(Integer.parseInt(this.season) - 1);
            }
        } else if (this.getDateUnit() == 3) {
            this.year = String.valueOf(Integer.parseInt(this.year) - 1);
        } else {
            int a = DateUtils.getIntervalDays(this.getStartDate(), this.getEndDate());
            this.startDateUp = DateUtils.addDay(this.getStartDate(), -a);
            this.endDateUp = this.getStartDate();
        }
    }

    public Integer getDownloadNum() {
        if (this.dateUnit != null) {
            return CalculatorUtils.downloadNumByDateUnit(this.dateUnit);
        }
        return 20;
    }

    /**
     * @Description 设置当前周期的 上一周期
     * @createTime 2021年07月06日
     * @Copyright voc
     */
    public void SetUpCycle() {
        String lastFromDate = "";
        String lastEndDate = "";
        if (StrUtil.isBlank(this.getStartDate())) {
            return;
        }

        this.year = String.valueOf(DateUtils.getYear(this.getStartDate()));
        //日期单位(0为周，1为月，2为季，3为年，-1为自定义)
        if (this.getDateUnit().equals(1)) {
            this.month = String.valueOf(DateUtils.getMonth(this.getStartDate()) + 1);
            lastFromDate = DateUtils.addMonth(this.getStartDate(), -1);
            lastEndDate = DateUtil.endOfMonth(DateUtils.StringToDate(lastFromDate, DateStyle.YYYY_MM_DD_HH_MM_SS)).toString();
        } else if (this.dateUnit.equals(2)) {
            this.season = DateUtils.getSeason(this.getStartDate());
            lastFromDate = DateUtils.addMonth(this.getStartDate(), -3);
            lastEndDate = DateUtil.endOfQuarter(DateUtils.StringToDate(lastFromDate, DateStyle.YYYY_MM_DD_HH_MM_SS)).toString();
        } else if (this.dateUnit.equals(0)) {
            this.week = String.valueOf(DateUtils.getWeekOfYear(DateUtil.parseDate(this.getStartDate())));
            lastFromDate = DateUtils.addDay(this.getStartDate(), -7);
            lastEndDate = DateUtil.endOfWeek(DateUtils.StringToDate(lastFromDate, DateStyle.YYYY_MM_DD_HH_MM_SS)).toString();
        } else if (this.dateUnit.equals(3)) {
            lastFromDate = DateUtils.addYear(this.getStartDate(), -1);
            lastEndDate = DateUtils.addDay(this.getStartDate(), -1);
        } else if (this.dateUnit.equals(-1)) {
           /* int a= DateUtils.getIntervalDays(this.getStartDate(),this.getEndDate());
            this.startDateUp=DateUtils.addDay(this.getStartDate(),-a);
            this.endDateUp=this.getStartDate();
            */

            Long day = DateUtils.getDays(this.getEndDate(), this.getStartDate()) + 1;
            this.startDateUp = DateUtils.addDay(this.getStartDate(), (int) -day);
            this.endDateUp = DateUtils.addDay(this.getStartDate(), -1).substring(0, 10) + " 23:59:59";
            return;
        }
        this.startDateUp = lastFromDate;
        this.endDateUp = lastEndDate;
    }


    public DateTime getNewDate() {
        return DateUtil.offsetDay(new Date(), -1);
    }


    public List<SvwDate> getDateTimes() {

        String end, startStr;
        long n;
        if (StrUtil.isBlank(this.getEndDate()) || StrUtil.isBlank(this.getStartDate())) {
            return new ArrayList<>();
        }
        Date today = DateUtils.strToDate(this.getEndDate());
        Date start = DateUtils.strToDate(this.getStartDate());
        if (this.getDateUnit() == -1 && this.getDateUnit() != null) {
            end = DateUtils.dateToStr(today);
            startStr = DateUtils.dateToStr(start);
            n = DateUtils.getTwoDayLong(startStr, end);
            System.out.println("间隔：" + n + "天");
            n = n + 1;


        }
        List<SvwDate> trends = DateUtils.GetTime(start, today, this.getDateUnit());
        return trends;
    }

    public List<SvwDate> getDateTimesByCustom(long n) {

        String end, startStr;
        if (this.getEndDate() == null && this.getStartDate() == null) {
            return new ArrayList<>();
        }
        Date today = DateUtils.strToDate(this.getEndDate());
        Date start = DateUtils.strToDate(this.getStartDate());
        end = DateUtils.dateToStr(today);
        startStr = DateUtils.dateToStr(start);
        List<SvwDate> trends = null;
        if (DateUtils.getTwoDayLong(startStr, end) < 1) {
            LocalDate localDate = today.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate startDate = localDate.minusDays(n - 1);
            ZonedDateTime zonedDateTime = startDate.atStartOfDay(ZoneId.systemDefault());
            start = Date.from(zonedDateTime.toInstant());
            trends = DateUtils.GetTime(start, today, this.getDateUnit());
        } else {
            trends = DateUtils.GetTime(start, today, this.getDateUnit());
        }
        return trends;
    }


    public List<String> getMapKey(List<SvwDate> list) {
        List<String> keys = new ArrayList<>();
        list.stream().forEach(s -> {
            String key;
            if (this.getDateUnit() == 0) {
                key = s.getYear() + "-" + s.getWeek();
            } else if (this.getDateUnit() == 1) {
                key = s.getYear() + "-" + (s.getMonth().length() == 1 ? "0" + s.getMonth() : s.getMonth());
            } else if (this.getDateUnit() == 2) {
                key = s.getYear() + "-" + s.getSeason();
            } else if (this.getDateUnit() == 3) {
                key = s.getYear();
            } else {
                key = IDateUtils.convertTime(s.getStartDate());
            }
            keys.add(key);
        });
        return keys;
    }

    public String getUserId() {
        if (StrUtil.isBlank(this.userId)) {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            this.userId = loginUser.getId();
        }
        return userId;
    }


    public String getStatisticType() {
        if (this.dateUnit != null) {
            return CalculatorUtils.periodNumToStr(this.dateUnit);
        } else {
            return null;
        }
    }
}
