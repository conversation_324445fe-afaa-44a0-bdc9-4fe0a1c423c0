package com.car.stats.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: LargeDigitaFilesModel
 * @Package: com.voc.service.insights.engine.api.model
 * @Description:
 * @Author: cuick
 * @Date: 2024/12/15 18:46
 * @Version:1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LargeDigitaFilesModel implements Serializable {

    String id;
    String taskId;
    String userId;
    String taskName;
    String type;
    String status;
    String fileKey;
    String fileUrl;
    String fileName;
    LocalDateTime createTime;
}
