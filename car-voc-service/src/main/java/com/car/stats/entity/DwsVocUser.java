package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * TF_DWS_VOC_USER
 *
 */
@TableName("TF_DWS_VOC_USER")
@ApiModel(value="DwsVocUserDWS用户统发声计表")
@Data
public class DwsVocUser implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value="ID")
    private String id;

    /**
     * 用户类型
     */
    @ApiModelProperty(value="用户类型")
    private BigDecimal userId;

    /**
     * 用户类型
     */
    @ApiModelProperty(value="用户类型")
    private String userType;

    /**
     * 用户等级
     */
    @ApiModelProperty(value="用户等级")
    private BigDecimal userLevel;

    /**
     * 公域渠道
     */
    @ApiModelProperty(value="公域渠道")
    private String channelId;

    /**
     * 计数
     */
    @ApiModelProperty(value="计数")
    private BigDecimal statistic;

    private String dataSource;

    private static final long serialVersionUID = 1L;
}
