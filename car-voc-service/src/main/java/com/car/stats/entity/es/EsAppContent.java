package com.car.stats.entity.es;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Document;

/**
 * 
 * @version 1.0.0
 * @ClassName EsAppContentVo.java
 * @Description TODO
 * @createTime 2022年11月02日 13:52
 * @Copyright voc
 */
@NoArgsConstructor
@Data
@Document(indexName = "svw_app_data_vocs")
public class EsAppContent {



    @JsonProperty("beanClass")
    private String beanClass;
    @JsonProperty("id")
    private Long id;
    @JsonProperty("artCreateTime")
    private String artCreateTime;
    @JsonProperty("commCounts")
    private Integer commCounts;
    @JsonProperty("createdTime")
    private String createdTime;
    @JsonProperty("deleteFlag")
    private Integer deleteFlag;
    @JsonProperty("feedContent")
    private String feedContent;
    @JsonProperty("forwardCounts")
    private Integer forwardCounts;
    @JsonProperty("groupId")
    private Long groupId;
    @JsonProperty("isSelected")
    private Integer isSelected;
    @JsonProperty("isTop")
    private Integer isTop;
    @JsonProperty("isTopicOffshelf")
    private Integer isTopicOffshelf;
    @JsonProperty("likeCounts")
    private Integer likeCounts;
    @JsonProperty("modifiedTime")
    private String modifiedTime;
    @JsonProperty("modifyTimestamp")
    private Long modifyTimestamp;
    @JsonProperty("quaScore")
    private Integer quaScore;
    @JsonProperty("totalQuaScore")
    private Integer totalQuaScore;
    @JsonProperty("userId")
    private Long userId;
    @JsonProperty("artPubStatus")
    private Integer artPubStatus;
    @JsonProperty("artReviewStatus")
    private Integer artReviewStatus;
    @JsonProperty("isApprove")
    private Integer isApprove;
    @JsonProperty("pictureUrlList")
    private String pictureUrlList;
    @JsonProperty("bizType")
    private Integer bizType;
    @JsonProperty("essenceApproveStatus")
    private Integer essenceApproveStatus;
    @JsonProperty("artContent")
    private String artContent;
    @JsonProperty("editMode")
    private Integer editMode;
    @JsonProperty("artTitle")
    private String artTitle;
    @JsonProperty("hotScore")
    private Integer hotScore;
    @JsonProperty("modifiedBy")
    private String modifiedBy;
    @JsonProperty("createdBy")
    private String createdBy;
    @JsonProperty("artWords")
    private Integer artWords;
    @JsonProperty("address")
    private Object address;
    @JsonProperty("location")
    private Object location;
    @JsonProperty("noCommPub")
    private Integer noCommPub;
    @JsonProperty("approveTime")
    private String approveTime;
    @JsonProperty("approverId")
    private Long approverId;
    @JsonProperty("viewPersons")
    private Integer viewPersons;
    @JsonProperty("artReviewDate")
    private String artReviewDate;
    @JsonProperty("contentType")
    private Integer contentType;
    @JsonProperty("videoUrl")
    private Object videoUrl;
    @JsonProperty("syncStatus")
    private Integer syncStatus;
    @JsonProperty("publishType")
    private Object publishType;
    @JsonProperty("activityName")
    private Object activityName;
    @JsonProperty("activityIdList")
    private String activityIdList;
    @JsonProperty("topicIdList")
    private String topicIdList;
    @JsonProperty("artReeditReviewStatus")
    private Integer artReeditReviewStatus;
    @JsonProperty("lastCommTime")
    private String lastCommTime;
    @JsonProperty("maActivityName")
    private String maActivityName;
    @JsonProperty("maActivityType")
    private String maActivityType;
    @JsonProperty("maInviteUrl")
    private String maInviteUrl;
    @JsonProperty("maActivityId")
    private Object maActivityId;
    @JsonProperty("videoList")
    private String videoList;
    @JsonProperty("externalChainList")
    private String externalChainList;
    @JsonProperty("topicCategoryType")
    private Integer topicCategoryType;
    @JsonProperty("deleteReason")
    private String deleteReason;
    @JsonProperty("events")
    private String events;
    @JsonProperty("awardTopicId")
    private Long awardTopicId;
    @JsonProperty("prizeValidityBegin")
    private String prizeValidityBegin;
    @JsonProperty("prizeValidityEnd")
    private String prizeValidityEnd;
    @JsonProperty("topicApproveStatus")
    private Integer topicApproveStatus;
    @JsonProperty("rejectReasonType")
    private Object rejectReasonType;
    @JsonProperty("activityId")
    private Object activityId;
    @JsonProperty("feedUrlList")
    private String feedUrlList;
    @JsonProperty("adjustDirection")
    private Object adjustDirection;
    @JsonProperty("adjustQuaScore")
    private Object adjustQuaScore;
    @JsonProperty("videoCoverUrl")
    private String videoCoverUrl;
    @JsonProperty("videoTime")
    private Object videoTime;
    @JsonProperty("videoVertical")
    private Object videoVertical;
    @JsonProperty("collectionId")
    private Object collectionId;
    @JsonProperty("feedPictureList")
    private String feedPictureList;
}
