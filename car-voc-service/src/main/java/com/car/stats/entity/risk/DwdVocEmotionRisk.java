package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_EMOTION_RISK
 *
 */
@ApiModel(value="generate.TfDwdVocEmotionRisk")
@TableName("TF_DWD_VOC_EMOTION_RISK")
@Data
public class DwdVocEmotionRisk extends VocRisk implements Serializable {
    private String id;

    private String channelId;

    private String firstDimensionCode;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private BigDecimal totalNum;

    private BigDecimal negativeNum;

    private BigDecimal praiseNum;

    private BigDecimal riskIndex;

    private String brandCode;


    private Date createTime;

    private static final long serialVersionUID = 1L;
}
