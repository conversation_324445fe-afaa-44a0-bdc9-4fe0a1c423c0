package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * @version 1.0.0
 * @ClassName AdsVocFirstIntentionDi.java
 * @Description TODO
 * @createTime 2022年10月13日 15:29
 * @Copyright voc
 */

@Data
@TableName("TF_DWS_VOC_EMOTION_USER")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ADS意图统计表", description="ADS意图统计表")
public class DwsVocIntention {
    /**
     * 区域
     */
    @ApiModelProperty(value="区域")
    private String area;

    /**
     * 用户类型
     */
    @ApiModelProperty(value="用户类型")
    private String userType;

    /**
     * 用户等级
     */
    @ApiModelProperty(value="用户等级")
    private BigDecimal userLevel;

    /**
     * 品牌
     */
    @ApiModelProperty(value="品牌")
    private String brandCode;

    /**
     * 车系
     */
    @ApiModelProperty(value="车系")
    private String carSeriesCode;

    /**
     * 车型组
     */
    @ApiModelProperty(value="车型组")
    private String carGroup;

    /**
     * 车分类
     */
    @ApiModelProperty(value="车分类")
    private String carType;

    /**
     * 数据来源：APP、DCC、400
     */
    @ApiModelProperty(value="数据来源：APP、DCC、400")
    private String dataSource;

    /**
     * 公域渠道
     */
    @ApiModelProperty(value="公域渠道")
    private String channelId;

    /**
     * 一级标签
     */
    @ApiModelProperty(value="一级标签")
    private String firstDimensionCode;




    /**
     * 意图
     */
    @ApiModelProperty(value="意图")
    private String intentionType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private LocalDateTime publishDate;

    /**
     * 计数
     */
    @ApiModelProperty(value="计数")
    private BigDecimal statistic;

    private String id;
}
