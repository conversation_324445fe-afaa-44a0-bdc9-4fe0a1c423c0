package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 *
 */
@ApiModel(value="DwsVocWorkorderEmotionUserDWS用户统计表")
@Data
@TableName("TF_DWD_VOC_SENTENCE")
public class DwsVocWorkorderEmotionUser implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value="ID")
    private String id;

    /**
     * 区域
     */
    @ApiModelProperty(value="区域")
    private String province;

    /**
     * 用户类型
     */
    @ApiModelProperty(value="用户类型")
    private String oneId;

    /**
     * 用户类型
     */
    @ApiModelProperty(value="用户类型")
    private String userType;

    /**
     * 用户等级
     */
    @ApiModelProperty(value="用户等级")
    private BigDecimal userLevel;

    /**
     * 品牌
     */
    @ApiModelProperty(value="品牌")
    private String brandCode;

    /**
     * 车系
     */
    @ApiModelProperty(value="车系")
    private String carSeriesCode;

    /**
     * 车型组
     */
    @ApiModelProperty(value="车型组")
    private String carGroup;

    /**
     * 车分类
     */
    @ApiModelProperty(value="车分类")
    private String carType;

    /**
     * 数据来源：APP、DCC、400
     */
    @ApiModelProperty(value="数据来源：APP、DCC、400")
    private String dataSource;

    /**
     * 公域渠道
     */
    @ApiModelProperty(value="公域渠道")
    private String channelId;

    private String mediaFirstCategory;

    private String mediaName;

    /**
     * 一级标签
     */
    @ApiModelProperty(value="一级标签")
    private String firstDimensionCode;

    /**
     * 情感
     */
    @ApiModelProperty(value="情感")
    private String dimensionEmotion;

    /**
     * 意图
     */
    @ApiModelProperty(value="意图")
    private String intentionType;

    private Date createTime;

    private Date updateTime;

    private Date publishDate;

    /**
     * 计数
     */
    @ApiModelProperty(value="计数")
    private BigDecimal statistic;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private String emotionKeyword;

    private static final long serialVersionUID = 1L;
}
