package com.car.stats.entity.wo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.annotation.DataDesensitization;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 原始工单数据
 *
 * @TableName t317_csv_rescue_wo_i_d
 */
@Data
@TableName(value = "t317_csv_rescue_wo_i_d")
public class WoOriginalData implements Serializable {

    /**
     * 工单编号
     */
    @TableId
    @ApiModelProperty(value = "工单编号", example = "exampleWoNum")
    private String woNum;

    @ApiModelProperty(value = "code", example = "exampleCode")
    private String code;

    @ApiModelProperty(value = "OneID", example = "exampleOneID")
    private String oneid;

    @ApiModelProperty(value = "工单主题", example = "exampleWoTheme")
    private String woTheme;

    @ApiModelProperty(value = "工单类型", example = "exampleWoType")
    private String woType;

    @ApiModelProperty(value = "工单内容", example = "exampleWoContent")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    private String woContent;

    @ApiModelProperty(value = "来源渠道", example = "exampleWoChannel")
    private String woChannel;

    @ApiModelProperty(value = "客户姓名", example = "exampleCustomerName")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.NAME})
    private String customerName;

    @ApiModelProperty(value = "用户类型", example = "exampleCustomerType")
    private String customerType;

    @ApiModelProperty(value = "客户省份", example = "exampleCustomerProvince")
    private String customerProvince;

    @ApiModelProperty(value = "客户市", example = "exampleCustomerCity")
    private String customerCity;

    @ApiModelProperty(value = "客户满意度", example = "exampleCustomerSatisfaction")
    private String customerSatisfaction;

    @ApiModelProperty(value = "品牌", example = "exampleBrand")
    private String brand;

    @ApiModelProperty(value = "车系", example = "exampleCarSeries")
    private String carSeries;

    @ApiModelProperty(value = "车型名称", example = "exampleModelName")
    private String modelName;

    @ApiModelProperty(value = "服务商", example = "exampleServiceStationInfo")
    private String serviceStationInfo;

    @ApiModelProperty(value = "处理意见", example = "exampleDisposeOpinion")
    private String disposeOpinion;

    @ApiModelProperty(value = "救援提供渠道", example = "exampleRescueProvideChannelName")
    private String rescueProvideChannelName;

    @ApiModelProperty(value = "救援订单编号", example = "exampleRescueOrderNum")
    private String rescueOrderNum;

    @ApiModelProperty(value = "数据日期", example = "20231010")
    private Integer dataDate;

    @ApiModelProperty(value = "经销商", example = "exampleDealer")
    private String dealer;

    @ApiModelProperty(value = "大区", example = "exampleDealerRegion1")
    @TableField("dealer_region_1")
    private String dealerRegion1;

    @ApiModelProperty(value = "小区", example = "exampleDealerRegion2")
    @TableField("dealer_region_2")
    private String dealerRegion2;

    @ApiModelProperty(value = "状态", example = "exampleStatus")
    private String status;

    @ApiModelProperty(value = "状态code", example = "exampleStatusCode")
    private String statusCode;

    @ApiModelProperty(value = "工单四级标签", example = "exampleTagCode")
    private String tagCode;

    @ApiModelProperty(value = "vin", example = "exampleVin")
    @DataDesensitization(type = DataDesensitization.DesensitizationType.VIN)
    private String vin;

    @ApiModelProperty(value = "phone", example = "phone")
    @DataDesensitization(type = DataDesensitization.DesensitizationType.PHONE)
    private String customerPhone;

    @ApiModelProperty(value = "是否救援", example = "exampleIsRescue")
    private String isRescue;

    @ApiModelProperty(value = "网点编码", example = "exampleDlrCode")
    private String dlrCode;

    @ApiModelProperty(value = "救援网点名称", example = "exampleDlrName")
    private String dlrName;

    @ApiModelProperty(value = "首次反馈时间", example = "2023-10-10 10:00:00")
    private Date dlrFirstFeedbackTime;

    @ApiModelProperty(value = "购车网点名称", example = "exampleVehicleDlrName")
    private String vehicleDlrName;

    @ApiModelProperty(value = "区域督导姓名", example = "exampleRegionalSupervisor")
    private String regionalSupervisor;

    @ApiModelProperty(value = "来源", example = "exampleChannelSource")
    private String channelSource;

    @ApiModelProperty(value = "不满意原因分类", example = "exampleDissatisfactionCategory")
    private String dissatisfactionCategory;

    @ApiModelProperty(value = "工单关闭时间", example = "2023-10-10 10:00:00")
    private Date closeTime;

    @ApiModelProperty(value = "是否一次性解决", example = "exampleIsOneTimeResolution")
    private String isOneTimeResolution;

    @ApiModelProperty(value = "未一次性解决原因", example = "exampleReason")
    private String reason;

    @ApiModelProperty(value = "是否回访客户", example = "exampleIsFollowupCustomer")
    private String isFollowupCustomer;

    @ApiModelProperty(value = "是否意愿到店", example = "exampleIsVisit")
    private String isVisit;

    @ApiModelProperty(value = "是否批量订单", example = "exampleIsBatchOrder")
    private String isBatchOrder;

    @ApiModelProperty(value = "创建时间", example = "2023-10-10 10:00:00")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "2023-10-10 10:00:00")
    private Date modifyTime;

    @ApiModelProperty(value = "首次下发时间", example = "2023-10-10 10:00:00")
    private Date firstStepTime;

    @ApiModelProperty(value = "渠道2级", example = "exampleChannelSource2")
    @TableField("channel_source_2")
    private String channelSource2;

    @ApiModelProperty(value = "渠道3级", example = "exampleChannelSource3")
    @TableField("channel_source_3")
    private String channelSource3;

    @ApiModelProperty(value = "是否后置工单 0否 1是", example = "0")
    private String needFlow;

    @ApiModelProperty(value = "同步时间", example = "2023-10-10 10:00:00")
    private Date syncTime;

    @ApiModelProperty(value = "voc渠道1级", example = "exampleVocChannel1")
    @TableField("voc_channel_1")
    private String vocChannel1;

    @ApiModelProperty(value = "voc渠道2级", example = "exampleVocChannel2")
    @TableField("voc_channel_2")
    private String vocChannel2;

    @ApiModelProperty(value = "voc渠道3级", example = "exampleVocChannel3")
    @TableField("voc_channel_3")
    private String vocChannel3;

    @ApiModelProperty(value = "任一指标code", example = "exampleTagCodeRandom")
    private String tagCodeRandom;

}