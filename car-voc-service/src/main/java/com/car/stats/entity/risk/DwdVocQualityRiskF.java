package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_QUALITY_RISK
 *
 */
@ApiModel(value="generate.TfDwdVocQualityRiskF")
@Data
@TableName("TF_DWD_VOC_QUALITY_RISK")
public class DwdVocQualityRiskF implements Serializable {
    private String id;

    private String channelId;

    private String firstDimensionCode;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private BigDecimal totalNum;

    private BigDecimal qualityIndex;

    private BigDecimal riskIndex;

    private String statisticType;

    private Date publishDate;

    private String dateYear;

    private String dateMonth;

    private String dateWeek;

    private Date createTime;

    private BigDecimal pNum;

    private BigDecimal sNum;

    private BigDecimal channelNum;

    private String brandCode;

    private static final long serialVersionUID = 1L;
}
