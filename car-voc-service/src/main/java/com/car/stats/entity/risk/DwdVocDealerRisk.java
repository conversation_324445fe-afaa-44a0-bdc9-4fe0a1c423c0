package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_RISK
 *
 */
@ApiModel(value="网点风险")
@TableName("tf_dwd_voc_dealer_risk")
@Data
public class DwdVocDealerRisk extends VocRisk implements Serializable {
    private String id;

//    private String channelId;

    private String firstDimensionCode;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private BigDecimal totalNum;

    private BigDecimal negativeNum;

    private BigDecimal complainNum;

    private BigDecimal riskIndex;

    private String brandCode;
    private String dlrName;


    private Date createTime;
}
