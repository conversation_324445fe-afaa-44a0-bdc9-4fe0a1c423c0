package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tf_dwd_voc_sentence")
public class DwdVocSentence {


    @JsonProperty("contentId")
    private String contentId;
    @JsonProperty("contentType")
    private String contentType;
    @JsonProperty("id")
    private String id;
    @JsonProperty("oneId")
    private String oneId;

    @JsonProperty("isOneId")
    private String isOneId;
    private String originalIndex;
    private String brandCode;
    private String carSeriesCode;

    @JsonProperty("userType")
    private String userType;
    @JsonProperty("userLevel")
    private Integer userLevel;
    @JsonProperty("publishTime")
    private String publishTime;
    @JsonProperty("province")
    private String province;
    @JsonProperty("url")
    private String url;
    @JsonProperty("channelId")
    private String channelId;
    @JsonProperty("displayName")
    private String displayName;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("viewCounts")
    private String viewCounts;
    @JsonProperty("commCounts")
    private String commCounts;
    @JsonProperty("likeCounts")
    private String likeCounts;
    @JsonProperty("sentence")
    private String sentence;
    @JsonProperty("emotionKeyword")
    private String emotionKeyword;
    @JsonProperty("topicCode")
    private String topicCode;
    @JsonProperty("firstDimensionCode")
    private String firstDimensionCode;
    @JsonProperty("threeDimensionCode")
    private String threeDimensionCode;
    @JsonProperty("dimensionEmotion")
    private String dimensionEmotion;
    @JsonProperty("intentionType")
    private String intentionType;
    @JsonProperty("isModelResult")
    private Integer isModelResult;
    @JsonProperty("topicProportion")
    private Double topicProportion;
    @JsonProperty("secondDimensionCode")
    private String secondDimensionCode;
    @JsonProperty("secondDimension")
    private String secondDimension;
    @JsonProperty("topic")
    private String topic;
    @JsonProperty("firstDimension")
    private String firstDimension;
    @JsonProperty("threeDimension")
    private String threeDimension;
    @JsonProperty("topicDesc")
    private String topicDesc;
    @JsonProperty("level")
    private String level;
    @JsonProperty("tagType")
    private String tagType;
    @JsonProperty("status")
    private String status;
    @JsonProperty("woType")
    private String woType;
}
