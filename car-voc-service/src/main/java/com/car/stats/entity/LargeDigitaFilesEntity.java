package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Title: LargeDigitaFilesModel
 * @Package: com.voc.service.insights.engine.api.model
 * @Description:
 * @Author: cuick
 * @Date: 2024/12/15 18:46
 * @Version:1.0
 */
@Builder
@Data
@TableName("voc_attachment_download_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class LargeDigitaFilesEntity implements Serializable {

    String id;
    String userId;
    String taskId;
    String taskName;
    String type;
    String status;
    String fileKey;
    LocalDateTime createTime;
}
