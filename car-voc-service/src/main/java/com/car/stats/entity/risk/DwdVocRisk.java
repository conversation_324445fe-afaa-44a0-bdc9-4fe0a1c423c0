package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_RISK
 *
 */
@ApiModel(value="TF_DWD_VOC_RISK")
//@TableName("TF_DWD_VOC_EMOTION_RISK")
@TableName("TF_DWD_VOC_EMOTION_RISK")
@Data
public class DwdVocRisk extends VocRisk implements Serializable {
    @ApiModelProperty(value="ID")
    private String id;
    private String firstDimensionCode;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private BigDecimal totalNum;

    private BigDecimal negativeNum;

    private BigDecimal riskIndex;

    private String channelId;

    private String brandCode;


    private Date createTime;

    private static final long serialVersionUID = 1L;
}
