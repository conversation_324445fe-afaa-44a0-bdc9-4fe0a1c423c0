package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * @Description 案例表
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "案例表", description = "案例表")
public class CaseLibrary implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "案例主题")
    private String name;

    @ApiModelProperty(value = "案例简述")
    private String sketch;

    @ApiModelProperty(value = "案例总结")
    private String content;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件url地址")
    private String fileUrl;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "案例链接")
    private String caseUrl;

    @ApiModelProperty(value = "案例分类id")
    private String caseClassifyId;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "车系id")
    private String pBrandId;

    @ApiModelProperty(value = "车系名称")
    private String pBrandName;

    @ApiModelProperty(value = "授权品牌")
    private String brandIds;

    @ApiModelProperty(value = "点赞数")
    private Integer likeCount;

    @ApiModelProperty(value = "浏览量")
    private Integer browse;

    @ApiModelProperty(value = "下载量")
    private Integer download;

    @ApiModelProperty(value = "审核状态（0待审核，1已通过，2已驳回）")
    private Integer status;

    @ApiModelProperty(value = "驳回原因")
    private String reason;

    @ApiModelProperty(value = "分享部门id")
    private String departmentId;

    @ApiModelProperty(value = "分享部门名称")
    private String departmentName;

    @ApiModelProperty(value = "首页推荐（0否，1是）")
    private Integer recommend;

    @ApiModelProperty(value = "分享人员")
    private String createBy;

    @ApiModelProperty(value = "分享时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
