package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_USER_RISK
 *
 */
@ApiModel(value="DwdVocUserRisk")
@Data
@TableName("TF_DWD_VOC_USER_RISK")
public class DwdVocUserRisk extends VocRisk implements Serializable {
    private String id;

    private String userId;

    private String userType;

    private BigDecimal userLevel;

    private String displayName;

    private BigDecimal negativeNum;

    private BigDecimal complainNum;

    private BigDecimal channelNum;

    private BigDecimal emotionNum;

    private String riskLevel;

    private BigDecimal riskIndex;


    @TableField(exist = false)
    private Date startDate;
    @TableField(exist = false)
    private Date endDate;

    private String brandCode;




    private Date createTime;

    private static final long serialVersionUID = 1L;
}
