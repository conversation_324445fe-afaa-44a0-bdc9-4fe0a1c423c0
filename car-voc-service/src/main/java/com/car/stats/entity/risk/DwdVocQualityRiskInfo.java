package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_QUALITY_RISK_INFO
 *
 */
@ApiModel(value="generate.TfDwdVocQualityRiskInfo")
@Data
@TableName("TF_DWD_VOC_QUALITY_RISK_INFO")
public class DwdVocQualityRiskInfo implements Serializable {
    private String channelId;

    private String riskId;

    private String statisticType;

    private Date publishDate;

    private String dateYear;

    private String dateMonth;

    private String dateWeek;

    private Date createTime;

    private String firstDimensionCode;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private String keyword;

    private Long keywordNum;

    private BigDecimal keywordWeight;

    private static final long serialVersionUID = 1L;
}
