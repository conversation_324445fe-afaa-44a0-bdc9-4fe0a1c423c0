package com.car.stats.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWS_VOC_EMOTION
 *
 */
@Data
@TableName("TF_DWS_VOC_EMOTION_USER")
public class DwsVocEmotionDi implements Serializable {
    /**
     * ID
     */
    private String id;

    /**
     * 区域
     */
    private String area;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户等级
     */
    private BigDecimal userLevel;

    /**
     * 品牌
     */
    private String brandCode;

    /**
     * 车系
     */
    private String carSeriesCode;

    /**
     * 车型组
     */
    private String carGroup;

    /**
     * 车分类
     */
    private String carType;

    /**
     * 数据来源：APP、DCC、400
     */
    private String dataSource;

    /**
     * 公域渠道
     */
    private String channelId;

    /**
     * 一级标签
     */
    private String firstDimensionCode;

    /**
     * 二级标签
     */
    private String secondDimensionCode;

    /**
     * 三级标签
     */
    private String threeDimensionCode;

    /**
     * 四级标签/话题
     */
    private String topicCode;

    /**
     * 关键词/情感词
     */
    private String emotionKeyword;

    /**
     * 情感
     */
    private String dimensionEmotion;

    private Date createTime;

    private Date updateTime;

    private Date publishDate;

    /**
     * 计数
     */
    private BigDecimal statistic;

    private static final long serialVersionUID = 1L;
}
