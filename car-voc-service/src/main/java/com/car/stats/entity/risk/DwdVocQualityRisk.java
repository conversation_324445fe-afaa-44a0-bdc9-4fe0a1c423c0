package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TF_DWD_VOC_RISK
 *
 */
@ApiModel(value="TF_DWD_VOC_QUALITY_RISK")
//@TableName("TF_DWD_VOC_QUALITY_RISK")
@TableName("TF_DWD_VOC_QUALITY_RISK")
@Data
public class DwdVocQualityRisk extends VocRisk implements Serializable {
    @ApiModelProperty(value="ID")
    private String id;
    private String channelId;


    private BigDecimal riskIndex;

    private BigDecimal totalNum;

    private Date createTime;

    private String firstDimensionCode;

    private String secondDimensionCode;

    private String threeDimensionCode;

    private String topicCode;

    private String brandCode;


    private static final long serialVersionUID = 1L;
}
