package com.car.stats.entity.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName VocRisk.java
 * @Description TODO
 * @createTime 2023年07月27日 10:21
 * @Copyright voc
 */
@Data
public class VocRisk {
    private String statisticType;

    @TableField(exist = false)
    private String statisticTypeStr;

    private Date publishDate;
    private String dateYear;
    private String dateQuarter;

    private String dateMonth;

    private String dateWeek;

    public String getStatisticTypeStr() {
        return CalculatorUtils.getPeriod(statisticType);
    }
}
