package com.car.stats.strksMapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.DwdVocSentence;
import com.car.stats.entity.DwsVocUser;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.vo.ChannelVo;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.UserChannelPubNumVo;
import com.car.voc.common.Result;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocUserMapper.java
 * @Description TODO
 * @createTime 2022年11月02日 10:09
 * @Copyright voc
 */
public interface DwdVocSentenceMapper extends BaseMapper <DwdVocSentence> {
    List<ChannelVo> channel2Distribution(@Param("model")  FilterCriteriaModel model);

    List<HomePurposeTrendVo> intentionNumAndUserNumList(FilterCriteriaModel model);

}
