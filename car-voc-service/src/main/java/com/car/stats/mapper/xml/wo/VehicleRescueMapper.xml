<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.wo.VehicleRescueMapper">
    <resultMap id="BaseResultMap" type="com.car.stats.entity.wo.WoOriginalData">
        <id property="woNum" column="wo_num" jdbcType="VARCHAR"/>
        <result property="oneid" column="oneid" jdbcType="VARCHAR"/>
        <result property="woTheme" column="wo_theme" jdbcType="VARCHAR"/>
        <result property="brand" column="brand" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="customerProvince" column="customer_province" jdbcType="VARCHAR"/>
        <result property="modelName" column="model_name" jdbcType="VARCHAR"/>
        <result property="serviceStationInfo" column="service_station_info" jdbcType="VARCHAR"/>
        <result property="woType" column="wo_type" jdbcType="VARCHAR"/>
        <result property="woContent" column="wo_content" jdbcType="VARCHAR"/>
        <result property="disposeOpinion" column="dispose_opinion" jdbcType="VARCHAR"/>
        <result property="rescueProvideChannelName" column="rescue_provide_channel_name" jdbcType="VARCHAR"/>
        <result property="rescueOrderNum" column="rescue_order_num" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="syncTime" column="sync_time" jdbcType="TIMESTAMP"/>
        <result property="dataDate" column="data_date" jdbcType="INTEGER"/>
        <result property="customerCity" column="customer_city" jdbcType="VARCHAR"/>
        <result property="dealerRegion1" column="dealer_region_1" jdbcType="VARCHAR"/>
        <result property="dealerRegion2" column="dealer_region_2" jdbcType="VARCHAR"/>
        <result property="dealer" column="dealer" jdbcType="VARCHAR"/>
        <result property="tagCode" column="tag_code" jdbcType="OTHER"/>
        <result property="woChannel" column="wo_channel" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        wo_num,oneid,wo_theme,
        brand,create_time,customer_province,
        model_name,service_station_info,wo_type,
        wo_content,dispose_opinion,rescue_provide_channel_name,
        rescue_order_num,customer_name,sync_time,
        data_date,customer_city,dealer_region_1,
        dealer_region_2,dealer,tag_code,
        wo_channel
    </sql>

    <select id="customerSatisfaction" resultType="com.car.stats.vo.StatisticVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
            ROUND(COUNT( CASE WHEN vid.customer_satisfaction = '满意' THEN 1 END ) / COUNT( 1 ) * 100,2) as statistic
        FROM
            wo_original_data vid
        WHERE 1 = 1
    </select>

    <select id="rescueStatus" resultType="com.car.stats.vo.wo.WoRescueVolumeVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
            COUNT( CASE WHEN vid.is_rescue IS NULL OR vid.is_rescue = '' OR vid.is_rescue = 'null' OR vid.is_rescue = '否' THEN 1 END ) AS unRescueNum,
            COUNT( CASE WHEN vid.is_rescue = '是' THEN 1 END ) AS rescueNum,
            COUNT( 1 ) AS statistic
        FROM
            wo_original_data vid
        WHERE 1 = 1
    </select>

    <select id="rescueStatusTrend" resultType="com.car.stats.vo.wo.WoRescueVolumeVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr" />
        SELECT
        case when length(cc.date_)>9 then date_format(date_, '%Y/%m/%d')
        else cc.date_ end as date_str,
            vid.* ,
            ROUND(rescueNum / statistic * 100, 2) AS rescueNumP,
            ROUND(unRescueNum / statistic * 100, 2) AS unRescueNumP
        FROM
            common_date_tools cc
        LEFT JOIN (
            SELECT
                <include refid="publicDateFilterCriteria.vo-groupby-com-cycle-table" /> AS date_create,
                COUNT( CASE WHEN vid.is_rescue IS NULL OR vid.is_rescue = '' OR vid.is_rescue = 'null' OR vid.is_rescue = '否'  THEN 1 END ) AS unRescueNum,
                COUNT( CASE WHEN vid.is_rescue = '是' THEN 1 END ) AS rescueNum,
                COUNT( 1 ) AS statistic
            FROM
                wo_original_data vid
            WHERE
                1 = 1
            group by date_create
        ) vid ON cc.date_ = vid.date_create
        ORDER BY date_ ASC
    </select>

    <select id="carSeriesTop" resultType="com.car.stats.vo.ProportionCarSeriesVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        car_series as carSeriesName,
        statisticP,
        voice_c as statistic,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY car_series ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
            car_series ,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
            from wo_original_data vid
            left join  (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
            )c9 on date(vid.create_time) BETWEEN startDate and endDate
            WHERE 1=1
            and date(vid.create_time) BETWEEN startDate and endDate
            GROUP BY date_str,car_series
        </if>

        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            car_series,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
            FROM common_date_tools cdt
            LEFT JOIN wo_original_data vid ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),
            car_series, cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
        <if test="model.rownum !=null and model.rownum != '' " >
            limit #{model.rownum}
        </if>
    </select>

    <select id="carModelTop" resultType="com.car.stats.vo.ProportionCarSeriesVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        model_name as carSeriesName,
        statisticP,
        voice_c as statistic,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY model_name ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
            model_name ,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
            from wo_original_data vid
            left join  (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
            )c9 on date(vid.create_time) BETWEEN startDate and endDate
            WHERE 1=1 and model_name is not null
            and date(vid.create_time) BETWEEN startDate and endDate
            GROUP BY date_str,model_name
        </if>

        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            model_name,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
            FROM common_date_tools cdt
            LEFT JOIN wo_original_data vid ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1 and model_name is not null
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),
            model_name, cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
        <if test="model.rownum !=null and model.rownum != '' " >
            limit #{model.rownum}
        </if>
    </select>

    <select id="outletsTop" resultType="com.car.stats.vo.wo.BranchesVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
            dlr_code as dlrCode,
            dlr_name as branchName,
            dealer_region_1 as region1,
            dealer_region_2 as region2,
            customer_province as province,
            customer_city as city,
            count(1) as statistic
        FROM
            wo_original_data vid
        WHERE
            1=1
            and dlr_code is not null and dlr_name is not null
        GROUP BY
            dlr_code,dlr_name,dealer_region_1,dealer_region_2,customer_province,customer_city
        order by statistic desc
        <if test="model.rownum !=null and model.rownum != '' " >
            limit #{model.rownum}
        </if>
    </select>

    <select id="workOrderDetailsList" resultType="com.car.stats.vo.wo.WoRescueVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
            *
        FROM
            wo_original_data vid
        WHERE
            1=1
        order by create_time desc
    </select>

    <select id="customerType" resultType="com.car.stats.vo.wo.WoCustomerTypeProportionVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
            customer_type as customerType,
            count(1) as statistic,
            count(distinct oneid) as userCount,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP,
            ROUND(COUNT(distinct oneid) / SUM(COUNT(distinct oneid)) OVER () * 100, 2) AS userCountP
        from
            wo_original_data vid
        where
            1=1
            and customer_type != '未购车'
        group by customer_type
    </select>

    <select id="receiver" resultType="com.car.stats.vo.wo.WoReceiverVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        select
            rescue_type AS rescueType,
            count(1) as statistic,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
        from wo_original_data
        where
            1=1
            and rescue_type is not null
        group by rescue_type
    </select>
</mapper>
