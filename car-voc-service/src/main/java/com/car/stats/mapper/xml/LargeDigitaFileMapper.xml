<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.LargeDigitaFileMapper">

    <select id="getFileList" resultType="com.car.stats.entity.LargeDigitaFilesEntity">
        select
            id,task_id,user_id,task_name,`type`,status,file_key,create_time
        from(
                select
                    id,task_id,user_id,task_name,`type`,status,file_key,create_time,
                    ROW_NUMBER() OVER (PARTITION BY  `user_id`,`type` ORDER BY `create_time` desc) as rn
                from voc_attachment_download_record
                where user_id = #{userId}
            )f
        where rn = 1
    </select>

    <select id="getFile" resultType="com.car.stats.entity.LargeDigitaFilesEntity">
        select
        id,task_id,user_id,task_name,`type`,status,file_key,create_time
        from(
        select
        id,task_id,user_id,task_name,`type`,status,file_key,create_time,
        ROW_NUMBER() OVER (PARTITION BY  `user_id`,`type` ORDER BY `create_time` desc) as rn
        from voc_attachment_download_record
        where user_id = #{userId}
        )f
        where rn = 1
        order by create_time desc
        limit 1
    </select>
</mapper>
