package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.CaseLibrary;
import com.car.stats.model.CaseLibraryModel;
import com.car.voc.entity.SysCaseClassify;
import com.car.voc.vo.SysCaseClassifyListVo;
import org.apache.ibatis.annotations.Param;

/**
 * TODO
 *
 * @Description 案例库 Mapper
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
public interface CaseLibraryMapper extends BaseMapper<CaseLibrary> {

    Integer getCaseNum(@Param("model") CaseLibraryModel caseLibraryModel);

    Integer getDepNum(@Param("model") CaseLibraryModel caseLibraryModel);

    Integer getPeopleNum(@Param("model") CaseLibraryModel caseLibraryModel);

    String getDescCreateTime(@Param("model") CaseLibraryModel caseLibraryModel);
}
