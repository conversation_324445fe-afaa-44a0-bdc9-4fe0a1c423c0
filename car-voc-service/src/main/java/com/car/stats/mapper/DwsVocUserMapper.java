package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.DwsVocUser;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.vo.UserChannelPubNumVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocUserMapper.java
 * @Description TODO
 * @createTime 2022年11月02日 10:09
 * @Copyright voc
 */
public interface DwsVocUserMapper extends BaseMapper <DwsVocUser> {
    List<UserChannelPubNumVo> userChannelPubNum(@Param("model") LabelDetailFilterModel model);

    List<UserChannelPubNumVo> userChannelPubNumUserIds(@Param("userIds") Set<String> userIds);

    List<UserChannelPubNumVo> userChannelPubNumFilter(@Param("model")  LabelDetailFilterModel model,@Param("userId") String userId );

    BigDecimal getComplaintUserChannelNum(@Param("model") ComplaintUserTopModel model, @Param("userId") String id);
}
