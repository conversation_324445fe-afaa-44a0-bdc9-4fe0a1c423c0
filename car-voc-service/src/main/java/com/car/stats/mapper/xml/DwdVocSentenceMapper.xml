<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.strksMapper.DwdVocSentenceMapper">

    <select id="channel2Distribution"   resultType="com.car.stats.vo.ChannelVo">

        SELECT
        sour.*
        FROM
        (
        SELECT
        ch.pid as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT  JOIN voc_channel_category ch ON vid.channel_id = ch.id
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        GROUP BY ch.pid
        ) sour
        where 1=1
    </select>

    <select id="intentionNumAndUserNumList" resultType="com.car.stats.vo.HomePurposeTrendVo">
        
        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr,
        SUM(CASE WHEN vid.INTENTION_TYPE = #{model.intention} THEN vid.STATISTIC ELSE 0 END) AS intention,
        COUNT(DISTINCT CASE WHEN vid.INTENTION_TYPE = #{model.intention} THEN vid.USER_ID END) AS userCount
        <!--SUM(vid.STATISTIC) AS intentionTotal,
        COUNT(DISTINCT vid.USER_ID) AS userTotal -->
        FROM TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.query_no_intention_com_vid" />
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
    </select>



</mapper>
