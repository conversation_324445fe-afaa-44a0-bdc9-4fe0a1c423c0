<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="publicDateFilterCriteria">


    <sql id="queryCom_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.qualityThreeCodes"/>
        <include refid="ComFilterMapper.general_query_Tag_Disables" />
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>
    <sql id="queryCom_vid_up">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.up_date" />
        <include refid="ComFilterMapper.qualityThreeCodes"/>
        <include refid="ComFilterMapper.general_query_Tag_Disables" />
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>


    <sql id="query_no_intention_com_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.qualityThreeCodes"/>
        <include refid="ComFilterMapper.general_query_Tag_Disables" />
    </sql>

    <sql id="querychan_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.qualityThreeCodes"/>
    </sql>



    <sql id="queryCom_WORKORDER_vid">
        <include refid="ComFilterMapper.general_WORKORDER_query" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.general_query_Other_Tag" />
    </sql>
    <sql id="queryCom_WORKORDER_vid_emotion">
        <include refid="ComFilterMapper.general_WORKORDER_query" />
        <include refid="ComFilterMapper.emotion_types"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.general_query_Other_Tag" />
    </sql>

<sql id="queryCom_complaint_web_vid">
        <include refid="ComFilterMapper.general_WORKORDER_query" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.general_query_Other_Tag" />
    </sql>




    <sql id="queryCom_vid_notDatasource">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.qualityThreeCodes"/>
        <include refid="ComFilterMapper.general_query_Tag_Disables" />
    </sql>


    <sql id="queryProductCom_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.qualityThreeCodes" />
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.this_problemLevel"/>
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>
    <sql id="queryProductchan_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_chan_dataSorce" />
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.qualityThreeCodes" />

        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.this_problemLevel"/>
    </sql>

    <sql id="queryProductCom_vid_up">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.qualityThreeCodes" />

        <include refid="ComFilterMapper.up_date" />
        <include refid="ComFilterMapper.this_problemLevel"/>
    </sql>

    <sql id="queryRepair_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
    </sql>

    <sql id="queryRepair_dis_parts_vid">
        <include refid="ComFilterMapper.general_query" />
    </sql>

    <sql id="sr_query_intention_com_vid">
        <include refid="ComFilterMapper.sr_general_query" />
        <include refid="ComFilterMapper.sr_general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.tag_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.qualityThreeCodes"/>
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.topicCodeTagQuery" />
        <include refid="ComFilterMapper.general_query_Tag_Disables" />
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_flow" />
        <include refid="ComFilterMapper.wo_type_list" />
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>



   <sql id="sr_query_date_com_vid">
        <include refid="ComFilterMapper.sr_general_query" />
        <include refid="ComFilterMapper.sr_general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.tag_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.qualityThreeCodes"/>
       <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.general_query_Tag_Disables" />
       <include refid="ComFilterMapper.wo_type" />
       <include refid="ComFilterMapper.wo_type_list" />
       <include refid="ComFilterMapper.wo_flow" />
       <include refid="ComFilterMapper.wo_user_type"/>
       <include refid="ComFilterMapper.wo-dlr" />
    </sql>

    <sql id="wo_com_vid">
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.create_time between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
            and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="model.woType !=null and model.woType != ''">
            and vid.wo_type = #{model.woType}
        </if>
        <if test="model.channelIds != null and model.channelIds.size()>0">
            and vid.wo_channel in
            <foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
            and
            <foreach item="item" collection="model.secondDimensionCodes" separator="or" open="(" close=")" index="">
                vid.tag_code like concat('%',#{item},'%')
            </foreach>
        </if>
    </sql>



    <!--质量的下钻查询 -->
    <sql id="queryPop_vid">
        <if test="model.createDate !=null and model.createDate != '' ">
            and  #{model.createDate}>=vid.create_time
        </if>
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.this_problemLevel"/>
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>
    <!-- 业务的下钻查询 -->
    <sql id="queryPop_vid_emotion">

        <if test="model.createDate !=null and model.createDate != '' ">
          and  #{model.createDate}>=vid.create_time
        </if>
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_types"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo-dlr" />
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_user_type"/>
    </sql>
    <!-- 所有标签的下钻查询 -->
    <sql id="queryPop_allTags_vid_emotion">
        <if test="model.createDate !=null and model.createDate != '' ">
          and  #{model.createDate}>=vid.create_time
        </if>
        <include refid="ComFilterMapper.sr_general_query" />
        <include refid="ComFilterMapper.sr_general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_types"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>

    <sql id="repairQueryPop_vid">
        <include refid="ComFilterMapper.general_query" />
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.this_date" />
    </sql>


    <sql id="queryRisk_vid1">
        <include refid="ComFilterMapper.general_risk_query" />
        <include refid="ComFilterMapper.this_risk_date" />
    </sql>

    <sql id="queryRisk_vid">
        <include refid="ComFilterMapper.general_risk_query" />
        <include refid="ComFilterMapper.this_risk_date1" />
        <include refid="ComFilterMapper.this_risk_date_year" />
        <include refid="ComFilterMapper.this_statisticType" />
    </sql>

    <sql id="queryUserRisk_vid1">
        <include refid="ComFilterMapper.this_risk_date1" />
        <include refid="ComFilterMapper.this_risk_date_year" />
        <include refid="ComFilterMapper.this_statisticType" />
    </sql>
    <sql id="queryUserRisk_vid">
        <include refid="ComFilterMapper.this_date" />
    </sql>


    <sql id="groupby-com-cycle">
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            date_format(vid.PUBLISH_DATE,'%Y-%m-%d')
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
        <!--    date_format(next_day(vid.PUBLISH_DATE+15/24 - 7,2),'YYYY-iw')-->
            <!--   DATE_FORMAT(vid.PUBLISH_DATE,'%Y-%v') -->
               concat(DATE_FORMAT( vid.PUBLISH_DATE , '%Y' ),'-',DATE_FORMAT( vid.PUBLISH_DATE , '%u' ) )
               <!--== concat(REPLACE(to_char(next_day(vid.PUBLISH_DATE+15/24 - 7,2),'YYYY-iw'),'-','年'),'季')==-->
     </if>
     <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            date_format(vid.PUBLISH_DATE,'%Y-%m')
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
          <!--  date_format(vid.PUBLISH_DATE,'%Y-%q')-->
            CONCAT(YEAR(vid.PUBLISH_DATE),'-',quarter(vid.PUBLISH_DATE))

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 " >
            date_format(vid.PUBLISH_DATE,'%Y')
        </if>

    </sql>

    <sql id="groupby-com-wo">
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            date_format(vid.create_time,'%Y-%m-%d')
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
            <!--    date_format(next_day(vid.PUBLISH_DATE+15/24 - 7,2),'YYYY-iw')-->
            <!--   DATE_FORMAT(vid.PUBLISH_DATE,'%Y-%v') -->
            concat(DATE_FORMAT( vid.create_time , '%Y' ),'-',DATE_FORMAT( vid.create_time , '%u' ) + 1)
            <!--== concat(REPLACE(to_char(next_day(vid.PUBLISH_DATE+15/24 - 7,2),'YYYY-iw'),'-','年'),'季')==-->
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            date_format(vid.create_time,'%Y-%m')
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
            <!--  date_format(vid.PUBLISH_DATE,'%Y-%q')-->
            CONCAT(YEAR(vid.create_time),'-',quarter(vid.create_time))

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 " >
            date_format(vid.create_time,'%Y')
        </if>

    </sql>

    <sql id="groupby-com-cycle-replace">
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            date_format(vid.PUBLISH_DATE,'%Y-%m-%d')
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
        <!--    date_format(next_day(vid.PUBLISH_DATE+15/24 - 7,2),'YYYY-iw')-->
            <!--   DATE_FORMAT(vid.PUBLISH_DATE,'%Y-%v') -->
               concat(DATE_FORMAT( vid.PUBLISH_DATE , '%Y' ),'-',DATE_FORMAT( vid.PUBLISH_DATE , '%u' ) + 1)
               <!--== concat(REPLACE(to_char(next_day(vid.PUBLISH_DATE+15/24 - 7,2),'YYYY-iw'),'-','年'),'季')==-->
     </if>
     <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            replace (date_format(vid.PUBLISH_DATE,'%Y-%m'),'-0','-')
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
          <!--  date_format(vid.PUBLISH_DATE,'%Y-%q')-->
            CONCAT(YEAR(vid.PUBLISH_DATE),'-',quarter(vid.PUBLISH_DATE))

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 " >
            date_format(vid.PUBLISH_DATE,'%Y')
        </if>

    </sql>




    <sql id="groupby-date-com-all">

        select
        dateCycle.dateStr,
        datay.*
        from (

        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY), '%Y-%m-%d')
            AS date
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.date AS dateStr
            FROM calendar
            WHERE calendar.date >= DATE_FORMAT(#{model.startDate}, '%Y-%m-%d')
            AND DATE_FORMAT(#{model.endDate}, '%Y-%m-%d') >=calendar.date
            GROUP BY calendar.date
            ORDER BY calendar.date ASC
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) WEEK), '%Y-%V') AS week
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT
            calendar.week AS dateStr
            FROM calendar
            WHERE calendar.week >= concat(DATE_FORMAT( #{model.startDate} , '%Y' ),'-',DATE_FORMAT( #{model.startDate} , '%u' ) + 1)
            AND concat(DATE_FORMAT( #{model.endDate} , '%Y' ),'-',DATE_FORMAT( #{model.endDate} , '%u' ) + 1) >=calendar.week
            GROUP BY calendar.week
            ORDER BY calendar.week ASC
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH), '%Y-%m') AS month
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT
            calendar.month AS dateStr
            FROM calendar
            WHERE calendar.month >= DATE_FORMAT(#{model.startDate}, '%Y-%m')
            AND DATE_FORMAT(#{model.endDate}, '%Y-%m')>=calendar.month
            GROUP BY calendar.month
            ORDER BY calendar.month asc
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 ">
            WITH calendar AS (
            SELECT CONCAT(YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH)), '-',
            QUARTER(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH))) AS quarter
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.quarter AS dateStr
            FROM calendar
            WHERE calendar.quarter >= CONCAT(YEAR(#{model.startDate}), '-', QUARTER(#{model.startDate}))
            AND CONCAT(YEAR(#{model.endDate}), '-', QUARTER(#{model.endDate}))>=calendar.quarter
            GROUP BY calendar.quarter
            ORDER BY calendar.quarter ASC

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 ">
            WITH calendar AS (
            SELECT YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) YEAR)) AS year
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.year AS dateStr
            FROM calendar
            WHERE calendar.year >= YEAR(#{model.startDate})
            AND YEAR(#{model.endDate}) >=calendar.year
            GROUP BY calendar.year
            ORDER BY calendar.year ASC
        </if>
        ) dateCycle  left  join
    </sql>



    <sql id="groupby-date-com-all-data-airing">

        select
        dateCycle.dateStr,
        datay.*
        from (

        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY), '%Y-%m-%d')
            AS date
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.date AS dateStr
            FROM calendar
            WHERE calendar.date >= DATE_FORMAT(#{model.startDate}, '%Y-%m-%d')
            AND DATE_FORMAT(#{model.endDate}, '%Y-%m-%d') >=calendar.date
            GROUP BY calendar.date
            ORDER BY calendar.date ASC
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 ">
            select replace (t1.dateStr,'-0','-') as dateStr from (
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) WEEK), '%Y-%V') AS week
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT
            calendar.week AS dateStr
            FROM calendar
            WHERE calendar.week >= concat(DATE_FORMAT( #{model.startDate} , '%Y' ),'-',DATE_FORMAT( #{model.startDate} , '%u+1' ) )
            AND concat(DATE_FORMAT( #{model.endDate} , '%Y' ),'-',DATE_FORMAT( #{model.endDate} , '%u+1' ) ) >=calendar.week
            GROUP BY calendar.week
            ORDER BY calendar.week ASC
            )t1
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH), '%Y-%m') AS month
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT
            calendar.month AS dateStr
            FROM calendar
            WHERE calendar.month >= DATE_FORMAT(#{model.startDate}, '%Y-%m')
            AND DATE_FORMAT(#{model.endDate}, '%Y-%m')>=calendar.month
            GROUP BY calendar.month
            ORDER BY calendar.month asc
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 ">
            WITH calendar AS (
            SELECT CONCAT(YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH)), '-',
            QUARTER(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH))) AS quarter
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.quarter AS dateStr
            FROM calendar
            WHERE calendar.quarter >= CONCAT(YEAR(#{model.startDate}), '-', QUARTER(#{model.startDate}))
            AND CONCAT(YEAR(#{model.endDate}), '-', QUARTER(#{model.endDate}))>=calendar.quarter
            GROUP BY calendar.quarter
            ORDER BY calendar.quarter ASC

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 ">
            WITH calendar AS (
            SELECT YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) YEAR)) AS year
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.year AS dateStr
            FROM calendar
            WHERE calendar.year >= YEAR(#{model.startDate})
            AND YEAR(#{model.endDate}) >=calendar.year
            GROUP BY calendar.year
            ORDER BY calendar.year ASC
        </if>
        ) dateCycle  left  join
    </sql>


    <sql id="groupby-date-com-all-new">
        select
        dateCycle.dateStr,
        dateCycle.dayNum,
        datay.*
        from (
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY), '%Y-%m-%d')
            AS date
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.date AS dateStr
            FROM calendar
            WHERE calendar.date >= DATE_FORMAT(#{model.startDate}, '%Y-%m-%d')
            AND DATE_FORMAT(#{model.endDate}, '%Y-%m-%d') >=calendar.date
            GROUP BY calendar.date
            ORDER BY calendar.date ASC
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) WEEK), '%Y-%V') AS week,
            LAST_DAY(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) WEEK)) AS lastDay
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT
            calendar.week AS dateStr,
            case calendar.week  when concat(DATE_FORMAT( now() , '%Y' ),'-',DATE_FORMAT( #{model.endDate} , '%u' ) + 1) then WEEKDAY(CURDATE())+1
            else DAYOFWEEK(calendar.lastDay) end as dayNum
            FROM calendar
            WHERE calendar.week >= concat(DATE_FORMAT( #{model.startDate} , '%Y' ),'-',DATE_FORMAT( #{model.startDate} , '%u' ) + 1)
            AND concat(DATE_FORMAT( #{model.endDate} , '%Y' ),'-',DATE_FORMAT( #{model.endDate} , '%u' ) + 1) >=calendar.week
            GROUP BY calendar.week
            ORDER BY calendar.week ASC
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 ">
            WITH calendar AS (
            SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH), '%Y-%m') AS month,
            LAST_DAY(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH)) AS lastDay
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT
            calendar.month AS dateStr,
            case calendar.month  when date_format(now(),'%Y-%m') then day(now())
            else DAYOFMONTH(calendar.lastDay) end as dayNum
            FROM calendar
            WHERE calendar.month >= DATE_FORMAT(#{model.startDate}, '%Y-%m')
            AND DATE_FORMAT(#{model.endDate}, '%Y-%m')>=calendar.month
            GROUP BY calendar.month
            ORDER BY calendar.month asc
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 ">
            WITH calendar AS (
            SELECT CONCAT(YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH)), '-',
            QUARTER(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH))) AS quarter
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.quarter AS dateStr
            FROM calendar
            WHERE calendar.quarter >= CONCAT(YEAR(#{model.startDate}), '-', QUARTER(#{model.startDate}))
            AND CONCAT(YEAR(#{model.endDate}), '-', QUARTER(#{model.endDate}))>=calendar.quarter
            GROUP BY calendar.quarter
            ORDER BY calendar.quarter ASC

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 ">
            WITH calendar AS (
            SELECT YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) YEAR)) AS year
            FROM
            <include refid="ComFilterMapper.date-group-base"/>
            )
            SELECT calendar.year AS dateStr
            FROM calendar
            WHERE calendar.year >= YEAR(#{model.startDate})
            AND YEAR(#{model.endDate}) >=calendar.year
            GROUP BY calendar.year
            ORDER BY calendar.year ASC
        </if>
        ) dateCycle  left  join
    </sql>
    <sql id="groupby-date-com-all-table">
        (select
        dateCycle.dateStr,
        dateCycle.dayNum
        from (
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 ">
            select
            DATE_FORMAT(tdd .format_date,'%Y-%m-%d') as dateStr,
            1 as dayNum
            from
            tf_dim_date tdd
            where
            tdd .format_date >=#{model.startDate} and
            #{model.endDate}>=tdd .format_date
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 ">
            select
            concat(tdd .date_week_year,'-',tdd .date_week) as dateStr,
            count(1) as dayNum
            from
            tf_dim_date tdd
            where
            tdd .format_date >=#{model.startDate} and
            #{model.endDate}>=tdd .format_date
            group by concat(tdd .date_week_year,'-',tdd .date_week)
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 ">
            select
            concat(tdd .date_year,'-',tdd .date_month) as dateStr,
            count(1) as dayNum
            from
            tf_dim_date tdd
            where
            tdd .format_date >=#{model.startDate} and
            #{model.endDate}>=tdd .format_date
            group by concat(tdd .date_year,'-',tdd .date_month)
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 ">
            select
            concat(tdd .date_year,'-',tdd .date_quarter) as dateStr,
            count(1) as dayNum
            from
            tf_dim_date tdd
            where
            tdd .format_date >=#{model.startDate} and
            #{model.endDate}>=tdd .format_date
            group by concat(tdd .date_year,'-',tdd .date_quarter)
        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 ">
            select
            tdd .date_year as dateStr,
            count(1) as dayNum
            from
            tf_dim_date tdd
            where
            tdd .format_date >=#{model.startDate} and
            #{model.endDate}>=tdd .format_date
            group by tdd .date_year
        </if>
        ) dateCycle )
    </sql>




     <sql id="groupby-date-com-all-replace">

            select
            dateCycle.dateStr,
            dateCycle.dayNum,
            datay.*
            from (

            <!--======================日==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==-1 ">
                WITH calendar AS (
                SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY), '%Y-%m-%d')
                AS date
                FROM
                <include refid="ComFilterMapper.date-group-base"/>
                )
                SELECT calendar.date AS dateStr
                FROM calendar
                WHERE calendar.date >= DATE_FORMAT(#{model.startDate}, '%Y-%m-%d')
                AND DATE_FORMAT(#{model.endDate}, '%Y-%m-%d') >=calendar.date
                GROUP BY calendar.date
                ORDER BY calendar.date ASC
            </if>
            <!--======================周度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==0 ">
                WITH calendar AS (
                SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) WEEK), '%Y-%V') AS
                week
                FROM
                <include refid="ComFilterMapper.date-group-base"/>
                )
                SELECT calendar.week AS dateStr
                FROM calendar
                WHERE calendar.week >= concat(DATE_FORMAT( #{model.startDate} , '%Y' ),'-',DATE_FORMAT( #{model.startDate} , '%u' ) + 1)
                AND concat(DATE_FORMAT( #{model.endDate} , '%Y' ),'-',DATE_FORMAT( #{model.endDate} , '%u' ) + 1) >=calendar.week
                GROUP BY calendar.week
                ORDER BY calendar.week ASC
            </if>
            <!--======================月度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==1 ">
                WITH calendar AS (
                SELECT DATE_FORMAT(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH), '%Y-%m') AS month,
                LAST_DAY(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH)) AS lastDayOfMonth
                FROM
                <include refid="ComFilterMapper.date-group-base"/>
                )
                SELECT replace(calendar.month,'-0','-') AS dateStr,
                case calendar.month  when date_format(now(),'%Y-%m') then day(now())
                else DAYOFMONTH(calendar.lastDayOfMonth) end as dayNum
                FROM calendar
                WHERE calendar.month >= DATE_FORMAT(#{model.startDate}, '%Y-%m')
                AND DATE_FORMAT(#{model.endDate}, '%Y-%m')>=calendar.month
                GROUP BY calendar.month
                ORDER BY calendar.month asc
            </if>
            <!--======================季度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==2 ">
                WITH calendar AS (
                SELECT CONCAT(YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH)), '-',
                QUARTER(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) MONTH))) AS quarter
                FROM
                <include refid="ComFilterMapper.date-group-base"/>
                )
                SELECT calendar.quarter AS dateStr
                FROM calendar
                WHERE calendar.quarter >= CONCAT(YEAR(#{model.startDate}), '-', QUARTER(#{model.startDate}))
                AND CONCAT(YEAR(#{model.endDate}), '-', QUARTER(#{model.endDate}))>=calendar.quarter
                GROUP BY calendar.quarter
                ORDER BY calendar.quarter ASC

            </if>
            <!--======================年度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==3 ">
                WITH calendar AS (
                SELECT YEAR(DATE_ADD(#{model.startDate}, INTERVAL (a.a + (10 * b.a) + (100 * c.a)) YEAR)) AS year
                FROM
                <include refid="ComFilterMapper.date-group-base"/>
                )
                SELECT calendar.year AS dateStr
                FROM calendar
                WHERE calendar.year >= YEAR(#{model.startDate})
                AND YEAR(#{model.endDate}) >=calendar.year
                GROUP BY calendar.year
                ORDER BY calendar.year ASC
            </if>
            ) dateCycle  left  join
        </sql>


    <sql id="groupby-com-cycle-table">
                <!--======================日==================-->
                <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
                    date_format(vid.PUBLISH_DATE,'%Y-%m-%d')
                </if>
            <!--======================周度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==0 " >
               concat(tdd.DATE_WEEK_YEAR,'-',tdd.DATE_WEEK)
         </if>
         <!--======================月度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==1 " >
                concat(tdd.DATE_YEAR,'-',tdd.DATE_MONTH)
            </if>
            <!--======================季度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==2 " >
                concat( tdd.DATE_YEAR,'-',tdd.DATE_QUARTER)
            </if>
            <!--======================年度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==3 " >
                date_format(vid.PUBLISH_DATE,'%Y')
            </if>

        </sql>

    <sql id="dayGroupCompletion">
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT TO_CHAR(SUBSTR(#{model.startDate}, 1, 10), 'YYYY-MM-DD') + ROWNUM - 1,'YYYY-MM-DD') AS dateStr
            FROM dual
            CONNECT BY (SUBSTR(#{model.endDate}, 1, 10), 'YYYY-MM-DD')+1 - SUBSTR(#{model.startDate}, 1, 10), 'YYYY-MM-DD'))>=ROWNUM
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
            SELECT
            tdd.date_week_year || '-'  || tdd.DATE_WEEK  as dateStr
            FROM
            TF_DIM_DATE tdd
            WHERE
            tdd.FORMAT_DATE>=#{model.startDate}
            AND #{model.endDate}>=tdd.FORMAT_DATE
            GROUP BY tdd.date_week_year || '-'  || tdd.DATE_WEEK
            ORDER BY dateStr asc
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            to_char(vid.PUBLISH_DATE,'YYYY-MM')
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
            to_char(vid.PUBLISH_DATE,'YYYY-q')
        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 " >
            to_char(vid.PUBLISH_DATE,'YYYY')
        </if>


         </sql>

    <sql id="groupby-com-cycle-sr">
        with common_date_tools as (
        WITH date_range AS (
        SELECT
        DATE(#{model.endDate}) AS endDate,
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            #{model.numCycles} AS dateNum,
            #{model.dateUnit} as date_unit
            ),
            num_sequence AS (
            SELECT ROW_NUMBER() OVER() - 1 AS num
            FROM ( SELECT 1 FROM ( SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 ) t1 CROSS JOIN ( SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1) t2
            ) t
            ),
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            CASE
            WHEN DATE(#{model.startDate}) = DATE(#{model.endDate}) THEN
            DATE_SUB(DATE(#{model.startDate}), INTERVAL 6 DAY)
            ELSE
            DATE(#{model.startDate})
            END AS startDate,
            CASE
            WHEN DATE(#{model.startDate}) = DATE(#{model.endDate}) THEN
            DATEDIFF(DATE(#{model.endDate}), DATE_SUB(DATE(#{model.startDate}), INTERVAL 6 DAY))+2
            ELSE
            DATEDIFF(DATE(#{model.endDate}), DATE(#{model.startDate})) + 2
            END AS dateNum,
            #{model.dateUnit} as date_unit
            ),
        </if>

        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            days AS (
            SELECT
            DATE_FORMAT(dt, '%Y-%m-%d') AS date_,
            DATE_FORMAT(dt, '%Y-%m-%d') AS start_date,
            DATE_FORMAT(dt, '%Y-%m-%d') AS end_date
            FROM (
            SELECT startDate + INTERVAL num DAY as dt
            FROM date_range
            JOIN (
            SELECT a.N + b.N * 10 + c.N * 100 AS num
            FROM (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a
            CROSS JOIN (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b
            CROSS JOIN (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
            ) numbers
            WHERE  (SELECT dateNum FROM date_range)>num
            ) dates
            )
            SELECT
            date_ AS date_,
            dr.endDate as endDate_,
            dr.dateNum,
            dr.date_unit,
            start_date as startDate,
            end_date as endDate,
            1 AS days
            FROM days d, date_range dr
            WHERE dr.endDate>=date_
            AND date_ >= dr.startDate
            ORDER BY date_ ASC
        </if>
        <include refid="ComFilterMapper.week_month_quarter_year"/>
        )
    </sql>
    <sql id="groupby-com-cycle-sr-range">
        with common_date_tools as (
        WITH date_range AS (
        SELECT
        DATE(#{model.endDate}) AS endDate,
        <if test="model.dateUnit !=null and model.dateUnit !=-1 ">
            #{model.numCycles} AS dateNum,
            #{model.dateUnit} as date_unit
            ),
            num_sequence AS (
            SELECT ROW_NUMBER() OVER() - 1 AS num
            FROM ( SELECT 1 FROM ( SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1
            UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 ) t1 CROSS JOIN (
            SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION
            ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1) t2
            ) t
            ),
        </if>

        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            DATE(#{model.startDate}) AS startDate,
            DATEDIFF(DATE(#{model.endDate}), DATE(#{model.startDate})) + 1 AS dateNum
            UNION ALL
            SELECT
            DATE_SUB(DATE(#{model.startDate}), INTERVAL 1 DAY) AS endDate,
            DATE_SUB(DATE(#{model.startDate}),
            INTERVAL DATEDIFF(DATE(#{model.endDate}), DATE(#{model.startDate}))+1 DAY) AS startDate,
            DATEDIFF(DATE(#{model.endDate}), DATE(#{model.startDate})) + 1 AS dateNum
            )
            SELECT
            DATE_FORMAT(endDate, '%Y%m%d') as date_,
            endDate as endDate_,
            dateNum,
            -1 as date_unit,
            startDate,
            endDate,
            1 as days
            FROM date_range

        </if>
        <include refid="ComFilterMapper.week_month_quarter_year"/>
        )
    </sql>
    <!-- ====================== 工单 ====================   -->
    <sql id="wo-groupby-com-cycle-sr-range">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range"/>,
        <include refid="publicDateFilterCriteria.wo-original-data-base">
            <property name="isTrend" value="true"/>
        </include>
    </sql>

    <!-- ====================== 工单趋势 ====================   -->
    <sql id="wo-groupby-com-cycle-sr">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr"/>,
        <include refid="publicDateFilterCriteria.wo-original-data-base">
            <property name="isTrend" value="true"/>
        </include>
    </sql>

    <sql id = "wo-original-data-base" >
        wo_original_data AS (
            SELECT * from (
                SELECT
                    vid.*,
                    vid.tag_code_random AS topic_code,
                    LEFT(vid.tag_code_random, 5) AS first_dimension_code,
                    LEFT(vid.tag_code_random, 8) AS second_dimension_code,
                    LEFT(vid.tag_code_random, 11) AS three_dimension_code
                FROM
                t317_csv_rescue_wo_i_d vid
                WHERE 1 = 1
                    <include refid="publicDateFilterCriteria.wo_original_query_base"/>
            ) vid
            where 1 = 1
            <include refid="publicDateFilterCriteria.wo_original_lable_query"/>
        )
    </sql>

    <sql id="wo-original-data">
        WITH
        <include refid="publicDateFilterCriteria.wo-original-data-base">
            <property name="isTrend" value="false"/>
        </include>
    </sql>

    <!-- 意图分析百分比计算（提及数）SQL片段 -->
    <sql id="intentionPercentageMention">
        round((CASE WHEN (consult + complaint + praise+suggest+other) = 0 THEN 0
            WHEN consult = GREATEST(consult , complaint , praise,suggest,other) THEN
            100 - ROUND((complaint / (consult + complaint + praise+suggest+other)) * 100, 2) - ROUND((praise/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((suggest/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((other/(consult + complaint + praise+suggest+other)) * 100, 2)
            ELSE ROUND((consult / (consult + complaint + praise+suggest+other)) * 100, 2) END),2) as consultP,

        round((CASE WHEN (consult + complaint + praise+suggest+other) = 0 THEN 0
        WHEN complaint = GREATEST(consult , complaint , praise,suggest,other) THEN
        100 - ROUND((consult / (consult + complaint + praise+suggest+other)) * 100, 2) - ROUND((praise/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((suggest/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((other/(consult + complaint + praise+suggest+other)) * 100, 2)
        ELSE ROUND((complaint / (consult + complaint + praise+suggest+other)) * 100, 2) END),2) as complaintP,

        round((CASE WHEN (consult + complaint + praise+suggest+other) = 0 THEN 0
        WHEN praise = GREATEST(consult , complaint , praise,suggest,other) THEN
        100 - ROUND((consult / (consult + complaint + praise+suggest+other)) * 100, 2) - ROUND((complaint/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((suggest/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((other/(consult + complaint + praise+suggest+other)) * 100, 2)
        ELSE ROUND((praise / (consult + complaint + praise+suggest+other)) * 100, 2) END),2) as praiseP,

        round((CASE WHEN (consult + complaint + praise+suggest+other) = 0 THEN 0
        WHEN suggest = GREATEST(consult , complaint , praise,suggest,other) THEN
        100 - ROUND((consult / (consult + complaint + praise+suggest+other)) * 100, 2) - ROUND((complaint/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((praise/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((other/(consult + complaint + praise+suggest+other)) * 100, 2)
        ELSE ROUND((suggest / (consult + complaint + praise+suggest+other)) * 100, 2) END),2) as suggestP,

        round((CASE WHEN (consult + complaint + praise+suggest+other) = 0 THEN 0
        WHEN other = GREATEST(consult , complaint , praise,suggest,other) THEN
        100 - ROUND((consult / (consult + complaint + praise+suggest+other)) * 100, 2) - ROUND((complaint/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((praise/(consult + complaint + praise+suggest+other)) * 100, 2)- ROUND((suggest/(consult + complaint + praise+suggest+other)) * 100, 2)
        ELSE ROUND((other / (consult + complaint + praise+suggest+other)) * 100, 2) END),2) as otherP
    </sql>


    <!-- 通用意图提及量与用户计算sql 片段 -->
    <sql id="commonIntentionUserCountQuery">
        <!-- 根据数据类型选择统计方式 -->
        <if test="model.dataType !=null and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers">
            count(distinct (CASE INTENTION_TYPE WHEN '咨询' THEN one_id ELSE NULL END)) AS consult_c,
            count(distinct (CASE INTENTION_TYPE WHEN '投诉' THEN one_id ELSE NULL END)) AS complaint_c,
            count(distinct (CASE INTENTION_TYPE WHEN '表扬' THEN one_id ELSE NULL END)) AS praise_c,
            count(distinct (CASE INTENTION_TYPE WHEN '建议' THEN one_id ELSE NULL END)) AS suggest_c,
            count(distinct (CASE INTENTION_TYPE WHEN '其他' THEN one_id ELSE NULL END)) AS other_c,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention">
            sum(CASE WHEN intention_type = '咨询' THEN 1 ELSE 0 END) as consult_c,
            sum(CASE WHEN intention_type = '投诉' THEN 1 ELSE 0 END) as complaint_c,
            sum(CASE WHEN intention_type = '表扬' THEN 1 ELSE 0 END) as praise_c,
            sum(CASE WHEN intention_type = '建议' THEN 1 ELSE 0 END) as suggest_c,
            sum(CASE WHEN intention_type = '其他' THEN 1 ELSE 0 END) as other_c,
        </if>
    </sql>



    <!-- 通用情感提及量与用户计算sql 片段 -->
    <sql id="commonEmotionUserCountQuery">
        <!-- 根据数据类型选择统计方式 -->
        <if test="model.dataType !=null and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers">
            count(distinct (CASE dimension_emotion WHEN '正面' THEN one_id ELSE NULL END)) AS positive_c,
            count(distinct (CASE dimension_emotion WHEN '负面' THEN one_id ELSE NULL END)) AS negative_c,
            count(distinct (CASE dimension_emotion WHEN '中性' THEN one_id ELSE NULL END)) AS neutral_c,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention">
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c,
            sum(CASE WHEN dimension_emotion = '中性' THEN 1 ELSE 0 END) as neutral_c,
        </if>
    </sql>

    <!-- 情感分析百分比计算(提及数) -->
    <sql id="emotionPercentageMention">
        round(CASE WHEN (positive + negative + neutral) = 0 THEN 0
                 WHEN positive = GREATEST(positive, negative, neutral) THEN
                 100 - ROUND((negative / (positive + negative + neutral)) * 100, 2)
                 - ROUND((neutral / (positive + negative + neutral)) * 100, 2)
                 ELSE ROUND((positive / (positive + negative + neutral)) * 100, 2) END,2) as positiveP,
        round(CASE WHEN (positive + negative + neutral) = 0 THEN 0
            WHEN negative = GREATEST(positive, negative, neutral) THEN
                100 - ROUND((positive / (positive + negative + neutral)) * 100, 2)
                    - ROUND((neutral / (positive + negative + neutral)) * 100, 2)
            ELSE ROUND((negative / (positive + negative + neutral)) * 100, 2) END,2) as negativeP,
        round(CASE WHEN (positive + negative + neutral) = 0 THEN 0
            WHEN neutral = GREATEST(positive, negative, neutral) THEN
                100 - ROUND((positive / (positive + negative + neutral)) * 100, 2)
                    - ROUND((negative / (positive + negative + neutral)) * 100, 2)
            ELSE ROUND((neutral / (positive + negative + neutral)) * 100, 2) END,2) as neutralP
    </sql>


    <!-- 情感分析百分比计算(用户数) -->
    <sql id="emotionPercentageUsers">
        round((positive/userCount)*100,2) as positiveP,
        round((negative/userCount)*100,2) as negativeP,
        round((neutral/userCount)*100,2) as neutralP,
    </sql>

    <!-- 基础环比指标: 计算各项指标的环比数据 -->
    <sql id="baseChainRatios">
        ROUND(CASE WHEN voice_r = 0 AND (intention - voice_r) != 0 THEN 999999
                 ELSE IFNULL((intention - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as intentionR,
        ROUND(CASE WHEN oneid_r = 0 AND (userCount - oneid_r) != 0 THEN 999999
            ELSE IFNULL((userCount - oneid_r) / IF(oneid_r = 0, 1, oneid_r) * 100, 0) END, 2) as userCountR,
        ROUND(CASE WHEN voice_avgr = 0 AND (intentionA - voice_avgr) != 0 THEN 999999
            ELSE IFNULL((intentionA - voice_avgr) / IF(voice_avgr = 0, 1, voice_avgr) * 100, 0) END, 2) as intentionAR,
        ROUND(CASE WHEN oneid_avgr = 0 AND (userCountA - oneid_avgr) != 0 THEN 999999
            ELSE IFNULL((userCountA - oneid_avgr) / IF(oneid_avgr = 0, 1, oneid_avgr) * 100, 0) END, 2) as userCountAR
    </sql>

    <sql id="vo-groupby-com-cycle-table">
        <!--======================日==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            date(create_time)
        </if>
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
            CONCAT(LEFT(YEARWEEK(create_time, 3), 4), '-', RIGHT(YEARWEEK(create_time, 3), 2))
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            concat(year(create_time), '-', LPAD(month(create_time), 2, 0))
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
            concat(year(create_time), '-', quarter(create_time))
        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 " >
            concat(year(create_time))
        </if>

    </sql>

    <!-- 日期格式转换-自定义: 根据不同的date_unit转换日期格式 -->
    <sql id="dateFormatCase">
        CASE
            WHEN date_unit = 0 THEN CONCAT(LEFT(YEARWEEK(publish_date, 3), 4), '-', RIGHT(YEARWEEK(publish_date, 3), 2))
            WHEN date_unit = 1 THEN concat(year(publish_date), '-', LPAD(month(publish_date), 2, 0))
            WHEN date_unit = 2 THEN concat(year(publish_date), '-', quarter(publish_date))
            WHEN date_unit = 3 THEN concat(year(publish_date))
        END
    </sql>

    <!-- 日期格式转换-趋势: 根据不同的date_unit转换日期格式 -->
    <sql id="dateFormatCase-Trend">
        CASE
            WHEN date_unit = -1 THEN date(publish_date)
            WHEN date_unit = 0 THEN CONCAT(LEFT(YEARWEEK(publish_date, 3), 4), '-', RIGHT(YEARWEEK(publish_date, 3), 2))
            WHEN date_unit = 1 THEN concat(year(publish_date), '-', LPAD(month(publish_date), 2, 0))
            WHEN date_unit = 2 THEN concat(year(publish_date), '-', quarter(publish_date))
            WHEN date_unit = 3 THEN concat(year(publish_date))
        END
    </sql>

    <!-- 工单 原数据-日期格式转换-自定义: 根据不同的date_unit转换日期格式 -->
    <sql id="wo_create_time_FormatCase">
        CASE
            when date_unit = -1 then DATE_FORMAT(create_time, '%Y%m%d')
            WHEN date_unit = 0 THEN CONCAT(LEFT(YEARWEEK(create_time, 3), 4), '-', RIGHT(YEARWEEK(create_time, 3), 2))
            WHEN date_unit = 1 THEN concat(year(create_time), '-', LPAD(month(create_time), 2, 0))
            WHEN date_unit = 2 THEN concat(year(create_time), '-', quarter(create_time))
            WHEN date_unit = 3 THEN concat(year(create_time))
        END
    </sql>

    <!--工单-原数据 日期格式转换-趋势: 根据不同的date_unit转换日期格式 -->
    <sql id="wo_create_time_FormatCase-Trend">
        CASE
            WHEN date_unit = -1 THEN  date(create_time)
            WHEN date_unit = 0 THEN CONCAT(LEFT(YEARWEEK(create_time, 3), 4), '-', RIGHT(YEARWEEK(create_time, 3), 2))
            WHEN date_unit = 1 THEN concat(year(create_time), '-', LPAD(month(create_time), 2, 0))
            WHEN date_unit = 2 THEN concat(year(create_time), '-', quarter(create_time))
            WHEN date_unit = 3 THEN concat(year(create_time))
        END
    </sql>


    <!-- 通用日期范围查询 -->
    <sql id="commonDateQuery">
        from tf_dwd_voc_sentence vid,
        (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
        )c9
        where publish_date BETWEEN startDate and endDate
    </sql>

    <!-- 工单-原数据-通用日期范围查询 -->
    <sql id="wo_org_commonDateQuery">
        from wo_original_data vid,
        (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
        )c9
        where date(create_time) BETWEEN startDate and endDate
    </sql>
    <!-- 通用日期范围查询可带关联 -->
    <sql id="commonDateQuery_relevancy">
        from
        (
            select date_unit,
                   min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
        )c9,
        tf_dwd_voc_sentence vid
    </sql>


    <!-- 基础指标计算: 包括意图数、用户数及其平均值 -->
    <sql id="baseMetrics">
        ifnull(voice_c, 0) AS intention,
        ifnull(oneid_c, 0) AS userCount,
        round(ifnull(voice_c / cc.days, 0), 2) AS intentionA,
        round(ifnull(oneid_c / cc.days, 0), 2) AS userCountA
    </sql>

    <!-- 业务的下钻查询 -->
    <sql id="queryPop_vid_emotion_sr">

        <if test="model.createDate !=null and model.createDate != '' ">
            and  #{model.createDate}>=vid.create_time
        </if>
        <include refid="ComFilterMapper.tag_type"/>
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.sr_general_query" />
        <include refid="ComFilterMapper.sr_general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_types"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.secondAndthreeLableQuery" />
        <include refid="ComFilterMapper.wo_type" />
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_user_type"/>
        <include refid="ComFilterMapper.wo-dlr" />
    </sql>

<!--===================工单数据查询基础筛选条件=====================================================-->
    <sql id="wo_original_query">
        <include refid="ComFilterMapper.wo_create_query" />
        <include refid="publicDateFilterCriteria.wo_original_query-trend" />
    </sql>

    <sql id="wo_original_query-trend">
        <include refid="ComFilterMapper.wo_type"/>
        <include refid="ComFilterMapper.wo-car-series" />
        <include refid="ComFilterMapper.wo-province" />
        <include refid="ComFilterMapper.wo-dlr" />
        <include refid="ComFilterMapper.wo_flow"/>
        <include refid="ComFilterMapper.wo_type_list"/>
        <include refid="ComFilterMapper.wo_status"/>
        <include refid="ComFilterMapper.wo_customer_type"/>
        <include refid="ComFilterMapper.wo_satisfaction_status" />
        <include refid="ComFilterMapper.wo_rescue_status" />
        <include refid="ComFilterMapper.wo_data_sources" />
        <include refid="ComFilterMapper.wo_time" />
    </sql>
    <!--  工单标签筛选条件  -->
    <sql id="wo_original_lable_query">
        <include refid="ComFilterMapper.wo-lable-query" />
    </sql>
<!--===================工单数据查询基础筛选条件=====================================================-->
    <sql id="wo_original_query_base">
        <if test="${isTrend} != true ">
            <include refid="ComFilterMapper.wo_create_query" />
        </if>
        <if test="${isTrend} == true ">
            <if test="model.endDate !=null and model.endDate != ''">
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s') >= vid.create_time
            </if>
        </if>
        <include refid="publicDateFilterCriteria.wo_original_query-trend" />
    </sql>

<!--===================大区去重=====================================================-->
    <sql id="voc_brand_region_max">
        (select
             distinct brand ,application_type,regional_code ,province_code
         from
             voc_brand_region vbr
        )
    </sql>
<!--===================大小去重=====================================================-->
    <sql id="voc_brand_region_min">
        (select
             distinct brand ,application_type,community_code ,province_code,city_code
         from
             voc_brand_region vbr
        )
    </sql>

</mapper>
