<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocIntentionMapper">

    <select id="mentionGroupByDate" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.IntentionTrendVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) as total,
            count(distinct(CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID ELSE NULL END )) AS consul,
            count(distinct(CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL END )) AS praise,
            count(distinct(CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL END )) AS complaint,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  ">
            sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consul,
            sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
            sum(vid.STATISTIC) AS total,
        </if>
        <choose>
            <when test="model.getDateUnit == 0">
                CONCAT(CONCAT(dm.date_week_year, '-'),dm.DATE_WEEK) as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY dm.date_week_year,dm.DATE_WEEK
            </when>
            <when test="model.getDateUnit == 1">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_MONTH) as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>

                GROUP BY dm.DATE_YEAR,dm.DATE_MONTH
            </when>
            <when test="model.getDateUnit == 2">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_QUARTER) as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY dm.DATE_YEAR,dm.DATE_QUARTER
            </when>
            <when test="model.getDateUnit == 3">
                dm.DATE_YEAR as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY dm.DATE_YEAR
            </when>
            <otherwise>
                vid.PUBLISH_DATE as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY vid.PUBLISH_DATE
            </otherwise>
        </choose>
    </select>
    <select id="emotionGroupByDate" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.DateTrendEmotionVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) as total,
            count(distinct(CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END )) AS positive,
            count(distinct(CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END )) AS negative,
            count(distinct(CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END )) AS neutral,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            sum(vid.STATISTIC) AS total,
        </if>
        <choose>
            <when test="model.getDateUnit == 0">
                CONCAT(CONCAT(dm.date_week_year, '-'),dm.DATE_WEEK) as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY dm.date_week_year,dm.DATE_WEEK
            </when>
            <when test="model.getDateUnit == 1">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_MONTH) as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>

                GROUP BY dm.DATE_YEAR,dm.DATE_MONTH
            </when>
            <when test="model.getDateUnit == 2">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_QUARTER) as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY dm.DATE_YEAR,dm.DATE_QUARTER
            </when>
            <when test="model.getDateUnit == 3">
                dm.DATE_YEAR as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY dm.DATE_YEAR
            </when>
            <otherwise>
                vid.PUBLISH_DATE as DATE_TIME
                FROM
                TF_DWS_VOC_EMOTION_USER vid
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY vid.PUBLISH_DATE
            </otherwise>
        </choose>
    </select>

    <select id="overviewIntentionRatio" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.IntentionRatioVo">
        select
           f.*
        from(
            SELECT
            <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
                count(distinct(vid.USER_ID)) AS total,
                count(distinct(CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID ELSE NULL END )) AS consult,
                count(distinct(CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL END )) AS praise,
                count(distinct(CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL END )) AS complaint,
                count(distinct(CASE vid.INTENTION_TYPE WHEN '建议' THEN vid.USER_ID ELSE NULL END )) AS suggest,
                count(distinct(CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.USER_ID ELSE NULL END )) AS other
            </if>
            <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
                sum(vid.STATISTIC) AS total,
                sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,
                sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
                sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
                sum( CASE vid.INTENTION_TYPE WHEN '建议' THEN vid.STATISTIC ELSE 0 END ) AS suggest,
                sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END ) AS other
            </if>
            FROM
            TF_DWS_VOC_EMOTION_USER vid
            where 1=1
            <include refid="publicDateFilterCriteria.queryCom_vid"/>
        )f
    </select>


    <select id="overviewVehicleShare" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.ProportionCarSeriesVo">

        SELECT
        car.NAME as carSeriesName,
        car.CODE as carSeriesCode,
        IFNULL(cst.STATISTIC, 0) AS statistic,
        cst.DATE_TIME
        FROM
        VOC_BRAND_PRODUCT_MANAGER car
        LEFT JOIN
        (
        SELECT
        vid.CAR_SERIES_CODE AS carSeriesCode,
        <choose>
            <when test="model.getDateUnit == 0">
                CONCAT(CONCAT(dm.date_week_year, '-'),dm.DATE_WEEK) as DATE_TIME,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
                    COUNT(DISTINCT vid.USER_ID) AS statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
                    SUM(vid.STATISTIC) AS statistic
                </if>

                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY vid.CAR_SERIES_CODE,dm.date_week_year,dm.DATE_WEEK
            </when>
            <when test="model.getDateUnit == 1">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_MONTH) as DATE_TIME,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
                    COUNT(DISTINCT vid.USER_ID) AS statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
                    SUM(vid.STATISTIC) AS statistic
                </if>

                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>

                GROUP BY vid.CAR_SERIES_CODE,dm.DATE_YEAR,dm.DATE_MONTH
            </when>
            <when test="model.getDateUnit == 2">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_QUARTER) as DATE_TIME,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
                    COUNT(DISTINCT vid.USER_ID) AS statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
                    SUM(vid.STATISTIC) AS statistic
                </if>

                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY vid.CAR_SERIES_CODE,dm.DATE_YEAR,dm.DATE_QUARTER
            </when>
            <when test="model.getDateUnit == 3">
                dm.DATE_YEAR as DATE_TIME,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
                    COUNT(DISTINCT vid.USER_ID) AS statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
                    SUM(vid.STATISTIC) AS statistic
                </if>

                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY vid.CAR_SERIES_CODE,dm.DATE_YEAR
            </when>
            <otherwise>
                vid.PUBLISH_DATE as DATE_TIME,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
                    COUNT(DISTINCT vid.USER_ID) AS statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
                    SUM(vid.STATISTIC) AS statistic
                </if>

                FROM
                TF_DWS_VOC_EMOTION_USER vid
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                GROUP BY vid.CAR_SERIES_CODE,vid.PUBLISH_DATE
            </otherwise>
        </choose>

        ) cst ON car.CODE=cst.CARSERIESCODE

        WHERE
        1=1
        AND car.NAME is not NULL
        AND car.DEL_FLAG=0
        AND car.P_ID!='0'
        order by  car.SORT_NO asc
    </select>
    <select id="dwsVocIntentionMapperList" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.ProportionCarSeriesVo">

        SELECT
        car.NAME as carSeriesName,
        car.CODE as carSeriesCode,
        IFNULL(cst.STATISTIC, 0) AS statistic,
        ROUND((IFNULL(cst.STATISTIC, 0)/(
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) AS statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            SUM(vid.STATISTIC) AS statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid where 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>

        ))*100, 2) AS proportion
        FROM
        VOC_BRAND_PRODUCT_MANAGER car
        LEFT JOIN
        (
        SELECT
        vid.CAR_SERIES_CODE AS carSeriesCode,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) AS statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            SUM(vid.STATISTIC) AS statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid where 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>

        GROUP BY vid.CAR_SERIES_CODE
        ) cst ON car.CODE=cst.CARSERIESCODE
        WHERE
        1=1
        AND car.NAME is not NULL
        AND car.DEL_FLAG=0
        AND car.P_ID!='0'
        ORDER BY car.SORT_NO
    </select>


    <select id="carGroupIntentionsProportion" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarGroupIntentionsProportionVo">
        SELECT
        vid.CAR_GROUP AS carGroupId,
        carg.NAME AS carGroupStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID ELSE NULL END)) AS consult,
            ROUND((count( distinct (CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID)*100),2) AS consultP,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL END)) AS complaint,
            ROUND((count( distinct (CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID)*100),2) AS complaintP,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL END)) AS praise,
            ROUND((count( distinct (CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID)*100),2) AS praiseP,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.USER_ID ELSE NULL END)) AS other,
            ROUND((count( distinct (CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID) *100), 2) AS otherP,
            COUNT(DISTINCT vid.USER_ID) AS intentionsTotal
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2) AS
            consultP,

            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2) AS
            complaintP,

            sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2) AS
            praiseP,

            sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END ) AS other,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100), 2) AS
            otherP,

            SUM(vid.STATISTIC) AS intentionsTotal
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN VOC_MODEL_GROUP carg ON vid.CAR_GROUP=carg.ID
        where
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.carGroups != null and model.carGroups.size()>0">
            and vid.CAR_GROUP in
            <foreach item="item" collection="model.carGroups" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.CAR_GROUP,carg.NAME

    </select>


    <select id="energyClassificationIntentions" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarGroupIntentionsProportionVo">
        SELECT
        carg.ENERGY_TYPE AS carGroupStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID ELSE NULL END)) AS consult,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL END)) AS complaint,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL END)) AS praise,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.USER_ID ELSE NULL END)) AS other
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2) AS
            consultP,

            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2) AS
            complaintP,

            sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2) AS
            praiseP,

            sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END ) AS other,
            ROUND((sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100), 2) AS
            otherP,

            SUM(vid.STATISTIC) AS intentionsTotal
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN VOC_BRAND_PRODUCT_MANAGER carg ON vid.CAR_SERIES_CODE=carg.CODE
        where
        1=1
        and carg.ENERGY_TYPE is not null
        <include refid="publicDateFilterCriteria.queryCom_vid"/>

        GROUP BY carg.ENERGY_TYPE

    </select>
    <select id="carSeriesDistributionIntention" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarIntentionVo">
        select
        t.*
        from
        (

        SELECT
        vid.car_series_code AS carSeries,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID ELSE NULL END)) AS consult,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL END)) AS complaint,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL END)) AS praise,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '建议' THEN vid.USER_ID ELSE NULL END)) AS suggest,
            count( distinct (CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.USER_ID ELSE NULL END)) AS other,
            count(distinct vid.USER_ID) AS total

        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,

            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,

            sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,

            sum( CASE vid.INTENTION_TYPE WHEN '建议' THEN vid.STATISTIC ELSE 0 END ) AS suggest,

            sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END ) AS other,

            SUM(vid.STATISTIC) AS total
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where
        1=1
        and vid.car_series_code  is not null
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        GROUP BY vid.car_series_code
        order by  case when carSeries like '%A00' then 1 else 0 end , total desc
        ) t
        WHERE
        1=1
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 10
        </if>
    </select>

    <select id="carSeriesDistributionEmotion" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarEmotionVo">
       select
        *
        from
        (
        SELECT
        vid.car_series_code AS carSeries,car.show_img,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.dimension_emotion WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            count( distinct (CASE vid.dimension_emotion WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            count( distinct (CASE vid.dimension_emotion WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral,
            count(distinct vid.USER_ID) AS total

        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.dimension_emotion WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.dimension_emotion WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.dimension_emotion WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            sum(vid.STATISTIC) AS total
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join voc_brand_product_manager car on  vid.car_series_code=car.code
        where
        1=1
        and vid.car_series_code is not null
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        GROUP BY vid.car_series_code,car.show_img
        order by  case when carSeries like '%A00' then 1 else 0 end , total desc
        ) t
        WHERE
        1=1
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
<!--        <if test="model.rownum ==null ">-->
<!--            limit 10-->
<!--        </if>-->

    </select>

    <select id="carGroupEmotionProportion" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarGroupEmotionProportionVo">
        SELECT
        vid.CAR_GROUP AS carGroupId,
        carg.NAME AS carGroupStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            ROUND((count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID)*100),2) AS positiveP,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            ROUND((count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID)*100),2) AS negativeP,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral,
            ROUND((count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END))/COUNT(DISTINCT
            vid.USER_ID)*100),2) AS neutralP,
            COUNT(DISTINCT vid.USER_ID) AS emotionTotal
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            ROUND((sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2)
            AS positiveP,

            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            ROUND((sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2)
            AS negativeP,

            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            ROUND((sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2)
            AS neutralP,
            SUM(vid.STATISTIC) AS emotionTotal
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN VOC_MODEL_GROUP carg ON vid.CAR_GROUP=carg.ID
        where 1=1

        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.carGroups != null and model.carGroups.size()>0">
            and vid.CAR_GROUP in
            <foreach item="item" collection="model.carGroups" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.CAR_GROUP,carg.NAME

    </select>

    <select id="energyClassification" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarGroupEmotionProportionVo">
        SELECT
        carg.ENERGY_TYPE AS carGroupStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,

            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,

            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral

        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            ROUND((sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2)
            AS positiveP,

            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            ROUND((sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2)
            AS negativeP,

            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            ROUND((sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END )/SUM(vid.STATISTIC)*100),2)
            AS neutralP,
            SUM(vid.STATISTIC) AS emotionTotal
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN VOC_BRAND_PRODUCT_MANAGER carg ON vid.CAR_SERIES_CODE=carg.CODE
        where 1=1
        and carg.ENERGY_TYPE is not null
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        GROUP BY carg.ENERGY_TYPE

    </select>


    <select id="overviewCarTypeRato" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.CarTypeProportionVo">
        SELECT
        sdi.ITEM_VALUE as carType,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) statistic
        </if>

        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            SUM(vid.STATISTIC) AS statistic
        </if>

        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join SYS_DICT_ITEM sdi on vid.CAR_TYPE=sdi.ITEM_TEXT

        WHERE
        1=1
        and vid.CAR_TYPE is not null
        and sdi.DICT_ID='1380781205222674434'
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        GROUP BY sdi.ITEM_VALUE
        order by statistic desc
    </select>

    <select id="overviewEmotionalProportion" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.TrendEmotionVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral,
            COUNT(DISTINCT vid.USER_ID) AS total
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,

            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,

            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,

            ((sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END )+sum(CASE vid.DIMENSION_EMOTION
            WHEN '正面' THEN vid.STATISTIC ELSE 0 END )+ sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE
            0 END ))) AS total
        </if>

        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
    </select>


    <select id="overviewHighFrequencyWords" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            (count(distinct vid.USER_ID)) AS statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            (sum(vid.STATISTIC)) AS statistic
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.hotWords != null and model.hotWords.size()>0">
            and vid.EMOTION_KEYWORD in
            <foreach item="item" collection="model.hotWords" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.EMOTION_KEYWORD
        ORDER BY STATISTIC DESC
        ) t
        WHERE
        1=1
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>

    <select id="overviewProportionUserTypes" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.UserTypeProportionVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        SUM(vid.STATISTIC) AS statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null
        <if test="model.hotWords != null and model.hotWords.size()>0">
            and vid.EMOTION_KEYWORD in
            <foreach item="item" collection="model.hotWords" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.EMOTION_KEYWORD
        ORDER BY STATISTIC DESC

        )
        where 1=1
            limit 50
    </select>

    <select id="overviewFocusTop" parameterType="com.car.stats.model.FilterCriteriaModel"
            resultType="com.car.stats.vo.LabelEmotionTopVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.SECOND_DIMENSION_CODE AS secondDimensionCode,
        tag2.NAME||'-'||tag1.NAME AS secondDimensionStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN VOC_BUSINESS_TAG tag2 ON vid.SECOND_DIMENSION_CODE=tag2.TAG_CODE
        LEFT JOIN VOC_BUSINESS_TAG tag1 ON tag2.PID=tag1.id
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        and tag2.NAME is NOT NULL
        GROUP BY vid.SECOND_DIMENSION_CODE,tag2.NAME||'-'||tag1.NAME


        ) t
        WHERE
        1=1
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 20
        </if>
    </select>

    <select id="overviewFocusProportionEmotion" parameterType="com.car.stats.model.FocucLabelDetailModel"
            resultType="com.car.stats.vo.EmotionProportionVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
        </if>

        <choose>
            <when test="model.getDateUnit == 0">
                CONCAT(CONCAT(dm.date_week_year, '-'),dm.DATE_WEEK) as DATE_TIME  FROM
                TF_DWS_VOC_EMOTION_USER vid  JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                <if test="model.secondDimensionCode != null and model.secondDimensionCode !='' ">
                    AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
                </if>
                group by dm.date_week_year,dm.DATE_WEEK
            </when>
            <when test="model.getDateUnit == 1">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_MONTH) as DATE_TIME  FROM
                TF_DWS_VOC_EMOTION_USER vid  JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                <if test="model.secondDimensionCode != null and model.secondDimensionCode !='' ">
                    AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
                </if>
                group by dm.DATE_YEAR,dm.DATE_MONTH
            </when>
            <when test="model.getDateUnit == 2">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_QUARTER) as DATE_TIME  FROM
                TF_DWS_VOC_EMOTION_USER vid  JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                <if test="model.secondDimensionCode != null and model.secondDimensionCode !='' ">
                    AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
                </if>
                group by dm.DATE_YEAR,dm.DATE_QUARTER
            </when>
            <when test="model.getDateUnit == 3">
                dm.DATE_YEAR as DATE_TIME  FROM
                TF_DWS_VOC_EMOTION_USER vid  JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                <if test="model.secondDimensionCode != null and model.secondDimensionCode !='' ">
                    AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
                </if>
                group by dm.DATE_YEAR
            </when>
            <otherwise>
                vid.PUBLISH_DATE as DATE_TIME  FROM
                TF_DWS_VOC_EMOTION_USER vid
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryCom_vid"/>
                <if test="model.secondDimensionCode != null and model.secondDimensionCode !='' ">
                    AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
                </if>
                group by vid.PUBLISH_DATE
            </otherwise>
        </choose>

    </select>
    <select id="overviewEmotionalTrends" parameterType="com.car.stats.model.FocucLabelDetailModel"
            resultType="com.car.stats.vo.DateTrendEmotionVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL END)) AS positive,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END)) AS negative,
            count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END)) AS neutral,
            (count( distinct (CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL END))+count( distinct (CASE
            vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL END))+count( distinct (CASE vid.DIMENSION_EMOTION
            WHEN '正面' THEN vid.USER_ID ELSE NULL END))) AS total
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            ((sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END )+sum(CASE vid.DIMENSION_EMOTION
            WHEN '正面' THEN vid.STATISTIC ELSE 0 END )+ sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE
            0 END ))) AS total
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.secondDimensionCode != null">
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
    </select>


    <select id="threeLabelsTop" resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.THREE_DIMENSION_CODE AS labelCode,vid.DIMENSION_EMOTION as emotion_type,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) AS statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            SUM(vid.STATISTIC) AS statistic
        </if>

        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.secondDimensionCode != null and model.secondDimensionCode != '' ">
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>


        GROUP BY vid.THREE_DIMENSION_CODE,vid.DIMENSION_EMOTION
        ORDER BY statistic DESC


    </select>


    <select id="fourLabelsTop" resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.TOPIC_CODE AS labelCode,vid.TOPIC_CODE,vid.DIMENSION_EMOTION as emotion_type,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) AS statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            SUM(vid.STATISTIC) AS statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.secondDimensionCode != null">
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        GROUP BY vid.TOPIC_CODE,vid.DIMENSION_EMOTION
        ORDER BY statistic DESC


    </select>


    <sql id="purposeTjlC">
        <if test="reportVo.purpose != null">
            AND vid.INTENTION_TYPE = #{reportVo.purpose}
        </if>
    </sql>

</mapper>
