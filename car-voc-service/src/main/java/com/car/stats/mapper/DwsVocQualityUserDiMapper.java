package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.DwsVocQualityUserDi;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.stats.vo.risk.*;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.vo.ProportionAreaVo;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocUserDiServiceMapper.java
 * @Description TODO
 * @createTime 2022年10月19日 13:55
 * @Copyright voc
 */
public interface DwsVocQualityUserDiMapper extends BaseMapper<DwsVocQualityUserDi> {
    Map<String, Object> queryUserNumQuality(@Param("model") ProductQualityFilterCriteriaModel model);
    BigDecimal userNumQuality(@Param("model") ProductQualityFilterCriteriaModel model);

    HomePurposeTrendVo intentionNumAndUserNum(@Param("model") FilterCriteriaModel model);


    BigDecimal userLevelNum(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> overviewRegionalDistribution(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> regionalUser(@Param("model") FilterCriteriaModel model);

    List<DateUserStatisticVo> qualityProblemTrend(@Param("model") FilterCriteriaModel model);

    DateUserStatisticVo overviewProblemTrends(@Param("model") FilterCriteriaModel model);


    List<TopVoiceUsersVo> topVoiceUsers(@Param("model") FilterCriteriaModel model);

    List<HomePurposeTrendVo> intendedUserTrends(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> productRegionalDistribution(@Param("model")  ProductQualityFilterCriteriaModel model);
    List<RegionUserVo> provinceMap(@Param("model")  ProductQualityFilterCriteriaModel model);
    BigDecimal userQulitityCount(@Param("model")  ProductQualityFilterCriteriaModel model);
    List<TopQualityUsersVo> topQualityUsers(@Param("model")  ProductQualityFilterCriteriaModel model);

    List<RegionUserVo> productRegionalUser(@Param("model") ProductQualityFilterCriteriaModel model);

    List<UserLabelVo> thirdTagDistributionByTopic(@Param("model") LabelDetailFilterModel model,@Param("topicName") String topicName);

    HomePurposeTrendVo trendChangeLabel(@Param("model") LabelDetailFilterModel model);
    List<HomePurposeTrendVo> repairTrendChangeLabel(@Param("model") LabelDetailFilterModel model);

    Map<String, Object> userAndStatistic(@Param("model")  LabelDetailFilterModel model);

    Page<UserListInfoVo> getUserList(@Param("model") LabelDetailFilterModel model, Page<UserListInfoVo> page);

    List<UserLabelVo> thirdDimensionCodeBySecond(@Param("model") LabelDetailFilterModel model);
    List<UserLabelVo> secondDimensionCodeByFirst(@Param("model") LabelDetailFilterModel model);
    List<UserLabelVo> fistDimensionCode(@Param("model") LabelDetailFilterModel model);

    List<UserLabelVo> topicTagByThird(@Param("model")  LabelDetailFilterModel model);

    Map<String, Object> userAndStatisticGenerality(@Param("model") LabelDetailFilterModel model);

    HomePurposeTrendVo trendChangeLabelGenerality(@Param("model") LabelDetailFilterModel model);
    List<HomePurposeTrendVo> repairTrendChangeLabelGenerality(@Param("model") LabelDetailFilterModel model);

    List<HomePurposeTrendVo> intentionNumAndUserNumList(@Param("model") FilterCriteriaModel model);

    VocOverBriefingValueVo riskBriefingValue(@Param("model") RiskEventInsightModel model);

    BigDecimal riskStatisticTotal(@Param("model") RiskEventInsightModel model);

    Map<String, Object> riskUserNum(@Param("model") RiskEventInsightModel model);

    BigDecimal riskUserTotalNum(@Param("model") RiskEventInsightModel model);

    List<String> riskCarSeries(@Param("model")  RiskEventInsightModel model);
    List<String> riskCarSeriesExport(@Param("model")  RiskEventInsightModel model);
    Long riskWarningUserNumExport(@Param("model")  RiskEventInsightModel model);

    List<HighHotWordsVo > riskHotWordsOpinion(@Param("model")   RiskEventInsightModel model);

    IntentionEmotionTrendVo emotionIntentionTrend(@Param("model")  RiskEventInsightModel model1);
    HomePurposeTrendVo intentionTrend(@Param("model")  RiskEventInsightModel model1);

    List<HighHotWordsVo> riskHotWords(@Param("model") RiskEventInsightModel model);

    VoiceUserVo riskVoiceUserTrend(@Param("model") RiskEventInsightModel model);

    List<VoiceUserTopVo> voiceUserTop(@Param("model") RiskEventInsightModel model);
    List<VoiceUserTopVo> voiceUserTop1(@Param("model") RiskEventInsightModel model);

    List<CdpUserNumVo> cdpUserProportion(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> productRegionUser(@Param("model") ProductQualityFilterCriteriaModel model);
    List<RegionUserVo> focusRegionalTop(@Param("model") ProductQualityFilterCriteriaModel model);
    List<RegionUserVo> focusCommunityTop(@Param("model") ProductQualityFilterCriteriaModel model);

    List<EmotionIntentionVo> qualityEmotionIntention(@Param("model")  RiskAllTypesModel model);

    List<ChannelVo> qualityChangeChannel(@Param("model")  RiskAllTypesModel model);

    List<HighHotWordsVo> qualityHotWords(@Param("model") RiskAllTypesModel model);

    List<HighHotWordsVo> hotWords(@Param("model") FilterCriteriaModel model);

    DataDryingContrastVo dataDryingContrast(@Param("model") RiskAllTypesModel model,@Param("type") String type);

    Long riskWarningUserNum(@Param("model")  RiskEventInsightModel model,@Param("riskModel") DwdVocQualityRisk risk);

    Map<String, BigDecimal> riskTrend(@Param("model")  RiskEventInsightModel model);

    List<HighHotWordsVo> lastTagHotWords(@Param("model")  LabelDetailFilterModel model);

    List<ChannelVo> riskChannelDistribution(@Param("model") FilterCriteriaModel model);

    Map<String, BigDecimal> riskAllTotal(@Param("model") RiskEventInsightModel model);

    List<String> riskCarSeriesStr(@Param("model") RiskEventInsightModel model);
    List<ProportionCarSeriesVo> riskCarSeriesProption(@Param("model") FilterCriteriaModel model,@Param("risk") DwdVocQualityRisk risk);
    List<ProportionAreaVo> riskAreaProption(@Param("model") FilterCriteriaModel model,@Param("risk") DwdVocQualityRisk risk);
    List<ChannelVo> riskDataSourceDistribution(@Param("model") FilterCriteriaModel model,@Param("risk") DwdVocQualityRisk risk);

    List<CarEmotionVo> carSeriesDistribution(@Param("model")  RiskEventInsightModel model);

    List<RegionUserVo> focusRegionalTopRisk(@Param("model") RiskEventInsightModel model);
}
