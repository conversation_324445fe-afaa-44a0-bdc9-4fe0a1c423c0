package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.LargeDigitaFilesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: cuick
 * @创建时间: 2024/6/11 13:23
 * @描述:
 **/
@Mapper
@Repository
public interface LargeDigitaFileMapper extends BaseMapper<LargeDigitaFilesEntity> {

    List<LargeDigitaFilesEntity> getFileList(LargeDigitaFilesEntity entity);

    LargeDigitaFilesEntity getFile(LargeDigitaFilesEntity entity);
}
