<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocWorkorderEmotionUserMapper">

    <select id="workOrderUserType1" resultType="com.car.stats.vo.WorkOrderUserVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.MEDIA_NAME as mediaName,
        COUNT(DISTINCT vid.ONE_ID) AS userNum,
        COUNT(1) AS statistic
        FROM
        TF_DWD_VOC_SENTENCE vid
        WHERE
        1=1
        and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
        and vid.MEDIA_NAME is not null
        <include refid="publicDateFilterCriteria.queryCom_WORKORDER_vid" />
        <if test="model.workOrderType1 != null and model.workOrderType1 != '' ">
            AND vid.MEDIA_FIRST_CATEGORY = #{model.workOrderType1}
        </if>
        <if test="model.mediaNames != null and model.mediaNames.size()>0">
            and vid.MEDIA_NAME in
            <foreach item="item" collection="model.mediaNames" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.MEDIA_NAME
        ORDER BY statistic DESC
        ) t
        WHERE
        1=1
        limit 10

    </select>



    <select id="userAndStatistic"  resultType="java.util.Map">
        SELECT
        COUNT(1) as "intention",
        COUNT(DISTINCT vid.ONE_ID)  as "userCount"
        FROM
        TF_DWD_VOC_SENTENCE vid
        WHERE
        1=1
        AND vid.MEDIA_FIRST_CATEGORY is not null
        AND vid.MEDIA_NAME is not null

        and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_WORKORDER_vid_emotion" />
        <if test="model.mediaName1 != null and model.mediaName1 != '' ">
            <if test=" model.mediaName1 == 'all_media' ">
                AND vid.MEDIA_FIRST_CATEGORY is not null
            </if>
            <if test=" model.mediaName1 != 'all_media' ">
                AND vid.MEDIA_FIRST_CATEGORY = #{model.mediaName1}
            </if>
        </if>
        <if test="model.mediaName2 != null and model.mediaName2 != '' ">
            <if test=" model.mediaName2 == 'all_media' ">
                AND vid.MEDIA_NAME is not null
            </if>
            <if test=" model.mediaName2 != 'all_media' ">
                AND vid.MEDIA_NAME = #{model.mediaName2}
            </if>
        </if>
    </select>


    <select id="trendChangeLabelList"  resultType="com.car.stats.vo.HomePurposeTrendVo">

        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr,
        COUNT( DISTINCT vid.ONE_ID ) AS "userCount",
        COUNT( 1 ) AS "intention"
        FROM
        TF_DWD_VOC_SENTENCE vid
        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryCom_WORKORDER_vid_emotion" />
        AND vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.mediaName1 != null and model.mediaName1 != '' ">
            <if test=" model.mediaName1 == 'all_media' ">
                AND vid.MEDIA_FIRST_CATEGORY is not null
            </if>
            <if test=" model.mediaName1 != 'all_media' ">
                AND vid.MEDIA_FIRST_CATEGORY = #{model.mediaName1}
            </if>
        </if>
        <if test="model.mediaName2 != null and model.mediaName2 != '' ">
            <if test=" model.mediaName2 == 'all_media' ">
                AND vid.MEDIA_NAME is not null
            </if>
            <if test=" model.mediaName2 != 'all_media' ">
                AND vid.MEDIA_NAME = #{model.mediaName2}
            </if>
        </if>
        GROUP BY
                 <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        <if test="model.topicCode != null and model.topicCode != '' ">
            , vid.TOPIC_CODE
        </if>

        ORDER BY dateStr desc

    </select>
        <select id="getUserList"  resultType="com.car.stats.vo.popvo.UserListInfoVo">

                    SELECT
                    T.ONE_ID as userId,
                    T.DISPLAY_NAME as userName,
                    T.IS_ONE_ID  as isOneId,
                    T.USER_TYPE as userType,
                    T.USER_LEVEL as userLevel,
                    count(1) as statistic,
                    sum( CASE T.INTENTION_TYPE WHEN '咨询' THEN 1 ELSE 0 END ) AS consult,
                    sum( CASE T.INTENTION_TYPE WHEN '投诉' THEN 1 ELSE 0 END ) AS complaint,
                    sum( CASE T.INTENTION_TYPE WHEN '表扬' THEN 1 ELSE 0 END ) AS praise,
                    sum( CASE T.DIMENSION_EMOTION WHEN '正面' THEN 1 ELSE 0 END ) AS positive,
                    sum( CASE T.DIMENSION_EMOTION WHEN '负面' THEN 1 ELSE 0 END ) AS negative
                    FROM
                    (
                    SELECT vid.ONE_ID,vid.DISPLAY_NAME,vid.IS_ONE_ID,vid.CHANNEL_ID,vid.INTENTION_TYPE,vid.DIMENSION_EMOTION,vid.USER_TYPE,vid.USER_LEVEL
                    , ROW_NUMBER() OVER (PARTITION BY vid.ONE_ID ORDER BY vid.PUBLISH_DATE DESC) AS RW
                    FROM TF_DWD_VOC_SENTENCE vid
                    WHERE
                    1=1
                    and vid.ONE_ID is not null
                    AND vid.MEDIA_FIRST_CATEGORY is not null
                    and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
                    <if test="model.topicCode !=null and model.topicCode !=''" >
                        AND vid.TOPIC_CODE=#{model.topicCode}
                    </if>
                    <include refid="publicDateFilterCriteria.queryCom_WORKORDER_vid_emotion" />
                    <if test="model.mediaName1 != null and model.mediaName1 != '' ">
                        <if test=" model.mediaName1 == 'all_media' ">
                            AND vid.MEDIA_FIRST_CATEGORY is not null
                        </if>
                        <if test=" model.mediaName1 != 'all_media' ">
                            AND vid.MEDIA_FIRST_CATEGORY = #{model.mediaName1}
                        </if>
                    </if>
                    <if test="model.mediaName2 != null and model.mediaName2 != '' ">
                        <if test=" model.mediaName2 == 'all_media' ">
                            AND vid.MEDIA_NAME is not null
                        </if>
                        <if test=" model.mediaName2 != 'all_media' ">
                            AND vid.MEDIA_NAME = #{model.mediaName2}
                        </if>
                    </if>

                    ) T
                    WHERE
                    T.RW = 1
                    GROUP BY T.ONE_ID,T.DISPLAY_NAME,T.IS_ONE_ID,T.USER_TYPE,T.USER_LEVEL
                    ORDER BY statistic,T.ONE_ID DESC

        </select>

    <select id="trendChangeLabelList1"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        SELECT
        COUNT(1) as "intention",
        COUNT(DISTINCT vid.ONE_ID)  as "userCount"
        FROM
        TF_DWD_VOC_SENTENCE vid
        WHERE
        1=1
        and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_WORKORDER_vid" />
        <if test="model.mediaName1 != null and model.mediaName1 != '' ">
            <if test=" model.mediaName1 == 'all_media' ">
                AND vid.MEDIA_FIRST_CATEGORY is not null
            </if>
            <if test=" model.mediaName1 != 'all_media' ">
                AND vid.MEDIA_FIRST_CATEGORY = #{model.mediaName1}
            </if>
        </if>
        <if test="model.mediaName2 != null and model.mediaName2 != '' ">
            <if test=" model.mediaName2 == 'all_media' ">
                AND vid.MEDIA_NAME is not null
            </if>
            <if test=" model.mediaName2 != 'all_media' ">
                AND vid.MEDIA_NAME = #{model.mediaName2}
            </if>
        </if>
    </select>


    <select id="mediaTheme1" resultType="com.car.stats.vo.LabelUserVo">
        SELECT
            *
        FROM
            (
                SELECT
                lab.*,
                t.NAME as labelStr
                FROM
                (

                        SELECT
                            vid.TOPIC_CODE as labelCode,
                            COUNT(DISTINCT vid.ONE_ID) AS userNum,
                            COUNT(1) AS statistic
                        FROM
                        TF_DWD_VOC_SENTENCE vid
                        WHERE
                            1=1
                        and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
                        and vid.MEDIA_NAME is not null
                     <include refid="publicDateFilterCriteria.queryCom_WORKORDER_vid" />
                        <if test="model.mediaName != null and model.mediaName != '' ">
                            AND vid.MEDIA_NAME = #{model.mediaName}
                        </if>
                        <if test="model.workOrderType1 != null and model.workOrderType1 != '' ">
                            AND vid.MEDIA_FIRST_CATEGORY = #{model.workOrderType1}
                        </if>

                        <if test="model.labelCodes != null and model.labelCodes.size()>0">
                            and vid.TOPIC_CODE in
                            <foreach item="item" collection="model.labelCodes" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                        </if>
                        GROUP BY vid.TOPIC_CODE
                        ORDER BY statistic DESC
                    )   lab
                    LEFT JOIN VOC_BUSINESS_TAG t on t.TAG_CODE=lab.labelCode
                    WHERE
                    1=1
                    and t.NAME is not null
                    ORDER BY statistic DESC
            )
        WHERE
            1=1
          limit 20

    </select>

</mapper>
