package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.DwsVocEmotionUserDi;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.ComFilterCriteriaModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.stats.vo.risk.DataDryingContrastVo;
import com.car.stats.vo.risk.EmotionIntentionVo;
import com.car.voc.model.risk.RiskAllTypesModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocEmotionUserDiMapper.java
 * @Description TODO
 * @createTime 2022年10月20日 15:08
 * @Copyright voc
 */
public interface DwsVocEmotionUserDiMapper extends BaseMapper<DwsVocEmotionUserDi> {
    List<RegionUserVo> regionalDistribution(@Param("model")  FilterCriteriaModel model);
    List<RegionUserVo> focusRegionalTop(@Param("model")  FilterCriteriaModel model);
    List<RegionUserVo> focusCommunityTop(@Param("model")  FilterCriteriaModel model);

    List<RegionUserVo> regionalUser(@Param("model") FilterCriteriaModel model);

    List<TopVoiceUsersVo> topVoiceUsers(@Param("model") FilterCriteriaModel model);

    Map<String, Long> userNum(@Param("model") FilterCriteriaModel model);
    BigDecimal userDistinctNum(@Param("model") FilterCriteriaModel model);

    Map<String, Object> homeBriefingValue(@Param("model") FilterCriteriaModel model);

    Map<String, Object> VipUser(@Param("model") FilterCriteriaModel model);

    Map<String, Object> certifiedOwner(@Param("model") FilterCriteriaModel model);

    VocOverBriefingValueVo overviewBriefingValue(@Param("model")  FilterCriteriaModel model);
    VocOverBriefingValueVo overviewBriefingTotalValue(@Param("model")  FilterCriteriaModel model);

    List<ChannelStatisticVo> sourceChannel(@Param("model") FilterCriteriaModel model);

    List<ChannelUserVo> channelDistribution(@Param("model")  FilterCriteriaModel model);

    List<UserLabelVo> secondTagDistribution(@Param("model")  LabelDetailFilterModel model);

    List<UserLabelVo> thirdTagDistribution(@Param("model")  LabelDetailFilterModel model);

    List<HomePurposeTrendVo> trendChangeLabel(@Param("model") LabelDetailFilterModel model);

    Map<String, String> userAndStatistic(@Param("model")  LabelDetailFilterModel model);

    Page<UserListInfoVo> getUserList(@Param("model") LabelDetailFilterModel model, Page<UserListInfoVo> page);
    Page<UserListInfoVo> getallTagUserList(@Param("model") LabelDetailFilterModel model, Page<UserListInfoVo> page);

    List<UserLabelVo> topicTagDistribution(@Param("model") LabelDetailFilterModel model);

    EmotionProportionVo emotionPropUser(@Param("userId") String userId);

    IntentionRatioVo intentionsUser(@Param("userId") String userId);

    List<ChannelVo> channelDistr(@Param("userId") String userId);

    List<HighHotWordsVo> hotWordsUser(@Param("model") LabelDetailFilterModel model);

    DateUserStatisticVo intentionUserTrends(@Param("model") FilterCriteriaModel model);

    List<String> riskCarSeries(@Param("model") RiskEventInsightModel model);
    List<String> riskCarSeriesExport(@Param("model") RiskEventInsightModel model);

    List<HighHotWordsVo> riskHotWordsOpinion(@Param("model")  RiskEventInsightModel model);

    BigDecimal riskStatisticTotal(@Param("model")  RiskEventInsightModel model);

    VocOverBriefingValueVo riskBriefingValue(@Param("model") RiskEventInsightModel model);

    Map<String, BigDecimal> riskUserNum(@Param("model")  RiskEventInsightModel model);

    BigDecimal riskUserTotalNum(@Param("model") RiskEventInsightModel model);

    List<String> homeChannelStrs(@Param("model") FilterCriteriaModel model);
    HomePurposeTrendVo trendChangeLabelNew(@Param("model") LabelDetailFilterModel model);

    List<HomePurposeTrendVo> trendChangeLabelList(@Param("model") LabelDetailFilterModel model);
    List<HomePurposeTrendVo> trendChangeLabelListNew(@Param("model") LabelDetailFilterModel model);
    List<EmotionProportionVo> positiveNegativeTrend(@Param("model") LabelDetailFilterModel model);
    List<EmotionProportionVo> allPositiveNegativeTrend(@Param("model") LabelDetailFilterModel model);

    List<ChannelUserVo> complaintWebsiteTop(@Param("model") FilterCriteriaModel model);

    List<LabelUserVo> complaintWebsiteThemeTop(@Param("model") FilterCriteriaModel model);

    List<ChannelUserVo> dataSourceDistribution(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> regionUser(@Param("model") FilterCriteriaModel model);

    List<ChannelUserVo> channelDistributionChUser(@Param("model") FilterCriteriaModel model);

    List<ChannelUserVo> dataSourceDistributionChUser(@Param("model") FilterCriteriaModel model);

    List<String> riskCarSeriesByUserId(@Param("model") ComFilterCriteriaModel model );

    Set<String> riskAggProblemByUserId(@Param("model") ComFilterCriteriaModel model);

    List<HighHotWordsVo> riskHotWordsByUserId(@Param("model") ComFilterCriteriaModel model);

    Long riskWarningUserNum(@Param("model") RiskEventInsightModel model, @Param("riskModel")DwdVocRisk riskModel);
    Long riskBranchesWarningUserNum(@Param("model") RiskEventInsightModel model, @Param("riskModel") DwdVocDealerRisk riskModel);

    List<EmotionIntentionVo> eventEmotionIntention(@Param("model") RiskAllTypesModel model);

    List<ChannelVo> eventChangeChannel(@Param("model") RiskAllTypesModel model);

    List<HighHotWordsVo> eventHotWords(@Param("model")  RiskAllTypesModel model);

    DataDryingContrastVo dataDryingContrast(@Param("model")  RiskAllTypesModel model,@Param("type") String type);

    Map riskTrend(@Param("model") RiskEventInsightModel model);

    List<Map> listRiskTrend(@Param("model") RiskEventInsightModel model);

    List<HighHotWordsVo> lastTagHotWords(@Param("model")  LabelDetailFilterModel model);

    @Select("select trace_id from t317_ons_session_info_i_d where session_id = #{sessionId} limit 1 ")
    String getSessionIdByTraceId(@Param("sessionId") String sessionId);


    List<String> getSessionIdByTraceIdList(@Param("sessionIdList") List<String> sessionId);

    @Select("SELECT type,send_user_nick as sendUserNick,message_content_all as messageContentAll,message_send_time as messageSendTime FROM t317_ons_session_info_i_d WHERE session_id=#{sessionId} ")
    List<Map<String,Object>> sessionListUserId(@Param("sessionId") String sessionId);

    List<Map<String,Object>> sessionListUserIdList(@Param("sessionIdList") List<String> sessionId);

    @Select("SELECT type,send_user_nick as sendUserNick,message_content_all as messageContentAll,message_send_time as messageSendTime FROM t317_ons_session_info_i_d WHERE trace_id=#{sessionId} ")
    List<Map<String,Object>> sessionListUserIdNew(@Param("sessionId") String sessionId);

//    @Select("SELECT u.session_id as sessionId, u.business_type as businessType, u.send_user_nick as visitorName, c.send_user_nick as executor, u.brand_name as brandName, u.model_name as modelName, u.level1_question_classification as level1QuestionClassification, u.level2_question_classification as level2QuestionClassification, u.message_content_all as messageContentAll FROM t317_ons_session_info_i_d u LEFT JOIN t317_ons_session_info_i_d c ON u.session_id = c.session_id WHERE u.session_id = #{sessionId} AND c.type = '1'  AND u.type = '0' LIMIT 1")
//    @Select("select uses.*, exeu.send_user_nick  as executor from ( select u.session_id as sessionId, u.business_type as businessType, u.send_user_nick as visitorName, u.brand_name as brandName, u.model_name as modelName, u.level1_question_classification as level1QuestionClassification, u.level2_question_classification as level2QuestionClassification, u.message_content_all as messageContentAll from t317_ons_session_info_i_d u where u.session_id =#{sessionId} AND  u.type < 4 and u.send_user_nick NOT IN ('robot','system') LIMIT 1 ) uses left join t317_ons_session_info_i_d exeu on uses.sessionId=exeu.session_id and exeu .type>=4 AND exeu.send_user_nick NOT IN ('robot','system')  LIMIT 1")
    @Select("select tab1.* from ( SELECT uses.*, exeu.send_user_nick AS executor, exeu.`type`+0 as orderType FROM ( SELECT u.session_id AS sessionId, u.business_type AS businessType, u.send_user_nick AS visitorName, u.brand_name AS brandName, u.model_name AS modelName, u.level1_question_classification AS level1QuestionClassification, u.level2_question_classification AS level2QuestionClassification, u.message_content_all AS messageContentAll FROM t317_ons_session_info_i_d u WHERE u.session_id = #{sessionId} AND u.type ='2' LIMIT 1 ) uses LEFT JOIN t317_ons_session_info_i_d exeu ON uses.sessionId = exeu.session_id AND exeu.type in ('4','16','32') ) tab1 where 1=1 order by orderType asc LIMIT 1;")
    Map<String, Object> getSessionOne(@Param("sessionId") String sessionId);

    List<Map<String,Object>> getSessionOneList(@Param("sessionIdList") List<String> sessionId);

    @Select("select tab1.* from ( SELECT uses.*, exeu.send_user_nick AS executor, exeu.`type`+0 as orderType FROM ( SELECT u.trace_id AS sessionId, u.business_type AS businessType, u.send_user_nick AS visitorName, u.brand_name AS brandName, u.model_name AS modelName, u.level1_question_classification AS level1QuestionClassification, u.level2_question_classification AS level2QuestionClassification, u.message_content_all AS messageContentAll FROM t317_ons_session_info_i_d u WHERE u.trace_id = #{sessionId} AND u.type ='2' LIMIT 1 ) uses LEFT JOIN t317_ons_session_info_i_d exeu ON uses.sessionId = exeu.trace_id AND exeu.type in ('4','16','32') ) tab1 where 1=1 order by orderType asc LIMIT 1;")
    Map<String, Object> getSessionOneNew(@Param("sessionId") String sessionId);

    Map<String, BigDecimal> riskAllTotal(@Param("model") RiskEventInsightModel model);

    List<String> riskCarSeriesStr(@Param("model") RiskEventInsightModel model);

    BigDecimal userStatisticTotal(@Param("model") ComFilterCriteriaModel model);

    List<RegionUserVo> provinceMap(@Param("model") FilterCriteriaModel model);
    List<IntentionTrendVo> eventUserStatictis(@Param("model") FilterCriteriaModel model);

}
