<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocIntentionDiMapper">



    <select id="queryUserNum" resultType="java.util.Map">
        SELECT
        sum(tabThis.thisUserNum) as thisUserNum,
        sum(tabUp.upUserNum) as thisUserNumUp
        FROM
        (SELECT
        COUNT(DISTINCT vid.USER_ID) as thisUserNum
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        ) tabThis,
        (SELECT
        COUNT(DISTINCT vid.USER_ID) AS upUserNum
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
        ) tabUp

    </select>

    <select id="dataSourceDistribution" resultType="com.car.stats.vo.ChannelStatisticVo">
        select
        sour.*
        from
            (SELECT
        vid.DATA_SOURCE as dataSource,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        and
        (vid.DATA_SOURCE !='1372001238165561345')
        and vid.DATA_SOURCE!='1372001238165593852'
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.DATA_SOURCE
        ORDER BY statistic desc) sour
        where 1=1 limit 10

    </select>



    <select id="purposeCb" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="java.util.Map">
        select
        sum(ta.表扬) 表扬,
        sum(ta.投诉) 投诉,
        sum(ta.其他) 其他,
        sum(ta.咨询) 咨询,
        sum(ta.总数) 总数
        from
        (
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as 总数,
            count(distinct(CASE  vid.INTENTION_TYPE WHEN '咨询' THEN vid.USER_ID else null  END )) AS 咨询,
            count(distinct(CASE  vid.INTENTION_TYPE WHEN '表扬' THEN vid.USER_ID ELSE NULL  END )) AS 表扬,
            count(distinct(CASE  vid.INTENTION_TYPE WHEN '其他' THEN vid.USER_ID ELSE NULL  END )) AS 其他,
            count(distinct(CASE  vid.INTENTION_TYPE WHEN '投诉' THEN vid.USER_ID ELSE NULL  END )) AS 投诉
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as 总数,
            sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS 咨询,
            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS 投诉,
            sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS 表扬,
            sum( CASE vid.INTENTION_TYPE WHEN '其他' THEN vid.STATISTIC ELSE 0 END ) as 其他
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
            ) ta
    </select>

    <select id="homeProportionEmotion" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.EmotionProportionVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as total,
            count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
            count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative,
            count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL  END )) AS neutral
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            sum(vid.STATISTIC) as total
        </if>

        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
    </select>

    <select id="purposeTjl"   resultType="java.util.Map">
        SELECT
        tabThis.thisMent AS totalMentions,
        tabUp.upMent as totalMentionsUp
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where  1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as upMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
            ) tabUp

    </select>


    <select id="homeChannelTrends" resultType="com.car.stats.vo.ChannelStatisticVo">
        SELECT
        sour.*
        FROM
        (
        SELECT
        vid.CHANNEL_ID as dataSource,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY vid.CHANNEL_ID
        ) sour
    </select>

    <select id="homeChannelTrends1" resultType="com.car.stats.vo.ChannelStatisticVo">
        SELECT
        vid.CHANNEL_ID as dataSource,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            COUNT(DISTINCT vid.USER_ID) as statistic,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   ">
            SUM(vid.STATISTIC) as statistic,
        </if>
        <choose>
            <when test="model.getDateUnit == 0">
                CONCAT(CONCAT(dm.date_week_year, '-'),dm.DATE_WEEK) as dateStr
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid_notDatasource"/>
                <if test="model.channelList != null and model.channelList.size()>0">
                    and vid.CHANNEL_ID in
                    <foreach item="item" collection="model.channelList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                GROUP BY vid.CHANNEL_ID,dm.date_week_year,dm.DATE_WEEK
            </when>
            <when test="model.getDateUnit == 1">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_MONTH) as dateStr
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid_notDatasource"/>
                <if test="model.channelList != null and model.channelList.size()>0">
                    and vid.CHANNEL_ID in
                    <foreach item="item" collection="model.channelList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                GROUP BY vid.CHANNEL_ID,dm.DATE_YEAR,dm.DATE_MONTH
            </when>
            <when test="model.getDateUnit == 2">
                CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_QUARTER) as dateStr
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid_notDatasource"/>
                <if test="model.channelList != null and model.channelList.size()>0">
                    and vid.CHANNEL_ID in
                    <foreach item="item" collection="model.channelList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                GROUP BY vid.CHANNEL_ID,dm.DATE_YEAR,dm.DATE_QUARTER
            </when>
            <when test="model.getDateUnit == 3">
                dm.DATE_YEAR as dateStr
                FROM
                TF_DWS_VOC_EMOTION_USER vid left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid_notDatasource"/>
                <if test="model.channelList != null and model.channelList.size()>0">
                    and vid.CHANNEL_ID in
                    <foreach item="item" collection="model.channelList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                GROUP BY vid.CHANNEL_ID,dm.DATE_YEAR
            </when>
            <otherwise>
                vid.PUBLISH_DATE as dateStr
                FROM
                TF_DWS_VOC_EMOTION_USER vid
                where 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid_notDatasource"/>
                <if test="model.channelList != null and model.channelList.size()>0">
                    and vid.CHANNEL_ID in
                    <foreach item="item" collection="model.channelList" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                GROUP BY vid.CHANNEL_ID,vid.PUBLISH_DATE
            </otherwise>
        </choose>

    </select>




    <select id="mentionTop"   resultType="com.car.stats.vo.TopTopicVo">

        SELECT
            *
        FROM
            (SELECT
                 vid.TOPIC_CODE as labelCode,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    COUNT(DISTINCT vid.USER_ID) as referenceNum
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                    sum( vid.STATISTIC ) AS referenceNum
                </if>

             FROM
                 TF_DWS_VOC_EMOTION_USER vid

             WHERE
                 1 = 1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
             GROUP BY
                 vid.TOPIC_CODE
             ORDER BY
                 referenceNum DESC
                ) t


        WHERE
        1=1
        <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="mentionTopUp"   resultType="com.car.stats.vo.TopTopicVo">
        SELECT
            *
        FROM
            (SELECT
                 vid.TOPIC_CODE as labelCode,
                 tag.NAME as labelStr,
                tag1.NAME||'#'||tag2.NAME||'#'||tag3.NAME||'#'||tag.NAME as labelAllStr,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    COUNT(DISTINCT vid.USER_ID) as referenceNum,
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                    sum( vid.STATISTIC ) AS referenceNum
                </if>
             FROM
                 TF_DWS_VOC_QUALITY_USER  vid
                LEFT JOIN VOC_BUSINESS_TAG tag ON vid.TOPIC_CODE=tag.TAG_CODE
                LEFT JOIN VOC_BUSINESS_TAG tag3 ON tag3.ID=tag.PID
                LEFT JOIN VOC_BUSINESS_TAG tag2 ON tag2.ID=tag3.PID
                LEFT JOIN VOC_BUSINESS_TAG tag1 ON tag1.ID=tag2.PID

             WHERE
                 1 = 1
                and tag.NAME is not null
                 <if test="codes != null and codes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="codes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <include refid="publicDateFilterCriteria.queryCom_vid" />
             GROUP BY
                 vid.TOPIC_CODE,tag.NAME,tag1.NAME||'#'||tag2.NAME||'#'||tag3.NAME||'#'||tag.NAME
             ORDER BY
                 referenceNum DESC)
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>

    <select id="overviewBriefingValue"  resultType="com.car.stats.vo.VocOverBriefingValueVo">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
            where 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
            )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
            where 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid_up"/>

        ) tabUp

    </select>




    <select id="themeDistribution"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.FIRST_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on vid.FIRST_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        group by  vid.FIRST_DIMENSION_CODE,tag.NAME
        order by statistic desc

    </select>


    <select id="highFrequencyWords"   resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) AS  statistic
        </if>


        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null

        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.hotWords != null and model.hotWords.size()>0">
            and vid.EMOTION_KEYWORD in
            <foreach item="item" collection="model.hotWords" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.EMOTION_KEYWORD
        ORDER BY STATISTIC DESC,keyword DESC

        ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 50
        </if>
    </select>


    <select id="sourceChannel"   resultType="com.car.stats.vo.ChannelStatisticVo">
        SELECT
        vid.voc_channel_2 as dataSource,
            SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER   vid
        where 1=1
        <include refid="publicDateFilterCriteria.querychan_vid"/>
        GROUP BY
        vid.voc_channel_2
        ORDER BY statistic desc

    </select>

    <select id="secondThemeShare"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.SECOND_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on (vid.SECOND_DIMENSION_CODE) = tag.TAG_CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        group by  vid.SECOND_DIMENSION_CODE,tag.NAME
        order by statistic desc
    </select>
<select id="threeDistribution"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on (vid.THREE_DIMENSION_CODE) = tag.TAG_CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        group by  vid.THREE_DIMENSION_CODE,tag.NAME
        order by statistic desc
    </select>

    <select id="threeThemeShare"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on (vid.THREE_DIMENSION_CODE) = tag.TAG_CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        group by  vid.THREE_DIMENSION_CODE,tag.NAME
        order by statistic desc
    </select>


    <select id="themeShare"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.FIRST_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER   vid
        left join VOC_BUSINESS_TAG tag on vid.FIRST_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <!--  <if test="model.startDate !=null and model.startDate != ''and
                         model.endDate !=null and model.endDate != ''">
                   and vid.PUBLISH_DATE>=#{model.startDate}
                   AND #{model.endDate}>=vid.PUBLISH_DATE
               </if> -->
        group by  vid.FIRST_DIMENSION_CODE,tag.NAME
        order by  statistic desc
    </select>

    <select id="quantityTop"   resultType="com.car.stats.vo.LabelVo">
        select
            *
        from
            (
                SELECT
                    vid.TOPIC_CODE as labelCode,
                    (tag.NAME||'-'||tag3.NAME) as labelStr,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                        left join VOC_BUSINESS_TAG tag on vid.TOPIC_CODE = tag.TAG_CODE
                        left join VOC_BUSINESS_TAG tag3 on tag.PID = tag3.ID
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                  and  tag.NAME is not NULL AND tag3.NAME is not NULL
                group by  vid.TOPIC_CODE,(tag.NAME||'-'||tag3.NAME)
                order by statistic desc
            ) f
        where
        1=1
       <!--  limit 20-->
    </select>


    <select id="channelDistribution"   resultType="com.car.stats.vo.ChannelVo">
        SELECT
            sour.*,
            cc.NAME as channelStr
        FROM
            (
                SELECT
                    vid.CHANNEL_ID as channelId,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   " >
                        SUM(vid.STATISTIC) as statistic
                    </if>
                FROM
                TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
               <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY vid.CHANNEL_ID
            ) sour
                LEFT JOIN voc_channel_category cc ON sour.channelId=cc.ID
    </select>



    <select id="manyQuestion"   resultType="com.car.stats.vo.LabelVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.TOPIC_CODE as labelCode,
                    tag.NAME as labelStr,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   " >
                        SUM(vid.STATISTIC) AS statistic
                    </if>
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                    LEFT JOIN VOC_BUSINESS_TAG tag on vid.TOPIC_CODE=tag.TAG_CODE
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryCom_vid"/>
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                  and tag.NAME is NOT NULL
                GROUP BY vid.TOPIC_CODE,tag.NAME
                ORDER BY STATISTIC DESC

            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
              limit #{model.rownum}
          </if>
        <if test="model.rownum==null">
            limit 20
        </if>
    </select>





</mapper>
