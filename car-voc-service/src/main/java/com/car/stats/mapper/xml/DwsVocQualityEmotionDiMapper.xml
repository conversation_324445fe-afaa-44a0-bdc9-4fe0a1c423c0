<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocQualityEmotionDiMapper">


    <select id="qualityProblemTop"   resultType="com.car.stats.vo.QualityLabelVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.THREE_DIMENSION_CODE as label3Code,
                    vid.TOPIC_CODE as labelCode,
                    qp3.NAME||'：'||qp4.NAME as labelStr,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                        left join VOC_FAULT_PROBLEM qp4 on vid.TOPIC_CODE = qp4.CODE
                        left join VOC_FAULT_PROBLEM qp3 on qp4.PID = qp3.ID
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryCom_vid" />
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.THREE_DIMENSION_CODE,vid.TOPIC_CODE,qp3.NAME||'：'||qp4.NAME
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="typicalProblems"   resultType="com.car.stats.vo.QualityLabelVo">
        SELECT
            *
        FROM
        (
                SELECT
                vid.first_dimension_code as firstDimensionCode,
                vid.second_dimension_code as secondDimensionCode,
                vid.THREE_DIMENSION_CODE as label3Code,
                vid.TOPIC_CODE as labelCode,
                qp3.NAME||'：'||qp4.NAME as labelStr,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    COUNT(DISTINCT vid.USER_ID) as statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                    sum(vid.STATISTIC) as statistic
                </if>
                FROM
                TF_DWS_VOC_QUALITY_USER  vid
                left join VOC_FAULT_PROBLEM qp4 on vid.TOPIC_CODE = qp4.CODE
                left join VOC_FAULT_PROBLEM qp3 on qp4.PID = qp3.ID
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                 <include refid="ComFilterMapper.qualityThreeCodes" />

                group by  vid.first_dimension_code,vid.second_dimension_code,vid.THREE_DIMENSION_CODE,vid.TOPIC_CODE,qp3.NAME||'：'||qp4.NAME
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="channelDistribution"   resultType="com.car.stats.vo.ChannelVo">
        SELECT
        sour.*,
        cc.NAME as channelStr
        FROM
        (
        SELECT
        vid.DATA_SOURCE as channelId,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        WHERE
        1=1

        GROUP BY vid.DATA_SOURCE
        ) sour
        LEFT JOIN voc_channel_category cc ON sour.channelId=cc.ID
        where  1=1
        and cc.NAME is not null

    </select>

    <select id="channelDistributionProduct"   resultType="com.car.stats.vo.ChannelVo">
        SELECT
        sour.*
        FROM
        (
        SELECT
        vid.CHANNEL_ID as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        GROUP BY vid.CHANNEL_ID
        ) sour
    </select>
 <select id="channel2DistributionProduct"   resultType="com.car.stats.vo.ChannelVo">
        SELECT
        sour.*
        FROM
        (
        SELECT
        ch.pid as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        LEFT  JOIN voc_channel_category ch ON vid.channel_id = ch.id
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        GROUP BY ch.pid
        ) sour
    </select>


    <select id="commonProblems"   resultType="com.car.stats.vo.CommonProblemsVo">
        select
            *
            from
            (
            SELECT
            vid.topic_code AS labelCode,
            fp.NAME AS labelStr,
            <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                COUNT(DISTINCT vid.USER_ID) as statistic
            </if>
            <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                SUM( vid.STATISTIC ) AS statistic
            </if>
            FROM
            TF_DWS_VOC_QUALITY_USER  vid
            LEFT JOIN VOC_FAULT_PROBLEM fp ON vid.TOPIC_CODE=fp.CODE
            WHERE
            1=1
              and fp.NAME not in (
                                '故障',
                                '中断',
                                '质量缺陷',
                                '质量差/质量问题',
                                '召回',
                                '疑似设计缺陷',
                                '也不响了',
                                '延迟',
                                '小',
                                '无法显示',
                                '问题',
                                '题图老旧',
                                '损坏/无法使用',
                                '损坏',
                                '司机',
                                '受限',
                                '失效',
                                '设计缺陷',
                                '设计不佳',
                                '缺口',
                                '配置缺失',
                                '经撞击故障',
                                '坏了',
                                '坏掉',
                                '关不上',
                                '故障/无法工作',
                                '功能失常',
                                '高',
                                '范围小',
                                '操作繁琐',
                                '不足',
                                '不准',
                                '不一致',
                                '不符',
                                '爆炸',
                                '报警',
                                '版本低'
                                )
            <include refid="publicDateFilterCriteria.queryProductCom_vid" />
            <include refid="ComFilterMapper.qualityThreeCodes" />
            and fp.NAME is not null
            GROUP BY fp.NAME
            ORDER BY statistic DESC
        ) t WHERE 1=1
        <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 6
        </if>
    </select>
    <select id="commonProblemsTotal"   resultType="java.math.BigDecimal">
        select
            *
            from
            (
            SELECT
            fp.NAME AS labelStr,
            <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                COUNT(DISTINCT vid.USER_ID) as statistic
            </if>
            <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                SUM( vid.STATISTIC ) AS statistic
            </if>
            FROM
            TF_DWS_VOC_QUALITY_USER  vid
            LEFT JOIN VOC_FAULT_PROBLEM fp ON vid.TOPIC_CODE=fp.CODE
            WHERE
            1=1
            <include refid="publicDateFilterCriteria.queryProductCom_vid" />
            and fp.NAME is not null
            GROUP BY fp.NAME
            ORDER BY statistic DESC
        ) where
    </select>

    <select id="severityRatio"   resultType="com.car.stats.vo.SeverityRatioVo">

        SELECT
        serdict.problemLevel as problemLevelStr,
        IFNULL(tabprlleve.statistic,0) as statistic
        FROM
        (
        SELECT
        s.item_text problemLevel,
        s.SORT_ORDER
        FROM
        sys_dict_item s
        WHERE
        s.dict_id = ( SELECT id FROM sys_dict WHERE dict_code = 'seriousness_type' )
        ) serdict
        LEFT JOIN (
        SELECT
        vid.PROBLEM_LEVEL problemLevelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM( vid.STATISTIC ) AS statistic
        </if>
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1 = 1
        AND vid.PROBLEM_LEVEL IS NOT NULL
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        GROUP BY
        vid.PROBLEM_LEVEL
        ) tabprlleve ON problemLevelStr = problemLevel
        WHERE
        1 = 1
        ORDER BY
        serdict.SORT_ORDER ASC
    </select>

    <select id="componentTrendParts"   resultType="com.car.stats.vo.ComponentTrendPartVo">
        SELECT
            SUM(vid.STATISTIC) as  statistic,
        COUNT(DISTINCT vid.USER_ID) as userNum
        FROM
            TF_DWS_VOC_QUALITY_USER  vid
        WHERE
            1=1
          <include refid="publicDateFilterCriteria.queryProductCom_vid"/>
          AND vid.THREE_DIMENSION_CODE is NOT NULL

    </select>

    <select id="componentTrendRepair"   resultType="com.car.stats.vo.ComponentTrendPartVo">
        SELECT
        SUM(vid.STATISTIC) as  statistic
        FROM
        <choose>
            <when test="model.getDateUnit == 0">
                TF_DWS_VOC_REPAIR_EMOTION_WF vid where 1=1
                and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
            </when>
            <when test="model.getDateUnit == 1">
                TF_DWS_VOC_REPAIR_EMOTION_MF vid where vid.date_year = #{model.year} and vid.date_month = #{model.month}
            </when>
            <when test="model.getDateUnit == 2">
                TF_DWS_VOC_REPAIR_EMOTION_QF vid where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
            </when>
            <when test="model.getDateUnit == 3">
                TF_DWS_VOC_REPAIR_EMOTION_YF vid where vid.date_year = #{model.year}
            </when>
            <otherwise>
                    TF_DWS_VOC_REPAIR_EMOTION vid where 1=1
            </otherwise>
        </choose>


        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            and vid.BUS_TYPE = 'user'
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            and vid.BUS_TYPE = 'tag'
        </if>
        <include refid="publicDateFilterCriteria.queryRepair_vid"/>
        AND vid.THREE_DIMENSION_CODE is NOT NULL

    </select>

    <select id="commonProblemsNumParts"   resultType="java.math.BigDecimal">
        SELECT
            COUNT(DISTINCT vid.THREE_DIMENSION_CODE)
        FROM
            TF_DWS_VOC_QUALITY_USER vid
                LEFT JOIN VOC_FAULT_PROBLEM fp ON vid.TOPIC_CODE=fp.CODE
        WHERE
            1=1
          <include refid="publicDateFilterCriteria.queryProductCom_vid" />
          AND fp.NAME=#{topicLabelStr}

    </select>


    <select id="soarProblem"   resultType="com.car.stats.vo.SoarProblemVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.first_dimension_code as firstDimensionCode,
                    vid.second_dimension_code as secondDimensionCode,
                    vid.THREE_DIMENSION_CODE as label3Code,
                    vid.TOPIC_CODE as label4Code,
                    qp3.NAME as label3Str,
                    qp4.NAME as label4Str,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                        left join VOC_FAULT_PROBLEM qp4 on vid.TOPIC_CODE = qp4.CODE
                        left join VOC_FAULT_PROBLEM qp3 on qp4.PID = qp3.ID
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <include refid="ComFilterMapper.qualityThreeCodes" />
                group by  vid.first_dimension_code,vid.second_dimension_code,vid.THREE_DIMENSION_CODE,vid.TOPIC_CODE,qp3.NAME,qp4.NAME
                order by  statistic desc
            ) t
        WHERE 1=1
        <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>

    <select id="groupBy4"   resultType="com.car.stats.vo.LabelVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.TOPIC_CODE as labelCode,
                    qp4.NAME as labelStr,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                        left join VOC_FAULT_PROBLEM qp4 on vid.TOPIC_CODE = qp4.CODE
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
               <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''">
                 and  vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
               </if>
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>

                group by  vid.TOPIC_CODE,qp4.NAME
                order by  statistic desc
            )
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>
  <select id="groupBy3"   resultType="com.car.stats.vo.LabelVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.THREE_DIMENSION_CODE as labelCode,
                    qp3.NAME as labelStr,
                  <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                      COUNT(DISTINCT vid.USER_ID) as statistic
                  </if>
                  <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                      sum(vid.STATISTIC) as statistic
                  </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                    left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
               <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
                and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
               </if>
              <if test="model.topicCodes != null and model.topicCodes.size()>0">
                  and vid.TOPIC_CODE in
                  <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                      #{item}
                  </foreach>
              </if>
              <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                  and vid.THREE_DIMENSION_CODE in
                  <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                      #{item}
                  </foreach>
              </if>
                group by  vid.THREE_DIMENSION_CODE,qp3.NAME
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>

    <select id="repairGroupBy3"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        qp3.NAME as labelStr,
        sum(vid.STATISTIC) as statistic
        FROM
        <choose>
            <when test="model.getDateUnit == 0">
                TF_DWS_VOC_REPAIR_EMOTION_WF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                where 1=1
                and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
            </when>
            <when test="model.getDateUnit == 1">
                TF_DWS_VOC_REPAIR_EMOTION_MF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                where vid.date_year = #{model.year} and vid.date_month = #{model.month}
            </when>
            <when test="model.getDateUnit == 2">
                TF_DWS_VOC_REPAIR_EMOTION_QF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
            </when>
            <when test="model.getDateUnit == 3">
                TF_DWS_VOC_REPAIR_EMOTION_YF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                where vid.date_year = #{model.year}
            </when>
            <otherwise>
                TF_DWS_VOC_REPAIR_EMOTION vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                where 1=1
            </otherwise>
        </choose>

        <include refid="publicDateFilterCriteria.queryRepair_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
            and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
        </if>
        <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
            and vid.THREE_DIMENSION_CODE in
            <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            and vid.BUS_TYPE = 'user'
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            and vid.BUS_TYPE = 'tag'
        </if>
        group by  vid.THREE_DIMENSION_CODE,qp3.NAME
        order by  statistic desc
        ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>



<select id="repairGroupBy2"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.SECOND_DIMENSION_CODE as labelCode,
        qp3.NAME as labelStr,
        sum(vid.STATISTIC) as statistic
        FROM
        <choose>
            <when test="model.getDateUnit == 0">
                TF_DWS_VOC_REPAIR_EMOTION_WF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                where 1=1
                and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
            </when>
            <when test="model.getDateUnit == 1">
                TF_DWS_VOC_REPAIR_EMOTION_MF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                where vid.date_year = #{model.year} and vid.date_month = #{model.month}
            </when>
            <when test="model.getDateUnit == 2">
                TF_DWS_VOC_REPAIR_EMOTION_QF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
            </when>
            <when test="model.getDateUnit == 3">
                TF_DWS_VOC_REPAIR_EMOTION_YF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                where vid.date_year = #{model.year}
            </when>
            <otherwise>
                TF_DWS_VOC_REPAIR_EMOTION vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                where 1=1
            </otherwise>
        </choose>

        <include refid="publicDateFilterCriteria.queryRepair_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
            and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
        </if>
        <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
            and vid.THREE_DIMENSION_CODE in
            <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            and vid.BUS_TYPE = 'user'
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            and vid.BUS_TYPE = 'tag'
        </if>
        group by  vid.SECOND_DIMENSION_CODE,qp3.NAME
        order by  statistic desc
        )
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="secondDistribution"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.SECOND_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.SECOND_DIMENSION_CODE
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="groupBy2"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.SECOND_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
                    and vid.SECOND_DIMENSION_CODE in
                    <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.SECOND_DIMENSION_CODE
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>
    <select id="groupBy1"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.first_dimension_code as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
                    and vid.SECOND_DIMENSION_CODE in
                    <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.first_dimension_code
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>



    <select id="dataSourceDistribution"  resultType="com.car.stats.vo.ChannelVo">
        select
        sour.*
        from
        (SELECT
        vid.voc_channel_2 as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            count(distinct vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE 1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        GROUP BY
        vid.voc_channel_2
        ORDER BY statistic desc) sour

        where 1=1 limit 10
    </select>




    <select id="distributionProblemParts"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.FIRST_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.FIRST_DIMENSION_CODE
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="distributionProblemParts2"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.SECOND_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.SECOND_DIMENSION_CODE
                order by  statistic desc
            ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>

    <select id="distributionProblem3"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            ta.*
        FROM
            (
                SELECT
                    vid.THREE_DIMENSION_CODE as labelCode,
                    qp2.NAME as labelStr,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                        left join VOC_FAULT_PROBLEM qp2 on vid.THREE_DIMENSION_CODE = qp2.CODE
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                  and qp2.NAME is not null
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                group by  vid.THREE_DIMENSION_CODE,qp2.NAME
                order by  statistic desc
            ) ta
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>
    <select id="repairDistributionProblem3"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.THREE_DIMENSION_CODE as labelCode,
                    qp3.NAME as labelStr,
                    sum(vid.STATISTIC) as statistic
                FROM
                <choose>
                        <when test="model.getDateUnit == 0">
                            TF_DWS_VOC_REPAIR_EMOTION_WF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                            where 1=1
                            and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
                        </when>
                        <when test="model.getDateUnit == 1">
                            TF_DWS_VOC_REPAIR_EMOTION_MF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                            where vid.date_year = #{model.year} and vid.date_month = #{model.month}
                        </when>
                        <when test="model.getDateUnit == 2">
                            TF_DWS_VOC_REPAIR_EMOTION_QF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                            where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
                        </when>
                        <when test="model.getDateUnit == 3">
                            TF_DWS_VOC_REPAIR_EMOTION_YF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                            where vid.date_year = #{model.year}
                        </when>
                        <otherwise>
                            TF_DWS_VOC_REPAIR_EMOTION vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
                            where 1=1
                        </otherwise>
                </choose>

                <include refid="publicDateFilterCriteria.queryRepair_vid" />
                <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
                    and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
                </if>
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    and vid.BUS_TYPE = 'user'
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                    and vid.BUS_TYPE = 'tag'
                </if>
                group by  vid.THREE_DIMENSION_CODE,qp3.NAME
                order by  statistic desc
                )
                WHERE 1=1
    <if test="model.rownum!=null">
                    limit #{model.rownum}
                </if>
                <if test="model.rownum==null">
                    limit 10
                </if>
    </select>
<select id="repairDistributionProblem2"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            *
        FROM
            (
                SELECT
                    vid.SECOND_DIMENSION_CODE as labelCode,
                    qp3.NAME as labelStr,
                    sum(vid.STATISTIC) as statistic
                FROM
                <choose>
                        <when test="model.getDateUnit == 0">
                            TF_DWS_VOC_REPAIR_EMOTION_WF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                            where 1=1
                            and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
                        </when>
                        <when test="model.getDateUnit == 1">
                            TF_DWS_VOC_REPAIR_EMOTION_MF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                            where vid.date_year = #{model.year} and vid.date_month = #{model.month}
                        </when>
                        <when test="model.getDateUnit == 2">
                            TF_DWS_VOC_REPAIR_EMOTION_QF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                            where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
                        </when>
                        <when test="model.getDateUnit == 3">
                            TF_DWS_VOC_REPAIR_EMOTION_YF vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                            where vid.date_year = #{model.year}
                        </when>
                        <otherwise>
                            TF_DWS_VOC_REPAIR_EMOTION vid left join VOC_FAULT_PROBLEM qp3 on vid.SECOND_DIMENSION_CODE = qp3.CODE
                            where 1=1
                        </otherwise>
                </choose>

                <include refid="publicDateFilterCriteria.queryRepair_vid" />
                <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
                    and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
                </if>
                <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
                    and vid.THREE_DIMENSION_CODE in
                    <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    and vid.BUS_TYPE = 'user'
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                    and vid.BUS_TYPE = 'tag'
                </if>
                group by  vid.SECOND_DIMENSION_CODE,qp3.NAME
                order by  statistic desc
                )
                WHERE 1=1
    <if test="model.rownum!=null">
                    limit #{model.rownum}
                </if>
                <if test="model.rownum==null">
                    limit 10
                </if>
    </select>


<select id="distributionProblem4"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
            ta.*
        FROM
            (
                SELECT
                    vid.TOPIC_CODE as labelCode,
                    qp2.NAME as labelStr,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                FROM
                    TF_DWS_VOC_QUALITY_USER  vid
                        left join VOC_FAULT_PROBLEM qp2 on vid.TOPIC_CODE = qp2.CODE
                WHERE
                    1=1
                  <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                  and qp2.NAME is not null
                group by  vid.TOPIC_CODE,qp2.NAME
                order by  statistic desc
            ) ta
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>


    <select id="threeDistribution"   resultType="com.car.stats.vo.QualityLabelTreeVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />

        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
            and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
        </if>
        group by  vid.THREE_DIMENSION_CODE
        order by  statistic desc
        ) t
        WHERE 1=1
    <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
    </select>

<select id="repairThreeDistribution"   resultType="com.car.stats.vo.QualityLabelTreeVo">
    SELECT
    *
    FROM
    (
    SELECT
    vid.THREE_DIMENSION_CODE as labelCode,
    qp3.NAME as labelStr,
    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
        COUNT(DISTINCT vid.IS_ONE_ID) as statistic
    </if>
    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
        sum(vid.STATISTIC) as statistic
    </if>
    FROM
    <choose>
        <when test="model.getDateUnit == 0">
            TF_DWS_VOC_REPAIR_EMOTION_WF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
            where 1=1
            and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
        </when>
        <when test="model.getDateUnit == 1">
            TF_DWS_VOC_REPAIR_EMOTION_MF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
            where vid.date_year = #{model.year} and vid.date_month = #{model.month}
        </when>
        <when test="model.getDateUnit == 2">
            TF_DWS_VOC_REPAIR_EMOTION_QF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
            where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
        </when>
        <when test="model.getDateUnit == 3">
            TF_DWS_VOC_REPAIR_EMOTION_YF vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
            where vid.date_year = #{model.year}
        </when>
        <otherwise>
            TF_DWS_VOC_REPAIR_EMOTION vid left join VOC_FAULT_PROBLEM qp3 on vid.THREE_DIMENSION_CODE = qp3.CODE
            where 1=1
        </otherwise>
    </choose>

    <include refid="publicDateFilterCriteria.queryRepair_dis_parts_vid" />
    <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''">
        and vid.SECOND_DIMENSION_CODE=#{model.secondDimensionCode}
    </if>

    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
        and vid.BUS_TYPE = 'user'
    </if>
    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
        and vid.BUS_TYPE = 'tag'
    </if>
    group by  vid.THREE_DIMENSION_CODE,qp3.NAME
    order by  statistic desc
    )
    WHERE 1=1
    <if test="model.rownum!=null">
        limit #{model.rownum}
    </if>
    <if test="model.rownum==null">
        limit 10
    </if>
    </select>


    <select id="briefingValue"   resultType="com.car.stats.vo.VocOverBriefingValueVo">
        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent AS "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        where 1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        where 1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid_up" />

        ) tabUp
    </select>

    <select id="briefingTotalValue"   resultType="com.car.stats.vo.VocOverBriefingValueVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct vid.USER_ID) as totalMentions
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention ">
            SUM(STATISTIC) as totalMentions
        </if>
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        where 1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />

    </select>


    <select id="sourceChannel"   resultType="com.car.stats.vo.ChannelStatisticVo">
        SELECT
        vid.voc_channel_2 as dataSource,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.queryProductchan_vid" />
        GROUP BY
        vid.voc_channel_2
        ORDER BY statistic desc

    </select>



</mapper>
