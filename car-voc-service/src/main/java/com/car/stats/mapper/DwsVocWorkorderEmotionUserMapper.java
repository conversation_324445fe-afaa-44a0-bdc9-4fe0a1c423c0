package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.DwsVocWorkorderEmotionUser;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.WorkOrderFilteModel;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.LabelUserVo;
import com.car.stats.vo.WorkOrderUserVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 
 * @version 1.0.0
 * @ClassName DwsVocWorkorderEmotionUserMapper.java
 * @Description TODO
 * @createTime 2022年11月07日 14:44
 * @Copyright voc
 */
public interface DwsVocWorkorderEmotionUserMapper extends BaseMapper<DwsVocWorkorderEmotionUser>  {
    List<WorkOrderUserVo> workOrderUserType1(@Param("model")  WorkOrderFilteModel model);

    List<LabelUserVo> mediaTheme1(@Param("model")   WorkOrderFilteModel model);

    Map<String, String> userAndStatistic(@Param("model") LabelDetailFilterModel model);

    List<HomePurposeTrendVo> trendChangeLabelList(@Param("model")  LabelDetailFilterModel model);

    IPage<UserListInfoVo> getUserList(@Param("model") LabelDetailFilterModel model, Page<UserListInfoVo> page);
}
