package com.car.stats.mapper.wo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.RegionUserVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.VocOverBriefingValueVo;
import com.car.stats.vo.popvo.PopUpVo;
import com.car.stats.vo.wo.WoLastDealVo;
import com.car.voc.vo.WoTypeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t317_csv_rescue_wo_i_d(救援工单)】的数据库操作Mapper
 * @createDate 2024-12-05 15:22:07
 * @Entity com.car.stats.entity.wo.T317CsvRescueWoID
 */
public interface WoBaseOriginalDataMapper extends BaseMapper<WoOriginalData> {


    StatisticVo workOrderNumber(@Param("model") FilterCriteriaModel model);

    PopUpVo woTitle(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> provinceMap(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> regionalTop(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> communityTop(@Param("model") FilterCriteriaModel model);

    List<WoTypeVo> woTypeProportion(@Param("model") FilterCriteriaModel model);

    List<WoTypeVo> woStatusDistribution(@Param("model") FilterCriteriaModel model);

    List<WoTypeVo> woRegin1Distribution(@Param("model") FilterCriteriaModel model);

    List<WoTypeVo> woRegin2Distribution(@Param("model") FilterCriteriaModel model);

    List<WoTypeVo> woTrend(@Param("model") FilterCriteriaModel model);

    WoTypeVo workOrderSatisfaction(@Param("model") FilterCriteriaModel model);

    Page<WoLastDealVo> woList(IPage<WoLastDealVo> page, @Param("model") FilterCriteriaModel model);


}
