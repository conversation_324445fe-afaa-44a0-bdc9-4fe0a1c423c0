<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocUserMapper">

    <select id="userChannelPubNum" resultType="com.car.stats.vo.UserChannelPubNumVo">
        SELECT
            vus.*,
            ch.NAME as channelStr
        FROM
            (
                SELECT
                    us.CHANNEL_ID AS channelId,
                    us.USER_ID AS userId,
                    us.DISPLAY_NAME AS userName,
                    us.USER_TYPE AS userType,
                    us.USER_LEVEL AS userLevel,
                    SUM(us.STATISTIC) as pubNum,
                    IF(MIN(us.data_source)= 'Customer-data', 1, 0)  as isOneId
                FROM
                    TF_DWS_VOC_USER   us
                WHERE
                    1=1
                <if test="model.userId !=null and model.userId !=''" >
                    and us.USER_ID=#{model.userId}
                </if>

                GROUP BY us.CHANNEL_ID,us.USER_ID,us.DISPLAY_NAME,us.USER_TYPE,us.USER_LEVEL
            ) vus
                LEFT JOIN voc_channel_category ch ON vus.channelId=ch.ID

    </select>
    <select id="userChannelPubNumUserIds" resultType="com.car.stats.vo.UserChannelPubNumVo">
        SELECT
            vus.*,
            ch.NAME as channelStr
        FROM
            (
                SELECT
                    us.CHANNEL_ID AS channelId,
                    us.USER_ID AS userId,
                    max(us.DISPLAY_NAME) AS userName,
                    us.USER_TYPE AS userType,
                    us.USER_LEVEL AS userLevel,
                    IF(MIN(us.data_source)= 'Customer-data', 1, 0)  as isOneId,
                    SUM(us.STATISTIC) as pubNum
                FROM
                    TF_DWS_VOC_USER   us
                WHERE
                    1=1
                <if test="userIds != null and userIds.size()>0">
                    and
                    <foreach item="item" collection="userIds" separator="or" open="(" close=")" index="">
                        us.USER_ID= #{item}
                    </foreach>
                </if>

                GROUP BY us.CHANNEL_ID,us.USER_ID,us.USER_TYPE,us.USER_LEVEL
            ) vus
                LEFT JOIN voc_channel_category ch ON vus.channelId=ch.ID

    </select>
    <select id="getComplaintUserChannelNum" resultType="java.math.BigDecimal">
        select count(distinct vid.CHANNEL_ID)
        from TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        and vid.USER_ID=#{userId}
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
        and vid.INTENTION_TYPE='投诉'

    </select>
<select id="userChannelPubNumFilter" resultType="com.car.stats.vo.UserChannelPubNumVo">
        SELECT
            vus.*,
            ch.NAME as channelStr
        FROM
            (
                SELECT
                    vid.CHANNEL_ID AS channelId,
                    max(vid.DISPLAY_NAME) AS userName,
                    vid.USER_TYPE AS userType,
                    vid.USER_LEVEL AS userLevel,
                    SUM(vid.STATISTIC) as pubNum
                FROM
                    TF_DWS_VOC_USER vid
                WHERE
                    1=1
            <include refid="publicDateFilterCriteria.queryPop_vid" />
            and vid.USER_ID=#{userId}
                GROUP BY vid.CHANNEL_ID,vid.USER_TYPE,vid.USER_LEVEL
            ) vus
                LEFT JOIN voc_channel_category ch ON vus.channelId=ch.ID

    </select>



</mapper>
