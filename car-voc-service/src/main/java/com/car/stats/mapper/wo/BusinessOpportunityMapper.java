package com.car.stats.mapper.wo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.ChannelVo;
import com.car.stats.vo.ProportionCarSeriesVo;
import com.car.stats.vo.RegionUserVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.BranchesVo;
import com.car.stats.vo.wo.BuyCarTypeVo;
import com.car.stats.vo.wo.VisitIntentVo;
import com.car.stats.vo.wo.WoBusinessVo;
import com.car.voc.common.Result;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t317_csv_rescue_wo_i_d(救援工单)】的数据库操作Mapper
* @createDate 2024-12-05 15:22:07
* @Entity com.car.stats.entity.wo.T317CsvRescueWoID
*/
public interface BusinessOpportunityMapper extends BaseMapper<WoOriginalData> {

    List<ChannelVo> clueDist(@Param("model")  FilterCriteriaModel model);
    List<BranchesVo> intendedBranchTop(@Param("model")  FilterCriteriaModel model);

    List<ProportionCarSeriesVo> intendedCarTop(@Param("model")  FilterCriteriaModel model);

    Page<WoBusinessVo> woOrderList(IPage<WoBusinessVo> page, @Param("model")   FilterCriteriaModel model);

    VisitIntentVo visitIntent(@Param("model")  FilterCriteriaModel model);

    List<BuyCarTypeVo> carPurchaseDemandTrend(@Param("model") FilterCriteriaModel model);
}
