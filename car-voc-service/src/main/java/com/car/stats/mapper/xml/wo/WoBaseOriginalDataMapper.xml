<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.wo.WoBaseOriginalDataMapper">

    <resultMap id="BaseResultMap" type="com.car.stats.entity.wo.WoOriginalData">
            <id property="woNum" column="wo_num" jdbcType="VARCHAR"/>
            <result property="oneid" column="oneid" jdbcType="VARCHAR"/>
            <result property="woTheme" column="wo_theme" jdbcType="VARCHAR"/>
            <result property="brand" column="brand" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
            <result property="customerProvince" column="customer_province" jdbcType="VARCHAR"/>
            <result property="modelName" column="model_name" jdbcType="VARCHAR"/>
            <result property="serviceStationInfo" column="service_station_info" jdbcType="VARCHAR"/>
            <result property="woType" column="wo_type" jdbcType="VARCHAR"/>
            <result property="woContent" column="wo_content" jdbcType="VARCHAR"/>
            <result property="disposeOpinion" column="dispose_opinion" jdbcType="VARCHAR"/>
            <result property="rescueProvideChannelName" column="rescue_provide_channel_name" jdbcType="VARCHAR"/>
            <result property="rescueOrderNum" column="rescue_order_num" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="syncTime" column="sync_time" jdbcType="TIMESTAMP"/>
            <result property="dataDate" column="data_date" jdbcType="INTEGER"/>
            <result property="customerCity" column="customer_city" jdbcType="VARCHAR"/>
            <result property="dealerRegion1" column="dealer_region_1" jdbcType="VARCHAR"/>
            <result property="dealerRegion2" column="dealer_region_2" jdbcType="VARCHAR"/>
            <result property="dealer" column="dealer" jdbcType="VARCHAR"/>
            <result property="tagCode" column="tag_code" jdbcType="OTHER"/>
            <result property="woChannel" column="wo_channel" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        wo_num,oneid,wo_theme,
        brand,create_time,customer_province,
        model_name,service_station_info,wo_type,
        wo_content,dispose_opinion,rescue_provide_channel_name,
        rescue_order_num,customer_name,sync_time,
        data_date,customer_city,dealer_region_1,
        dealer_region_2,dealer,tag_code,
        wo_channel
    </sql>

    <select id="workOrderNumber"  resultType="com.car.stats.vo.StatisticVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        <!-- 情感分析基础数据 -->
        ifnull(voice_c, 0) AS statistic,
        ifnull(LEAD(voice_c) OVER (ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
            COUNT(wo_num) as voice_c,
            COUNT(DISTINCT oneid) as oneid_c
            <include refid="publicDateFilterCriteria.wo_org_commonDateQuery" />
            GROUP BY date_str
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            COUNT(wo_num) as voice_c,
            COUNT(DISTINCT oneid) as oneid_c
            FROM common_date_tools cdt
            LEFT JOIN wo_original_data vid
            ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
    </select>


    <select id="woTitle" resultType="com.car.stats.vo.popvo.PopUpVo">
        <include refid="publicDateFilterCriteria.wo-original-data" />
        SELECT
            count( 1 ) as statistic,
            COUNT( DISTINCT oneid ) as userCount
        FROM
            wo_original_data
    </select>


    <select id="provinceMap" resultType="com.car.stats.vo.RegionUserVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr-range" />
        SELECT
        *,
        di.ITEM_VALUE as regionCode,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        customer_province as regionStr,
        userNumP,
        statisticP,
        voice_c as statistic,
        userNum,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY customer_province ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
            SELECT
            <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
            REPLACE(REPLACE(customer_province, '省', ''), '市', '') customer_province,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,count(distinct oneid) as userNum,
            ROUND(COUNT(DISTINCT oneid) / SUM(COUNT(DISTINCT oneid)) OVER (PARTITION BY cdt.endDate) * 100, 2)       AS userNumP,
            ROUND(COUNT(1)  / SUM(COUNT(1)) OVER (PARTITION BY cdt.endDate) * 100,2)  AS statisticP
            FROM common_date_tools cdt
            LEFT JOIN wo_original_data vid ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            GROUP BY date_str,REPLACE(REPLACE(customer_province, '省', ''), '市', ''), cdt.startDate, cdt.endDate
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        LEFT JOIN SYS_DICT_ITEM di ON f2.regionStr=di.item_text and di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>


    <!-- ================================================商机线索-大区排行==================================================== -->
    <select id="regionalTop"  resultType="com.car.stats.vo.RegionUserVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        dealer_region_1 as regionStr,
        statisticP,
        voice_c as statistic,
        userNum,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY dealer_region_1 ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
            SELECT
            <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
            dealer_region_1,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,count(distinct oneid) as userNum,
            ROUND(COUNT(DISTINCT oneid) / SUM(COUNT(DISTINCT oneid)) OVER (PARTITION BY cdt.endDate) * 100, 2)       AS userNumP,
            ROUND(COUNT(1)  / SUM(COUNT(1)) OVER (PARTITION BY cdt.endDate) * 100,2)  AS statisticP
            FROM common_date_tools cdt
            LEFT JOIN wo_original_data vid ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            GROUP BY date_str,dealer_region_1, cdt.startDate, cdt.endDate
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>


    <!-- ================================================小区排行==================================================== -->
    <select id="communityTop"  resultType="com.car.stats.vo.RegionUserVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr-range"/>
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        dealer_region_2 as regionStr,
        statisticP,
        voice_c as statistic,
        userNum,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY dealer_region_2 ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        SELECT
        <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
        dealer_region_2,
        cdt.startDate, cdt.endDate,
        COUNT(1) AS voice_c,count(distinct oneid) as userNum,
        ROUND(COUNT(DISTINCT oneid) / SUM(COUNT(DISTINCT oneid)) OVER (PARTITION BY cdt.endDate) * 100, 2)       AS userNumP,
        ROUND(COUNT(1)  / SUM(COUNT(1)) OVER (PARTITION BY cdt.endDate) * 100,2)  AS statisticP
        FROM common_date_tools cdt
        LEFT JOIN wo_original_data vid ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
        WHERE 1=1
        GROUP BY date_str,dealer_region_2, cdt.startDate, cdt.endDate
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>


    <select id="woTypeProportion" resultType="com.car.voc.vo.WoTypeVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        select vid.wo_type as woType,count(1) as statistic
        from wo_original_data vid
        where 1=1
        group by vid.wo_type
    </select>

    <select id="woRegin1Distribution" resultType="com.car.voc.vo.WoTypeVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        select vid.dealer_region_1 as area,count(1) as statistic,count(distinct oneid) as userNum
        from wo_original_data vid
        where 1=1 and dealer_region_1 is not null
        group by vid.dealer_region_1
    </select>
    <select id="woList" resultType="com.car.stats.vo.wo.WoLastDealVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
            *
        FROM
            wo_original_data vid
        WHERE
            1=1
        order by create_time desc
    </select>

    <select id="woRegin2Distribution" resultType="com.car.voc.vo.WoTypeVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        select vid.dealer_region_2 as area,count(1) as statistic,count(distinct oneid) as userNum
        from wo_original_data vid
        where 1=1 and dealer_region_2 is not null
        <include refid="publicDateFilterCriteria.wo_original_query" />
        group by vid.dealer_region_2
    </select>

    <select id="woTrend" resultType="com.car.voc.vo.WoTypeVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr" />
        SELECT  case when length(cc.date_)>9 then date_format(date_, '%Y/%m/%d')
        else cc.date_ end as date_str, vid.*
            , ROUND(satisfactionNum / statistic * 100, 2) AS satisfactionNumP
            , ROUND(unSatisfactionNum / statistic * 100, 2) AS unSatisfactionNumP
        FROM common_date_tools cc
        LEFT JOIN (
            SELECT <include refid="publicDateFilterCriteria.vo-groupby-com-cycle-table" /> AS date_create
                , COUNT(CASE
                WHEN  vid.customer_satisfaction = '不满意'
                THEN 1
                END) AS unSatisfactionNum
                , COUNT(CASE
                WHEN vid.customer_satisfaction = '满意' THEN 1
                END) AS satisfactionNum
                , COUNT(1) AS statistic
        FROM wo_original_data vid
        WHERE 1 = 1
            GROUP BY date_create
            ) vid
            ON cc.date_ = vid.date_create
    </select>

    <select id="workOrderSatisfaction" resultType="com.car.voc.vo.WoTypeVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        select count(1) as statistic,
               sum(
               case when customer_satisfaction = '满意' and status <![CDATA[ >= ]]> 3  then 1 else 0 end
        ) as satisfactionNum,
            sum(case when status <![CDATA[ >= ]]> 3 then 1 else 0 end) as closeCount,
            sum(case when status <![CDATA[ < ]]> 3 then 1 else 0 end) as openCount
        from wo_original_data vid
        where 1=1
    </select>

    <select id="woStatusDistribution" resultType="com.car.voc.vo.WoTypeVo">
        <include refid="publicDateFilterCriteria.wo-original-data"/>
        SELECT
        CASE
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[<]]> 12 THEN  '<![CDATA[<12H]]>'
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ >= ]]> 12 AND TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ < ]]> 24 THEN '12-24H'
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ >= ]]> 24 AND TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ < ]]> 48 THEN '24-48H'
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ >= ]]> 48 AND TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ < ]]> 72 THEN '48-72H'
        ELSE '>72H'
        END AS dateStr,
        case when status <![CDATA[ >= ]]>3 then 4 else 2 end  as status,
        COUNT(*) AS statistic
        FROM
        wo_original_data vid
        where 1=1
        <include refid="publicDateFilterCriteria.wo_original_query" />
        GROUP BY
        -- 根据CASE WHEN判断的结果进行分组
        CASE
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[<]]> 12 THEN   '<![CDATA[<12H]]>'
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ >= ]]> 12 AND TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ < ]]> 24 THEN '12-24H'
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ >= ]]> 24 AND TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ < ]]> 48 THEN '24-48H'
        WHEN TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time ELSE NOW() END
        ) <![CDATA[ >= ]]> 48 AND TIMESTAMPDIFF(HOUR, create_time,
        CASE WHEN status <![CDATA[ >= ]]> 3 THEN close_time  ELSE NOW() END
        ) <![CDATA[ < ]]> 72 THEN '48-72H'
        ELSE '>72H'
        end,case when status <![CDATA[ >= ]]>3 then 4 else 2 end ;
    </select>

</mapper>
