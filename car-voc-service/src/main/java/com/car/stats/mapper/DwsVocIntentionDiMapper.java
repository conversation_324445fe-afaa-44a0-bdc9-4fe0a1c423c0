package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.DwsVocIntentionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocIntentionDiServiceMapper.java
 * @Description TODO
 * @createTime 2022年10月10日 16:30
 * @Copyright voc
 */
public interface DwsVocIntentionDiMapper extends BaseMapper<DwsVocIntentionDi> {

    Map<String, BigDecimal> purposeCb(@Param("model") FilterCriteriaModel model);

    Map<String, BigDecimal> purposeTjl(@Param("model") FilterCriteriaModel model,@Param("reportVo") HomeIntentionReportVo reportVo);


    List<TopTopicVo> mentionTop(@Param("model") FilterCriteriaModel model, @Param("reportVo")  HomeIntentionReportVo reportVo);

    List<TopTopicVo> mentionTopUp(List<String> codes, @Param("model") FilterCriteriaModel model,@Param("reportVo") HomeIntentionReportVo reportVo);

    EmotionProportionVo homeProportionEmotion(@Param("model") FilterCriteriaModel model);

    List<ChannelStatisticVo> homeChannelTrends(@Param("model") FilterCriteriaModel model);
    List<ChannelStatisticVo> homeChannelTrends1(@Param("model") FilterCriteriaModel model);

    List<ChannelStatisticVo> sourceChannel(@Param("model") FilterCriteriaModel model);

    VocOverBriefingValueVo overviewBriefingValue(@Param("model") FilterCriteriaModel model);

    List<LabelVo> themeDistribution(@Param("model") FilterCriteriaModel model);

    List<HighHotWordsVo> highFrequencyWords(@Param("model") FilterCriteriaModel model);

    List<LabelVo> secondThemeShare(@Param("model") FilterCriteriaModel model);
    List<LabelVo> threeThemeShare(@Param("model") FilterCriteriaModel model);

    List<LabelVo> themeShare(@Param("model") FilterCriteriaModel model);

    List<LabelVo> manyQuestion(@Param("model")  FilterCriteriaModel model);

    List<LabelVo> quantityTop(@Param("model") FilterCriteriaModel model);

    List<ChannelVo> channelDistribution(@Param("model") FilterCriteriaModel model);

    Map<String, BigDecimal> queryUserNum(@Param("model")  FilterCriteriaModel model);

    List<ChannelStatisticVo> dataSourceDistribution(@Param("model") FilterCriteriaModel model);

    List<LabelVo> threeDistribution(@Param("model") FilterCriteriaModel model);
}
