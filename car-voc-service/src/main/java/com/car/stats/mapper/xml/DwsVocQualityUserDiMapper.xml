<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocQualityUserDiMapper">

    <sql id="dataDryingContrast_dateUnit">
        <if test="model.dateUnit == 0">
            case
            when TIMESTAMPDIFF(week,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(week,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == 1">
            case
            when TIMESTAMPDIFF(month,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(month,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == 2">
            case
            when TIMESTAMPDIFF(quarter,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(quarter,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == 3">
            case
            when TIMESTAMPDIFF(year,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(year,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == -1">
            case
            when TIMESTAMPDIFF(DAY,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(DAY,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
    </sql>

    <select id="queryUserNumQuality" resultType="java.util.Map">
        SELECT
            tabThis.thisUserNum as "thisUserNum",
            tabUp.upUserNum as "thisUserNumUp"
        FROM
            (SELECT
                 COUNT(DISTINCT vid.USER_ID) as thisUserNum
             FROM
                 TF_DWS_VOC_QUALITY_USER  vid
             WHERE
                 1=1
                <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                ) tabThis,
            (SELECT
                 COUNT(DISTINCT vid.USER_ID) AS upUserNum
             FROM
                 TF_DWS_VOC_QUALITY_USER  vid
             WHERE
                 1=1
                <include refid="publicDateFilterCriteria.queryProductCom_vid_up" />
                ) tabUp

    </select>
    <select id="userNumQuality" resultType="java.math.BigDecimal">
        SELECT
            tabThis.thisUserNum as "thisUserNum"
        FROM
            (SELECT
                 COUNT(DISTINCT vid.USER_ID) as thisUserNum
             FROM
                 TF_DWS_VOC_QUALITY_USER  vid
             WHERE
                 1=1
                <include refid="publicDateFilterCriteria.queryProductCom_vid" />
                ) tabThis


    </select>
 <select id="intentionNumAndUserNum" resultType="com.car.stats.vo.HomePurposeTrendVo">
     SELECT
         SUM(vid.STATISTIC) as intention,
         COUNT(DISTINCT vid.USER_ID) as  userCount
     FROM
       TF_DWS_VOC_EMOTION_USER vid
     WHERE
         1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
    </select>


    <select id="intentionNumAndUserNumList" resultType="com.car.stats.vo.HomePurposeTrendVo">

        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr,
        SUM(CASE WHEN vid.INTENTION_TYPE = #{model.intention} THEN vid.STATISTIC ELSE 0 END) AS intention,
        COUNT(DISTINCT CASE WHEN vid.INTENTION_TYPE = #{model.intention} THEN vid.USER_ID END) AS userCount
        <!--SUM(vid.STATISTIC) AS intentionTotal,
        COUNT(DISTINCT vid.USER_ID) AS userTotal -->
        FROM TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.query_no_intention_com_vid" />
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        </select>

    <select id="intentionNumAndUserNumList_old" resultType="com.car.stats.vo.HomePurposeTrendVo">

        select
        uts.*,ito.intentionTotal as intentionTotal,uto.userTotal as userTotal
        from

        (select
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr,
        sum(vid.STATISTIC)  as intentionTotal
        from TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.query_no_intention_com_vid" />
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle" /> ) ito,

        ( select
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr,
        COUNT(DISTINCT vid.USER_ID)  as userTotal
            from TF_DWS_VOC_EMOTION_USER vid
            where 1=1
                <include refid="publicDateFilterCriteria.query_no_intention_com_vid" />
                group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        ) uto,
        (SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr,
             SUM(vid.STATISTIC) as intention,
             COUNT(DISTINCT vid.USER_ID) as  userCount
        FROM
           TF_DWS_VOC_EMOTION_USER vid
         WHERE
             1=1
            <include refid="publicDateFilterCriteria.queryCom_vid" />
            group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
            ORDER BY dateStr) uts
        where  1=1 and ito.dateStr=uts.dateStr and uts.dateStr=uto.dateStr

        </select>


    <select id="riskBriefingValue"  resultType="com.car.stats.vo.VocOverBriefingValueVo">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        where 1=1

        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        where  1=1
        <include refid="ComFilterMapper.up_date"/>
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>

        ) tabUp

    </select>

    <select id="riskStatisticTotal"   resultType="java.math.BigDecimal">
        SELECT
        SUM(vid.STATISTIC) statistic
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
    </select>

    <select id="riskUserNum" resultType="java.util.Map">

        SELECT
        tabThis.userCount AS "userCount",
        tabUp.userCount as "userCountUp"
        FROM
        (SELECT
        COUNT(DISTINCT USER_ID) AS userCount
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>

        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        )  tabThis,

        (SELECT
        COUNT(DISTINCT USER_ID) AS userCount
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.up_date"/>
        <include refid="ComFilterMapper.general_query_dataSorce" />

        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        ) tabUp
    </select>

    <select id="riskUserTotalNum"   resultType="java.math.BigDecimal">
        SELECT
        count(distinct vid.USER_ID) statistic
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        and vid.DIMENSION_EMOTION='负面'
    </select>


    <select id="riskCarSeries"   resultType="java.lang.String">


        SELECT
        vid.CAR_SERIES_CODE
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        AND vid.CAR_SERIES_CODE is NOT NULL
        GROUP BY vid.CAR_SERIES_CODE
        order by  case when CAR_SERIES_CODE like '%A00' then 1 else 0 end ,  sum(vid.STATISTIC) desc



    </select>

    <select id="riskCarSeriesExport"   resultType="java.lang.String">


        SELECT
        car.name
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        left join voc_brand_product_manager car on vid.CAR_SERIES_CODE=car.code

        WHERE
        1=1
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        AND vid.CAR_SERIES_CODE is NOT NULL
        GROUP BY car.name
        order by sum(vid.STATISTIC) desc



    </select>
 <select id="riskWarningUserNumExport"   resultType="java.lang.Long">

     select count(distinct vid.USER_ID)
     from TF_DWS_VOC_QUALITY_USER vid
     where
     vid.TOPIC_CODE=#{model.topicCode}
     and vid.DIMENSION_EMOTION='负面'
     <if test="model.startDate !=null and model.startDate != ''and
                          model.endDate !=null and model.endDate != ''">
         and vid.PUBLISH_DATE>=#{model.startDate}
         AND #{model.endDate}>=vid.PUBLISH_DATE
     </if>
     <if test="model.createDate !=null and model.createDate != '' ">
         AND #{model.createDate}>=vid.create_time
     </if>
    <include refid="ComFilterMapper.com_risk_brand_createDate"/>



    </select>

    <select id="riskHotWordsOpinion"   resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        tab.EMOTION_KEYWORD as keyword,
        tab.statistic
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD,
        SUM(vid.STATISTIC) statistic
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
        AND vid.EMOTION_KEYWORD IS NOT NULL
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        and vid.DIMENSION_EMOTION='负面'

        GROUP BY vid.EMOTION_KEYWORD
        ORDER BY statistic DESC,vid.EMOTION_KEYWORD desc
        ) tab
        WHERE
        1=1
        limit 10

    </select>

    <select id="emotionIntentionTrend"  resultType="com.car.stats.vo.risk.IntentionEmotionTrendVo">
        SELECT
        sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS 	complaint,
        sum(CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        ORDER BY statistic DESC
    </select>

    <select id="intentionTrend"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        SELECT
        SUM(vid.statistic) AS intentionTotal,
        count(distinct vid.USER_ID)  userTotal
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        ORDER BY statistic DESC
    </select>

    <select id="carSeriesDistribution"  resultType="com.car.stats.vo.CarEmotionVo">
        SELECT
        vid.car_series_code as carSeries,
        SUM(vid.statistic) AS total,
        ROUND(SUM(vid.statistic) / SUM(SUM(vid.statistic)) OVER () * 100, 2) AS totalP
        FROM
        tf_dws_voc_quality_user vid
        WHERE
        vid.dimension_emotion = '负面'
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        GROUP BY
        vid.car_series_code
        ORDER BY
        total DESC
    </select>

    <select id="riskHotWords"  resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        tab.*
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        vid.DIMENSION_EMOTION AS emotionType,
        SUM(vid.STATISTIC) AS  statistic
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        GROUP BY vid.EMOTION_KEYWORD,vid.DIMENSION_EMOTION
        ORDER BY STATISTIC DESC,keyword desc
        ) tab
        WHERE
        1=1
        <if test="model.rownum !=null" >
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>

    <select id="riskVoiceUserTrend"  resultType="com.car.stats.vo.risk.VoiceUserVo">
        SELECT
        count(distinct vid.USER_ID)  userNum
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
    </select>
    <select id="voiceUserTop"  resultType="com.car.stats.vo.risk.VoiceUserTopVo">
    SELECT
    chus.*,
    ch.NAME channelStr
    FROM
    (
    SELECT
    vid.CHANNEL_ID AS channelId,
    max(vid.DISPLAY_NAME) AS displayName,
    vid.USER_ID,vid.IS_ONE_ID,
    sum(vid.STATISTIC) userNum
    FROM
    TF_DWS_VOC_QUALITY_USER  vid
    WHERE
    1=1
    and vid.DISPLAY_NAME is not null
    and vid.CHANNEL_ID is not null
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>
    <if test="model.topicCode !=null and model.topicCode !=''" >
        AND vid.TOPIC_CODE =#{model.topicCode}
    </if>
    <if test="model.brandCode !=null and model.brandCode !=''" >
        AND vid.brand_code =#{model.brandCode}
    </if>
    <if test="model.createDate !=null and model.createDate != '' ">
        AND #{model.createDate}>=vid.create_time
    </if>
    GROUP BY vid.CHANNEL_ID,vid.USER_ID,vid.IS_ONE_ID
    ORDER BY userNum DESC
    ) chus  left join voc_channel_category ch on chus.channelId=ch.id
    WHERE
        1=1
    <if test="model.rownum !=null" >
        limit #{model.rownum}
    </if>
    <if test="model.rownum ==null ">
        limit 50
    </if>
</select>
    <select id="voiceUserTop1"  resultType="com.car.stats.vo.risk.VoiceUserTopVo">
        SELECT
        chus.*,
        ch.NAME channelStr
        FROM
        (
        SELECT
        vid.DATA_SOURCE AS channelId,
        vid.DISPLAY_NAME AS displayName,
        vid.USER_ID,vid.IS_ONE_ID,
        sum(vid.STATISTIC) userNum
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        WHERE
        1=1
        and vid.DISPLAY_NAME is not null
        and vid.CHANNEL_ID is not null
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        GROUP BY vid.DATA_SOURCE,vid.DISPLAY_NAME,vid.USER_ID,vid.IS_ONE_ID
        ORDER BY userNum DESC
        ) chus  left join voc_channel_category ch on chus.channelId=ch.id
        WHERE
        1=1
        <if test="model.rownum !=null" >
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>
    <select id="cdpUserProportion"  resultType="com.car.stats.vo.CdpUserNumVo">
        SELECT
            vid.IS_ONE_ID as "isOneId",
            COUNT(DISTINCT vid.USER_ID) as "number"
        FROM
        TF_DWS_VOC_EMOTION_USER  vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY vid.IS_ONE_ID

    </select>





 <select id="userLevelNum" resultType="java.math.BigDecimal">
     SELECT
         COUNT(DISTINCT vid.USER_ID) as userTypeNum
     FROM
     TF_DWS_VOC_EMOTION_USER vid
     WHERE
         1=1
       <include refid="publicDateFilterCriteria.queryCom_vid" />
       <if test="model.userLevel !=null and model.userLevel !=''">
       AND vid.USER_LEVEL =#{model.userLevel}
       </if>

 </select>

 <select id="overviewRegionalDistribution" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
         sf.*,
         di.ITEM_TEXT as regionStr
     FROM
         (
             SELECT
                 PROVINCE as regionCode,
                 count( DISTINCT USER_ID ) as userNum,
                 sum( STATISTIC ) as statistic
             FROM
                TF_DWS_VOC_EMOTION_USER vid
             WHERE
                 1=1
               <include refid="publicDateFilterCriteria.queryCom_vid"/>
             GROUP BY
                 PROVINCE
             order by statistic desc
         ) sf
             LEFT JOIN SYS_DICT_ITEM di ON sf.regionCode=di.ITEM_VALUE
     WHERE
         1=1
       AND di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'



 </select>

 <select id="productRegionalDistribution" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
         sf.*,
         di.ITEM_TEXT as regionStr
     FROM
         (
             SELECT
                 PROVINCE as regionCode,
                 count( DISTINCT USER_ID ) as userNum,
                 sum( STATISTIC ) as statistic
             FROM
                 TF_DWS_VOC_QUALITY_USER  vid
             WHERE
                 1=1
               <include refid="publicDateFilterCriteria.queryProductCom_vid"/>
             GROUP BY
                 PROVINCE
             order by statistic desc
         ) sf
             LEFT JOIN SYS_DICT_ITEM di ON sf.regionCode=di.ITEM_VALUE
     WHERE
         1=1
       AND di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'



 </select>

 <select id="provinceMap" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
         sf.*,
         di.ITEM_TEXT as regionStr
     FROM
         (
             SELECT
                 PROVINCE as regionCode,
                 count( DISTINCT USER_ID ) as userNum,
                 sum( STATISTIC ) as statistic,
                 ROUND(COUNT(DISTINCT USER_ID) / SUM(COUNT(DISTINCT USER_ID)) OVER () * 100, 2) AS userNumP,
                 ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
             FROM
                 TF_DWS_VOC_QUALITY_USER  vid
             WHERE
                 1=1
               <include refid="publicDateFilterCriteria.queryProductCom_vid"/>
             GROUP BY
                 PROVINCE
             order by statistic desc
         ) sf
             LEFT JOIN SYS_DICT_ITEM di ON sf.regionCode=di.ITEM_VALUE
     WHERE
         1=1
       AND di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'



 </select>

 <select id="regionalUser" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
     area.*,
     di.ITEM_TEXT AS regionStr
     FROM
     (
     SELECT
     pa.AREA_CODE AS regionCode,
     count( DISTINCT USER_ID ) AS userNum,
     sum( STATISTIC ) AS statistic
     FROM
     TF_DWS_VOC_EMOTION_USER vid
     LEFT JOIN TC_PROVINCE_AREA pa ON vid.PROVINCE = pa.PROVINCE_CODE
     WHERE
     1 = 1
     <include refid="publicDateFilterCriteria.queryCom_vid" />

     GROUP BY
     pa.AREA_CODE
     ORDER BY statistic DESC
     ) area
     LEFT JOIN SYS_DICT_ITEM di ON area.regionCode=di.ITEM_VALUE
     WHERE
     1=1
     AND di.DICT_ID='4b82677b6c1408df4be21ada9a584fde'
     order by area.statistic desc


 </select>


 <select id="productRegionalUser" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
     area.*,
     di.ITEM_TEXT AS regionStr
     FROM
     (
     SELECT
     pa.AREA_CODE AS regionCode,
     count( DISTINCT USER_ID ) AS userNum,
     sum( STATISTIC ) AS statistic
     FROM
     TF_DWS_VOC_QUALITY_USER  vid
     LEFT JOIN TC_PROVINCE_AREA pa ON vid.PROVINCE = pa.PROVINCE_CODE
     WHERE
     1 = 1
     <include refid="publicDateFilterCriteria.queryProductCom_vid" />
     GROUP BY
     pa.AREA_CODE
     ORDER BY userNum DESC
     ) area
     LEFT JOIN SYS_DICT_ITEM di ON area.regionCode=di.ITEM_VALUE
     WHERE
     1=1
     AND di.DICT_ID='4b82677b6c1408df4be21ada9a584fde'
     order by  area.userNum desc
 </select>


 <select id="productRegionUser" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
     area.*,
     di.ITEM_TEXT AS regionStr
     FROM
     (
     SELECT
     vid.AREA_CODE AS regionCode,
     count( DISTINCT USER_ID ) AS userNum,
     sum( STATISTIC ) AS statistic
     FROM
     TF_DWS_VOC_QUALITY_USER  vid
     WHERE
     1 = 1
     <include refid="publicDateFilterCriteria.queryProductCom_vid" />
     GROUP BY
     vid.AREA_CODE
     ORDER BY userNum DESC
     ) area
     LEFT JOIN SYS_DICT_ITEM di ON area.regionCode=di.ITEM_VALUE
     WHERE
     1=1
     and di.ITEM_VALUE != '0000000'
     AND di.DICT_ID='4b82677b6c1408df4be21ada9a584fde'
     order by
     area.statistic
        desc
 </select>

 <select id="focusRegionalTop" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
     vbr.regional_code as regionCode,
     count( DISTINCT USER_ID ) as userNum,
     sum( STATISTIC ) as statistic
     FROM
     TF_DWS_VOC_QUALITY_USER vid
     LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_max" />  vbr on vid.province =vbr.province_code
     WHERE
     1=1
     and vid.brand_code =vbr.brand
     <if test="model.brandCode != null and model.brandCode != 'A11'">
         and vbr.application_type =2
     </if>
     <if test="model.brandCode !=null and model.brandCode !=''" >
         AND vid.brand_code =#{model.brandCode}
     </if>
     <include refid="publicDateFilterCriteria.queryProductCom_vid" />
     GROUP BY
     vbr.regional_code
     order by statistic desc
 </select>

 <select id="focusCommunityTop" resultType="com.car.stats.vo.RegionUserVo">
     SELECT
     vbr.community_code as regionCode,
     count( DISTINCT USER_ID ) as userNum,
     sum( STATISTIC ) as statistic
     FROM
     TF_DWS_VOC_QUALITY_USER vid
     LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_min" />  vbr on vid.city =vbr.city_code
     WHERE
     1=1
     and vid.brand_code =vbr.brand
     and vbr.city_code is not null
     <if test="model.brandCode != null and model.brandCode != 'A11'">
         and vbr.application_type =2
     </if>
     <if test="model.brandCode !=null and model.brandCode !=''" >
         AND vid.brand_code =#{model.brandCode}
     </if>
     <include refid="publicDateFilterCriteria.queryProductCom_vid" />
     GROUP BY
     vbr.community_code
     order by statistic desc
 </select>



 <select id="qualityEmotionIntention" resultType="com.car.stats.vo.risk.EmotionIntentionVo">

     <include refid="publicDateFilterCriteria.groupby-date-com-all-data-airing" />
     (select
     <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr1,
     sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
     sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
     sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
     from
     TF_DWS_VOC_QUALITY_USER vid
     WHERE 1=1
     <include refid="ComFilterMapper.general_query_dataSorce" />

     <if test="model.riskName !=null and model.riskName !=''" >
         AND vid.TOPIC_CODE =#{model.riskName}
     </if>
     <include refid="ComFilterMapper.this_date" />
     GROUP BY
     <include refid="publicDateFilterCriteria.groupby-com-cycle" />
     ORDER BY dateStr1 asc ) datay on dateCycle.dateStr=datay.dateStr1
 </select>


 <select id="qualityEmotionIntention1" resultType="com.car.stats.vo.risk.EmotionIntentionVo">
     select *
     from
     ( <include refid="publicDateFilterCriteria.dayGroupCompletion"/>) dgc
     left join
     (
    select
     <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dated,
     sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
     sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
     sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
     from
     TF_DWS_VOC_QUALITY_USER vid
     WHERE 1=1
     <if test="model.riskName !=null and model.riskName !=''" >
         AND vid.TOPIC_CODE =#{model.riskName}
     </if>
     <include refid="ComFilterMapper.this_date" />
     GROUP BY
     <include refid="publicDateFilterCriteria.groupby-com-cycle" />) nc on dgc.dateStr=nc.dated
     ORDER BY dgc.dateStr asc
 </select>



 <select id="qualityChangeChannel" resultType="com.car.stats.vo.ChannelVo">
     <include refid="publicDateFilterCriteria.groupby-date-com-all-data-airing"/>
     (
     select
     <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr1,
     vid.CHANNEL_ID as channelId,
     sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS statistic
     from
     TF_DWS_VOC_QUALITY_USER vid
     WHERE 1=1
     <include refid="ComFilterMapper.general_query_dataSorce" />
     <if test="model.riskName !=null and model.riskName !=''" >
         AND vid.TOPIC_CODE =#{model.riskName}
     </if>
     <include refid="ComFilterMapper.this_date" />
     GROUP BY
     <include refid="publicDateFilterCriteria.groupby-com-cycle" />,
     vid.CHANNEL_ID
     order by dateStr1 asc
    ) datay on dateCycle.dateStr=datay.dateStr1

 </select>

 <select id="qualityChangeChannel1" resultType="com.car.stats.vo.ChannelVo">
     select *
     from
     ( <include refid="publicDateFilterCriteria.dayGroupCompletion"/>) dgc
     left join
     (select
     <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dated,
     vid.CHANNEL_ID as channelId,
     sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS statistic
     from
     TF_DWS_VOC_QUALITY_USER vid
     WHERE 1=1
     <if test="model.riskName !=null and model.riskName !=''" >
         AND vid.TOPIC_CODE =#{model.riskName}
     </if>
     <include refid="ComFilterMapper.this_date" />
     GROUP BY
     <include refid="publicDateFilterCriteria.groupby-com-cycle" />,
     vid.CHANNEL_ID
     ) nc on dgc.dateStr=nc.dated
     ORDER BY dgc.dateStr asc
 </select>

 <select id="qualityHotWords" resultType="com.car.stats.vo.HighHotWordsVo">
     select
     *
     from
     (
     select
     vid.EMOTION_KEYWORD as keyword,
     sum(vid.STATISTIC) as  statistic
     from
     TF_DWS_VOC_QUALITY_USER vid
     where
     1=1
     and vid.EMOTION_KEYWORD is not null
     <include refid="ComFilterMapper.general_query_dataSorce" />

     <if test="model.riskName !=null and model.riskName !=''" >
         AND vid.TOPIC_CODE =#{model.riskName}
     </if>
     <if test="model.endDate !=null and model.endDate != ''">
         AND #{model.endDate}>=vid.PUBLISH_DATE
     </if>
     <if test="model.startDate !=null and model.startDate != ''">
         AND vid.PUBLISH_DATE>=#{model.startDate}
     </if>

     group by vid.EMOTION_KEYWORD
     order by statistic desc
     ) te
     where
     1=1  limit 50
 </select>


 <select id="dataDryingContrast" resultType="com.car.stats.vo.risk.DataDryingContrastVo">
     select
         <if test="type !=null and type == 1 " >
             CEILING(te.userNum / date) as userNum,
             CEILING(te.complaint / date) as complaint,
             CEILING(te.negative / date) as negative
         </if>
         <if test="type !=null and type == 0 " >
             *
         </if>
     from
     (
     select
     <include refid="dataDryingContrast_dateUnit"></include>
     count(distinct vid.USER_ID) as userNum,
     sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
     sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
     from
     TF_DWS_VOC_QUALITY_USER vid
     where
     1=1
     <include refid="ComFilterMapper.general_query_dataSorce" />
     and vid.DIMENSION_EMOTION='负面'
     <if test="model.riskName !=null and model.riskName !=''" >
         AND vid.TOPIC_CODE =#{model.riskName}
     </if>
     <if test="model.endDate !=null and model.endDate != ''">
         AND #{model.endDate}>=vid.PUBLISH_DATE
     </if>
     <if test="model.startDate !=null and model.startDate != ''">
         AND vid.PUBLISH_DATE>=#{model.startDate}
     </if>
     ) te
     where
     1=1
 </select>


 <select id="riskWarningUserNum" resultType="java.lang.Long">
     select count(distinct vid.USER_ID)
     from TF_DWS_VOC_QUALITY_USER vid
     where
     1=1
     <include refid="ComFilterMapper.general_query_dataSorce" />
     and vid.TOPIC_CODE=#{model.topicCode}
     and vid.DIMENSION_EMOTION='负面'
     <if test="model.startDate !=null and model.startDate != ''and
                          model.endDate !=null and model.endDate != ''">
         and vid.PUBLISH_DATE>=#{model.startDate}
         AND #{model.endDate}>=vid.PUBLISH_DATE
     </if>
     <if test="model.createDate !=null and model.createDate != '' ">
         AND #{model.createDate}>=vid.create_time
     </if>
     <if test="model.brandCode !=null and model.brandCode !=''" >
         AND vid.brand_code =#{model.brandCode}
     </if>

 </select>


    <select id="lastTagHotWords"  resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        COUNT(distinct vid.USER_ID) as userCount,
        sum(vid.STATISTIC) as statistic,
        (sum(vid.STATISTIC))/total_count.agecount as statisticP,
        (count(distinct vid.USER_ID))/total_count.ageUser as userCountP
        FROM
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_QUALITY_USER vid
            </otherwise>
        </choose>,
        (SELECT
        COUNT(DISTINCT vid.USER_ID) as ageUser,
        sum(vid.STATISTIC) AS agecount
        FROM
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_QUALITY_USER vid
            </otherwise>
        </choose>

        WHERE
        1=1
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />) as total_count
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.hotWords != null and model.hotWords.size()>0">
            and vid.EMOTION_KEYWORD in
            <foreach item="item" collection="model.hotWords" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.EMOTION_KEYWORD
        ORDER BY STATISTIC DESC
        ) t
        WHERE
        1=1
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>




    <select id="riskTrend"  resultType="java.util.Map">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        where 1=1
        <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.com_risk_brand_createDate" />

        and vid.DIMENSION_EMOTION='负面'

        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        where  1=1
        <include refid="ComFilterMapper.up_date" />
        <include refid="ComFilterMapper.com_risk_brand_createDate" />

        and vid.DIMENSION_EMOTION='负面'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        ) tabUp

    </select>




    <select id="qualityProblemTrend"   resultType="com.car.stats.vo.DateUserStatisticVo">
        SELECT
            to_char( vid.PUBLISH_DATE, 'yyyy-mm-dd' ) AS dateStr,
            count(DISTINCT USER_ID) AS userNum,
            sum( vid.STATISTIC ) AS statistic
        FROM
            TF_DWS_VOC_QUALITY_USER  vid
        WHERE
            1 = 1
          <if test="model.startDate !=null and model.startDate != ''and
                                model.endDate !=null and model.endDate != ''">
           and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
          </if>
        GROUP BY
            to_char( vid.PUBLISH_DATE, 'yyyy-mm-dd' )
        ORDER BY dateStr ASC

    </select>

    <select id="overviewProblemTrends"   resultType="com.car.stats.vo.DateUserStatisticVo">
        SELECT
            count(DISTINCT USER_ID) AS userNum,
            sum( vid.STATISTIC ) AS statistic
        FROM
            TF_DWS_VOC_QUALITY_USER  vid
        WHERE
            1 = 1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
    </select>


    <select id="topVoiceUsers"   resultType="com.car.stats.vo.TopVoiceUsersVo">
        SELECT
            *
        FROM
            (
                SELECT
                    USER_ID as userId,
                    max(DISPLAY_NAME) as userName,
                    USER_LEVEL as userLevel,
                    USER_TYPE as userType,
                    SUM(vid.STATISTIC) AS statistic
                FROM
                TF_DWS_VOC_QUALITY_USER  vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY USER_ID,USER_LEVEL,USER_TYPE
                ORDER BY statistic DESC
            )
        where 1=1
            limit 10
    </select>



    <select id="intendedUserTrends"   resultType="com.car.stats.vo.HomePurposeTrendVo">
        SELECT
        to_char( vid.PUBLISH_DATE, 'yyyy-mm-dd' ) AS dateStr,
        COUNT(DISTINCT vid.USER_ID) as userCount,
        sum( vid.STATISTIC ) AS intention
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryCom_vid" />

        GROUP BY
        to_char( vid.PUBLISH_DATE, 'yyyy-mm-dd' )
        ORDER BY dateStr ASC
    </select>


    <select id="thirdTagDistributionByTopic"  resultType="com.car.stats.vo.popvo.UserLabelVo">
        SELECT
        fc.*,
        tn.NAME as labelStr
        FROM
        (
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        LEFT JOIN VOC_FAULT_PROBLEM fp ON vid.TOPIC_CODE=fp.CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        AND fp.NAME=#{topicName}
        group by  vid.THREE_DIMENSION_CODE

        ) fc
        LEFT JOIN VOC_FAULT_PROBLEM tn ON fc.labelCode=tn.CODE
        where
        1=1
        and tn.NAME is not null
        ORDER by statistic desc
    </select>

    <select id="thirdDimensionCodeBySecond"  resultType="com.car.stats.vo.popvo.UserLabelVo">
        SELECT
        fc.*,
        tn.NAME as labelStr
        FROM
        (
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_QUALITY_USER vid
            </otherwise>
        </choose>
        LEFT JOIN VOC_FAULT_PROBLEM fp ON vid.TOPIC_CODE=fp.CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />

        group by  vid.THREE_DIMENSION_CODE

        ) fc
        LEFT JOIN VOC_FAULT_PROBLEM tn ON fc.labelCode=tn.CODE
        where
        1=1
        and tn.NAME is not null
        ORDER by statistic desc

    </select>
 <select id="secondDimensionCodeByFirst"  resultType="com.car.stats.vo.popvo.UserLabelVo">
        SELECT
        fc.*
        FROM
        (
        SELECT
        vid.SECOND_DIMENSION_CODE as labelCode,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
     <choose>
         <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
             tf_dws_voc_nps_record vid
         </when>
         <otherwise>
             TF_DWS_VOC_QUALITY_USER vid
         </otherwise>
     </choose>
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />

        group by  vid.SECOND_DIMENSION_CODE

        ) fc
        where
        1=1
        ORDER by statistic desc

    </select>
<select id="fistDimensionCode"  resultType="com.car.stats.vo.popvo.UserLabelVo">
        SELECT
        fc.*
        FROM
        (
        SELECT
        vid.FIRST_DIMENSION_CODE as labelCode,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid
    WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        group by  vid.FIRST_DIMENSION_CODE
        ) fc
        where
        1=1
        ORDER by statistic desc

    </select>

    <select id="topicTagByThird"  resultType="com.car.stats.vo.popvo.UserLabelVo">
        SELECT
        fc.*,
        tn.NAME as labelStr
        FROM
        (
        SELECT
        vid.TOPIC_CODE as labelCode,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_QUALITY_USER vid
            </otherwise>
        </choose>
        LEFT JOIN VOC_FAULT_PROBLEM fp ON vid.TOPIC_CODE=fp.CODE
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        group by  vid.TOPIC_CODE
        ) fc
        LEFT JOIN VOC_FAULT_PROBLEM tn ON fc.labelCode=tn.CODE
        where
        1=1
        and tn.NAME is not null
        ORDER by statistic desc
    </select>

    <select id="trendChangeLabel"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        <include refid="popUserAndStatistic" />
    </select>
    <select id="repairTrendChangeLabel"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        <include refid="repairPopUserAndStatistic" />
    </select>

    <select id="trendChangeLabelGenerality"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        <include refid="popUserAndStatisticGenerality" />
    </select>
    <select id="repairTrendChangeLabelGenerality"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        <include refid="repairPopUserAndStatisticGenerality" />
    </select>


    <select id="userAndStatistic"  resultType="java.util.Map">
        <include refid="popUserAndStatistic" />
    </select>

    <select id="userAndStatisticGenerality"  resultType="java.util.Map">
        <include refid="popUserAndStatisticGenerality" />
    </select>

    <sql id="repairPopUserAndStatistic">
        SELECT
        vid.BUS_TYPE,
        SUM(vid.STATISTIC) as "intention"
        FROM
        <choose>
            <when test="model.getDateUnit == 0">
                TF_DWS_VOC_REPAIR_EMOTION_WF vid where 1=1
                and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
            </when>
            <when test="model.getDateUnit == 1">
                TF_DWS_VOC_REPAIR_EMOTION_MF vid where vid.date_year = #{model.year} and vid.date_month = #{model.month}
            </when>
            <when test="model.getDateUnit == 2">
                TF_DWS_VOC_REPAIR_EMOTION_QF vid where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
            </when>
            <when test="model.getDateUnit == 3">
                TF_DWS_VOC_REPAIR_EMOTION_YF vid where vid.date_year = #{model.year}
            </when>
            <otherwise>
                TF_DWS_VOC_REPAIR_EMOTION vid where 1=1
            </otherwise>
        </choose>


        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.THREE_DIMENSION_CODE in (select code from VOC_FAULT_PROBLEM where id in (select pid from VOC_FAULT_PROBLEM where CODE = #{model.topicCode}))
        </if>

        <include refid="publicDateFilterCriteria.repairQueryPop_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        group by vid.BUS_TYPE

    </sql>

    <sql id="popUserAndStatistic">
        select
        sum(ta.userCount) as userCount,
        ta.intention
        from
        (
        SELECT
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as intention
        FROM

        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_QUALITY_USER vid
            </otherwise>
        </choose>


        WHERE
        1=1


        <include refid="publicDateFilterCriteria.queryPop_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>




        ) ta
    </sql>

    <sql id="popUserAndStatisticGenerality">
       select
        sum(ta.userCount) userCount,
        ta.intention intention
        from
        ( SELECT
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as intention
        FROM
        TF_DWS_VOC_QUALITY_USER  vid
        left join VOC_FAULT_PROBLEM tn on vid.TOPIC_CODE=tn.CODE
        WHERE
        1=1

        <if test="model.topic !=null and model.topic !=''" >
            AND vid.TOPIC_CODE=tn.CODE
            and tn.NAME=#{model.topic}
        </if>
        <include refid="publicDateFilterCriteria.queryPop_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>) ta

    </sql>
    <sql id="repairPopUserAndStatisticGenerality">
        SELECT
        bus_type,
        SUM(vid.STATISTIC) as intention
        FROM
        <choose>
            <when test="model.getDateUnit == 0">
                TF_DWS_VOC_REPAIR_EMOTION_WF vid where 1=1
                and vid.DATE_YEAR = #{model.year} and vid.date_week = #{model.week}
            </when>
            <when test="model.getDateUnit == 1">
                TF_DWS_VOC_REPAIR_EMOTION_MF vid where vid.date_year = #{model.year} and vid.date_month = #{model.month}
            </when>
            <when test="model.getDateUnit == 2">
                TF_DWS_VOC_REPAIR_EMOTION_QF vid where vid.date_year = #{model.year} and vid.date_quarter = #{model.season}
            </when>
            <when test="model.getDateUnit == 3">
                TF_DWS_VOC_REPAIR_EMOTION_YF vid where vid.date_year = #{model.year}
            </when>
            <otherwise>
                TF_DWS_VOC_REPAIR_EMOTION vid where 1=1
            </otherwise>
        </choose>
        <if test="model.topic !=null and model.topic !=''" >
            AND vid.THREE_DIMENSION_CODE in (select code from VOC_FAULT_PROBLEM where id in (select pid from VOC_FAULT_PROBLEM where name = #{model.topic}))
        </if>

        <include refid="publicDateFilterCriteria.repairQueryPop_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>

        group by vid.bus_type


    </sql>

    <select id="getUserList1"  resultType="com.car.stats.vo.popvo.UserListInfoVo">
        SELECT
        vocuser.USER_ID as userId,
        vocuser.DISPLAY_NAME as userName,
        vocuser.IS_ONE_ID as isOneId,
        SUM(vid.STATISTIC) as statistic,
        sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,
        sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        FROM
        (
        SELECT
        T.USER_ID,
        T.DISPLAY_NAME,
        T.IS_ONE_ID
        FROM (
        SELECT vid.USER_ID,vid.DISPLAY_NAME,vid.IS_ONE_ID,
        ROW_NUMBER() OVER(PARTITION BY vid.USER_ID ORDER BY vid.PUBLISH_DATE DESC) RW
        FROM TF_DWS_VOC_QUALITY_USER  vid
        <!-- ===========共性问题 -=============begin -->
        <if test="model.topic !=null and model.topic !=''" >
            left join VOC_FAULT_PROBLEM tn on vid.TOPIC_CODE=tn.CODE
            WHERE
            1=1
            AND vid.TOPIC_CODE=tn.CODE
            and tn.NAME=#{model.topic}
        </if>
        <!-- ===========共性问题 -=============end -->
        <if test="model.topic ==null " >
            WHERE
            1=1
        </if>
        AND vid.DISPLAY_NAME is NOT NULL
        AND vid.USER_ID IS NOT NULL
        and vid.TOPIC_CODE is NOT NULL
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        and vid.USER_ID is not null
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>

        ) T
        WHERE T.RW = 1
        GROUP BY T.USER_ID,T.DISPLAY_NAME,T.IS_ONE_ID
        ) vocuser
        LEFT JOIN
        TF_DWS_VOC_QUALITY_USER  vid ON vocuser.USER_ID=vid.USER_ID
        WHERE
        1=1
        GROUP BY vocuser.USER_ID,vocuser.DISPLAY_NAME,vocuser.IS_ONE_ID
        ORDER BY statistic DESC

    </select>
    <select id="getUserList"  resultType="com.car.stats.vo.popvo.UserListInfoVo">


        SELECT
        vid.USER_ID userId,
        max(vid.display_name) AS userName,
        SUM(vid.statistic) statistic,
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                "1" AS isOneId,
            </when>
            <otherwise>
                if (sum(CASE vid.data_source WHEN 'Customer-data' THEN 1 ELSE 0 END)>0,"1","0")  AS isOneId,
            </otherwise>
        </choose>
        vid.user_type as userType,
        0 as userLevel,
        sum( case vid.intention_type when '咨询' then vid.statistic else 0 end ) as consult,
        sum( case vid.intention_type when '投诉' then vid.statistic else 0 end ) as complaint,
        sum( case vid.intention_type when '表扬' then vid.statistic else 0 end ) as praise,
        sum( case vid.intention_type when '建议' then vid.statistic else 0 end ) as suggest,
        sum( case vid.dimension_emotion when '正面' then vid.statistic else 0 end ) as positive,
        sum( case vid.dimension_emotion when '负面' then vid.statistic else 0 end ) as negative,
        (sum( case vid.dimension_emotion when '正面' then vid.statistic else 0 end )-sum( case vid.dimension_emotion when '负面' then vid.statistic else 0 end ))/(sum( case vid.dimension_emotion when '正面' then vid.statistic else 0 end )+sum( case vid.dimension_emotion when '负面' then vid.statistic else 0 end )) as emotionWorth

        FROM
        TF_DWS_VOC_QUALITY_USER vid
        <!-- ===========共性问题 -=============begin -->
        <if test="model.topic !=null and model.topic !=''" >
            left join VOC_FAULT_PROBLEM tn on vid.TOPIC_CODE=tn.CODE
            WHERE
            1=1
            AND vid.TOPIC_CODE=tn.CODE
            and tn.NAME=#{model.topic}
        </if>
        <!-- ===========共性问题 -=============end -->
        <if test="model.topic ==null " >
            WHERE
            1=1
        </if>
        and vid.USER_ID is not null
        and vid.TOPIC_CODE is NOT NULL
        <include refid="publicDateFilterCriteria.queryPop_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        GROUP BY vid.USER_ID

        <if test="model.column !=null and model.column !='' and model.order !=null and model.order !='' " >
            ORDER BY ${model.column} ${model.order},userId desc
        </if>
        <if test="model.column ==null or model.column ==''  " >
            ORDER BY statistic DESC,userId desc
        </if>





    </select>
<select id="getUserLists"  resultType="com.car.stats.vo.popvo.UserListInfoVo">

        SELECT
        T.USER_ID as userId,
        T.DISPLAY_NAME as userName,
        T.IS_ONE_ID  as isOneId,
        SUM(T.STATISTIC) as statistic,
        sum( CASE T.INTENTION_TYPE WHEN '咨询' THEN T.STATISTIC ELSE 0 END ) AS consult,
        sum( CASE T.INTENTION_TYPE WHEN '投诉' THEN T.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE T.INTENTION_TYPE WHEN '表扬' THEN T.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE T.DIMENSION_EMOTION WHEN '正面' THEN T.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE T.DIMENSION_EMOTION WHEN '负面' THEN T.STATISTIC ELSE 0 END ) AS negative
        FROM (
        SELECT vid.USER_ID,vid.DISPLAY_NAME,vid.IS_ONE_ID,vid.STATISTIC,vid.INTENTION_TYPE,vid.DIMENSION_EMOTION,
        ROW_NUMBER() OVER(PARTITION BY vid.USER_ID ORDER BY vid.PUBLISH_DATE DESC) RW
        FROM TF_DWS_VOC_QUALITY_USER  vid
        <!-- ===========共性问题 -=============begin -->
        <if test="model.topic !=null and model.topic !=''" >
            left join VOC_FAULT_PROBLEM tn on vid.TOPIC_CODE=tn.CODE
            WHERE
            1=1
            AND vid.TOPIC_CODE=tn.CODE
            and tn.NAME=#{model.topic}
        </if>
        <!-- ===========共性问题 -=============end -->
        <if test="model.topic ==null " >
            WHERE
            1=1
        </if>
        and vid.USER_ID is not null
        and vid.TOPIC_CODE is NOT NULL
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>

        ) T
        WHERE T.RW = 1
        GROUP BY T.USER_ID,T.DISPLAY_NAME,T.IS_ONE_ID
        ORDER BY statistic DESC


    </select>

    <select id="userQulitityCount" resultType="java.math.BigDecimal">
        SELECT
        COUNT(STATISTIC)
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />


    </select>

    <select id="topQualityUsers" resultType="com.car.stats.vo.TopQualityUsersVo">
        WITH user_stats AS (
            SELECT
                USER_ID AS userId,
                max(vid.DISPLAY_NAME) as userName,
                SUM(STATISTIC) AS statistic,
                SUM(CASE WHEN DIMENSION_EMOTION = '正面' THEN STATISTIC ELSE 0 END) AS positive,
                SUM(CASE WHEN DIMENSION_EMOTION = '负面' THEN STATISTIC ELSE 0 END) AS negative,
                CASE WHEN MIN(data_source) = 'Customer-data' THEN 1 ELSE 0 END AS isOneId
            FROM TF_DWS_VOC_QUALITY_USER vid
            WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryProductCom_vid" />
            GROUP BY USER_ID
        ),
        ranked_topics AS (
            SELECT
                USER_ID AS userId,
                topic_code AS most_common_topic_code,
                ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY SUM(STATISTIC) DESC) AS rn
            FROM TF_DWS_VOC_QUALITY_USER vid
            WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryProductCom_vid" />
            GROUP BY USER_ID, topic_code
        )
        SELECT
            u.userId,
            u.userName,
            u.statistic,
            u.positive,
            u.negative,
            u.isOneId,
            rt.most_common_topic_code AS topicCode
        FROM user_stats u
        JOIN ranked_topics rt ON u.userId = rt.userId and rt.rn = 1
        ORDER BY u.statistic DESC
        <if test="model.rownum !=null" >
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 20
        </if>
    </select>

    <select id="riskChannelDistribution" resultType="com.car.stats.vo.ChannelVo">

        SELECT
        sour.*
        FROM
        (
        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle-table"/>
        AS dateStr,
        vid.CHANNEL_ID as channelId,
            SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        LEFT JOIN TF_DIM_DATE tdd ON vid.PUBLISH_DATE=tdd.FORMAT_DATE

        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date"/>

        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate} &gt;= vid.create_time
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        GROUP BY
        <include refid="publicDateFilterCriteria.groupby-com-cycle-table"/>
        ,
        vid.CHANNEL_ID
        ORDER BY dateStr desc
        ) sour
        where 1=1
    </select>

    <select id="riskAllTotal"  resultType="java.util.Map">

        SELECT
        SUM(STATISTIC) as statisticTotal,
        count(distinct vid.user_id ) as userTotal
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        where 1=1
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>

    </select>

    <select id="riskCarSeriesStr"   resultType="java.lang.String">

        SELECT
        concat( vbpm.name ,'(',sum(vid.STATISTIC),')')
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        left join voc_brand_product_manager vbpm on vid.car_series_code =vbpm .code
        WHERE
        1=1
        and vid.TOPIC_CODE=#{model.topicCode}
        AND vid.CAR_SERIES_CODE is NOT NULL
        AND vbpm.name is NOT NULL
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        GROUP BY vid.CAR_SERIES_CODE
        order by case when CAR_SERIES_CODE like '%A00' then 1 else 0 end , sum(vid.STATISTIC) desc



    </select>

    <select id="riskAreaProption" resultType="com.car.voc.vo.ProportionAreaVo">

        select r.regional_name as areaStr,
               r.regional_code as areaCode,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid join voc_brand_region r on vid.province = r.province_code
        where 1=1
        and vid.province is not null
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="risk.channelId !=null and risk.channelId != '' ">
            AND vid.channel_id is not null
        </if>
        <if test="risk.brandCode !=null and risk.brandCode != '' ">
            AND vid.brand_code = #{risk.brandCode}
            AND r.brand = #{risk.brandCode}
        </if>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND to_date(#{model.createDate},'YYYY-MM-DD hh24:mi:ss')>=vid.create_time
        </if>
        GROUP BY vid.AREA_CODE

    </select>
    <select id="riskCarSeriesProption" resultType="com.car.stats.vo.ProportionCarSeriesVo">

        select vid.CAR_SERIES_CODE,
        SUM(vid.STATISTIC) as statistic
        from TF_DWS_VOC_QUALITY_USER vid
        where 1=1
        and vid.CAR_SERIES_CODE is not null
        and vid.CAR_SERIES_CODE !='A12A00'
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="risk.channelId !=null and risk.channelId != '' ">
            AND vid.channel_id is not null
        </if>
        <if test="risk.brandCode !=null and risk.brandCode != '' ">
            AND vid.brand_code = #{risk.brandCode}
        </if>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND to_date(#{model.createDate},'YYYY-MM-DD hh24:mi:ss')>=vid.create_time
        </if>
        group by vid.CAR_SERIES_CODE
    </select>
    <select id="riskDataSourceDistribution" resultType="com.car.stats.vo.ChannelVo">
        select
            * from
        (SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle-table"/>
        AS dateStr,
        vid.voc_channel_3 as channelId,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        LEFT JOIN TF_DIM_DATE tdd ON vid.PUBLISH_DATE=tdd.FORMAT_DATE
        WHERE 1=1
        <if test="model.createDate !=null and model.createDate != '' ">
            AND to_date(#{model.createDate},'YYYY-MM-DD hh24:mi:ss')>=vid.create_time
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="risk.channelId !=null and risk.channelId != '' ">
            AND vid.channel_id is not null
        </if>
        <if test="risk.brandCode !=null and risk.brandCode != '' ">
            AND vid.brand_code = #{risk.brandCode}
        </if>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by<include refid="publicDateFilterCriteria.groupby-com-cycle-table"/>,
        vid.voc_channel_3
        ORDER BY statistic desc) sour

        where 1=1 limit 10
    </select>

    <select id="hotWords" resultType="com.car.stats.vo.HighHotWordsVo">
        select
        *
        from
        (
        select
        vid.EMOTION_KEYWORD as keyword,
        sum(CASE WHEN vid.dimension_emotion = '正面' THEN vid.STATISTIC ELSE 0 END) as positive,
        sum(CASE WHEN vid.dimension_emotion = '中性' THEN vid.STATISTIC ELSE 0 END) as neutral,
        sum(CASE WHEN vid.dimension_emotion = '负面' THEN vid.STATISTIC ELSE 0 END) as negative,
        sum(vid.STATISTIC) as  statistic
        from
        TF_DWS_VOC_QUALITY_USER vid
        where
        1=1
        and vid.EMOTION_KEYWORD is not null
            <include refid="publicDateFilterCriteria.queryCom_vid"/>
        group by vid.EMOTION_KEYWORD
        order by statistic desc
        ) te
        where
        1=1  limit 50
    </select>


    <select id="focusRegionalTopRisk" resultType="com.car.stats.vo.RegionUserVo">
        WITH vocquality_total AS (
        SELECT SUM(STATISTIC) AS total_statistic
        FROM TF_DWS_VOC_QUALITY_USER vid
        WHERE dimension_emotion = '负面'
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date" />
        <if test="model.topicCode != null and model.topicCode != ''">
            AND TOPIC_CODE = #{model.topicCode}
        </if>
        <if test="model.createDate != null and model.createDate != ''">
            AND #{model.createDate} >= create_time
        </if>
        )
        SELECT
        vbr.regional_code AS regionCode,
        SUM(vid.STATISTIC) AS statistic,
        ROUND(SUM(vid.STATISTIC) / vt.total_statistic * 100, 2) AS statisticP
        FROM
        TF_DWS_VOC_QUALITY_USER vid
        LEFT JOIN
        <include refid="publicDateFilterCriteria.voc_brand_region_max" /> vbr
        ON vid.province = vbr.province_code
        AND vid.brand_code = vbr.brand
        <if test="model.brandCode != null and model.brandCode != 'A11'">
            AND vbr.application_type = 2
        </if>
        <if test="model.brandCode != null and model.brandCode != ''">
            AND vid.brand_code = #{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date" />
        <if test="model.topicCode != null and model.topicCode != ''">
            AND vid.TOPIC_CODE = #{model.topicCode}
        </if>
        <if test="model.createDate != null and model.createDate != ''">
            AND #{model.createDate} >= vid.create_time
        </if>
        CROSS JOIN
        vocquality_total vt
        WHERE
        vbr.regional_code IS NOT NULL
        AND
        vid.dimension_emotion = '负面'
        <include refid="publicDateFilterCriteria.queryProductCom_vid" />
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.this_date" />
        <if test="model.topicCode != null and model.topicCode != ''">
            AND vid.TOPIC_CODE = #{model.topicCode}
        </if>
        <if test="model.createDate != null and model.createDate != ''">
            AND #{model.createDate} >= vid.create_time
        </if>
        GROUP BY
        vbr.regional_code, vt.total_statistic
        ORDER BY
        statistic DESC
    </select>


</mapper>
