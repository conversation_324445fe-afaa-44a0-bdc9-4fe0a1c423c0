package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.risk.BriefingTrendVo;
import com.car.stats.vo.risk.RiskExportResultVo;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface DwdVocDealerRiskMapper extends BaseMapper<DwdVocDealerRisk> {
    IPage<RiskPointAggVo> riskPointAggNew(Page<RiskPointAggVo> page, RiskEventInsightModel model, RiskRuleVo vo);

    List<DwdVocDealerRisk> riskBranchesFiltering(@Param("model")  VocRiskWarningRules riskWarningRules, @Param("rule")  RiskRuleVo vo);
}
