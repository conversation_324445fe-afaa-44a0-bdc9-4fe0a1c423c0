package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.DwsVocQualityEmotionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.FocucLabelDetailModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocQualityEmotionDiMapper.java
 * @Description TODO
 * @createTime 2022年10月18日 16:54
 * @Copyright voc
 */
public interface DwsVocQualityEmotionDiMapper extends BaseMapper<DwsVocQualityEmotionDi> {
    List<QualityLabelVo> qualityProblemTop(@Param("model") FilterCriteriaModel model);

    List<QualityLabelTreeVo> secondDistribution(@Param("model") FilterCriteriaModel model);
    List<QualityLabelTreeVo> distributionProblemParts(@Param("model") ProductQualityFilterCriteriaModel model);
    List<QualityLabelTreeVo> distributionProblemParts2(@Param("model") ProductQualityFilterCriteriaModel model);

    List<QualityLabelTreeVo> threeDistribution(@Param("model") FocucLabelDetailModel model);

    VocOverBriefingValueVo briefingValue(@Param("model") ProductQualityFilterCriteriaModel model);
    VocOverBriefingValueVo briefingTotalValue(@Param("model") ProductQualityFilterCriteriaModel model);

    List<ChannelStatisticVo> sourceChannel(@Param("model")  ProductQualityFilterCriteriaModel model);

    List<SoarProblemVo> soarProblem(@Param("model")  ProductQualityFilterCriteriaModel model);

    List<LabelVo> groupBy4(@Param("model") ProductQualityFilterCriteriaModel model);
    List<LabelVo> groupBy3(@Param("model") ProductQualityFilterCriteriaModel model);
    List<LabelVo> repairGroupBy3(@Param("model") ProductQualityFilterCriteriaModel model);

    List<QualityLabelVo> typicalProblems(@Param("model")  ProductQualityFilterCriteriaModel model);

    List<ChannelVo> channelDistribution(@Param("model") FilterCriteriaModel model);
    List<ChannelVo> channelDistributionProduct(@Param("model") ProductQualityFilterCriteriaModel model);
    List<ChannelVo> channel2DistributionProduct(@Param("model") ProductQualityFilterCriteriaModel model);

    List<CommonProblemsVo> commonProblems(@Param("model") ProductQualityFilterCriteriaModel model);

    List<SeverityRatioVo> severityRatio(@Param("model") ProductQualityFilterCriteriaModel model);

    ComponentTrendPartVo componentTrendParts(@Param("model")  ProductQualityFilterCriteriaModel model);

    ComponentTrendPartVo componentTrendRepair(@Param("model")  ProductQualityFilterCriteriaModel model);

    BigDecimal commonProblemsNumParts(@Param("model") ProductQualityFilterCriteriaModel model, @Param("topicLabelStr") String topicLabelStr);

    List<QualityLabelTreeVo> groupBy2(@Param("model") ProductQualityFilterCriteriaModel model);
    List<QualityLabelTreeVo> groupBy1(@Param("model") ProductQualityFilterCriteriaModel model);

    List<ChannelVo> dataSourceDistribution(@Param("model")  ProductQualityFilterCriteriaModel model);

    BigDecimal commonProblemsTotal(@Param("model") ProductQualityFilterCriteriaModel model);

    List<QualityLabelTreeVo> distributionProblem3(@Param("model")  ProductQualityFilterCriteriaModel model);
    List<QualityLabelTreeVo> repairDistributionProblem3(@Param("model")  ProductQualityFilterCriteriaModel model);

    List<QualityLabelTreeVo> distributionProblem4(@Param("model") ProductQualityFilterCriteriaModel model);

    List<QualityLabelTreeVo> repairGroupBy2(@Param("model") ProductQualityFilterCriteriaModel model);

    List<QualityLabelTreeVo> repairDistributionProblem2(@Param("model")  ProductQualityFilterCriteriaModel model);

    List<QualityLabelTreeVo> repairThreeDistribution(@Param("model") FocucLabelDetailModel modelt);
}
