package com.car.stats.mapper.wo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.ProportionCarSeriesVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VehicleRescueMapper extends BaseMapper<WoOriginalData> {

    StatisticVo customerSatisfaction(@Param("model") FilterCriteriaModel model);

    WoRescueVolumeVo rescueStatus(@Param("model") FilterCriteriaModel model);

    List<WoRescueVolumeVo> rescueStatusTrend(@Param("model") FilterCriteriaModel model);

    List<ProportionCarSeriesVo> carSeriesTop(@Param("model") FilterCriteriaModel model);

    List<ProportionCarSeriesVo> carModelTop(@Param("model") FilterCriteriaModel model);

    List<BranchesVo> outletsTop(@Param("model") FilterCriteriaModel model);

    Page<WoRescueVo> workOrderDetailsList(IPage<WoRescueVo> page, @Param("model") FilterCriteriaModel model);

    List<WoCustomerTypeProportionVo> customerType(@Param("model") FilterCriteriaModel model);

    List<WoReceiverVo> receiver(@Param("model") FilterCriteriaModel model);
}
