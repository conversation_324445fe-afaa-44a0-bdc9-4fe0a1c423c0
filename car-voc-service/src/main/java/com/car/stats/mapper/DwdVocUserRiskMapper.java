package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DwdVocUserRiskMapper.java
 * @Description TODO
 * @createTime 2022年12月21日 11:11
 * @Copyright voc
 */
public interface DwdVocUserRiskMapper extends BaseMapper<DwdVocUserRisk> {
    IPage<ComplaintUserVo> complaintUserList(Page<ComplaintUserVo> page, @Param("model") ComplaintUserTopModel model,@Param("rule") RiskRuleVo rule);
    IPage<ComplaintUserVo> complaintUserList1(Page<ComplaintUserVo> page, @Param("model") ComplaintUserTopModel model);

    IPage<ComplaintUserVo> complaintUserListTop(Page<ComplaintUserVo> page,  @Param("model") ComplaintUserTopModel model);

    BigDecimal getComplaintUser(@Param("model")  FilterCriteriaModel model,@Param("rule") RiskRuleVo rule);

    List<DwdVocUserRisk> riskUserFiltering(@Param("model") VocRiskWarningRules model,@Param("rule") RiskRuleVo rule);

    List<DwdVocUserRisk> riskUserFilteringNew(@Param("model") VocRiskWarningRules riskWarningRules,@Param("rule") RiskRuleVo vo);
}
