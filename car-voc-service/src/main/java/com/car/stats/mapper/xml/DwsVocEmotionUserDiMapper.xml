<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocEmotionUserDiMapper">

    <select id="regionalDistribution" resultType="com.car.stats.vo.RegionUserVo">
        SELECT
            sf.*,
            di.ITEM_TEXT as regionStr
        FROM
            (
                SELECT
                    PROVINCE as regionCode,
                    count( DISTINCT USER_ID ) as userNum,
                    sum( STATISTIC ) as statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY
                    PROVINCE
                order by statistic desc
            ) sf
                LEFT JOIN SYS_DICT_ITEM di ON sf.regionCode=di.ITEM_VALUE
        WHERE
            1=1
          AND di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'



    </select>
        <!-- =======================聚焦关注大区排行 ============================================-->
    <select id="focusRegionalTop" resultType="com.car.stats.vo.RegionUserVo">
                SELECT
                    vbr.regional_code as regionCode,
                    count( DISTINCT USER_ID ) as userNum,
                    sum( STATISTIC ) as statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                    LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_max" />  vbr on vid.province =vbr.province_code
                WHERE
                    1=1
                and vid.brand_code =vbr.brand
                <if test="model.brandCode != null and model.brandCode != 'A11'">
                    and vbr.application_type =2
                </if>
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY
                vbr.regional_code
                order by statistic desc

    </select>
    <!-- =======================聚焦关注小区排行 ============================================-->
    <select id="focusCommunityTop" resultType="com.car.stats.vo.RegionUserVo">
                SELECT
                    community_code as regionCode,
                    count( DISTINCT USER_ID ) as userNum,
                    sum( STATISTIC ) as statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                    LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_min" />  vbr on vid.city =vbr.city_code
                WHERE
                    1=1
                and vid.brand_code =vbr.brand
                and vbr.city_code is not null
                <if test="model.brandCode != null and model.brandCode != 'A11'">
                    and vbr.application_type =2
                </if>
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY
                vbr.community_code
                order by statistic desc

    </select>
    <select id="eventUserStatictis" resultType="com.car.stats.vo.IntentionTrendVo">
        select b.dataStr,IFNULL(total,0) total,IFNULL(userCount,0) userCount FROM
        (<choose>
        <when test="model.dateUnit !=null and model.dateUnit ==-1 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_EMOTION_USER vid
            WHERE 1=1
            and vid.DIMENSION_EMOTION = '负面'
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.intention_type"/>
            <include refid="ComFilterMapper.wo-dlr"/>
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT DATE_FORMAT(tdd.FORMAT_DATE, '%Y-%m-%d') dataStr FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY DATE_FORMAT(tdd.FORMAT_DATE, '%Y-%m-%d')) b
            ON a.dataStr = b.dataStr
            order by b.dataStr
        </when>
        <when test="model.dateUnit !=null and model.dateUnit ==0 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_EMOTION_USER vid
            WHERE 1=1
            and vid.DIMENSION_EMOTION = '负面'
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT concat(tdd.DATE_WEEK_YEAR,'-',TDD.DATE_WEEK) dataStr,min(tdd.format_date) format FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_WEEK_YEAR, TDD.DATE_WEEK) b
            ON a.dataStr = b.dataStr
            order by b.format
        </when>
        <when test="model.dateUnit !=null and model.dateUnit ==1 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_EMOTION_USER vid
            WHERE 1=1
            and vid.DIMENSION_EMOTION = '负面'
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT concat(tdd.DATE_YEAR,'-',TDD.DATE_MONTH) dataStr,min(tdd.format_date) format FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_YEAR, TDD.DATE_MONTH) b
            ON a.dataStr = b.dataStr
            order by b.format
        </when>
        <when test="model.dateUnit !=null and model.dateUnit ==2 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_EMOTION_USER vid
            WHERE 1=1
            and vid.DIMENSION_EMOTION = '负面'
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT concat(tdd.DATE_YEAR,'-',TDD.DATE_QUARTER) dataStr,min(tdd.format_date) format FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_YEAR,TDD.DATE_QUARTER) b
            ON a.dataStr = b.dataStr
            order by b.format
        </when>
        <otherwise>
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_EMOTION_USER vid
            WHERE 1=1
            and vid.DIMENSION_EMOTION = '负面'
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT tdd.DATE_YEAR dataStr FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_YEAR) b
            ON a.dataStr = b.dataStr
            order by b.dataStr
        </otherwise>
    </choose>
    </select>
 <select id="provinceMap" resultType="com.car.stats.vo.RegionUserVo">
        SELECT
            sf.*,
            di.ITEM_TEXT as regionStr
        FROM
            (
                SELECT
                    PROVINCE as regionCode,
                    count( DISTINCT USER_ID ) as userNum,
                    sum( STATISTIC ) as statistic,
                 ROUND(COUNT(DISTINCT USER_ID) / SUM(COUNT(DISTINCT USER_ID)) OVER () * 100, 2) AS userNumP,
                 ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY
                    PROVINCE
                order by statistic desc
            ) sf
                LEFT JOIN SYS_DICT_ITEM di ON sf.regionCode=di.ITEM_VALUE
        WHERE
            1=1
          AND di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'



    </select>

    <select id="regionalUser" resultType="com.car.stats.vo.RegionUserVo">
        SELECT
        area.*,
        di.ITEM_TEXT AS regionStr
        FROM
        (
        SELECT
        pa.AREA_CODE AS regionCode,
        vid.PROVINCE,
        count( DISTINCT USER_ID ) AS userNum,
        sum( STATISTIC ) AS statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN TC_PROVINCE_AREA pa ON vid.PROVINCE = pa.PROVINCE_CODE
        WHERE
        1 = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        pa.AREA_CODE,vid.PROVINCE
        ORDER BY userNum DESC
        ) area
        LEFT JOIN SYS_DICT_ITEM di ON area.regionCode=di.ITEM_VALUE
        WHERE
        1=1
        AND di.DICT_ID='4b82677b6c1408df4be21ada9a584fde'
        order by area.userNum desc
    </select>
 <select id="regionUser_old" resultType="com.car.stats.vo.RegionUserVo">
        SELECT
        vid.AREA_CODE AS regionCode,
        count( DISTINCT USER_ID ) AS userNum,
        sum( STATISTIC ) AS statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1 = 1
         <if test="model.brandCode !=null and model.brandCode !=''" >
             AND vid.brand_code =#{model.brandCode}
         </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.AREA_CODE
        ORDER BY userNum DESC

    </select>

 <select id="regionUser" resultType="com.car.stats.vo.RegionUserVo">
        SELECT
        area.*,
     di.ITEM_TEXT AS regionStr
     FROM
        (
        SELECT
        vid.AREA_CODE AS regionCode,
        count( DISTINCT USER_ID ) AS userNum,
        sum( STATISTIC ) AS statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1 = 1
         <if test="model.brandCode !=null and model.brandCode !=''" >
             AND vid.brand_code =#{model.brandCode}
         </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.AREA_CODE
        ORDER BY userNum DESC
        ) area
        LEFT JOIN SYS_DICT_ITEM di ON area.regionCode=di.ITEM_VALUE
        WHERE
        1=1
         AND di.DICT_ID='4b82677b6c1408df4be21ada9a584fde'
         and di.ITEM_VALUE != '0000000'
         order by area.userNum desc
    </select>

    <select id="topVoiceUsers" resultType="com.car.stats.vo.TopVoiceUsersVo">
        WITH user_stats AS (
        SELECT
            USER_ID AS userId,
            max(vid.display_name) as userName,
            SUM(STATISTIC) AS statistic,
            SUM(CASE WHEN DIMENSION_EMOTION = '正面' THEN STATISTIC ELSE 0 END) AS positive,
            SUM(CASE WHEN DIMENSION_EMOTION = '负面' THEN STATISTIC ELSE 0 END) AS negative,
            CASE WHEN MIN(data_source) = 'Customer-data' THEN 1 ELSE 0 END AS isOneId
            FROM tf_dws_voc_emotion_user vid
        WHERE
            1=1
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY USER_ID
        ),
        ranked_topics AS (
        SELECT
            USER_ID AS userId,
            topic_code AS most_common_topic_code,
            ROW_NUMBER() OVER (PARTITION BY USER_ID ORDER BY SUM(STATISTIC) DESC) AS rn
        FROM tf_dws_voc_emotion_user vid
        WHERE
            1=1
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY USER_ID, topic_code
        )
        SELECT
            u.userId,
            u.userName,
            u.statistic,
            u.positive,
            u.negative,
            u.isOneId,
            rt.most_common_topic_code AS topicCode
        FROM user_stats u
        JOIN ranked_topics rt ON u.userId = rt.userId and rt.rn = 1
        ORDER BY u.statistic DESC
        <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 20
        </if>

    </select>

    <select id="emotionPropUser" resultType="com.car.stats.vo.EmotionProportionVo">
        SELECT
            SUM(vid.STATISTIC) AS total,
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral
        FROM
            TF_DWS_VOC_EMOTION_USER vid
        WHERE
            1=1
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            and vid.USER_ID=#{userId}
    </select>

    <select id="intentionsUser" resultType="com.car.stats.vo.IntentionRatioVo">
        SELECT
            SUM(vid.STATISTIC) as total,
            sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,
            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
            sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise
        FROM
            TF_DWS_VOC_EMOTION_USER vid
        WHERE
            1=1
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            and vid.USER_ID=#{userId}
    </select>
     <select id="channelDistr" resultType="com.car.stats.vo.ChannelVo">
                 SELECT
                 vid.CHANNEL_ID as channelId,
                 SUM(vid.STATISTIC) as statistic
                 FROM
                 TF_DWS_VOC_EMOTION_USER vid
                 where 1=1
                 <if test="model.brandCode !=null and model.brandCode !=''" >
                     AND vid.brand_code =#{model.brandCode}
                 </if>
                 and vid.USER_ID=#{userId}
                 GROUP BY
                 vid.CHANNEL_ID
        </select>
     <select id="hotWordsUser" resultType="com.car.stats.vo.HighHotWordsVo">
                 select te.*
                 from
                    (
                        SELECT
                            vid.EMOTION_KEYWORD as keyword,
                            vid.DIMENSION_EMOTION as emotionType,
                            SUM(vid.STATISTIC) as statistic
                        FROM
                     <choose>
                         <when test="model.menuName !=null and model.menuName =='productQuality'">
                             TF_DWS_VOC_QUALITY_USER vid
                         </when>
                         <otherwise>
                             TF_DWS_VOC_EMOTION_USER vid
                         </otherwise>
                     </choose>
                        where 1=1
                         <if test="model.brandCode !=null and model.brandCode !=''" >
                             AND vid.brand_code =#{model.brandCode}
                         </if>
                          and vid.USER_ID=#{model.userId}
                          and vid.EMOTION_KEYWORD is not null
                    <include refid="ComFilterMapper.this_date"/>

                     GROUP BY
                            vid.EMOTION_KEYWORD,vid.DIMENSION_EMOTION
                        ORDER BY statistic desc
                        ) te
                    where 1=1 limit 50
        </select>


    <select id="intentionUserTrends"   resultType="com.car.stats.vo.DateUserStatisticVo">
        SELECT
        count(DISTINCT USER_ID) AS userNum,
        sum( vid.STATISTIC ) AS statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1 = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
    </select>


    <select id="riskCarSeries"   resultType="java.lang.String">

        SELECT
        vid.CAR_SERIES_CODE
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
            and vid.TOPIC_CODE=#{model.topicCode}
          AND vid.CAR_SERIES_CODE is NOT NULL
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        GROUP BY vid.CAR_SERIES_CODE
        order by case when CAR_SERIES_CODE like '%A00' then 1 else 0 end , sum(vid.STATISTIC) desc
    </select>

    <select id="riskCarSeriesExport"   resultType="java.lang.String">

        SELECT
        car.name
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join voc_brand_product_manager car on vid.CAR_SERIES_CODE=car.code
        WHERE
        1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
            and vid.TOPIC_CODE=#{model.topicCode}
          AND vid.CAR_SERIES_CODE is NOT NULL
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        GROUP BY car.name
        order by sum(vid.STATISTIC) desc
    </select>


    <select id="riskCarSeriesStr"   resultType="java.lang.String">

        SELECT
        concat( vbpm.name ,'(',sum(vid.STATISTIC),')')
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join voc_brand_product_manager vbpm on vid.car_series_code =vbpm .code
        WHERE
        1=1
            and vid.TOPIC_CODE=#{model.topicCode}
            and vid.DIMENSION_EMOTION='负面'
          AND vid.CAR_SERIES_CODE is NOT NULL
          AND vbpm.name is NOT NULL
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        <if test="model.riskUserId != null and model.riskUserId != '' ">
            AND vid.user_id = #{model.riskUserId}
        </if>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        GROUP BY vid.CAR_SERIES_CODE
        order by case when CAR_SERIES_CODE like '%A00' then 1 else 0 end , sum(vid.STATISTIC) desc



    </select>



 <select id="riskCarSeriesByUserId"   resultType="java.lang.String">

        (
        SELECT
            usercar.*
        FROM
        (
        SELECT
        vid.CAR_SERIES_CODE
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
             <if test="model.userId !=null and model.userId != ''">
                 and vid.USER_ID=#{model.userId}
             </if>
             <if test="model.brandCode !=null and model.brandCode !=''" >
                 AND vid.brand_code =#{model.brandCode}
             </if>
             <include refid="ComFilterMapper.this_date"/>
                 AND vid.CAR_SERIES_CODE is NOT NULL
            and vid.DIMENSION_EMOTION ='负面'
            GROUP BY vid.CAR_SERIES_CODE
        ) usercar
        WHERE
            1=1
        limit 3
        )

    </select>

 <select id="userStatisticTotal"   resultType="java.math.BigDecimal">
     SELECT
     SUM(vid.STATISTIC) statistic
     FROM
     TF_DWS_VOC_EMOTION_USER vid
     WHERE
     1=1
     <if test="model.userId !=null and model.userId != ''">
         and vid.USER_ID=#{model.userId}
     </if>
     <if test="model.brandCode !=null and model.brandCode !=''" >
         AND vid.brand_code =#{model.brandCode}
     </if>
     AND vid.DIMENSION_EMOTION ='负面'


 </select>

<select id="riskAggProblemByUserId"   resultType="java.lang.String">

        (
        SELECT
            usertop.*
        FROM
        (
        SELECT
        vid.TOPIC_CODE
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <if test="model.userId !=null and model.userId != ''">
            and vid.USER_ID=#{model.userId}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="ComFilterMapper.this_date"/>
                AND vid.TOPIC_CODE is NOT NULL
            and vid.DIMENSION_EMOTION ='负面'
            GROUP BY vid.TOPIC_CODE
        ) usertop
        where 1=1
            limit 10
        )

    </select>

<select id="riskHotWordsByUserId"   resultType="com.car.stats.vo.HighHotWordsVo">

        (
        SELECT
            userkey.*
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD as  keyword,
        SUM(vid.STATISTIC) statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
            <if test="model.userId !=null and model.userId != ''">
                and vid.USER_ID=#{model.userId}
            </if>
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            <include refid="ComFilterMapper.this_date"/>
            AND vid.EMOTION_KEYWORD is NOT NULL
            and vid.DIMENSION_EMOTION ='负面'
            GROUP BY vid.EMOTION_KEYWORD
        ) userkey
        where 1=1
            limit 10
        )

    </select>

<select id="riskWarningUserNum"   resultType="java.lang.Long">
    select count(distinct vid.USER_ID)
    from TF_DWS_VOC_EMOTION_USER vid
    where
    vid.TOPIC_CODE=#{model.topicCode}
    <include refid="ComFilterMapper.general_query_dataSorce" />

    and vid.DIMENSION_EMOTION='负面'

    <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
        and vid.PUBLISH_DATE>=#{model.startDate}
        AND #{model.endDate}>=vid.PUBLISH_DATE
    </if>
    <if test="model.brandCode !=null and model.brandCode !=''" >
        AND vid.brand_code =#{model.brandCode}
    </if>
    <if test="model.createDate !=null and model.createDate != '' ">
        AND #{model.createDate}>=vid.create_time
    </if>

    </select>


<select id="riskBranchesWarningUserNum"   resultType="java.lang.Long">
    select count(distinct vid.USER_ID)
    from TF_DWS_VOC_EMOTION_USER vid
    where
    1=1
    <include refid="ComFilterMapper.general_query_dataSorce" />
    <include refid="ComFilterMapper.intention_type"/>
    <include refid="ComFilterMapper.wo-dlr"/>
    and vid.DIMENSION_EMOTION='负面'

    <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
        and vid.PUBLISH_DATE>=#{model.startDate}
        AND #{model.endDate}>=vid.PUBLISH_DATE
    </if>
    <if test="model.brandCode !=null and model.brandCode !=''" >
        AND vid.brand_code =#{model.brandCode}
    </if>
    <if test="model.createDate !=null and model.createDate != '' ">
        AND #{model.createDate}>=vid.create_time
    </if>

    </select>


    <select id="riskHotWordsOpinion"   resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
            tab.EMOTION_KEYWORD as keyword,
            tab.statistic
        FROM
            (
                SELECT
                    vid.EMOTION_KEYWORD,
                    SUM(vid.STATISTIC) statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <include refid="ComFilterMapper.general_query_dataSorce" />
                <include refid="ComFilterMapper.emotion_type"/>
                <include refid="ComFilterMapper.intention_type"/>
                  AND vid.EMOTION_KEYWORD IS NOT NULL
                <if test="model.topicCode !=null and model.topicCode !=''" >
                    AND vid.TOPIC_CODE =#{model.topicCode}
                </if>
                <include refid="ComFilterMapper.wo-dlr"/>

                <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
                    and vid.PUBLISH_DATE>=#{model.startDate}
                    AND #{model.endDate}>=vid.PUBLISH_DATE
                </if>
                <if test="model.riskUserId != null and model.riskUserId != '' ">
                    AND vid.user_id = #{model.riskUserId}
                </if>
                <include refid="ComFilterMapper.com_risk_brand_createDate"/>
                   and vid.DIMENSION_EMOTION='负面'
                GROUP BY vid.EMOTION_KEYWORD
                ORDER BY statistic DESC,vid.EMOTION_KEYWORD desc
            ) tab
        WHERE
            1=1
          limit 10

    </select>


    <select id="riskStatisticTotal"   resultType="java.math.BigDecimal">
                SELECT
                    SUM(vid.STATISTIC) statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <if test="model.topicCode !=null and model.topicCode !=''" >
                    AND vid.TOPIC_CODE =#{model.topicCode}
                </if>
                <include refid="ComFilterMapper.general_query_dataSorce" />

                <include refid="ComFilterMapper.emotion_type"/>
                <include refid="ComFilterMapper.intention_type"/>
                <include refid="ComFilterMapper.wo-dlr"/>
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>



                AND vid.DIMENSION_EMOTION='负面'
    </select>



    <select id="riskUserTotalNum"   resultType="java.math.BigDecimal">
                SELECT
                    count(distinct vid.USER_ID) statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <if test="model.topicCode !=null and model.topicCode !=''" >
                    AND vid.TOPIC_CODE =#{model.topicCode}
                </if>
                <include refid="ComFilterMapper.general_query_dataSorce" />
                <include refid="ComFilterMapper.emotion_type"/>
                <include refid="ComFilterMapper.intention_type"/>
                <include refid="ComFilterMapper.wo-dlr"/>
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>


                and vid.DIMENSION_EMOTION='负面'
    </select>


    <select id="homeChannelStrs"   resultType="java.lang.String">
                select
                chId.CHANNEL_ID as NAME
                from
                (
                SELECT
                vid.CHANNEL_ID
                FROM
                TF_DWS_VOC_EMOTION_USER vid
                WHERE
                1=1
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>
                <include refid="publicDateFilterCriteria.querychan_vid" />
                group by  vid.CHANNEL_ID
                    ) chId
                left join  voc_channel_category ch  on chId.CHANNEL_ID=ch.ID


    </select>




    <select id="userNum" resultType="java.util.Map">

        SELECT
            tabThis.userCount AS "userCount",
            tabUp.userCount as "userCountUp"
               FROM
            (SELECT
                 COUNT(DISTINCT USER_ID) AS userCount
             FROM
                 TF_DWS_VOC_EMOTION_USER vid
             WHERE
                 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />

                )  tabThis,

            (SELECT
                 COUNT(DISTINCT USER_ID) AS userCount
             FROM
                 TF_DWS_VOC_EMOTION_USER vid
             WHERE
                 1=1
             <include refid="publicDateFilterCriteria.queryCom_vid_up" />

                ) tabUp
    </select>


    <select id="userDistinctNum" resultType="java.math.BigDecimal">

        SELECT
            tabThis.userCount AS "userCount"
               FROM
            (SELECT
                 COUNT(DISTINCT USER_ID) AS userCount
             FROM
                 TF_DWS_VOC_EMOTION_USER vid
             WHERE
                 1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
            )  tabThis


    </select>

    <select id="riskUserNum" resultType="java.util.Map">

       SELECT
        tabThis.userCount AS "userCount",
        tabUp.userCount as "userCountUp"
        FROM
        (SELECT
        COUNT(DISTINCT USER_ID) AS userCount
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        )  tabThis,

        (SELECT
        COUNT(DISTINCT USER_ID) AS userCount
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.up_date" />
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        ) tabUp
    </select>

    <select id="homeBriefingValue" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="java.util.Map">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UPMENT as "upMent"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where
        1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
        ) tabUp

    </select>
<select id="VipUser" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="java.util.Map">

        SELECT
        tabThis.thisMent AS "vipUser",
        tabUp.UPMENT as  "UpMent"
        FROM
        (SELECT
        COUNT(DISTINCT USER_ID) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        AND vid.USER_LEVEL=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        )  tabThis,

        (SELECT
        COUNT(DISTINCT USER_ID) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where
        1=1
        AND vid.USER_LEVEL=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
        ) tabUp

    </select>


    <select id="overviewBriefingValue" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.VocOverBriefingValueVo">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where  1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
        ) tabUp

    </select>

    <select id="overviewBriefingTotalValue" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.VocOverBriefingValueVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers ">
            count( distinct vid.USER_ID) as totalMentions
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention ">
            SUM(STATISTIC) as totalMentions
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />


    </select>


    <select id="riskBriefingValue"  resultType="com.car.stats.vo.VocOverBriefingValueVo">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="ComFilterMapper.general_query_dataSorce"/>
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.topicCode !=null and model.topicCode !=''">
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.riskUserId != null and model.riskUserId != '' ">
            AND vid.user_id = #{model.riskUserId}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where  1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.up_date" />
        <include refid="ComFilterMapper.wo-dlr"/>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>

        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.riskUserId != null and model.riskUserId != '' ">
            AND vid.user_id = #{model.riskUserId}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        ) tabUp

    </select>

    <select id="riskAllTotal"  resultType="java.util.Map">

        SELECT
        SUM(STATISTIC) as statisticTotal,
        count(distinct vid.user_id ) as userTotal
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <if test="model.riskUserId != null and model.riskUserId != '' ">
            AND vid.user_id = #{model.riskUserId}
        </if>
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>

    </select>



    <select id="sourceChannel" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.ChannelStatisticVo">
        SELECT
        vid.CHANNEL_ID as dataSource,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.querychan_vid" />
        GROUP BY
        vid.CHANNEL_ID
        ORDER BY statistic desc

    </select>
    <select id="channelDistribution" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.ChannelUserVo">
        select *
        from (
        SELECT
        vid.CHANNEL_ID as dataSource,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.CHANNEL_ID
        ORDER BY statistic desc
                 )
        where 1=1 limit 10
    </select>
    <select id="channelDistributionChUser" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.ChannelUserVo">
        select *
        from (
        SELECT
        vid.CHANNEL_ID as dataSource,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.CHANNEL_ID
        ORDER BY statistic desc
                 ) t
        where 1=1
          limit 10
    </select>
<select id="dataSourceDistribution" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.ChannelUserVo">
        select *
        from (
        SELECT
        vid.DATA_SOURCE as dataSource,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        and
        (vid.DATA_SOURCE !='1372001238165561345'
        and vid.DATA_SOURCE!='1372001238165321313')

    <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.DATA_SOURCE
        ORDER BY statistic desc
                 )
        where 1=1 limit 10
    </select>

<select id="dataSourceDistributionChUser" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.ChannelUserVo">
        select *
        from (
        SELECT
        vid.DATA_SOURCE as dataSource,
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        and
        (vid.DATA_SOURCE !='1372001238165561345'
        and vid.DATA_SOURCE!='1372001238165593852')

    <include refid="publicDateFilterCriteria.queryCom_vid" />
        GROUP BY
        vid.DATA_SOURCE
        ORDER BY statistic desc
                 ) t
        where 1=1 limit 10
    </select>



    <select id="eventEmotionIntention_old"  resultType="com.car.stats.vo.risk.EmotionIntentionVo">


        select
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr,
        sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        from
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        <if test="model.riskName !=null and model.riskName !=''" >
            AND vid.TOPIC_CODE =#{model.riskName}
        </if>
        <include refid="ComFilterMapper.this_date" />
        GROUP BY
        <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        order by dateStr asc
    </select>

<select id="eventEmotionIntention"  resultType="com.car.stats.vo.risk.EmotionIntentionVo">


    <include refid="publicDateFilterCriteria.groupby-date-com-all-data-airing"/>
       (
    select
    <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr1,
    sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
    sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
    sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
    from
    TF_DWS_VOC_EMOTION_USER vid
    WHERE 1=1
    <include refid="ComFilterMapper.general_query_dataSorce" />

    <if test="model.riskName !=null and model.riskName !=''" >
        AND vid.TOPIC_CODE =#{model.riskName}
    </if>
    <include refid="ComFilterMapper.wo-dlr"/>
    <include refid="ComFilterMapper.this_date" />
    GROUP BY
    <include refid="publicDateFilterCriteria.groupby-com-cycle" />
    order by dateStr1 asc
       ) datay on dateCycle.dateStr=datay.dateStr1
    </select>

<select id="eventEmotionIntention1"  resultType="com.car.stats.vo.risk.EmotionIntentionVo">

        select *
        from
        ( <include refid="publicDateFilterCriteria.dayGroupCompletion"/>) dgc
        left join
        (select
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dated,
        sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        from
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        <if test="model.riskName !=null and model.riskName !=''" >
            AND vid.TOPIC_CODE =#{model.riskName}
        </if>
        <include refid="ComFilterMapper.this_date" />
        GROUP BY
        <include refid="publicDateFilterCriteria.groupby-com-cycle" />


        ) nc on dgc.dateStr=nc.dated
        ORDER BY dgc.dateStr asc
    </select>

    <select id="eventChangeChannel"  resultType="com.car.stats.vo.ChannelVo">

        <include refid="publicDateFilterCriteria.groupby-date-com-all-data-airing"/>
        (
        select
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dateStr1,
        vid.CHANNEL_ID as channelId,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS statistic
        from
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <if test="model.riskName !=null and model.riskName !='' and model.dlrName == null " >
            AND vid.TOPIC_CODE =#{model.riskName}
        </if>
        <include refid="ComFilterMapper.wo-dlr"/>
        <include refid="ComFilterMapper.this_date" />
        GROUP BY
        <include refid="publicDateFilterCriteria.groupby-com-cycle" />,
        vid.CHANNEL_ID
        order by dateStr1 asc
        ) datay on dateCycle.dateStr=datay.dateStr1
        </select>
<select id="eventChangeChannel1"  resultType="com.car.stats.vo.ChannelVo">

            select *
            from
            ( <include refid="publicDateFilterCriteria.dayGroupCompletion"/>) dgc
            left join
            (select
            <include refid="publicDateFilterCriteria.groupby-com-cycle" /> as dated,
            vid.CHANNEL_ID as channelId,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS statistic
            from
            TF_DWS_VOC_EMOTION_USER vid
            WHERE 1=1
            <if test="model.riskName !=null and model.riskName !=''" >
                AND vid.TOPIC_CODE =#{model.riskName}
            </if>
            <include refid="ComFilterMapper.this_date" />
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle" />,
            vid.CHANNEL_ID
            ) nc on dgc.dateStr=nc.dated
            ORDER BY dgc.dateStr asc
        </select>

    <select id="eventHotWords"  resultType="com.car.stats.vo.HighHotWordsVo">

           select
            *
            from
            (
            select
            vid.EMOTION_KEYWORD as keyword,
            sum(vid.STATISTIC) as  statistic
            from
            TF_DWS_VOC_EMOTION_USER vid
            where
            1=1
            <include refid="ComFilterMapper.general_query_dataSorce" />
                and vid.EMOTION_KEYWORD is not null
            <if test="model.riskName !=null and model.riskName !='' and model.dlrName == null " >
                AND vid.TOPIC_CODE =#{model.riskName}
            </if>
            <include refid="ComFilterMapper.wo-dlr"/>
            <if test="model.endDate !=null and model.endDate != ''">
                AND #{model.endDate}>=vid.PUBLISH_DATE
            </if>
            <if test="model.startDate !=null and model.startDate != ''">
                AND vid.PUBLISH_DATE>=#{model.startDate}
            </if>

             group by vid.EMOTION_KEYWORD
                order by statistic desc
                ) te
            where
            1=1  limit 50


    </select>
    <select id="dataDryingContrast"  resultType="com.car.stats.vo.risk.DataDryingContrastVo">

            select
                <if test="type !=null and type == 1 " >
                    CEILING(te.userNum / date) as userNum,
                    CEILING(te.complaint / date) as complaint,
                    CEILING(te.negative / date) as negative
                </if>
                <if test="type !=null and type == 0 " >
                   *
                </if>
            from
            (
            select
            <include refid="dataDryingContrast_dateUnit"></include>
            count(distinct vid.USER_ID) as userNum,
            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
            from
            TF_DWS_VOC_EMOTION_USER vid
            where
            1=1
            <include refid="ComFilterMapper.general_query_dataSorce" />
              and vid.DIMENSION_EMOTION='负面'
            <include refid="ComFilterMapper.wo-dlr"/>
            <if test="model.riskName !=null and model.riskName !='' and model.dlrName == null" >
                AND vid.TOPIC_CODE =#{model.riskName}
            </if>
            <if test="model.endDate !=null and model.endDate != ''">
                AND #{model.endDate}>=vid.PUBLISH_DATE
            </if>
            <if test="model.startDate !=null and model.startDate != ''">
                AND vid.PUBLISH_DATE>=#{model.startDate}
            </if>
                ) te
            where
            1=1


    </select>
    <select id="riskTrend"  resultType="java.util.Map">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="ComFilterMapper.this_date" />
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
          and vid.DIMENSION_EMOTION='负面'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where  1=1
        <include refid="ComFilterMapper.up_date" />
        <include refid="ComFilterMapper.com_risk_brand_createDate"/>
        and vid.DIMENSION_EMOTION='负面'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        ) tabUp

    </select>
    <select id="listRiskTrend"  resultType="java.util.Map">

        SELECT
        tabThis.topicCode as topicCode,
        tabThis.dateStr as dateStr,
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        vid.TOPIC_CODE as topicCode,
        <include refid="publicDateFilterCriteria.groupby-com-cycle"/> as dateStr,
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="ComFilterMapper.this_date" />
          and vid.DIMENSION_EMOTION='负面'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        group by TOPIC_CODE, <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
        )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where  1=1
        <include refid="ComFilterMapper.up_date" />
        and vid.DIMENSION_EMOTION='负面'
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        ) tabUp

    </select>

    <select id="lastTagHotWords"  resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        *
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        COUNT(distinct vid.USER_ID) as userCount,
        sum(vid.STATISTIC) as statistic,
        (sum(vid.STATISTIC))/total_count.agecount as statisticP,
        (count(distinct vid.USER_ID))/total_count.ageUser as userCountP
        FROM
        TF_DWS_VOC_EMOTION_USER vid,
        (SELECT
            COUNT(DISTINCT vid.USER_ID) as ageUser,
            sum(vid.STATISTIC) AS agecount
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />) as total_count
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.hotWords != null and model.hotWords.size()>0">
            and vid.EMOTION_KEYWORD in
            <foreach item="item" collection="model.hotWords" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY vid.EMOTION_KEYWORD
        ORDER BY STATISTIC DESC
        ) t
        WHERE
        1=1
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>


 <select id="secondTagDistribution"  resultType="com.car.stats.vo.popvo.UserLabelVo">
         SELECT
         fc.*,
         tn.NAME as labelStr
         FROM
         (
             SELECT
             vid.SECOND_DIMENSION_CODE as labelCode,
             count(distinct vid.USER_ID) as userCount,
             SUM(vid.STATISTIC) as statistic
             FROM
             <choose>
                 <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                     tf_dws_voc_nps_record vid
                 </when>
                 <otherwise>
                     TF_DWS_VOC_EMOTION_USER vid
                 </otherwise>
             </choose>



         WHERE
             1=1
             <include refid="publicDateFilterCriteria.queryPop_vid" />

             GROUP BY vid.SECOND_DIMENSION_CODE
             order by statistic desc
         ) fc
         LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
         where
         1=1
         and tn.NAME is not null
        order by fc.statistic desc
    </select>


     <select id="thirdTagDistribution"  resultType="com.car.stats.vo.popvo.UserLabelVo">

         SELECT
         fc.*,
         tn.NAME as labelStr
         FROM
         (
         SELECT
         vid.THREE_DIMENSION_CODE as labelCode,
         count(distinct vid.USER_ID) as userCount,
         SUM(vid.STATISTIC) as statistic
         FROM
         <choose>
             <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                 tf_dws_voc_nps_record vid
             </when>
             <otherwise>
                 TF_DWS_VOC_EMOTION_USER vid
             </otherwise>
         </choose>
         WHERE
         1=1
         <include refid="publicDateFilterCriteria.queryPop_vid" />

         GROUP BY vid.THREE_DIMENSION_CODE
         order by statistic desc
         ) fc
         LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
         where
         1=1
         and tn.NAME is not null
         order by fc.statistic desc
     </select>

     <select id="topicTagDistribution"  resultType="com.car.stats.vo.popvo.UserLabelVo">

         SELECT
         fc.*,
         tn.NAME as labelStr
         FROM
         (
         SELECT
         vid.TOPIC_CODE as labelCode,
         count(distinct vid.USER_ID) as userCount,
         SUM(vid.STATISTIC) as statistic
         FROM
         <choose>
             <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                 tf_dws_voc_nps_record vid
             </when>
             <otherwise>
                 TF_DWS_VOC_EMOTION_USER vid
             </otherwise>
         </choose>
         WHERE
         1=1
         <include refid="publicDateFilterCriteria.queryPop_vid" />

         GROUP BY vid.TOPIC_CODE
         order by statistic desc
         ) fc
         LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
         where
         1=1
         and tn.NAME is not null
         order by fc.statistic desc
     </select>

	<sql id="queryCom_nps_default_date_filter_group_attrs">
        <if test="model.dateUnit == 0">
            concat(year(startDate), '-',LPAD(week(startDate),2,0) ) as dateUnit
        </if>
        <if test="model.dateUnit == 1">
            concat(year(startDate),'-', LPAD(month (startDate),2,0) ) as dateUnit
        </if>
        <if test="model.dateUnit == 2">
            concat(year(startDate),'-', LPAD(quarter (startDate),2,0) ) as dateUnit
        </if>
        <if test="model.dateUnit == 3">
            concat(year(startDate) ) as dateUnit
        </if>
        <if test="model.dateUnit == -1">
            date_format(startDate , '%Y-%m-%d')  as dateUnit
        </if>
    </sql>

	<sql id="queryCom_nps_default_date_filter_date_between">
		date(publish_date) between
        <if test="model.dateUnit == 0">
			DATE_ADD( date(#{model.endDate}), interval -11 week) and date(#{model.endDate})
        </if>
        <if test="model.dateUnit == 1">
			DATE_ADD( date(#{model.endDate}), interval -11 month) and date(#{model.endDate})
        </if>
        <if test="model.dateUnit == 2">
		DATE_ADD( date(#{model.endDate}), interval -11 quarter) and date(#{model.endDate})
        </if>
        <if test="model.dateUnit == 3">
			DATE_ADD( date(#{model.endDate}), interval -11 year) and date(#{model.endDate})
        </if>
        <if test="model.dateUnit == -1">
			date(#{model.startDate}) and date(#{model.endDate})
        </if>
    </sql>

	<sql id="queryCom_nps_date_filter_with_11">
        with date_filter as (
        select ( @date := DATE_ADD( @date, INTERVAL 1 DAY )) AS startDate ,endDate
        <if test="model.dateUnit == -1">
            FROM tf_dws_voc_nps_supplt,( SELECT @date := DATE_ADD( startDate, INTERVAL - 1 DAY )
        </if>
        <if test="model.dateUnit == 0">
            FROM tf_dws_voc_nps_supplt,( SELECT @date := date(DATE_SUB( DATE_ADD( date(endDate), interval -1 day ), interval 10 week))
        </if>
        <if test="model.dateUnit == 1">
            FROM tf_dws_voc_nps_supplt,( SELECT @date := date(DATE_SUB( DATE_ADD( date(endDate), interval -1 day ), interval 10 month))
        </if>
        <if test="model.dateUnit == 2">
            FROM tf_dws_voc_nps_supplt,( SELECT @date := date(DATE_SUB( DATE_ADD( date(endDate), interval -1 day ), interval 10 quarter))
        </if>
        <if test="model.dateUnit == 3">
            FROM tf_dws_voc_nps_supplt,( SELECT @date := date(DATE_SUB( DATE_ADD( date(endDate), interval -1 day ), interval 10 year))
        </if>
        ,b_.*
        from
        (
        select
        startDate,
        endDate
        from
        ( 	select
        date(#{model.startDate}) as startDate,
        date(#{model.endDate}) as endDate
        ) a_
        ) b_
        ) temp
        WHERE @date &lt; date(endDate)
        )
    </sql>

    <sql id="dataDryingContrast_dateUnit">
        <if test="model.dateUnit == 0">
            case
            when TIMESTAMPDIFF(week,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(week,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == 1">
            case
            when TIMESTAMPDIFF(month,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(month,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == 2">
            case
            when TIMESTAMPDIFF(quarter,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(quarter,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == 3">
            case
            when TIMESTAMPDIFF(year,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(year,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
        <if test="model.dateUnit == -1">
            case
            when TIMESTAMPDIFF(DAY,#{model.startDate},#{model.endDates}) = 0 then 1
            else TIMESTAMPDIFF(DAY,#{model.startDate},#{model.endDates}) end
            as date,
        </if>
    </sql>

     <select id="trendChangeLabel"  resultType="com.car.stats.vo.HomePurposeTrendVo">

		select
			dateUnit,
			replace(intention, ',', '') as intention,
			case
				when intention is null and intention_c is null   then format(0, 2)
				when intention is null and intention_c is not null then replace(format(-intention_c * 100, 2), ',', '')
				when intention is not null and intention_c is null then replace(format(intention * 100, 2), ',', '')
				when intention is not null and intention_c is not null then replace(format((intention - intention_c) /intention_c  * 100 , 2), ',', '')
			end as intentionR,
			replace(userCount, ',', '') as userCount,
			case
				when userCount is null and userCount_c is null   then format(0, 2)
				when userCount is null and userCount_c is not null then replace(format(-userCount_c * 100, 2), ',', '')
				when userCount is not null and userCount_c is null then replace(format(userCount * 100, 2), ',', '')
				when userCount is not null and userCount_c is not null then replace(format((userCount - userCount_c) /intention_c * 100, 2), ',', '')
			end as userCountR
		from (
			select
				intention,
				lag (intention ,1,0) over(order  by dateUnit)  intention_c,
				userCount,
				lag (userCount ,1,0) over(order  by dateUnit)  userCount_c,
				dateUnit
			from
				(
				<include refid="com.car.stats.mapper.DwsVocEmotionUserDiMapper.queryCom_nps_date_filter_with_11"/>
				select
					df.dateUnit,
					intention,
					userCount
				from (
					select
						<include refid="com.car.stats.mapper.DwsVocEmotionUserDiMapper.queryCom_nps_default_date_filter_group_attrs"/>
					from date_filter
					group by dateUnit
				) df
				left join
				(
					select
						sum(STATISTIC) as intention,    /* 33678 */
						COUNT(DISTINCT USER_ID) as userCount,  /* 12204 */
						<include refid="com.car.stats.mapper.DwsVocEmotionUserDiMapper.queryCom_nps_default_date_filter_group_attrs"/>
					from (
						select publish_date, STATISTIC, USER_ID  ,CHANNEL_ID,SECOND_DIMENSION_CODE,AREA_CODE,
							THREE_DIMENSION_CODE,PROVINCE,
							date(publish_date) as startDate
						from TF_DWS_VOC_EMOTION_USER
						where
							<include refid="com.car.stats.mapper.DwsVocEmotionUserDiMapper.queryCom_nps_default_date_filter_date_between"/>
							<if test="model.dataSources != null  and model.dataSources.size() >0">
								AND DATA_SOURCE in
								<foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
									#{item}
								</foreach>
							</if>
					)a group by dateUnit
				) vid on df.dateUnit = vid.dateUnit
				<if test="model.channelIds != null  and model.channelIds.size() >0">
				left join (
				select id ,name from voc_channel_category
					where id in
					<foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				)c1 on c1.id =  CHANNEL_ID
				</if>
				<if test="model.secondDimensionCodes != null  and model.secondDimensionCodes.size() >0 ">
				left join
				(
					select tag_code,name from
					VOC_BUSINESS_TAG tag
					where tag_code in
					<foreach item="item" collection="model.secondDimensionCodes"  separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				)c2 on c2.tag_code = THREE_DIMENSION_CODE
				</if>
				<if test="(model.province != null  and model.province.size() >0)  ">
				left join
				(
					select * from
					TC_PROVINCE_AREA
					where AREA_CODE in
					<foreach item="item" collection="model.province"  separator="," open="(" close=")" index="">
						#{item}
					</foreach>
				)c3 on c3.AREA_CODE = PROVINCE
				</if>
			) f1
			order by dateUnit desc
		)vv
     </select>

     <select id="trendChangeLabelList"  resultType="com.car.stats.vo.HomePurposeTrendVo">
           <include refid="popUserAndStatisticList" />

     </select>
    <select id="trendChangeLabelNew"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        <include refid="popUserAndStatisticNew" />

    </select>
     <select id="trendChangeLabelListNew"  resultType="com.car.stats.vo.HomePurposeTrendVo">
           <include refid="popUserAndStatisticListNew" />

     </select>

     <select id="positiveNegativeTrend"  resultType="com.car.stats.vo.EmotionProportionVo">
         SELECT
         <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr,

         <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
             count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
             count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative,
             count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL  END )) AS neutral,
             COUNT(DISTINCT vid.USER_ID) as total
         </if>
         <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
             sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
             sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
             sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
             sum(vid.STATISTIC) as total
         </if>
         FROM
         TF_DWS_VOC_EMOTION_USER vid
         WHERE
         1=1
         <include refid="publicDateFilterCriteria.queryCom_vid" />
         <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
             and vid.SECOND_DIMENSION_CODE in
             <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                 #{item}
             </foreach>
         </if>
         group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
         ORDER BY dateStr desc
     </select>
 <select id="allPositiveNegativeTrend"  resultType="com.car.stats.vo.EmotionProportionVo">
             <include refid="publicDateFilterCriteria.groupby-com-cycle-sr" />
             SELECT
             *
             FROM (
             SELECT
             cc.*,
             case when length(cc.date_)>9 then date_format(date_, '%Y/%m/%d')
             else cc.date_ end as date_str,
             ifnull(total, 0) AS total,
             ifnull(neutral, 0) AS neutral,
             ifnull(positive, 0) AS positive,
             ifnull(negative, 0) AS negative,
             ifnull(LEAD(total) over(order by date_ desc),0) as total_r,
             ifnull(LEAD(positive) over(order by date_ desc),0) as positive_r,
             ifnull(LEAD(negative) over(order by date_ desc),0) as negative_r
             FROM common_date_tools cc
             LEFT JOIN (
             SELECT
             <include refid="publicDateFilterCriteria.dateFormatCase-Trend" /> AS date_str,
             sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN 1 ELSE 0 END ) AS positive,
             sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN 1 ELSE 0 END ) AS negative,
             sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN 1 ELSE 0 END ) AS neutral,
             count(1) as total
             <include refid="publicDateFilterCriteria.commonDateQuery" />
             <include refid="publicDateFilterCriteria.queryPop_vid_emotion_sr" />
             <if test="model.topicCode !=null and model.topicCode !=''" >
                 AND vid.TOPIC_CODE=#{model.topicCode}
             </if>
             <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
                 AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
             </if>
             GROUP BY date_str
             ) f1 ON cc.date_ = f1.date_str
             ORDER BY date_ DESC
             ) f2
             ORDER BY date_ ASC
     </select>

     <select id="complaintWebsiteTop"  resultType="com.car.stats.vo.ChannelUserVo">
         SELECT
             eb.*,
             ch.NAME as "dataSourceStr"
         FROM
             (
                 SELECT
                     vid.CHANNEL_ID as "dataSource",
                     count(1)  as "statistic",
                     count(distinct vid.ONE_ID) as "userNum"
                 FROM
                    TF_DWD_VOC_SENTENCE vid
                 left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
                 WHERE
                     1=1
             and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
             and vcc.PID='1372001238165561301'
             <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />
                GROUP BY vid.CHANNEL_ID
             ) eb LEFT JOIN voc_channel_category ch ON eb.dataSource=ch.ID
        order by eb.statistic desc
     </select>

     <select id="complaintWebsiteThemeTop"  resultType="com.car.stats.vo.LabelUserVo">
         select
                *
                from (
                 SELECT
                 eb.*,
                 ch.NAME as "labelStr"
                 FROM
                 (
                 SELECT
                 vid.TOPIC_CODE as "labelCode",
                 count(1) as "statistic",
                 count(distinct vid.ONE_ID) as "userNum"
                 FROM
                TF_DWD_VOC_SENTENCE vid
                left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
                WHERE
                 1=1
                and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
                and vcc.PID='1372001238165561301'
             <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />
                 <if test="model.topicCodes != null and model.topicCodes.size()>0">
                     and vid.TOPIC_CODE in
                     <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                         #{item}
                     </foreach>
                 </if>
                 GROUP BY vid.TOPIC_CODE
                 ) eb LEFT JOIN VOC_BUSINESS_TAG ch ON eb."labelCode"=ch.TAG_CODE
                  where ch.name is not null
                    order by "statistic" desc
                    )
                where 1=1
                limit 50
     </select>


     <select id="userAndStatistic"  resultType="java.util.Map">
         <include refid="popUserAndStatistic" />

     </select>

    <sql id="popUserAndStatisticNew">
        select
        sum(ta.userCount) as userCount,
        ta.intention
        from
        (
        SELECT
        count(distinct vid.USER_ID) as userCount,
        SUM(vid.STATISTIC) as intention
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        ) ta
    </sql>



    <sql id="popUserAndStatistic">
        select
        CONCAT(t.userCount,'') as userCount,
        CONCAT(t.intention,'') as intention
        from (
        SELECT
        SUM(vid.STATISTIC) as intention,
        count(distinct vid.USER_ID) as userCount
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />

        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
         ) t
    </sql>

    <sql id="popUserAndStatisticList">
        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr,
        SUM(vid.STATISTIC) as "intention",
        count(distinct vid.USER_ID) as "userCount"
        FROM
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_EMOTION_USER vid
            </otherwise>
        </choose>

        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        ORDER BY dateStr desc
    </sql>

    <sql id="popUserAndStatisticListNew">
        <include refid="publicDateFilterCriteria.groupby-date-com-all"/>
        (
        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr1,
        SUM(vid.STATISTIC) as "intention",
        count(distinct vid.USER_ID) as "userCount"
        FROM
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                tf_dws_voc_nps_record vid
            </when>
            <otherwise>
                TF_DWS_VOC_EMOTION_USER vid
            </otherwise>
        </choose>

        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        ORDER BY dateStr1 desc
        ) datay on dateCycle.dateStr=datay.dateStr1


    </sql>

    <select id="getUserList1"  resultType="com.car.stats.vo.popvo.UserListInfoVo">
        SELECT
        vocuser.USER_ID as userId,
        vocuser.DISPLAY_NAME as userName,
        vocuser.IS_ONE_ID as isOneId,
        SUM(vid.STATISTIC) as statistic,
        sum( CASE vid.INTENTION_TYPE WHEN '咨询' THEN vid.STATISTIC ELSE 0 END ) AS consult,
        sum( CASE vid.INTENTION_TYPE WHEN '表扬' THEN vid.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        FROM
        (
        SELECT
               vid.USER_ID,
               vid.DISPLAY_NAME,
               vid.IS_ONE_ID
            FROM TF_DWS_VOC_EMOTION_USER vid
         WHERE 1=1
         and vid.USER_ID is not null
        <include refid="publicDateFilterCriteria.queryPop_vid" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        GROUP BY vid.USER_ID,vid.DISPLAY_NAME,vid.IS_ONE_ID
        ) vocuser
        LEFT JOIN
        TF_DWS_VOC_EMOTION_USER vid ON vocuser.USER_ID=vid.USER_ID
        WHERE
        1=1
        GROUP BY vocuser.USER_ID,vocuser.DISPLAY_NAME,vocuser.IS_ONE_ID
        ORDER BY statistic DESC

    </select>

    <select id="getUserList"  resultType="com.car.stats.vo.popvo.UserListInfoVo">
        SELECT
        vid.USER_ID userId,
        max(vid.display_name) userName,
        sum(vid.statistic) statistic,
        count(distinct vid.channel_id) as publishCount,
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                "1" AS isOneId,
            </when>
            <otherwise>
                if (sum(CASE vid.data_source WHEN 'Customer-data' THEN 1 ELSE 0 END)>0,"1","0")  AS isOneId,
            </otherwise>
        </choose>

        sum( case vid.intention_type when '咨询' then vid.statistic else 0 end ) as consult,
        sum( case vid.intention_type when '投诉' then vid.statistic else 0 end ) as complaint,
        sum( case vid.intention_type when '表扬' then vid.statistic else 0 end ) as praise,
        sum( case vid.intention_type when '建议' then vid.statistic else 0 end ) as suggest,
        sum( case vid.dimension_emotion when '正面' then vid.statistic else 0 end ) as positive,
        sum( case vid.dimension_emotion when '负面' then vid.statistic else 0 end ) as negative,
        (sum( case vid.dimension_emotion when '正面' then vid.statistic else 0 end )-sum( case vid.dimension_emotion when '负面' then vid.statistic else 0 end ))/(sum( case vid.dimension_emotion when '正面' then vid.statistic else 0 end )+sum( case vid.dimension_emotion when '负面' then vid.statistic else 0 end )) as emotionWorth,
        vid.user_type as userType,
        0 as userLevel
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.USER_ID is not null
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
                <if test="model.province != null and model.province.size()>0">
                    and vid.province_code in
                    <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>

        </otherwise>
        </choose>



        GROUP BY vid.USER_ID
        <if test="model.column !=null and model.column !='' and model.order !=null and model.order !='' " >
            ORDER BY ${model.column} ${model.order},userId desc
        </if>
        <if test="model.column ==null or model.column =='' " >
            ORDER BY statistic DESC,userId desc
        </if>


    </select>

    <select id="getallTagUserList"  resultType="com.car.stats.vo.popvo.UserListInfoVo">
        SELECT
        vid.one_id userId,
        max(vid.display_name) userName,
        count(1) statistic,
        count(distinct vid.channel_id) as publishCount,
        if (sum(CASE vid.voc_channel_1 WHEN 'Customer-data' THEN 1 ELSE 0 END)>0,"1","0")  AS isOneId,
        sum( case vid.intention_type when '咨询' then 1 else 0 end ) as consult,
        sum( case vid.intention_type when '投诉' then 1 else 0 end ) as complaint,
        sum( case vid.intention_type when '表扬' then 1 else 0 end ) as praise,
        sum( case vid.intention_type when '建议' then 1 else 0 end ) as suggest,
        sum( case vid.dimension_emotion when '正面' then 1 else 0 end ) as positive,
        sum( case vid.dimension_emotion when '负面' then 1 else 0 end ) as negative,
        (sum( case vid.dimension_emotion when '正面' then 1 else 0 end )-sum( case vid.dimension_emotion when '负面' then 1 else 0 end ))/(sum( case vid.dimension_emotion when '正面' then 1 else 0 end )+sum( case vid.dimension_emotion when '负面' then 1 else 0 end )) as emotionWorth,
        vid.user_type as userType,
        0 as userLevel
        FROM
        tf_dwd_voc_sentence vid
        WHERE
        1=1
        and vid.one_id is not null
        <include refid="publicDateFilterCriteria.queryPop_allTags_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <choose>
            <when test="model.menuName !=null and model.menuName =='feedbackAnalysis'">
                <include refid="com.car.voc.mapper.VocNPSAnalysisMapper.queryCom_nps_default_attrs_filter"/>
                <if test="model.province != null and model.province.size()>0">
                    and vid.province_code in
                    <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>

        </otherwise>
        </choose>



        GROUP BY vid.one_id
        <if test="model.column !=null and model.column !='' and model.order !=null and model.order !='' " >
            ORDER BY ${model.column} ${model.order},one_id desc
        </if>
        <if test="model.column ==null or model.column =='' " >
            ORDER BY statistic DESC,one_id desc
        </if>


    </select>




<select id="getUserLists"  resultType="com.car.stats.vo.popvo.UserListInfoVo">
        SELECT
        T.USER_ID as userId,
        T.DISPLAY_NAME as userName,
        T.IS_ONE_ID  as isOneId,
        T.USER_TYPE as userType,
        T.USER_LEVEL as userLevel,
        SUM(T.STATISTIC) as statistic,
        sum( CASE T.INTENTION_TYPE WHEN '咨询' THEN T.STATISTIC ELSE 0 END ) AS consult,
        sum( CASE T.INTENTION_TYPE WHEN '投诉' THEN T.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE T.INTENTION_TYPE WHEN '表扬' THEN T.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE T.DIMENSION_EMOTION WHEN '正面' THEN T.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE T.DIMENSION_EMOTION WHEN '负面' THEN T.STATISTIC ELSE 0 END ) AS negative
        FROM
        (
        SELECT vid.USER_ID,vid.DISPLAY_NAME,vid.IS_ONE_ID,vid.STATISTIC,vid.INTENTION_TYPE,vid.DIMENSION_EMOTION,vid.USER_TYPE,vid.USER_LEVEL
        , ROW_NUMBER() OVER (PARTITION BY vid.USER_ID ORDER BY vid.PUBLISH_DATE DESC) AS RW
        FROM TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.USER_ID is not null
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>

        ) T
        WHERE
        T.RW = 1
        GROUP BY T.USER_ID,T.DISPLAY_NAME,T.IS_ONE_ID,T.USER_TYPE,T.USER_LEVEL
        ORDER BY statistic DESC

    </select>

    <select id="getUserList_5"  resultType="com.car.stats.vo.popvo.UserListInfoVo">
        SELECT
        T.USER_ID as userId,
        T.DISPLAY_NAME as userName,
        T.IS_ONE_ID  as isOneId,
        T.USER_TYPE as userType,
        T.USER_LEVEL as userLevel,
        SUM(T.STATISTIC) as statistic,
        sum( CASE T.INTENTION_TYPE WHEN '咨询' THEN T.STATISTIC ELSE 0 END ) AS consult,
        sum( CASE T.INTENTION_TYPE WHEN '投诉' THEN T.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE T.INTENTION_TYPE WHEN '表扬' THEN T.STATISTIC ELSE 0 END ) AS praise,
        sum( CASE T.DIMENSION_EMOTION WHEN '正面' THEN T.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE T.DIMENSION_EMOTION WHEN '负面' THEN T.STATISTIC ELSE 0 END ) AS negative
        FROM
        (
        select *
        from (
        select
        vid.*,
        @rownum := @rownum+1 ,
        if(@pdept=vid.USER_ID,@rank:=@rank+1,@rank:=1) as rank,
        @pdept:=vid.USER_ID
        from (select * from TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.USER_ID is not null
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion" />
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
            AND vid.THREE_DIMENSION_CODE=#{model.thirdDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        ) vid
        ,
        (select @rownum :=0 , @pdept := null ,@rank:=0) c
        order by vid.PUBLISH_DATE DESC
        ) result
        where rank = 1

        ) T

        WHERE
        1=1
        GROUP BY T.USER_ID,T.DISPLAY_NAME,T.IS_ONE_ID,T.USER_TYPE,T.USER_LEVEL
        ORDER BY statistic,T.USER_ID DESC

    </select>



    <select id="certifiedOwner" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="java.util.Map">

        SELECT
        tabThis.thisMent AS "certifiedOwner",
        tabUp.UPMENT AS "UpMent"
        FROM
        (SELECT
        COUNT(DISTINCT USER_ID) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        AND vid.USER_TYPE=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        )  tabThis,

        (SELECT
        COUNT(DISTINCT USER_ID) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where
        1=1
        AND vid.USER_TYPE=1
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
        ) tabUp

    </select>


    <select id="getSessionIdByTraceIdList"  resultType="java.lang.String">
        select trace_id from t317_ons_session_info_i_d
        where
        <if test="sessionIdList != null and sessionIdList.size()>0">
            session_id in
            <foreach item="item" collection="sessionIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
     group by trace_id

    </select>


    <select id="sessionListUserIdList"  resultType="java.util.Map">
        select
            type,
            session_id AS sessionId,
            send_user_nick as sendUserNick,
            message_content_all as messageContentAll,
            message_send_time as messageSendTime
        from
            t317_ons_session_info_i_d
        where
        <if test="sessionIdList != null and sessionIdList.size()>0">
            session_id in
            <foreach item="item" collection="sessionIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getSessionOneList"  resultType="java.util.Map">
        WITH filtered_sessions AS (
            SELECT
                u.session_id AS sessionId,
                u.business_type AS businessType,
                u.send_user_nick AS visitorName,
                u.brand_name AS brandName,
                u.model_name AS modelName,
                u.level1_question_classification AS level1QuestionClassification,
                u.level2_question_classification AS level2QuestionClassification,
                u.message_content_all AS messageContentAll
            FROM t317_ons_session_info_i_d u
            WHERE
               u.type = '2'
            <if test="sessionIdList != null and sessionIdList.size()>0">
                AND u.session_id in
                <foreach item="item" collection="sessionIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        )
        select * from
            (SELECT
                 uses.*,
                 exeu.send_user_nick AS executor,
                 exeu.`type` + 0 AS orderType,
                 ROW_NUMBER() OVER (
        PARTITION BY uses.sessionId
        ORDER BY exeu.`type` + 0 ASC
       ) AS rn
             FROM filtered_sessions uses
                      LEFT JOIN t317_ons_session_info_i_d exeu
                                ON uses.sessionId = exeu.session_id
                                    AND exeu.type IN ('4', '16', '32')
            ) f1
        WHERE rn = 1
    </select>


</mapper>
