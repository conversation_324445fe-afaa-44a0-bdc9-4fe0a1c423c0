<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwsVocEmotionDiMapper">


    <select id="emotionIntentionTrend"  resultType="com.car.stats.vo.risk.IntentionEmotionTrendVo">
        SELECT
            COUNT(DISTINCT vid.USER_ID) as userNum,
            sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS 	complaint,
            sum(CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        FROM
            TF_DWS_VOC_EMOTION_USER vid
        WHERE
            1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        ORDER BY statistic DESC
    </select>

    <select id="riskVoiceUserTrend"  resultType="com.car.stats.vo.risk.VoiceUserVo">
        SELECT
            count(distinct vid.USER_ID)  userNum
        FROM
            TF_DWS_VOC_EMOTION_USER vid
        WHERE
            1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
          <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
    </select>


    <select id="riskHotWords"  resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        tab.*
        FROM
        (
        SELECT
        vid.EMOTION_KEYWORD AS keyword,
        vid.DIMENSION_EMOTION AS emotionType,
        -1 AS weight,
        SUM(vid.STATISTIC) AS  statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.EMOTION_KEYWORD is not null
        and vid.DIMENSION_EMOTION='负面'
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.PUBLISH_DATE>=#{model.startDate}
            AND #{model.endDate}>=vid.PUBLISH_DATE
        </if>
        <!--        <include refid="publicDateFilterCriteria.queryCom_vid" />-->
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>

        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        GROUP BY vid.EMOTION_KEYWORD,vid.DIMENSION_EMOTION
        ORDER BY STATISTIC DESC,keyword desc
        ) tab
        WHERE
        1=1
        <if test="model.rownum !=null" >
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>


    <select id="voiceUserTop"  resultType="com.car.stats.vo.risk.VoiceUserTopVo">
        SELECT
        chus.*,
        ch.NAME channelStr
        FROM
        (
        SELECT
        vid.CHANNEL_ID AS channelId,
        max(vid.DISPLAY_NAME) AS displayName,
        vid.IS_ONE_ID as isOneId,
        vid.USER_ID,
        sum(vid.STATISTIC) as userNum
       <!-- count(distinct vid.USER_ID) userNum -->
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.DISPLAY_NAME is not null
        and vid.CHANNEL_ID is not null
        <include refid="ComFilterMapper.general_query_dataSorce" />
        <include refid="ComFilterMapper.emotion_type"/>
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.this_date"/>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND #{model.createDate}>=vid.create_time
        </if>
        GROUP BY vid.CHANNEL_ID,vid.IS_ONE_ID,vid.USER_ID
        ORDER BY userNum DESC
        ) chus  left join voc_channel_category ch on chus.channelId=ch.id
        WHERE
        1=1
        <if test="model.rownum !=null" >
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>
    <select id="voiceUserTop1"  resultType="com.car.stats.vo.risk.VoiceUserTopVo">
        SELECT
        chus.*,
        ch.NAME channelStr
        FROM
        (
        SELECT
        vid.DATA_SOURCE AS channelId,
        vid.DISPLAY_NAME AS displayName,
        vid.IS_ONE_ID as isOneId,
        vid.USER_ID,
        sum(vid.STATISTIC) as userNum
        <!-- count(distinct vid.USER_ID) userNum -->
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        and vid.DISPLAY_NAME is not null
        and vid.DATA_SOURCE is not null
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
        GROUP BY vid.DATA_SOURCE,vid.DISPLAY_NAME,vid.IS_ONE_ID,vid.USER_ID
        ORDER BY userNum DESC
        ) chus  left join voc_channel_category ch on chus.channelId=ch.id
        WHERE
        1=1
        <if test="model.rownum !=null" >
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 50
        </if>
    </select>



    <select id="sumQuantityTop"  resultType="java.math.BigDecimal">
        SELECT
        SUM(STATISTIC) as "statistic"
        FROM TF_DWS_VOC_EMOTION_USER vid WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
    </select>


    <select id="dataSourceDistribution"  resultType="com.car.stats.vo.ChannelVo">
        select
        sour.*
        from
        (SELECT
        vid.voc_channel_2 as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            count(distinct vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY
        vid.voc_channel_2
        ORDER BY statistic desc) sour
        where 1=1 limit 10
    </select>
<select id="riskDataSourceDistribution"  resultType="com.car.stats.vo.ChannelVo">
        select
        sour.*
        from (
        (SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle-table" /> AS dateStr,
        vid.DATA_SOURCE as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            count(distinct vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT JOIN TF_DIM_DATE tdd ON vid.PUBLISH_DATE=tdd.FORMAT_DATE
    WHERE 1=1
        and
        (vid.DATA_SOURCE !='1372001238165561345'
        and vid.DATA_SOURCE!='1372001238165593852')
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle-table" />,
        vid.DATA_SOURCE
        ORDER BY statistic desc) sour
        )
        where 1=1 limit 10
    </select>




    <select id="overviewBriefingValue" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.VocOverBriefingValueVo">

        SELECT
        tabThis.thisMent AS "totalMentions",
        tabUp.UpMent as "totalMentionsUp"
        FROM
        (SELECT
        SUM(STATISTIC) as thisMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
            where 1=1
            <include refid="publicDateFilterCriteria.queryCom_vid" />
            )  tabThis,

        (SELECT
        SUM(STATISTIC) as UpMent
        FROM
        TF_DWS_VOC_EMOTION_USER vid
            where 1=1
            <include refid="publicDateFilterCriteria.queryCom_vid_up" />
            ) tabUp

    </select>


    <select id="sourceChannel" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.ChannelStatisticVo">
        SELECT
          vid.voc_channel_2 as dataSource,
            SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        <include refid="publicDateFilterCriteria.querychan_vid" />
        GROUP BY
        vid.voc_channel_2
        ORDER BY statistic desc

    </select>
    <select id="focusProportionEmotion" parameterType="com.car.stats.model.FilterCriteriaModel" resultType="com.car.stats.vo.EmotionProportionVo">
        SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
            count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative,
            count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL  END )) AS neutral,
            COUNT(DISTINCT vid.USER_ID) as total
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
            sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
            sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
            sum(vid.STATISTIC) as total
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
    </select>

<select id="themeDistribution"   resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
        vid.FIRST_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on vid.FIRST_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
       <include refid="publicDateFilterCriteria.queryCom_vid" />
    <include refid="ComFilterMapper.intention_type"/>
    <include refid="ComFilterMapper.wo-dlr"/>
        group by  vid.FIRST_DIMENSION_CODE,tag.NAME
        order by statistic desc
    </select>
    <select id="threeDistribution"   resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on vid.THREE_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
       <include refid="publicDateFilterCriteria.queryCom_vid" />
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>

        group by  vid.THREE_DIMENSION_CODE,tag.NAME
        order by statistic desc
    </select>

    <select id="topicDistribution"   resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
        vid.topic_code as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on vid.THREE_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
       <include refid="publicDateFilterCriteria.queryCom_vid" />
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>

        group by  vid.topic_code,tag.NAME
        order by statistic desc
    </select>

    <select id="secondDistribution"   resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
        vid.SECOND_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on vid.SECOND_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
       <include refid="publicDateFilterCriteria.queryCom_vid" />
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>
        group by  vid.SECOND_DIMENSION_CODE,tag.NAME
        order by statistic desc
    </select>
    <select id="riskCarSeriesProption" resultType="com.car.stats.vo.ProportionCarSeriesVo">

        select vid.CAR_SERIES_CODE,
        SUM(vid.STATISTIC) as statistic
        from TF_DWS_VOC_EMOTION_USER vid
        where 1=1
        and vid.CAR_SERIES_CODE is not null
        and vid.CAR_SERIES_CODE !='A12A00'
        and vid.DIMENSION_EMOTION = '负面'
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND to_date(#{model.createDate},'YYYY-MM-DD hh24:mi:ss')>=vid.create_time
        </if>
        group by vid.CAR_SERIES_CODE
    </select>
    <select id="riskAreaProption" resultType="com.car.voc.vo.ProportionAreaVo">

        select r.regional_code as areaCode,
               r.regional_name as areaStr,
        SUM(vid.STATISTIC) as statistic
        FROM
        TF_DWS_VOC_EMOTION_USER vid join voc_brand_region r on vid.province = r.province_code
        where 1=1
        and vid.province is not null
        and vid.DIMENSION_EMOTION = '负面'
        <include refid="ComFilterMapper.intention_type"/>
        <include refid="ComFilterMapper.wo-dlr"/>
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
            AND r.brand =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid"/>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.createDate !=null and model.createDate != '' ">
            AND to_date(#{model.createDate},'YYYY-MM-DD hh24:mi:ss')>=vid.create_time
        </if>
        GROUP BY vid.AREA_CODE

    </select>
    <select id="channelQuantityTop"   resultType="com.car.stats.vo.LabelVo">

        select
        current.labelCode,
        current.statistic,
        current.statisticP,
        (current.statistic - previous.statistic) / previous.statistic AS statisticR
        from
        (SELECT
        vid.topic_code as labelCode,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic,
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic,
        </if>
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID)
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC)
        </if>/ total_count.agecount AS statisticP
        FROM
        TF_DWS_VOC_EMOTION_USER vid,
        (SELECT
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID)
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC)
        </if> AS agecount
        FROM TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid" />) as total_count
        WHERE
        1=1
       <include refid="publicDateFilterCriteria.queryCom_vid" />
        group by  vid.topic_code
        order by statistic desc
        <if test="model.rownum!=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum==null">
            limit 10
        </if>
        ) as current
        LEFT
        join

        (
        SELECT
        vid.topic_code as labelCode,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>

        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <include refid="publicDateFilterCriteria.queryCom_vid_up" />
        group by  vid.topic_code
        ) as previous
        ON current.labelCode = previous.labelCode

    </select>


<select id="threeThemeShare"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.THREE_DIMENSION_CODE as labelCode,
        tag.NAME as labelStr,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            sum(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        left join VOC_BUSINESS_TAG tag on vid.THREE_DIMENSION_CODE = tag.TAG_CODE
        WHERE
        1=1
        and tag.NAME is not null
       <include refid="publicDateFilterCriteria.queryCom_vid" />
        group by  vid.THREE_DIMENSION_CODE,tag.NAME
    </select>

<select id="threeThemeShare_new"   resultType="com.car.stats.vo.LabelVo">
                select
                dateCycle.dateStr,
                dateCycle.labelCode,
                dateCycle.dayNum,
                datay.statistic,
                LAG(datay.statistic) OVER (ORDER BY dateCycle.labelCode, dateCycle.dateStr ASC) AS statisticUp
                from (
                select
                dateCycle.dateStr,
                tag.tag_code as labelCode,
                dateCycle.dayNum
                from
                (   select
                    t3.tag_code
                from voc_business_tag t3
                left join voc_business_tag t2 on t2.id=t3.pid
                left join voc_business_tag t1 on t2.pid=t1.id
                where
                1=1
                <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
                    and t2.tag_code in
                    <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
                    and t2.tag_code in
                    <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                ) tag,
                <include refid="publicDateFilterCriteria.groupby-date-com-all-table"/> dateCycle
                where 1=1
                ) dateCycle left join (
                select
                dsf.*
                from
                (
                SELECT
                <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr1,
                vid.THREE_DIMENSION_CODE as labelCode,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    COUNT(DISTINCT vid.USER_ID) as statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                    sum(vid.STATISTIC) as statistic
                </if>
                FROM
                TF_DWS_VOC_EMOTION_USER vid
                WHERE
                1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    AND vid.brand_code =#{model.brandCode}
                </if>
                group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />,vid.THREE_DIMENSION_CODE
                ORDER BY dateStr1 desc
                ) dsf
                ) datay on dateCycle.dateStr=datay.dateStr1 and dateCycle.labelCode=datay.labelCode
    </select>


    <select id="themeShare"   resultType="com.car.stats.vo.LabelVo">
            SELECT
            vid.FIRST_DIMENSION_CODE as labelCode,
            tag.NAME as labelStr,
            <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                COUNT(DISTINCT vid.USER_ID) as statistic
            </if>
            <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                sum(vid.STATISTIC) as statistic
            </if>
            FROM
            TF_DWS_VOC_EMOTION_USER vid
            left join VOC_BUSINESS_TAG tag on vid.FIRST_DIMENSION_CODE = tag.TAG_CODE
            WHERE
            1=1
           <include refid="publicDateFilterCriteria.queryCom_vid" />
            group by  vid.FIRST_DIMENSION_CODE,tag.NAME
        </select>
    <select id="themeShare_new"   resultType="com.car.stats.vo.LabelVo">
            select
            dateCycle.dateStr,
            dateCycle.labelCode,
            dateCycle.dayNum,
            datay.statistic,
            LAG(datay.statistic) OVER (ORDER BY dateCycle.labelCode, dateCycle.dateStr ASC) AS statisticUp
            from (
                    select
                    dateCycle.dateStr,
                    tag.tag_code as labelCode,
                    dateCycle.dayNum
                    from
                    voc_business_tag tag,
                    <include refid="publicDateFilterCriteria.groupby-date-com-all-table"/> dateCycle
                    where tag.pid=0
                    <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
                        and tag.tag_code in
                        <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
            ) dateCycle left join (
              select
                dsf.*
                from
                (
                    SELECT
                    <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr1,
                    vid.FIRST_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum(vid.STATISTIC) as statistic
                    </if>
                    FROM
                    TF_DWS_VOC_EMOTION_USER vid
                    WHERE
                    1=1
                    <include refid="publicDateFilterCriteria.queryCom_vid" />
                    <if test="model.brandCode !=null and model.brandCode !=''" >
                        AND vid.brand_code =#{model.brandCode}
                    </if>
                    group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />,vid.FIRST_DIMENSION_CODE
                    ORDER BY dateStr1 desc
                ) dsf
            ) datay on dateCycle.dateStr=datay.dateStr1 and dateCycle.labelCode=datay.labelCode
        ORDER BY
        CAST(SUBSTRING_INDEX(dateCycle.dateStr, '-', 1) AS UNSIGNED) ASC,
        CAST(SUBSTRING_INDEX(dateCycle.dateStr, '-', -1) AS UNSIGNED) ASC
    </select>

<select id="secondThemeShare"   resultType="com.car.stats.vo.LabelVo">
        SELECT
        vid.SECOND_DIMENSION_CODE as labelCode,
        sum(vid.STATISTIC) as statistic,
        tag.NAME as labelStr
        FROM
        TF_DWS_VOC_EMOTION_USER  vid left join VOC_BUSINESS_TAG tag on vid.second_dimension_code = tag.TAG_CODE
        WHERE
        1=1

        <if test="model.firstDimensionCode != null">
            AND vid.FIRST_DIMENSION_CODE =#{model.firstDimensionCode}
        </if>
    <include refid="publicDateFilterCriteria.queryCom_vid" />
    group by  vid.SECOND_DIMENSION_CODE
    </select>

<select id="secondThemeShare_new"   resultType="com.car.stats.vo.LabelVo">
            select
            dateCycle.dateStr,
            dateCycle.labelCode,
            dateCycle.dayNum,
            datay.statistic,
            LAG(datay.statistic) OVER (ORDER BY dateCycle.labelCode, dateCycle.dateStr ASC) AS statisticUp
            from (
            select
            dateCycle.dateStr,
            tag.tag_code as labelCode,
            dateCycle.dayNum
            from
            (   select
                    t2.tag_code
                    from voc_business_tag t2
                    left join voc_business_tag t1 on t1.tag_code=#{model.firstDimensionCode} and t2.pid=t1.id
                    where  t2.pid=t1.id
                    <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
                        and t2.tag_code in
                        <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
            ) tag,
            <include refid="publicDateFilterCriteria.groupby-date-com-all-table"/> dateCycle
            where 1=1
            ) dateCycle left join (
            select
            dsf.*
            from
            (
            SELECT
            <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr1,
            vid.SECOND_DIMENSION_CODE as labelCode,
            <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                COUNT(DISTINCT vid.USER_ID) as statistic
            </if>
            <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                sum(vid.STATISTIC) as statistic
            </if>
            FROM
            TF_DWS_VOC_EMOTION_USER vid
            WHERE
            1=1
            <include refid="publicDateFilterCriteria.queryCom_vid" />
            <if test="model.brandCode !=null and model.brandCode !=''" >
                AND vid.brand_code =#{model.brandCode}
            </if>
            <if test="model.firstDimensionCode != null">
                AND vid.FIRST_DIMENSION_CODE =#{model.firstDimensionCode}
            </if>
            group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />,vid.SECOND_DIMENSION_CODE
            ORDER BY dateStr1 desc
            ) dsf
            ) datay on dateCycle.dateStr=datay.dateStr1 and dateCycle.labelCode=datay.labelCode
            ordry by dateCycle.dateStr desc
    </select>

<select id="highFrequencyWords"   resultType="com.car.stats.vo.HighHotWordsVo">
    SELECT
    *
    FROM
    (
    SELECT
    vid.EMOTION_KEYWORD AS keyword,
    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
        (count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL  END ))+count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END ))+count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END ))) as statistic,
        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '中性' THEN vid.USER_ID ELSE NULL  END )) AS neutral,
        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative
    </if>
    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
        sum( CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END ) AS neutral,
        (sum(CASE vid.DIMENSION_EMOTION WHEN '中性' THEN vid.STATISTIC ELSE 0 END)+sum(CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END)+sum(CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END)) AS statistic
    </if>

    FROM
    TF_DWS_VOC_EMOTION_USER vid
    WHERE
    1=1
   and vid.EMOTION_KEYWORD is not null

    <include refid="publicDateFilterCriteria.queryCom_vid" />
    <if test="model.hotWords != null and model.hotWords.size()>0">
        and vid.EMOTION_KEYWORD in
        <foreach item="item" collection="model.hotWords" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </if>
    GROUP BY vid.EMOTION_KEYWORD
    ORDER BY STATISTIC DESC, keyword DESC

    ) t
    WHERE 1=1
    <if test="model.rownum!=null">
        limit #{model.rownum}
    </if>
    <if test="model.rownum==null">
        limit 50
    </if>
    </select>

<select id="channelDistribution"   resultType="com.car.stats.vo.ChannelVo">

        SELECT
            sour.*
        FROM
            (
                SELECT
                 vid.CHANNEL_ID as channelId,
                <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                    COUNT(DISTINCT vid.USER_ID) as statistic
                </if>
                <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   " >
                    SUM(vid.STATISTIC) as statistic
                </if>
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>

                GROUP BY vid.CHANNEL_ID
            ) sour
            where 1=1
    </select>
    <select id="channel2Distribution"   resultType="com.car.stats.vo.ChannelVo">

        SELECT
        sour.*
        FROM
        (
        SELECT
        ch.pid as channelId,
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as statistic
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   " >
            SUM(vid.STATISTIC) as statistic
        </if>
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        LEFT  JOIN voc_channel_category ch ON vid.channel_id = ch.id
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        GROUP BY ch.pid
        ) sour
        where 1=1
    </select>


    <select id="riskChannelDistribution"   resultType="com.car.stats.vo.ChannelVo">

        SELECT
            sour.*
        FROM
            (
                SELECT
             <include refid="publicDateFilterCriteria.groupby-com-cycle-table" /> AS dateStr,
                vid.voc_channel_3 as channelId,
        SUM(vid.STATISTIC) as statistic
                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                LEFT JOIN TF_DIM_DATE tdd ON vid.PUBLISH_DATE=tdd.FORMAT_DATE

             WHERE
                    1=1
        <include refid="ComFilterMapper.general_query_dataSorce" />
                <include refid="ComFilterMapper.emotion_type"/>
                <include refid="ComFilterMapper.intention_type"/>
                <include refid="ComFilterMapper.this_date" />
                <include refid="ComFilterMapper.wo-dlr"/>
                <if test="model.topicCodes != null and model.topicCodes.size()>0">
                    and vid.TOPIC_CODE in
                    <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    and vid.brand_code = #{model.brandCode}
                </if>
                <if test="model.createDate !=null and model.createDate != '' ">
                    AND #{model.createDate}>=vid.create_time
                </if>
                GROUP BY
        <include refid="publicDateFilterCriteria.groupby-com-cycle-table" /> ,
        vid.voc_channel_3
         ORDER BY dateStr desc
        ) sour
            where 1=1
    </select>

<select id="complaintWebsiteChannelTrend"   resultType="com.car.stats.vo.ChannelVo">
    SELECT
        sour.*,
        cc.NAME as channelStr
    FROM
        (
            SELECT
             vid.CHANNEL_ID as channelId,
            <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                COUNT(DISTINCT vid.ONE_ID) as statistic,
            </if>
            <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention   " >
                count(1) as statistic,
            </if>

    <choose>
        <when test="model.getDateUnit == 0">
            CONCAT(CONCAT(dm.date_week_year, '-'),dm.DATE_WEEK) as dateStr
            FROM
            TF_DWD_VOC_SENTENCE vid
            left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
            left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
            WHERE
            1=1
            and vcc.PID='1372001238165561301'
            and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
            <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />

            GROUP BY vid.CHANNEL_ID,dm.date_week_year,dm.DATE_WEEK
        </when>
        <when test="model.getDateUnit == 1">
            CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_MONTH) as dateStr
            FROM
            TF_DWD_VOC_SENTENCE vid
            left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
            left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
            WHERE
            1=1
            and vcc.PID='1372001238165561301'
            and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
            <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />

            GROUP BY vid.CHANNEL_ID,dm.DATE_YEAR,dm.DATE_MONTH
        </when>
        <when test="model.getDateUnit == 2">
            CONCAT(CONCAT(dm.DATE_YEAR, '-'),dm.DATE_QUARTER) as dateStr
            FROM
            TF_DWD_VOC_SENTENCE vid
            left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
            left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
            WHERE
            1=1
            and vcc.PID='1372001238165561301'
            and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
            <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />
            GROUP BY vid.CHANNEL_ID,dm.DATE_YEAR,dm.DATE_QUARTER
        </when>
        <when test="model.getDateUnit == 3">
            dm.DATE_YEAR as dateStr FROM
            TF_DWD_VOC_SENTENCE vid
            left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
            left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
            WHERE
            1=1
            and vcc.PID='1372001238165561301'
            and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
            <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />
            GROUP BY vid.CHANNEL_ID,dm.DATE_YEAR
        </when>
        <otherwise>
            vid.PUBLISH_DATE as dateStr
            FROM
            TF_DWD_VOC_SENTENCE vid
            left join  voc_channel_category vcc on vid.CHANNEL_ID=vcc.ID
            left JOIN TF_DIM_DATE dm ON vid.PUBLISH_DATE = dm.FORMAT_DATE
            WHERE
            1=1
            and vcc.PID='1372001238165561301'
            and vid.FIRST_DIMENSION_CODE  NOT LIKE 'Q100%'
            <include refid="publicDateFilterCriteria.queryCom_complaint_web_vid" />
            GROUP BY vid.CHANNEL_ID,vid.PUBLISH_DATE
        </otherwise>
    </choose>

        ) sour
            LEFT JOIN voc_channel_category cc ON sour.channelId=cc.ID
        order by sour.statistic desc
    </select>

    <select id="analyEmotionMentionEate"   resultType="com.car.stats.vo.AnalyEmotionEateVo">
        SELECT
            fc.*,
            tn.NAME as labelStr
        FROM
            (
                SELECT
                    vid.FIRST_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
                        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
                        SUM(vid.STATISTIC) as statistic
                    </if>

                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                GROUP BY vid.FIRST_DIMENSION_CODE

            ) fc
            LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
            where
                1=1
              and tn.NAME is not null
    </select>
    <select id="analyEmotionMentionEate2"   resultType="com.car.stats.vo.AnalyEmotionEateVo">
        SELECT
            fc.*,
            tn.NAME as labelStr
        FROM
            (
                SELECT
                    vid.SECOND_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
                        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
                        SUM(vid.STATISTIC) as statistic
                    </if>

                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />

                GROUP BY vid.SECOND_DIMENSION_CODE

            ) fc
            LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
            where
            1=1
            and tn.NAME is not null
    </select>
    <select id="analyEmotionMentionEate3"   resultType="com.car.stats.vo.AnalyEmotionEateVo">
        SELECT
            fc.*,
            tn.NAME as labelStr
        FROM
            (
                SELECT
                    vid.THREE_DIMENSION_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
                        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
                        SUM(vid.STATISTIC) as statistic
                    </if>

                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
            <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
                AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
            </if>
                GROUP BY vid.THREE_DIMENSION_CODE

            ) fc
            LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
            where
            1=1
            and tn.NAME is not null
    </select>
    <select id="analyEmotionMentionEate4"   resultType="com.car.stats.vo.AnalyEmotionEateVo">
        SELECT
            fc.*,
            tn.NAME as labelStr
        FROM
            (
                SELECT
                    vid.TOPIC_CODE as labelCode,
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as statistic,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '正面' THEN vid.USER_ID ELSE NULL  END )) AS positive,
                        count(distinct(CASE  vid.DIMENSION_EMOTION WHEN '负面' THEN vid.USER_ID ELSE NULL  END )) AS negative
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
                        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative,
                        SUM(vid.STATISTIC) as statistic
                    </if>

                FROM
                    TF_DWS_VOC_EMOTION_USER vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryCom_vid" />
                <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
                    AND vid.THREE_DIMENSION_CODE =#{model.thirdDimensionCode}
                </if>
                GROUP BY vid.TOPIC_CODE

            ) fc
            LEFT JOIN VOC_BUSINESS_TAG tn ON fc.labelCode=tn.TAG_CODE
           where
            1=1
            and tn.NAME is not null
    </select>
        <select id="quantitytop"   resultType="cn.hutool.json.JSONObject">
            SELECT
            cs.*, f.TAG_CODE "firstDimensionCode", s.TAG_CODE "secondDimensionCode",
            th.TAG_CODE "threeDimensionCode", t.TAG_CODE "topicCode",
            f.NAME AS "firstDimension", s.NAME AS "secondDimension", th.NAME AS "threeDimension", t.NAME AS "topic"
            FROM (
            SELECT
            this.*, u.*,
            this."statisticThis"+u."statisticUp" AS "total"
            FROM (
            SELECT vid.TOPIC_CODE as "topicCodeThis",
            <if test="model.channelIds != null and model.channelIds.size()>0">
                <foreach item="item" collection="model.channelIds" separator="" open="" close="" index="">
                    sum( CASE vid.CHANNEL_ID WHEN '${item}'  THEN vid.STATISTIC ELSE 0 END )  th${item},
                </foreach>
            </if>
            SUM(STATISTIC) as "statisticThis"
            FROM TF_DWS_VOC_EMOTION_USER  vid WHERE 1=1
            <include refid="publicDateFilterCriteria.queryCom_vid" />
            GROUP BY vid.TOPIC_CODE ORDER BY "statisticThis" DESC ) this,
            ( SELECT
            vid.TOPIC_CODE as "topicCodeUp",
            <if test="model.channelIds != null and model.channelIds.size()>0">
                <foreach item="item" collection="model.channelIds" separator="" open="" close="" index="">
                    sum( CASE vid.CHANNEL_ID WHEN '${item}' THEN vid.STATISTIC ELSE 0 END )  up${item},
                </foreach>
            </if>
            SUM(STATISTIC) as "statisticUp"
            FROM TF_DWS_VOC_EMOTION_USER  vid
            WHERE 1=1
            <include refid="publicDateFilterCriteria.queryCom_vid_up" />
            GROUP BY vid.TOPIC_CODE ORDER BY "statisticUp" DESC ) u
            WHERE 1=1 AND
            u."topicCodeUp"=this."topicCodeThis" ORDER BY "total" DESC ) cs
            LEFT JOIN VOC_BUSINESS_TAG t ON cs."topicCodeThis"=t.TAG_CODE
            LEFT JOIN VOC_BUSINESS_TAG th ON t.PID=th.ID
            LEFT JOIN VOC_BUSINESS_TAG s ON th.PID=s.ID
            LEFT JOIN VOC_BUSINESS_TAG f ON s.PID=f.ID
            WHERE cs."topicCodeThis" = t.TAG_CODE ORDER BY "total" DESC
        </select>
    <select id="quantitytop111111"   resultType="com.car.stats.vo.MentiontopVo">
            SELECT
            cs.*
            FROM (
            SELECT
            this.*
            FROM (
            SELECT
                   vid.TOPIC_CODE as "topicCode",
                   vid.THREE_DIMENSION_CODE as "threeDimensionCode",
                   vid.SECOND_DIMENSION_CODE as "secondDimensionCode",
                   vid.FIRST_DIMENSION_CODE as "firstDimensionCode",
                    <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
                        COUNT(DISTINCT vid.USER_ID) as "statistic"
                    </if>
                    <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
                        SUM(STATISTIC) as "statistic"
                    </if>
            FROM TF_DWS_VOC_EMOTION_USER vid WHERE 1=1
            <include refid="publicDateFilterCriteria.queryCom_vid" />
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            GROUP BY vid.TOPIC_CODE ,vid.THREE_DIMENSION_CODE,vid.SECOND_DIMENSION_CODE,vid.FIRST_DIMENSION_CODE
            ORDER BY "statistic" DESC ) this
            WHERE 1=1
            limit 11
             ) cs
            WHERE 1=1
            ORDER BY cs."statistic" DESC
        </select>
    <select id="quantitytop222222222222"   resultType="com.car.stats.vo.MentiontopVo">
        SELECT
        cs.*
        FROM (
        SELECT
        this.*
        FROM (
        SELECT vid.TOPIC_CODE as "topicCode",
               vid.THREE_DIMENSION_CODE as "threeDimensionCode",
               vid.SECOND_DIMENSION_CODE as "secondDimensionCode",
               vid.FIRST_DIMENSION_CODE as "firstDimensionCode",
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as "statistic"
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(STATISTIC) as "statistic"
        </if>
        FROM TF_DWS_VOC_EMOTION_USER vid WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        GROUP BY
                 vid.TOPIC_CODE ,
                vid.THREE_DIMENSION_CODE ,
                vid.SECOND_DIMENSION_CODE ,
                vid.FIRST_DIMENSION_CODE
        ORDER BY "statistic" DESC ) this
        WHERE 1=1
        limit 11
        ) cs

        WHERE 1=1
        ORDER BY cs."statistic" DESC
    </select>
 <select id="quantitytop2"   resultType="com.car.stats.vo.MentiontopVo">
        SELECT
        cs.*,
        f.TAG_CODE "firstDimensionCode",
        s.TAG_CODE "secondDimensionCode",
        s.ORDER_BY "orderBy",
        th.TAG_CODE "threeDimensionCode", t.TAG_CODE "topicCode",
        f.NAME AS "firstDimension", s.NAME AS "secondDimension", th.NAME AS "threeDimension", t.NAME AS "topic"
        FROM (
        SELECT
        this.*
        FROM (
        SELECT vid.TOPIC_CODE as "topicCode",
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as "statistic"
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(STATISTIC) as "statistic"
        </if>
        FROM TF_DWS_VOC_EMOTION_USER vid WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        GROUP BY vid.TOPIC_CODE ORDER BY statistic DESC ) this
        WHERE 1=1
        limit 10
        ) cs
        LEFT JOIN VOC_BUSINESS_TAG t ON cs.topicCode=t.TAG_CODE
        LEFT JOIN VOC_BUSINESS_TAG th ON t.PID=th.ID
        LEFT JOIN VOC_BUSINESS_TAG s ON th.PID=s.ID
        LEFT JOIN VOC_BUSINESS_TAG f ON s.PID=f.ID
        WHERE 1=1 AND t.TAG_CODE IS NOT NULL
        ORDER BY cs.statistic DESC
    </select>
<select id="quantityChanFistTop2"   resultType="com.car.stats.vo.MentiontopVo">
        SELECT
        cs.*,
        f.TAG_CODE "firstDimensionCode",
        s.TAG_CODE "secondDimensionCode",
        th.TAG_CODE "threeDimensionCode", t.TAG_CODE "topicCode",
        f.NAME AS "firstDimension", s.NAME AS "secondDimension", th.NAME AS "threeDimension", t.NAME AS "topic"
        FROM (
        SELECT
        this.*
        FROM (
        SELECT vid.TOPIC_CODE as "topicCode",
        <if test="model.dataType !=null  and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers " >
            COUNT(DISTINCT vid.USER_ID) as "statistic"
        </if>
        <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention  " >
            SUM(STATISTIC) as "statistic"
        </if>
        FROM TF_DWS_VOC_EMOTION_USER vid WHERE 1=1
        <include refid="publicDateFilterCriteria.queryCom_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        GROUP BY vid.TOPIC_CODE ORDER BY "statistic" DESC ) this
        WHERE 1=1
        limit 11
        ) cs
        LEFT JOIN VOC_BUSINESS_TAG t ON cs."topicCode"=t.TAG_CODE
        LEFT JOIN VOC_BUSINESS_TAG th ON t.PID=th.ID
        LEFT JOIN VOC_BUSINESS_TAG s ON th.PID=s.ID
        LEFT JOIN VOC_BUSINESS_TAG f ON s.PID=f.ID
        WHERE 1=1 AND t.TAG_CODE IS NOT NULL
        ORDER BY cs."statistic" DESC
    </select>

</mapper>
