<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.VocSentenceMapper">

<!--  ===============================领导页面净情感值（NSR）===================================  -->
    <select id="emotionalPurification"  resultType="com.car.stats.vo.NsrVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN nsrUp = 0 AND (nsr - nsrUp) != 0 THEN 999999
        ELSE IFNULL((nsr - nsrUp) / IF(nsrUp = 0, 1, nsrUp) * 100, 0) END, 2) as nsrr
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        ifnull(round((((positive_c-negative_c)/(positive_c+negative_c))*100), 2),0) AS nsr,
        ifnull(round((((ifnull(LEAD(positive_c) OVER (ORDER BY date_ DESC), 0)-ifnull(LEAD(negative_c) OVER (ORDER BY date_ DESC), 0))/(ifnull(LEAD(positive_c) OVER (ORDER BY date_ DESC), 0)+ifnull(LEAD(negative_c) OVER (ORDER BY date_ DESC), 0)))*100), 2),0) AS nsrUp,
        <!-- 情感分析基础数据 -->
        ifnull(positive_c, 0) AS positive,
        ifnull(negative_c, 0) AS negative,
        <!-- 环比数据 -->
        ifnull(LEAD(positive_c) OVER (ORDER BY date_ DESC), 0) AS positive_r,
        ifnull(LEAD(negative_c) OVER (ORDER BY date_ DESC), 0) AS negative_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
    </select>
<!--=========================================领导页面提及量与用户数=========================================================================-->
 <select id="mentionsUser"  resultType="com.car.stats.vo.VocOverBriefingValueVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
     ROUND(CASE WHEN oneid_r = 0 AND (usersNum - oneid_r) != 0 THEN 999999
     ELSE IFNULL((usersNum - oneid_r) / IF(oneid_r = 0, 1, oneid_r) * 100, 0) END, 2) as usersNumR,
     ROUND(CASE WHEN voice_r = 0 AND (totalMentions - voice_r) != 0 THEN 999999
     ELSE IFNULL((totalMentions - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as totalMentionsR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        <!-- 情感分析基础数据 -->
        ifnull(voice_c, 0) AS totalMentions,
        ifnull(oneid_c, 0) AS usersNum,

        ifnull(LEAD(voice_c) OVER (ORDER BY date_ DESC), 0) AS voice_r,
        ifnull(LEAD(oneid_c) OVER (ORDER BY date_ DESC), 0) AS oneid_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            COUNT(1) as voice_c,
            COUNT(DISTINCT one_id) as oneid_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
                AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
            </if>
            GROUP BY date_str
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            COUNT(1) as voice_c,
            COUNT(DISTINCT one_id) as oneid_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
                AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
            </if>
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
    </select>
<!--===========================================领导页面-来源渠道================================================================================= -->
 <select id="sourceChannel"  resultType="com.car.stats.vo.ChannelVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        voc_channel_2 as channelId,
        <!-- 情感分析基础数据 -->
        ifnull(voice_c, 0) AS statistic,
        ifnull(LEAD(voice_c) OVER (ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,voc_channel_2,
            COUNT(1) as voice_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str,voc_channel_2
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,voc_channel_2,
            cdt.startDate, cdt.endDate,
            COUNT(1) as voice_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), voc_channel_2,cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
    </select>
    <!-- ================================================领导层-车系情感分布查询==================================================== -->
 <select id="homeLeaderCarDistribution"  resultType="com.car.stats.vo.CarEmotionVo">
     <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
     SELECT
     *,
     <include refid="publicDateFilterCriteria.baseChainRatios" />,
     <!-- 根据数据类型选择情感分析百分比计算方式 -->
     <if test="model.dataType !=null and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers">
         <include refid="publicDateFilterCriteria.emotionPercentageUsers" />
         userCount as total,
     </if>
     <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention">
         <include refid="publicDateFilterCriteria.emotionPercentageMention" />,
         intention as total,
     </if>
     <!-- 情感分析环比计算 -->
     ROUND(CASE WHEN positive_r = 0 AND (positive - positive_r) != 0 THEN 999999
     ELSE IFNULL((positive - positive_r) / IF(positive_r = 0, 1, positive_r) * 100, 0) END, 2) as positiveR,
     ROUND(CASE WHEN negative_r = 0 AND (negative - negative_r) != 0 THEN 999999
     ELSE IFNULL((negative - negative_r) / IF(negative_r = 0, 1, negative_r) * 100, 0) END, 2) as negativeR,
     ROUND(CASE WHEN neutral_r = 0 AND (neutral - neutral_r) != 0 THEN 999999
     ELSE IFNULL((neutral - neutral_r) / IF(neutral_r = 0, 1, neutral_r) * 100, 0) END, 2) as neutralR
     FROM (
     SELECT
     cc.*,
     cc.date_ AS dateStr,
     car_series_code as carSeries,
     <include refid="publicDateFilterCriteria.baseMetrics" />,
     <if test="model.dataType !=null and model.dataType == @com.car.voc.common.enums.DataEnum@numUsers">
         ifnull(LEAD(oneid_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS totalR,

     </if>
     <if test="model.dataType != null and model.dataType == @com.car.voc.common.enums.DataEnum@numMention">
         ifnull(LEAD(voice_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS totalR,
     </if>

     <!-- 情感分析基础数据 -->
     ifnull(positive_c, 0) AS positive,
     ifnull(negative_c, 0) AS negative,
     ifnull(neutral_c, 0) AS neutral,
     <!-- 环比数据 -->
     ifnull(LEAD(positive_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS positive_r,
     ifnull(LEAD(negative_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS negative_r,
     ifnull(LEAD(neutral_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS neutral_r,
     ifnull(LEAD(voice_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS voice_r,
     ifnull(LEAD(oneid_c) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS oneid_r,
     ifnull(LEAD(voice_c/cc.days) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS voice_avgr,
     ifnull(LEAD(oneid_c/cc.days) OVER (PARTITION BY car_series_code ORDER BY date_ DESC), 0) AS oneid_avgr
     FROM common_date_tools cc
     LEFT JOIN (
     <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
         SELECT
         <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
         car_series_code,
         COUNT(1) AS voice_c,
         <include refid="publicDateFilterCriteria.commonEmotionUserCountQuery"/>
         COUNT(DISTINCT one_id) AS oneid_c
         <include refid="publicDateFilterCriteria.commonDateQuery" />
         <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
         GROUP BY date_str, car_series_code
     </if>

     <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
         SELECT
         DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,car_series_code,
         cdt.startDate, cdt.endDate,
         COUNT(1) AS voice_c,
         <include refid="publicDateFilterCriteria.commonEmotionUserCountQuery"/>
         COUNT(DISTINCT one_id) AS oneid_c
         FROM common_date_tools cdt
         LEFT JOIN tf_dwd_voc_sentence vid
         ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
         WHERE 1=1
         <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
         GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),car_series_code, cdt.startDate, cdt.endDate
     </if>
     ) f1 ON cc.date_ = f1.date_str
     ORDER BY date_ DESC
     ) f2
     WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
     AND carSeries IS NOT NULL
     ORDER BY intention DESC
 </select>

    <!-- ================================================领导层-标签下钻趋势查询==================================================== -->
    <select id="allTagsTrend"  resultType="com.car.stats.vo.HomePurposeTrendVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr" />
        SELECT
        *,
        ROUND(CASE WHEN oneid_r = 0 AND (userCount - oneid_r) != 0 THEN 999999
        ELSE IFNULL((userCount - oneid_r) / IF(oneid_r = 0, 1, oneid_r) * 100, 0) END, 2) as userCountR,
        ROUND(CASE WHEN oneid_avgr = 0 AND (userCountA - oneid_avgr) != 0 THEN 999999
        ELSE IFNULL((userCountA - oneid_avgr) / IF(oneid_avgr = 0, 1, oneid_avgr) * 100, 0) END, 2) as userCountAR,
        ROUND(CASE WHEN voice_r = 0 AND (intention - voice_r) != 0 THEN 999999
        ELSE IFNULL((intention - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as intentionR,
        ROUND(CASE WHEN voice_avgr = 0 AND (intentionA - voice_avgr) != 0 THEN 999999
        ELSE IFNULL((intentionA - voice_avgr) / IF(voice_avgr = 0, 1, voice_avgr) * 100, 0) END, 2) as intentionAR,
        <include refid="publicDateFilterCriteria.baseChainRatios" />
        FROM (
        SELECT
        cc.*,
        case when length(cc.date_)>9 then date_format(date_, '%Y/%m/%d')
        else cc.date_ end as date_str,
        ifnull(voice_c, 0) AS intention,
        ifnull(oneid_c, 0) AS userCount,
        round(ifnull(voice_c / cc.days, 0), 2) AS intentionA,
        round(ifnull(oneid_c / cc.days, 0), 2) AS userCountA,
        ifnull(LEAD(voice_c) over(order by date_ desc),0) as voice_r,
        ifnull(LEAD(oneid_c) over(order by date_ desc),0) as oneid_r,
        ifnull(LEAD(voice_c/cc.days) over(order by date_ desc),0) as voice_avgr,
        ifnull(LEAD(oneid_c/cc.days) over(order by date_ desc),0) as oneid_avgr
        FROM common_date_tools cc
        LEFT JOIN (
        SELECT
        <include refid="publicDateFilterCriteria.dateFormatCase-Trend" /> AS date_str,
        COUNT(1) as voice_c,
        COUNT(DISTINCT one_id) as oneid_c
        <include refid="publicDateFilterCriteria.commonDateQuery" />
        <include refid="publicDateFilterCriteria.queryPop_vid_emotion_sr" />
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE=#{model.topicCode}
        </if>
        <if test="model.emotionKeyword !=null and model.emotionKeyword !=''" >
            AND vid.EMOTION_KEYWORD =#{model.emotionKeyword}
        </if>
        GROUP BY date_str
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        ORDER BY date_ ASC
    </select>
    <!-- ================================================领导层-意图占比==================================================== -->
    <select id="homeLeaderIntentionProp"  resultType="com.car.stats.vo.IntentionRatioVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
        <include refid="publicDateFilterCriteria.intentionPercentageMention" />,
        (consult+complaint+praise+suggest+other) as total,
        ROUND(CASE WHEN consult_r = 0 AND (consult - consult_r) != 0 THEN 999999
        ELSE IFNULL((consult - consult_r) / IF(consult_r = 0, 1, consult_r) * 100, 0) END, 2) as consultR,
        ROUND(CASE WHEN complaint_r = 0 AND (complaint - complaint_r) != 0 THEN 999999
        ELSE IFNULL((complaint - complaint_r) / IF(complaint_r = 0, 1, complaint_r) * 100, 0) END, 2) as complaintR,
        ROUND(CASE WHEN praise_r = 0 AND (praise - praise_r) != 0 THEN 999999
        ELSE IFNULL((praise - praise_r) / IF(praise_r = 0, 1, praise_r) * 100, 0) END, 2) as praiseR,
        ROUND(CASE WHEN suggest_r = 0 AND (suggest - suggest_r) != 0 THEN 999999
        ELSE IFNULL((suggest - suggest_r) / IF(suggest_r = 0, 1, suggest_r) * 100, 0) END, 2) as suggestR,
        ROUND(CASE WHEN other_r = 0 AND (other - other_r) != 0 THEN 999999
        ELSE IFNULL((other - other_r) / IF(other_r = 0, 1, other_r) * 100, 0) END, 2) as otherR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        ifnull(consult_c, 0) AS consult,
        ifnull(complaint_c, 0) AS complaint,
        ifnull(praise_c, 0) AS praise,
        ifnull(suggest_c, 0) AS suggest,
        ifnull(other_c, 0) AS other,
        <!-- 环比数据 -->
        ifnull(LEAD(consult_c) OVER (ORDER BY date_ DESC), 0) AS consult_r,
        ifnull(LEAD(complaint_c) OVER (ORDER BY date_ DESC), 0) AS complaint_r,
        ifnull(LEAD(praise_c) OVER (ORDER BY date_ DESC), 0) AS praise_r,
        ifnull(LEAD(suggest_c) OVER (ORDER BY date_ DESC), 0) AS suggest_r,
        ifnull(LEAD(other_c) OVER (ORDER BY date_ DESC), 0) AS other_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            <include refid="publicDateFilterCriteria.commonIntentionUserCountQuery" />
            COUNT(1) AS voice_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            <include refid="publicDateFilterCriteria.commonIntentionUserCountQuery" />
            COUNT(1) AS voice_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
    </select>
<!-- ================================================领导层-情感占比==================================================== -->
    <select id="homeLeaderEmotionProp"  resultType="com.car.stats.vo.EmotionProportionVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT

        *,
        <include refid="publicDateFilterCriteria.emotionPercentageMention" />
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        ifnull(positive_c, 0) AS positive,
        ifnull(negative_c, 0) AS negative,
        ifnull(neutral_c, 0) AS neutral,
        ifnull(voice_c, 0) AS total
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            <include refid="publicDateFilterCriteria.commonEmotionUserCountQuery" />
            COUNT(1) AS voice_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            <include refid="publicDateFilterCriteria.commonEmotionUserCountQuery" />
            COUNT(1) AS voice_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
    </select>


<!-- ================================================领导层-质量一级标签分布==================================================== -->
    <select id="qTag1Dist"  resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
       first_dimension_code  AS labelCode,
        COUNT(1) as statistic
        from tf_dwd_voc_sentence vid
        where  1=1
        and tag_type='2'
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        GROUP BY  first_dimension_code
        order by statistic DESC
    </select>
<!-- ================================================领导层-一级标签分布==================================================== -->
    <select id="allTag1Dist"  resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
        CASE
        WHEN tag_type = '1' THEN first_dimension_code
        WHEN tag_type = '2' THEN 'Q0001'
        END AS labelCode,
        COUNT(1) as statistic
        from tf_dwd_voc_sentence vid
        where  1=1
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        GROUP BY  CASE
        WHEN tag_type = '1' THEN first_dimension_code
        WHEN tag_type = '2' THEN 'Q0001'
        END
        order by statistic DESC
    </select>



<!-- ================================================领导层-二级标签分布==================================================== -->
    <select id="allTag2Dist"  resultType="com.car.stats.vo.ThemeDistrVo">
        SELECT
        CASE
            WHEN tag_type = '1' THEN second_dimension_code
            WHEN tag_type = '2' THEN second_dimension_code
        END AS labelCode,
        COUNT(1) as statistic
        from tf_dwd_voc_sentence vid
        where
            1=1
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        GROUP BY  CASE
            WHEN tag_type = '1' THEN second_dimension_code
            WHEN tag_type = '2' THEN second_dimension_code
        END
        order by statistic DESC
    </select>


<!-- ================================================领导层-三级标签分布==================================================== -->
    <select id="allTag3Dist"  resultType="com.car.stats.vo.ThemeDistrVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *
        FROM (
        SELECT
        cc.*,
        cc.date_ as dateStr,
        three_dimension_code as labelCode,
        ifnull(voice_c, 0) AS statistic
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,three_dimension_code,
            COUNT(1) as voice_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str,three_dimension_code
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            three_dimension_code,
            COUNT(1) as voice_c,
            COUNT(DISTINCT one_id) as oneid_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate,three_dimension_code
        </if>

        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY date_ ASC
    </select>
    <select id="tagDistribution3"  resultType="com.car.stats.vo.ThemeDistrVo">
        select
        vid.three_dimension_code as labelCode,
        count(1) as statistic,
        count(distinct one_id) as userNum
        from tf_dwd_voc_sentence vid
        where 1=1
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        group by vid.three_dimension_code

    </select>
    <select id="tagDistribution2"  resultType="com.car.stats.vo.ThemeDistrVo">
        select
        vid.second_dimension_code as labelCode,
        count(1) as statistic,
        count(distinct one_id) as userNum
        from tf_dwd_voc_sentence vid
        where 1=1
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        group by vid.second_dimension_code

    </select>
    <select id="tagDistribution4"  resultType="com.car.stats.vo.ThemeDistrVo">
        select
        vid.topic_code as labelCode,
        count(1) as statistic,
        count(distinct one_id) as userNum
        from tf_dwd_voc_sentence vid
        where 1=1
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        group by vid.topic_code

    </select>
    <select id="tagDistribution"  resultType="com.car.stats.vo.ThemeDistrVo">
        select
        vid.first_dimension_code as labelCode,
        count(1) as statistic,
        count(distinct one_id) as userNum
        from tf_dwd_voc_sentence vid
        where 1=1
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        group by vid.first_dimension_code

    </select>
    <select id="hotWords"  resultType="com.car.stats.vo.HighHotWordsVo">
        select
        vid.emotion_keyword as keyword,
        count(CASE WHEN vid.dimension_emotion = '正面' THEN 1 END) as positive,
        count(CASE WHEN vid.dimension_emotion = '中性' THEN 1 END) as neutral,
        count(CASE WHEN vid.dimension_emotion = '负面' THEN 1 END) as negative,
        count(1) as statistic
        from tf_dwd_voc_sentence vid
        where 1=1
        and emotion_keyword is not null
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        group by vid.emotion_keyword
        order by statistic desc
        <if test="model.rownum !=null">
            limit #{model.rownum}
        </if>
        <if test="model.rownum ==null ">
            limit 30
        </if>

    </select>

<!-- ================================================领导层-四级标签分布==================================================== -->
    <select id="allTag4Dist"  resultType="com.car.stats.vo.ThemeDistrVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *
        FROM (
        SELECT
        cc.*,
        cc.date_ as dateStr,
        topic_code as labelCode,
        ifnull(voice_c, 0) AS statistic
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,topic_code,
            COUNT(1) as voice_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str,topic_code
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            cdt.startDate, cdt.endDate,
            topic_code,
            COUNT(1) as voice_c,
            COUNT(DISTINCT one_id) as oneid_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'), cdt.startDate, cdt.endDate,topic_code
        </if>

        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY date_ ASC
    </select>

<!-- ================================================领导层-车机智能化问题==================================================== -->
    <select id="homeLeaderCarIntelligent"  resultType="com.car.stats.vo.QualityLabelVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        labelCode,
        voice_c as statistic,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY labelCode ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            CASE WHEN tag_type = 2 THEN CONCAT(three_dimension_code, '#', topic_code) ELSE topic_code END as labelCode,
            COUNT(1) AS voice_c
            <include refid="publicDateFilterCriteria.commonDateQuery" />
            and (
            (tag_type = 2 AND three_dimension_code LIKE 'Q1001027%')
            OR
            (tag_type = 1 AND first_dimension_code = 'B1004')
            )
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str,labelCode
        </if>

        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            CASE WHEN tag_type = 2 THEN CONCAT(three_dimension_code, '#', topic_code) ELSE topic_code END as labelCode,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid
            ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            and (
            (tag_type = 2 AND three_dimension_code LIKE 'Q1001027%')
            OR
            (tag_type = 1 AND first_dimension_code = 'B1004')
            )
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),
            labelCode, cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>

<!-- ================================================领导层-大区排行==================================================== -->
    <select id="homeLeaderRegionalTop"  resultType="com.car.stats.vo.RegionUserVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
        <include refid="publicDateFilterCriteria.emotionPercentageMention"/>,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        regionCode,
        statisticP,
        voice_c as statistic,
        positive_c as positive,
        neutral_c as neutral,
        negative_c as negative,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY regionCode ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            vbr.regional_code as regionCode,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP,
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '中性' THEN 1 ELSE 0 END) as neutral_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c
            from tf_dwd_voc_sentence vid
            left join  (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
            )c9 on vid.publish_date BETWEEN startDate and endDate
            LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_max" /> vbr on vid.province =vbr.province_code
            WHERE 1=1
            and vid.brand_code =vbr.brand
            <if test="model.brandCode != null and model.brandCode != 'A11'">
                and vbr.application_type =2
            </if>
            and vid.publish_date BETWEEN startDate and endDate
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str,vbr.regional_code
        </if>

        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            vbr.regional_code as regionCode,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP,
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '中性' THEN 1 ELSE 0 END) as neutral_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_max" /> vbr on vid.province =vbr.province_code
            WHERE 1=1
            and vid.brand_code =vbr.brand
            <if test="model.brandCode != null and model.brandCode != 'A11'">
                and vbr.application_type =2
            </if>
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),
            vbr.regional_code, cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>

<!-- ================================================领导层-小区排行==================================================== -->
    <select id="homeLeaderCommunityTop"  resultType="com.car.stats.vo.RegionUserVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
       <include refid="publicDateFilterCriteria.emotionPercentageMention"/>,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        regionCode,
        statisticP,
        voice_c as statistic,
        neutral_c as neutral,
        positive_c as positive,
        negative_c as negative,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY regionCode ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.dateFormatCase" /> AS date_str,
            vbr.community_code as regionCode,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP,
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '中性' THEN 1 ELSE 0 END) as neutral_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c
            from tf_dwd_voc_sentence vid
            left join  (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
            )c9 on vid.publish_date BETWEEN startDate and endDate
            LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_min" /> vbr on vid.city =vbr.city_code
            WHERE 1=1
            and vid.brand_code =vbr.brand
            and vbr.city_code is not null
            <if test="model.brandCode != null and model.brandCode != 'A11'">
                and vbr.application_type =2
            </if>
            and vid.publish_date BETWEEN startDate and endDate
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY date_str,vbr.community_code
        </if>

        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            vbr.community_code as regionCode,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP,
            sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive_c,
            sum(CASE WHEN dimension_emotion = '中性' THEN 1 ELSE 0 END) as neutral_c,
            sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative_c
            FROM common_date_tools cdt
            LEFT JOIN tf_dwd_voc_sentence vid ON vid.publish_date BETWEEN cdt.startDate AND cdt.endDate
            LEFT JOIN <include refid="publicDateFilterCriteria.voc_brand_region_min" />  vbr on vid.city =vbr.city_code
            WHERE 1=1
            and vid.brand_code =vbr.brand
            and vbr.city_code is not null
            <if test="model.brandCode != null and model.brandCode != 'A11'">
                and vbr.application_type =2
            </if>
            <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),
            vbr.community_code, cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>

    <!-- ================================================领导层-地图省份==================================================== -->
    <select id="provinceMap" resultType="com.car.stats.vo.RegionUserVo">
        SELECT
        sf.*,
        <include refid="publicDateFilterCriteria.emotionPercentageMention"/>,
        di.ITEM_TEXT as regionStr
        FROM
        (
        SELECT
        PROVINCE as regionCode,
        count( DISTINCT one_id ) as userNum,
        count(1) as statistic,
        sum(CASE WHEN dimension_emotion = '正面' THEN 1 ELSE 0 END) as positive,
        sum(CASE WHEN dimension_emotion = '负面' THEN 1 ELSE 0 END) as negative,
        sum(CASE WHEN dimension_emotion = '中性' THEN 1 ELSE 0 END) as neutral,
        ROUND(COUNT(DISTINCT one_id) / SUM(COUNT(DISTINCT one_id)) OVER () * 100, 2) AS userNumP,
        ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
        FROM
        tf_dwd_voc_sentence vid
        WHERE
        1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        GROUP BY
        PROVINCE
        order by statistic desc
        ) sf
        LEFT JOIN SYS_DICT_ITEM di ON sf.regionCode=di.ITEM_VALUE
        WHERE
        1=1
        AND di.DICT_ID='53aad639aca4b5c010927cf610c3ff9c'
    </select>

    <!-- ================================================商机线索-购车意向分布==================================================== -->
    <select id="carPurchaseDist" resultType="com.car.stats.vo.popvo.UserLabelVo">
        SELECT
        second_dimension_code as labelCode,
        count( DISTINCT one_id ) as userCount,
        count(1) as statistic,
        ROUND(COUNT(DISTINCT one_id) / SUM(COUNT(DISTINCT one_id)) OVER () * 100, 2) AS userCountP,
        ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
        FROM
        tf_dwd_voc_sentence vid
        WHERE
        1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        and vid.FIRST_DIMENSION_CODE='B1001'
        <include refid="publicDateFilterCriteria.sr_query_intention_com_vid" />
        GROUP BY
        second_dimension_code
        order by statistic desc
        limit 10
    </select>


    <!-- ================================================商机线索-购车关注热点==================================================== -->
    <select id="purchaseConcernsPriority" resultType="com.car.stats.vo.HighHotWordsVo">
        SELECT
        emotion_keyword as keyword,
        count(1) as statistic,
        0 as negative,
        0 as neutral,
        count(1) as positive
        FROM
        tf_dwd_voc_sentence vid
        WHERE
        1=1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.sr_query_date_com_vid" />
        and emotion_keyword is not null
        GROUP BY
        emotion_keyword
        order by statistic desc
        limit 30
    </select>



</mapper>
