<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ComFilterMapper">

    <!--  =======================公共筛选条件========start=======-->
    <sql id="general_query">

        <if test="model.isOneId != null "> AND vid.IS_ONE_ID =#{model.isOneId} </if>

        <if test="model.firstDimensionCode !=null and model.firstDimensionCode !=''" >
            AND vid.FIRST_DIMENSION_CODE =#{model.firstDimensionCode}
        </if>
        <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
            and vid.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="model.brandCode != null and model.brandCode != ''">
            AND vid.brand_code = #{model.brandCode}
        </if>
        <!--====================标签权限=============begin=============-->
        <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
            and vid.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--====================标签权限=============end=============-->

        <!--====================车系权限=============begin=============-->
        <if test="model.carSeriesPowers != null and model.carSeriesPowers.size()>0">
            and vid.CAR_SERIES_CODE in
            <foreach item="item" collection="model.carSeriesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--====================车系权限=============end=============-->

                <choose>
                    <when test="model.province != null and model.province.size()>0">
                        and vid.PROVINCE in
                        <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </when>
                    <!--====================省份权限=============begin=============-->

                    <otherwise>
                        <if test="model.provinceCodesPowers != null and model.provinceCodesPowers.size()>0">
                            and (
                            ( vid.PROVINCE in
                            <foreach item="item" collection="model.provinceCodesPowers" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                            )
                            or vid.PROVINCE is null

                            )
                        </if>
                    </otherwise>
                    <!--====================省份权限=============end=============-->
                </choose>


        <if test="model.region != null and model.region =='0000000' ">
            AND vid.PROVINCE is null
            AND vid.AREA_CODE =#{model.region}
        </if>

        <if test="model.carSeries != null and model.carSeries.size()>0">
            and vid.CAR_SERIES_CODE in
            <foreach item="item" collection="model.carSeries" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.carSeriesDisables != null and model.carSeriesDisables.size()>0">
            and vid.CAR_SERIES_CODE not in
            <foreach item="item" collection="model.carSeriesDisables" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    <!--
              <if test="model.areas != null and model.areas.size()>0">
            and vid.AREA_CODE  in
            <foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

      -->




    </sql>


    <!--  =======================公共筛选条件 StarRocks========start=======-->
    <sql id="sr_general_query">

        <if test="model.firstDimensionCode !=null and model.firstDimensionCode !=''" >
            AND vid.FIRST_DIMENSION_CODE =#{model.firstDimensionCode}
        </if>
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
        </if>
        <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
            and vid.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="model.firstDimensionCodes != null and model.firstDimensionCodes.size()>0">
            and vid.FIRST_DIMENSION_CODE in
            <foreach item="item" collection="model.firstDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="model.brandCode != null and model.brandCode != ''">
            AND vid.brand_code = #{model.brandCode}
        </if>
        <!--====================标签权限=============begin=============-->
        <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
            and vid.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--====================标签权限=============end=============-->

        <!--====================车系权限=============begin=============-->
        <if test="model.carSeriesPowers != null and model.carSeriesPowers.size()>0">
            and vid.CAR_SERIES_CODE in
            <foreach item="item" collection="model.carSeriesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--====================车系权限=============end=============-->
                <choose>
                    <when test="model.province != null and model.province.size()>0">
                        and vid.PROVINCE in
                        <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </when>
                    <!--====================省份权限=============begin=============-->

                    <otherwise>
                        <if test="model.provinceCodesPowers != null and model.provinceCodesPowers.size()>0">
                            and (
                            ( vid.PROVINCE in
                            <foreach item="item" collection="model.provinceCodesPowers" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                            )
                            or vid.PROVINCE is null

                            )
                        </if>
                    </otherwise>
                    <!--====================省份权限=============end=============-->
                </choose>



        <if test="model.region != null and model.region =='0000000' ">
            AND vid.PROVINCE is null
            AND vid.AREA_CODE =#{model.region}
        </if>

        <if test="model.carSeries != null and model.carSeries.size()>0">
            and vid.CAR_SERIES_CODE in
            <foreach item="item" collection="model.carSeries" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.carSeriesDisables != null and model.carSeriesDisables.size()>0">
            and vid.CAR_SERIES_CODE not in
            <foreach item="item" collection="model.carSeriesDisables" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--
                  <if test="model.areas != null and model.areas.size()>0">
                and vid.AREA_CODE  in
                <foreach item="item" collection="model.areas" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>

          -->



    </sql>



    <!--====================渠道过滤StarRocks=============begin=============-->
    <sql id="sr_general_query_dataSorce">
        <!--====================渠道过滤====================-->
        <choose>
            <when test="model.channelIds != null and model.channelIds.size()>0">
                and vid.CHANNEL_ID in
                <foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>

            <when test="model.channelIds != null and model.channelIds.size()==0 and model.dataSources != null and model.dataSources.size()>0 ">
                and vid.voc_channel_1 in
                <foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
            <when test="model.channelIds == null and model.dataSources != null and model.dataSources.size()>0 ">
                and vid.voc_channel_1 in
                <foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
        </choose>





        <!--====================渠道权限=============begin=============-->
        <if test="model.channelIdPowers != null and model.channelIdPowers.size()>0">
            and vid.CHANNEL_ID in
            <foreach item="item" collection="model.channelIdPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


    </sql>
    <!--====================渠道过滤=============end=============-->








    <sql id="general_query_Tag_Disables" >
        <if test="model.tagTopicCodesDisables != null and model.tagTopicCodesDisables.size()>0">
            and vid.TOPIC_CODE not in
            <foreach item="item" collection="model.tagTopicCodesDisables" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.qualityTopicCodesDisables != null and model.qualityTopicCodesDisables.size()>0">
            and vid.TOPIC_CODE not in
            <foreach item="item" collection="model.qualityTopicCodesDisables" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="general_query_Other_Tag" >
        <if test="model.otherTag != null and model.otherTag.size()>0">
            and vid.TOPIC_CODE not in
            <foreach item="item" collection="model.otherTag" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>



    <!--====================渠道过滤=============begin=============-->
    <sql id="general_query_dataSorce">





        <!--====================渠道过滤====================-->
        <choose>
            <when test="model.channelIds != null and model.channelIds.size()>0">
                and vid.CHANNEL_ID in
                <foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>

            <when test="model.channelIds != null and model.channelIds.size()==0 and model.dataSources != null and model.dataSources.size()>0 ">
                and vid.DATA_SOURCE in
                <foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
            <when test="model.channelIds == null and model.dataSources != null and model.dataSources.size()>0 ">
                and vid.DATA_SOURCE in
                <foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
        </choose>





        <!--====================渠道权限=============begin=============-->
        <if test="model.channelIdPowers != null and model.channelIdPowers.size()>0">
            and vid.CHANNEL_ID in
            <foreach item="item" collection="model.channelIdPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


    </sql>
    <!--====================渠道过滤=============end=============-->




    <sql id="general_query_chan_dataSorce">

        <choose>
            <when test="model.channelIds != null and model.channelIds.size()>0">
                and vid.CHANNEL_ID in
                <foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
            <when test="model.channelIds != null and model.channelIds.size()==0 and model.dataSources != null and model.dataSources.size()>0 ">
                and vid.DATA_SOURCE in
                <foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
            <when test="model.channelIds == null and model.dataSources != null and model.dataSources.size()>0 ">
                and vid.DATA_SOURCE in
                <foreach item="item" collection="model.dataSources" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
        </choose>


        <!--====================渠道权限=============begin=============-->
        <if test="model.channelIdPowers != null and model.channelIdPowers.size()>0">
            and vid.CHANNEL_ID in
            <foreach item="item" collection="model.channelIdPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


    </sql>
    <sql id="qualityThreeCodes">
        <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
            and vid.THREE_DIMENSION_CODE in
            <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

    </sql>
    <!--  =======================公共筛选条件========end=======-->

    <!--  =======================公共筛选条件========start=======-->
    <sql id="general_WORKORDER_query">

        <if test="model.isOneId != null "> AND vid.IS_ONE_ID =#{model.isOneId} </if>

        <if test="model.firstDimensionCode !=null and model.firstDimensionCode !=''" >
            AND vid.FIRST_DIMENSION_CODE =#{model.firstDimensionCode}
        </if>
        <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
            and vid.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


        <!--====================标签权限=============begin=============-->
        <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
            and vid.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--====================标签权限=============end=============-->


        <!--====================车系权限=============begin=============-->
        <if test="model.carSeriesPowers != null and model.carSeriesPowers.size()>0">
            and vid.CAR_SERIES_CODE in
            <foreach item="item" collection="model.carSeriesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--====================车系权限=============end=============-->



        <if test="model.channelIds != null and model.channelIds.size()>0">
            and vid.CHANNEL_ID in
            <foreach item="item" collection="model.channelIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


        <if test="model.province != null and model.province.size()>0">
            and vid.PROVINCE in
            <foreach item="item" collection="model.province" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="model.region != null and model.region =='0000000' ">
            AND vid.PROVINCE is null
            AND vid.AREA_CODE is null
        </if>

        <if test="model.carSeries != null and model.carSeries.size()>0">
            and vid.CAR_SERIES_CODE in
            <foreach item="item" collection="model.carSeries" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>
    <!--  =======================公共筛选条件========end=======-->




    <!--  =======================风险公共筛选条件========start=======-->
    <sql id="general_risk_query">



        <if test="model.firstDimensionCode !=null and model.firstDimensionCode !=''" >
            AND vid.FIRST_DIMENSION_CODE =#{model.firstDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.TOPIC_CODE =#{model.topicCode}
        </if>
    <!--  <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
         and vid.SECOND_DIMENSION_CODE in
         <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
             #{item}
         </foreach>
     </if>
     -->

 </sql>
 <!--  =======================风险公共筛选条件========end=======-->





    <!--  =======================多品牌风险公共筛选条件========start=======-->

    <sql id="com_risk_brand_createDate">
        <if test="model.brandCode != null and model.brandCode != ''">
            AND vid.brand_code =#{model.brandCode}
        </if>
        <if test="model.createDate !=null and model.createDate != ''">
            AND #{model.createDate}>=vid.create_time
        </if>
    </sql>
    <!--  =======================多品牌风险公共筛选条件========end=======-->



    <sql id="this_problemLevel">
        <if test="model.problemLevel !=null and model.problemLevel != ''">
            AND vid.PROBLEM_LEVEL =#{model.problemLevel}
        </if>
    </sql>

    <sql id="this_date">
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.publish_date between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
            and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
        </if>
    </sql>

    <sql id="this_date_new">
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.publish_date between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
        </if>
    </sql>

    <sql id="this_risk_date">
            <!--======================日==================-->
                <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                    and vid.PUBLISH_DATE>=#{model.startDate}
                    AND #{model.endDate}>=vid.PUBLISH_DATE
                </if>
    </sql>
    <sql id="this_risk_date1">
            <!--======================日==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
                <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                    and vid.PUBLISH_DATE>=#{model.startDate}
                    AND #{model.endDate}>=vid.PUBLISH_DATE
                </if>
            </if>
            <!--======================周度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==0 " >
                and vid.DATE_WEEK=#{model.week}
            </if>
            <!--======================月度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==1 " >
                and vid.DATE_MONTH=#{model.month}
            </if>
            <!--======================季度==================-->
            <if test="model.dateUnit !=null and model.dateUnit ==2 " >
                and vid.DATE_QUARTER=#{model.season}
            </if>


    </sql>


        <sql id="this_risk_date_year">
                <!--======================年度==================-->
            <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
                and vid.DATE_YEAR=#{model.year}
            </if>

        </sql>
        <sql id="this_statisticType">
                <!--======================统计周期类型==================-->
            <if test="model.statisticType !=null and model.statisticType !=''   " >
                 and vid.statistic_type=#{model.statisticType}
           </if>
       </sql>

   <sql id="up_date">
       <if test="model.startDateUp !=null and model.startDateUp != ''and
                 model.endDateUp !=null and model.endDateUp != ''">
           and vid.publish_date between  DATE_FORMAT(#{model.startDateUp}, '%Y-%m-%d %H:%i:%s')
           and DATE_FORMAT(#{model.endDateUp}, '%Y-%m-%d %H:%i:%s')
       </if>
   </sql>

   <sql id="emotion_type">
       <if test="model.emotion != null and model.emotion != '' ">
           AND vid.DIMENSION_EMOTION =#{model.emotion}
       </if>
   </sql>
   <sql id="tag_type">
       <if test="model.tagType != null  ">
           AND vid.tag_type =#{model.tagType}
       </if>
   </sql>
    <sql id="emotion_types">
        <choose>
            <when test="model.emotions != null and model.emotions.size()>0">
                and vid.DIMENSION_EMOTION in
                <foreach item="item" collection="model.emotions" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </when>
            <when test="model.emotion != null and model.emotion != '' ">
                AND vid.DIMENSION_EMOTION =#{model.emotion}
            </when>
            <otherwise></otherwise>
        </choose>

    </sql>
   <sql id="intention_type">
       <if test="model.intention != null and model.intention != '' ">
           AND vid.INTENTION_TYPE = #{model.intention}
       </if>
   </sql>


   <sql id="secondAndthreeLableQuery">
       <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''  " >
           AND vid.SECOND_DIMENSION_CODE =#{model.secondDimensionCode}
       </if>

       <if test="model.thirdDimensionCode !=null and model.thirdDimensionCode !=''" >
           AND vid.THREE_DIMENSION_CODE =#{model.thirdDimensionCode}
       </if>

   </sql>

   <sql id="topicCodeTagQuery">
       <if test="model.topicCode !=null and model.topicCode !=''  " >
           AND vid.topic_code =#{model.topicCode}
       </if>
   </sql>



   <sql id="riskWarningLevel" >
       <if test="model.riskWarningLevel">
           and vid.RISK_LEVEL=#{model.riskWarningLevel}
       </if>
   </sql>


    <sql id="date-group-base">
            (select * from voc_date_num) AS a,
            (select * from voc_date_num) AS b,
            (select * from voc_date_num) AS c
    </sql>

    <sql id="week_month_quarter_year">
        <!--======================周度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
            weeks AS (
            SELECT
            CONCAT( LEFT(YEARWEEK(base_date, 3), 4), '-', RIGHT(YEARWEEK(base_date, 3), 2)) AS week,
            DATE_FORMAT(base_date, '%Y-%m-%d') AS start_date,
            DATE_FORMAT(
            CASE
            WHEN DATE_FORMAT(base_date, '%Y-%m-%d') = DATE_FORMAT(CURRENT_DATE(), '%Y-%m-%d')
            THEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
            WHEN DATE_ADD(base_date, INTERVAL 6 DAY) >= CURRENT_DATE()
            THEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)  -- 如果大于等于今天，则设置为昨天
            ELSE DATE_ADD(base_date, INTERVAL 6 DAY)
            END,
            '%Y-%m-%d'
            ) AS end_date
            FROM (
            SELECT DATE_SUB( DATE( DATE_ADD( DATE_SUB(d.endDate, INTERVAL (d.dateNum - 1) WEEK), INTERVAL m.num WEEK ) ),
                    INTERVAL WEEKDAY( DATE_ADD( DATE_SUB(d.endDate, INTERVAL (d.dateNum - 1) WEEK), INTERVAL m.num WEEK ) ) DAY ) AS base_date
            FROM date_range d
            CROSS JOIN num_sequence m
            WHERE (SELECT dateNum FROM date_range) > m.num
            ) dates
            )
            SELECT
            week AS date_,dr.endDate as endDate_,dr.dateNum,dr.date_unit,
            start_date as startDate,
            end_date as endDate,
            DATEDIFF(DATE(end_date), DATE(start_date)) + 1 AS days
            FROM weeks w,date_range dr
            WHERE  (SELECT CONCAT( LEFT(YEARWEEK(endDate, 3), 4), '-', RIGHT(YEARWEEK(endDate, 3), 2)) FROM date_range )>=CONCAT( LEFT(YEARWEEK(start_date, 3), 4), '-', RIGHT(YEARWEEK(start_date, 3), 2))
            ORDER BY date_ ASC
        </if>
        <!--======================月度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            months AS (
            SELECT
            DATE_FORMAT(base_date, '%Y-%m') AS month,
            DATE_FORMAT(base_date, '%Y-%m-%d') AS start_date,
            DATE_FORMAT(
            CASE
            WHEN DATE_FORMAT(base_date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE(), '%Y-%m')
            THEN  DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
            ELSE DATE_SUB(DATE_ADD(base_date, INTERVAL 1 MONTH), INTERVAL 1 DAY)
            END,
            '%Y-%m-%d'
            ) AS end_date
            FROM (
            SELECT  DATE_FORMAT( DATE_ADD( DATE_SUB(d.endDate, INTERVAL (d.dateNum - 1) MONTH), INTERVAL m.num MONTH ), '%Y-%m-01' ) AS base_date
            FROM date_range d
            CROSS JOIN num_sequence m
            WHERE (SELECT dateNum FROM date_range) > m.num
            ) dates
            )
            SELECT
            month AS date_,dr.endDate as endDate_,dr.dateNum,dr.date_unit,
            start_date as startDate,
            end_date as endDate,
            DATEDIFF(DATE(end_date), DATE(start_date)) + 1 AS days
            FROM months m,date_range dr
            WHERE  (SELECT DATE_FORMAT(endDate, '%Y-%m') FROM date_range)>=month
            ORDER BY date_ ASC
        </if>
        <!--======================季度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
            quarters AS (
            SELECT
            CONCAT(YEAR(base_date), '-', QUARTER(base_date)) AS quarter,
            DATE_FORMAT( DATE_SUB( DATE_ADD( DATE(CONCAT(YEAR(base_date), '-',
                    (QUARTER(base_date) - 1) * 3 + 1, '-01')), INTERVAL 3 MONTH ), INTERVAL 3 MONTH ), '%Y-%m-%d' ) AS start_date,
            DATE_FORMAT( CASE WHEN QUARTER(base_date) = QUARTER(CURRENT_DATE()) AND YEAR(base_date) = YEAR(CURRENT_DATE())
            THEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
            ELSE DATE_SUB( DATE_ADD( DATE(CONCAT(YEAR(base_date), '-', (QUARTER(base_date) - 1) * 3 + 1, '-01')), INTERVAL 3 MONTH ),
            INTERVAL 1 DAY )
            END, '%Y-%m-%d' ) AS end_date
            FROM (
            SELECT
            DATE_SUB(
            DATE_ADD(d.endDate, INTERVAL m.num QUARTER),
            INTERVAL (d.dateNum - 1) QUARTER
            ) AS base_date
            FROM date_range d
            CROSS JOIN num_sequence m
            WHERE (SELECT dateNum FROM date_range) > m.num
            ) dates
            )
            SELECT
            quarter AS date_,dr.endDate as endDate_,dr.dateNum,dr.date_unit,
            start_date as startDate,
            end_date as endDate,
            DATEDIFF(DATE(end_date), DATE(start_date)) + 1 AS days
            FROM quarters q,date_range dr
            WHERE ( SELECT CONCAT(YEAR(endDate), '-', QUARTER(endDate)) FROM date_range )>=quarter
            ORDER BY date_ ASC

        </if>
        <!--======================年度==================-->
        <if test="model.dateUnit !=null and model.dateUnit ==3 " >
            years AS (
            SELECT
            DATE_FORMAT(base_date, '%Y') AS year,
            DATE_FORMAT(base_date, '%Y-%m-%d') AS start_date,
            DATE_FORMAT(
            CASE
            WHEN YEAR(base_date) = YEAR(CURRENT_DATE())
            THEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
            ELSE DATE_SUB(
            DATE_ADD(
            DATE_ADD(base_date, INTERVAL 1 YEAR),
            INTERVAL 0 DAY
            ),
            INTERVAL 1 DAY
            )
            END,
            '%Y-%m-%d'
            ) AS end_date
            FROM (
            SELECT STR_TO_DATE( CONCAT( YEAR( DATE_ADD( DATE_SUB(d.endDate, INTERVAL (d.dateNum - 1) YEAR), INTERVAL m.num YEAR ) ), '-01-01' ), '%Y-%m-%d' ) AS base_date
                FROM date_range d
                CROSS JOIN num_sequence m WHERE
            (SELECT dateNum FROM date_range) > m.num
                ) dates
            )
            SELECT
            year AS date_,dr.endDate as endDate_,dr.dateNum,dr.date_unit,
            start_date as startDate,
            end_date as endDate,
            DATEDIFF(DATE(end_date), DATE(start_date)) + 1 AS days
            FROM years y,date_range dr
            WHERE (SELECT DATE_FORMAT(endDate, '%Y') FROM date_range)>=year
            ORDER BY date_ ASC
        </if>
    </sql>

    <!--====================工单时间筛选=============begin=============-->
    <sql id="wo_create_query">
        <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
            and vid.create_time between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
            and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
        </if>
    </sql>


 <!--====================工单表扩展=============begin=============-->
    <sql id="wo_table_view">
        SELECT
        vid.*,
            tag_code_random as original_tag_code,
            LEFT(tag_code_random, 5) as first_dimension_code,
            LEFT(tag_code_random, 8) as second_dimension_code,
            LEFT(tag_code_random, 11) as three_dimension_code,
            tag_code_random as topic_code
        FROM t317_csv_rescue_wo_i_d vid
        where 1=1
        <include refid="publicDateFilterCriteria.wo_original_query"/>
    </sql>


    <sql id="wo-lable-query">
        <if test="model.firstDimensionCode !=null and model.firstDimensionCode !=''" >
            AND vid.first_dimension_code = #{model.firstDimensionCode}
        </if>
        <if test="model.secondDimensionCode !=null and model.secondDimensionCode !=''" >
            AND vid.second_dimension_code = #{model.secondDimensionCode}
        </if>
        <if test="model.threeDimensionCode !=null and model.threeDimensionCode !=''" >
            AND vid.three_dimension_code = #{model.threeDimensionCode}
        </if>
        <if test="model.topicCode !=null and model.topicCode !=''" >
            AND vid.topic_code = #{model.topicCode}
        </if>
        <if test="model.firstDimensionCodes != null and model.firstDimensionCodes.size()>0">
            and vid.first_dimension_code in <foreach item="item" collection="model.firstDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
            and vid.second_dimension_code in <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.threeDimensionCodes != null and model.threeDimensionCodes.size()>0">
            and vid.three_dimension_code in <foreach item="item" collection="model.threeDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.topic_code in <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--========================================标签权限==========================================-->
        <if test="model.secondDimensionCodesPowers != null and model.secondDimensionCodesPowers.size()>0">
            and (  vid.second_dimension_code in <foreach item="item" collection="model.secondDimensionCodesPowers" separator="," open="(" close=")" index="">
                #{item}
            </foreach> or vid.second_dimension_code is null)
        </if>
        <if test="model.tagTopicCodesDisables != null and model.tagTopicCodesDisables.size()>0">
            and vid.topic_code not in <foreach item="item" collection="model.tagTopicCodesDisables" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.qualityTopicCodesDisables != null and model.qualityTopicCodesDisables.size()>0">
            and vid.topic_code not in <foreach item="item" collection="model.qualityTopicCodesDisables" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.otherTag != null and model.otherTag.size()>0">
            and vid.topic_code not in <foreach item="item" collection="model.otherTag" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="wo-car-series">
        <if test="model.carSeriesNames != null and model.carSeriesNames.size()>0">
            and (vid.car_series in <foreach item="item" collection="model.carSeriesNames" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            <if test="model.brandCode == 'A11' and (model.carSeries == null or model.carSeries.size() == 0 or model.carSeries.contains('A11A00'))">
                or vid.car_series is null
            </if>
            )
        </if>
        <if test="model.brandNames != null and model.brandNames.size()>0">
            and vid.brand in <foreach item="item" collection="model.brandNames" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="wo-province">
        <if test="model.provinceNames != null and model.provinceNames.size()>0">
            and vid.customer_province in
            <foreach item="item" collection="model.provinceNames" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="wo-dlr">
        <!--   网点     -->
        <if test="model.dlrName != null and model.dlrName != ''">
            and vid.dlr_name = #{model.dlrName}
        </if>
        <if test="model.dlrCode != null and model.dlrCode != ''">
            and vid.dlr_code = #{model.dlrCode}
        </if>
    </sql>
    <sql id="wo_type">
        <if test="model.woType !=null and model.woType !=''" >
            AND vid.WO_TYPE = #{model.woType}
        </if>
    </sql>
    <sql id="wo_type_list">
        <if test="model.woTypeList !=null and model.woTypeList.size()>0" >
            AND vid.WO_TYPE  in
            <foreach item="item" collection="model.woTypeList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


    </sql>
    <sql id="wo_flow">
        <if test="model.woFlow !=null and model.woFlow !=''" >
            AND vid.need_flow = #{model.woFlow}
        </if>
    </sql>
    <sql id="wo_customer_type">
        <if test="model.userTypes != null and model.userTypes.size()>0">
            and (vid.customer_type in
            <foreach item="item" collection="model.userTypes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            <if test="model.userTypes.contains('未知')">
                or vid.customer_type is null
            </if>
            )
        </if>
    </sql>
    <sql id="wo_user_type">
        <if test="model.userTypes != null and model.userTypes.size()>0">
            and (vid.user_type in
            <foreach item="item" collection="model.userTypes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            <if test="model.userTypes.contains('未知')">
                or vid.user_type is null
            </if>
            )
        </if>
    </sql>
    <sql id="wo_status">
        <if test="model.status !=null and model.status !=''">
            <if test="model.status == 'closed'">
                AND vid.status <![CDATA[ >= ]]> 3
            </if>
            <if test="model.status == 'notClosed'">
                AND vid.status <![CDATA[ < ]]> 3
            </if>
        </if>
    </sql>
    <sql id="wo_satisfaction_status">
        <if test="model.satisfactionStatus !=null and model.satisfactionStatus !=''">
            <if test="model.satisfactionStatus == 'yes'">
                AND vid.customer_satisfaction = '满意'
            </if>
            <if test="model.satisfactionStatus == 'no'">
                AND vid.customer_satisfaction = '不满意'
            </if>
        </if>
    </sql>
    <sql id="wo_rescue_status">
        <if test="model.rescueStatus !=null and model.rescueStatus !=''">
            <if test="model.rescueStatus == 'yes'">
                AND vid.is_rescue = '是'
            </if>
            <if test="model.rescueStatus == 'no'">
                AND (vid.is_rescue IS NULL OR vid.is_rescue = '' OR vid.is_rescue = 'null' OR vid.is_rescue = '否')
            </if>
        </if>
    </sql>
    <sql id="wo_data_sources">
        <if test="model.woDataSource != null and model.woDataSource !=''">
            and vid.channel_source_2 = #{model.woDataSource}
        </if>
        <if test="model.woDataSources != null and model.woDataSources.size()>0">
            and (vid.channel_source_3 in
            <foreach item="item" collection="model.woDataSources" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            )
        </if>
    </sql>
    <sql id="wo_time">
        <if test="model.woTime != null and model.woTime !='' and model.woTime == 'LAST_24_HOURS'">
            and TIMESTAMPDIFF(HOUR, vid.create_time, CASE WHEN vid.status <![CDATA[ >= ]]> 3 THEN vid.close_time ELSE NOW() END) <![CDATA[ <= ]]> 24
        </if>
        <if test="model.woTime != null and model.woTime !='' and model.woTime == 'LAST_48_HOURS'">
            and TIMESTAMPDIFF(HOUR, vid.create_time, CASE WHEN vid.status <![CDATA[ >= ]]> 3 THEN vid.close_time ELSE NOW() END) <![CDATA[ <= ]]> 48
        </if>
        <if test="model.woTime != null and model.woTime !='' and model.woTime == 'LAST_72_HOURS'">
            and TIMESTAMPDIFF(HOUR, vid.create_time, CASE WHEN vid.status <![CDATA[ >= ]]> 3 THEN vid.close_time ELSE NOW() END) <![CDATA[ <= ]]> 72
        </if>
        <if test="model.woTime != null and model.woTime !='' and model.woTime == 'LAST_96_HOURS'">
            and TIMESTAMPDIFF(HOUR, vid.create_time, CASE WHEN vid.status <![CDATA[ >= ]]> 3 THEN vid.close_time ELSE NOW() END) <![CDATA[ <= ]]> 96
        </if>
        <if test="model.woTime != null and model.woTime !='' and model.woTime == 'LAST_120_HOURS'">
            and TIMESTAMPDIFF(HOUR, vid.create_time, CASE WHEN vid.status <![CDATA[ >= ]]> 3 THEN vid.close_time ELSE NOW() END) <![CDATA[ <= ]]> 120
        </if>
        <if test="model.woTime != null and model.woTime !='' and model.woTime == 'OVER_120_HOURS'">
            and TIMESTAMPDIFF(HOUR, vid.create_time, CASE WHEN vid.status <![CDATA[ >= ]]> 3 THEN vid.close_time ELSE NOW() END) <![CDATA[ > ]]> 120
        </if>
    </sql>


</mapper>
