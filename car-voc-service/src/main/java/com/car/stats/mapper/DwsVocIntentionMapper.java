package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.DwsVocIntention;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.FocucLabelDetailModel;
import com.car.stats.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName AdsVocFirstIntentionDiMapper.java
 * @Description TODO
 * @createTime 2022年10月13日 15:37
 * @Copyright voc
 */
public interface DwsVocIntentionMapper extends BaseMapper<DwsVocIntention> {


    List<IntentionTrendVo> mentionGroupByDate(@Param("model") FilterCriteriaModel model);
    List<DateTrendEmotionVo> emotionGroupByDate(@Param("model") FilterCriteriaModel model);


    IntentionRatioVo overviewIntentionRatio(@Param("model") FilterCriteriaModel model);

    List<ProportionCarSeriesVo> overviewVehicleShare(@Param("model") FilterCriteriaModel model);

    List<CarGroupIntentionsProportionVo> carGroupIntentionsProportion(@Param("model") FilterCriteriaModel model);

    List<CarGroupEmotionProportionVo> carGroupEmotionProportion(@Param("model") FilterCriteriaModel model);

    List<CarTypeProportionVo> overviewCarTypeRato(@Param("model") FilterCriteriaModel model);

    TrendEmotionVo overviewEmotionalProportion(@Param("model") FilterCriteriaModel model);

    List<HighHotWordsVo> overviewHighFrequencyWords(@Param("model") FilterCriteriaModel model);

    List<UserTypeProportionVo> overviewProportionUserTypes(@Param("model") FilterCriteriaModel model);

    List<LabelEmotionTopVo> overviewFocusTop(@Param("model") FilterCriteriaModel model);

    List<EmotionProportionVo> overviewFocusProportionEmotion(@Param("model")  FocucLabelDetailModel model);

    DateTrendEmotionVo overviewEmotionalTrends(@Param("model")  FocucLabelDetailModel model);

    List<LabelVo> threeLabelsTop(@Param("model") FocucLabelDetailModel model);

    List<LabelVo> fourLabelsTop(@Param("model") FocucLabelDetailModel model);

    List<List<ProportionCarSeriesVo>> dwsVocIntentionMapperList(@Param("model") FilterCriteriaModel model);

    List<CarGroupEmotionProportionVo> energyClassification(@Param("model") FilterCriteriaModel model);

    List<CarGroupIntentionsProportionVo> energyClassificationIntentions(@Param("model") FilterCriteriaModel model);

    List<CarEmotionVo> carSeriesDistributionEmotion(@Param("model") FilterCriteriaModel model);

    List<CarIntentionVo> carSeriesDistributionIntention(@Param("model") FilterCriteriaModel model);
}
