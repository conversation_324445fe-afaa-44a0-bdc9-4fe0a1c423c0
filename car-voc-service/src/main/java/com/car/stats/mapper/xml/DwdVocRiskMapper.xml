<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwdVocRiskMapper">

    <sql id="after_rule_table_keyword">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        qf.ID IN
        (
        SELECT
        id
        FROM
        TF_DWD_VOC_EMOTION_RISK q
        WHERE
        1=1
        and q.STATISTIC_TYPE='d'
        and q.NEGATIVE_NUM>=#{rule.negativeNumD}
        and q.COMPLAIN_NUM>=#{rule.complaintNumD}

        <if test="rule.riskWordsNumD !=null   ">
        AND
        (
        q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_EMOTION_RISK_INFO vid WHERE vid.STATISTIC_TYPE='d' AND
        vid.KEYWORD_NUM>=#{rule.riskWordsNumD}
        <include refid="ComFilterMapper.this_date"/>)
        )
        </if>

        )
        OR
        qf.ID in
        (
        SELECT
        id
        FROM
        TF_DWD_VOC_EMOTION_RISK q
        WHERE
        1=1
        and q.STATISTIC_TYPE='w'
        and q.NEGATIVE_NUM>=#{rule.negativeNumW}
        and q.COMPLAIN_NUM>=#{rule.complaintNumW}
        <if test="rule.riskWordsNumW !=null  ">

        AND
        (
        q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_EMOTION_RISK_INFO vid WHERE vid.STATISTIC_TYPE='w' AND
        vid.KEYWORD_NUM>=#{rule.riskWordsNumW}
        <include refid="ComFilterMapper.this_date"/>)
        )
        </if>

        )
        OR
        qf.ID in
        (
        SELECT
        id
        FROM
        TF_DWD_VOC_EMOTION_RISK q
        WHERE
        1=1
        and q.STATISTIC_TYPE='m'
        and q.NEGATIVE_NUM>=#{rule.negativeNumM}
        and q.COMPLAIN_NUM>=#{rule.complaintNumM}
        <if test="rule.riskWordsNumM !=null   ">

        AND
        (
        q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_EMOTION_RISK_INFO vid WHERE vid.STATISTIC_TYPE='m' AND
        vid.KEYWORD_NUM>=#{rule.riskWordsNumM}
        <include refid="ComFilterMapper.this_date"/>
        )
        )
        </if>

        )
        OR
        qf.ID in
        (
        SELECT
        id
        FROM
        TF_DWD_VOC_EMOTION_RISK q
        WHERE
        1=1
        and q.STATISTIC_TYPE='q'
        and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
        and q.COMPLAIN_NUM>=#{rule.complaintNumQ}
        <if test="rule.riskWordsNumQ !=null   ">

        AND
        (
        q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_EMOTION_RISK_INFO vid WHERE vid.STATISTIC_TYPE=
        'q' AND vid.KEYWORD_NUM>=#{rule.riskWordsNumQ}
        <include refid="ComFilterMapper.this_date"/>  )
        )
        </if>

        )

        )
    </sql>


    <sql id="after_rule_table_bak1">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1=1
            and
              (
            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.NEGATIVE_NUM>=#{rule.negativeNumD}
            and q.COMPLAIN_NUM>=#{rule.complaintNumD}
            )
            )
        )
    </sql>

    <sql id="after_rule_table_riskEventFiltering">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1=1
            and
              (
            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.NEGATIVE_NUM>=#{rule.negativeNumD}
            and q.COMPLAIN_NUM>=#{rule.complaintNumD}
            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.NEGATIVE_NUM>=#{rule.negativeNumW}
            and q.COMPLAIN_NUM>=#{rule.complaintNumW}
            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.NEGATIVE_NUM>=#{rule.negativeNumM}
            and q.COMPLAIN_NUM>=#{rule.complaintNumM}

            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='q'
            and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
            and q.COMPLAIN_NUM>=#{rule.complaintNumQ}

            )

                  )
        )
    </sql>
    <!--带风险关键字的-->
    <sql id="after_rule_table_riskEventFiltering_keyword">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1=1
          and qf.risk_type=1
          and qf.PUBLISH_DATE>= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
          and
              (
            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
        <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
        LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
        </if>

            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.NEGATIVE_NUM>=#{rule.negativeNumD}
            and q.COMPLAIN_NUM>=#{rule.complaintNumD}
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
            GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumD}
            </if>

        )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
        <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
        LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
        </if>

        WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.NEGATIVE_NUM>=#{rule.negativeNumW}
            and q.COMPLAIN_NUM>=#{rule.complaintNumW}
        <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >

        GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumW}
        </if>

        )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
        <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >

        LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
        </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.NEGATIVE_NUM>=#{rule.negativeNumM}
            and q.COMPLAIN_NUM>=#{rule.complaintNumM}
        <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >

        GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumM}
        </if>

            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
        <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >

        LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
        </if>

            WHERE
            1=1
            and q.STATISTIC_TYPE='q'
            and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
            and q.COMPLAIN_NUM>=#{rule.complaintNumQ}
        <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >

        GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumQ}
        </if>

            )
            or
            qf.ID in
            (
                SELECT
                    id
                FROM
                    TF_DWD_VOC_EMOTION_RISK q
        <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >

        LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
        </if>

            WHERE
                    1=1
                  and q.STATISTIC_TYPE='y'
                  and q.NEGATIVE_NUM>=#{rule.negativeNumY}
                  and q.COMPLAIN_NUM>=#{rule.complaintNumY}
        <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
             GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumY}
        </if>


        )

                  )
        )
    </sql>


    <!--不带关键字的-->
<sql id="after_rule_table_bak">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1=1
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            and
            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.NEGATIVE_NUM>=#{rule.negativeNumD}
            and q.COMPLAIN_NUM>=#{rule.complaintNumD}
            )
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
            and
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.NEGATIVE_NUM>=#{rule.negativeNumW}
            and q.COMPLAIN_NUM>=#{rule.complaintNumW}
            )
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            and
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.NEGATIVE_NUM>=#{rule.negativeNumM}
            and q.COMPLAIN_NUM>=#{rule.complaintNumM}

            )
           </if>
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
            and
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='q'
            and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
            and q.COMPLAIN_NUM>=#{rule.complaintNumQ}

            )
            </if>
            <if test="model.dateUnit !=null and model.dateUnit ==3 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_EMOTION_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE='y'
                and q.NEGATIVE_NUM>=#{rule.negativeNumY}
                and q.COMPLAIN_NUM>=#{rule.complaintNumY}
                )
            </if>


        )
    </sql>


    <!--带关键字的-->
<sql id="after_rule_table_bak_keyword">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1=1
        and qf.risk_type=1
        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            and
            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
            LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>

            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.NEGATIVE_NUM>=#{rule.negativeNumD}
            and q.COMPLAIN_NUM>=#{rule.complaintNumD}
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
            GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumD}
            </if>
            )
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >
            and
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
            LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.NEGATIVE_NUM>=#{rule.negativeNumW}
            and q.COMPLAIN_NUM>=#{rule.complaintNumW}
            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
            GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumW}
            </if>

            )
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >
            and
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
            LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.NEGATIVE_NUM>=#{rule.negativeNumM}
            and q.COMPLAIN_NUM>=#{rule.complaintNumM}
            <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
            GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumM}
            </if>
            )
           </if>
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >
            and
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
            LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='q'
            and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
            and q.COMPLAIN_NUM>=#{rule.complaintNumQ}
            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
            GROUP BY rk.risk_id
            HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumQ}
            </if>

            )
            </if>
            <if test="model.dateUnit !=null and model.dateUnit ==3 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_EMOTION_RISK q
                <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
                </if>
                WHERE
                1=1
                and q.STATISTIC_TYPE='y'
                and q.NEGATIVE_NUM>=#{rule.negativeNumY}
                and q.COMPLAIN_NUM>=#{rule.complaintNumY}
                <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumY}
                </if>
                )
            </if>


        )
    </sql>




    <!--带关键字的-->
    <sql id="after_rule_table_bak_keyword_RiskExport">
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1=1
        and qf.risk_type=1
        and
          (


            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>

            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.NEGATIVE_NUM>=#{rule.negativeNumD}
            and q.COMPLAIN_NUM>=#{rule.complaintNumD}
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumD}
            </if>
            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.NEGATIVE_NUM>=#{rule.negativeNumW}
            and q.COMPLAIN_NUM>=#{rule.complaintNumW}
            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumW}
            </if>

            )

            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.NEGATIVE_NUM>=#{rule.negativeNumM}
            and q.COMPLAIN_NUM>=#{rule.complaintNumM}
            <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumM}
            </if>
            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='q'
            and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
            and q.COMPLAIN_NUM>=#{rule.complaintNumQ}
            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumQ}
            </if>

            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_EMOTION_RISK q
            <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id=rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='y'
            and q.NEGATIVE_NUM>=#{rule.negativeNumY}
            and q.COMPLAIN_NUM>=#{rule.complaintNumY}
            <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                GROUP BY rk.risk_id
                HAVING SUM(rk.keyword_num)>=#{rule.riskWordsNumY}
            </if>
            )


        )



        )
    </sql>






    <sql id="riskListCom1">
        select *
        from (
        select
        vid.*,
        @rownum := @rownum+1 ,
        if(@pdept=vid.TOPIC_CODE,@rank:=@rank+1,@rank:=1) as rank,
        @pdept:=vid.TOPIC_CODE
        from (select * from <include refid="after_rule_table_bak"/> vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
                            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                                and vid.TOPIC_CODE in
                                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                                    #{item}
                                </foreach>
                            </if>) vid
                            ,
        (select @rownum :=0 , @pdept := null ,@rank:=0) c
        order by vid.NEGATIVE_NUM DESC
        ) result
        where rank = 1
    </sql>

    <sql id="riskListCom">
        (SELECT vid.*
        FROM
        <include refid="after_rule_table_bak_keyword" /> vid

        WHERE
        1 = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc
        )
    </sql>

    <sql id="riskListCom_RiskExport">
        (SELECT vid.*
        FROM
        <include refid="after_rule_table_bak_keyword_RiskExport" /> vid

        WHERE
        1 = 1
        and
        vid .statistic_type !='d'

        ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc
        )
    </sql>


    <sql id="riskListCom_old">
        (SELECT T.*
        FROM (SELECT vid.*,
        ROW_NUMBER() OVER(PARTITION BY vid.TOPIC_CODE ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc) RW
        FROM
        <include refid="after_rule_table_bak_keyword" /> vid

        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc
        ) T
        WHERE T.RW = 1)
    </sql>

    <select id="riskPointAggNew"  resultType="com.car.stats.vo.risk.RiskPointAggVo">

        SELECT
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.RISK_INDEX AS riskIndex,
        risk.TOTAL_NUM AS totalNum,
        risk.PUBLISH_DATE AS publishDate,
        risk.STATISTIC_TYPE AS statisticType,
        risk.NEGATIVE_NUM AS negativeNum,
        risk.brand_code AS brandCode,
        risk.risk_level as riskLevel,
        risk.create_time as createTime
        FROM
        (
        SELECT vid.*, vrwrd.risk_level
        FROM TF_DWD_VOC_EMOTION_RISK vid
            join (select distinct q.id as id
                from (
                    SELECT q.*, ifnull(sum(rk.keyword_num), 0) as risk_words_num
                    FROM TF_DWD_VOC_EMOTION_RISK q
                    LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id = rk.risk_id
                    where q.risk_type = 1
                        <if test="model.brandCode !=null and model.brandCode !=''" >
                            and q.brand_code = #{model.brandCode}
                        </if>
                        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
                            and q.publish_date>=#{model.startDate}
                            AND #{model.endDate}>=q.publish_date
                        </if>
                        <if test="model.topicCodes != null and model.topicCodes.size()>0">
                            and q.TOPIC_CODE in
                            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                        </if>
                    group by q.id
                ) q
                left join voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
                left join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
                WHERE 1 = 1
                    and q.risk_type = 1
                    and vrwr.risk_type = '风险事件洞察'
                    and q.STATISTIC_TYPE = vrwrd.insight_cycle
                    AND q.NEGATIVE_NUM >= vrwrd.negative_num
                    AND q.COMPLAIN_NUM >= vrwrd.complaint_num
                    AND q.risk_words_num >= vrwrd.risk_words_num
                    <if test="model.brandCode !=null and model.brandCode !=''" >
                        and q.brand_code = #{model.brandCode}
                    </if>
                    <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
                        and q.SECOND_DIMENSION_CODE in
                        <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                            #{item}
                        </foreach>
                    </if>
                    <if test="model.dateUnit !=null and model.dateUnit == -1 " >
                        and vrwrd.insight_cycle = 'd'
                        AND q.STATISTIC_TYPE = 'd'
                    </if>
                    <if test="model.dateUnit !=null and model.dateUnit == 0 " >
                        and vrwrd.insight_cycle = 'w'
                        AND q.STATISTIC_TYPE = 'w'
                    </if>
                    <if test="model.dateUnit !=null and model.dateUnit == 1 " >
                        and vrwrd.insight_cycle = 'm'
                        AND q.STATISTIC_TYPE = 'm'
                    </if>
                    <if test="model.dateUnit !=null and model.dateUnit == 2 " >
                        and vrwrd.insight_cycle = 'q'
                        AND q.STATISTIC_TYPE = 'q'
                    </if>
                    <if test="model.dateUnit !=null and model.dateUnit == 3 " >
                        and vrwrd.insight_cycle = 'y'
                        AND q.STATISTIC_TYPE = 'y'
                    </if>
        ) filtered_ids on vid.id = filtered_ids.id
        left join voc_risk_warning_rules vrwr on vid.brand_code = vrwr.brand_code
        left join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id

        where 1 = 1
        and vrwr.risk_type = '风险事件洞察'
        and vrwrd.type = '2'
        AND cast(vid.risk_index  as signed) >= cast(vrwrd.risk_level_min  as signed) AND cast(vrwrd.risk_level_max as signed) > cast(vid.risk_index  as signed)
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
            and vid.publish_date>=#{model.startDate}
            AND #{model.endDate}>=vid.publish_date
        </if>

        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by vid.RISK_INDEX desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by vid.NEGATIVE_NUM desc
        </if>
        ) risk
        WHERE
        1 = 1
        <if test="model.tag !=null and model.tag !=''" >
            and risk.TOPIC_CODE like  CONCAT('%', #{model.tag} ,'%')
        </if>
        <if test="model.riskLevel !=null and model.riskLevel !=''" >
            and risk.risk_level = #{model.riskLevel}
        </if>
        group by risk.TOPIC_CODE
        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by risk.RISK_INDEX desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by risk.NEGATIVE_NUM desc
        </if>
    </select>


<!--    <select id="riskPointAggNew"  resultType="com.car.stats.vo.risk.RiskPointAggVo">-->
<!--        SELECT-->
<!--        distinct-->
<!--        risk.ID riskId,-->
<!--        risk.TOPIC_CODE topicCode,-->
<!--        risk.RISK_INDEX as riskIndex,-->
<!--        risk.TOTAL_NUM as totalNum,-->
<!--        risk.PUBLISH_DATE as publishDate,-->
<!--        risk.STATISTIC_TYPE as statisticType,-->
<!--        risk.NEGATIVE_NUM as negativeNum,-->
<!--        risk.brand_code as brandCode-->
<!--        FROM-->
<!--        (-->
<!--        <include refid="riskListCom" />-->
<!--        ) risk-->

<!--        where-->
<!--        1=1-->
<!--        <if test="model.orderByType !=null and model.orderByType =='index' " >-->
<!--            order by risk.RISK_INDEX desc-->
<!--        </if>-->
<!--        <if test="model.orderByType !=null and model.orderByType =='num' " >-->
<!--            order by risk.NEGATIVE_NUM desc-->
<!--        </if>-->


<!--    </select>-->
 <select id="riskExport"  resultType="com.car.stats.vo.risk.RiskExportResultVo">
        SELECT
         CASE
         WHEN risk.risk_index >= 90 THEN "S"
         WHEN (risk.risk_index > 70 AND 90 > risk.risk_index) THEN "A"
         WHEN (risk.risk_index > 50 AND 70 >= risk.risk_index) THEN  "B"
         WHEN (risk.risk_index > 30 AND 50 >= risk.risk_index) THEN "C"
         WHEN (risk.risk_index >= 0 AND 30 >= risk.risk_index) THEN "D" end riskLevel,
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.RISK_INDEX as riskIndex,
        risk.TOTAL_NUM as totalNum,
        risk.PUBLISH_DATE as publishDate,
        risk.STATISTIC_TYPE as statisticType,
        risk.date_year as dateYear,
        risk.date_month as dateMonth,
        risk.date_week as dateWeek,
        risk.NEGATIVE_NUM as negativeNum
        FROM
        (
        <include refid="riskListCom_RiskExport" />
        ) risk

        where
        1=1
        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by risk.RISK_INDEX desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by risk.NEGATIVE_NUM desc
        </if>


    </select>

    <select id="riskPointAggNew1"  resultType="com.car.stats.vo.risk.RiskPointAggVo">
        SELECT
        distinct
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.RISK_INDEX as riskIndex,
        risk.TOTAL_NUM as totalNum,
        risk.PUBLISH_DATE as publishDate,
        risk.STATISTIC_TYPE as statisticType,
        risk.NEGATIVE_NUM as negativeNum
        FROM
        (
        SELECT T.*
        FROM (SELECT vid.*,
        ROW_NUMBER() OVER(PARTITION BY vid.TOPIC_CODE ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc) RW
        FROM TF_DWD_VOC_EMOTION_RISK vid
        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc
        ) T
        WHERE T.RW = 1
        ) risk

        where
        1=1
        ORDER BY risk.NEGATIVE_NUM desc,risk.RISK_INDEX desc


    </select>

    <select id="riskPointAggNew_voc"  resultType="com.car.stats.vo.risk.RiskPointAggVo">
        SELECT
        distinct
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.RISK_LEVEL grade,
        risk.RISK_INDEX as riskIndex,
        risk.NEGATIVE_NUM as negativeNum
        FROM
        (
        SELECT T.*
        FROM (SELECT vid.*,
        ROW_NUMBER() OVER(PARTITION BY vid.TOPIC_CODE ORDER BY vid.NEGATIVE_NUM desc) RW
        FROM TF_DWD_VOC_RISK vid
        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY vid.NEGATIVE_NUM desc,vid.RISK_INDEX desc
        ) T
        WHERE T.RW = 1
        ) risk

        where
        1=1
        ORDER BY risk.NEGATIVE_NUM desc,risk.RISK_INDEX desc


    </select>

    <select id="getRiskEvents"  resultType="java.math.BigDecimal">
        SELECT
        count( DISTINCT risk.TOPIC_CODE )
        FROM
        (
        (
        SELECT
        vid.*
        FROM
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_EMOTION_RISK qf
        WHERE
        1 = 1
        AND qf.risk_type = 1
        AND qf.ID IN (
        SELECT
        q.id
        FROM
        TF_DWD_VOC_EMOTION_RISK q
        left join
        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and vrwr.risk_type = '风险事件洞察'
        and vrwrd.type = '1'
        <if test="model.dateUnit !=null and model.dateUnit == -1 " >
            and vrwrd.insight_cycle = 'd'
            AND q.STATISTIC_TYPE = 'd'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 0 " >
            and vrwrd.insight_cycle = 'w'
            AND q.STATISTIC_TYPE = 'w'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 1 " >
            and vrwrd.insight_cycle = 'm'
            AND q.STATISTIC_TYPE = 'm'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 2 " >
            and vrwrd.insight_cycle = 'q'
            AND q.STATISTIC_TYPE = 'q'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 3 " >
            and vrwrd.insight_cycle = 'y'
            AND q.STATISTIC_TYPE = 'y'
        </if>
        AND q.NEGATIVE_NUM >= vrwrd.negative_num
        AND q.COMPLAIN_NUM >= vrwrd.complaint_num
        )
        ) vid
        WHERE
        1 = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


        ORDER BY
        vid.NEGATIVE_NUM DESC,
        vid.RISK_INDEX DESC
        )
        ) risk
    </select>

<!--    <select id="getRiskEvents"  resultType="java.math.BigDecimal">-->
<!--        SELECT-->
<!--        count(distinct risk.TOPIC_CODE)-->
<!--        FROM-->
<!--        (-->
<!--        <include refid="riskListCom" />-->
<!--        ) risk-->
<!--    </select>-->
    <select id="getRiskEvents2"  resultType="java.math.BigDecimal">
        select count(distinct vid.TOPIC_CODE)
        FROM TF_DWD_VOC_EMOTION_RISK vid
        where
        1=1
        <include refid="ComFilterMapper.this_risk_date" />
        <include refid="ComFilterMapper.this_risk_date_year" />
        <include refid="ComFilterMapper.this_statisticType" />
        <!-- <if test="model.startDate !=null and model.startDate != ''and
                   model.endDate !=null and model.endDate != ''">
             and vid.PUBLISH_DATE>=#{model.startDate}
             AND #{model.endDate}>=vid.PUBLISH_DATE
         </if>-->
    </select>

    <select id="riskEventFiltering1"  resultType="com.car.stats.entity.risk.DwdVocEmotionRisk">
        SELECT
            er.*
        FROM
            TF_DWD_VOC_EMOTION_RISK er
        WHERE
            1 = 1
          AND er.NEGATIVE_NUM >= 100
          AND er.COMPLAIN_NUM >= 100
        ORDER BY
            er.PUBLISH_DATE DESC
    </select>


    <select id="riskEventFiltering_old"  resultType="com.car.stats.entity.risk.DwdVocEmotionRisk">

        SELECT
            er.*
        FROM
        TF_DWD_VOC_EMOTION_RISK er
        WHERE
            1=1
          and er.RISK_INDEX >=#{model.pushCondition}
          and er.PUBLISH_DATE>='2023-01-01 00:00:00'
        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and er.create_time>#{model.lastWarningTime}
        </if>
        order by er.create_time desc
    </select>


    <select id="riskEventFilteringNew"  resultType="com.car.stats.entity.risk.DwdVocEmotionRisk">
        SELECT
        er.*
        FROM
        (
            SELECT
            *
            FROM
            TF_DWD_VOC_EMOTION_RISK qf
            WHERE
            1 = 1
            AND qf.risk_type = 1
<!--
            AND qf.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
-->
            AND (
                qf.ID IN (
                            select
                            qa.id
                            from(
                                select
                                q.id as id,
                                q.risk_words_num
                                from
                                (
                                SELECT q.*, ifnull(sum(rk.keyword_num),0) as risk_words_num
                                FROM TF_DWD_VOC_EMOTION_RISK q
                                LEFT JOIN tf_dwd_voc_emotion_risk_info rk ON q.id = rk.risk_id
                                where q.risk_type =1
<!--
                                      AND q.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
-->
                                    <if test="model.brandCode !=null and model.brandCode !=''" >
                                        and q.brand_code = #{model.brandCode}
                                    </if>
                                    <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                                        and q.create_time>#{model.lastWarningTime}
                                    </if>
                                group by q.id
                                ) q
                                left join
                                voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
                                left join
                                voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
                                WHERE
                                1 = 1
                                and q.risk_type =1
                                and vrwr.risk_type = '风险事件洞察'
                                and q.STATISTIC_TYPE = vrwrd.insight_cycle
                                AND q.NEGATIVE_NUM >= vrwrd.negative_num
                                AND q.COMPLAIN_NUM >= vrwrd.complaint_num
                                AND q.risk_words_num >=  vrwrd.risk_words_num
                            ) as qa
                )
            )
        ) er
        left join
        voc_risk_warning_rules vrwr on er.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE 1 = 1
        and vrwrd.type = 2
        and er.risk_index >= vrwrd.risk_level_min
        and vrwrd.risk_level_max > er.risk_index
        and vrwr.risk_type = '风险事件洞察'
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and er.brand_code = #{model.brandCode}
        </if>
        <if test="model.pushConditionList != null and model.pushConditionList.size() >0">
            and vrwrd.risk_level in
            <foreach item="item" collection="model.pushConditionList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and er.create_time>#{model.lastWarningTime}
        </if>
        ORDER BY
        er.risk_index DESC,
        er.statistic_type DESC
    </select>


    <select id="riskEventFiltering"  resultType="com.car.stats.entity.risk.DwdVocEmotionRisk">

        SELECT
        er.*
        FROM
        <include refid="after_rule_table_riskEventFiltering_keyword" /> er
        WHERE
        1=1
        and er.RISK_INDEX >=#{model.pushCondition}
        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and er.create_time>#{model.lastWarningTime}
        </if>
        order by er.risk_index DESC,er.statistic_type DESC
    </select>

    <select id="dataAnalysisWaringNum" resultType="string">

        SELECT
            to_char(risk.PUBLISH_DATE,'YYYY-MM-DD') as  dateStr

        FROM
            TF_DWD_VOC_EMOTION_RISK risk
            LEFT JOIN VOC_RISK_WARNING_RULES_DETAILED rd ON risk.STATISTIC_TYPE=rd.INSIGHT_CYCLE
        where
            1=1
          and risk.NEGATIVE_NUM>=rd.NEGATIVE_NUM
          AND risk.COMPLAIN_NUM>=rd.COMPLAINT_NUM
          and risk.STATISTIC_TYPE=#{risk.statisticType}
          and rd.INSIGHT_CYCLE=#{risk.statisticType}
          and rd.WARN_RULE_ID='lsdfldskflskfsd34242425'
          and risk.TOPIC_CODE =#{risk.topicCode}
          and risk.PUBLISH_DATE>='2023-01-01 00:00:00'

        group by to_char(risk.PUBLISH_DATE,'YYYY-MM-DD')

        order by dateStr desc


    </select>

    <select id="riskKeywords" resultType="string">
        select tab.KEYWORD
        from (
                 SELECT vid.KEYWORD
                 FROM TF_DWD_VOC_EMOTION_RISK_INFO vid
                 WHERE
                     1 = 1
                   and vid.RISK_ID =#{risk.id}
                 ORDER BY vid.KEYWORD_NUM desc
             ) tab
        where 1=1
            limit 10
    </select>



    <select id="dataAnalysisBriefingTrend"  resultType="com.car.stats.vo.risk.BriefingTrendVo">
        SELECT
        <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr,
        SUM(RISK_INDEX) as riskIndex
        FROM
        TF_DWD_VOC_EMOTION_RISK vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
        ORDER BY dateStr desc

    </select>



</mapper>
