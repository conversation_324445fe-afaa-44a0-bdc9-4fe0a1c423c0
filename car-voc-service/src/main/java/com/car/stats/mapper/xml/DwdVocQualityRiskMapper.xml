<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwdVocQualityRiskMapper">

        <sql id="after_rule_table">
            (
            SELECT
            *
            FROM
            TF_DWD_VOC_QUALITY_RISK qf
            WHERE


            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.TOTAL_NUM>=#{rule.statisticD}
            AND q.CHANNEL_NUM>=#{rule.channelNumD}
            <if test="rule.riskWordsNumD !=null   ">

            AND
            (
            q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_QUALITY_RISK_INFO vid WHERE vid.STATISTIC_TYPE='d' AND
            vid.KEYWORD_NUM>=#{rule.riskWordsNumD}
            <include refid="ComFilterMapper.this_date"/>)
            )
            </if>

            )
            OR
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.TOTAL_NUM>=#{rule.statisticW}
            AND q.CHANNEL_NUM>=#{rule.channelNumW}
            <if test="rule.riskWordsNumW !=null  ">

            AND
            (
            q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_QUALITY_RISK_INFO vid WHERE vid.STATISTIC_TYPE='w' AND
            vid.KEYWORD_NUM>=#{rule.riskWordsNumW}
            <include refid="ComFilterMapper.this_date"/>)
            )
            </if>
            )
            OR
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.TOTAL_NUM>=#{rule.statisticM}
            AND q.CHANNEL_NUM>=#{rule.channelNumM}
            <if test="rule.riskWordsNumM !=null   ">
            AND
            (
            q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_QUALITY_RISK_INFO vid WHERE vid.STATISTIC_TYPE='m' AND
            vid.KEYWORD_NUM>=#{rule.riskWordsNumM}
            <include refid="ComFilterMapper.this_date"/>
            )
            )
            </if>


            )
            OR
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            WHERE
            1=1
            and q.STATISTIC_TYPE=
            'q'
            and q.TOTAL_NUM>=#{rule.statisticQ}
            AND q.CHANNEL_NUM>=#{rule.channelNumQ}

            <if test="rule.riskWordsNumQ !=null   ">
                AND
                (
                q.ID IN (SELECT DISTINCT vid.risk_id FROM TF_DWD_VOC_QUALITY_RISK_INFO vid WHERE vid.STATISTIC_TYPE=
                'q' AND vid.KEYWORD_NUM>=#{rule.riskWordsNumQ}
                <include refid="ComFilterMapper.this_date"/>  )
                )
            </if>


            )




        )
        </sql>

        <sql id="after_rule_table_bak_2">
            (
            SELECT
            *
            FROM
            TF_DWD_VOC_QUALITY_RISK qf
            WHERE
            1=1
                and
                qf.ID IN
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE='d'
                and q.TOTAL_NUM>=#{rule.statisticD}
                AND q.CHANNEL_NUM>=#{rule.channelNumD}
                )


        )
        </sql>

        <sql id="after_rule_table_riskQualiytFiltering">
            (
                SELECT *
                FROM TF_DWD_VOC_QUALITY_RISK qf
                WHERE 1 = 1
                  and qf.risk_type=1
                  and qf.PUBLISH_DATE>= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                  and
                (
                      qf.ID IN
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE = 'd'
                            and q.TOTAL_NUM >= #{rule.statisticD}
                            AND q.CHANNEL_NUM >= #{rule.channelNumD}
                      )
                  or qf.ID in
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE = 'w'
                            and q.TOTAL_NUM >= #{rule.statisticW}
                            AND q.CHANNEL_NUM >= #{rule.channelNumW}
                      )
                  or qf.ID in
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE = 'm'
                            and q.TOTAL_NUM >= #{rule.statisticM}
                            AND q.CHANNEL_NUM >= #{rule.channelNumM}
                      )

                  or qf.ID in
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE ='q'
                            and q.TOTAL_NUM >= #{rule.statisticQ}
                            AND q.CHANNEL_NUM >= #{rule.channelNumQ}
                      )
            )

            )
        </sql>
    <!--预警带关键字的-->
    <sql id="after_rule_table_riskQualiytFiltering_keyword">
            (
                SELECT *
                FROM TF_DWD_VOC_QUALITY_RISK qf
                WHERE 1 = 1
                  and qf.PUBLISH_DATE>='2023-01-01 00:00:00'
                  and
                (
                      qf.ID IN
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                            </if>
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE = 'd'
                            and q.TOTAL_NUM >= #{rule.statisticD}
                            AND q.CHANNEL_NUM >= #{rule.channelNumD}
                            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                                GROUP BY
                                rk.risk_id
                                HAVING
                                SUM( rk.keyword_num ) >=#{rule.riskWordsNumD}
                            </if>
                      )
                  or qf.ID in
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                            </if>
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE = 'w'
                            and q.TOTAL_NUM >= #{rule.statisticW}
                            AND q.CHANNEL_NUM >= #{rule.channelNumW}
                            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                                GROUP BY
                                rk.risk_id
                                HAVING
                                SUM( rk.keyword_num ) >=#{rule.riskWordsNumW}
                            </if>
                      )
                  or qf.ID in
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                        <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                            LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                        </if>
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE = 'm'
                            and q.TOTAL_NUM >= #{rule.statisticM}
                            AND q.CHANNEL_NUM >= #{rule.channelNumM}
                        <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                            GROUP BY
                            rk.risk_id
                            HAVING
                            SUM( rk.keyword_num ) >=#{rule.riskWordsNumM}
                        </if>
                      )

                  or qf.ID in
                      (
                          SELECT id
                          FROM TF_DWD_VOC_QUALITY_RISK q
                            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                            </if>
                          WHERE 1 = 1
                            and q.STATISTIC_TYPE ='q'
                            and q.TOTAL_NUM >= #{rule.statisticQ}
                            AND q.CHANNEL_NUM >= #{rule.channelNumQ}
                            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                                GROUP BY
                                rk.risk_id
                                HAVING
                                SUM( rk.keyword_num ) >=#{rule.riskWordsNumQ}
                            </if>
                      )
                    or
                        qf.ID in
                        (
                        SELECT
                        id
                        FROM
                        TF_DWD_VOC_QUALITY_RISK q
                        <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                            LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                        </if>
                        WHERE
                        1=1
                        and q.STATISTIC_TYPE='y'
                        and q.TOTAL_NUM>=#{rule.statisticY}
                        AND q.CHANNEL_NUM>=#{rule.channelNumY}
                        <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                            GROUP BY
                            rk.risk_id
                            HAVING
                            SUM( rk.keyword_num ) >=#{rule.riskWordsNumY}
                        </if>
                        )
            )

            )
        </sql>
    <!--==========不带关键字-=======-->
 <sql id="after_rule_table_bak">
            (
            SELECT
            *
            FROM
            TF_DWD_VOC_QUALITY_RISK qf
            WHERE
            1=1
            <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
                and
                qf.ID IN
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE='d'
                and q.TOTAL_NUM>=#{rule.statisticD}
                AND q.CHANNEL_NUM>=#{rule.channelNumD}

                )
            </if>
            <if test="model.dateUnit !=null and model.dateUnit ==0 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE='w'
                and q.TOTAL_NUM>=#{rule.statisticW}
                AND q.CHANNEL_NUM>=#{rule.channelNumW}

                )
               </if>
            <if test="model.dateUnit !=null and model.dateUnit ==1 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE='m'
                and q.TOTAL_NUM>=#{rule.statisticM}
                AND q.CHANNEL_NUM>=#{rule.channelNumM}

                )
               </if>

            <if test="model.dateUnit !=null and model.dateUnit ==2 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE=
                'q'
                and q.TOTAL_NUM>=#{rule.statisticQ}
                AND q.CHANNEL_NUM>=#{rule.channelNumQ}
                )
                </if>

                 <if test="model.dateUnit !=null and model.dateUnit ==3 " >
                     and
                     qf.ID in
                     (
                     SELECT
                     id
                     FROM
                     TF_DWD_VOC_QUALITY_RISK q
                     WHERE
                     1=1
                     and q.STATISTIC_TYPE='y'
                     and q.TOTAL_NUM>=#{rule.statisticY}
                     AND q.CHANNEL_NUM>=#{rule.channelNumY}
                     )
                 </if>

        )
        </sql>
    <!--==========带关键字-=======-->
    <sql id="after_rule_table_bak_keyword">
            (
            SELECT
            *
            FROM
            TF_DWD_VOC_QUALITY_RISK qf
            WHERE
            1=1
              and qf.risk_type=1
            <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
                and
                qf.ID IN
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                </if>
                WHERE
                1=1
                and q.STATISTIC_TYPE='d'
                and q.TOTAL_NUM>=#{rule.statisticD}
                AND q.CHANNEL_NUM>=#{rule.channelNumD}
                <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumD}
                </if>

                )
            </if>
            <if test="model.dateUnit !=null and model.dateUnit ==0 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                </if>
                WHERE
                1=1
                and q.STATISTIC_TYPE='w'
                and q.TOTAL_NUM>=#{rule.statisticW}
                AND q.CHANNEL_NUM>=#{rule.channelNumW}
                <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumW}
                </if>
                )
               </if>
            <if test="model.dateUnit !=null and model.dateUnit ==1 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                </if>
                WHERE
                1=1
                and q.STATISTIC_TYPE='m'
                and q.TOTAL_NUM>=#{rule.statisticM}
                AND q.CHANNEL_NUM>=#{rule.channelNumM}
                <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumM}
                </if>
                )
               </if>

            <if test="model.dateUnit !=null and model.dateUnit ==2 " >
                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_QUALITY_RISK q
                <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                </if>
                WHERE
                1=1
                and q.STATISTIC_TYPE='q'
                and q.TOTAL_NUM>=#{rule.statisticQ}
                AND q.CHANNEL_NUM>=#{rule.channelNumQ}
                <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumQ}
                </if>
                )
                </if>

                 <if test="model.dateUnit !=null and model.dateUnit ==3 " >
                     and
                     qf.ID in
                     (
                     SELECT
                     id
                     FROM
                     TF_DWD_VOC_QUALITY_RISK q
                    <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                    LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                    </if>
                     WHERE
                     1=1
                     and q.STATISTIC_TYPE='y'
                     and q.TOTAL_NUM>=#{rule.statisticY}
                     AND q.CHANNEL_NUM>=#{rule.channelNumY}
                    <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                    GROUP BY
                     rk.risk_id
                     HAVING
                     SUM( rk.keyword_num ) >=#{rule.riskWordsNumY}
                    </if>
                     )
                 </if>

        )
        </sql>

    <!--==========带关键字-=======-->
    <sql id="after_rule_table_bak_keyword_RiskExport">
            (
            SELECT
            *
            FROM
            TF_DWD_VOC_QUALITY_RISK qf
            WHERE
            1=1
              and qf.risk_type=1
              and (


            qf.ID IN
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='d'
            and q.TOTAL_NUM>=#{rule.statisticD}
            AND q.CHANNEL_NUM>=#{rule.channelNumD}
            <if test="rule.riskWordsNumD !=null and rule.riskWordsNumD >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumD}
            </if>

            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='w'
            and q.TOTAL_NUM>=#{rule.statisticW}
            AND q.CHANNEL_NUM>=#{rule.channelNumW}
            <if test="rule.riskWordsNumW !=null and rule.riskWordsNumW >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumW}
            </if>
            )
            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='m'
            and q.TOTAL_NUM>=#{rule.statisticM}
            AND q.CHANNEL_NUM>=#{rule.channelNumM}
            <if test="rule.riskWordsNumM !=null and rule.riskWordsNumM >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumM}
            </if>
            )

            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='q'
            and q.TOTAL_NUM>=#{rule.statisticQ}
            AND q.CHANNEL_NUM>=#{rule.channelNumQ}
            <if test="rule.riskWordsNumQ !=null and rule.riskWordsNumQ >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumQ}
            </if>
            )

            or
            qf.ID in
            (
            SELECT
            id
            FROM
            TF_DWD_VOC_QUALITY_RISK q
            <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
            </if>
            WHERE
            1=1
            and q.STATISTIC_TYPE='y'
            and q.TOTAL_NUM>=#{rule.statisticY}
            AND q.CHANNEL_NUM>=#{rule.channelNumY}
            <if test="rule.riskWordsNumY !=null and rule.riskWordsNumY >0 " >
                GROUP BY
                rk.risk_id
                HAVING
                SUM( rk.keyword_num ) >=#{rule.riskWordsNumY}
            </if>
            )

                )

        )
        </sql>

    <sql id="riskListCom1">
        select *
        from (
        select
        vid.*,
        @rownum := @rownum+1 ,
        if(@pdept=vid.TOPIC_CODE,@rank:=@rank+1,@rank:=1) as rank,
        @pdept:=vid.TOPIC_CODE
        from (select * from <include refid="after_rule_table_bak"/> vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>) vid
        ,
        (select @rownum :=0 , @pdept := null ,@rank:=0) c
        order by vid.TOTAL_NUM DESC
        ) result
        where rank = 1
    </sql>

    <sql id="riskListCom">
        (SELECT vid.*
        FROM <include refid="after_rule_table_bak_keyword" /> vid
        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY vid.TOTAL_NUM DESC
        )

    </sql>

    <sql id="riskListCom_RiskExport">
        (SELECT vid.*
        FROM <include refid="after_rule_table_bak_keyword_RiskExport" /> vid
        WHERE
        1 = 1
        and
        vid .statistic_type !='d'
        and  vid .statistic_type ='m'

        ORDER BY vid.TOTAL_NUM DESC
        )

    </sql>


    <sql id="riskListCom_old">
        (
        SELECT T.*
        FROM (SELECT vid.*,
        ROW_NUMBER() OVER(PARTITION BY vid.TOPIC_CODE ORDER BY vid.TOTAL_NUM DESC) RW
        FROM <include refid="after_rule_table_bak_keyword" /> vid
        WHERE
        1 = 1
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        ORDER BY vid.TOTAL_NUM DESC
        ) T
        WHERE T.RW = 1
        )

    </sql>

    <select id="riskPointAggNew"  resultType="com.car.stats.vo.risk.RiskPointAggVo">

        SELECT
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.PUBLISH_DATE AS publishDate,
        risk.STATISTIC_TYPE AS statisticType,
        risk.RISK_INDEX AS riskIndex,
        risk.TOTAL_NUM AS totalNum,
        risk.brand_code AS brandCode ,
        risk.riskLevel AS riskLevel,
        risk.create_time as createTime
        FROM
        (
        SELECT vid.*, vrwrd.risk_level AS riskLevel
        FROM TF_DWD_VOC_QUALITY_RISK vid
            join (select distinct q.id as id
                from (SELECT q.*, ifnull(sum(rk.keyword_num), 0) as risk_words_num
                    FROM TF_DWD_VOC_QUALITY_RISK q
                    LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                    where q.risk_type = 1
                        and q.channel_id is null
                        <if test="model.brandCode !=null and model.brandCode !=''" >
                            and q.brand_code = #{model.brandCode}
                        </if>
                        <if test="model.tag !=null and model.tag !=''" >
                            AND (q.first_dimension_code  =#{model.tag} or q.second_dimension_code  =#{model.tag} or q.three_dimension_code  =#{model.tag})
                        </if>
                        <if test="model.topicCodes != null and model.topicCodes.size()>0">
                            and (
                            <foreach item="item" collection="model.topicCodes" separator="or" open="(" close=")" index="">
                                q.TOPIC_CODE like CONCAT('%', #{item} ,'%')
                            </foreach>
                            )
                        </if>
                        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
                            and q.publish_date>=#{model.startDate} AND #{model.endDate}>=q.publish_date
                        </if>
                    group by q.id) q
                    left join voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
                    left join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
                    WHERE 1 = 1
                        and q.risk_type = 1
                        and vrwr.risk_type = '质量问题风险'
                        and q.STATISTIC_TYPE = vrwrd.insight_cycle
                        AND q.TOTAL_NUM >= vrwrd.statistic
                        AND q.CHANNEL_NUM >= vrwrd.channel_num
                        AND q.risk_words_num >= vrwrd.risk_words_num
                        <if test="model.brandCode !=null and model.brandCode !=''" >
                            and q.brand_code = #{model.brandCode}
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == -1 " >
                            and vrwrd.insight_cycle = 'd'
                            AND q.STATISTIC_TYPE = 'd'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 0 " >
                            and vrwrd.insight_cycle = 'w'
                            AND q.STATISTIC_TYPE = 'w'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 1 " >
                            and vrwrd.insight_cycle = 'm'
                            AND q.STATISTIC_TYPE = 'm'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 2 " >
                            and vrwrd.insight_cycle = 'q'
                            AND q.STATISTIC_TYPE = 'q'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 3 " >
                            and vrwrd.insight_cycle = 'y'
                            AND q.STATISTIC_TYPE = 'y'
                        </if>
            ) filtered_ids on vid.id = filtered_ids.id
        LEFT JOIN voc_risk_warning_rules vrwr ON vid.brand_code = vrwr.brand_code
        LEFT JOIN voc_risk_warning_rules_detailed vrwrd ON vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and vid.channel_id is null
        AND vrwr.risk_type = '质量问题风险'
        AND vrwrd.type = '2'
        AND cast(vid.risk_index  as signed) >= cast(vrwrd.risk_level_min  as signed) AND cast(vrwrd.risk_level_max as signed) > cast(vid.risk_index  as signed)

        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>

        <if test="model.tag !=null and model.tag !=''" >
            AND (vid.first_dimension_code  =#{model.tag} or vid.second_dimension_code  =#{model.tag} or vid.three_dimension_code  =#{model.tag})
        </if>

        <include refid="publicDateFilterCriteria.queryRisk_vid" />

        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and (
            <foreach item="item" collection="model.topicCodes" separator="or" open="(" close=")" index="">
                vid.TOPIC_CODE like CONCAT('%', #{item} ,'%')
            </foreach>
            )
        </if>
        <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
            and vid.publish_date>=#{model.startDate}
            AND #{model.endDate}>=vid.publish_date
        </if>
        ORDER BY
        vid.TOTAL_NUM DESC
        ) risk
        WHERE
        1 = 1
        <if test="model.riskLevel !=null and model.riskLevel != ''" >
            AND risk.riskLevel = #{model.riskLevel}
        </if>
        group by risk.TOPIC_CODE
        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by risk.RISK_INDEX desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by risk.TOTAL_NUM desc
        </if>


    </select>


    <select id="riskRescueList"  resultType="com.car.stats.vo.risk.RiskPointAggVo">
        SELECT
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.PUBLISH_DATE AS publishDate,
        risk.STATISTIC_TYPE AS statisticType,
        risk.RISK_INDEX AS riskIndex,
        risk.TOTAL_NUM AS totalNum,
        risk.brand_code AS brandCode ,
        risk.riskLevel AS riskLevel,
        risk.create_time as createTime
        FROM
        (
        SELECT vid.*, vrwrd.risk_level AS riskLevel
        FROM TF_DWD_VOC_QUALITY_RISK vid
            join (select distinct q.id as id
                from (SELECT q.*, ifnull(sum(rk.keyword_num), 0) as risk_words_num
                    FROM TF_DWD_VOC_QUALITY_RISK q
                    LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
                    where q.risk_type = 1
                        and q.channel_id is not null
                        <if test="model.brandCode !=null and model.brandCode !=''" >
                            and q.brand_code = #{model.brandCode}
                        </if>
                        <if test="model.tag !=null and model.tag !=''" >
                            AND (q.first_dimension_code  =#{model.tag} or q.second_dimension_code  =#{model.tag} or q.three_dimension_code  =#{model.tag})
                        </if>
                        <if test="model.topicCodes != null and model.topicCodes.size()>0">
                            and (
                            <foreach item="item" collection="model.topicCodes" separator="or" open="(" close=")" index="">
                                q.TOPIC_CODE like CONCAT('%', #{item} ,'%')
                            </foreach>
                            )
                        </if>
                        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
                            and q.publish_date>=#{model.startDate} AND #{model.endDate}>=q.publish_date
                        </if>
                    group by q.id) q
                    left join voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
                    left join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
                    WHERE 1 = 1
                        and q.risk_type = 1
                        and vrwr.risk_type = '救援故障预警'
                        and q.STATISTIC_TYPE = vrwrd.insight_cycle
                        AND q.TOTAL_NUM >= vrwrd.negative_num
                        AND q.CHANNEL_NUM >= vrwrd.channel_num
                        AND q.risk_words_num >= vrwrd.risk_words_num
                        <if test="model.brandCode !=null and model.brandCode !=''" >
                            and q.brand_code = #{model.brandCode}
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == -1 " >
                            and vrwrd.insight_cycle = 'd'
                            AND q.STATISTIC_TYPE = 'd'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 0 " >
                            and vrwrd.insight_cycle = 'w'
                            AND q.STATISTIC_TYPE = 'w'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 1 " >
                            and vrwrd.insight_cycle = 'm'
                            AND q.STATISTIC_TYPE = 'm'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 2 " >
                            and vrwrd.insight_cycle = 'q'
                            AND q.STATISTIC_TYPE = 'q'
                        </if>
                        <if test="model.dateUnit !=null and model.dateUnit == 3 " >
                            and vrwrd.insight_cycle = 'y'
                            AND q.STATISTIC_TYPE = 'y'
                        </if>
            ) filtered_ids on vid.id = filtered_ids.id
        LEFT JOIN voc_risk_warning_rules vrwr ON vid.brand_code = vrwr.brand_code
        LEFT JOIN voc_risk_warning_rules_detailed vrwrd ON vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and vid.channel_id is not null
        AND vrwr.risk_type = '救援故障预警'
        AND vrwrd.type = '2'
        AND cast(vid.risk_index  as signed) >= cast(vrwrd.risk_level_min  as signed) AND cast(vrwrd.risk_level_max as signed) > cast(vid.risk_index  as signed)

        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>

        <if test="model.tag !=null and model.tag !=''" >
            AND (vid.first_dimension_code  =#{model.tag} or vid.second_dimension_code  =#{model.tag} or vid.three_dimension_code  =#{model.tag})
        </if>

        <include refid="publicDateFilterCriteria.queryRisk_vid" />

        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and (
            <foreach item="item" collection="model.topicCodes" separator="or" open="(" close=")" index="">
                vid.TOPIC_CODE like CONCAT('%', #{item} ,'%')
            </foreach>
            )
        </if>
        <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
            and vid.publish_date>=#{model.startDate}
            AND #{model.endDate}>=vid.publish_date
        </if>
        ORDER BY
        vid.TOTAL_NUM DESC
        ) risk
        WHERE
        1 = 1
        <if test="model.riskLevel !=null and model.riskLevel != ''" >
            AND risk.riskLevel = #{model.riskLevel}
        </if>
        group by risk.TOPIC_CODE
        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by risk.RISK_INDEX desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by risk.TOTAL_NUM desc
        </if>


    </select>


<!--    <select id="riskPointAggNew"  resultType="com.car.stats.vo.risk.RiskPointAggVo">-->
<!--        SELECT-->
<!--            distinct-->
<!--            risk.ID riskId,-->
<!--            risk.TOPIC_CODE topicCode,-->
<!--            risk.PUBLISH_DATE as publishDate,-->
<!--            risk.STATISTIC_TYPE as statisticType,-->
<!--            risk.RISK_INDEX as riskIndex,-->
<!--            risk.TOTAL_NUM as totalNum,-->
<!--            risk.brand_code as brandCode-->
<!--        FROM-->
<!--            (-->
<!--            <include refid="riskListCom"/>-->
<!--            ) risk-->
<!--        where-->
<!--            1=1-->
<!--        <if test="model.orderByType !=null and model.orderByType =='index' " >-->
<!--            order by risk.RISK_INDEX desc-->
<!--        </if>-->
<!--        <if test="model.orderByType !=null and model.orderByType =='num' " >-->
<!--            order by risk.TOTAL_NUM desc-->
<!--        </if>-->


<!--    </select>-->

    <select id="riskPointAggNew2"  resultType="com.car.stats.vo.risk.RiskPointAggVo">
        SELECT
            distinct
            risk.ID riskId,
            risk.TOPIC_CODE topicCode,
        risk.PUBLISH_DATE as publishDate,
        risk.STATISTIC_TYPE as statisticType,
            risk.RISK_INDEX as riskIndex,
            risk.TOTAL_NUM as totalNum
        FROM
            (
                SELECT T.*
                FROM (SELECT vid.*,
                             ROW_NUMBER() OVER(PARTITION BY vid.TOPIC_CODE ORDER BY vid.TOTAL_NUM DESC) RW
                      FROM TF_DWD_VOC_QUALITY_RISK vid
                      WHERE
                        1 = 1
                        <include refid="publicDateFilterCriteria.queryRisk_vid" />
                        <if test="model.topicCodes != null and model.topicCodes.size()>0">
                            and vid.TOPIC_CODE in
                            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                                #{item}
                            </foreach>
                        </if>
                      ORDER BY vid.TOTAL_NUM DESC
                     ) T
                WHERE T.RW = 1
            ) risk
        where
            1=1
        ORDER BY totalNum DESC


    </select>

    <select id="getQualityRisk"  resultType="java.math.BigDecimal">
        SELECT
        count( DISTINCT risk.TOPIC_CODE )
        FROM
        (
        (
        SELECT
        vid.*
        FROM
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_QUALITY_RISK qf
        WHERE
        1 = 1
        AND qf.risk_type = 1
        AND qf.ID IN (
        SELECT
        q.id
        FROM
        TF_DWD_VOC_QUALITY_RISK q
        left join
        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and vrwr.risk_type = '风险事件洞察'
        and vrwrd.type = '1'
        <if test="model.dateUnit !=null and model.dateUnit == -1 " >
            and vrwrd.insight_cycle = 'd'
            AND q.STATISTIC_TYPE = 'd'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 0 " >
            and vrwrd.insight_cycle = 'w'
            AND q.STATISTIC_TYPE = 'w'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 1 " >
            and vrwrd.insight_cycle = 'm'
            AND q.STATISTIC_TYPE = 'm'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 2 " >
            and vrwrd.insight_cycle = 'q'
            AND q.STATISTIC_TYPE = 'q'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 3 " >
            and vrwrd.insight_cycle = 'y'
            AND q.STATISTIC_TYPE = 'y'
        </if>
        AND q.TOTAL_NUM >= vrwrd.statistic
        AND q.CHANNEL_NUM >= vrwrd.channel_num
        )
        ) vid
        WHERE
        1 = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        )
        ) risk

    </select>


<!--    <select id="getQualityRisk"  resultType="java.math.BigDecimal">-->
<!--        SELECT-->
<!--        count(distinct risk.TOPIC_CODE)-->
<!--        FROM-->
<!--        (-->
<!--        <include refid="riskListCom" />-->
<!--        ) risk-->

<!--    </select>-->


    <select id="getQualityRisk1"  resultType="java.math.BigDecimal">
       select count(distinct TOPIC_CODE)
        from
        TF_DWD_VOC_QUALITY_RISK vid
        where 1=1

        <include refid="ComFilterMapper.this_risk_date" />
        <include refid="ComFilterMapper.this_risk_date_year" />
        <include refid="ComFilterMapper.this_statisticType" />

    </select>



    <select id="dataAnalysisBriefingTrend"  resultType="com.car.stats.vo.risk.BriefingTrendVo">
        SELECT
                <include refid="publicDateFilterCriteria.groupby-com-cycle" /> AS dateStr,
                SUM(RISK_INDEX) as riskIndex
                FROM
                     TF_DWD_VOC_QUALITY_RISK  vid
                WHERE
                    1=1
                <include refid="publicDateFilterCriteria.queryRisk_vid" />
                group by <include refid="publicDateFilterCriteria.groupby-com-cycle" />
                ORDER BY dateStr desc

    </select>

    <select id="qualityUserStatictis" resultType="com.car.stats.vo.IntentionTrendVo">
        select b.dataStr,IFNULL(total,0) total,IFNULL(userCount,0) userCount FROM
        (<choose>
        <when test="model.dateUnit !=null and model.dateUnit ==-1 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_QUALITY_USER vid
            WHERE 1=1
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="risk.channelId !=null and risk.channelId != '' ">
                AND vid.channel_id is not null
            </if>
            <if test="risk.brandCode !=null and risk.brandCode != '' ">
                AND vid.brand_code = #{risk.brandCode}
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT DATE_FORMAT(tdd.FORMAT_DATE, '%Y-%m-%d') dataStr FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                 tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY DATE_FORMAT(tdd.FORMAT_DATE, '%Y-%m-%d')) b
            ON a.dataStr = b.dataStr
            order by b.dataStr
        </when>
        <when test="model.dateUnit !=null and model.dateUnit ==0 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_QUALITY_USER vid
            WHERE 1=1
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT concat(tdd.DATE_WEEK_YEAR,'-',TDD.DATE_WEEK) dataStr,min(tdd.format_date) format FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                 tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_WEEK_YEAR , TDD.DATE_WEEK) b
            ON a.dataStr = b.dataStr
            order by b.format
        </when>
        <when test="model.dateUnit !=null and model.dateUnit ==1 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_QUALITY_USER vid
            WHERE 1=1
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT concat(tdd.DATE_YEAR,'-',TDD.DATE_MONTH) dataStr,min(tdd.format_date) format FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                 tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_YEAR , TDD.DATE_MONTH) b
            ON a.dataStr = b.dataStr
            order by b.format
        </when>
        <when test="model.dateUnit !=null and model.dateUnit ==2 ">
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_QUALITY_USER vid
            WHERE 1=1
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT concat(tdd.DATE_YEAR,'-',TDD.DATE_QUARTER) dataStr,min(tdd.format_date) format FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                 tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP tdd.DATE_YEAR,TDD.DATE_QUARTER) b
            ON a.dataStr = b.dataStr
            order by b.format
        </when>
        <otherwise>
            select
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>
            as dataStr,
            sum( vid.STATISTIC ) AS total,
            count(distinct vid.user_id) as userCount
            from
            TF_DWS_VOC_QUALITY_USER vid
            WHERE 1=1
            <if test="model.topicCodes != null and model.topicCodes.size()>0">
                and vid.TOPIC_CODE  in
                <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <include refid="ComFilterMapper.this_date"/>
            GROUP BY
            <include refid="publicDateFilterCriteria.groupby-com-cycle"/>) a right join
            (SELECT tdd.DATE_YEAR dataStr FROM TF_DIM_DATE tdd WHERE
            <if test="model.startDate !=null and model.startDate != ''and
                  model.endDate !=null and model.endDate != ''">
                 tdd.FORMAT_DATE between  DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s')
                and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
            </if>
            GROUP BY tdd.DATE_YEAR) b
            ON a.dataStr = b.dataStr
            order by b.dataStr
        </otherwise>
    </choose>


    </select>

    <select id="riskQualiytFiltering_old"  resultType="com.car.stats.entity.risk.DwdVocQualityRiskF">

        SELECT
        er.*
        FROM
        TF_DWD_VOC_QUALITY_RISK er
        WHERE
        1=1
            and er.RISK_INDEX >= #{model.pushCondition}
            and er.PUBLISH_DATE>='2023-01-01 00:00:00'

        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and er.create_time>#{model.lastWarningTime}
        </if>
        order by er.CREATE_TIME desc
    </select>
    <select id="riskQualiytFiltering"  resultType="com.car.stats.entity.risk.DwdVocQualityRiskF">

            SELECT
            er.*
            FROM
            <include refid="after_rule_table_riskQualiytFiltering" /> er
            WHERE
            1=1
                and er.RISK_INDEX >= #{model.pushCondition}
            <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                and er.create_time>#{model.lastWarningTime}
            </if>
        order by er.risk_index DESC,er.statistic_type DESC
    </select>


    <select id="riskQualiytFilteringNew"  resultType="com.car.stats.entity.risk.DwdVocQualityRiskF">
        SELECT
        er.*
        FROM
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_QUALITY_RISK qf
        WHERE
        1 = 1
        AND qf.risk_type = 1
<!--
        AND qf.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
-->
        AND (
        qf.ID IN (
        select
        qa.id
        from(
        select
        q.id as id,
        q.risk_words_num
        from
        ( SELECT q.*, ifnull(sum(rk.keyword_num),0) as risk_words_num
        FROM TF_DWD_VOC_QUALITY_RISK q
        LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
        where q.risk_type =1
        and q.channel_id is null
<!--
              AND q.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
-->
              <if test="model.brandCode !=null and model.brandCode !=''" >
                  and q.brand_code = #{model.brandCode}
              </if>
              <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                and q.create_time>#{model.lastWarningTime}
              </if>
        group by q.id ) q
        left join
        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and q.risk_type =1
        and vrwr.risk_type = '质量问题风险'
        and q.STATISTIC_TYPE = vrwrd.insight_cycle
        AND q.TOTAL_NUM >= vrwrd.statistic
        AND q.CHANNEL_NUM >= vrwrd.channel_num
        AND q.risk_words_num >= vrwrd.risk_words_num
        ) as qa
        )
        )
        ) er
        left join
        voc_risk_warning_rules vrwr on er.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE 1 = 1
        and vrwrd.type = 2
        and er.risk_index >= vrwrd.risk_level_min
        and vrwrd.risk_level_max > er.risk_index
        and vrwr.risk_type = '质量问题风险'
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and er.brand_code = #{model.brandCode}
        </if>
        <if test="model.pushConditionList != null and model.pushConditionList.size() >0">
            and vrwrd.risk_level in
            <foreach item="item" collection="model.pushConditionList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
                <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                    and er.create_time>#{model.lastWarningTime}
                </if>
        ORDER BY
        er.risk_index DESC,
        er.statistic_type DESC
    </select>

    <select id="riskRescueFiltering"  resultType="com.car.stats.entity.risk.DwdVocQualityRiskF">
        SELECT
        er.*
        FROM
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_QUALITY_RISK qf
        WHERE
        1 = 1
        AND qf.risk_type = 1
<!--
        AND qf.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
-->
        AND (
        qf.ID IN (
        select
        qa.id
        from(
        select
        q.id as id,
        q.risk_words_num
        from
        ( SELECT q.*, ifnull(sum(rk.keyword_num),0) as risk_words_num
        FROM TF_DWD_VOC_QUALITY_RISK q
        LEFT JOIN tf_dwd_voc_quality_risk_info rk ON q.id = rk.risk_id
        where q.risk_type =1
        and q.channel_id is not null
<!--
              AND q.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
-->
              <if test="model.brandCode !=null and model.brandCode !=''" >
                  and q.brand_code = #{model.brandCode}
              </if>
              <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                and q.create_time>#{model.lastWarningTime}
              </if>
        group by q.id ) q
        left join
        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and q.risk_type =1
        and vrwr.risk_type = '救援故障预警'
        and q.STATISTIC_TYPE = vrwrd.insight_cycle
        AND q.TOTAL_NUM >= vrwrd.negative_num
        AND q.CHANNEL_NUM >= vrwrd.channel_num
        AND q.risk_words_num >= vrwrd.risk_words_num
        ) as qa
        )
        )
        ) er
        left join
        voc_risk_warning_rules vrwr on er.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE 1 = 1
        and vrwrd.type = 2
        and er.risk_index >= vrwrd.risk_level_min
        and vrwrd.risk_level_max > er.risk_index
        and vrwr.risk_type = '救援故障预警'
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and er.brand_code = #{model.brandCode}
        </if>
        <if test="model.pushConditionList != null and model.pushConditionList.size() >0">
            and vrwrd.risk_level in
            <foreach item="item" collection="model.pushConditionList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
                <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                    and er.create_time>#{model.lastWarningTime}
                </if>
        ORDER BY
        er.risk_index DESC,
        er.statistic_type DESC
    </select>



    <select id="dataAnalysisWaringNum" resultType="string">
        SELECT
            DATE_FORMAT(risk.PUBLISH_DATE, '%Y-%m-%d') as  dateStr
        FROM
            TF_DWD_VOC_QUALITY_RISK risk
            LEFT JOIN VOC_RISK_WARNING_RULES_DETAILED rd ON risk.STATISTIC_TYPE=rd.INSIGHT_CYCLE

        where
        1=1
          and risk.TOTAL_NUM>=rd.STATISTIC
          AND risk.CHANNEL_NUM>=rd.CHANNEL_NUM
          and risk.STATISTIC_TYPE=#{risk.statisticType}
          and rd.INSIGHT_CYCLE=#{risk.statisticType}
          and rd.WARN_RULE_ID='lsdfj34243241311434'
          and risk.TOPIC_CODE =#{risk.topicCode}
          and risk.PUBLISH_DATE>='2023-01-01 00:00:00'

        group by DATE_FORMAT(risk.PUBLISH_DATE, '%Y-%m-%d')

        order by dateStr desc


    </select>
    <select id="riskExport_old"  resultType="com.car.stats.vo.risk.RiskExportResultVo">
        SELECT
        CASE
        WHEN risk.risk_index >= 90 THEN "S"
        WHEN (risk.risk_index > 70 AND 90 > risk.risk_index) THEN "A"
        WHEN (risk.risk_index > 50 AND 70 >= risk.risk_index) THEN  "B"
        WHEN (risk.risk_index > 30 AND 50 >= risk.risk_index) THEN "C"
        WHEN (risk.risk_index >= 0 AND 30 >= risk.risk_index) THEN "D" end riskLevel,
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.RISK_INDEX as riskIndex,
        risk.TOTAL_NUM as totalNum,
        risk.PUBLISH_DATE as publishDate,
        risk.create_time as createTime,
        risk.STATISTIC_TYPE as statisticType,
        risk.date_year as dateYear,
        risk.date_month as dateMonth,
        risk.date_week as dateWeek
        FROM
        (
        <include refid="riskListCom_RiskExport" />
        ) risk

        where
        1=1
        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by risk.RISK_INDEX desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by risk.NEGATIVE_NUM desc
        </if>
    </select>

    <select id="riskExport"  resultType="com.car.stats.vo.risk.RiskExportResultVo">
        SELECT
        risk.ID riskId,
        risk.TOPIC_CODE topicCode,
        risk.RISK_INDEX as riskIndex,
        risk.TOTAL_NUM as totalNum,
        risk.PUBLISH_DATE as publishDate,
        risk.create_time as createTime,
        risk.STATISTIC_TYPE as statisticType,
        risk.date_year as dateYear,
        risk.date_month as dateMonth,
        risk.brand_code as brandCode,
        vbpm.name as brandName,
        risk.date_week as dateWeek
        FROM
        TF_DWD_VOC_QUALITY_RISK risk
        left join voc_brand_product_manager vbpm  on risk.brand_code =vbpm.code
        where
        1=1
        <if test="model.brandCode != null and model.brandCode != ''">
            AND risk.brand_code =#{model.brandCode}
        </if>
        and risk.RISK_INDEX>0
        order by totalNum desc
        limit 10000
    </select>




</mapper>
