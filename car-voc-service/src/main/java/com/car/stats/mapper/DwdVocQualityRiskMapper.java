package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocQualityRiskF;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.IntentionTrendVo;
import com.car.stats.vo.risk.BriefingTrendVo;
import com.car.stats.vo.risk.RiskExportResultVo;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DwdVocRiskMapper.java
 * @Description TODO
 * @createTime 2022年11月25日 13:39
 * @Copyright voc
 */
public interface DwdVocQualityRiskMapper extends BaseMapper<DwdVocQualityRisk> {

    IPage<RiskPointAggVo> riskPointAggNew(Page<RiskPointAggVo> page, @Param("model") RiskEventInsightModel model,@Param("rule") RiskRuleVo rule);
    IPage<RiskPointAggVo> riskRescueList(Page<RiskPointAggVo> page, @Param("model") RiskEventInsightModel model,@Param("rule") RiskRuleVo rule);
    IPage<RiskPointAggVo> riskPointAggNew2(Page<RiskPointAggVo> page, @Param("model") RiskEventInsightModel model);

    List<BriefingTrendVo> dataAnalysisBriefingTrend(@Param("model") RiskEventInsightModel model);
    List<IntentionTrendVo> qualityUserStatictis(@Param("model") FilterCriteriaModel model,@Param("risk") DwdVocQualityRisk risk);
    BigDecimal getQualityRisk(@Param("model") RiskEventInsightModel model,@Param("rule") RiskRuleVo vo);
    BigDecimal getQualityRisk1(@Param("model") FilterCriteriaModel model);

    List<DwdVocQualityRiskF> riskQualiytFiltering(@Param("model") VocRiskWarningRules rule,@Param("rule") RiskRuleVo vo);

    List<String> dataAnalysisWaringNum(@Param("risk") DwdVocQualityRisk risk);
    List<RiskExportResultVo> riskExport(@Param("model") RiskEventInsightModel model, @Param("rule") RiskRuleVo vo);

    List<DwdVocQualityRiskF> riskQualiytFilteringNew(@Param("model") VocRiskWarningRules riskWarningRules,@Param("rule") RiskRuleVo vo);

    List<DwdVocQualityRiskF> riskRescueFiltering(@Param("model")  VocRiskWarningRules riskWarningRules, @Param("rule") RiskRuleVo vo);
}
