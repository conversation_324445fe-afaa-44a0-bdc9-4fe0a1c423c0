package com.car.stats.mapper;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.stats.entity.DwsVocEmotionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelSelectModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.*;
import com.car.stats.vo.risk.IntentionEmotionTrendVo;
import com.car.stats.vo.risk.VoiceUserTopVo;
import com.car.stats.vo.risk.VoiceUserVo;
import com.car.voc.vo.ProportionAreaVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocEmotionDiMapper.java
 * @Description TODO
 * @createTime 2022年10月17日 00:02
 * @Copyright voc
 */
public interface DwsVocEmotionDiMapper extends BaseMapper<DwsVocEmotionDi> {
    VocOverBriefingValueVo overviewBriefingValue(@Param("model") FilterCriteriaModel model);

    List<ChannelStatisticVo> sourceChannel(@Param("model") FilterCriteriaModel model);

    EmotionProportionVo focusProportionEmotion(@Param("model")FilterCriteriaModel model);

    List<ThemeDistrVo> themeDistribution(@Param("model") FilterCriteriaModel model);

    List<LabelVo> themeShare(@Param("model") FilterCriteriaModel model);
    List<LabelVo> themeShare_new(@Param("model") FilterCriteriaModel model);

    List<LabelVo> secondThemeShare(@Param("model") FilterCriteriaModel model);
    List<LabelVo> secondThemeShare_new(@Param("model") FilterCriteriaModel model);

    List<HighHotWordsVo> highFrequencyWords(@Param("model") FilterCriteriaModel model);

    List<ChannelVo> channelDistribution(@Param("model") FilterCriteriaModel model);
    List<ChannelVo> riskChannelDistribution(@Param("model") FilterCriteriaModel model);
    List<ChannelVo> complaintWebsiteChannelTrend(@Param("model") FilterCriteriaModel model);

    List<JSONObject > quantitytop(@Param("model") FilterCriteriaModel model);
    List<MentiontopVo > quantitytop2(@Param("model") FilterCriteriaModel model);
    List<JSONObject> quantitytop1(@Param("model") FilterCriteriaModel model);

    List<AnalyEmotionEateVo> analyEmotionMentionEate(@Param("model") LabelSelectModel model);

    List<ThemeDistrVo> threeDistribution(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> topicDistribution(@Param("model") FilterCriteriaModel model);

    List<LabelVo> threeThemeShare(@Param("model") FilterCriteriaModel model);
    List<LabelVo> threeThemeShare_new(@Param("model") FilterCriteriaModel model);

    List<AnalyEmotionEateVo> analyEmotionMentionEate2(@Param("model")  LabelSelectModel model);
    List<AnalyEmotionEateVo> analyEmotionMentionEate3(@Param("model")  LabelSelectModel model);
    List<AnalyEmotionEateVo> analyEmotionMentionEate4(@Param("model")  LabelSelectModel model);

    IntentionEmotionTrendVo emotionIntentionTrend(@Param("model") RiskEventInsightModel model1);

    List<HighHotWordsVo> riskHotWords(@Param("model")  RiskEventInsightModel model);

    VoiceUserVo riskVoiceUserTrend(@Param("model") RiskEventInsightModel model);

    List<VoiceUserTopVo> voiceUserTop(@Param("model") RiskEventInsightModel model);
    List<VoiceUserTopVo> voiceUserTop1(@Param("model") RiskEventInsightModel model);

    BigDecimal sumQuantityTop(@Param("model") FilterCriteriaModel model);

    List<ChannelVo> dataSourceDistribution(@Param("model") FilterCriteriaModel model);
    List<ChannelVo> riskDataSourceDistribution(@Param("model") FilterCriteriaModel model);

    List<MentiontopVo > quantityChanFistTop2(@Param("model")  FilterCriteriaModel model);

    List<ThemeDistrVo> secondDistribution(@Param("model") FilterCriteriaModel model);

    List<LabelVo> channelQuantityTop(@Param("model") FilterCriteriaModel model);

    List<ChannelVo> channel2Distribution(@Param("model")  FilterCriteriaModel model);

    List<LabelVo> channelQuantityTop2(@Param("model")  FilterCriteriaModel model);
    @Select("SELECT RISK_ID  FROM VOC_RISK_ALL_TYPES t JOIN VOC_RISK_HANDLING_RECORD r ON t.id=r.WARNING_RISK_ID WHERE risk = #{1} GROUP BY RISK_ID  ")
    List<String> getWaringList(String topicCode);
    List<ProportionCarSeriesVo> riskCarSeriesProption(@Param("model") FilterCriteriaModel model);
    List<ProportionAreaVo> riskAreaProption(@Param("model") FilterCriteriaModel model);

}
