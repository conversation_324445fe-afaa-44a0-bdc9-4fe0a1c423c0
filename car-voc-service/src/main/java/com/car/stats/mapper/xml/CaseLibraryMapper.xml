<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.CaseLibraryMapper">

    <sql id="condition_if">
        where 1 = 1
        <if test="model.caseClassifyId != null and model.caseClassifyId != ''">
            AND case_classify_id = #{model.caseClassifyId}
        </if>
        <if test="model.recommend != null and model.recommend != ''">
            AND recommend = #{model.recommend}
        </if>
        <if test="model.status != null and model.status != ''">
            AND status = #{model.status}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            AND
            (
                name like CONCAT('%', #{model.keyword} ,'%')
                or
                sketch like CONCAT('%', #{model.keyword} ,'%')
                or
                content like CONC<PERSON>('%', #{model.keyword} ,'%')
            )
        </if>
    </sql>

    <select id="getCaseNum" resultType="int">
        select
            count(1) as caseNum
        from
            case_library
        <include refid="condition_if"/>
    </select>

    <select id="getDepNum" resultType="int">
        select
            count(a.department) as depNum
        from
            (
                select
                    department_id as department
                from
                    case_library
                <include refid="condition_if"/>
                group by department_id
            )as a
    </select>

    <select id="getPeopleNum" resultType="int">
        select
            count(a.createBy) as peopleNum
        from
            (
                select
                    create_by as createBy
                from
                    case_library
                <include refid="condition_if"/>
                group by create_by
            )as a
    </select>

    <select id="getDescCreateTime" resultType="java.lang.String">
        select
            update_time
        from
            case_library
        <include refid="condition_if"/>
        order by update_time desc
        limit 1
    </select>
</mapper>
