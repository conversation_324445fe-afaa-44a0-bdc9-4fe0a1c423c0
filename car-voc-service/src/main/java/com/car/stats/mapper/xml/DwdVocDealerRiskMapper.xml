<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwdVocDealerRiskMapper">

    <select id="riskPointAggNew"  resultType="com.car.stats.vo.risk.RiskPointAggVo">

        SELECT
        risk.ID riskId,
        CONCAT(risk.dlr_code,'|',risk.dlr_name) as riskName,
        risk.RISK_INDEX AS riskIndex,
        risk.complain_num AS totalNum,
        risk.PUBLISH_DATE AS publishDate,
        risk.STATISTIC_TYPE AS statisticType,
        risk.NEGATIVE_NUM AS negativeNum,
        risk.brand_code AS brandCode,
        risk.risk_level as riskLevel,
        risk.create_time as createTime
        FROM
        (
        SELECT vid.*, vrwrd.risk_level
        FROM tf_dwd_voc_dealer_risk vid
        join (select distinct q.id as id
        from (
        SELECT q.*, ifnull(sum(rk.keyword_num), 0) as risk_words_num
        FROM tf_dwd_voc_dealer_risk q
        LEFT JOIN tf_dwd_voc_dealer_risk_info rk ON q.id = rk.risk_id
        where q.risk_type = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and q.brand_code = #{model.brandCode}
        </if>
        <if test="model.startDate !=null and model.startDate != ''and model.endDate !=null and model.endDate != ''">
            and q.publish_date>=#{model.startDate}
            AND #{model.endDate}>=q.publish_date
        </if>
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and q.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by q.id
        ) q
        left join voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE 1 = 1
        and q.risk_type = 1
        and vrwr.risk_type = '网点风险预警'
        and q.STATISTIC_TYPE = vrwrd.insight_cycle
        AND q.NEGATIVE_NUM >= vrwrd.negative_num
        AND q.COMPLAIN_NUM >= vrwrd.complaint_num
        AND q.risk_words_num >= vrwrd.risk_words_num
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and q.brand_code = #{model.brandCode}
        </if>
        <if test="model.secondDimensionCodes != null and model.secondDimensionCodes.size()>0">
            and q.SECOND_DIMENSION_CODE in
            <foreach item="item" collection="model.secondDimensionCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == -1 " >
            and vrwrd.insight_cycle = 'd'
            AND q.STATISTIC_TYPE = 'd'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 0 " >
            and vrwrd.insight_cycle = 'w'
            AND q.STATISTIC_TYPE = 'w'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 1 " >
            and vrwrd.insight_cycle = 'm'
            AND q.STATISTIC_TYPE = 'm'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 2 " >
            and vrwrd.insight_cycle = 'q'
            AND q.STATISTIC_TYPE = 'q'
        </if>
        <if test="model.dateUnit !=null and model.dateUnit == 3 " >
            and vrwrd.insight_cycle = 'y'
            AND q.STATISTIC_TYPE = 'y'
        </if>
        ) filtered_ids on vid.id = filtered_ids.id
        left join voc_risk_warning_rules vrwr on vid.brand_code = vrwr.brand_code
        left join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id

        where 1 = 1
        and vrwr.risk_type = '网点风险预警'
        and vrwrd.type = '2'
        AND cast(vid.risk_index  as signed) >= cast(vrwrd.risk_level_min  as signed) AND cast(vrwrd.risk_level_max as signed) > cast(vid.risk_index  as signed)
        <if test="model.brandCode !=null and model.brandCode !=''" >
            AND vid.brand_code =#{model.brandCode}
        </if>
        <include refid="publicDateFilterCriteria.queryRisk_vid" />
        <if test="model.topicCodes != null and model.topicCodes.size()>0">
            and vid.TOPIC_CODE in
            <foreach item="item" collection="model.topicCodes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.startDate !=null and model.startDate != ''and
                              model.endDate !=null and model.endDate != ''">
            and vid.publish_date>=#{model.startDate}
            AND #{model.endDate}>=vid.publish_date
        </if>

        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by vid.RISK_INDEX desc,vid.complain_num desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by vid.complain_num desc
        </if>
        ) risk
        WHERE
        1 = 1
        <if test="model.tag !=null and model.tag !=''" >
            and risk.TOPIC_CODE like  CONCAT('%', #{model.tag} ,'%')
        </if>
        <if test="model.riskLevel !=null and model.riskLevel !=''" >
            and risk.risk_level = #{model.riskLevel}
        </if>
        <if test="model.orderByType !=null and model.orderByType =='index' " >
            order by risk.RISK_INDEX desc,vid.complain_num desc
        </if>
        <if test="model.orderByType !=null and model.orderByType =='num' " >
            order by risk.complain_num desc
        </if>
    </select>



    <select id="riskBranchesFiltering"  resultType="com.car.stats.entity.risk.DwdVocDealerRisk">
        SELECT
        er.*
        FROM
        (
        SELECT
        *
        FROM
        tf_dwd_voc_dealer_risk qf
        WHERE
        1 = 1
        AND qf.risk_type = 1
        <!--
                    AND qf.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
        -->
        AND (
        qf.ID IN (
        select
        qa.id
        from(
        select
        q.id as id,
        q.risk_words_num
        from
        (
        SELECT q.*, ifnull(sum(rk.keyword_num),0) as risk_words_num
        FROM tf_dwd_voc_dealer_risk q
        LEFT JOIN tf_dwd_voc_dealer_risk_info rk ON q.id = rk.risk_id
        where q.risk_type =1
        <!--
                                              AND q.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
        -->
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and q.brand_code = #{model.brandCode}
        </if>
        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and q.create_time>#{model.lastWarningTime}
        </if>
        group by q.id
        ) q
        left join
        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and q.risk_type =1
        and vrwr.risk_type = '网点风险预警'
        and q.STATISTIC_TYPE = vrwrd.insight_cycle
        AND q.NEGATIVE_NUM >= vrwrd.negative_num
        AND q.COMPLAIN_NUM >= vrwrd.complaint_num
        AND q.risk_words_num >=  vrwrd.risk_words_num
        ) as qa
        )
        )
        ) er
        left join
        voc_risk_warning_rules vrwr on er.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE 1 = 1
        and vrwrd.type = 2
        and er.risk_index >= vrwrd.risk_level_min
        and vrwrd.risk_level_max > er.risk_index
        and vrwr.risk_type = '网点风险预警'
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and er.brand_code = #{model.brandCode}
        </if>
        <if test="model.pushConditionList != null and model.pushConditionList.size() >0">
            and vrwrd.risk_level in
            <foreach item="item" collection="model.pushConditionList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and er.create_time>#{model.lastWarningTime}
        </if>
        ORDER BY
        er.risk_index DESC,
        er.statistic_type DESC
    </select>




</mapper>
