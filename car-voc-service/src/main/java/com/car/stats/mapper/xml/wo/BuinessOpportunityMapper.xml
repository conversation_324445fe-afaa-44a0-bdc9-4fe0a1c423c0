<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.wo.BusinessOpportunityMapper">
    <!--========================================商机线索来源分布=========================================================================-->
    <select id="clueDist"  resultType="com.car.stats.vo.ChannelVo">

        select
        vid.channel_source_3 as channelStr,
        count(1) as statistic,
        ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
        from (<include refid="ComFilterMapper.wo_table_view"/>) vid
        where
            1=1
        and vid.channel_source_3 is not null
        <include refid="publicDateFilterCriteria.wo_original_query"/>
        <include refid="ComFilterMapper.wo-lable-query"/>
        group by vid.channel_source_3
    </select>
    <!--========================================到店愿意占比=========================================================================-->
    <select id="visitIntent"  resultType="com.car.stats.vo.wo.VisitIntentVo">
        select
            t1.*,
        ROUND((willing / total) * 100, 2) AS willingP,
        ROUND((unwilling / total) * 100, 2) AS unwillingP,
        ROUND((unclear / total) * 100, 2) AS unclearP
        from (
        select
        sum(CASE WHEN is_visit  = '是' THEN 1 ELSE 0 END) as willing,
        sum(CASE WHEN is_visit = '否' THEN 1 ELSE 0 END) as unwilling,
        sum(CASE WHEN is_visit is null THEN 1 ELSE 0 END) as unclear,
        count(1) as total
        from
        (<include refid="ComFilterMapper.wo_table_view"/>) vid
        where
        1=1
        <include refid="ComFilterMapper.wo-lable-query"/>
        ) t1
    </select>
    <!--========================================购车需求趋势=========================================================================-->
    <select id="carPurchaseDemandTrend"  resultType="com.car.stats.vo.wo.BuyCarTypeVo">
        <include refid="publicDateFilterCriteria.wo-groupby-com-cycle-sr" />
        SELECT
        *,
        ROUND((batch / total) * 100, 2) AS batchP,
        ROUND((single / total) * 100, 2) AS singleP
        FROM (
        SELECT
        cc.*,
        case when length(cc.date_)>9 then date_format(date_, '%Y/%m/%d')
        else cc.date_ end as date_str,
        ifnull(total, 0) AS total,
        ifnull(batch, 0) AS batch,
        ifnull(single, 0) AS single
        FROM common_date_tools cc
        LEFT JOIN (
        SELECT
        <include refid="publicDateFilterCriteria.wo_create_time_FormatCase-Trend" /> AS date_str,
        COUNT(1) as total,
        sum(CASE WHEN is_batch_order  = '是' THEN 1 ELSE 0 END) as batch,
        sum(CASE WHEN is_batch_order = '否'  THEN 1 ELSE 0 END) as single
        <include refid="publicDateFilterCriteria.wo_org_commonDateQuery" />
        <include refid="publicDateFilterCriteria.wo_original_query" />
        <include refid="publicDateFilterCriteria.wo_original_lable_query" />
        GROUP BY date_str
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        ORDER BY date_ ASC
    </select>
    <!-- ================================================商机线索-意向网点排行==================================================== -->
    <select id="intendedBranchTop" resultType="com.car.stats.vo.wo.BranchesVo">
        SELECT
        dlr_code as dlrCode,
        dlr_name as branchName,
        dealer_region_1 as region1,
        dealer_region_2 as region2,
        customer_province as province,
        customer_city as city,
        count(1) as statistic
        FROM
        (<include refid="ComFilterMapper.wo_table_view"/>) vid
        WHERE
        1=1
        and dlr_code is not null
        <include refid="publicDateFilterCriteria.wo_original_query" />
        <include refid="publicDateFilterCriteria.wo_original_lable_query" />
        GROUP BY
        dlr_code,dlr_name,dealer_region_1,dealer_region_2,customer_province,customer_city
        order by statistic desc
        limit 10
    </select>

    <!-- ================================================商机线索-意向车系排行==================================================== -->
    <select id="intendedCarTop" resultType="com.car.stats.vo.ProportionCarSeriesVo">
        <include refid="publicDateFilterCriteria.groupby-com-cycle-sr-range" />
        SELECT
        *,
        ROUND(CASE WHEN voice_r = 0 AND (statistic - voice_r) != 0 THEN 999999
        ELSE IFNULL((statistic - voice_r) / IF(voice_r = 0, 1, voice_r) * 100, 0) END, 2) as statisticR
        FROM (
        SELECT
        cc.*,
        cc.date_ AS dateStr,
        car_series as carSeriesName,
        statisticP,
        voice_c as statistic,
        <!-- 环比数据 -->
        ifnull(LEAD(voice_c) OVER (PARTITION BY car_series ORDER BY date_ DESC), 0) AS voice_r
        FROM common_date_tools cc
        LEFT JOIN (
        <if test="model.dateUnit !=null and model.dateUnit !=-1 " >
            SELECT
            <include refid="publicDateFilterCriteria.wo_create_time_FormatCase" /> AS date_str,
            car_series ,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
            from (<include refid="ComFilterMapper.wo_table_view"/>) vid
            left join  (
            select date_unit,
            min(startDate) as startDate, max(endDate) as endDate
            from common_date_tools
            group by date_unit
            )c9 on vid.create_time BETWEEN startDate and endDate
            WHERE 1=1
            and vid.create_time BETWEEN startDate and endDate
            and ( vid.car_series is not null or vid.car_series !='')
            <include refid="publicDateFilterCriteria.wo_original_query" />
            <include refid="publicDateFilterCriteria.wo_original_lable_query" />
            GROUP BY date_str,car_series
        </if>

        <if test="model.dateUnit !=null and model.dateUnit ==-1 " >
            SELECT
            DATE_FORMAT(cdt.endDate, '%Y%m%d') AS date_str,
            car_series,
            cdt.startDate, cdt.endDate,
            COUNT(1) AS voice_c,
            ROUND(COUNT(1) / SUM(COUNT(1)) OVER () * 100, 2) AS statisticP
            FROM common_date_tools cdt
            LEFT JOIN (<include refid="ComFilterMapper.wo_table_view"/>) vid ON date(vid.create_time) BETWEEN cdt.startDate AND cdt.endDate
            WHERE 1=1
            and vid.car_series is not null
            <include refid="publicDateFilterCriteria.wo_original_query" />
            <include refid="publicDateFilterCriteria.wo_original_lable_query" />
            GROUP BY DATE_FORMAT(cdt.endDate, '%Y%m%d'),
            car_series, cdt.startDate, cdt.endDate
        </if>
        ) f1 ON cc.date_ = f1.date_str
        ORDER BY date_ DESC
        ) f2
        WHERE date_ = (SELECT MAX(date_) FROM common_date_tools)
        ORDER BY statistic DESC
    </select>

    <!-- ================================================商机线索-工单列表==================================================== -->
    <select id="woOrderList" resultType="com.car.stats.vo.wo.WoBusinessVo">
        SELECT
        *
        FROM
        ( <include refid="ComFilterMapper.wo_table_view"/>) vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.wo_original_query" />
        <include refid="publicDateFilterCriteria.wo_original_lable_query"/>
        order by create_time desc
    </select>
</mapper>
