<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.stats.mapper.DwdVocUserRiskMapper">

    <sql id="after_rule_table_bak">
        (
            SELECT *
            FROM TF_DWD_VOC_USER_RISK qf
            WHERE 1 = 1
              and qf.display_name is not null
              and qf.PUBLISH_DATE>='2023-09-01 00:00:00'
              and qf.PUBLISH_DATE>= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
              and (
                        qf.ID IN
                        (
                            SELECT id
                            FROM TF_DWD_VOC_USER_RISK q
                            WHERE 1 = 1
                              and q.STATISTIC_TYPE = 'd'
                              and q.NEGATIVE_NUM >= #{rule.negativeNumD}
                              and q.COMPLAIN_NUM >= #{rule.complaintNumD}
                              AND q.CHANNEL_NUM >= #{rule.channelNumD}
                        )
                    or
                        qf.ID in
                        (
                            SELECT id
                            FROM TF_DWD_VOC_USER_RISK q
                            WHERE 1 = 1
                              and q.STATISTIC_TYPE = 'w'
                              and q.NEGATIVE_NUM >= #{rule.negativeNumW}
                              and q.COMPLAIN_NUM >= #{rule.complaintNumW}
                              AND q.CHANNEL_NUM >= #{rule.channelNumW}
                        )
                    or
                        qf.ID in
                        (
                            SELECT id
                            FROM TF_DWD_VOC_USER_RISK q
                            WHERE 1 = 1
                              and q.STATISTIC_TYPE = 'm'
                              and q.NEGATIVE_NUM >= #{rule.negativeNumM}
                              and q.COMPLAIN_NUM >= #{rule.complaintNumM}
                              AND q.CHANNEL_NUM >= #{rule.channelNumM}
                        )
                    or
                        qf.ID in
                        (
                            SELECT id
                            FROM TF_DWD_VOC_USER_RISK q
                            WHERE 1 = 1
                              and q.STATISTIC_TYPE = 'q'
                              and q.NEGATIVE_NUM >= #{rule.negativeNumQ}
                              and q.COMPLAIN_NUM >= #{rule.complaintNumQ}
                              AND q.CHANNEL_NUM >= #{rule.channelNumQ}
                        )
                )
        )
    </sql>
<sql id="after_rule_table_bak_1">
        (
            SELECT
                *
            FROM
                TF_DWD_VOC_USER_RISK qf
            WHERE
                  1=1
                <if test="model.dateUnit !=null and model.dateUnit ==-1 " >

                            and
                            qf.ID IN
                            (
                                SELECT
                                    id
                                FROM
                                    TF_DWD_VOC_USER_RISK q
                                WHERE
                                    1=1
                                  and q.STATISTIC_TYPE='d'
                                  and q.NEGATIVE_NUM>=#{rule.negativeNumD}
                                  and q.COMPLAIN_NUM>=#{rule.complaintNumD}
                                  AND q.CHANNEL_NUM>=#{rule.channelNumD}

                            )
                </if>
        <if test="model.dateUnit !=null and model.dateUnit ==0 " >

                and
                    qf.ID in
                    (
                        SELECT
                            id
                        FROM
                            TF_DWD_VOC_USER_RISK q
                        WHERE
                            1=1
                          and q.STATISTIC_TYPE='w'
                          and q.NEGATIVE_NUM>=#{rule.negativeNumW}
                          and q.COMPLAIN_NUM>=#{rule.complaintNumW}
                          AND q.CHANNEL_NUM>=#{rule.channelNumW}

                    )
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==1 " >

                and
                    qf.ID in
                    (
                        SELECT
                            id
                        FROM
                            TF_DWD_VOC_USER_RISK q
                        WHERE
                            1=1
                          and q.STATISTIC_TYPE='m'
                          and q.NEGATIVE_NUM>=#{rule.negativeNumM}
                          and q.COMPLAIN_NUM>=#{rule.complaintNumM}
                          AND q.CHANNEL_NUM>=#{rule.channelNumM}


                    )
        </if>
        <if test="model.dateUnit !=null and model.dateUnit ==2 " >

         and
                    qf.ID in
                    (
                        SELECT
                            id
                        FROM
                            TF_DWD_VOC_USER_RISK q
                        WHERE
                            1=1
                          and q.STATISTIC_TYPE='q'
                          and q.NEGATIVE_NUM>=#{rule.negativeNumQ}
                          and q.COMPLAIN_NUM>=#{rule.complaintNumQ}
                          AND q.CHANNEL_NUM>=#{rule.channelNumQ}


                    )
        </if>
            <if test="model.dateUnit !=null and model.dateUnit ==3 " >

                and
                qf.ID in
                (
                SELECT
                id
                FROM
                TF_DWD_VOC_USER_RISK q
                WHERE
                1=1
                and q.STATISTIC_TYPE='y'
                and q.NEGATIVE_NUM>=#{rule.negativeNumY}
                and q.COMPLAIN_NUM>=#{rule.complaintNumY}
                AND q.CHANNEL_NUM>=#{rule.channelNumY}


                )
            </if>


        )
    </sql>




    <sql id="riskListCom">
        (
        SELECT
        t.USER_ID as "userId",
        t.ID as "id",
        t.DISPLAY_NAME as "userName",
        t.COMPLAIN_NUM as "complaint",
        t.NEGATIVE_NUM as "negativeNum",
        t.CHANNEL_NUM as "channel",
        t.EMOTION_NUM as "emotionNum",
        t.RISK_INDEX as "riskIndex",
        t.brand_code as brandCode
        FROM (SELECT distinct vid.*
        FROM <include refid="after_rule_table_bak_1" /> vid
        WHERE
        1 = 1
        AND vid.DISPLAY_NAME IS NOT NULL
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
        ORDER BY  vid.COMPLAIN_NUM DESC,vid.RISK_INDEX  DESC
        ) T
        )
    </sql>



    <sql id="riskListCom_old">
        (
        SELECT
        t.USER_ID as "userId",
        t.ID as "id",
        t.DISPLAY_NAME as "userName",
        t.COMPLAIN_NUM as "complaint",
        t.NEGATIVE_NUM as "negativeNum",
        t.CHANNEL_NUM as "channel",
        t.EMOTION_NUM as "emotionNum",
        t.RISK_INDEX as "riskIndex"
        FROM (SELECT distinct vid.*,
        ROW_NUMBER() OVER(PARTITION BY vid.USER_ID ORDER BY vid.COMPLAIN_NUM DESC,vid.RISK_INDEX desc ) RW
        FROM <include refid="after_rule_table_bak_1" /> vid
        WHERE
        1 = 1
        AND vid.DISPLAY_NAME IS NOT NULL
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
        ORDER BY  vid.COMPLAIN_NUM DESC,vid.RISK_INDEX  DESC
        ) T
        WHERE T.RW = 1
        )
    </sql>

    <sql id="riskListCom1">



        (
        SELECT
        t.USER_ID as "userId",
        t.ID as "id",
        t.DISPLAY_NAME as "userName",
        t.COMPLAIN_NUM as "complaint",
        t.NEGATIVE_NUM as "negativeNum",
        t.CHANNEL_NUM as "channel",
        t.EMOTION_NUM as "emotionNum",
        t.RISK_INDEX as "riskIndex"
        FROM (
        select *
        from (
        select
        vid.*,
        @rownum := @rownum+1 ,
        if(@pdept=vid.USER_ID,@rank:=@rank+1,@rank:=1) as rank,
        @pdept:=vid.USER_ID
        from (select * from <include refid="after_rule_table_bak"/> vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
        ) vid
        ,
        (select @rownum :=0 , @pdept := null ,@rank:=0) c
        order by vid.COMPLAIN_NUM DESC
        ) result
        where rank = 1

            ) T
        )
    </sql>

    <select id="complaintUserList"  resultType="com.car.stats.vo.risk.ComplaintUserVo">
        SELECT
        risk.*
        FROM
        (

        SELECT
        t.USER_ID AS "userId",
        t.ID AS "id",
        t.display_name AS "userName",
        t.PUBLISH_DATE AS publishDate,
        t.STATISTIC_TYPE AS statisticType,
        t.COMPLAIN_NUM AS "complaint",
        t.NEGATIVE_NUM AS "negativeNum",
        t.CHANNEL_NUM AS "channel",
        t.EMOTION_NUM AS "emotionNum",
        t.RISK_INDEX AS "riskIndex",
        t.brand_code AS brandCode ,
        t.riskLevel AS riskLevel,
        t.create_time as createTime
        FROM
        (
        SELECT
        vid.id,
        vid.user_id,
        vid.user_type,
        vid.user_level,
        max(vid.display_name) as display_name,
        vid.negative_num,
        vid.complain_num,
        vid.channel_num,
        vid.emotion_num,
        vid.risk_level,
        vid.risk_index,
        vid.statistic_type,
        vid.publish_date,
        vid.date_year,
        vid.date_month,
        vid.date_week,
        vid.date_quarter,
        vid.create_time,
        vid.brand_code,
        vrwrd.risk_level AS riskLevel
        FROM
            tf_dwd_voc_user_risk vid
        inner join (
            select distinct q.id
                from tf_dwd_voc_user_risk q
                join voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code and vrwr.risk_type = '高频投诉用户'
                join voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id and vrwrd.type = '1'
            where
                1 = 1
                <if test="model.brandCode !=null and model.brandCode !=''" >
                    and q.brand_code = #{model.brandCode}
                </if>
                <if test="model.dateUnit !=null and model.dateUnit == -1 " >
                    and vrwrd.insight_cycle = 'd'
                    AND q.STATISTIC_TYPE = 'd'
                </if>
                <if test="model.dateUnit !=null and model.dateUnit == 0 " >
                    and vrwrd.insight_cycle = 'w'
                    AND q.STATISTIC_TYPE = 'w'
                </if>
                <if test="model.dateUnit !=null and model.dateUnit == 1 " >
                    and vrwrd.insight_cycle = 'm'
                    AND q.STATISTIC_TYPE = 'm'
                </if>
                <if test="model.dateUnit !=null and model.dateUnit == 2 " >
                    and vrwrd.insight_cycle = 'q'
                    AND q.STATISTIC_TYPE = 'q'
                </if>
                <if test="model.dateUnit !=null and model.dateUnit == 3 " >
                    and vrwrd.insight_cycle = 'y'
                    AND q.STATISTIC_TYPE = 'y'
                </if>
                and q.negative_num >= vrwrd.negative_num
                and q.complain_num >= vrwrd.complaint_num
                and q.channel_num >= vrwrd.channel_num
                and vrwrd.emotion_num > q.emotion_num * 100
        ) filtered_ids on vid.id = filtered_ids.id

        LEFT JOIN voc_risk_warning_rules vrwr ON vid.brand_code = vrwr.brand_code
        LEFT JOIN voc_risk_warning_rules_detailed vrwrd ON vrwr.id = vrwrd.warn_rule_id

        WHERE
        1 = 1
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and vid.brand_code = #{model.brandCode}
        </if>
        AND vrwr.risk_type = '高频投诉用户'
        AND vrwrd.type = '2'
        AND cast(vid.risk_index  as signed) >= cast(vrwrd.risk_level_min  as signed) AND cast(vrwrd.risk_level_max  as signed) > cast(vid.risk_index  as signed)
        <if test="model.riskLevel !=null and model.riskLevel !=''" >
            and vrwrd.risk_Level = #{model.riskLevel}
        </if>
        AND vid.DISPLAY_NAME IS NOT NULL
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />

        <if test="model.startDate !=null and model.startDate != ''and
                      model.endDate !=null and model.endDate != ''">
            and vid.publish_date>=#{model.startDate}
            AND #{model.endDate}>=vid.publish_date
        </if>
        group by vid.user_id
        ORDER BY
        vid.COMPLAIN_NUM DESC,
        vid.RISK_INDEX DESC
        ) T
        ORDER BY  "complaint" DESC,"riskIndex" DESC
        ) risk
        WHERE
        1 = 1





    </select>

<!--    <select id="complaintUserList"  resultType="com.car.stats.vo.risk.ComplaintUserVo">-->
<!--        SELECT-->
<!--        distinct-->
<!--        risk.*-->
<!--        FROM-->
<!--        (-->
<!--        <include refid="riskListCom" />-->
<!--        ) risk-->
<!--        where-->
<!--        1=1-->
<!--        ORDER BY  "complaint" DESC,"riskIndex" DESC-->


<!--    </select>-->

    <select id="complaintUserList1"  resultType="com.car.stats.vo.risk.ComplaintUserVo">
        SELECT
            distinct
            risk.*
        FROM
            (
                SELECT
                       t.USER_ID as "userId",
                       t.ID as "id",
                       t.DISPLAY_NAME as "userName",
                       t.COMPLAIN_NUM as "complaint",
                       t.NEGATIVE_NUM as "negativeNum",
                       t.CHANNEL_NUM as "channel",
                       t.EMOTION_NUM as "emotionNum",
                       t.RISK_INDEX as "riskIndex"
                FROM (SELECT distinct vid.*,
                                      ROW_NUMBER() OVER(PARTITION BY vid.USER_ID ORDER BY vid.COMPLAIN_NUM DESC,vid.RISK_INDEX desc ) RW
                      FROM TF_DWD_VOC_USER_RISK vid
                      WHERE
                          1 = 1
                        AND vid.DISPLAY_NAME IS NOT NULL
                        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
                      ORDER BY  vid.COMPLAIN_NUM DESC,vid.RISK_INDEX  DESC
                     ) T
                WHERE T.RW = 1
            ) risk
        where
            1=1
        ORDER BY  "complaint" DESC,"riskIndex" DESC


    </select>

<!--    <select id="getComplaintUser"  resultType="java.math.BigDecimal">-->

<!--        SELECT-->
<!--        count(1)-->
<!--        FROM-->
<!--        (-->
<!--        (-->
<!--        SELECT-->
<!--        vid.*-->
<!--        FROM-->
<!--        (-->
<!--        SELECT-->
<!--        *-->
<!--        FROM-->
<!--        TF_DWD_VOC_USER_RISK qf-->
<!--        WHERE-->
<!--        1 = 1-->
<!--        AND qf.ID IN (-->
<!--        SELECT-->
<!--        q.id-->
<!--        FROM-->
<!--        TF_DWD_VOC_USER_RISK q-->
<!--        left join-->
<!--        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code-->
<!--        left join-->
<!--        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id-->
<!--        WHERE-->
<!--        1 = 1-->
<!--        and vrwr.risk_type = '风险事件洞察'-->
<!--        and vrwrd.type = '1'-->
<!--        <if test="model.dateUnit !=null and model.dateUnit == -1 " >-->
<!--            and vrwrd.insight_cycle = 'd'-->
<!--            AND q.STATISTIC_TYPE = 'd'-->
<!--        </if>-->
<!--        <if test="model.dateUnit !=null and model.dateUnit == 0 " >-->
<!--            and vrwrd.insight_cycle = 'w'-->
<!--            AND q.STATISTIC_TYPE = 'w'-->
<!--        </if>-->
<!--        <if test="model.dateUnit !=null and model.dateUnit == 1 " >-->
<!--            and vrwrd.insight_cycle = 'm'-->
<!--            AND q.STATISTIC_TYPE = 'm'-->
<!--        </if>-->
<!--        <if test="model.dateUnit !=null and model.dateUnit == 2 " >-->
<!--            and vrwrd.insight_cycle = 'q'-->
<!--            AND q.STATISTIC_TYPE = 'q'-->
<!--        </if>-->
<!--        <if test="model.dateUnit !=null and model.dateUnit == 3 " >-->
<!--            and vrwrd.insight_cycle = 'y'-->
<!--            AND q.STATISTIC_TYPE = 'y'-->
<!--        </if>-->
<!--        AND q.NEGATIVE_NUM >= vrwrd.negative_num-->
<!--        AND q.COMPLAIN_NUM >= vrwrd.complaint_num-->
<!--        AND q.CHANNEL_NUM >= vrwrd.channel_num-->
<!--        )-->
<!--        ) vid-->
<!--        WHERE-->
<!--        1 = 1-->

<!--        <if test="model.brandCode !=null and model.brandCode !=''" >-->
<!--            AND vid.brand_code =#{model.brandCode}-->
<!--        </if>-->
<!--        AND vid.DISPLAY_NAME IS NOT NULL-->
<!--        <include refid="publicDateFilterCriteria.queryRisk_vid" />-->


<!--        )-->
<!--        ) risk-->
<!--    </select>-->


    <select id="getComplaintUser"  resultType="java.math.BigDecimal">

        SELECT
        count(1)
        FROM
        <include refid="riskListCom"/> vid
    </select>
    <select id="complaintUserListTop"  resultType="com.car.stats.vo.risk.ComplaintUserVo">
        SELECT
        USER_ID as "id",
        max(vid.DISPLAY_NAME) as userName,
        vid.IS_ONE_ID as isOneId,
        sum( CASE vid.INTENTION_TYPE WHEN '投诉' THEN vid.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE vid.DIMENSION_EMOTION WHEN '正面' THEN vid.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE vid.DIMENSION_EMOTION WHEN '负面' THEN vid.STATISTIC ELSE 0 END ) AS negative
        FROM
        TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
        and vid.INTENTION_TYPE='投诉'
        and vid.DIMENSION_EMOTION='负面'
        and vid.IS_ONE_ID='1'
        GROUP BY USER_ID,vid.IS_ONE_ID
        ORDER BY complaint DESC

    </select>

<select id="complaintUserListTop1"  resultType="com.car.stats.vo.risk.ComplaintUserVo">
        SELECT
        T.USER_ID as "id" ,
        T.DISPLAY_NAME as userName,
        T.IS_ONE_ID  as isOneId,
        T.USER_TYPE as userType,
        T.USER_LEVEL as userLevel,
        SUM(T.STATISTIC) as statistic,
        sum( CASE T.INTENTION_TYPE WHEN '投诉' THEN T.STATISTIC ELSE 0 END ) AS complaint,
        sum( CASE T.DIMENSION_EMOTION WHEN '正面' THEN T.STATISTIC ELSE 0 END ) AS positive,
        sum( CASE T.DIMENSION_EMOTION WHEN '负面' THEN T.STATISTIC ELSE 0 END ) AS negative
        FROM
        (
        SELECT vid.USER_ID,vid.DISPLAY_NAME,vid.IS_ONE_ID,vid.STATISTIC,vid.INTENTION_TYPE,vid.DIMENSION_EMOTION,vid.USER_TYPE,vid.USER_LEVEL,
        ROW_NUMBER() OVER(PARTITION BY vid.USER_ID ORDER BY vid.PUBLISH_DATE DESC) RW
        FROM TF_DWS_VOC_EMOTION_USER vid
        WHERE
        1=1
        <include refid="publicDateFilterCriteria.queryUserRisk_vid" />
        and vid.INTENTION_TYPE='投诉'
        and vid.DIMENSION_EMOTION='负面'
        and vid.DISPLAY_NAME is not null
        ) T
        WHERE
        T.RW = 1
        GROUP BY T.USER_ID,T.DISPLAY_NAME,T.IS_ONE_ID,T.USER_TYPE,T.USER_LEVEL
        ORDER BY complaint DESC

    </select>




    <select id="riskUserFiltering"  resultType="com.car.stats.entity.risk.DwdVocUserRisk">

        SELECT
        er.*
        FROM
        <include refid="after_rule_table_bak" /> er
        WHERE
        1=1
        and er.RISK_INDEX >=#{model.pushCondition}
        <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
            and er.create_time>#{model.lastWarningTime}
        </if>
        order by er.create_time desc
    </select>

    <select id="riskUserFilteringNew"  resultType="com.car.stats.entity.risk.DwdVocUserRisk">
        SELECT
        er.*
        FROM
        (
        SELECT
        *
        FROM
        TF_DWD_VOC_USER_RISK qf
        WHERE
        1 = 1
        AND qf.display_name IS NOT NULL
        AND qf.PUBLISH_DATE >= DATE_SUB( CURDATE(), INTERVAL 3 MONTH )
        AND (
        qf.ID IN (
        select
        qa.id
        from(
        select
        q.id as id,
        vrwrd.risk_words_num
        from
        TF_DWD_VOC_USER_RISK q

        left join
        voc_risk_warning_rules vrwr on q.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE
        1 = 1
        and vrwr.risk_type = '高频投诉用户'
        and q.STATISTIC_TYPE = vrwrd.insight_cycle
        and q.NEGATIVE_NUM >= vrwrd.negative_num
        and q.COMPLAIN_NUM >= vrwrd.complaint_num
        AND q.CHANNEL_NUM >= vrwrd.channel_num
        AND vrwrd.emotion_num>q.emotion_num*100
        ) as qa
        )
        )
        ) er
        left join
        voc_risk_warning_rules vrwr on er.brand_code = vrwr.brand_code
        left join
        voc_risk_warning_rules_detailed vrwrd on vrwr.id = vrwrd.warn_rule_id
        WHERE 1 = 1
        and vrwrd.type = 2
        and er.risk_index >= vrwrd.risk_level_min
        and vrwrd.risk_level_max > er.risk_index
        and vrwr.risk_type = '高频投诉用户'
        <if test="model.brandCode !=null and model.brandCode !=''" >
            and er.brand_code = #{model.brandCode}
        </if>
        <if test="model.pushConditionList != null and model.pushConditionList.size() >0">
            and vrwrd.risk_level in
            <foreach item="item" collection="model.pushConditionList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
                <if test="model.lastWarningTime !=null and model.lastWarningTime !=''" >
                    and er.create_time>#{model.lastWarningTime}
                </if>
        ORDER BY
        er.risk_index DESC,
        er.statistic_type DESC
    </select>

</mapper>
