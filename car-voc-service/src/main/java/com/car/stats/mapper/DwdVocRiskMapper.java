package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.ComFilterCriteriaModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.risk.BriefingTrendVo;
import com.car.stats.vo.risk.RiskExportResultVo;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DwdVocRiskMapper.java
 * @Description TODO
 * @createTime 2022年11月25日 13:39
 * @Copyright voc
 */
public interface DwdVocRiskMapper extends BaseMapper<DwdVocRisk> {

    IPage<RiskPointAggVo> riskPointAggNew(Page<RiskPointAggVo> page, @Param("model") RiskEventInsightModel model,@Param("rule") RiskRuleVo rule);
    IPage<RiskPointAggVo> riskPointAggNew1(Page<RiskPointAggVo> page, @Param("model") RiskEventInsightModel model);

    List<BriefingTrendVo> dataAnalysisBriefingTrend(@Param("model") RiskEventInsightModel model);


    BigDecimal getRiskEvents(@Param("model") RiskEventInsightModel model,@Param("rule") RiskRuleVo vo);
    BigDecimal getRiskEvents1(@Param("model") FilterCriteriaModel model);

    List<DwdVocEmotionRisk> riskEventFiltering(@Param("model") VocRiskWarningRules model,@Param("rule")  RiskRuleVo rule);

    List<String> dataAnalysisWaringNum(@Param("risk")DwdVocRisk risk);

    List<String> riskKeywords(@Param("risk") DwdVocRisk risk);

    List<RiskExportResultVo> riskExport(@Param("model") RiskEventInsightModel model, @Param("rule") RiskRuleVo vo);

    List<DwdVocEmotionRisk> riskEventFilteringNew(@Param("model") VocRiskWarningRules rule,@Param("rule") RiskRuleVo vo);
}
