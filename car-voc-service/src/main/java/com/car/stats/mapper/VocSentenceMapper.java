package com.car.stats.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.DwdVocSentence;
import com.car.stats.entity.DwsVocEmotionUserDi;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.ComFilterCriteriaModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.stats.vo.risk.DataDryingContrastVo;
import com.car.stats.vo.risk.EmotionIntentionVo;
import com.car.voc.model.risk.RiskAllTypesModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocEmotionUserDiMapper.java
 * @Description TODO
 * @createTime 2022年10月20日 15:08
 * @Copyright voc
 */
public interface VocSentenceMapper extends BaseMapper<DwdVocSentence> {
    NsrVo emotionalPurification(@Param("model")  FilterCriteriaModel model);

    List<HomePurposeTrendVo> allTagsTrend(@Param("model")  LabelDetailFilterModel model);

    VocOverBriefingValueVo mentionsUser(@Param("model")  FilterCriteriaModel model);
    VocOverBriefingValueVo mentionsUser(@Param("model")  LabelDetailFilterModel model);

    List<ChannelVo> sourceChannel(@Param("model")  FilterCriteriaModel model);

    List<CarEmotionVo> homeLeaderCarDistribution(@Param("model")  FilterCriteriaModel model);

    IntentionRatioVo homeLeaderIntentionProp(@Param("model")  FilterCriteriaModel model);

    List<EmotionProportionVo> homeLeaderEmotionProp(@Param("model")  FilterCriteriaModel model);

    List<ThemeDistrVo> qTag1Dist(@Param("model")  FilterCriteriaModel model);
    List<ThemeDistrVo > allTag1Dist(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> allTag2Dist(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> allTag3Dist(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> allTag4Dist(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> tagDistribution3(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> tagDistribution2(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> tagDistribution4(@Param("model") FilterCriteriaModel model);
    List<ThemeDistrVo> tagDistribution(@Param("model") FilterCriteriaModel model);
    List<HighHotWordsVo> hotWords(@Param("model") FilterCriteriaModel model);

    List<QualityLabelVo> homeLeaderCarIntelligent(@Param("model") FilterCriteriaModel model);

    List<RegionUserVo> provinceMap(@Param("model") FilterCriteriaModel model);

    List<UserLabelVo> carPurchaseDist(@Param("model")  FilterCriteriaModel model);

    List<HighHotWordsVo> purchaseConcernsPriority(@Param("model")   FilterCriteriaModel model);

    List<RegionUserVo> homeLeaderRegionalTop(@Param("model")  FilterCriteriaModel model);
    List<RegionUserVo> homeLeaderCommunityTop(@Param("model")  FilterCriteriaModel model);
}
