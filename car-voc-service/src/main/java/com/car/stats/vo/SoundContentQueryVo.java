package com.car.stats.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.stats.vo.popvo.UserSoundVo;
import com.car.voc.annotation.DataDesensitization;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @version 1.0.0
 * @ClassName UserInfoVo.java
 * @Description TODO
 * @createTime 2022年10月31日 20:07
 * @Copyright voc
 */
@Data
public class SoundContentQueryVo extends UserSoundVo implements Serializable {
    @ExcelProperty(value = "姓名/昵称",order = 19)
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.NAME})
    String userName;
    @ExcelProperty(value = "用户ID",order = 27)
    String userId;
    @ExcelIgnore
    Integer userLevel;
    @ExcelIgnore
    String userType;
    @ExcelProperty(value = "省份",order = 17)
    String province;
    @ExcelProperty(value = "区域",order = 16)
    String region;
    @ExcelIgnore
    String channelId;
    @ExcelProperty(value = "三级渠道",order = 15)
    String channelStr;
    @ExcelProperty(value = "一级渠道",order = 13)
    String channelStr1;
    @ExcelProperty(value = "二级渠道",order = 14)
    String channelStr2;
    @ExcelIgnore
    String viewCounts;
    @ExcelIgnore
    String likeCounts;
    @ExcelIgnore
    String commCounts;
    @ExcelIgnore
    String tagType;
    @ExcelProperty(value = "标签类型",order = 7)
    String tagTypeStr;

    @ExcelIgnore
    String contentId;




    @ExcelProperty(value = "原文标题",order = 20)
    String title;
    @ExcelProperty(value = "原文内容",order = 21)
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    String content;
    @ExcelProperty(value = "原文类型",order = 26)
    String contentType;
    @ExcelIgnore
    String classify1;
    @ExcelProperty(value = "原声一级分类",order = 22)
    String classify1Str;
    @ExcelIgnore
    String classify2;
    @ExcelProperty(value = "原声二级分类",order = 23)
    String classify2Str;
    @ExcelIgnore
    String classify3;
    @ExcelProperty(value = "原声三级分类",order = 24)
    String classify3Str;
    @ExcelIgnore
    String classify4;
    @ExcelProperty(value = "原声四级分类",order = 25)
    String classify4Str;

    @ExcelProperty(value = "原车系",order = 16)
    String priCarSeriesName;
    @ExcelProperty(value = "数据状态",order = 28)
    String status;

    public String getTagTypeStr() {
        if (this.tagType!=null){
            return "1".equals(this.tagType)?"业务标签":"质量标签";
        }else {
            return "";
        }

    }

    public SoundContentQueryVo(EsDataSentenceVocs content) {
        super(content);
        userName=content.getDisplayName();
        userId=content.getOneId();
        userLevel=content.getUserLevel();
        userType=content.getUserType();
        province=content.getProvince();
        channelId=content.getChannelId();
        viewCounts=content.getViewCounts();
        likeCounts=content.getLikeCounts();
        commCounts=content.getCommCounts();
        contentId=content.getContentId();
        this.tagType=content.getTagType();
        this.status=content.getStatus();

    }

    public SoundContentQueryVo() {
    }


}
