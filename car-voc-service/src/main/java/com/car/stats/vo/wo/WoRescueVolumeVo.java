package com.car.stats.vo.wo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WoRescueVolumeVo {

    @ApiModelProperty(value = "时间")
    private String dateStr;
    /**
     * 救援量
     */
    @ApiModelProperty(value = "救援量")
    private BigDecimal rescueNum;
    /**
     * 未救援量
     */
    @ApiModelProperty(value = "未救援量")
    private BigDecimal unRescueNum;
    /**
     * 救援量占比
     */
    @ApiModelProperty(value = "救援量占比")
    private BigDecimal rescueNumP;
    /**
     * 未救援量占比
     */
    @ApiModelProperty(value = "未救援量占比")
    private BigDecimal unRescueNumP;
    /**
     * 统计量
     */
    @ApiModelProperty(value = "提及量")
    private BigDecimal statistic = BigDecimal.ZERO;

}
