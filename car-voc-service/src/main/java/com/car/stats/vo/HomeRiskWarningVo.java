package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName HomeRiskWarningVo.java
 * @Description TODO
 * @createTime 2023年01月12日 22:02
 * @Copyright voc
 */
@Data
public class HomeRiskWarningVo {
        BigDecimal riskEvents = BigDecimal.ZERO;;
        BigDecimal riskEventsAdd = BigDecimal.ZERO;;
        BigDecimal qualityRisk = BigDecimal.ZERO;;
        BigDecimal qualityRiskAdd = BigDecimal.ZERO;;
        BigDecimal complaintUser = BigDecimal.ZERO;;
        BigDecimal complaintUserAdd = BigDecimal.ZERO;;
        BigDecimal toBeReviewed = BigDecimal.ZERO;;
        BigDecimal toBeConfirmed = BigDecimal.ZERO;;

}
