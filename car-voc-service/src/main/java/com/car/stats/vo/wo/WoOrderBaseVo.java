package com.car.stats.vo.wo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.enums.ContactTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ColumnWidth(25)
@ToString
public abstract class WoOrderBaseVo implements Serializable {

    @ApiModelProperty(value = "工单编号", example = "exampleWoNum")
    @ExcelIgnore
    private String woNum;

    @ApiModelProperty(value = "code", example = "exampleCode")
    @ExcelProperty(value = "案例编号")
    private String code;

    @ApiModelProperty(value = "客户姓名", example = "exampleCustomerName")
    @ExcelProperty(value = "个人客户")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.NAME})
    private String customerName;

    @ApiModelProperty(value = "一级类型", example = "exampleWoNum")
    @ExcelProperty(value = "一级类型")
    String firstDimensionCode;

    @ApiModelProperty(value = "二级类型", example = "exampleWoNum")
    @ExcelProperty(value = "二级类型")
    String secondDimensionCode;

    @ApiModelProperty(value = "三级类型", example = "exampleWoNum")
    @ExcelProperty(value = "三级类型")
    String threeDimensionCode;

    @ApiModelProperty(value = "四级类型", example = "exampleWoNum")
    @ExcelProperty(value = "四级类型")
    String topicCode;

    @ApiModelProperty(value = "描述", example = "exampleWoContent")
    @ExcelProperty(value = "描述")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    private String woContent;

    @ApiModelProperty(value = "创建时间", example = "2023-10-10 10:00:00")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "首次下发时间", example = "2023-10-10 10:00:00")
    @ExcelProperty(value = "下发网点时间")
    private Date firstStepTime;

    @ApiModelProperty(value = "网点首次反馈时间", example = "2023-10-10 10:00:00")
    @ExcelProperty(value = "网点首次反馈时间")
    private Date dlrFirstFeedbackTime;

    @ApiModelProperty(value = "状态", example = "exampleStatus")
    @ExcelProperty(value = "状态")
    private String statusStr;

    @ApiModelProperty(value = "购车网点名称", example = "exampleVehicleDlrName")
    @ExcelProperty(value = "购车网点名称")
    private String vehicleDlrName;

    @ApiModelProperty(value = "处理网点名称", example = "exampleDlrName")
    @ExcelProperty(value = "处理网点名称")
    private String dlrName;

    @ApiModelProperty(value = "网点编码", example = "exampleDlrCode")
    @ExcelProperty(value = "网点编码")
    private String dlrCode;

    @ApiModelProperty(value = "大区", example = "exampleDealerRegion1")
    @ExcelProperty(value = "处理网点所属大区")
    private String dealerRegion1;

    @ApiModelProperty(value = "小区", example = "exampleDealerRegion2")
    @ExcelProperty(value = "处理网点所属小区")
    private String dealerRegion2;

    @ApiModelProperty(value = "区域督导", example = "exampleRegionalSupervisor")
    @ExcelProperty(value = "区域督导")
    private String regionalSupervisor;

    @ApiModelProperty(value = "车系", example = "exampleCarSeries")
    @ExcelProperty(value = "车系")
    private String carSeries;

    @ApiModelProperty(value = "车型", example = "exampleModelName")
    @ExcelProperty(value = "车型")
    private String modelName;

    @ApiModelProperty(value = "来源渠道", example = "exampleWoChannel")
    @ExcelProperty(value = "来源分类")
    private String woChannel;

    @ApiModelProperty(value = "购车状态", example = "exampleCustomerType")
    @ExcelProperty(value = "购车状态")
    private String customerType;

    @ApiModelProperty(value = "工单渠道细分来源1级", example = "exampleChannelSource2")
    @ExcelProperty(value = "一级来源")
    private String channelSource2;

    @ApiModelProperty(value = "工单渠道细分来源2级", example = "exampleChannelSource3")
    @ExcelProperty(value = "二级来源")
    private String channelSource3;


    @ApiModelProperty(value = "不满意原因分类", example = "exampleDissatisfactionCategory")
    @ExcelProperty(value = "不满意原因分类")
    private String dissatisfactionCategory;

    /**
     * 工单关闭时长
     */
    @ExcelProperty(value = "工单关闭时长（天）")
    @ApiModelProperty(value = "工单关闭时长（天）")
    private Long woCloseTime;

    /**
     * 反馈时长
     */
    @ExcelProperty(value = "反馈时长（天）")
    @ApiModelProperty(value = "反馈时长（天）")
    private Long feedbackTime;

    @ApiModelProperty(value = "是否一次性解决", example = "exampleIsOneTimeResolution")
    @ExcelProperty(value = "是否一次性解决")
    private String isOneTimeResolution;

    @ApiModelProperty(value = "未一次性解决原因", example = "exampleReason")
    @ExcelProperty(value = "未一次性解决原因")
    private String reason;

    @ApiModelProperty(value = "是否回访客户", example = "exampleIsFollowupCustomer")
    @ExcelProperty(value = "是否回访客户")
    private String isFollowupCustomer;

    @ExcelProperty(value = "是否救援")
    @ApiModelProperty(value = "是否救援", example = "exampleIsRescue")
    private String isRescue;

    @ExcelProperty(value = "救援接收点")
    @ApiModelProperty(value = "救援接收点", example = "rescueType")
    private String rescueType;

    @ExcelProperty(value = "未实施救援原因")
    @ApiModelProperty(value = "未实施救援原因", example = "exampleIsRescue")
    private String unRescueReason;

    @ApiModelProperty(value = "客户是否满意", example = "exampleCustomerSatisfaction")
    @ExcelProperty(value = "客户是否满意")
    private String customerSatisfaction;

    @ApiModelProperty(value = "客户省份", example = "exampleCustomerProvince")
    @ExcelProperty(value = "省/(直辖)市(个人客户)")
    private String customerProvince;

    @ApiModelProperty(value = "客户市", example = "exampleCustomerCity")
    @ExcelProperty(value = "城市(个人客户)")
    private String customerCity;


    @ApiModelProperty(value = "状态", example = "exampleStatus")
    @ExcelIgnore
    private String status;

    @ApiModelProperty(value = "状态code", example = "exampleStatusCode")
    @ExcelIgnore
    private String statusCode;

    @ApiModelProperty(value = "品牌", example = "exampleBrand")
    @ExcelIgnore
    private String brand;

    @ApiModelProperty(value = "工单主题", example = "exampleWoTheme")
    @ExcelIgnore
    private String woTheme;

    @ApiModelProperty(value = "工单类型", example = "exampleWoType")
    @ExcelIgnore
    private String woType;

    @ApiModelProperty(value = "工单渠道细分来源2级", example = "exampleChannelSource")
    @ExcelIgnore
    private String channelSource;

    @ApiModelProperty(value = "OneID", example = "exampleOneID")
    @ExcelIgnore
    private String oneid;

    @ApiModelProperty(value = "处理意见", example = "exampleDisposeOpinion")
    @ExcelIgnore
    @DataDesensitization(type = DataDesensitization.DesensitizationType.PHONE)
    private String disposeOpinion;

    @ApiModelProperty(value = "是否意愿到店", example = "exampleIsVisit")
    @ExcelIgnore
    private String isVisit;

    @ApiModelProperty(value = "是否批量订单", example = "exampleIsBatchOrder")
    @ExcelIgnore
    private String isBatchOrder;

    @ExcelProperty(value = "救援提供渠道")
    @ExcelIgnore
    private String rescueProvideChannelName;

    @ExcelProperty(value = "救援订单编号")
    @ExcelIgnore
    private String rescueOrderNum;

    @ApiModelProperty(value = "工单关闭时间", example = "2023-10-10 10:00:00")
    @ExcelIgnore
    private Date closeTime;

    @ApiModelProperty(value = "服务商", example = "exampleServiceStationInfo")
    @ExcelIgnore
    private String serviceStationInfo;

    @ApiModelProperty(value = "数据日期", example = "20231010")
    @ExcelIgnore
    private Integer dataDate;

    @ApiModelProperty(value = "经销商", example = "exampleDealer")
    @ExcelIgnore
    private String dealer;

    @ApiModelProperty(value = "工单四级标签", example = "exampleTagCode")
    @ExcelIgnore
    private String tagCode;

    @ApiModelProperty(value = "vin", example = "exampleVin")
    @DataDesensitization(type = DataDesensitization.DesensitizationType.VIN)
    @ExcelProperty(value = "vin")
    private String vin;

    @ApiModelProperty(value = "customerPhone", example = "customerPhone")
    @DataDesensitization(type = DataDesensitization.DesensitizationType.PHONE)
    @ExcelProperty(value = "手机号")
    private String customerPhone;

    @ApiModelProperty(value = "更新时间", example = "2023-10-10 10:00:00")
    @ExcelIgnore
    private Date modifyTime;

    @ApiModelProperty(value = "voc渠道1级", example = "exampleVocChannel1")
    @ExcelIgnore
    private String vocChannel1;

    @ApiModelProperty(value = "voc渠道2级", example = "exampleVocChannel2")
    @ExcelIgnore
    private String vocChannel2;

    @ApiModelProperty(value = "voc渠道3级", example = "exampleVocChannel3")
    @ExcelIgnore
    private String vocChannel3;

    @ApiModelProperty(value = "任一指标code", example = "exampleTagCodeRandom")
    @ExcelIgnore
    private String tagCodeRandom;

    public void setRescueType(String rescueType) {
        if (StrUtil.isNotBlank(rescueType)) {
            rescueType = rescueType.replace("关单", "");
        }
        this.rescueType = rescueType;
    }

    /**
     * 工单关闭时长(天) = 关闭时间-创建时间
     *
     * @return 工单关闭时长(天)
     */
    public Long getWoCloseTime() {
        Date closeTime = this.getCloseTime();
        Date createTime = this.getCreateTime();
        if (closeTime != null && createTime != null) {
            woCloseTime = (closeTime.getTime() - createTime.getTime()) / (24 * 60 * 60 * 1000);
        }
        return woCloseTime;
    }

    /**
     * 获取反馈时长(天) = 首次反馈时间-首次下发时间
     *
     * @return 获取反馈时长
     */
    public Long getFeedbackTime() {
        Date firstFeedbackTime = this.getDlrFirstFeedbackTime();
        Date firstStepTime = this.getFirstStepTime();
        if (firstFeedbackTime != null && firstStepTime != null) {
            feedbackTime = (firstFeedbackTime.getTime() - firstStepTime.getTime()) / (24 * 60 * 60 * 1000);
        }
        return feedbackTime;
    }

    public String getStatusStr() {
        if (status == null) {
            return "";
        }
        try {
            return Integer.parseInt(status) <= 3 ? "未关闭" : "已关闭";
        } catch (Exception e) {
            return status;
        }
    }

    public String getWoChannel() {
        if (StrUtil.isBlank(this.woChannel)) {
            return "";
        }
        return ContactTypeEnum.getDescByCode(this.woChannel);
    }
}
