package com.car.stats.vo.risk;

import cn.hutool.core.date.DateUtil;
import com.car.stats.vo.HighHotWordsVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName RiskBriefingVo.java
 * @Description TODO
 * @createTime 2022年11月25日 18:36
 * @Copyright voc
 */
@Data
public class RiskBriefingVo {
    String earlyRelease;
    String earlyReleaseDate;
    String warnPeriod;
    String timeStr;
    BigDecimal statistic;
    BigDecimal statisticR;
    BigDecimal statisticTotal;
    String riskGradeTag;
    String riskName;
    String region;

    @ApiModelProperty(value = "开始日期",example = "2022-06-01 00:00:00")
    String startDate;
    @ApiModelProperty(value = "结束日期",example = "2022-10-01 00:00:00")
    String endDate;
    @ApiModelProperty(value = "日期单位(0为周，1为月，2为季，3为年度，-1为自定义)",example = "1")
    Integer dateUnit;




    @ApiModelProperty(value = "发声用数")
    BigDecimal usersNum;
    @ApiModelProperty(value = "发声用户环比")
    BigDecimal usersNumR;
    BigDecimal userTotalNum;
    Integer grade;
    Integer rise;
    Set<String> aggProblem;
    List<String> carSeries;
    List<HighHotWordsVo> hotWords;
    List<String> hotWordsOpinion;
    List<String> riskKeywords;
    List<EarlyWarningTrendVo> trendVos;

   /* public void setWarnPeriod(String statisticType) {
        this.warnPeriod= CalculatorUtils.getPeriod(statisticType);
    }*/

    public String getEndDate() {
        if (dateUnit!=null){
            switch (dateUnit){
                case -1:
                    return endDate;
                case 0:
                    return DateUtil.endOfWeek(DateUtil.parseDateTime(this.startDate)).toString();
                case 1:
                    return DateUtil.endOfMonth(DateUtil.parseDateTime(this.startDate)).toString();
                case 2:
                    return DateUtil.endOfQuarter(DateUtil.parseDateTime(this.startDate)).toString();
                default:
                    return DateUtil.endOfYear(DateUtil.parseDateTime(this.startDate)).toString();
            }
        }else {
            return null;
        }
    }
}
