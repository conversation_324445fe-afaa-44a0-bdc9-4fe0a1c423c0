package com.car.stats.vo.popvo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.SoundContentVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName PopUpVo.java
 * @Description TODO
 * @Copyright voc
 */
@Data
public class PopUpVo implements Serializable {
    String labelStr;
    BigDecimal statistic;
    BigDecimal statisticR;
    BigDecimal userCount;
    BigDecimal userCountR;
    List<UserLabelVo> aggDistribution;
    Page<SoundContentVo> soundList;
    List<HomePurposeTrendVo> trendChange;
}
