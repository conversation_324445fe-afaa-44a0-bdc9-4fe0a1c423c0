package com.car.stats.vo.risk;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName VoiceUserVo.java
 * @Description TODO
 * @createTime 2022年11月26日 21:32
 * @Copyright voc
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoiceUserVo {

    String dateStr;
    BigDecimal userNum = BigDecimal.ZERO;
    BigDecimal userNumR  = BigDecimal.ZERO;
}
