package com.car.stats.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName TrendChannelVo.java
 * @Description TODO
 * @createTime 2022年10月20日 15:55
 * @Copyright voc
 */
@Data
public class TrendChannelRiskVo {
    String dateStr;
    List<ChannelVo> channelVos;
    String uni;

    public TrendChannelRiskVo() {
    }

    public TrendChannelRiskVo(Map.Entry<String, List<ChannelVo>> entry) {
        this.dateStr=entry.getKey();
        this.setChannelVos(entry.getValue());
    }



    public String getDateStr() {
        if (dateStr.length()>9){
            return this.dateStr;
        }
        String year;
        if (dateStr.contains("-")){
            year=dateStr.substring(0,dateStr.indexOf("-"));
            uni=dateStr.substring(dateStr.indexOf("-")+1);
            String zero=uni.substring(0,1);
            if ("0".equals(zero)){
                uni=uni.replaceFirst("0","");
            }
            return year+"-"+uni;

        }else {
            year=dateStr;
            return year;
        }

    }
}
