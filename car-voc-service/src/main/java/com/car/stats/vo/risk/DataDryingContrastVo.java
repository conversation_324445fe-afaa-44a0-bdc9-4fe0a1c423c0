package com.car.stats.vo.risk;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName DataDryingContrastVo.java
 * @Description TODO
 * @createTime 2023年02月17日 13:53
 * @Copyright voc
 */
@Data
public class DataDryingContrastVo {
    BigDecimal negative;
    BigDecimal complaint;
    BigDecimal grumble;
    BigDecimal userNum;
    BigDecimal negativeR;
    BigDecimal complaintR;
    BigDecimal grumbleR;
    BigDecimal userNumR;

    public void setDayAverage(Integer frontDay) {
        if (frontDay!=null&&frontDay>0){
            this.negative= CalculatorUtils.avgePerDayNum(this.negative,new BigDecimal(frontDay));
            this.complaint=CalculatorUtils.avgePerDayNum(this.complaint,new BigDecimal(frontDay));
            this.grumble=CalculatorUtils.avgePerDayNum(this.grumble,new BigDecimal(frontDay));
            this.userNum=CalculatorUtils.avgePerDayNum(this.userNum,new BigDecimal(frontDay));
        }

    }
}
