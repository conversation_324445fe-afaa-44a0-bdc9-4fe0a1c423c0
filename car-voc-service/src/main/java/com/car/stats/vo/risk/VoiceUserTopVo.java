package com.car.stats.vo.risk;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName VoiceUserTopVo.java
 * @Description TODO
 * @createTime 2022年11月26日 21:56
 * @Copyright voc
 */
@Data
public class VoiceUserTopVo {
    String channelId;
    String channelStr;
    String displayName;
    String isOneId;
    BigDecimal userNum;
    String userId;


    public String getDisplayName() {
        if (StrUtil.isBlankIfStr(displayName)){
            return userId.substring(0,6);
        }
        return displayName;
    }
}
