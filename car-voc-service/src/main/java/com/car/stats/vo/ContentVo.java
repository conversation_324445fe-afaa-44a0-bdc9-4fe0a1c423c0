package com.car.stats.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.car.voc.annotation.DataDesensitization;
import lombok.Data;

/**
 * @version 1.0.0
 * @ClassName UserInfoVo.java
 * @Description TODO
 * @createTime 2022年10月31日 20:07
 * @Copyright voc
 */
@Data
@ColumnWidth(25)
public class ContentVo {
    @ExcelProperty(value = "姓名", order = 11)
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.NAME})
    String customerName;
    @ExcelIgnore
    String oneid;

    @ExcelProperty(value = "省份", order = 6)
    String customerProvince;
    @ExcelProperty(value = "区域", order = 5)
    String region;
    @ExcelIgnore
    String channelId;
    @ExcelIgnore
    String indexId;
    @ExcelProperty(value = "三级渠道", order = 4)
    String channelStr;
    @ExcelProperty(value = "二级渠道", order = 3)
    String channelStr2;
    @ExcelProperty(value = "一级渠道", order = 2)
    String channelStr1;
    @ExcelIgnore
    String id;
    @ExcelProperty(value = "原声ID", order = 1)
    String contentId;
    @ExcelProperty(value = "工单ID", order = 1)
    private String code;
    @ExcelProperty(value = "会话ID", order = 1)
    private String sessionId;
    @ExcelProperty(value = "通话ID", order = 1)
    private String callId;
    @ExcelProperty(value = "手机号", order = 11)
    @DataDesensitization(type = DataDesensitization.DesensitizationType.PHONE)
    private String phone;
    @ExcelProperty(value = "vin", order = 11)
    @DataDesensitization(type = DataDesensitization.DesensitizationType.VIN)
    private String vin;

    @ExcelProperty(value = "标题", order = 12)
    String title;
    @ExcelIgnore
    String brandCode;
    @ExcelProperty(value = "品牌", order = 7)
    String brandName;
    @ExcelIgnore
    String carSeriesCode;
    @ExcelIgnore
    String carSeries;

    @ExcelProperty(value = "车系", order = 8)
    String modelName;
    @ExcelProperty(value = "发声时间", order = 10)
    String createTime;

    @ExcelProperty(value = "原文内容", order = 14)
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    String content;
    @ExcelProperty(value = "原文类型", order = 13)
    String contentType;
    @ExcelIgnore
    String classify1;
    @ExcelProperty(value = "原声一级分类", order = 21)
    String classify1Str;

    @ExcelIgnore
    String classify2;
    @ExcelProperty(value = "原声二级分类", order = 22)
    String classify2Str;

    @ExcelIgnore
    String classify3;
    @ExcelProperty(value = "原声三级分类", order = 23)
    String classify3Str;

    @ExcelIgnore
    String classify4;
    @ExcelProperty(value = "原声四级分类", order = 24)
    String classify4Str;
    @ExcelProperty(value = "数据状态", order = 25)
    String tag;
    @ExcelIgnore
    String classification;

    public String getModelName() {
        if (StrUtil.isNotBlank(this.carSeries)) {
            this.modelName = this.carSeries;
            return this.carSeries;
        }
        return this.modelName;
    }

    public void setClassSplit(String split) {
        if (split == null || StrUtil.isBlank(this.classification)) {
            return;
        }
        if ("singleSubstring".equals(split)) {
            this.classify1 = classification.substring(0, 5);
            this.classify2 = classification.substring(0, 8);
            this.classify3 = classification.substring(0, 11);
            this.classify4 = classification;
        } else if ("json".equals(split)) {
            JSONArray classinfor = JSONUtil.parseArray(this.classification);
            if (classinfor.size() > 0) {
                JSONArray cas1 = (JSONArray) classinfor.get(0);
                for (int i = 0; i < cas1.size(); i++) {
                    JSONObject entries = (JSONObject) cas1.get(i);
                    String cary = entries.getStr("name");
                    if (StrUtil.isNotBlank(cary)) {
                        switch (i) {
                            case 0:
                                this.classify1 = cary;
                                break;
                            case 1:
                                this.classify2 = cary;
                                break;
                            case 2:
                                this.classify3 = cary;
                                break;
                            case 3:
                                this.classify4 = cary;
                                break;
                        }
                    }

                }
            }
        } else if (!"json".equals(split)) {
            String[] cla = this.classification.split(split);
            for (int i = 0; i < cla.length; i++) {
                if (i == 0) {
                    this.classify1 = cla[i];
                }
                if (i == 1) {
                    this.classify2 = cla[i];
                }
                if (i == 2) {
                    this.classify3 = cla[i];
                }
                if (i == 3) {
                    this.classify4 = cla[i];
                }
            }
        }
        this.classification = null;
    }
}
