package com.car.stats.vo.popvo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName UserListInfo.java
 * @Description TODO
 * @createTime 2022年10月31日 16:06
 * @Copyright voc
 */
@Data
@ColumnWidth(25)
public class UserListInfoVo {

    @ExcelProperty(value = "用户Id")
    String userId;
    @ExcelIgnore
    String isOneId;
    @ExcelIgnore
    String isOneIdStr;
    @ExcelProperty(value = "姓名")
    @DataDesensitization(type = DataDesensitization.DesensitizationType.NAME)
    String userName;

    @ExcelIgnore
    String userType;
    @ExcelIgnore
    String userTypeStr;
    @ExcelIgnore
    Integer userLevel;
    @ExcelIgnore
    String userLevelStr;
    @ExcelIgnore
    String channelId;
    @ExcelIgnore
    Set<String> channelStr;
    @ExcelProperty(value = "发声渠道")
    String channelStrs;
    @ExcelProperty(value = "发声数")
    BigDecimal publishCount;
    @ExcelProperty(value = "提及量")
    BigDecimal statistic;
    @ExcelProperty(value = "表扬")
    BigDecimal praise;
    @ExcelProperty(value = "投诉")
    BigDecimal complaint;
    @ExcelProperty(value = "咨询")
    BigDecimal consult;
    @ExcelProperty(value = "建议")
    BigDecimal suggest;
    @ExcelProperty(value = "净情感值")
    BigDecimal emotionWorth;
    @ExcelIgnore
    BigDecimal positive;
    @ExcelIgnore
    BigDecimal negative;

    public String getChannelStrs() {
        if (channelStr!=null&&channelStr.size()>0){
           return channelStr.toString();
        }
        return channelStrs;
    }

    public String getIsOneIdStr() {
        return CalculatorUtils.getIsOne(this.isOneId);
    }

    public String getUserType() {
        return StrUtil.isBlankIfStr(this.userType) ? "未知" : this.userType;
    }
}
