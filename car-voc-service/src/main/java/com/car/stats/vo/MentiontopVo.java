package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName MentiontopVo.java
 * @Description TODO
 * @createTime 2022年12月02日 15:41
 * @Copyright voc
 */
@Data
public class MentiontopVo {
    String firstDimensionCode;
    String secondDimensionCode;
    String threeDimensionCode;
    String topicCode;


    String firstDimension;
    String secondDimension;
    String threeDimension;
    String topic;


    String channelId;
    String channelStr;
    BigDecimal statistic = BigDecimal.ZERO;;
    Integer orderBy;

    BigDecimal statisticP = BigDecimal.ZERO;;
    BigDecimal statisticR = BigDecimal.ZERO;;

    List<ChannelVo> channelVos;

}
