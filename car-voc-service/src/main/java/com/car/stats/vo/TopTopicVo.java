package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 * @version 1.0.0
 * @ClassName TopTopicVo.java
 * @Description TODO
 * @createTime 2022年10月12日 19:07
 * @Copyright voc
 */
@Data
public class TopTopicVo {
    String labelStr;
    String labelAllStr;
    String labelCode;
    String labelAllCode;
    BigDecimal referenceNum = BigDecimal.ZERO;
    BigDecimal percentage = BigDecimal.ZERO;
    BigDecimal statistic = BigDecimal.ZERO;
    BigDecimal ringRatio = BigDecimal.ZERO;

    public String getLabelAllCode() {
        return CalculatorUtils.getAllTagCode(this.labelCode);
    }

    public BigDecimal getStatistic() {
        return referenceNum;
    }
}
