package com.car.stats.vo.risk;

import com.car.voc.common.util.CalculatorUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionEmotionTrendVo.java
 * @Description TODO
 * @createTime 2022年11月26日 18:46
 * @Copyright voc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntentionEmotionTrendVo {
    @Builder.Default
    BigDecimal complaint = BigDecimal.ZERO;
    @Builder.Default
    BigDecimal complaintA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal complaintAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal complaintP= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal complaintR= BigDecimal.ZERO;

    @Builder.Default
    BigDecimal userNum = BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumP= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumR= BigDecimal.ZERO;


    @Builder.Default
    BigDecimal praise= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal praiseA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal praiseAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal praiseR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal praiseP= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal negative= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal negativeA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal negativeAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal negativeR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal negativeP= BigDecimal.ZERO;
    String dateStr;

    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.praiseA= CalculatorUtils.avgePerDayNum(this.getPraise(),new BigDecimal(day));
        this.complaintA=CalculatorUtils.avgePerDayNum(this.getComplaint(),new BigDecimal(day));
        this.negativeA=CalculatorUtils.avgePerDayNum(this.getNegative(),new BigDecimal(day));
        this.userNumA=CalculatorUtils.avgePerDayNum(this.getUserNum(),new BigDecimal(day));
    }

}
