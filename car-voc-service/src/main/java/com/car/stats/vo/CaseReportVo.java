package com.car.stats.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * @Description 案例统计对象
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "案例统计对象", description = "案例统计对象")
public class CaseReportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "案例数量")
    private Integer caseNum;

    @ApiModelProperty(value = "分享部门")
    private Integer depNum;

    @ApiModelProperty(value = "分享人数")
    private Integer peopleNum;

    @ApiModelProperty(value = "最新分享")
    private String createTime;

}
