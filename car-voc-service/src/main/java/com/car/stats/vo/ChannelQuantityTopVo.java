package com.car.stats.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelQuantityTopVo
 * @Description TODO ckcui
 * @createTime 2023年11月10日 16:33
 * @Copyright voc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelQuantityTopVo implements Serializable {
    String channelId ;
    List<LabelVo> lableTop = new ArrayList<>();
    @JsonIgnore
    BigDecimal total = BigDecimal.ZERO;
}
