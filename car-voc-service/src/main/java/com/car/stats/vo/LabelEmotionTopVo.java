package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName LabeltopVo.java
 * @Description TODO
 * @createTime 2022年10月16日 10:59
 * @Copyright voc
 */
@Data
public class LabelEmotionTopVo {

   String secondDimensionCode;
   String secondDimensionStr;
   BigDecimal positive= BigDecimal.ZERO;
   BigDecimal negative= BigDecimal.ZERO;;
   BigDecimal neutral= BigDecimal.ZERO;;
   BigDecimal positiveP= BigDecimal.ZERO;;
   BigDecimal negativeP= BigDecimal.ZERO;;
   BigDecimal neutralP= BigDecimal.ZERO;;
   BigDecimal total= BigDecimal.ZERO;;
}
