package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName ComponentTrendPartVo.java
 * @Description TODO
 * @createTime 2022年10月23日 15:48
 * @Copyright voc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComponentTrendPartVo {
    String dateStr;
    @Builder.Default
    BigDecimal statistic = BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNum= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal userNumR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal repairStatistic= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal repairStatisticR= BigDecimal.ZERO;

    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.statisticA= CalculatorUtils.avgePerDayNum(this.statistic,new BigDecimal(day));
        this.userNumA=CalculatorUtils.avgePerDayNum(this.userNum,new BigDecimal(day));
//        this.repairStatistic= CalculatorUtils.avgePerDayNum(this.repairStatistic,new BigDecimal(day));
    }
}
