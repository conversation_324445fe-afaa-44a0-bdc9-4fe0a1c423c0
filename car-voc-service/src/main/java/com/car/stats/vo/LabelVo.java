package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName LabelVo.java
 * @Description TODO
 * @createTime 2022年10月16日 18:19
 * @Copyright voc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LabelVo extends DateDayVo  implements Serializable{
    String labelCode;
    String labelAllCode;
    String labelStr;
    String labelAllStr;
    @Builder.Default
    BigDecimal statistic = BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticUp = BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticP= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticR= BigDecimal.ZERO;
    String emotionType;

    public String getLabelAllCode() {
        return CalculatorUtils.getAllTagCode(this.getLabelCode());
    }

    /*public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.statisticA=CalculatorUtils.avgePerDayNum(this.statistic,new BigDecimal(day));
    }*/
}
