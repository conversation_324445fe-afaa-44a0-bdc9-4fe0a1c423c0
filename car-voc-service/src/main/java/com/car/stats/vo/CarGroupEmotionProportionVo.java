package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 * @version 1.0.0
 * @ClassName CarGroupEmotionProportionVo.java
 * @Description TODO
 * @createTime 2022年10月14日 13:36
 * @Copyright voc
 */
@Data
public class CarGroupEmotionProportionVo {
    String carGroupStr;
    String carGroupId;
    BigDecimal proportion = BigDecimal.ZERO;

    BigDecimal positive = BigDecimal.ZERO;
    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal negative = BigDecimal.ZERO;
    BigDecimal negativeP = BigDecimal.ZERO;
    BigDecimal neutral = BigDecimal.ZERO;
    BigDecimal neutralP = BigDecimal.ZERO;
    BigDecimal emotionTotal = BigDecimal.ZERO;
}
