package com.car.stats.vo.wo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ColumnWidth(25)
public class WoLastDealVo extends WoOrderBaseVo implements Serializable {

    @ApiModelProperty(value = "是否后置工单 0否 1是", example = "0")
    @ExcelIgnore
    private String needFlow;

}
