package com.car.stats.vo;

import com.car.voc.annotation.DataDesensitization;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName TopVoiceUsersVo.java
 * @Description TODO
 * @createTime 2023年01月18日 16:28
 * @Copyright voc
 */
@Data
public class TopQualityUsersVo {
    String userId;
    @DataDesensitization(type = DataDesensitization.DesensitizationType.NAME)
    String userName;
    String topicCode;
    String userType;
    String userLevel;
    String isOneId;
    BigDecimal statistic = BigDecimal.ZERO;
    BigDecimal statisticP = BigDecimal.ZERO;
    BigDecimal netWorth = BigDecimal.ZERO;
}
