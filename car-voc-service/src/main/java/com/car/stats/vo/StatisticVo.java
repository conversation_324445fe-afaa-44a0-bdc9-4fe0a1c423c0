package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 * @version 1.0.0
 * @ClassName StatisticVo.java
 * @Description TODO
 * @createTime 2022年12月16日 13:49
 * @Copyright voc
 */
@Data
public class StatisticVo {
    BigDecimal statistic = BigDecimal.ZERO;
    BigDecimal statisticP = BigDecimal.ZERO;
    BigDecimal statisticR = BigDecimal.ZERO;
    BigDecimal satisfactionStatistic = BigDecimal.ZERO;
    BigDecimal closeCount = BigDecimal.ZERO;
    BigDecimal openCount = BigDecimal.ZERO;

}
