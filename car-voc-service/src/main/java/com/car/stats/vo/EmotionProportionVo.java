package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName EmotionProportionVo.java
 * @Description TODO
 * @createTime 2022年10月13日 10:20
 * @Copyright voc
 */
@Data
public class EmotionProportionVo extends EmotionVo{
    /**
     * P   占比
     * R   环比
     * T  好/差评率
     * TR  (好/差评率)环比
     */

    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal negativeP = BigDecimal.ZERO;
    BigDecimal positiveT = BigDecimal.ZERO;
    BigDecimal negativeT = BigDecimal.ZERO;
    BigDecimal neutralP = BigDecimal.ZERO;
    BigDecimal negativeTR = BigDecimal.ZERO;
    BigDecimal positiveTR = BigDecimal.ZERO;
    String dateStr;

    BigDecimal positiveR = BigDecimal.ZERO;
    BigDecimal negativeR = BigDecimal.ZERO;
    BigDecimal neutralR = BigDecimal.ZERO;

    String dateTime;
}
