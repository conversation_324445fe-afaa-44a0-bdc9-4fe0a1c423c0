package com.car.stats.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName HomeBriefingValueVo.java
 * @Description TODO
 * @createTime 2022年10月10日 17:29
 * @Copyright voc
 */
@Data
@ApiModel(value="首页简报数值")
public class HomeBriefingValueVo {
    @ApiModelProperty(value = "总提及量")
    BigDecimal totalMentions = BigDecimal.ZERO;
    @ApiModelProperty(value = "发声用户")
    BigDecimal usersNum = BigDecimal.ZERO;
    @ApiModelProperty(value = "vip用户")
    BigDecimal vipUser = BigDecimal.ZERO;
    @ApiModelProperty(value = "认证车主")
    BigDecimal certifiedOwner = BigDecimal.ZERO;

    @ApiModelProperty(value = "总提及量环比")
    BigDecimal totalMentionsR = BigDecimal.ZERO;
    @ApiModelProperty(value = "发声用户环比")
    BigDecimal usersNumR = BigDecimal.ZERO;
    @ApiModelProperty(value = "vip用户环比")
    BigDecimal vipUserR = BigDecimal.ZERO;
    @ApiModelProperty(value = "认证车主环比")
    BigDecimal certifiedOwnerR = BigDecimal.ZERO;

    List<String> channelStr = new ArrayList<>();

}
