package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName CarEmotionVo.java
 * @Description TODO
 * @createTime 2023年06月06日 10:24
 * @Copyright voc
 */
@Data
public class CarEmotionVo extends EmotionVo{
    String carSeries;
    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal positiveR = BigDecimal.ZERO;


    BigDecimal negativeP = BigDecimal.ZERO;
    BigDecimal negativeR = BigDecimal.ZERO;


    BigDecimal neutralP = BigDecimal.ZERO;
    BigDecimal neutralR = BigDecimal.ZERO;
    BigDecimal totalR = BigDecimal.ZERO;
    BigDecimal totalP = BigDecimal.ZERO;
    BigDecimal emotionValue = BigDecimal.ZERO;
    String showImg;
    public BigDecimal getEmotionValue() {
        return CalculatorUtils.getEmotionalNetWorth(this.positive,this.negative);
    }

    public BigDecimal getPositiveP() {
        return CalculatorUtils.proportion(positive,this.getTotal());
    }

    public BigDecimal getNegativeP() {
        return CalculatorUtils.proportion(negative,this.getTotal());
    }

    public BigDecimal getNeutralP() {
        return CalculatorUtils.proportion(neutral,this.getTotal());
    }
}
