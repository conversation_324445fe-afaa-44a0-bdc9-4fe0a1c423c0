package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @version 1.0.0
 * @ClassName DateTrendEmotionVo.java
 * @Description TODO
 * @createTime 2022年10月16日 15:57
 * @Copyright voc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DateTrendEmotionVo extends EmotionVo implements Serializable {
    String dateStr;
    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal positiveA= BigDecimal.ZERO;
    BigDecimal positiveAR= BigDecimal.ZERO;
    BigDecimal positiveR= BigDecimal.ZERO;

    BigDecimal negativeP= BigDecimal.ZERO;
    BigDecimal negativeA= BigDecimal.ZERO;
    BigDecimal negativeAR= BigDecimal.ZERO;
    BigDecimal negativeR= BigDecimal.ZERO;

    BigDecimal neutralP= BigDecimal.ZERO;
    BigDecimal neutralA= BigDecimal.ZERO;
    BigDecimal neutralAR= BigDecimal.ZERO;
    BigDecimal neutralR= BigDecimal.ZERO;
    BigDecimal total= BigDecimal.ZERO;
    BigDecimal totalP= BigDecimal.ZERO;
    BigDecimal totalR= BigDecimal.ZERO;
    BigDecimal totalA= BigDecimal.ZERO;
    BigDecimal totalAR= BigDecimal.ZERO;
    String dateTime;


    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.positiveA= CalculatorUtils.avgePerDayNum(this.getPositive(),new BigDecimal(day));
        this.negativeA= CalculatorUtils.avgePerDayNum(this.getNegative(),new BigDecimal(day));
        this.neutralA= CalculatorUtils.avgePerDayNum(this.getNeutral(),new BigDecimal(day));
        this.totalA= CalculatorUtils.avgePerDayNum(this.getTotal(),new BigDecimal(day));
    }

    public BigDecimal getPositiveP() {
        return CalculatorUtils.proportion(this.positive,this.total);
    }

    public BigDecimal getNegativeP() {
        return CalculatorUtils.proportion(this.negative,this.total);
    }

    public BigDecimal getNeutralP() {
        return CalculatorUtils.proportion(this.neutral,this.total);
    }

    public BigDecimal getTotalP() {
        return new BigDecimal(100);
    }
}
