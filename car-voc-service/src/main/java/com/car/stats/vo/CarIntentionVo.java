package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName CarIntentionVo.java
 * @Description TODO
 * @createTime 2023年06月06日 10:21
 * @Copyright voc
 */
@Data
public class CarIntentionVo extends IntentionVo{
    String carSeries;
    BigDecimal consultP = BigDecimal.ZERO;
    BigDecimal complaintP= BigDecimal.ZERO;
    BigDecimal praiseP= BigDecimal.ZERO;
    BigDecimal suggestP= BigDecimal.ZERO;
    BigDecimal otherP= BigDecimal.ZERO;



    BigDecimal consultR= BigDecimal.ZERO;
    BigDecimal complaintR= BigDecimal.ZERO;
    BigDecimal praiseR= BigDecimal.ZERO;
    BigDecimal suggestR= BigDecimal.ZERO;
    BigDecimal otherR= BigDecimal.ZERO;
    BigDecimal totalR= BigDecimal.ZERO;

    public BigDecimal getConsultP() {
        return CalculatorUtils.proportion(consult,this.total);
    }

    public BigDecimal getComplaintP() {
        return CalculatorUtils.proportion(complaint,this.total);
    }

    public BigDecimal getPraiseP() { return CalculatorUtils.proportion(praise,this.total);}

    public BigDecimal getSuggestP() {
        return CalculatorUtils.proportion(suggest,this.total);
    }

    public BigDecimal getOtherP() {
        return CalculatorUtils.proportion(other,this.total);
    }
}
