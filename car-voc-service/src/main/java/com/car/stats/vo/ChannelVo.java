package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName ChannelVo.java
 * @Description TODO
 * @createTime 2022年10月20日 15:33
 * @Copyright voc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelVo {
    String channelStr;
    String dateStr;
    String channelId;
    @Builder.Default
    BigDecimal statistic = BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticA= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticAR= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticP= BigDecimal.ZERO;
    @Builder.Default
    BigDecimal statisticR= BigDecimal.ZERO;

    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.statisticA= CalculatorUtils.avgePerDayNum(this.statistic,new BigDecimal(day));
    }
}
