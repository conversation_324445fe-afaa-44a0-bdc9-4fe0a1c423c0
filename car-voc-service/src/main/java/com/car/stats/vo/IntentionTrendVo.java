package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionTrendVo.java
 * @Description TODO
 * @createTime 2022年10月13日 18:18
 * @Copyright voc
 */
@Data
public class IntentionTrendVo {
    String dataStr;
    BigDecimal total = BigDecimal.ZERO;;
    BigDecimal totalR = BigDecimal.ZERO;;
    BigDecimal totalP = BigDecimal.ZERO;;
    BigDecimal consul = BigDecimal.ZERO;;
    BigDecimal consulR = BigDecimal.ZERO;;
    BigDecimal complaint = BigDecimal.ZERO;;
    BigDecimal complaintR = BigDecimal.ZERO;;
    BigDecimal praise = BigDecimal.ZERO;;
    BigDecimal praiseR = BigDecimal.ZERO;;
    String dateTime;
    BigDecimal complain;
    BigDecimal complainR;
    BigDecimal userCount;
    BigDecimal userCountP;
    BigDecimal userCountR;
    BigDecimal authUser;
    BigDecimal vipUser;
    BigDecimal average;

    public void setAveragePerDay(Integer dateUnit,String endDate,Long dy) {
                BigDecimal day=new BigDecimal(dy);
            this.consul= CalculatorUtils.avgePerDayNum(consul,day);
            this.total=CalculatorUtils.avgePerDayNum(total,day);
            this.complaint=CalculatorUtils.avgePerDayNum(complaint,day);
            this.praise=CalculatorUtils.avgePerDayNum(praise,day);
    }
}
