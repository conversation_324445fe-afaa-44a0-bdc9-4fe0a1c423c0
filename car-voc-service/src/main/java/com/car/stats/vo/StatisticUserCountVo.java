package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName StatisticUserCountVo.java
 * @Description TODO
 * @createTime 2022年12月16日 13:57
 * @Copyright voc
 */
@Data
public class StatisticUserCountVo {
    BigDecimal statistic = BigDecimal.ZERO;;
    BigDecimal statisticP = BigDecimal.ZERO;;
    BigDecimal statisticR = BigDecimal.ZERO;;


    BigDecimal userCount = BigDecimal.ZERO;;
    BigDecimal userCountP = BigDecimal.ZERO;
    BigDecimal userCountR = BigDecimal.ZERO;


    BigDecimal userNum  = BigDecimal.ZERO;
    BigDecimal userNumP = BigDecimal.ZERO;
    BigDecimal userNumR = BigDecimal.ZERO;
}
