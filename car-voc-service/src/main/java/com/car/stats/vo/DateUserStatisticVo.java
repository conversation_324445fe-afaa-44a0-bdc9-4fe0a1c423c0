package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName DateUserStatisticVo.java
 * @Description TODO
 * @createTime 2022年10月20日 09:54
 * @Copyright voc
 */
@Data
public class DateUserStatisticVo {
    String dateStr;
    BigDecimal statistic = BigDecimal.ZERO;
    BigDecimal statisticA= BigDecimal.ZERO;
    BigDecimal statisticR= BigDecimal.ZERO;
    BigDecimal statisticAR= BigDecimal.ZERO;
    BigDecimal userNum= BigDecimal.ZERO;
    BigDecimal userNumA= BigDecimal.ZERO;
    BigDecimal userNumR= BigDecimal.ZERO;
    BigDecimal userNumAR= BigDecimal.ZERO;


    /*public BigDecimal getStatistic() {
        return statistic==null?new BigDecimal(0):statistic;
    }

    public BigDecimal getUserNum() {
        return userNum==null?new BigDecimal(0):userNum;
    }
*/
    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.statisticA= CalculatorUtils.avgePerDayNum(this.statistic,new BigDecimal(day));
        this.userNumA= CalculatorUtils.avgePerDayNum(this.userNum,new BigDecimal(day));
    }
}
