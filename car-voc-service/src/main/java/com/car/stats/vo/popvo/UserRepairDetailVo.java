package com.car.stats.vo.popvo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class UserRepairDetailVo {
    @ApiModelProperty(name = "维修类型")
    private String repairType;
    @ApiModelProperty(name = "结算单号")
    private String balanceNo;
    @ApiModelProperty(name = "送修症状")
    private String troubleDesc;
    @ApiModelProperty(name = "结算时间")
    private String balanceTime;
    @ApiModelProperty(name = "送修时间")
    private String createDate;
    @ApiModelProperty(name = "行驶里程")
    private BigDecimal inMileage;
    @ApiModelProperty(name = "维修类型代码")
    private String repairTypeCode;

    @ApiModelProperty(name = "跟进次数")
    private String followCount;

    @ApiModelProperty(name = "服务项目")
    private List<String> serviceProject;

    @ApiModelProperty(name = "更换零件")
    private List<RepairPartVo> partList;

    @ApiModelProperty(name = "经销商信息")
    private DealerVo dealer;
}
