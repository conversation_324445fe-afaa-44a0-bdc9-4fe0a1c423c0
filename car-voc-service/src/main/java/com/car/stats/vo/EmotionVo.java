package com.car.stats.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @version 1.0.0
 * @ClassName EmotionVo.java
 * @Description TODO
 * @createTime 2023年06月05日 16:52
 * @Copyright voc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmotionVo implements Serializable {

    BigDecimal positive = BigDecimal.ZERO;


    BigDecimal negative = BigDecimal.ZERO;


    BigDecimal neutral = BigDecimal.ZERO;

    BigDecimal total = BigDecimal.ZERO;
    BigDecimal totalR = BigDecimal.ZERO;



}
