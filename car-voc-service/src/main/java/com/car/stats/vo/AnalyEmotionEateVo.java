package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName AnalyEmotionEateVo.java
 * @Description TODO
 * @createTime 2022年10月21日 13:57
 * @Copyright voc
 */
@Data
public class AnalyEmotionEateVo {
    String labelStr;
    String labelCode;
    BigDecimal statistic = BigDecimal.ZERO;
    BigDecimal positive = BigDecimal.ZERO;
    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal negative = BigDecimal.ZERO;
    BigDecimal negativeP = BigDecimal.ZERO;
    BigDecimal positiveR = BigDecimal.ZERO;
    BigDecimal negativeR = BigDecimal.ZERO;
    BigDecimal statisticR = BigDecimal.ZERO;
}
