package com.car.stats.vo.popvo;

import com.car.stats.vo.ChannelVo;
import com.car.stats.vo.EmotionProportionVo;
import com.car.stats.vo.EmotionVo;
import com.car.stats.vo.HighHotWordsVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName UserDetailVo.java
 * @Description TODO
 * @createTime 2022年11月02日 14:33
 * @Copyright voc
 */
@Data
public class UserDetailVo extends EmotionVo {
    String userName;
    String address;
    String userId;
    String userType;
    Integer isOneId;
    Integer userLevel;
    List<UserCarVo> cars;
    Integer sex;
    BigDecimal netWorth;
    BigDecimal publishCount;
    BigDecimal channelCount;

    BigDecimal statistic;
    BigDecimal consult;
    BigDecimal complaint;
    BigDecimal praise;
    BigDecimal suggest;
    EmotionProportionVo emotionProp;
    List<ChannelVo> channelDis;
    List<HighHotWordsVo> hotWordsVos;

    public BigDecimal getConsult() {
        return consult==null?new BigDecimal(0):consult;
    }

    public BigDecimal getComplaint() {
        return complaint==null?new BigDecimal(0):complaint;
    }

    public BigDecimal getPraise() {
        return praise==null?new BigDecimal(0):praise;
    }
    public BigDecimal getSuggest() {
        return suggest==null?new BigDecimal(0):suggest;
    }
    BigDecimal positiveP;
    BigDecimal negativeP;
    BigDecimal neutralP;

}
