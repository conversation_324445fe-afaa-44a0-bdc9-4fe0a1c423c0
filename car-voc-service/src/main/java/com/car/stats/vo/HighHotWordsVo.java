package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @version 1.0.0
 * @ClassName HighHotWordsVo.java
 * @Description TODO
 * @createTime 2022年10月16日 09:52
 * @Copyright voc
 */
@Data
public class HighHotWordsVo {
    String keyword;
    String emotionType;
    BigDecimal statistic;
    BigDecimal statisticP;
    BigDecimal userCount;
    BigDecimal userCountP;
    BigDecimal positive;
    BigDecimal negative;
    BigDecimal neutral;
    BigDecimal soaringRate;
    Integer weight;

    public BigDecimal getSoaringRate() {
        return soaringRate == null ? new BigDecimal(0) : soaringRate;
    }

    public BigDecimal getStatistic() {
        if (positive == null || negative == null || neutral == null) {
            return statistic;
        }
        // 默认值为 positive，标识为 1
        BigDecimal max = positive;
        int weight = 1;
        // 如果 negative 更大，更新 max 和 weight
        if (negative.compareTo(max) > 0) {
            max = negative;
            weight = -1;
        }
        // 如果 neutral 更大，更新 max 和 weight
        if (neutral.compareTo(max) > 0) {
            max = neutral;
            weight = 0;
        }
        // 最终将最大值的权重赋值给 this.weight
        this.weight = weight;
        return max;
    }

    String hotWordStr;

    public String getHotWordStr() {
        return this.keyword + "(" + getStatistic().longValue() + ")";
    }

}
