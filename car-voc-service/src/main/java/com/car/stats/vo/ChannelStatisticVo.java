package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName ChannelStatisticVo.java
 * @Description TODO
 * @createTime 2022年10月13日 14:10
 * @Copyright voc
 */
@Data
public class ChannelStatisticVo {
    String dataSource;
    String dataSourceStr;
    BigDecimal statistic = BigDecimal.ZERO;
    BigDecimal statisticR = BigDecimal.ZERO;
    String dateStr;


    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.statistic= CalculatorUtils.avgePerDayNum(this.statistic,new BigDecimal(day));

    }
}
