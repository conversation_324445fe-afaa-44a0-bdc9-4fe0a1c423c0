package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionRatioVo.java
 * @Description TODO
 * @createTime 2022年10月14日 09:35
 * @Copyright voc
 */
@Data
public class IntentionRatioVo {
    BigDecimal consult = BigDecimal.ZERO;
    BigDecimal consultP = BigDecimal.ZERO;
    BigDecimal consultR = BigDecimal.ZERO;
    BigDecimal complaint = BigDecimal.ZERO;
    BigDecimal complaintP = BigDecimal.ZERO;
    BigDecimal complaintR = BigDecimal.ZERO;
    BigDecimal praise = BigDecimal.ZERO;
    BigDecimal praiseP = BigDecimal.ZERO;
    BigDecimal praiseR = BigDecimal.ZERO;
    BigDecimal suggest = BigDecimal.ZERO;
    BigDecimal suggestP = BigDecimal.ZERO;
    BigDecimal suggestR = BigDecimal.ZERO;
    BigDecimal other = BigDecimal.ZERO;
    BigDecimal otherP = BigDecimal.ZERO;
    BigDecimal otherR = BigDecimal.ZERO;
    BigDecimal total = BigDecimal.ZERO;
}
