package com.car.stats.vo.risk;

import com.car.voc.common.util.CalculatorUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * @version 1.0.0
 * @ClassName RiskPointAggVo.java
 * @Description TODO
 * @createTime 2022年11月23日 14:04
 * @Copyright voc
 */
@Data
public class RiskPointAggVo implements Serializable {
    String riskPointStr;
    String riskId;
    String riskName;
    String topicCode;
    String labelAllCode;
    Date publishDate;
    String grade;
    String statisticType;
    @ApiModelProperty(value = "走势",example = "1，-1，0为NEW,为null时为相同")
    Integer rise;
    BigDecimal riskIndex;
    BigDecimal negativeNum;
    BigDecimal totalNum;
    String riskGradeTag;
    String brandCode;
    String createTime;
    String endDate;
    String startDate;
    Integer dateUnit;



    public String getLabelAllCode() {
        if (topicCode==null){
            return "";
        }
        return CalculatorUtils.getAllTagCode(topicCode);
    }
}
