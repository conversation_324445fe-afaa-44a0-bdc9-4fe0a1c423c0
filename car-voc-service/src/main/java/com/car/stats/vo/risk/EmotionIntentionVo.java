package com.car.stats.vo.risk;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName EmotionIntentionVo.java
 * @Description TODO
 * @createTime 2023年02月20日 15:15
 * @Copyright voc
 */
@Data
public class EmotionIntentionVo {
    BigDecimal negative;
    BigDecimal complaint;
    BigDecimal grumble;
    String dateStr;
    String statisticType;
    Date publishDate;
    List<String> sections;
    String year;
    String uni;
    public String getDateStr() {
        if (dateStr.length()>9){
            return this.dateStr;
        }
        String year;
        if (dateStr.contains("-")){
            year=dateStr.substring(0,dateStr.indexOf("-"));
            uni=dateStr.substring(dateStr.indexOf("-")+1);
            String zero=uni.substring(0,1);
            if ("0".equals(zero)){
                uni=uni.replaceFirst("0","");
            }
            return year+"-"+uni;

        }else {
            year=dateStr;
            return year;
        }

    }
    /* public List<String> getSections() {
        if (statisticType!=null&&!statisticType.equals("d")){
            String year=dateStr.substring(0,dateStr.indexOf("-"));
            Integer uni= Integer.valueOf(dateStr.substring(dateStr.indexOf("-")+1));
            List<DateTime> dates=new ArrayList<>();
            if (statisticType.equals("q")){
//                dates= DateUtil.rangeToList(d)
            }
        }

        return sections;
    }

    public String getYear() {
        return dateStr.substring(0,dateStr.indexOf("-"));
    }

    public String getUni() {
        return dateStr.substring(dateStr.indexOf("-")+1);
    }*/
}
