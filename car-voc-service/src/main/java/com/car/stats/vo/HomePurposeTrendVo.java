package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @version 1.0.0
 * @ClassName HomePurposeTrendVo.java
 * @Description TODO
 * @createTime 2022年10月11日 14:21
 * @Copyright voc
 */

@Data
@ApiModel(value = "首页意图趋势")
@NoArgsConstructor
public class HomePurposeTrendVo {
    @ApiModelProperty(value = "时间")
    String dateStr;

    @ApiModelProperty(value = "提及量环比")

    BigDecimal intentionR = BigDecimal.ZERO;

    BigDecimal intentionP= BigDecimal.ZERO;

    BigDecimal intentionTotal= BigDecimal.ZERO;

    @ApiModelProperty(value = "提及量")
    BigDecimal intention= BigDecimal.ZERO;

    BigDecimal intentionA= BigDecimal.ZERO;

    BigDecimal intentionAR= BigDecimal.ZERO;

    @ApiModelProperty(value = "用户数")

    BigDecimal userCount= BigDecimal.ZERO;

    BigDecimal userCountP= BigDecimal.ZERO;

    BigDecimal userTotal= BigDecimal.ZERO;

    BigDecimal userCountA= BigDecimal.ZERO;

    BigDecimal userCountAR= BigDecimal.ZERO;

    @ApiModelProperty(value = "用户数环比")
    BigDecimal userCountR= BigDecimal.ZERO;
    @ApiModelProperty(value = "类型  用户/提及量", hidden = true)
    String busType;

    /*public HomePurposeTrendVo(String dateStr, BigDecimal intentionR, BigDecimal intention) {
        this.dateStr = dateStr;
        this.intentionR = intentionR;
        this.intention = intention;
    }*/


    /*public BigDecimal getIntention() {
        return intention == null ? new BigDecimal(0) : intention;
    }

    public BigDecimal getUserCount() {
        return userCount == null ? new BigDecimal(0) : userCount;
    }*/

   /* public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.intentionA = CalculatorUtils.avgePerDayNum(this.getIntention(), new BigDecimal(day));
        this.userCountA = CalculatorUtils.avgePerDayNum(this.getUserCount(), new BigDecimal(day));
    }*/

    /*public BigDecimal getIntentionP() {
        return CalculatorUtils.proportion(this.intention, intentionTotal);
    }

    public BigDecimal getUserCountP() {
        return CalculatorUtils.proportion(this.userCount, this.userTotal);
    }*/

    public HomePurposeTrendVo(String dateStr) {
        this.dateStr = dateStr;
    }

}
