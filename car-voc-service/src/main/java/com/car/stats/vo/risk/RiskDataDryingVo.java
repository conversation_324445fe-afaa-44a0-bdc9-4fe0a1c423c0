package com.car.stats.vo.risk;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.car.voc.vo.risk.RiskAllTypesVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 
 * @version 1.0.0
 * @ClassName RiskDataDryingVo.java  对比 contrast
 * @Description TODO
 * @createTime 2023年02月16日 15:08
 * @Copyright voc
 */
@Data
public class RiskDataDryingVo extends RiskAllTypesVo {
    @ApiModelProperty(value="响应周期")
    String responsePeriod;
    @ApiModelProperty(value="风险点总计、处理前、后")
    Map<String,DataDryingContrastVo> contrasts;
    public String getResponsePeriod() {
        return DateUtil.formatBetween(DateUtil.between(super.getAuditTime(),super.getProcessTime(), DateUnit.MS));
    }
}
