package com.car.stats.vo.es;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * 
 * @version 1.0.0
 * @ClassName AppRecordVo.java
 * @Description TODO
 * @createTime 2022年11月09日 17:11
 * @Copyright voc
 */
@Data
public class AppRecordVo {
    String artTitle;
    String artContent;
    String artCreateTime;
    Long viewPersons;
    Long commCounts;
    Long likeCounts;
    String url;
    String commContent;

    public String getArtContent() {
        if (StrUtil.isNotBlank(this.artContent)){
            return artContent;

        }else if (StrUtil.isNotBlank(this.getCommContent())){
            return commContent;
        }else {
            return artContent;

        }
    }
}
