package com.car.stats.vo.risk;

import cn.hutool.core.util.NumberUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 *
 * @version 1.0.0
 * @ClassName ComplaintUserVo.java
 * @Description TODO
 * @createTime 2022年12月21日 11:29
 * @Copyright voc
 */
@Data
public class ComplaintUserVo {
    String id;
    String userId;
    String userName;
    BigDecimal complaint;
    BigDecimal soundNum;
    BigDecimal channel;
    BigDecimal emotionNum;
    BigDecimal riskIndex;
    BigDecimal positive;
    BigDecimal negative;
    String riskGradeTag;
    String brandCode;
    String riskLevel;
    String createTime;
    String endDate;
    String startDate;
    Integer dateUnit;
    Date publishDate;
    String statisticType;
    public BigDecimal getEmotionNum() {
        return NumberUtil.round(NumberUtil.mul(this.emotionNum,100),2,RoundingMode.HALF_UP);
    }
}
