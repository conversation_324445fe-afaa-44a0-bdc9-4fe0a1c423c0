package com.car.stats.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName CarGroupRatoVo.java
 * @Description TODO
 * @createTime 2022年10月14日 11:39
 * @Copyright voc
 */
@Data
public class CarGroupIntentionsProportionVo {
    String carGroupStr;

    BigDecimal proportion = BigDecimal.ZERO;
    BigDecimal consult = BigDecimal.ZERO;
    BigDecimal consultP = BigDecimal.ZERO;
    BigDecimal complaint = BigDecimal.ZERO;
    BigDecimal complaintP = BigDecimal.ZERO;
    BigDecimal praise = BigDecimal.ZERO;
    BigDecimal praiseP = BigDecimal.ZERO;
    BigDecimal other = BigDecimal.ZERO;
    BigDecimal otherP = BigDecimal.ZERO;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    BigDecimal intentionsTotal = BigDecimal.ZERO;
}
