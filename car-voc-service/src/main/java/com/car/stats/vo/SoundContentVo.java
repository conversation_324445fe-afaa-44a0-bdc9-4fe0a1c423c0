package com.car.stats.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.stats.vo.popvo.UserSoundVo;
import com.car.voc.annotation.DataDesensitization;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @version 1.0.0
 * @ClassName UserInfoVo.java
 * @Description TODO
 * @createTime 2022年10月31日 20:07
 * @Copyright voc
 */
@Data
//@EqualsAndHashCode(of = {"userId","publishTime"})
public class SoundContentVo extends UserSoundVo implements Serializable {
    @ExcelProperty(value = "姓名")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.NAME})
    String userName;
    @ExcelProperty(value = "用户id")
    String userId;
    @ExcelIgnore
    Integer userLevel;
    @ExcelIgnore
    String userType;
    @ExcelProperty(value = "省份")
    String province;
    @ExcelIgnore
    String channelId;
    @ExcelProperty(value = "渠道")
    String channelStr;
    @ExcelIgnore
    String viewCounts;
    @ExcelIgnore
    String likeCounts;
    @ExcelIgnore
    String commCounts;
    @ExcelIgnore
    String tagType;

    public SoundContentVo(EsDataSentenceVocs content) {
        super(content);
        userName=content.getDisplayName();
        userId=content.getOneId();
        userLevel=content.getUserLevel();
        userType=content.getUserType();
        province=content.getProvince();
        channelId=content.getChannelId();
        viewCounts=content.getViewCounts();
        likeCounts=content.getLikeCounts();
        commCounts=content.getCommCounts();
        this.tagType=content.getTagType();

    }

    public SoundContentVo() {
    }


}
