package com.car.stats.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName VocOverBriefingValueVo.java
 * @Description TODO
 * @createTime 2022年10月13日 15:45
 * @Copyright voc
 */

@Data
@ApiModel(value="首页简报数值")
public class VocOverBriefingValueVo {
    @ApiModelProperty(value = "总提及量")
    BigDecimal totalMentions = BigDecimal.ZERO;
    BigDecimal totalMentionsUp= BigDecimal.ZERO;
    @ApiModelProperty(value = "发声用户")
    BigDecimal usersNum= BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道数")
    BigDecimal channelNum= BigDecimal.ZERO;

    @ApiModelProperty(value = "总提及量环比")
    BigDecimal totalMentionsR= BigDecimal.ZERO;
    @ApiModelProperty(value = "发声用户环比")
    BigDecimal usersNumR= BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道数环比")
    BigDecimal channelNumR= BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道top")
    List<String> channelTopStr =new ArrayList<>();

    public BigDecimal getTotalMentions() {
        if(this.totalMentions == null){
            this.totalMentions = new BigDecimal(0);
        }
        return totalMentions;
    }
}
