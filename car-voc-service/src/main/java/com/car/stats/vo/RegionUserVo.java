package com.car.stats.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName RegionUserVo.java
 * @Description TODO
 * @createTime 2022年10月19日 19:42
 * @Copyright voc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionUserVo extends EmotionProportionVo{
    /**
     * 区域编码
     */
    String regionCode;
    /**
     * 区域名称
     */
    String regionStr;
    /**
     * 区域提及量
     */
    @Builder.Default
    BigDecimal statistic = BigDecimal.ZERO;
    /**
     * 区域占比
     */
    @Builder.Default
    BigDecimal statisticP= BigDecimal.ZERO;
    /**
     * 区域环比
     */
    @Builder.Default
    BigDecimal statisticR= BigDecimal.ZERO;
    /**
     * 区域用户数
     */
    @Builder.Default
    BigDecimal userNum= BigDecimal.ZERO;
    /**
     * 区域用户数环比
     */
    @Builder.Default
    BigDecimal userNumR= BigDecimal.ZERO;
    /**
     * 区域用户数占比
     */
    @Builder.Default
    BigDecimal userNumP= BigDecimal.ZERO;
}
