package com.car.stats.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName HomeIntentionReportVo.java
 * @Description TODO
 * @createTime 2022年10月11日 14:05
 * @Copyright voc
 */
@Data
@ApiModel(value="首页意图简报")
public class HomeIntentionReportVo {
    @ApiModelProperty(value = "提及量占比")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    BigDecimal intentionP = BigDecimal.ZERO;;
    @ApiModelProperty(value = "意图")
    String purpose;
    @ApiModelProperty(value = "提及量环比")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    BigDecimal intentionR  = BigDecimal.ZERO;
    @ApiModelProperty(value = "提及量")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    BigDecimal intention = BigDecimal.ZERO;;

    @ApiModelProperty(value = "用户数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    BigDecimal userCount = BigDecimal.ZERO;;
    @ApiModelProperty(value = "用户数环比")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    BigDecimal userCountR = BigDecimal.ZERO;;

    @ApiModelProperty(value = "意图趋势")
    List<HomePurposeTrendVo> purposeTrends;
    @ApiModelProperty(value = "提及Top")
    List<TopTopicVo> mentionTop;
    @ApiModelProperty(value = "飙升Top")
    List<TopTopicVo> soarTop;


}
