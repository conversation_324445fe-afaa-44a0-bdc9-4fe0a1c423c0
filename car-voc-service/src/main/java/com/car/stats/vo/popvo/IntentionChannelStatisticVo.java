package com.car.stats.vo.popvo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionChannelStatisticVo.java
 * @Description TODO
 * @createTime 2022年11月02日 19:39
 * @Copyright voc
 */
@Data
public class IntentionChannelStatisticVo {
    String channelId;
    String channelStr;
    BigDecimal statistic;

    public IntentionChannelStatisticVo(String keyAsString, long docCount, String channelStr) {
        channelId=keyAsString;
        statistic=new BigDecimal(docCount);
        this.channelStr=channelStr;
    }
}
