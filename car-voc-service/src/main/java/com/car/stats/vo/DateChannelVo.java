package com.car.stats.vo;

import com.car.voc.common.util.CalculatorUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DateChannelVo.java
 * @Description TODO
 * @createTime 2022年10月13日 11:37
 * @Copyright voc
 */
@Data
public class DateChannelVo {
    String dateTime;
    List<ChannelStatisticVo> channels =new ArrayList<>();
    BigDecimal total = BigDecimal.ZERO;
    BigDecimal totalP = BigDecimal.ZERO;
    BigDecimal day = BigDecimal.ZERO;
    public DateChannelVo(String toDateStr, List<ChannelStatisticVo> datelists) {
        this.dateTime=toDateStr;
        this.channels=datelists;
    }

    public DateChannelVo() {
    }

    public void setAveragePerDay(Integer dateUnit, String endDate, Long day) {
        this.total= CalculatorUtils.avgePerDayNum(this.total,new BigDecimal(day));

    }

    public BigDecimal getTotal() {
        return total==null?new BigDecimal(0):total;
    }
}
