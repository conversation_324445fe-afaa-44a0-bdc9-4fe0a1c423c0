package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName DateEmotionTotalVo.java
 * @Description TODO
 * @createTime 2022年10月14日 17:29
 * @Copyright voc
 */
@Data
public class DateEmotionTotalVo {

    BigDecimal positive = BigDecimal.ZERO;
    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal negative = BigDecimal.ZERO;
    BigDecimal neutral = BigDecimal.ZERO;
    BigDecimal neutralP = BigDecimal.ZERO;
    BigDecimal negativeP = BigDecimal.ZERO;
    List<TrendEmotionVo> trends;
}
