package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 
 * @version 1.0.0
 * @ClassName UserTypeProportionVo.java
 * @Description TODO
 * @createTime 2022年10月16日 10:37
 * @Copyright voc
 */
@Data
public class UserTypeProportionVo {
    BigDecimal vipUser = BigDecimal.ZERO;
    BigDecimal nvipUser = BigDecimal.ZERO;
    BigDecimal authUser = BigDecimal.ZERO;
    BigDecimal nauthUser = BigDecimal.ZERO;
    BigDecimal vipUserP = BigDecimal.ZERO;
    BigDecimal nvipUserP = BigDecimal.ZERO;
    BigDecimal authUserP = BigDecimal.ZERO;
    BigDecimal nauthUserP = BigDecimal.ZERO;
    BigDecimal cdpP = BigDecimal.ZERO;
    BigDecimal cdpNum = BigDecimal.ZERO;
    BigDecimal ncdpP = BigDecimal.ZERO;
    BigDecimal ncdpNum = BigDecimal.ZERO;
}
