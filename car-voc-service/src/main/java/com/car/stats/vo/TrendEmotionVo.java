package com.car.stats.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @version 1.0.0
 * @ClassName TrendEmotionVo.java
 * @Description TODO
 * @createTime 2022年10月14日 17:16
 * @Copyright voc
 */
@Data
public class TrendEmotionVo {
    String dateStr;
    BigDecimal positive = BigDecimal.ZERO;
    BigDecimal positiveP = BigDecimal.ZERO;
    BigDecimal negative = BigDecimal.ZERO;
    BigDecimal negativeP = BigDecimal.ZERO;
    BigDecimal neutral = BigDecimal.ZERO;
    BigDecimal neutralP = BigDecimal.ZERO;;
    BigDecimal total = BigDecimal.ZERO;
}
