package com.car.stats.vo.risk;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.car.stats.vo.HighHotWordsVo;
import com.car.voc.common.util.CalculatorUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName RiskPointAggVo.java
 * @Description TODO
 * @createTime 2022年11月23日 14:04
 * @Copyright voc
 */
@Data
public class RiskExportResultVo implements Serializable {


    @ExcelProperty(value = "风险年度",order = 9)
    String dateYear;
//    @ExcelProperty(value = "风险月度",order = 8)
    @ExcelIgnore
    String  dateMonth;
//    @ExcelProperty(value = "风险月周",order = 7)
    @ExcelIgnore
    String dateWeek;

    @ExcelProperty(value = "风险Id",order = 1)

    String riskId;
//    @ExcelProperty(value = "风险等级",order = 2)
    @ExcelIgnore
    String riskLevel;
    @ExcelProperty(value = "风险问题",order = 3)
    String topicCodeName;
    @ExcelProperty(value = "品牌",order = 3)
    String brandName;
    @ExcelIgnore
    String brandCode;

    @ExcelProperty(value = "问题周期",order = 4)
    String statisticTypeStr;


    @ExcelProperty(value = "问题时间",order = 5)
    String riskDate;


    @ExcelIgnore
    String topicCode;
    @ExcelIgnore
    String timeStr;
    @ExcelIgnore
    String labelAllCode;
    @ExcelIgnore
    Date publishDate;
    @ExcelIgnore
    Date createTime;
    @ExcelIgnore
    String grade;
    @ExcelIgnore
    String statisticType;
//    @ExcelProperty(value = "问题综合指数",order = 2)
    @ExcelIgnore
    BigDecimal riskIndex;
//    @ExcelProperty(value = "负面提及量",order = 3)
    @ExcelIgnore
    BigDecimal negativeNum;
    @ExcelProperty(value = "用户数",order = 3)
    Long userNum;
    @ExcelProperty(value = "提及量",order = 3)
    BigDecimal totalNum;
    @ExcelIgnore
    String riskGradeTag;

    public String getLabelAllCode() {
        return CalculatorUtils.getAllTagCode(topicCode);
    }

    @ApiModelProperty(value = "开始日期",example = "2022-06-01 00:00:00")
    @ExcelIgnore
    String startDate;
    @ApiModelProperty(value = "结束日期",example = "2022-10-01 00:00:00")
    @ExcelIgnore
    String endDate;
    @ApiModelProperty(value = "日期单位(0为周，1为月，2为季，3为年度，-1为自定义)",example = "1")
    @ExcelIgnore
    Integer dateUnit;

//    @ExcelProperty(value = "涉及车型",order = 6)
    @ExcelIgnore
    String carSeries;


    public String getRiskDate() {
        return this.getStartDate()+"-"+this.getEndDate();
    }
}
