package com.car.stats.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName TrendLabelVo.java
 * @Description TODO
 * @createTime 2022年10月17日 09:52
 * @Copyright voc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrendLabelVo implements Serializable {
    String dateStr;
    @Builder.Default
    List<LabelVo> list =new ArrayList<>();
}
