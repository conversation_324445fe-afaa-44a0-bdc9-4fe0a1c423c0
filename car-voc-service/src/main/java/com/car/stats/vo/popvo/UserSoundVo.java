package com.car.stats.vo.popvo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.util.CalculatorUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * @version 1.0.0
 * @ClassName UserSoundVo.java
 * @Description TODO
 * @createTime 2022年10月30日 20:56
 * @Copyright voc
 */
@Data
@ColumnWidth(25)
public class UserSoundVo implements Serializable {
    @ExcelProperty(value = "声音片段内容",order = 4)
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    String sound;
    @ExcelProperty(value = "原文内容",order = 4)
    String content;
    @ExcelIgnore
    String isOneId;
    @ExcelIgnore
    String isOneIdStr;
    @ExcelProperty(value = "声音ID",order = 1)
    String id;
    @ExcelIgnore
    String soundType;
    @ExcelIgnore
    String contentType;
    @ExcelIgnore
    String carSeriesCode;


    @ExcelProperty(value = "车系",order = 3)
    String  carSeriesName;

    @ExcelIgnore
    String brandCode;
    @ExcelProperty(value = "品牌",order = 2)
    String brandName;

    @ExcelProperty(value = "声音一级标签",order = 8)
    String firstDimension;
    @ExcelProperty(value = "声音三级标签",order = 10)
    String thirdDimension;
    @ExcelProperty(value = "声音四级标签",order = 11)
    String topic;
    @ExcelProperty(value = "声音二级标签",order = 9)
    String secondDimension;
    @ExcelIgnore
    String firstDimensionCode;
    @ExcelIgnore
    String thirdDimensionCode;
    @ExcelIgnore
    String topicCode;
    @ExcelIgnore
    String secondDimensionCode;
    @ExcelProperty(value = "原声ID",order = 12)
    String contentId;
    @ExcelProperty(value = "情感",order = 5)
    String emotion;
    @ExcelProperty(value = "意图",order = 6)
    String intention;
    @ExcelProperty(value = "发声时间",order = 18)
    String publishTime;
    @ExcelProperty(value = "工单编号",order = 19)
    String code;
    @ExcelProperty(value = "会话ID",order = 19)
    private String sessionId;
    @ExcelProperty(value = "通话ID",order = 19)
    private String callId;
    @ExcelProperty(value = "手机号",order = 19)
    @DataDesensitization(type = DataDesensitization.DesensitizationType.PHONE)
    private String phone;
    @ExcelProperty(value = "vin",order = 19)
    @DataDesensitization(type = DataDesensitization.DesensitizationType.VIN)
    private String vin;
    @ExcelProperty(value = "热词",order = 19)
    private String emotionKeyword;


    @ExcelIgnore
    String url;
    @ExcelProperty(value = "严重性等级",order = 19)
    String level;
    @ExcelIgnore
    Integer sex;

    public UserSoundVo(EsDataSentenceVocs content) {
        firstDimension=content.getFirstDimension();
        secondDimension=content.getSecondDimension();
        thirdDimension=content.getThreeDimension();
        topic=content.getTopic();

        firstDimensionCode=content.getFirstDimensionCode();
        secondDimensionCode=content.getSecondDimensionCode();
        thirdDimensionCode=content.getThreeDimensionCode();
        topicCode=content.getTopicCode();


        contentId=content.getContentId();
        sound=content.getSentence();
        emotion=content.getDimensionEmotion();
        intention=content.getIntentionType();
        publishTime=content.getPublishTime();
        code =content.getCode();
        sessionId=content.getSessionId();
        callId =content.getCallId();
        phone =content.getPhone();
        vin =content.getVin();
        url=content.getUrl();
        this.isOneId=content.getIsOneId();
        this.contentType=content.getContentType();
        this.id=content.getId();
        this.level=content.getLevel();
        this.emotionKeyword=content.getEmotionKeyword();
        this.brandCode=content.getBrandCode();
    }

    public UserSoundVo() {
    }

    public String getIsOneIdStr() {
        return CalculatorUtils.getIsOne(this.isOneId);
    }


}
