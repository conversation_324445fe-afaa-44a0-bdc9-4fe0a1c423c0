package com.car.stats.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @ClassName TrendChannelVo.java
 * @Description TODO
 * @createTime 2022年10月20日 15:55
 * @Copyright voc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrendChannelVo {
    String dateStr;
    @Builder.Default
    List<ChannelVo> channelVos = new ArrayList<>();


    public TrendChannelVo(Map.Entry<String, List<ChannelVo>> entry) {
        this.dateStr = entry.getKey();
        this.setChannelVos(entry.getValue());
    }
}
