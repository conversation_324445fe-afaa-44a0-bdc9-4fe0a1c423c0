package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocUser;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.vo.UserChannelPubNumVo;

import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName IDwsVocUserService.java
 * @Description TODO
 * @createTime 2022年11月02日 10:08
 * @Copyright voc
 */
public interface IDwsVocUserService extends IService<DwsVocUser> {
    List<UserChannelPubNumVo> userChannelPubNum(LabelDetailFilterModel model);
    List<UserChannelPubNumVo> userChannelPubNumUserIds(Set<String> userId);

}
