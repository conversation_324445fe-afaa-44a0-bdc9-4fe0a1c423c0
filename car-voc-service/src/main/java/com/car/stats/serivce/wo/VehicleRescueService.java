package com.car.stats.serivce.wo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.ProportionCarSeriesVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface VehicleRescueService extends IService<WoOriginalData> {

    StatisticVo customerSatisfaction(FilterCriteriaModel model);

    WoRescueVolumeVo rescueStatus(FilterCriteriaModel model);

    List<WoRescueVolumeVo> rescueStatusTrend(FilterCriteriaModel model);

    List<ProportionCarSeriesVo> carSeriesTop(FilterCriteriaModel model);

    List<ProportionCarSeriesVo> carModelTop(FilterCriteriaModel model);

    List<BranchesVo> outletsTop(FilterCriteriaModel model);

    Page<WoRescueVo> workOrderDetailsList(FilterCriteriaModel model);

    List<WoCustomerTypeProportionVo> customerType(FilterCriteriaModel model);

    List<WoReceiverVo> receiver(FilterCriteriaModel model);
}
