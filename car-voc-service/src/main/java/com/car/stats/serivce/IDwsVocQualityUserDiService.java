package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocQualityUserDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.HighHotWordsVo;
import com.car.stats.vo.LableTrendVo;
import com.car.stats.vo.VocOverBriefingValueVo;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocUserDiService.java
 * @Description TODO
 * @createTime 2022年10月19日 13:55
 * @Copyright voc
 */
public interface IDwsVocQualityUserDiService extends IService<DwsVocQualityUserDi> {

    Result<?> qualityProblemTrend(FilterCriteriaModel model);

    Result<?> regionalDistribution(ProductQualityFilterCriteriaModel model);
    Result<?> focusRegionalTop(ProductQualityFilterCriteriaModel model);
    Result<?> focusCommunityTop(ProductQualityFilterCriteriaModel model);

    Result<?> rankingProvinces(ProductQualityFilterCriteriaModel model);
    Result<?> provinceMap(ProductQualityFilterCriteriaModel model);
    Result<?> topQualityUsers(ProductQualityFilterCriteriaModel model);


    Result<?> topVoiceUsers(FilterCriteriaModel model);

    List<UserLabelVo> thirdTagDistributionByTopic(LabelDetailFilterModel model, String topicName);

    LableTrendVo trendChangeLabel(LabelDetailFilterModel model);

    Map<String, Object> userAndStatistic(LabelDetailFilterModel model);

    Page<UserListInfoVo> getUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);

    List<UserLabelVo> thirdDimensionCodeBySecond(LabelDetailFilterModel model);
    List<UserLabelVo> secondDimensionCodeByFirst(LabelDetailFilterModel model);
    List<UserLabelVo> fistDimensionCode(LabelDetailFilterModel model);

    List<UserLabelVo> topicTagByThird(LabelDetailFilterModel model);

    Map<String, Object> userAndStatisticGenerality(LabelDetailFilterModel model);

    VocOverBriefingValueVo riskBriefingValue(RiskEventInsightModel model);

    BigDecimal riskStatisticTotal(RiskEventInsightModel model);

    Map<String, Object> riskUserNum(RiskEventInsightModel model);

    BigDecimal riskUserTotalNum(RiskEventInsightModel model);

    List<String> riskCarSeries(RiskEventInsightModel model);

    List<HighHotWordsVo > riskHotWordsOpinion(RiskEventInsightModel model);

    List<HighHotWordsVo > lastTagHotWords(LabelDetailFilterModel model);

    Map<String, BigDecimal> riskAllTotal(RiskEventInsightModel model1);

    List<String> riskCarSeriesStr(RiskEventInsightModel model1);

}
