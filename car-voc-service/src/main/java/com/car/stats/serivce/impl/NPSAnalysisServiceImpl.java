package com.car.stats.serivce.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.serivce.INPSAnalysisService;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.popvo.PopUpVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.common.util.SvwDate;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.BrandProductManagerMapper;
import com.car.voc.mapper.VocNPSAnalysisMapper;
import com.car.voc.model.BrandProductManagerModel;
import com.car.voc.model.LoginUser;
import com.car.voc.model.NPSFilterCriteriaModel;
import com.car.voc.model.VocBusinessTagModel;
import com.car.voc.service.*;
import com.car.voc.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.Bidi;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

import static com.car.voc.service.CommonService.getUserId;


/**
 * @version 1.0.0
 * @ClassName NPSAnalysisServiceImpl.java
 * @Description TODO
 * @createTime 2023年07月28日 13:22
 * @Copyright voc
 */
@Service
@Slf4j
public class NPSAnalysisServiceImpl implements INPSAnalysisService {

    static TimedCache<String, Map<String, VocBusinessTagModel>> businessTagModelCache = CacheUtil.newTimedCache(1000 * 30);
    static TimedCache<String, Map<String, String>> energyTypeCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 2);
    static TimedCache<String, Set<String>> userTagsCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);
    static TimedCache<String, SysRole> userQualityCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 12);

    static final String BUSINESS_TAGS_ONE_LEVEL_KEY = "BusinessCnTags_one";
    static final String BUSINESS_TAGS_KEY = "BusinessCnTags";
    static final String BUSINESS_TAGS_TWO_LEVEL_KEY = "BusinessCnTags_two";
    static final String ENERGY_TYPE_KEY = "energyType";
    static final String SEX_KEY = "sex";
    static final String BRAND_PRODUCT_KEY = "brandProduct";


    @Resource
    private RedisUtil redisUtil;
    @Autowired
    VocNPSAnalysisMapper vocNPSAnalysisMapper;
    /* @Autowired
     CommonService commonService;*/
    @Autowired
    IVocBusinessTagService iVocBusinessTagService;
    //    VocBusinessTagMapper vocBusinessTagMapper;
    @Autowired
    BrandProductManagerMapper brandProductManagerMapper;
    @Autowired
    IFaultProblemService iFaultProblemService;
    @Autowired
    ISysDictService sysDictService;

    @Autowired
    IProvinceAreaService iProvinceAreaService;
    @Autowired
    IBrandProductManagerService iBrandProductManagerService;
    @Autowired
    ISysRoleService iSysRoleService;

    /**
     * 调研人数统计
     * 1、通过问卷编号来统计调研参与人次
     * 2、得分展示，左侧为条件下区域得分，右侧为对应上一级得分（当为全国时，
     * 此处只显示左侧）,同时展示较上个周期的环比值；（得分计算：即通过计算调
     * 研得分的平均值，如：全国平均得分=总值/计算数量）
     * 3、调研参与者分类占比，初步定义规则为：0~6 为诋毁者，7~8 为中立者，9~10
     * 为推荐者。通过数据来计算不同分类参与者的人数据，环比与当期占比
     *
     *
     * <0或者>10的异常值，按空值处理
     * 1.分类时不统计这部分异常数据
     * 2.平均值不统计这部分异常数据
     * 3.总人数要统计人数
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> participantScore(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
//判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            //全国
            if(ObjectUtil.isNull(model.getProvince()) || CollUtil.isEmpty(model.getProvince())){
                //1、全国得分
                List<NPSAnalysisTrendVo> list1 = vocNPSAnalysisMapper.analysisDefault(model);

                final NPSAnalysisTrendVo firstItem = list1.stream().findFirst().orElse(null);


                NPSParticipantScoreVo vo = NPSParticipantScoreVo.builder()
                        .nationalScores(firstItem.getAvgScores())
                        .nationalScoresChian(firstItem.getAvgScoresC())
                        .build();
                log.info("participantScoreDefault.call:{}", vo);

                /**
                 * 全国时显示 全国得分+全国区域排名
                 */

                List<NPSAnalysisTrendVo> allArea = Optional.ofNullable(vocNPSAnalysisMapper.analysisTrendArea3(model)).orElse(Collections.EMPTY_LIST);
                List<NPSAreaScoreVo> areaList = allArea.stream()
                        .map(e -> {
                            return NPSAreaScoreVo.builder()
                                    .areaScores(e.getAvgScores())
                                    .areaChianScores(e.getAvgScoresC())
                                    .areaCode(e.getCode())
                                    .build();
                        })
                        .collect(Collectors.toList());
                vo.setAreaList(areaList);
                log.info("participantAllAreaScore.call:{}", allArea.size());

                return Result.OK(vo);
            }else{

                //1、区域得分
                List<NPSAnalysisTrendVo> list1 = vocNPSAnalysisMapper.analysisDefault(model);
                final NPSAnalysisTrendVo firstItem = list1.stream().findFirst().orElse(null);
                //2 、 全国得分
                NPSFilterCriteriaModel model1 = NPSFilterCriteriaModel.builder().build();
                BeanUtil.copyProperties(model, model1);
                model1.setProvince(null);
                model1.setRegion(null);
                List<NPSAnalysisTrendVo> list2 = vocNPSAnalysisMapper.analysisDefault(model1);
                final NPSAnalysisTrendVo firstItem1 = list2.stream().findFirst().orElse(null);

                final List<NPSAnalysisTrendVo> allArea = Optional.ofNullable(vocNPSAnalysisMapper.analysisTrendArea3(model1)).orElse(Collections.EMPTY_LIST);
                final Optional<String> nationalRanking = allArea.stream().filter(e -> e.getCode().equals(firstItem.getCode())).map(NPSAnalysisTrendVo::getNationalRanking).findFirst();
                final Optional<String> nationalRankingC = allArea.stream().filter(e -> e.getCode().equals(firstItem.getCode())).map(NPSAnalysisTrendVo::getNationalRankingC).findFirst();
                final int nationalRankingR = (nationalRankingC.isPresent() ? Integer.valueOf(nationalRankingC.get()) : 0)
                         - (nationalRanking.isPresent() ? Integer.valueOf(nationalRanking.get() ) : 0);

                NPSParticipantScoreVo vo = NPSParticipantScoreVo.builder()
                        .areaCode(firstItem.getCode())
                        .areaScores(firstItem.getAvgScores())
                        .areaChianScores(firstItem.getAvgScoresC())
                        .nationalScores(firstItem1.getAvgScores())
                        .nationalScoresChian(firstItem1.getAvgScoresC())
                        .nationalRanking(nationalRanking.isPresent() ? nationalRanking.get() : null)
                        .nationalRankingChian(String.valueOf(nationalRankingR))
                        .nationalAreaCount(String.valueOf(allArea.size()))
                        .build();
                log.info("participantScoreDefault.call:{}", vo);



                return Result.OK(vo);
            }


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.OK(Collections.EMPTY_LIST);
        }
    }


    public void setAreasAuthority(NPSFilterCriteriaModel model) {

//        List<DictVo> ls = sysDictService.queryDictItemsByCode("area");
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<Object> areaSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_AREA, model.getBrandCode(), loginUser.getId()));
        List<String> roleAreasCodes = areaSet.stream().map(String::valueOf).collect(Collectors.toList());
//        ls = ls.stream().filter(s -> roleAreasCodes.contains(s.getValue())).collect(Collectors.toList());

        /*List<String> provinceIdList = new ArrayList<>();
        ls.forEach(e -> {
            List<DictVo> chs = iProvinceAreaService.queryProvinceByAreaCode(e.getValue(), model.getBrandCode());
            Collections.sort(chs, (DictVo o1, DictVo o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getText(), o2.getText()));

            e.setChildes(chs);
            if (CollectionUtils.isNotEmpty(chs)) {
                List<String> list = chs.stream().map(DictVo::getValue).collect(Collectors.toList());
                provinceIdList.addAll(list);
            }
        });*/
        //设置大区数据
        model.setAreas(roleAreasCodes);
    }

    @Override
    public Result<?> participantProportion(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            List<NPSParticipantProportionVo> voList = vocNPSAnalysisMapper.participantProportion(model);
            return Result.OK(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.OK(Collections.EMPTY_LIST);
        }
    }


    @Override
    public Result<?> analysisTrend(NPSFilterCriteriaModel model) {
        Set<NPSAnalysisLabelVo> voList = new HashSet();
//        boolean isDefaultSearFilter = CollUtil.isEmpty(model.getProvince()) ? true : false;
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            List<NPSAnalysisTrendVo> list2 = vocNPSAnalysisMapper.analysisTrendArea(model);

            List<NPSAnalysisTrendVo> list1 = vocNPSAnalysisMapper.analysisTrendDefault(model);


            list1.addAll(list2);
            log.info("");
            Map<String, List<NPSAnalysisTrendVo>> map = list1.stream().collect(Collectors.groupingBy(NPSAnalysisTrendVo::getDateUnit));

            map.keySet().stream().forEach(e -> {
                voList.add(NPSAnalysisLabelVo.builder()
                        .dateStr(model.getDateUnit().equals(1) ? this.replaceMonthStr(e) : e)
                        .list(map.get(e))
                        .build());
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        List<NPSAnalysisLabelVo> list = CollUtil.sort(voList, new NPSComparator());
        //删除最后一条
        if (ObjectUtil.isNotEmpty(list) && voList.size() >= 2) {
            list.remove(0);
        }

        return Result.OK(list);
    }

    private String replaceMonthStr(String str) {
        try {
            String[] splitArr = str.split("-");
            if (splitArr.length > 1) {
                String month = splitArr[splitArr.length - 1];
                if (month.startsWith("0")) {
                    month = month.replace("0", "");
                }
                final String dateStr = splitArr[0].concat("-").concat(month);
                return dateStr;
            }
            return str;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return str;
    }

    /**
     * 通过 NPS 得分+人次数、NLP 情感+提及量的数据，结合车系进行分析分布、占
     * 比及趋势
     * 1、NPS 维度分析，通过调研人次+得分进行数据统计车系参与人次分布，不同
     * 参与类型（诋毁者／中立者／推荐者）占比，环比
     * 2、NLP 情感分析，通过提及量中对应的情感倾向（正面／负面／中性）占比，
     * 环比
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> carSeriesDistribution(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            if (StrUtil.isBlank(model.getCarSeriesDistributionType())) {
                model.setCarSeriesDistributionType(NPSFilterCriteriaModel.CARSERIESDISTRIBUTIONTYPE_DEFAULT_VALUE);
            }

            if ("1".equals(model.getCarSeriesDistributionType())) {
                List<NPSCarScoreVo> voList = vocNPSAnalysisMapper.carSeriesScoreDistribution(model);
                return Result.OK(CollUtil.sort(voList, new NPSComparator()));
            } else if ("2".equals(model.getCarSeriesDistributionType())) {
                List<NPSCarEmotionVo> voList = vocNPSAnalysisMapper.carSeriesEmotionDistribution(model);
                return Result.OK(CollUtil.sort(voList, new NPSComparator()));
            }
//            List<NPSCarScoreVo> voList = vocNPSAnalysisMapper.carSeriesScoreDistribution(model);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.OK(Collections.EMPTY_LIST);
    }

    /**
     * 情感趋势变化
     * 1、通过不同的近 6 个周期条件下提及量对应的情感倾向走趋势变化
     * 2、鼠标放上去，可看到不同时期的情感提及量对应数据量占比，环比情况
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> carSeriesEmotionTrend(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }

            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            List<NPSCarEmotionTrendVo> voList = vocNPSAnalysisMapper.carSeriesEmotionTrend(model);

            voList.stream().forEach(e -> {

                e.setDateStr(model.getDateUnit().equals(1) ? this.replaceMonthStr(e.getDateStr()) : e.getDateStr());
            });

            //删除最后一条
            voList = CollUtil.sort(voList, new NPSComparator());
            if (ObjectUtil.isNotEmpty(voList) && voList.size() >= 2) {
                voList.remove(0);
            }

            return Result.OK(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.OK(Collections.EMPTY_LIST);
        }
    }

    /**
     * 高频热词
     * 1、通过观点模型分析出用户发声聚焦的热词，可展示全部、正面、负面、中性的热
     * 词分布
     * 2、默认展示 TOP50 的热词，可下载 TOP500 的热词提及数据
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> focusAnalysisHotWords(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            if (ObjectUtil.isEmpty(model.getTopn())) {
                model.setTopn(NPSFilterCriteriaModel.TOPN_DEFAULT_VALUE);
            }
            List<NPSHighHotWordsVo> voList = vocNPSAnalysisMapper.focusAnalysisHotWords(model);
            return Result.OK(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.OK(Collections.EMPTY_LIST);
        }
    }

    private NPSFilterCriteriaModel setNpsQualityDataAuthority(NPSFilterCriteriaModel model){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        final String userId = sysUser.getId();

        final String accessToken = SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);

        Optional<SysRole> roleModel = Optional.empty();
        if (!userQualityCache.containsKey(accessToken)) {
            roleModel = Optional.ofNullable(iSysRoleService.getRoleByUserId(userId));
            userQualityCache.put(accessToken, roleModel.get());
        } else {
            roleModel = Optional.ofNullable(userQualityCache.get(accessToken));
        }

        log.trace("accessToken {}", accessToken);

        //设置大区数据
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Set<Object> areaSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_AREA, model.getBrandCode(), loginUser.getId()));
        List<String> roleAreasCodes = areaSet.stream().map(String::valueOf).collect(Collectors.toList());
        model.setAreas(roleAreasCodes);

        Boolean sQuality = false;
        String qualityText = roleModel.get().getQualityText();
        if (wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(qualityText)){
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(model.getBrandCode())){
                sQuality = (Boolean) object.get(model.getBrandCode());
            }
        }

        if (ObjectUtil.isNull(model.getTagType()) || Integer.valueOf(1).equals(model.getTagType())) { //业务标签
            if (roleModel.isPresent() && sQuality) {
                log.info("当前用户拥有质量数据权限 {}", userId);
            } else {
                log.info("当前用户不拥有质量数据权限 {}", userId);
                model.setTagType(1);   //只访问业务数据
            }
            //用户登陆时获取的权限数据的缓存功能，下次登陆会被抛弃使用 token:values
            Optional<Object> cacheUserTags = Optional.empty();
            if (!userTagsCache.containsKey(accessToken)) {
                cacheUserTags = Optional.ofNullable(redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG,model.getBrandCode(),userId)));
                if (cacheUserTags.isPresent() && CollUtil.isNotEmpty((Set<String>) cacheUserTags.get())) {
                    userTagsCache.put(accessToken, (Set<String>) cacheUserTags.get());
                }
            } else {
                cacheUserTags = Optional.ofNullable(userTagsCache.get(accessToken));
            }
//            Optional<Object> userTag = Optional.ofNullable(redisUtil.sGet(CacheConstant.SYS_USER_TAG + userId));
            boolean userTagIsPresent = (cacheUserTags.isPresent() && CollUtil.isNotEmpty((Set<String>) cacheUserTags.get()));

            if (userTagIsPresent) {
                final Set<String> userTags = (Set<String>) cacheUserTags.get();

                //一级标签过滤
                if (CollUtil.isEmpty(model.getFirstDimensionCodes())) {
                    Map<String, VocBusinessTagModel> tags_one = this.getBusinessCnTagsOneLevel();
                    final Set<String> firstSet = tags_one.values().stream().map(VocBusinessTagModel::getTagCode).collect(Collectors.toSet());
//                    userTags.retainAll(firstSet);

                    HashSet<String> values = new HashSet<>(userTags);
                    values.retainAll(firstSet);
                    model.setFirstDimensionCodes(values);
                }

                //二级标签过滤
                if (CollUtil.isEmpty(model.getSecondDimensionCodes())) {
                    Map<String, VocBusinessTagModel> tags_two = this.getBusinessCnTagsTwoLevel();
                    final Set<String> secondSet = tags_two.values().stream().map(VocBusinessTagModel::getTagCode).collect(Collectors.toSet());
//                    userTags.retainAll(secondSet);
                    HashSet<String> values = new HashSet<>(userTags);
                    values.retainAll(secondSet);
                    model.setSecondDimensionCodes(values);
                }

            }
            return model;
            //} else if (ObjectUtil.isNotNull(model.getTagType()) && Integer.valueOf(1).equals(model.getTagType())) {

        } else if (ObjectUtil.isNotNull(model.getTagType()) && Integer.valueOf(2).equals(model.getTagType())) {  //质量标签
            if (roleModel.isPresent() && sQuality) {
                log.info("当前用户拥有质量数据权限 {}", userId);
                model.setTagType(2);
            }
            return model;
        } else {
            ; /*


        Optional<Object> userTag = Optional.ofNullable(redisUtil.sGet(CacheConstant.SYS_USER_TAG + userId));
        boolean userTagIsPresent = (userTag.isPresent() && CollUtil.isNotEmpty((Set<String>) userTag.get()));
        if(roleModel.isPresent() || userTagIsPresent){
            if (roleModel.isPresent() && !roleModel.get().isQuality()) {
                log.info("当前用户无质量数据权限");
                model.setTagType(2);
            }

            if(userTagIsPresent){
                Set<String> userTags = (Set<String>) userTag.get();
                Map<String, VocBusinessTagModel> tags = this.getBusinessCnTagsOneLevel();
                //一级标签
                final Set<String> firstSet = tags.values().stream().map(VocBusinessTagModel::getTagCode).collect(Collectors.toSet());
                userTags.retainAll( firstSet);

                if(!userTags.isEmpty()){
                    model.setFirstDimensionCodes(userTags);
                }
            }
            return model;
        }*/
        }

        return null;

        /*Optional<SysRole> sysRole = Optional.ofNullable(iSysRoleSe

        return null;

        /*Optional<SysRole> sysRole = Optional.ofNullable(iSysRoleService.getRoleByUserId(userId));
        if (sysRole.isPresent()) {
            if (!sysRole.get().isQuality()) {
                log.info("当前用户无质量数据权限");
                model.setTagType(2);
            }
            return sysRole.get().isQuality();
        }
        log.info("当前用户无质量数据权限");
        model.setTagType(2);
        return false;*/
    }

    /**
     * 聚焦分布
     * 1、通过标签体系分析出发声提及的标签数据量，对应情感分布、占比、环比
     * 2、通过筛选标签层级(包括品牌体验、产品体验、智能化体验、销售服务、售后服务、
     * 权益服务、数字化体验、产品质量)可查看不同的标签分布鼠标放上去，可看到情
     * 感提及量总量／占比、本标签对应情感占比，环比情况
     * 3、默认展示层级下所有的标签，可下载所有的 8 个大块标签提及量、各情感提及量
     * 的占比、环比值
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> focusDistribution(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }

            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            if (StrUtil.isNotBlank(model.getFirstDimensionCode()) &&
                    StrUtil.isNotBlank(model.getSecondDimensionCode()) &&
                    StrUtil.isNotBlank(model.getThirdDimensionCode())) {
                //1,2,3
                model.setDimensionRsLevel("3");
            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode()) &&
                    StrUtil.isNotBlank(model.getSecondDimensionCode()) &&
                    StrUtil.isBlank(model.getThirdDimensionCode())) {
                //1,2
                model.setDimensionRsLevel("2");
            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode()) &&
                    StrUtil.isBlank(model.getSecondDimensionCode()) &&
                    StrUtil.isBlank(model.getThirdDimensionCode())) {
                //1
                model.setDimensionRsLevel("1");
            } else {
                ;
            }

            model.setTopn(NPSFilterCriteriaModel.TOPN_DEFAULT_VALUE);


            List<NPSFousDistributionVo> voList = vocNPSAnalysisMapper.focusDistributionEmotion(model);
            voList.parallelStream().forEach(e -> {
                if (StrUtil.isBlank(model.getDimensionRsLevel())) {
                    e.setSecondDimensionCode(null);
                    e.setThreeDimensionCode(null);
                    e.setTopicCode(null);
                    e.setLabelCode(e.getFirstDimensionCode());
                } else if ("1".equals(model.getDimensionRsLevel())) {
                    e.setThreeDimensionCode(null);
                    e.setTopicCode(null);
                    e.setLabelCode(e.getSecondDimensionCode());
                } else if ("2".equals(model.getDimensionRsLevel())) {
                    e.setTopicCode(null);
                    e.setLabelCode(e.getThreeDimensionCode());
                } else if ("3".equals(model.getDimensionRsLevel())) {
                    e.setLabelCode(e.getTopicCode());
                } else {
                    e.setLabelCode(e.getTopicCode());
                }
            });
            return Result.OK(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.OK(Collections.EMPTY_LIST);
    }


    /**
     * 用户意图分析
     * 通过 NLP 分析调研反馈的用户数据，结合标签对应的用户投诉、抱怨、咨询、建议、
     * 其他的意图数据的分析
     * <p>
     * 用户意图占比
     * 1、通过观点模型分析出用户发声聚焦的意图，分析不同意图在条件下的分布占比
     * 2、可下载统计数据，意图数据量、占比与环比
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> intentionAnalysisProportion(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            List<NPSIntentionAnalysisProportionVo> voList = vocNPSAnalysisMapper.intentionAnalysisProportion(model);
            return Result.OK(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.OK(Collections.EMPTY_LIST);
        }
    }

    /**
     * 用户意图分析
     * 通过 NLP 分析调研反馈的用户数据，结合标签对应的用户投诉、抱怨、咨询、建议、
     * 其他的意图数据的分析
     * <p>
     * 用户意图趋势
     * 1、通过不同的近 12 个周期条件下提及量对应的意图走趋势变化
     * 2、鼠标放上去，可看到不同时期的意图提及量对应数据量占比，环比情况
     * 3、可下载所有的 8 个大块标签提及量、各意图提及量的占比、环比值
     *
     * @param model
     * @return
     */
    @Override
    public Result<?> intentionAnalysisTrend(NPSFilterCriteriaModel model) {
        try {
            if (!this.isNPSDataAuthority()) {
                return Result.OK(null);
            }
            //判断出是否拥有质量数据权限
            if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
                return Result.OK(null);
            }

            List<NPSIntentionAnalysisProportionVo> voList = vocNPSAnalysisMapper.intentionAnalysisTrend(model);

            voList.stream().forEach(e -> {

                e.setDateStr(model.getDateUnit().equals(1) ? this.replaceMonthStr(e.getDateStr()) : e.getDateStr());
            });

            //删除最后一条
            voList = CollUtil.sort(voList, new NPSComparator());
            if (ObjectUtil.isNotEmpty(voList) && voList.size() >= 2) {
                voList.remove(0);
            }

            return Result.OK(voList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.OK(Collections.EMPTY_LIST);
        }
    }

    private boolean isNPSDataAuthority() {
        Set<Object> channelSet = new HashSet<>();

        QueryWrapper<BrandProductManager> query = new QueryWrapper<>();
        query.lambda().eq(BrandProductManager::getPId, 0);
        Page<BrandProductManager> page = new Page<>(0, 100);
        IPage<BrandProductManager> pageList = iBrandProductManagerService.page(page, query);
        List<BrandProductManager> records = pageList.getRecords();
        for (BrandProductManager brandProductManager : records) {
            Set<Object> objects = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_CHANNEL, brandProductManager.getBrandCode(), getUserId()));
            if (CollectionUtils.isNotEmpty(objects)) {
                channelSet.addAll(objects);
            }
        }
        Object npsChannelId = channelSet.stream().filter(e -> "1691461637700567042".equals(e)).findFirst().orElse(null);
        if (ObjectUtil.isNotNull(npsChannelId)) {
            return true;
        } else {
            log.info("npsChannalId {}", npsChannelId);
        }
        return false;
//        return true;
    }

    @Override
    public IPage<NPSDataVo> list(Page<NPSDataVo> page, NPSFilterCriteriaModel model) {
        if (!this.isNPSDataAuthority()) {
            return new Page<>();
        }
//判断出是否拥有质量数据权限
        if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
            return new Page<>();
        }


        Map<String, String> energyTypeMap = this.getEnergyType();
        Map<String, String> sexMap = this.getSexDict();
//        Map<String, VocBusinessTag> tags = this.getBusinessCnTags();

        model.setKeyword(StrUtil.trim(model.getKeyword()));
        /*com.baomidou.mybatisplus.core.metadata.IPage<NPSDataVo> relist
                = vocNPSAnalysisMapper.list((com.baomidou.mybatisplus.core.metadata.IPage<NPSDataVo>)commonService.converPage(page), model);*/
        IPage<NPSDataVo> relist = vocNPSAnalysisMapper.list(page, model);
        List<NPSDataVo> list = relist.getRecords().parallelStream()
                .map(e -> {
                    //类型
                    e.setDataType("NPS");

                    //能源类型
                    if (StrUtil.isNotBlank(e.getEnergyType()) && energyTypeMap.containsKey(e.getEnergyType())) {
                        e.setEnergyType(energyTypeMap.get(e.getEnergyType()));
                    }

                    if (StrUtil.isNotBlank(e.getClientSex()) && sexMap.containsKey(e.getClientSex())) {
                        e.setClientSex(sexMap.get(e.getClientSex()));
                    }

                    return e;
                })
                .collect(Collectors.toList());
        relist.setRecords(list);
        return relist;
    }

    @Override
    public Result<?> exportToXls(NPSFilterCriteriaModel model, Page<NPSDataVo> page) {
        if (!this.isNPSDataAuthority()) {
            return Result.OK(null);
        }
        //判断出是否拥有质量数据权限
        if(ObjectUtil.isNull(setNpsQualityDataAuthority(model))){
            return Result.OK(null);
        }

        Map<String, String> energyTypeMap = this.getEnergyType();
        Map<String, String> sexMap = this.getSexDict();
        Map<String, String> tags = this.getBusinessCnTags();
        Map<String, String> brandProductMap = this.geBrandProducts();

        model.setKeyword(StrUtil.trim(model.getKeyword()));
//        IPage<NPSDataVo> relist = (IPage<NPSDataVo>) vocNPSAnalysisMapper.list((com.baomidou.mybatisplus.extension.plugins.pagination.Page<NPSDataVo>) commonService.converPage(page), model);
        IPage<NPSDataVo> relist = vocNPSAnalysisMapper.list(page, model);
        List<NPSDataVo> list = relist.getRecords().parallelStream()
                .map(e -> {
                    e.setDataType("NPS");

                    if (StrUtil.isNotBlank(e.getFirstDimensionCode()) && tags.containsKey(e.getFirstDimensionCode())) {
                        e.setFirstDimensionCode(tags.get(e.getFirstDimensionCode()));
                    }
                    if (StrUtil.isNotBlank(e.getSecondDimensionCode()) && tags.containsKey(e.getSecondDimensionCode())) {
                        e.setSecondDimensionCode(tags.get(e.getSecondDimensionCode()));
                    }
                    if (StrUtil.isNotBlank(e.getThirdDimensionCode()) && tags.containsKey(e.getThirdDimensionCode())) {
                        e.setThirdDimensionCode(tags.get(e.getThirdDimensionCode()));
                    }
                    if (StrUtil.isNotBlank(e.getTopicCode()) && tags.containsKey(e.getTopicCode())) {
                        e.setTopicCode(tags.get(e.getTopicCode()));
                    }
                    //能源类型
                    if (StrUtil.isNotBlank(e.getEnergyType()) && energyTypeMap.containsKey(e.getEnergyType())) {
                        e.setEnergyType(energyTypeMap.get(e.getEnergyType()));
                    }

                    if (StrUtil.isNotBlank(e.getClientSex()) && sexMap.containsKey(e.getClientSex())) {
                        e.setClientSex(sexMap.get(e.getClientSex()));
                    }
                    if (StrUtil.isNotBlank(e.getBrandCode()) && brandProductMap.containsKey(e.getBrandCode())) {
                        e.setBrandCode(brandProductMap.get(e.getBrandCode()));
                    }

                    if (StrUtil.isNotBlank(e.getCarSeriesCode()) && brandProductMap.containsKey(e.getCarSeriesCode())) {
                        e.setCarSeriesCode(brandProductMap.get(e.getCarSeriesCode()));
                    }
                    return e;
                })
                .collect(Collectors.toList());
        relist.setRecords(list);

        return Result.OK(relist);
    }



    private Map<String, String> getBusinessCnTags() {
        /*if (cache.containsKey(BUSINESS_TAGS_KEY)) {
            return cache.get(BUSINESS_TAGS_KEY);
        }*/

//        List<InternationalVo> tags = vocBusinessTagMapper.internationalCnTags_();
        List<VocBusinessTagModel> tags = iVocBusinessTagService.findAll();


        Map<String, String> map = tags.parallelStream().collect(Collectors.toMap(VocBusinessTagModel::getTagCode, VocBusinessTagModel::getName, (key1, key2) -> key2));

        List<InternationalVo> list = iFaultProblemService.internationalCnTags();
        Map<String, String> map2 = list.parallelStream().collect(Collectors.toMap(InternationalVo::getCode, InternationalVo::getTextCn, (key1, key2) -> key2));

        map.putAll(map2);
//        cache.put(BUSINESS_TAGS_KEY, map);
        return map;
    }

    private Map<String, VocBusinessTagModel> getBusinessCnTagsOneLevel() {
        if (businessTagModelCache.containsKey(BUSINESS_TAGS_KEY)) {
            return businessTagModelCache.get(BUSINESS_TAGS_KEY);
        }
        List<VocBusinessTagModel> tags = iVocBusinessTagService.findAll();

        Map<String, VocBusinessTagModel> map = tags.parallelStream()
                .filter(e -> e.getPid().equalsIgnoreCase(IVocBusinessTagService.ROOT_PID_VALUE))
                .collect(Collectors.toMap(VocBusinessTagModel::getTagCode, e -> e, (key1, key2) -> key2));

        businessTagModelCache.put(BUSINESS_TAGS_ONE_LEVEL_KEY, map);
        return map;
    }

    private Map<String, VocBusinessTagModel> getBusinessCnTagsTwoLevel() {
        if (businessTagModelCache.containsKey(BUSINESS_TAGS_TWO_LEVEL_KEY)) {
            return businessTagModelCache.get(BUSINESS_TAGS_TWO_LEVEL_KEY);
        }
        //一级
        Map<String, VocBusinessTagModel> oneLevel = this.getBusinessCnTagsOneLevel();
        final Set<String> firstSet = oneLevel.values().stream().map(VocBusinessTagModel::getId).collect(Collectors.toSet());
        //所有
        List<VocBusinessTagModel> tags = iVocBusinessTagService.findAll();

        Map<String, VocBusinessTagModel> map = tags.parallelStream()
                .filter(e -> firstSet.contains(e.getPid()))
                .collect(Collectors.toMap(VocBusinessTagModel::getTagCode, e -> e, (key1, key2) -> key2));

        businessTagModelCache.put(BUSINESS_TAGS_TWO_LEVEL_KEY, map);
        return map;
    }


    private Map<String, String> geBrandProducts() {
        if (energyTypeCache.containsKey(BRAND_PRODUCT_KEY)) {
            return energyTypeCache.get(BRAND_PRODUCT_KEY);
        }

//        List<InternationalVo> tags = vocBusinessTagMapper.internationalCnTags_();
        List<BrandProductManagerModel> list = iBrandProductManagerService.findAll();

        Map<String, String> map = list.parallelStream().collect(Collectors.toMap(BrandProductManagerModel::getCode, e -> e.getName(), (key1, key2) -> key2));
        energyTypeCache.put(BUSINESS_TAGS_KEY, map);
        return map;
    }


    private Map<String, String> getEnergyType() {
        if (energyTypeCache.containsKey(ENERGY_TYPE_KEY)) {
            return energyTypeCache.get(ENERGY_TYPE_KEY);
        }

        List<DictVo> dictVoList = sysDictService.queryDictItemsByCode("energy_type");
        Map<String, String> map = dictVoList.stream().collect(Collectors.toMap(DictVo::getValue, e -> e.getText(), (key1, key2) -> key2));

        energyTypeCache.put(ENERGY_TYPE_KEY, map);

        return map;
    }

    private Map<String, String> getSexDict() {
        if (energyTypeCache.containsKey(SEX_KEY)) {
            return energyTypeCache.get(SEX_KEY);
        }

        List<DictVo> DictVoList = sysDictService.queryDictItemsByCode("sex");
        Map<String, String> map = DictVoList.stream().collect(Collectors.toMap(DictVo::getValue, e -> e.getText(), (key1, key2) -> key2));

        energyTypeCache.put(SEX_KEY, map);

        return map;
    }

    class NPSComparator implements Comparator<Object> {
        @Override
        public int compare(Object o1, Object o2) {
            try {
                if (ObjectUtil.isEmpty(o1) || ObjectUtil.isEmpty(o2)) {
                    return 0;
                }
                // || StrUtil.isBlank(o1.getDateStr()) || StrUtil.isBlank(o2.getDateStr())
                Object o1Str = ReflectUtil.getFieldValue(o1, "dateStr");
                Object o2Str = ReflectUtil.getFieldValue(o2, "dateStr");

                String[] arr1 = Optional.ofNullable(String.valueOf(o1Str)).get().split("-");
                String[] arr2 = Optional.ofNullable(String.valueOf(o2Str)).get().split("-");

                int comparator = toInt(arr1[0], -1) - toInt(arr2[0], -2);

                if (comparator == 0) {
                    comparator = toInt(arr1[1], -1) - toInt(arr2[1], -2);
                    if (comparator == 0) {
                        return toInt(arr1[2], -1) - toInt(arr2[2], -2);
                    }
                }
                return comparator;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return 0;
        }

        private int toInt(String str, int delVal) {
            try {
                return Integer.parseInt(str);
            } catch (Exception e) {
                return delVal;
            }
        }
    }

    public static void main(String[] r) {
        String s = "2023-09";
        String[] splitArr = s.split("-");
        if (splitArr.length > 1) {
            String month = splitArr[splitArr.length - 1];
            if (month.startsWith("0")) {
                month = month.replace("0", "");
            }
            System.out.println(month);
            System.out.println(splitArr[0].concat("-").concat(month));
        }
    }

    @Override
    public Map<String, String> userAndStatistic(LabelDetailFilterModel model) {
        return vocNPSAnalysisMapper.userAndStatistic(model);
    }

}
