package com.car.stats.serivce.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.stats.entity.risk.VocRisk;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.*;
import com.car.stats.vo.HighHotWordsVo;
import com.car.stats.vo.VocOverBriefingValueVo;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SendMessageUtils;
import com.car.voc.entity.*;
import com.car.voc.mail.ExchangeMailUtils;
import com.car.voc.model.LoginUser;
import com.car.voc.model.risk.RiskAlertReviewerModel;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.service.*;
import com.car.voc.vo.risk.RiskAlertReviewerVo;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DingtalkMessageServiceImpl.java
 * @Description TODO
 * @createTime 2023年07月26日 13:53
 * @Copyright voc
 */
@Service
@Log4j2
public class DingtalkMessageServiceImpl implements SendMessageService {


    //开放平台key
    @Value("${configuration.dingTalk.appKey}")
    String appKey;
    //开放平台秘钥
    @Value("${configuration.dingTalk.appSecret}")
    String appSecret;
    //调用地址(发送工作通知)
    @Value("${configuration.dingTalk.workNoticesUrl}")
    String url;

    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ISysDepartService departService;
    @Autowired
    VocProjectGroupService projectGroupService;
    @Autowired
    IVocRiskAllTypesService riskAllTypesService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IFaultProblemService faultProblemService;
    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    IDwdVocUserRiskService userRiskService;
    @Autowired
    IDwdVocQualityRiskService qvocRiskService;

    @Autowired
    IVocBusinessTagService businessTagService;
    @Autowired
    IFaultProblemService problemService;
    @Autowired
    IBrandProductManagerService brandProductManagerService;
    @Autowired
    ISendMessageService iSendMessageService;
    @Autowired
    private IVocRiskAlertReviewerService riskAlertReviewerService;
    @Override
    public boolean sendMessage(RiskAllTypesModel model) {

        try {


            boolean send = false;
            for (VocRiskHandlingRecord processor : model.getProcessors()) {
                if (processor.isSendDingtalk()) {
                    send = true;
                }
                if (processor.isSendMessage()) {
                    send = true;
                }
                if (processor.isSendMail()) {
                    send = true;
                }
            }
            if (!send) {
                return false;
            }

            String t3 = "     VOC管理平台风险预警有新的派发任务，此任务须尽快处理，以触达客户的响应；请及时进行处理。 \n";

            VocRiskAllTypes risk = riskAllTypesService.getById(model.getId());
            DwdVocRisk buRisk;
            DwdVocQualityRisk qrisk;
            DwdVocUserRisk urisk;
            String riskDate = "";
            StringBuffer riskName = new StringBuffer();
            StringBuffer fmtjl = new StringBuffer();
            StringBuffer fsyf = new StringBuffer();
            StringBuffer sjcx = new StringBuffer();
            StringBuffer gdrc = new StringBuffer();
            if ("1".equals(risk.getRiskType())) {
                riskName.append(businessTagService.getNameByCode(risk.getRiskName().substring(0, 5)));
                riskName.append("-" + businessTagService.getNameByCode(risk.getRiskName()));
                buRisk = vocRiskService.getById(risk.getRiskId());
                riskDate = setAilRiskDate(buRisk);
                setFmtjl_b(buRisk, fmtjl, fsyf);
                setSjcx_b(buRisk, sjcx);
                setGdrc_b(buRisk, gdrc);
            } else if ("2".equals(risk.getRiskType())) {
                riskName.append(faultProblemService.getNameByCode(risk.getRiskName().substring(0, 11)));
                riskName.append("-" + faultProblemService.getNameByCode(risk.getRiskName()));
                qrisk = qvocRiskService.getById(risk.getRiskId());
                riskDate = setAilRiskDate(qrisk);
                setFmtjl_q(qrisk, fmtjl, fsyf);
                setSjcx_q(qrisk, sjcx);
                setGdrc_q(qrisk, gdrc);

            } else if ("3".equals(risk.getRiskType())) {
                riskName.append("用户-" + risk.getRiskName());
                urisk = userRiskService.getById(risk.getRiskId());
                riskDate = setAilRiskDate(urisk);
                setFmtjl_u(urisk, fmtjl, fsyf);
                setSjcx_u(urisk, sjcx);
                setGdrc_u(urisk, gdrc);


            }
            String fsyfs = "-----------------------------------------------------------\n" +
                    "发声用户:&emsp; **" + fsyf + "** \n";
            if ("3".equals(risk.getRiskType())) {
                fsyfs = "";
            }
            String cx = "";
            if (sjcx.length() > 0) {
                cx = "-----------------------------------------------------------\n" +
                        "涉及车型:&emsp; **" + sjcx + "** \n";
            }
            String rc = "";
            if (gdrc.length() > 0) {
                rc = "-----------------------------------------------------------\n" +

                        "观点热词:&emsp; **" + gdrc + "** \n";
            }
            String risklevel=StrUtil.isNotBlank(risk.getNewRiskLevel())?risk.getNewRiskLevel():risk.getRiskLevel();
            String brandName=brandProductManagerService.getCarNameByCarCode(risk.getBrandCode());
            LoginUser suus = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            SysDepart dep = departService.getDepartByUserId(suus.getId());
            String table = "  \n" +
                    "-----------------------------------------------------------\n" +
                    "风险名称:&emsp; **" + riskName + "** \n" +
                    "\n" +
                    "-----------------------------------------------------------\n" +
                    "风险等级:&emsp; **" + risklevel + "** \n" +
                    "\n" +
                    "-----------------------------------------------------------\n" +
                    "分析周期:&emsp; **" + riskDate + "** \n" +
                    "\n" +
                    "-----------------------------------------------------------\n" +
                    "负面提及量:&emsp; **" + fmtjl + "**\n" +
                    "\n" +
                    fsyfs +
                    "\n" +
                    "-----------------------------------------------------------\n" +
                    "对应品牌:&emsp; **" + brandName + "**\n" +
                    "\n" +
                    cx +
                    "\n" +
                    rc +
                    "\n" +
                    "-----------------------------------------------------------\n";

            String last = "  \n请登录用户声音VOC管理平台查阅; " +
                    "\n" +
                    "此预警任务由**" + dep.getDepartName() + "-" + suus.getRealname() + "**确认派发，如有问题请尽快联系VOC运营组协助处置！\n";
            for (VocRiskHandlingRecord processor : model.getProcessors()) {
                if (StrUtil.isNotBlank(processor.getMailContent())){
                    t3=t3+"\n"+processor.getMailContent();
                }
                String useinfo="";
                SysUser pu = sysUserService.getById(processor.getProcessUserId());
                if (processor.getBindType()==1){
                    SysDepart pp = departService.getById(processor.getProcessDepartId());
                    useinfo = pp.getDepartName() + "-" + pu.getRealname();
                }else {
                    VocProjectGroup byId = projectGroupService.getById(processor.getProcessDepartId());
                    useinfo = byId.getProjectName() + "-" + pu.getRealname();
                }


                //钉钉
                if (processor.isSendDingtalk()) {

                    String title = "\n" +
                            "标题：VOC洞察风险预警提醒  \n" +
                            "内容：**" + useinfo + "**：您好！  \n" + t3 +
                            "\n";
//                SendMessageUtils.sendDingtalk("1100011000",title,table,last);
//                    SendMessageUtils.sendDingtalk(pu.getUsername(), title, table, last);
                    iSendMessageService.sendDingtalk(pu.getUsername(), title, table, last);
                }
                String syif = "发声用户：" + fsyf + "，";
                if ("3".equals(risk.getRiskType())) {
                    syif = "";
                }
                String cars = "";
                if (sjcx.length() > 0) {
                    cars = "涉及"+brandName+":" + sjcx + "等车型。";
                }
                //短信
                if (!StrUtil.isBlankIfStr(pu.getPhone()) && processor.isSendMessage()) {
                    String msg = "尊敬的" + useinfo + "，您有新的VOC预警任务[" + riskName + "]需要处理。" +processor.getMailContent()+"  "+
                            "此风险于" + riskDate + "洞察发现，" + syif + "负面提及量：" + fmtjl +"，风险等级：" + risklevel + "。" + cars + "请尽快登录VOC系统确认事件处理。";
//                    SendMessageUtils.sendMessage(pu.getPhone(), msg);
                    msg="你有一项编号为【"+riskName+"】的事务需要处理。";
                    iSendMessageService.sendMessage(pu.getPhone(), msg);
                }

            }
            RiskAlertReviewerModel sdf = new RiskAlertReviewerModel();
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            sdf.setReviewerUserId(sysUser.getId());
            RiskAlertReviewerVo reviewerVo = riskAlertReviewerService.getByIdVo(sdf);

            String title = "VOC风险警示邮件－1个风险点";
            Set<String> ids = new HashSet<>();
            for(VocRiskHandlingRecord e : model.getProcessors()) {
                if (e.isSendMail()) {
                    SysUser pu = sysUserService.getById(e.getProcessUserId());
                    String useinfo="";
                    if (e.getBindType()==1){
                        SysDepart pp = departService.getById(e.getProcessDepartId());
                        useinfo = pp.getDepartName() + "-" + pu.getRealname();
                    }else {
                        VocProjectGroup byId = projectGroupService.getById(e.getProcessDepartId());
                        useinfo = byId.getProjectName() + "-" + pu.getRealname();
                    }
                    ExchangeMailUtils exchangeMailUtils = new ExchangeMailUtils(model);
//                        if (StringUtils.isEmpty(e.getMailAddress()) || !e.getMailAddress().contains("csvw.com")) {
//                            throw new RuntimeException("邮箱格式不正确");
//                        }
                    String msg =  "尊敬的" + useinfo + "您好！" +"<br>"+
                            "东风汽车客户之声VOC系统洞察分析出1个警示风险点，由VOC运营（"+reviewerVo.getReviewerDepartName()+"/"+reviewerVo.getReviewerUserName()+"）确认并提示您进行处理，请登录VOC系统查阅处理。";
                    String[] a = new String[]{e.getMailAddress()};
                    String[] cc = new String[]{};
                    try {
                        if (ids.add(e.getMailAddress()) && StrUtil.isNotBlank(e.getMailChart())) {//确保不会重复发送
                            exchangeMailUtils.send(title, a, cc, e.getMailChart(),model.getPicturePath(),msg);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        throw new RuntimeException(ex);

                    }
                }
            }

        } catch (Exception e) {
            log.error("发送失败！");
            e.printStackTrace();
        }

        return true;
    }

    private void setGdrc_b(DwdVocRisk buRisk, StringBuffer gdrc) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setTopicCode(buRisk.getTopicCode());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        List<HighHotWordsVo> hots = emotionUserDiService.riskHotWordsOpinion(model1);
        List<String> hos = hots.stream().map(HighHotWordsVo::getHotWordStr).collect(Collectors.toList());
        if (hos != null && hos.size() > 0) {
            gdrc.append(String.join("、", hos));
        }
    }

    private void setGdrc_u(DwdVocUserRisk buRisk, StringBuffer gdrc) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setRiskUserId(buRisk.getUserId());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        List<HighHotWordsVo> hots = emotionUserDiService.riskHotWordsOpinion(model1);
        List<String> hos = hots.stream().map(HighHotWordsVo::getHotWordStr).collect(Collectors.toList());
        if (hos != null && hos.size() > 0) {
            gdrc.append(String.join("、", hos));
        }
    }

    private void setGdrc_q(DwdVocQualityRisk buRisk, StringBuffer gdrc) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setTopicCode(buRisk.getTopicCode());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        List<HighHotWordsVo> hots = qualityUserDiService.riskHotWordsOpinion(model1);
        List<String> hos = hots.stream().map(HighHotWordsVo::getHotWordStr).collect(Collectors.toList());
        if (hos != null && hos.size() > 0) {
            gdrc.append(String.join("、", hos));
        }
    }

    private void setSjcx_b(DwdVocRisk buRisk, StringBuffer sjcx) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setTopicCode(buRisk.getTopicCode());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));

        List<String> liss = emotionUserDiService.riskCarSeriesStr(model1);
        if (liss != null && liss.size() > 0) {
            sjcx.append(String.join("、", liss) + "等" + liss.size() + "个车系");
        }
    }

    private void setSjcx_u(DwdVocUserRisk buRisk, StringBuffer sjcx) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setRiskUserId(buRisk.getUserId());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        List<String> liss = emotionUserDiService.riskCarSeriesStr(model1);
        if (liss != null && liss.size() > 0) {
            sjcx.append(String.join("、", liss) + "等" + liss.size() + "个车系");
        }
    }

    private void setSjcx_q(DwdVocQualityRisk buRisk, StringBuffer sjcx) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setTopicCode(buRisk.getTopicCode());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        List<String> liss = qualityUserDiService.riskCarSeriesStr(model1);
        if (liss != null && liss.size() > 0) {
            sjcx.append(String.join("、", liss) + "等" + liss.size() + "个车系");
        }
    }

    public RiskEventInsightModel getRiskEventModel(String type, Date pud) {
        RiskEventInsightModel model1 = new RiskEventInsightModel();
        model1.setDateUnit(CalculatorUtils.periodStrToNum(type));
        model1.setStartDate(DateUtil.formatDateTime(pud));
        model1.setEndDate(CalculatorUtils.getRiskEndDate(model1.getDateUnit(), pud));
        model1.SetUpCycle();
        if (model1.getDateUnit() == -1) {
            model1.setStartDate(DateUtil.formatDateTime(pud));
            model1.setEndDate(DateUtil.formatDateTime(pud));
            model1.setEndDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(pud, -1)));
            model1.setStartDateUp(model1.getEndDateUp());
        }
        return model1;
    }


    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;

    public void setFmtjl_b(DwdVocRisk buRisk, StringBuffer fmtjl, StringBuffer fsyf) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setTopicCode(buRisk.getTopicCode());
        model1.setEmotion("负面");
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        VocOverBriefingValueVo re = emotionUserDiService.riskBriefingValue(model1);
        Map<String, BigDecimal> user = emotionUserDiService.riskUserNum(model1);
        Map<String, BigDecimal> total = emotionUserDiService.riskAllTotal(model1);
        String cycle = setAilRiskCycle(buRisk);
        String cyclep = cycle + "均";
        BigDecimal upr = CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp());
        String qoq = "，环比上" + cycle + "飙升";
        String uprr = "";
        if (upr != null && upr.compareTo(BigDecimal.ZERO) == 1) {
            if (upr.compareTo(new BigDecimal(999999))==0){
                uprr = qoq  + "+∞%";
            }else {
                uprr = qoq + upr + "%";
            }
        }
        if (re != null) {
            fmtjl.append(buRisk.getNegativeNum().longValue() + "条，高于" + cyclep + CalculatorUtils.proportion(buRisk.getNegativeNum(), total.get("statisticTotal")) + "%" + uprr);
        }
        if (user != null) {
            fsyf.append(user.get("userCount") + "位，高于" + cyclep);
            fsyf.append(CalculatorUtils.proportion(new BigDecimal(user.get("userCount") + ""), new BigDecimal(total.get("userTotal") + "")) + "%");
            Long uscou =new BigDecimal(user.get("userCount") + "").longValue();
            Long uscouup =new BigDecimal(user.get("userCountUp") + "").longValue();
            BigDecimal ur= CalculatorUtils.ringRatio(ObjectUtil.isNull(uscou) ? null : new BigDecimal(uscou), new BigDecimal(uscouup));
            String urr;
            if (ur.compareTo(new BigDecimal(999999))==0){
                urr = "+∞%";
            }else {
                urr =  ur + "%";
            }
            fsyf.append("" + qoq +urr);

        }
    }

    public void setFmtjl_u(DwdVocUserRisk buRisk, StringBuffer fmtjl, StringBuffer fsyf) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setRiskUserId(buRisk.getUserId());
        model1.setEmotion("负面");
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        VocOverBriefingValueVo re = emotionUserDiService.riskBriefingValue(model1);
        Map<String, BigDecimal> total = emotionUserDiService.riskAllTotal(model1);
        String cycle = setAilRiskCycle(buRisk);
        String cyclep = cycle + "均";
        String qoq = "，环比上" + cycle + "飙升";
        BigDecimal tjlr = CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp());
        String hb = "";
        if (tjlr != null && tjlr.compareTo(BigDecimal.ZERO) == 1) {
            if (tjlr.compareTo(new BigDecimal(999999))==0){
                hb = qoq  + "+∞%";
            }else {
                hb = qoq + tjlr + "%";

            }

        }
        if (re != null) {
            fmtjl.append(buRisk.getNegativeNum().longValue() + "条，高于" + cyclep + CalculatorUtils.proportion(buRisk.getNegativeNum(), total.get("statisticTotal")) + "%" + hb);
        }

    }

    @Autowired
    IDwsVocQualityUserDiService qualityUserDiService;


    public void setFmtjl_q(DwdVocQualityRisk buRisk, StringBuffer fmtjl, StringBuffer fsyf) {
        RiskEventInsightModel model1 = getRiskEventModel(buRisk.getStatisticType(), buRisk.getPublishDate());
        model1.setBrandCode(buRisk.getBrandCode());
        model1.setTopicCode(buRisk.getTopicCode());
        model1.setCreateDate(DateUtil.formatDateTime(buRisk.getCreateTime()));
        VocOverBriefingValueVo re = qualityUserDiService.riskBriefingValue(model1);
        Map<String, Object> user = qualityUserDiService.riskUserNum(model1);
        Map<String, BigDecimal> total = qualityUserDiService.riskAllTotal(model1);
        String cycle = setAilRiskCycle(buRisk);
        String cyclep = cycle + "均";
        String qoq = "，环比上" + cycle + "飙升";
        BigDecimal upr = CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp());
        String uprr = "";
        if (upr != null && upr.compareTo(BigDecimal.ZERO) == 1) {
            if (upr.compareTo(new BigDecimal(999999))==0){
                uprr = qoq  + "+∞%";
            }else {
                uprr = qoq + upr + "%";
            }
        }
        if (re != null) {
            fmtjl.append(buRisk.getTotalNum().longValue() + "条，高于" + cyclep + CalculatorUtils.proportion(buRisk.getTotalNum(), total.get("statisticTotal")) + "%" + uprr);
        }
        if (user != null) {
            fsyf.append(user.get("userCount") + "条，高于" + cyclep);
            fsyf.append(CalculatorUtils.proportion(new BigDecimal(user.get("userCount") + ""), new BigDecimal(total.get("userTotal") + "")) + "%");
            Long uscou =new BigDecimal(user.get("userCount") + "").longValue();
            Long uscouup =new BigDecimal(user.get("userCountUp") + "").longValue();
            BigDecimal upru = CalculatorUtils.ringRatio(ObjectUtil.isNull(uscou) ? null : new BigDecimal(uscou), new BigDecimal(uscouup));
            String uprru = "";
            if (upru != null && upru.compareTo(BigDecimal.ZERO) == 1) {
                if (upru.compareTo(new BigDecimal(999999))==0){
                    uprru = "+∞%";
                }else {
                    uprru =  upru + "%";
                }
                uprru = qoq + uprru;
            }

            fsyf.append(uprru);
        }

    }

    private String setAilRiskDate(VocRisk buRisk) {
        if ("d".equals(buRisk.getStatisticType())) {
            return DateUtil.formatDate(buRisk.getPublishDate());
        } else if ("w".equals(buRisk.getStatisticType())) {
            return buRisk.getDateYear() + "年" + buRisk.getDateWeek() + "周";
        } else if ("m".equals(buRisk.getStatisticType())) {
            return buRisk.getDateYear() + "年" + buRisk.getDateMonth() + "月";
        } else if ("q".equals(buRisk.getStatisticType())) {
            return buRisk.getDateYear() + "年" + buRisk.getDateQuarter() + "季";
        } else {
            return buRisk.getDateYear() + "年";
        }

    }

    private String setAilRiskCycle(VocRisk buRisk) {
        if ("d".equals(buRisk.getStatisticType())) {
            return "日";
        } else if ("w".equals(buRisk.getStatisticType())) {
            return "周";
        } else if ("m".equals(buRisk.getStatisticType())) {
            return "月";
        } else if ("q".equals(buRisk.getStatisticType())) {
            return "季";
        } else {
            return "年";
        }

    }
}
