package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocIntention;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.FocucLabelDetailModel;
import com.car.voc.common.Result;

/**
 *
 * @version 1.0.0
 * @ClassName IAdsVocFirstIntentionDiService.java
 * @Description TODO
 * @createTime 2022年10月13日 15:33
 * @Copyright voc
 */
public interface IDwsVocIntentionService extends IService<DwsVocIntention> {
    Result<?> overviewBriefingValue(FilterCriteriaModel model);

    Result<?> overviewMentionTrend(FilterCriteriaModel model);
    Result<?> emotionTrend(FilterCriteriaModel model);


    Result<?> overviewIntentionRatio(FilterCriteriaModel model);

    Result<?> overviewVehicleShare(FilterCriteriaModel model);


    Result<?> overviewCarTypeRato(FilterCriteriaModel model);

    Result<?> overviewEmotionalProportion(FilterCriteriaModel model);

    Result<?> overviewHighFrequencyWords(FilterCriteriaModel model);

    Result<?> overviewSoaringHotWords(FilterCriteriaModel model);


    Result<?> overviewFocusTop(FilterCriteriaModel model);

    Result<?> overviewFocusProportionEmotion(FocucLabelDetailModel model);
    Result<?> overviewFocusProportionEmotionThirdTop(FocucLabelDetailModel model);
    Result<?> overviewFocusProportionEmotionFourTop(FocucLabelDetailModel model);

    Result<?> overviewEmotionalTrends(FocucLabelDetailModel model);


    Result<?> energyClassification(FilterCriteriaModel model);

    Result<?> carSeriesDistribution(FilterCriteriaModel model);
}
