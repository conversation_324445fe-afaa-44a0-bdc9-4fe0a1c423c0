package com.car.stats.serivce.impl.wo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.mapper.wo.VehicleRescueMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.serivce.wo.VehicleRescueService;
import com.car.stats.vo.ProportionCarSeriesVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.*;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class VehicleRescueServiceImpl extends ServiceImpl<VehicleRescueMapper, WoOriginalData>
        implements VehicleRescueService {

    @Override
    public StatisticVo customerSatisfaction(FilterCriteriaModel model) {
        model.setStatus("closed");
        return baseMapper.customerSatisfaction(model);
    }

    @Override
    public WoRescueVolumeVo rescueStatus(FilterCriteriaModel model) {
        return baseMapper.rescueStatus(model);
    }

    @Override
    public List<WoRescueVolumeVo> rescueStatusTrend(FilterCriteriaModel model) {
        List<WoRescueVolumeVo> re = baseMapper.rescueStatusTrend(model);
        if (CollUtil.isNotEmpty(re)) {
            if (model.getDateUnit() == -1 && DateUtil.between(DateUtil.parseDate(model.getStartDate()), DateUtil.parse(model.getEndDate()), DateUnit.DAY) > 0) {
                re.sort(Comparator.comparing(WoRescueVolumeVo::getDateStr));
                return re;
            }
            re.remove(0);
        }
        return re;
    }

    @Override
    public List<ProportionCarSeriesVo> carSeriesTop(FilterCriteriaModel model) {
        List<ProportionCarSeriesVo> res = baseMapper.carSeriesTop(model);
        if (CollUtil.isNotEmpty(res) && res.size() == 1 && res.get(0).getStatistic() == null) {
            res.remove(0);
        }
        if (CollUtil.isNotEmpty(res) && model.getBrandCode().equals("A11")) {
            res = res.stream().peek(s -> {
                if (StrUtil.isBlank(s.getCarSeriesName())) {
                    s.setCarSeriesName("奕派-其他");
                }
            }).collect(Collectors.toList());
        }
        return res;
    }

    @Override
    public List<ProportionCarSeriesVo> carModelTop(FilterCriteriaModel model) {
        List<ProportionCarSeriesVo> res = baseMapper.carModelTop(model);
        if (CollUtil.isNotEmpty(res) && res.size() == 1 && res.get(0).getStatistic() == null) {
            res.remove(0);
        }
        return res;
    }

    @Override
    public List<BranchesVo> outletsTop(FilterCriteriaModel model) {
        return baseMapper.outletsTop(model);
    }

    @Override
    public Page<WoRescueVo> workOrderDetailsList(FilterCriteriaModel model) {
        IPage<WoRescueVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        return baseMapper.workOrderDetailsList(page, model);
    }

    @Override
    public List<WoCustomerTypeProportionVo> customerType(FilterCriteriaModel model) {
        List<WoCustomerTypeProportionVo> res = baseMapper.customerType(model).stream().peek(e -> {
            if (StrUtil.isBlank(e.getCustomerType())) {
                e.setCustomerType("未知");
            }
        }).collect(Collectors.toList());
        return res;
    }

    @Override
    public List<WoReceiverVo> receiver(FilterCriteriaModel model) {
        List<WoReceiverVo> receiver = baseMapper.receiver(model);
        if (CollUtil.isNotEmpty(receiver)) {
            return receiver.stream().filter(s -> !"关单".equals(s.getRescueType())).collect(Collectors.toList());
        }
        return receiver;
    }


}
