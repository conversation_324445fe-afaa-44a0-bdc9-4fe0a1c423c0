package com.car.stats.serivce.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocIntention;
import com.car.stats.mapper.DwsVocEmotionDiMapper;
import com.car.stats.mapper.DwsVocIntentionMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.FocucLabelDetailModel;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.IDwsVocIntentionService;
import com.car.stats.vo.*;
import com.car.stats.vo.risk.IntentionEmotionTrendVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.enums.SoundTypeEnum;
import com.car.voc.common.util.*;
import com.car.voc.service.ISysDictService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocChannelCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName AdsVocFirstIntentionDiServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月13日 15:33
 * @Copyright voc
 */
@Service
@Slf4j
public class DwsVocIntentionServiceImpl extends ServiceImpl<DwsVocIntentionMapper, DwsVocIntention> implements IDwsVocIntentionService {
    @Resource
    DwsVocIntentionMapper dwsVocIntentionMapper;
    @Autowired
    ISysDictService dictService;
    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Autowired
    IVocBusinessTagService tagService;

    @Autowired
    Executor defExecutor;
    @Override
    public Result<?> overviewBriefingValue(FilterCriteriaModel model) {
        model.SetUpCycle();
        VocOverBriefingValueVo re = emotionUserDiService.overviewBriefingValue(model);
        if (re == null) {
            re = new VocOverBriefingValueVo();
        }
        re.setTotalMentionsR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
        Map<String, Long> user = emotionUserDiService.userNum(model);
        if (user != null) {
            re.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
        }
        re.setUsersNumR(CalculatorUtils.ringRatio(re.getUsersNum(), CollUtil.isEmpty(user) ? null : BigDecimal.valueOf(user.get("userCountUp"))));
        List<ChannelStatisticVo> chans = emotionUserDiService.sourceChannel(model);
        if (model.isCPoint() || ((StrUtil.isNotBlank(model.getChannelId()) && !("1372001238165593852".equals(model.getChannelId()) || "1356178730703224804".equals(model.getChannelId()) || "1356178730703224803".equals(model.getChannelId()))))) {
            chans = chans.stream().filter(channelStatisticVo -> !("1356178730703224804".equals(channelStatisticVo.getDataSource()) || "1356178730703224803".equals(channelStatisticVo.getDataSource())) && model.getChannelIds().contains(channelStatisticVo.getDataSource())).collect(Collectors.toList());
        }
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<ChannelStatisticVo> chansup = emotionUserDiService.sourceChannel(model);
        if (model.isCPoint() || ((StrUtil.isNotBlank(model.getChannelId()) && !("1372001238165593852".equals(model.getChannelId()) || "1356178730703224804".equals(model.getChannelId()) || "1356178730703224803".equals(model.getChannelId()))))) {
            chansup = chansup.stream().filter(channelStatisticVo -> !("1356178730703224804".equals(channelStatisticVo.getDataSource()) || "1356178730703224803".equals(channelStatisticVo.getDataSource())) && model.getChannelIds().contains(channelStatisticVo.getDataSource())).collect(Collectors.toList());
        }

        if (chans != null) {
            re.setChannelNum(BigDecimal.valueOf(chans.size()));
            if (chansup != null) {
                re.setChannelNumR(new BigDecimal(chans.size() - chansup.size()));
            }
        }

        List<String> chStr = new ArrayList<>();
        for (int i = 0; i < chans.size(); i++) {
            chStr.add(chans.get(i).getDataSource());
        }
        re.setChannelTopStr(chStr);

        return Result.OK(re);
    }

    @Override
    public Result<?> overviewMentionTrend(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        model.SetUpCycle();
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        List<IntentionTrendVo> list = new ArrayList<>();
        model.setStartDate(dates.get(0).getStartDate());
        model.setEndDate(dates.get(dates.size() - 1).getEndDate());
        List<IntentionTrendVo> objectLists = dwsVocIntentionMapper.mentionGroupByDate(model);

        if (CollectionUtils.isEmpty(objectLists)) {
            return Result.OK(objectLists);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateTime(this.coveringDate(e.getDateTime()));
                    return e.getDateTime();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());



        /*final Map<String, List<IntentionTrendVo>> rsMap = objectLists.stream().collect(Collectors.groupingBy(IntentionTrendVo::getDateTime));
        List<IntentionTrendVo> objects = rsMap.keySet().stream().map( dateStr -> {
            IntentionTrendVo vo = new IntentionTrendVo();
            vo.setDateTime(dateStr);
            return vo;
        }).collect(Collectors.toList());*/


        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    IntentionTrendVo vo = new IntentionTrendVo();
                    vo.setDateTime(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(IntentionTrendVo::getDateTime, Comparator.nullsFirst(String::compareTo)));

        //计算
        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            IntentionTrendVo obj = objectLists.get(i);  //当前
            IntentionTrendVo preObj = objectLists.get(i - 1);   //前一个
            /*
            final Long days = DateUtil.between(DateUtil.parse(model.getEndDate()), DateUtil.parse(model.getStartDate()), DateUnit.DAY) + 1;
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setStatisticAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getStatistic(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getStatistic(), new BigDecimal(days));
                obj.setStatisticA(objAvgDays);
                obj.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }*/


            obj.setTotalR(CalculatorUtils.ringRatio(obj.getTotal(), preObj.getTotal()));
            obj.setConsulR(CalculatorUtils.ringRatio(obj.getConsul(), preObj.getConsul()));
            obj.setPraiseR(CalculatorUtils.ringRatio(obj.getPraise(), preObj.getPraise()));
            obj.setComplaintR(CalculatorUtils.ringRatio(obj.getComplaint(), preObj.getComplaint()));
        }


        //时间排序
        objectLists.sort(Comparator.comparing(IntentionTrendVo::getDateTime, Comparator.nullsFirst(String::compareTo)));
        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateTime(e.getDateTime().replaceAll("-", "/"));
                    } else {
                        e.setDateTime(this.coveringDate2(e.getDateTime()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s > objectLists.size() ? 1 : s - index, objectLists.size()));
        }

        return Result.OK(objectLists);

    }


    @Override
    public Result<?> emotionTrend(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        model.SetUpCycle();

        final int index = 6;
//        List<SvwDate> dates = model.getDateTimes();
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        List<DateTrendEmotionVo> objectLists = dwsVocIntentionMapper.emotionGroupByDate(model);
        if (CollUtil.isEmpty(objectLists)) {
            return Result.OK(Collections.EMPTY_LIST);
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    if (e.getDateTime().split(" ").length > 1) {
                        e.setDateStr(e.getDateTime().split(" ")[0]);
                        e.setDateStr(this.coveringDate(e.getDateStr()));
                    } else {
                        e.setDateStr(this.coveringDate(e.getDateTime()));
                    }
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    DateTrendEmotionVo vo = new DateTrendEmotionVo();
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(DateTrendEmotionVo::getDateStr, Comparator.nullsFirst(String::compareTo)));


        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            DateTrendEmotionVo obj = objectLists.get(i);  //当前
            DateTrendEmotionVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());

//            final Long days = DateUtil.between(DateUtil.parse(model.getEndDate()), DateUtil.parse(model.getStartDate()),DateUnit.DAY) +1;
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setPositiveAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getPositive(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getPositive(), new BigDecimal(preDays));
                obj.setPositiveA(objAvgDays);
                obj.setPositiveAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setNegativeAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getNegative(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getNegative(), new BigDecimal(preDays));
                obj.setNegativeA(objAvgDays);
                obj.setNegativeAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setNeutralAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getNeutral(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getNeutral(), new BigDecimal(preDays));
                obj.setNeutralA(objAvgDays);
                obj.setNeutralAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setTotalAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getTotal(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getTotal(), new BigDecimal(preDays));
                obj.setTotalA(objAvgDays);
                obj.setTotalAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }

            obj.setTotalR(CalculatorUtils.ringRatio(obj.getTotal(), preObj.getTotal()));
            obj.setPositiveR(CalculatorUtils.ringRatio(obj.getPositive(), preObj.getPositive()));
            obj.setNegativeR(CalculatorUtils.ringRatio(obj.getNegative(), preObj.getNegative()));
            obj.setNeutralR(CalculatorUtils.ringRatio(obj.getNeutral(), preObj.getNeutral()));

//            obj.setTotalAR(CalculatorUtils.ringRatio(obj.getTotalA(), preObj.getTotalA()));
//            obj.setPositiveAR(CalculatorUtils.ringRatio(obj.getPositiveA(), preObj.getPositiveA()));
//            obj.setNegativeAR(CalculatorUtils.ringRatio(obj.getNegativeA(), preObj.getNegativeA()));
//            obj.setNeutralAR(CalculatorUtils.ringRatio(obj.getNeutralA(), preObj.getNeutralA()));
        }


        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s >= index ? 1 : s - index, objectLists.size()));
        }

        return Result.OK(objectLists);

    }

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public Result<?> overviewIntentionRatio(FilterCriteriaModel model) {
        model.SetUpCycle();
        IntentionRatioVo re = dwsVocIntentionMapper.overviewIntentionRatio(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        IntentionRatioVo reup = dwsVocIntentionMapper.overviewIntentionRatio(model);
        if (re != null) {
            re.setPraiseP(CalculatorUtils.proportion(re.getPraise(), re.getTotal()));
            re.setComplaintP(CalculatorUtils.proportion(re.getComplaint(), re.getTotal()));
            re.setOtherP(CalculatorUtils.proportion(re.getOther(), re.getTotal()));
            re.setConsultP(CalculatorUtils.proportion(re.getConsult(), re.getTotal()));
            re.setSuggestP(CalculatorUtils.proportion(re.getSuggest(), re.getTotal()));
//            if (reup != null) {
            re.setPraiseR(CalculatorUtils.ringRatio(re.getPraise(), ObjectUtil.isNull(reup) ? null : reup.getPraise()));
            re.setComplaintR(CalculatorUtils.ringRatio(re.getComplaint(), ObjectUtil.isNull(reup) ? null : reup.getComplaint()));
            re.setConsultR(CalculatorUtils.ringRatio(re.getConsult(), ObjectUtil.isNull(reup) ? null : reup.getConsult()));
            re.setOtherR(CalculatorUtils.ringRatio(re.getOther(), ObjectUtil.isNull(reup) ? null : reup.getOther()));
            re.setSuggestR(CalculatorUtils.ringRatio(re.getSuggest(), ObjectUtil.isNull(reup) ? null : reup.getSuggest()));
//            }

        } else {
            re = new IntentionRatioVo();
        }

        return Result.OK(re);
    }

    @Override
    public Result<?> overviewVehicleShare(FilterCriteriaModel model) {
        List<DateCarSeriespVo> rel = new ArrayList<>();

        List<SvwDate> dates = model.getDateTimes();


        int index = model.isExcel() ? model.getDownloadNum() : 6;
        dates = dates.subList(dates.size() >= index ? dates.size() - index : 0, dates.size());

        model.setStartDate(dates.get(0).getStartDate());
        model.setEndDate(dates.get(dates.size() - 1).getEndDate());
        List<ProportionCarSeriesVo> list = dwsVocIntentionMapper.overviewVehicleShare(model);
        Map<String, List<ProportionCarSeriesVo>> maps = list.stream().filter(s -> s.getDateTime() != null && !"".equals(s.getDateTime())).collect(Collectors.groupingBy(ProportionCarSeriesVo::getDateTime));
        List<String> mapKey = model.getMapKey(dates);
        if (model.getDateUnit() == 3) {
            mapKey = mapKey.stream().sorted(Comparator.naturalOrder()).map(String::valueOf).collect(Collectors.toList());
        } else {
            mapKey = mapKey.stream().sorted(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    Date d1 = IDateUtils.parse(IDateUtils.convertTime(o1), Constants.YYYY_MM_DD_HH_MM_SS);
                    Date d2 = IDateUtils.parse(IDateUtils.convertTime(o2), Constants.YYYY_MM_DD_HH_MM_SS);
                    return d1.compareTo(d2);
                }
            }).collect(Collectors.toList());
        }


        mapKey.stream().forEach(dataTime -> {
            DateCarSeriespVo re = new DateCarSeriespVo();
            String beforeDate = DateUtils.getBeforeTime(dataTime, model.getDateUnit());
            List<ProportionCarSeriesVo> reda = maps.get(dataTime);
            if (reda == null) {

                return;
            }
            BigDecimal total = reda.stream().map(ProportionCarSeriesVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            List<ProportionCarSeriesVo> redup = maps.get(beforeDate);
            if (redup != null) {
                // 使用新的批量占比计算方法
                CalculatorUtils.proportion(reda, ProportionCarSeriesVo::getStatistic, ProportionCarSeriesVo::setProportion);

                // 处理环比计算
                reda.forEach(e ->
                        {
                            ProportionCarSeriesVo carSeriesVo = redup.stream().filter(c -> e.getCarSeriesCode().equals(c.getCarSeriesCode())).findFirst().orElse(null);
                            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(carSeriesVo) ? null : carSeriesVo.getStatistic()));
                        }
                );
            } else {
                // 使用新的批量占比计算方法
                CalculatorUtils.proportion(reda, ProportionCarSeriesVo::getStatistic, ProportionCarSeriesVo::setProportion);
            }
            re.setDateStr(model.getDateUnit() == -1 ? IDateUtils.convertTime(dataTime, Constants.YYYYMMDD, false) :
                    (model.getDateUnit() == 1 ? IDateUtils.convertTime(dataTime, Constants.YYYYMMDD) : dataTime));
            ProportionCarSeriesVo onet = reda.stream().filter(d -> "其他".equals(d.getCarSeriesName())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (onet != null) {
                reda.remove(onet);
                reda.add(onet);
            }
            re.setList(reda);
            rel.add(re);
        });
        if (rel.stream().filter(s -> s.getList() != null).findFirst().orElse(null) == null) {
            return Result.OK(null);
        }

        if (rel.size() >= index) {
            rel.remove(0);
        }
        return Result.OK(rel);
    }


    @Override
    public Result<?> overviewCarTypeRato(FilterCriteriaModel model) {
        List<CarTypeProportionVo> re = new ArrayList<>();
        VocOverBriefingValueVo totas = emotionUserDiService.overviewBriefingTotalValue(model);

        re = dwsVocIntentionMapper.overviewCarTypeRato(model);
        // 使用新的批量占比计算方法 - 这里需要特殊处理，因为分母是固定值
        BigDecimal totalMentions = totas.getTotalMentions();
        re.forEach(e -> {
            e.setProportion(CalculatorUtils.proportion(e.getStatistic(), totalMentions));
        });

        return Result.OK(re);
    }

    @Override
    public Result<?> overviewEmotionalProportion(FilterCriteriaModel model) {
        List<TrendEmotionVo> lsis = new ArrayList<>();
        List<SvwDate> dates = model.getDateTimes();
        int index = model.isExcel() ? model.getDownloadNum() : 6;
        boolean isPos = model.getEmotions().contains("正面");
        boolean isNeg = model.getEmotions().contains("负面");
        boolean isNeu = model.getEmotions().contains("中性");
        dates.subList(dates.size() <= index ? 0 : dates.size() - index, dates.size()).forEach(e -> {
            model.setStartDate(e.getStartDate());
            model.setEndDate(e.getEndDate());
            TrendEmotionVo emotionVo = dwsVocIntentionMapper.overviewEmotionalProportion(model);
            if (emotionVo == null) {
                emotionVo = new TrendEmotionVo();
            } else {
                BigDecimal total = new BigDecimal(0);
                if (isPos) {
                    total = total.add(emotionVo.getPositive());
                }
                if (isNeg) {
                    total = total.add(emotionVo.getNegative());
                }
                if (isNeu) {
                    total = total.add(emotionVo.getNeutral());
                }
                emotionVo.setPositiveP(CalculatorUtils.proportion(emotionVo.getPositive(), total));
                emotionVo.setNegativeP(CalculatorUtils.proportion(emotionVo.getNegative(), total));
                emotionVo.setNeutralP(CalculatorUtils.proportion(emotionVo.getNeutral(), total));
                if (!isPos) {
                    emotionVo.setPositive(null);
                    emotionVo.setPositiveP(null);
                }
                if (!isNeg) {
                    emotionVo.setNegative(null);
                    emotionVo.setNegativeP(null);
                }
                if (!isNeu) {
                    emotionVo.setNeutralP(null);
                    emotionVo.setNeutral(null);
                }
            }

            emotionVo.setDateStr(e.getTime());
            lsis.add(emotionVo);
        });
        DateEmotionTotalVo re = new DateEmotionTotalVo();

        BigDecimal posi = lsis.stream().map(TrendEmotionVo::getPositive).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal nega = lsis.stream().map(TrendEmotionVo::getNegative).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal neut = lsis.stream().map(TrendEmotionVo::getNeutral).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        re.setPositive(posi);
        re.setNegative(nega);
        re.setNeutral(neut);
        BigDecimal total = NumberUtil.add(posi, nega, neut);
        if (total.equals(new BigDecimal(0))) {
        }
        re.setPositiveP(CalculatorUtils.proportion(re.getPositive(), total));
        re.setNegativeP(CalculatorUtils.proportion(re.getNegative(), total));
        re.setNeutralP(CalculatorUtils.proportion(re.getNeutral(), total));
        if (total.longValue() > 0) {
            re.setTrends(lsis);
        }
        return Result.OK(re);
    }

    @Override
    public Result<?> overviewHighFrequencyWords(FilterCriteriaModel model) {
        model.setRownum(model.isExcel() ? model.getDownloadHotWordsNum() : null);
        List<HighHotWordsVo> hotWordsVos = dwsVocIntentionMapper.overviewHighFrequencyWords(model);
        return Result.OK(hotWordsVos);
    }

    @Override
    public Result<?> overviewSoaringHotWords(FilterCriteriaModel model) {
        model.SetUpCycle();
        model.setRownum(1000);
        List<HighHotWordsVo> hotWordsVos = dwsVocIntentionMapper.overviewHighFrequencyWords(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setHotWords(hotWordsVos.stream().map(HighHotWordsVo::getKeyword).collect(Collectors.toSet()));
        List<HighHotWordsVo> hotWordsVosUP = dwsVocIntentionMapper.overviewHighFrequencyWords(model);

        BigDecimal btotal = hotWordsVos.stream().map(HighHotWordsVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<HighHotWordsVo> avgbefre;
        if (hotWordsVos != null && hotWordsVos.size() > 0) {
            avge = NumberUtil.div(btotal, hotWordsVos.size());
            avgbefre = hotWordsVos.stream().filter(e -> (e.getStatistic().compareTo(avge)) > 0).collect(Collectors.toList());
        } else {
            avgbefre = hotWordsVos;
        }
        List<HighHotWordsVo> jihe = avgbefre;
        jihe.forEach(e -> {
            List<HighHotWordsVo> onet = hotWordsVosUP.stream().filter(d -> e.getKeyword().equals(d.getKeyword())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(onet)) {
                e.setSoaringRate(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
            }

        });
        jihe = jihe.stream().filter(e -> e.getSoaringRate().floatValue() > 0).collect(Collectors.toList());
        if (model.getDataType().equals(DataEnum.numMention)) {
            jihe.sort(Comparator.comparing(HighHotWordsVo::getSoaringRate, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(HighHotWordsVo::getStatistic)
                    .thenComparing(HighHotWordsVo::getKeyword)
                    .reversed());
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            jihe.sort(Comparator.comparing(HighHotWordsVo::getSoaringRate, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(HighHotWordsVo::getUserCount)
                    .thenComparing(HighHotWordsVo::getKeyword)
                    .reversed());
        }


        int index = model.isExcel() ? model.getDownloadHotWordsNum() : 50;
        if (jihe.size() >= index) {
            return Result.OK(new ArrayList<>(jihe.subList(0, index)));
        } else {
            return Result.OK(jihe);
        }
    }


    @Override
    public Result<?> overviewFocusTop(FilterCriteriaModel model) {
        model.setRownum(model.isExcel() ? model.getDownloadTagNum() : null);
        List<LabelEmotionTopVo> res = dwsVocIntentionMapper.overviewFocusTop(model);
        boolean isPos = model.getEmotions().contains("正面");
        boolean isNeg = model.getEmotions().contains("负面");
        boolean isNeu = model.getEmotions().contains("中性");
        res.forEach(e -> {
            BigDecimal total = new BigDecimal(0);
            if (isPos) {
                total = total.add(e.getPositive());
            }
            if (isNeg) {
                total = total.add(e.getNegative());
            }
            if (isNeu) {
                total = total.add(e.getNeutral());
            }
            e.setTotal(total);
            e.setNegativeP(CalculatorUtils.proportion(e.getNegative(), total));
            e.setPositiveP(CalculatorUtils.proportion(e.getPositive(), total));
            e.setNeutralP(CalculatorUtils.proportion(e.getNeutral(), total));
            if (!isPos) {
                e.setPositive(null);
                e.setPositiveP(null);
            }
            if (!isNeg) {
                e.setNegative(null);
                e.setNegativeP(null);
            }
            if (!isNeu) {
                e.setNeutralP(null);
                e.setNeutral(null);
            }
        });
        res = res.stream().filter(s -> s.getTotal().intValue() > 0).collect(Collectors.toList());
        res.sort(Comparator.comparing(LabelEmotionTopVo::getTotal, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(LabelEmotionTopVo::getPositive, Comparator.nullsFirst(BigDecimal::compareTo))
                .reversed());

        if (res.size() > 20) {
            res = res.subList(0, 20);
        }
        return Result.OK(res);
    }

    @Override
    public Result<?> overviewFocusProportionEmotion(FocucLabelDetailModel model) {
        model.SetUpCycle();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDate());

        List<EmotionProportionVo> list = dwsVocIntentionMapper.overviewFocusProportionEmotion(model);
        String startTime = DateUtils.getTimeFormat(model.getStartDateUp(), model.getDateUnit());
        String endTime = DateUtils.getTimeFormat(model.getEndDate(), model.getDateUnit());
        EmotionProportionVo re = list.stream().filter(s -> s.getDateTime().equals(endTime)).findFirst().orElse(null);
        if (re == null) {
            re = new EmotionProportionVo();
        } else {
            re.setTotal(NumberUtil.add(re.getNegative(), re.getPositive(), re.getNeutral()));
        }
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        EmotionProportionVo reUp = list.stream().filter(s -> s.getDateTime().equals(startTime)).findFirst().orElse(null);
        if (reUp == null) {
            reUp = new EmotionProportionVo();
        }
        re.setNegativeP(CalculatorUtils.proportion(re.getNegative(), re.getTotal()));
        re.setNeutralP(CalculatorUtils.proportion(re.getNeutral(), re.getTotal()));
        re.setPositiveP(CalculatorUtils.proportion(re.getPositive(), re.getTotal()));
        re.setPositiveR(CalculatorUtils.getPositiveRate(re.getPositive(), re.getNegative()));
        re.setNegativeR(CalculatorUtils.getPositiveRate(re.getNegative(), re.getPositive()));
        re.setNegativeTR(NumberUtil.sub(re.getNegativeR(), CalculatorUtils.getPositiveRate(reUp.getNegative(), reUp.getPositive())));
        re.setPositiveTR(NumberUtil.sub(re.getPositiveR(), CalculatorUtils.getPositiveRate(reUp.getPositive(), reUp.getNegative())));
        return Result.OK(re);
    }

    @Override
    public Result<?> overviewFocusProportionEmotionThirdTop(FocucLabelDetailModel model) {
        List<LabelVo> grouThree = dwsVocIntentionMapper.threeLabelsTop(model);
        Map<String, List<LabelVo>> map = grouThree.stream().collect(Collectors.groupingBy(LabelVo::getEmotionType));
        Map<String, List<LabelVo>> finalMap = map;
        int index = model.isExcel() ? model.getDownloadTagNum() : 5;
        map.keySet().stream().forEach(key -> {
            List<LabelVo> labelVos = finalMap.get(key);
            BigDecimal positotal = labelVos.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            // 使用新的批量占比计算方法
            CalculatorUtils.proportion(labelVos, LabelVo::getStatistic, LabelVo::setStatisticP);
            if (labelVos.size() >= index) {
                labelVos = labelVos.subList(0, index);
            }
            finalMap.put(key, labelVos);
        });

        return Result.OK(finalMap);
    }

    @Override
    public Result<?> overviewFocusProportionEmotionFourTop(FocucLabelDetailModel model) {
        List<LabelVo> grouFour = dwsVocIntentionMapper.fourLabelsTop(model);
        Map<String, List<LabelVo>> map = grouFour.stream().collect(Collectors.groupingBy(LabelVo::getEmotionType));
        Map<String, List<LabelVo>> finalMap = map;
        int index = model.isExcel() ? model.getDownloadTagNum() : 5;

        map.keySet().stream().forEach(key -> {
            List<LabelVo> labelVos = finalMap.get(key);

            if (labelVos.size() >= index) {
                labelVos = labelVos.subList(0, index);
            }
            finalMap.put(key, labelVos);
        });
        return Result.OK(finalMap);
    }

    @Override
    public Result<?> overviewEmotionalTrends(FocucLabelDetailModel model) {
        //修改环比计算+时间范围补齐
        List<DateTrendEmotionVo> objectLists = new ArrayList<>();
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            DateTrendEmotionVo emotionVo = dwsVocIntentionMapper.overviewEmotionalTrends(model);
            if (emotionVo == null) {
                continue;
            }
            emotionVo.setDateStr(date.getTime());
            if (model.getDateUnit() != -1) {//设置日均 averagePerDay()
                Long day = DateUtils.getDays(model.getEndDate(), model.getStartDate()) + 1;

                emotionVo.setAveragePerDay(model.getDateUnit(), model.getEndDate(), day);
            }

            /*if (a > 0) {
                emotionVo.setNegativeP(CalculatorUtils.proportion(emotionVo.getNegative(), emotionVo.getTotal()));
                emotionVo.setPositiveP(CalculatorUtils.proportion(emotionVo.getPositive(), emotionVo.getTotal()));
                emotionVo.setNeutralP(CalculatorUtils.proportion(emotionVo.getNeutral(), emotionVo.getTotal()));
                emotionVo.setNegativeR(CalculatorUtils.ringRatio(emotionVo.getNegative(), CollUtil.isEmpty(lsis) ? null : lsis.get(a - 1).getNegative()));
                emotionVo.setPositiveR(CalculatorUtils.ringRatio(emotionVo.getPositive(), CollUtil.isEmpty(lsis) ? null : lsis.get(a - 1).getPositive()));
                emotionVo.setNeutralR(CalculatorUtils.ringRatio(emotionVo.getNeutral(), CollUtil.isEmpty(lsis) ? null : lsis.get(a - 1).getNeutral()));
            }*/
            emotionVo.setDateStr(date.getTime());
            objectLists.add(emotionVo);
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    DateTrendEmotionVo vo = new DateTrendEmotionVo();
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(DateTrendEmotionVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            DateTrendEmotionVo obj = objectLists.get(i);  //当前
            DateTrendEmotionVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
//            final Long days = DateUtil.between(DateUtil.parse(model.getEndDate()), DateUtil.parse(model.getStartDate()),DateUnit.DAY) +1;

            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setPositiveAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getPositive(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getPositive(), new BigDecimal(preDays));
                obj.setPositiveA(objAvgDays);
                obj.setPositiveAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setNeutralAR(BigDecimal.ZERO);
            }else{

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getNeutral(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getNeutral(), new BigDecimal(days));
                obj.setNeutralA(objAvgDays);
                obj.setNeutralAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setNegativeAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getNegative(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getNegative(), new BigDecimal(days));
                obj.setNegativeA(objAvgDays);
                obj.setNegativeAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setTotalAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getTotal(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getTotal(), new BigDecimal(days));
                obj.setTotalA(objAvgDays);
                obj.setTotalAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            final BigDecimal total = NumberUtil.add(obj.getPositive(), obj.getNeutral(), obj.getNegative());
            obj.setTotal(total);

            obj.setNegativeP(CalculatorUtils.proportion(obj.getNegative(), total));
            obj.setPositiveP(CalculatorUtils.proportion(obj.getPositive(), total));
            obj.setNeutralP(CalculatorUtils.proportion(obj.getNeutral(), total));
            obj.setTotalP(CalculatorUtils.proportion(obj.getTotal(), total));


            obj.setNegativeR(CalculatorUtils.ringRatio(obj.getNegative(), preObj.getNegative()));
            obj.setPositiveR(CalculatorUtils.ringRatio(obj.getPositive(), preObj.getPositive()));
            obj.setNeutralR(CalculatorUtils.ringRatio(obj.getNeutral(), preObj.getNeutral()));
            obj.setTotalR(CalculatorUtils.ringRatio(obj.getTotal(), preObj.getTotal()));
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s > objectLists.size() ? 1 : s - index, objectLists.size()));
        }
        return Result.OK(objectLists);
    }


    @Override
    public Result<?> energyClassification(FilterCriteriaModel model) {
        VocOverBriefingValueVo re = emotionUserDiService.overviewBriefingTotalValue(model);

        if (model.getSoundType().equals(SoundTypeEnum.emotion)) {
            List<CarGroupEmotionProportionVo> emotions = dwsVocIntentionMapper.energyClassification(model);
            emotions.forEach(e -> {
                if (model.getDataType() == DataEnum.numUsers) {
                    e.setEmotionTotal(NumberUtil.add(e.getNegative(), e.getNeutral(), e.getPositive()));
                    e.setPositiveP(CalculatorUtils.proportion(e.getPositive(), e.getEmotionTotal()));
                    e.setNegativeP(CalculatorUtils.proportion(e.getNegative(), e.getEmotionTotal()));
                    e.setNeutralP(CalculatorUtils.proportion(e.getNeutral(), e.getEmotionTotal()));
                }

                e.setProportion(CalculatorUtils.proportion(e.getEmotionTotal(), re.getTotalMentions()));
            });
            BigDecimal total = emotions.stream().map(CarGroupEmotionProportionVo::getEmotionTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 使用新的批量占比计算方法
            CalculatorUtils.proportion(emotions, CarGroupEmotionProportionVo::getEmotionTotal, CarGroupEmotionProportionVo::setProportion);
            return Result.OK(emotions);
        } else if (model.getSoundType().equals(SoundTypeEnum.intention)) {
            List<CarGroupIntentionsProportionVo> intentions = dwsVocIntentionMapper.energyClassificationIntentions(model);
            intentions.forEach(e -> {
                if (model.getDataType() == DataEnum.numUsers) {
                    e.setIntentionsTotal(NumberUtil.add(e.getPraise(), e.getComplaint(), e.getConsult(), e.getOther()));
                    e.setPraiseP(CalculatorUtils.proportion(e.getPraise(), e.getIntentionsTotal()));
                    e.setComplaintP(CalculatorUtils.proportion(e.getComplaint(), e.getIntentionsTotal()));
                    e.setConsultP(CalculatorUtils.proportion(e.getConsult(), e.getIntentionsTotal()));
                    e.setOtherP(CalculatorUtils.proportion(e.getOther(), e.getIntentionsTotal()));
                }
                e.setProportion(CalculatorUtils.proportion(e.getIntentionsTotal(), re.getTotalMentions()));
            });
            BigDecimal total = intentions.stream().map(CarGroupIntentionsProportionVo::getIntentionsTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 使用新的批量占比计算方法
            CalculatorUtils.proportion(intentions, CarGroupIntentionsProportionVo::getIntentionsTotal, CarGroupIntentionsProportionVo::setProportion);
            return Result.OK(intentions);
        } else {
            return Result.OK();
        }
    }

    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;

    @Override
    public Result<?> carSeriesDistribution(FilterCriteriaModel model) {
//        VocOverBriefingValueVo re1 = dwsVocEmotionDiMapper.overviewBriefingValue(model);
//        Map<String, Long> user1 = emotionUserDiService.userNum(model);
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
//        if (re1 == null) {
//            re1 = new VocOverBriefingValueVo();
//        }
//        if (model.getDataType().equals(DataEnum.numMention)) {
//            total1.set(re1.getTotalMentions());
//        } else if (model.getDataType().equals(DataEnum.numUsers)) {
//            if (user1 != null) {
//                re1.setUsersNum(BigDecimal.valueOf(user1.get("userCount")));
//                total1.set(re1.getUsersNum());
//            }
//        }

        log.info("前端传值:{}", model);
        if (model.getSoundType() == null) {
            model.setSoundType(SoundTypeEnum.emotion);
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        if (model.getSoundType().equals(SoundTypeEnum.emotion)) {
            List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
            AtomicReference<List<CarEmotionVo>> ls = new AtomicReference<>();
            AtomicReference<List<CarEmotionVo>> lsUp = new AtomicReference<>();


            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                FilterCriteriaModel target =new FilterCriteriaModel();
                BeanUtils.copyProperties(model, target);

                VocOverBriefingValueVo re1 = dwsVocEmotionDiMapper.overviewBriefingValue(target);
                if (re1 == null) {
                    re1 = new VocOverBriefingValueVo();
                }
                if (target.getDataType().equals(DataEnum.numMention)) {
                    //提及量维度
                    total.set(re1.getTotalMentions());
                } else if (target.getDataType().equals(DataEnum.numUsers)) {
                    Map<String, Long> user1 = emotionUserDiService.userNum(target);
                    if (user1 != null) {
                        //用户数维度
                        re1.setUsersNum(BigDecimal.valueOf(user1.get("userCount")));
                        total.set(re1.getUsersNum());
                    }
                }
                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                FilterCriteriaModel target =new FilterCriteriaModel();
                BeanUtils.copyProperties(model, target);
                target.setRownum(10);
                ls.set(dwsVocIntentionMapper.carSeriesDistributionEmotion(target));

                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                FilterCriteriaModel target =new FilterCriteriaModel();
                BeanUtils.copyProperties(model, target);

                target.SetUpCycle();
                target.setStartDate(target.getStartDateUp());
                target.setEndDate(target.getEndDateUp());
                target.setRownum(null);
                lsUp.set(dwsVocIntentionMapper.carSeriesDistributionEmotion(target));

                return null;
            })));

            try {
                CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(30, TimeUnit.SECONDS);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            /*model.setRownum(10);
            List<CarEmotionVo> ls = dwsVocIntentionMapper.carSeriesDistributionEmotion(model);
            model.SetUpCycle();
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            model.setRownum(null);
            List<CarEmotionVo> lsUp = dwsVocIntentionMapper.carSeriesDistributionEmotion(model);*/
//            BigDecimal finalTotal1 = total;
            ls.get().forEach(e -> {
                CarEmotionVo k = lsUp.get().stream().filter(o -> e.getCarSeries().equals(o.getCarSeries())).findFirst().orElse(null);
                /*if (k != null) {
                    e.setTotalR(CalculatorUtils.ringRatio(e.getTotal(), k.getTotal()));
                    e.setPositiveR(CalculatorUtils.ringRatio(e.getPositive(),k.getPositive()));
                    e.setNegativeR(CalculatorUtils.ringRatio(e.getNegative(),k.getNegative()));
                    e.setNeutralR(CalculatorUtils.ringRatio(e.getNeutral(),k.getNeutral()));
                }*/
                e.setTotalR(CalculatorUtils.ringRatio(e.getTotal(), ObjectUtil.isNull(k) ? null : k.getTotal()));
                //正面环比
                e.setPositiveR(CalculatorUtils.ringRatio(e.getPositive(), ObjectUtil.isNull(k) ? null : k.getPositive()));
                //负面环比
                e.setNegativeR(CalculatorUtils.ringRatio(e.getNegative(), ObjectUtil.isNull(k) ? null : k.getNegative()));
                //中性环比
                e.setNeutralR(CalculatorUtils.ringRatio(e.getNeutral(), ObjectUtil.isNull(k) ? null : k.getNeutral()));
                e.setTotalP(CalculatorUtils.proportion(e.getTotal(), total.get()));
//                e.setTotal(finalTotal1);
            });

            if (ls.get().size() >= 10) {
                ls.set(ls.get().subList(0, ObjectUtil.isNotEmpty(model.getRownum()) ? model.getRownum() : 10));
            }
            return Result.OK(ls.get());

        } else if (model.getSoundType().equals(SoundTypeEnum.intention)) {

            List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
            AtomicReference<List<CarIntentionVo>> ls = new AtomicReference<>();
            AtomicReference<List<CarIntentionVo>> lsUp = new AtomicReference<>();
            model.setAccessToken(token);

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                FilterCriteriaModel target =new FilterCriteriaModel();
                BeanUtils.copyProperties(model, target);

                VocOverBriefingValueVo re1 = dwsVocEmotionDiMapper.overviewBriefingValue(target);
                if (re1 == null) {
                    re1 = new VocOverBriefingValueVo();
                }
                if (target.getDataType().equals(DataEnum.numMention)) {
                    //提及量维度
                    total.set(re1.getTotalMentions());
                } else if (target.getDataType().equals(DataEnum.numUsers)) {
                    Map<String, Long> user1 = emotionUserDiService.userNum(target);
                    if (user1 != null) {
                        //用户数维度
                        re1.setUsersNum(BigDecimal.valueOf(user1.get("userCount")));
                        total.set(re1.getUsersNum());
                    }
                }

                return null;
            })));
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                FilterCriteriaModel target =new FilterCriteriaModel();
                BeanUtils.copyProperties(model, target);
                target.setRownum(10);
                ls.set(dwsVocIntentionMapper.carSeriesDistributionIntention(target));

                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                FilterCriteriaModel target =new FilterCriteriaModel();
                BeanUtils.copyProperties(model, target);

                target.SetUpCycle();
                target.setStartDate(target.getStartDateUp());
                target.setEndDate(target.getEndDateUp());
                target.setRownum(null);
                lsUp.set(dwsVocIntentionMapper.carSeriesDistributionIntention(target));


                return null;
            })));

            try {
                CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(30, TimeUnit.SECONDS);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

           /* model.setRownum(10);
            List<CarIntentionVo> ls = dwsVocIntentionMapper.carSeriesDistributionIntention(model);
            model.SetUpCycle();
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            model.setRownum(null);
            List<CarIntentionVo> lsUp = dwsVocIntentionMapper.carSeriesDistributionIntention(model);*/

            ls.get().stream().forEach(e -> {
                CarIntentionVo k = lsUp.get().stream().filter(o -> e.getCarSeries().equals(o.getCarSeries())).findFirst().orElse(null);
//                e.setTotal(total.get());
                e.setComplaintR(CalculatorUtils.ringRatio(e.getComplaint(), ObjectUtil.isNull(k) ? null : k.getComplaint()));
                e.setPraiseR(CalculatorUtils.ringRatio(e.getPraise(), ObjectUtil.isNull(k) ? null : k.getPraise()));
                e.setConsultR(CalculatorUtils.ringRatio(e.getConsult(), ObjectUtil.isNull(k) ? null : k.getConsult()));
                e.setSuggestR(CalculatorUtils.ringRatio(e.getSuggest(), ObjectUtil.isNull(k) ? null : k.getSuggest()));
                e.setOtherR(CalculatorUtils.ringRatio(e.getOther(), ObjectUtil.isNull(k) ? null : k.getOther()));


//                e.setComplaintR(CalculatorUtils.ringRatio(e.getComplaint(), ObjectUtil.isNull(k) ? null : k.getComplaint()));
//                e.setPraiseR(CalculatorUtils.ringRatio(e.getPraise(), ObjectUtil.isNull(k) ? null : k.getPraise()));
//                e.setConsultR(CalculatorUtils.ringRatio(e.getConsult(), ObjectUtil.isNull(k) ? null : k.getConsult()));
//                e.setSuggestR(CalculatorUtils.ringRatio(e.getSuggest(), ObjectUtil.isNull(k) ? null : k.getSuggest()));
//                e.setOtherR(CalculatorUtils.ringRatio(e.getOther(), ObjectUtil.isNull(k) ? null : k.getOther()));

            });

//            ls.get().stream().forEach(e -> e.setTotal(total.get()));
            if (ls.get().size() >= 10) {
                ls.set(new ArrayList<>(ls.get().subList(0, ObjectUtil.isNotEmpty(model.getRownum()) ? model.getRownum() : 10)));
            }
            return Result.OK(ls.get());
        } else {
            return Result.OK(null);
        }
    }


}
