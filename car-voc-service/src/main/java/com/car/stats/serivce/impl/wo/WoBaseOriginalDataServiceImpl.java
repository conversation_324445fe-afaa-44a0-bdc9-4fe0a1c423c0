package com.car.stats.serivce.impl.wo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.mapper.wo.WoBaseOriginalDataMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.serivce.wo.WoBaseOriginalDataService;
import com.car.stats.vo.RegionUserVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.popvo.PopUpVo;
import com.car.stats.vo.wo.WoLastDealVo;
import com.car.voc.common.Result;
import com.car.voc.common.util.BigDecimalUtils;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.vo.WoAreaVo;
import com.car.voc.vo.WoOrderVo;
import com.car.voc.vo.WoTypeVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单公共接口
 *
 * <AUTHOR>
 * @description 针对表【t317_csv_rescue_wo_i_d(救援工单)】的数据库操作Service实现
 * @createDate 2024-12-05 15:22:07
 */
@Service
public class WoBaseOriginalDataServiceImpl extends ServiceImpl<WoBaseOriginalDataMapper, WoOriginalData>
        implements WoBaseOriginalDataService {

    @Override
    public PopUpVo woTitle(FilterCriteriaModel model) {
        return baseMapper.woTitle(model);
    }

    @Override
    public StatisticVo workOrderNumber(FilterCriteriaModel model) {
        return baseMapper.workOrderNumber(model);
    }

    @Override
    public Result<?> provinceMap(FilterCriteriaModel model) {
        model.setNumCycles(2);
        List<RegionUserVo> result = baseMapper.provinceMap(model)
                .stream()
                .filter(e -> e.getRegionStr() != null && e.getRegionCode() != null)
                .sorted(Comparator.comparing(RegionUserVo::getStatistic).reversed())
                .collect(Collectors.toList());
        return Result.OK(result);
    }

    @Override
    public Result<?> workOrderSatisfaction(FilterCriteriaModel model) {
        WoTypeVo woTypeVo = baseMapper.workOrderSatisfaction(model);
        StatisticVo statisticVo = new StatisticVo();
        statisticVo.setSatisfactionStatistic(CalculatorUtils.proportion(woTypeVo.getSatisfactionNum(), woTypeVo.getCloseCount()));
        statisticVo.setCloseCount(woTypeVo.getCloseCount());
        statisticVo.setOpenCount(woTypeVo.getOpenCount());
        return Result.OK(statisticVo);
    }

    @Override
    public Result<?> regionalTop(FilterCriteriaModel model) {
        model.setNumCycles(2);
        List<RegionUserVo> regions = baseMapper.regionalTop(model)
                .stream()
                .filter(e -> e.getRegionStr() != null)
                .sorted(Comparator.comparing(RegionUserVo::getStatistic).reversed())
                .collect(Collectors.toList());
        return Result.OK(regions);
    }

    @Override
    public Result<?> communityTop(FilterCriteriaModel model) {
        model.setNumCycles(2);
        List<RegionUserVo> regions = baseMapper.communityTop(model)
                .stream()
                .filter(e -> e.getRegionStr() != null)
                .sorted(Comparator.comparing(RegionUserVo::getStatistic).reversed())
                .collect(Collectors.toList());
        return Result.OK(regions);
    }

    @Override
    public Result<?> woTypeProportion(FilterCriteriaModel model) {
        List<WoTypeVo> list = baseMapper.woTypeProportion(model);
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(list, WoTypeVo::getStatistic, WoTypeVo::setStatisticP);
        return Result.OK(list);
    }

    @Override
    public Result<?> woStatusDistribution(FilterCriteriaModel model) {
        List<WoTypeVo> list = baseMapper.woStatusDistribution(model);
        BigDecimal total = list.stream().map(WoTypeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        Map<String, List<WoTypeVo>> map = list.stream().collect(Collectors.groupingBy(WoTypeVo::getDateStr));
        List<WoOrderVo> re = new ArrayList<>();
        map.keySet().forEach(e -> {
            WoOrderVo woOrderVo = new WoOrderVo();
            woOrderVo.setDateStr(e);
            List<WoTypeVo> child = map.get(e);
            BigDecimal sum = child.stream().map(WoTypeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            child.forEach(s -> {
                s.setStatisticP(CalculatorUtils.proportion(s.getStatistic(), total));
            });
            woOrderVo.setList(child);
            re.add(woOrderVo);
        });
        re.sort(Comparator.comparing(WoOrderVo::getDateStr));
        return Result.OK(re);
    }

    @Override
    public Page<WoLastDealVo> woList(FilterCriteriaModel model) {
        IPage<WoLastDealVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        return baseMapper.woList(page, model);
    }

    @Override
    public Result<?> woReginDistribution(FilterCriteriaModel model) {
        model.SetUpCycle();
        List<WoTypeVo> list1 = baseMapper.woRegin1Distribution(model);
        BigDecimal total1 = list1.stream().map(WoTypeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        List<WoTypeVo> list2 = baseMapper.woRegin2Distribution(model);
        BigDecimal total2 = list2.stream().map(WoTypeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<WoTypeVo> list1Up = baseMapper.woRegin1Distribution(model);
        List<WoTypeVo> list2Up = baseMapper.woRegin2Distribution(model);
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(list1, WoTypeVo::getStatistic, WoTypeVo::setStatisticP);
        CalculatorUtils.proportion(list2, WoTypeVo::getStatistic, WoTypeVo::setStatisticP);

        // 处理环比计算
        list1.stream().forEach(s -> {
            WoTypeVo woTypeVo = list1Up.stream().filter(e -> e.getArea().equals(s.getArea())).findAny().orElse(null);
            if (woTypeVo != null) {
                s.setStatisticR(CalculatorUtils.ringRatio(s.getStatistic(), woTypeVo.getStatistic()));
            }
        });
        list2.stream().forEach(s -> {
            WoTypeVo woTypeVo = list2Up.stream().filter(e -> e.getArea().equals(s.getArea())).findAny().orElse(null);
            if (woTypeVo != null) {
                s.setStatisticR(CalculatorUtils.ringRatio(s.getStatistic(), woTypeVo.getStatistic()));
            }
        });
        WoAreaVo areaVo = new WoAreaVo();
        areaVo.setArea1(list1);
        areaVo.setArea2(list2);
        return Result.OK(areaVo);
    }

    @Override
    public Result<?> woTrend(FilterCriteriaModel model) {
        List<WoTypeVo> list = baseMapper.woTrend(model);
        if (CollUtil.isNotEmpty(list)) {
            if (model.getDateUnit() == -1 && DateUtil.between(DateUtil.parseDate(model.getStartDate()), DateUtil.parse(model.getEndDate()), DateUnit.DAY) > 0) {
                list.sort(Comparator.comparing(WoTypeVo::getDateStr));
                return Result.OK(list);
            }
            list.remove(0);
        }
        return Result.OK(list);
    }


}
