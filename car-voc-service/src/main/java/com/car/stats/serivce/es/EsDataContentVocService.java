package com.car.stats.serivce.es;

import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.Result;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @ClassName EsDataContentVocService.java
 * @Description TODO
 * @createTime 2022年11月02日 13:41
 * @Copyright voc
 */
public interface EsDataContentVocService {
    Result<?> appSoundDetails(String contentId, String channelId, String contentType, String id);

    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN, DataDesensitization.DesensitizationType.NAME})
    Map getContentBySound(String index, String contentId);
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN, DataDesensitization.DesensitizationType.NAME})
    Map setContentByContentIds(List<String> contentIds, String index);
}
