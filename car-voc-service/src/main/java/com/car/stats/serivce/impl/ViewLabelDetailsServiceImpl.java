package com.car.stats.serivce.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.model.IntentionTrackModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.LargeDigitaFilesModel;
import com.car.stats.model.UserIntentionTrajectoryModel;
import com.car.stats.serivce.*;
import com.car.stats.serivce.es.EsDataContentVocService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.util.*;
import com.car.voc.entity.FaultProblem;
import com.car.voc.entity.SysRole;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocChannelCategoryService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.shiro.SecurityUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName ViewLabelDetailsServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月30日 20:29
 * @Copyright voc
 */
@Service
@Slf4j
public class ViewLabelDetailsServiceImpl implements ViewLabelDetailsService {
    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Autowired
    IDwsVocQualityUserDiService qualityUserDiService;
    @Autowired
    IVocBusinessTagService tagService;
    @Autowired
    IDwsVocUserService dwsVocUserService;

    @Autowired
    INPSAnalysisService inpsAnalysisService;
    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    IDwdVocDealerRiskService dealerRiskService;
    @Value("${configuration.esIndex.voc-sentence}")
    private String vocSentence;
    @Value("${configuration.esIndex.voc-sentence-repeated}")
    private String vocSentenceRepeated;

    @Autowired
    ILargeDigitaFilesService largeDigitaFilesService;

    @Override
    public Result<?> businessTagDetail(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取网点风险信息
            DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        } else if (StrUtil.isNotBlank(model.getRiskId())) {
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        PopUpVo pop = new PopUpVo();
        pop.setSoundList(getSoundList(model, page));
        return Result.OK(pop);
    }


    @Override
    public Result<?> businessTagDetailNew(LabelDetailFilterModel model, Page<SoundContentVo> page, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取网点风险信息
            DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        } else if (StrUtil.isNotBlank(model.getRiskId())) {
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        final String taskId = insertLargeDigitalFileRecord("businessTagDetailExportXls", "businessTagDetailExportXls");
        SpringContextUtils.setRequestURI(request.getRequestURI());
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportSoundListDataTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出数据失败", e);
            }
        });
        return Result.OK();
    }


    public void exportSoundListDataTask(LabelDetailFilterModel model, Page<SoundContentVo> soundContentVoPage, String taskId) throws Exception {

        Page<SoundContentVo> soundList = getSoundListExportXls(model, soundContentVoPage);
        log.info("待导出的结果总数据量:{}", soundList.getTotal());
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-声音列表";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                soundList.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<SoundContentVo> records = soundList.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<SoundContentVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    Page<SoundContentVo> contentVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<SoundContentVo> data = getSoundListExportXls(model, contentVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<SoundContentVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, SoundContentVo.class, 10000);
    }

    @Override
    public Result<?> allDataQuery(LabelDetailFilterModel model, Page<SoundContentQueryVo> page) {
        IPage<SoundContentQueryVo> pas = sentenceVocService.allDataQuery(model, page);
        return Result.OK(pas);
    }

    @Override
    public Result<?> allDataQueryNew(LabelDetailFilterModel model, Page<SoundContentQueryVo> page) {
        final String taskId = insertLargeDigitalFileRecord("allDataQueryExportXls", "allDataQueryExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportResultDataTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出数据失败", e);
            }
        });
        return Result.OK();
    }

    public void exportResultDataTask(LabelDetailFilterModel model, Page<SoundContentQueryVo> soundContentQueryVoPage, String taskId) throws Exception {

        Page<SoundContentQueryVo> pas = sentenceVocService.allDataQuery(model, soundContentQueryVoPage);
        log.info("本地上传/系统集成->待导出的结果总数据量:{}", pas.getTotal());
        EsDataSentenceVocService sentenceVocService = this.sentenceVocService;
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-声音列表查询";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                pas.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<SoundContentQueryVo> records = pas.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<SoundContentQueryVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    Page<SoundContentQueryVo> contentQueryVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<SoundContentQueryVo> data = sentenceVocService.allDataQuery(model, contentQueryVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<SoundContentQueryVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, SoundContentQueryVo.class, 10000);
    }

    @Override
    public Result<?> allSourceDataQuery(LabelDetailFilterModel model, Page<ContentVo> page) {
        List<String> esIndexs = sentenceVocService.queryIndexByChannelFilter(model);
        if (esIndexs != null && esIndexs.size() > 0) {
            IPage<ContentVo> pas = sentenceVocService.allSourceDataQuery(model, page, esIndexs);
            return Result.OK(pas);
        } else {
            IPage<ContentVo> re = new Page<>();
            return Result.OK(re);
        }

    }

    @Override
    public Result<?> allSourceGroupByTag(LabelDetailFilterModel model) {
        List<String> esIndexs = sentenceVocService.queryIndexByChannelFilter(model);
        if (esIndexs != null && esIndexs.size() > 0) {
            SourceTagTypeVo pas = sentenceVocService.allSourceGroupByTag(model, esIndexs);
            return Result.OK(pas);
        } else {
            SourceTagTypeVo re = new SourceTagTypeVo();
            return Result.OK(re);
        }
    }

    @Override
    public Result<?> allTagsClickSoundsList(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        PopUpVo pop = new PopUpVo();
        pop.setSoundList(onClickSoundsList(model, page));
        return Result.OK(pop);
    }


    @Override
    public Result<?> allTagsClickSoundsListNew(LabelDetailFilterModel model, Page<SoundContentVo> page, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        SpringContextUtils.setRequestURI(request.getRequestURI());
        final String taskId = insertLargeDigitalFileRecord("allTagsClickSoundsListExportXls", "allTagsClickSoundsListExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportOnClickSoundsListTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出全部声音列表失败", e);
            }
        });
        return Result.OK();
    }


    public void exportOnClickSoundsListTask(LabelDetailFilterModel model, Page<SoundContentVo> soundContentVoPage, String taskId) throws Exception {
        Page<SoundContentVo> soundContentVoIPage = this.onClickSoundsListExportXls(model, soundContentVoPage);
        log.info("待导出的结果总数据量:{}", soundContentVoIPage.getTotal());
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-声音列表";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                soundContentVoIPage.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<SoundContentVo> records = soundContentVoIPage.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<SoundContentVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    Page<SoundContentVo> contentVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<SoundContentVo> data = this.onClickSoundsListExportXls(model, contentVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<SoundContentVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, SoundContentVo.class, 10000);
    }

    @Override
    public Result<?> allTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        return Result.OK(emotionUserDiService.allTagUserList(model, page));
    }


    @Override
    public Result<?> allTagUserListNew(LabelDetailFilterModel model, Page<UserListInfoVo> page, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        SpringContextUtils.setRequestURI(request.getRequestURI());
        final String taskId = insertLargeDigitalFileRecord("allTagUserListExportXls", "allTagUserListExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportAllTagUserListTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出全部声音列表失败", e);
            }
        });
        return Result.OK();
    }

    public void exportAllTagUserListTask(LabelDetailFilterModel model, Page<UserListInfoVo> userListInfoPage, String taskId) throws Exception {

        Page<UserListInfoVo> userList = emotionUserDiService.allTagUserList(model, userListInfoPage);
        log.info("待导出的结果总数据量:{}", userList.getTotal());
        IDwsVocEmotionUserDiService emotionUserDiService = this.emotionUserDiService;
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-用户列表";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                userList.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<UserListInfoVo> records = userList.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<UserListInfoVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    log.info("<><><><><><><>:{},{}", page.getCurrent(), page.getSize());
                    Page<UserListInfoVo> infoVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<UserListInfoVo> data = emotionUserDiService.allTagUserList(model, infoVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<UserListInfoVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, UserListInfoVo.class, 3000);
    }

    @Override
    public Result<?> businessTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {
        if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取网点风险信息
            DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        } else if (StrUtil.isNotBlank(model.getRiskId())) {
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        return Result.OK(emotionUserDiService.getUserList(model, page));
    }


    @Override
    public Result<?> businessTagUserListNew(LabelDetailFilterModel model, Page<UserListInfoVo> page, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取网点风险信息
            DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        } else if (StrUtil.isNotBlank(model.getRiskId())) {
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        SpringContextUtils.setRequestURI(request.getRequestURI());
        final String taskId = insertLargeDigitalFileRecord("businessTagUserListExportXls", "businessTagUserListExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportUserListTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出用户列表失败", e);
            }
        });
        return Result.OK();
    }


    public void exportUserListTask(LabelDetailFilterModel model, Page<UserListInfoVo> userListInfoPage, String taskId) throws Exception {

        Page<UserListInfoVo> userList = emotionUserDiService.getUserList(model, userListInfoPage);
        log.info("待导出的结果总数据量:{}", userList.getTotal());
        IDwsVocEmotionUserDiService emotionUserDiService = this.emotionUserDiService;
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-用户列表";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                userList.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<UserListInfoVo> records = userList.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<UserListInfoVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    log.info("<><><><><><><>:{},{}", page.getCurrent(), page.getSize());
                    Page<UserListInfoVo> infoVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<UserListInfoVo> data = emotionUserDiService.getUserList(model, infoVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<UserListInfoVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("<<<<查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, UserListInfoVo.class, 3000);
    }

    @Override
    public Result<?> soundContentDetails(String contentId) {


        return null;
    }

    @Autowired
    IFaultProblemService faultProblemService;


    @Autowired
    SysUserRoleMapper sysUserRoleMapper;

    boolean getQuality(String brandCode) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(qualityText) && wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @Autowired
    IDwdVocQualityRiskService qualityRiskService;

    @Override
    public Result<?> qualityTagDetail(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        PopUpVo pop = new PopUpVo();

        if (!getQuality(model.getBrandCode())) {
            return Result.OK(pop);
        }

        pop.setSoundList(getQualitySoundList(model, page));

        return Result.OK(pop);

    }

    @Override
    public Result<?> qualityTagDetailNew(LabelDetailFilterModel model, Page<SoundContentVo> page, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        PopUpVo pop = new PopUpVo();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(pop);
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        SpringContextUtils.setRequestURI(request.getRequestURI());
        final String taskId = insertLargeDigitalFileRecord("qualityTagDetailExportXls", "qualityTagDetailExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportQualitySoundListTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出用户列表失败", e);
            }
        });
        return Result.OK(pop);

    }

    public void exportQualitySoundListTask(LabelDetailFilterModel model, Page<SoundContentVo> soundContentVoPage, String taskId) throws Exception {
        Page<SoundContentVo> soundContentVoIPage = this.getQualitySoundListExportXls(model, soundContentVoPage);
        log.info("待导出的结果总数据量:{}", soundContentVoIPage.getTotal());
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-声音列表";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                soundContentVoIPage.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<SoundContentVo> records = soundContentVoIPage.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<SoundContentVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    Page<SoundContentVo> contentVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<SoundContentVo> data = this.getQualitySoundListExportXls(model, contentVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<SoundContentVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, SoundContentVo.class, 10000);
    }


    private Page<SoundContentVo> getQualitySoundList(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        return sentenceVocService.getQualitySoundList(model, page);
    }

    private Page<SoundContentVo> getQualitySoundListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        return sentenceVocService.getQualitySoundListExportXls(model, page);
    }

    @Override
    public Result<?> qualityTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        return Result.OK(qualityUserDiService.getUserList(model, page));
    }


    @Override
    public Result<?> qualityTagUserListNew(LabelDetailFilterModel model, Page<UserListInfoVo> page, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        SpringContextUtils.setRequestURI(request.getRequestURI());
        final String taskId = insertLargeDigitalFileRecord("qualityTagUserListExportXls", "qualityTagUserListExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportQualityTagUserListTask(model, page, taskId);
            } catch (Exception e) {
                log.error("异步导出用户列表失败", e);
            }
        });
        return Result.OK();
    }


    public void exportQualityTagUserListTask(LabelDetailFilterModel model, Page<UserListInfoVo> userListInfoPage, String taskId) throws Exception {

        Page<UserListInfoVo> userList = qualityUserDiService.getUserList(model, userListInfoPage);
        log.info("待导出的结果总数据量:{}", userList.getTotal());
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-用户列表";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                userList.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<UserListInfoVo> records = userList.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<UserListInfoVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    log.info("<><><><><><><>:{},{}", page.getCurrent(), page.getSize());
                    Page<UserListInfoVo> infoVoPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<UserListInfoVo> data = qualityUserDiService.getUserList(model, infoVoPage);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<UserListInfoVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("<<<<查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, UserListInfoVo.class, 3000);
    }


    @Autowired
    EsDataContentVocService contentVocService;
    @Autowired
    IVocChannelCategoryService channelCategoryService;

    @Override
    public Result<?> soundDetails(String contentId, String channelId, String indexId, String id) {
        return contentVocService.appSoundDetails(contentId, channelId, indexId, id);
    }

    @Override
    public Result<?> userDetails(LabelDetailFilterModel model) {
        UserDetailVo userDetail = new UserDetailVo();
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }
        if (StrUtil.isNotBlank(model.getChannelId())) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelId()));
        }

        if (StrUtil.isNotBlank(model.getUserId())) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", model.getUserId()));
        }
        if (!StrUtil.isBlankIfStr(model.getMenuName()) && "productQuality".equals(model.getMenuName())) {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        } else {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        }
        List<IntentionChannelStatisticVo> vos = new ArrayList<>();

        sentenceVocService.setUserPublishCount(userDetail, model.getUserId(), vos, queryBuilder);

        userDetail.setPublishCount(vos.stream().filter(s -> "全部".equals(s.getChannelStr())).findFirst().orElse(null).getStatistic());


        EmotionProportionVo emotionProp = new EmotionProportionVo();

        queryUserEmotion(emotionProp, model, userDetail);
        userDetail.setEmotionProp(emotionProp);

        userDetail.setCars(new ArrayList<>());
        userDetail.setHotWordsVos(emotionUserDiService.hotWordsUser(model));
        userDetail.setUserId(model.getUserId());
        return Result.OK(userDetail);
    }

    private void queryUserChannels(UserDetailVo userDetail, String userId, List<ChannelVo> channelVos) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", userId));
        TermsAggregationBuilder intentionType = AggregationBuilders.terms("channelId").field("channelId.keyword").size(1000);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.addAggregation(intentionType);

        System.out.println("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        Terms agg = pagelist.getAggregations().get("channelId");

        BigDecimal total = new BigDecimal(pagelist.getTotalHits());
        agg.getBuckets().forEach(e -> {
            ChannelVo channelVo = new ChannelVo();
            channelVo.setChannelId(e.getKeyAsString());
            channelVo.setStatistic(new BigDecimal(e.getDocCount()));
            channelVo.setStatisticP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), total));
            VocChannelCategory category = channelCategoryService.getById(e.getKeyAsString());
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
            }
            channelVo.setChannelStr(cstr);
            channelVos.add(channelVo);
        });


    }

    @Override
    public void queryUserEmotion(EmotionProportionVo emotionProp, LabelDetailFilterModel model, UserDetailVo userDetailVo) {
        if (model.getUserId() == null) {
            return;
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", model.getUserId()));
        if (!StrUtil.isBlankIfStr(model.getMenuName()) && "productQuality".equals(model.getMenuName())) {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        } else {
//            queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }
        TermsAggregationBuilder dimensionEmotion = AggregationBuilders.terms("dimensionEmotion").field("dimensionEmotion.keyword").size(10);
        TermsAggregationBuilder intentionType = AggregationBuilders.terms("intentionType").field("intentionType.keyword").size(10);
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(10000);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();

        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(Pageable.ofSize(1));
        searchQuery.addAggregation(dimensionEmotion).addAggregation(intentionType).addAggregation(channelId);
        System.out.println("es查询：" + queryBuilder.toString());

        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        Terms agg = pagelist.getAggregations().get("dimensionEmotion");
        Terms aggintentionType = pagelist.getAggregations().get("intentionType");
        Terms aggchannelId = pagelist.getAggregations().get("channelId");


        List<ChannelVo> channelVos = new ArrayList<>();
        BigDecimal total = new BigDecimal(0);
        for (Terms.Bucket bucket : aggchannelId.getBuckets()) {
            total = NumberUtil.add(total, new BigDecimal(bucket.getDocCount()));
        }
        userDetailVo.setStatistic(total);
        BigDecimal finalTotal = total;


        aggchannelId.getBuckets().forEach(e -> {
            ChannelVo channelVo = new ChannelVo();
            channelVo.setChannelId(e.getKeyAsString());
            channelVo.setStatistic(new BigDecimal(e.getDocCount()));
            channelVo.setStatisticP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), finalTotal));
            VocChannelCategory category = channelCategoryService.getByIdCache(e.getKeyAsString());
            Terms aggContentId = e.getAggregations().get("contentId");
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
            }
            channelVo.setChannelStr(cstr);
            channelVos.add(channelVo);
        });
        userDetailVo.setChannelDis(channelVos);

        aggintentionType.getBuckets().forEach(e -> {
            if ("咨询".equals(e.getKeyAsString())) {
                userDetailVo.setConsult(new BigDecimal(e.getDocCount()));
            } else if ("投诉".equals(e.getKeyAsString())) {
                userDetailVo.setComplaint(new BigDecimal(e.getDocCount()));
            } else if ("表扬".equals(e.getKeyAsString())) {
                userDetailVo.setPraise(new BigDecimal(e.getDocCount()));
            } else if ("建议".equals(e.getKeyAsString())) {
                userDetailVo.setSuggest(new BigDecimal(e.getDocCount()));
            }
        });


        emotionProp.setTotal(total);
        agg.getBuckets().forEach(e -> {
            if ("正面".equals(e.getKeyAsString())) {
                emotionProp.setPositive(new BigDecimal(e.getDocCount()));
                emotionProp.setPositiveP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            } else if ("负面".equals(e.getKeyAsString())) {
                emotionProp.setNegative(new BigDecimal(e.getDocCount()));
                emotionProp.setNegativeP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            } else if ("中性".equals(e.getKeyAsString())) {
                emotionProp.setNeutral(new BigDecimal(e.getDocCount()));
                emotionProp.setNeutralP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            }
        });
        userDetailVo.setNetWorth(CalculatorUtils.getEmotionalNetWorth(emotionProp.getPositive(), emotionProp.getNegative()));
    }

    @Override
    public void queryUserEmotionBusiness(EmotionProportionVo emotionProp, UserListInfoVo user, UserDetailVo userDetailVo, LabelDetailFilterModel model) {
        if (user.getUserId() == null) {
            return;
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", user.getUserId()));
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }
        TermsAggregationBuilder dimensionEmotion = AggregationBuilders.terms("dimensionEmotion").field("dimensionEmotion.keyword").size(10);
        TermsAggregationBuilder intentionType = AggregationBuilders.terms("intentionType").field("intentionType.keyword").size(10);
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(10000);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(1000);
        channelId.subAggregation(contentId);

        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(Pageable.ofSize(1));
        searchQuery.addAggregation(dimensionEmotion).addAggregation(intentionType).addAggregation(channelId);
        System.out.println("es查询：" + queryBuilder.toString());

        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        Terms agg = pagelist.getAggregations().get("dimensionEmotion");
        Terms aggintentionType = pagelist.getAggregations().get("intentionType");
        Terms aggchannelId = pagelist.getAggregations().get("channelId");


        List<ChannelVo> channelVos = new ArrayList<>();
        BigDecimal total = new BigDecimal(0);
        for (Terms.Bucket bucket : aggchannelId.getBuckets()) {
            total = NumberUtil.add(total, new BigDecimal(bucket.getDocCount()));
        }
//        userDetailVo.setStatistic(total);
        BigDecimal finalTotal = total;

        AtomicInteger pucon = new AtomicInteger();

        aggchannelId.getBuckets().forEach(e -> {
            ChannelVo channelVo = new ChannelVo();
            channelVo.setChannelId(e.getKeyAsString());
            channelVo.setStatistic(new BigDecimal(e.getDocCount()));
            channelVo.setStatisticP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), finalTotal));
            VocChannelCategory category = channelCategoryService.getByIdCache(e.getKeyAsString());
            Terms aggContentId = e.getAggregations().get("contentId");
            pucon.addAndGet(aggContentId.getBuckets().size());
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
            }
            channelVo.setChannelStr(cstr);
            channelVos.add(channelVo);
        });
        userDetailVo.setChannelDis(channelVos);
        user.setPublishCount(new BigDecimal(pucon.get()));

        aggintentionType.getBuckets().forEach(e -> {
            if ("咨询".equals(e.getKeyAsString())) {
                userDetailVo.setConsult(new BigDecimal(e.getDocCount()));
            } else if ("投诉".equals(e.getKeyAsString())) {
                userDetailVo.setComplaint(new BigDecimal(e.getDocCount()));
            } else if ("表扬".equals(e.getKeyAsString())) {
                userDetailVo.setPraise(new BigDecimal(e.getDocCount()));
            }
        });


        emotionProp.setTotal(total);
        agg.getBuckets().forEach(e -> {
            if ("正面".equals(e.getKeyAsString())) {
                emotionProp.setPositive(new BigDecimal(e.getDocCount()));
                emotionProp.setPositiveP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            } else if ("负面".equals(e.getKeyAsString())) {
                emotionProp.setNegative(new BigDecimal(e.getDocCount()));
                emotionProp.setNegativeP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            } else if ("中性".equals(e.getKeyAsString())) {
                emotionProp.setNeutral(new BigDecimal(e.getDocCount()));
                emotionProp.setNeutralP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            }
        });
        userDetailVo.setNetWorth(CalculatorUtils.getEmotionalNetWorth(emotionProp.getPositive(), emotionProp.getNegative()));
    }

    @Override
    public void queryUserEmotionQuality(EmotionProportionVo emotionProp, UserListInfoVo user, UserDetailVo userDetailVo, LabelDetailFilterModel model) {
        if (user.getUserId() == null) {
            return;
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", user.getUserId()));
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }

        TermsAggregationBuilder dimensionEmotion = AggregationBuilders.terms("dimensionEmotion").field("dimensionEmotion.keyword").size(10);
        TermsAggregationBuilder intentionType = AggregationBuilders.terms("intentionType").field("intentionType.keyword").size(10);
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(10000);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(1000);
        channelId.subAggregation(contentId);

        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(Pageable.ofSize(1));
        searchQuery.addAggregation(dimensionEmotion).addAggregation(intentionType).addAggregation(channelId);
        System.out.println("es查询：" + queryBuilder.toString());

        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        Terms agg = pagelist.getAggregations().get("dimensionEmotion");
        Terms aggintentionType = pagelist.getAggregations().get("intentionType");
        Terms aggchannelId = pagelist.getAggregations().get("channelId");


        List<ChannelVo> channelVos = new ArrayList<>();
        BigDecimal total = new BigDecimal(0);
        for (Terms.Bucket bucket : aggchannelId.getBuckets()) {
            total = NumberUtil.add(total, new BigDecimal(bucket.getDocCount()));
        }
        userDetailVo.setStatistic(total);
        BigDecimal finalTotal = total;
        AtomicInteger pucon = new AtomicInteger();
        aggchannelId.getBuckets().forEach(e -> {
            ChannelVo channelVo = new ChannelVo();
            channelVo.setChannelId(e.getKeyAsString());
            channelVo.setStatistic(new BigDecimal(e.getDocCount()));
            channelVo.setStatisticP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), finalTotal));
            VocChannelCategory category = channelCategoryService.getByIdCache(e.getKeyAsString());
            Terms aggContentId = e.getAggregations().get("contentId");
            pucon.addAndGet(aggContentId.getBuckets().size());
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
            }
            channelVo.setChannelStr(cstr);
            channelVos.add(channelVo);
        });
        user.setPublishCount(new BigDecimal(pucon.get()));
        userDetailVo.setChannelDis(channelVos);

        aggintentionType.getBuckets().forEach(e -> {
            if ("咨询".equals(e.getKeyAsString())) {
                userDetailVo.setConsult(new BigDecimal(e.getDocCount()));
            } else if ("投诉".equals(e.getKeyAsString())) {
                userDetailVo.setComplaint(new BigDecimal(e.getDocCount()));
            } else if ("表扬".equals(e.getKeyAsString())) {
                userDetailVo.setPraise(new BigDecimal(e.getDocCount()));
            }
        });


        emotionProp.setTotal(total);
        agg.getBuckets().forEach(e -> {
            if ("正面".equals(e.getKeyAsString())) {
                emotionProp.setPositive(new BigDecimal(e.getDocCount()));
                emotionProp.setPositiveP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            } else if ("负面".equals(e.getKeyAsString())) {
                emotionProp.setNegative(new BigDecimal(e.getDocCount()));
                emotionProp.setNegativeP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            } else if ("中性".equals(e.getKeyAsString())) {
                emotionProp.setNeutral(new BigDecimal(e.getDocCount()));
                emotionProp.setNeutralP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), emotionProp.getTotal()));
            }
        });
        userDetailVo.setNetWorth(CalculatorUtils.getEmotionalNetWorth(emotionProp.getPositive(), emotionProp.getNegative()));
    }

    @Override
    public Result<?> lastTagHotWords(LabelDetailFilterModel model) {
        if (!StringUtils.isEmpty(model.getTopicCode()) && model.getTopicCode().contains("B")) {
            if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取风险信息
                DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
                model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
                model.setDlrName(risk.getDlrName());
                model.setIntention("投诉");
            } else if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
                DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
                model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            }
            return Result.OK(emotionUserDiService.lastTagHotWords(model));
        } else if (!StringUtils.isEmpty(model.getTopicCode()) && model.getTopicCode().contains("Q100")) {
            model.setSecondDimensionCodes(null);
            if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
                DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
                model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
                if (StrUtil.isNotBlank(risk.getChannelId())) {
                    model.setChannelIds(Arrays.asList(risk.getChannelId()));
                }
            }
            return Result.OK(qualityUserDiService.lastTagHotWords(model));
        } else {
            return null;
        }

    }

    private BoolQueryBuilder comQueryBUider(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        if (StrUtil.isNotBlank(model.getBrandCode())) {
            queryBuilder.must(QueryBuilders.termQuery("brandCode.keyword", model.getBrandCode()));
        }
        if (StrUtil.isNotBlank(model.getProblemLevel())) {
            queryBuilder.must(QueryBuilders.termQuery("level.keyword", model.getProblemLevel()));
        }
        if (StrUtil.isNotBlank(model.getTopic())) {
            queryBuilder.must(QueryBuilders.termQuery("topic.keyword", model.getTopic()));
        } else if (StrUtil.isNotBlank(model.getTopicCode())) {
            queryBuilder.must(QueryBuilders.termQuery("topicCode.keyword", model.getTopicCode()));
        } else if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("threeDimensionCode.keyword", model.getThirdDimensionCode()));
        } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCode()));
        } else if ((model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0)) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCodes()));
        }
        if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("firstDimensionCode.keyword", model.getFirstDimensionCode()));
        }
        if (StrUtil.isNotBlank(model.getEmotionKeyword())) {
            queryBuilder.must(QueryBuilders.termQuery("emotionKeyword.keyword", model.getEmotionKeyword()));
        }
        if (model.getOtherTag() != null && model.getOtherTag().size() > 0) {
            queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode.keyword", model.getOtherTag()));
        }
        if (StrUtil.isNotBlank(model.getEmotion())) {
            if (model.getEmotion().contains(",")) {
                List<String> list = Arrays.asList(model.getEmotion().split(","));
                queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", list));
            } else {
                queryBuilder.must(QueryBuilders.termQuery("dimensionEmotion.keyword", model.getEmotion()));
            }
        }
        if (StrUtil.isNotBlank(model.getIntention())) {
            queryBuilder.must(QueryBuilders.termQuery("intentionType.keyword", model.getIntention()));
        }
        if (model.getProvince() != null && model.getProvince().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("province.keyword", model.getProvince()));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }
        return queryBuilder;
    }

    @Override
    public Map<String, UserDetailVo> queryUserEmotionList(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder, Set<String> idList) {
        idList.removeAll(Collections.singleton(null));
        if (idList == null) {
            return null;
        }

        queryBuilder.must(QueryBuilders.termsQuery("oneId.keyword", idList));
        comQueryBUider(model, queryBuilder);


        TermsAggregationBuilder userIds = AggregationBuilders.terms("oneId").field("oneId.keyword").size(5000);
        TermsAggregationBuilder dimensionEmotion = AggregationBuilders.terms("dimensionEmotion").field("dimensionEmotion.keyword").size(10);
        TermsAggregationBuilder intentionType = AggregationBuilders.terms("intentionType").field("intentionType.keyword").size(10);
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(10000);
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(10000);
        channelId.subAggregation(contentId);
        userIds.subAggregation(dimensionEmotion);
        userIds.subAggregation(intentionType);
        userIds.subAggregation(channelId);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();


        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(Pageable.ofSize(1));
        searchQuery.addAggregation(userIds);
        System.out.println("es查询：" + queryBuilder.toString());

        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        Terms oneId = pagelist.getAggregations().get("oneId");
        Map<String, UserDetailVo> map = new HashMap<>();
        oneId.getBuckets().forEach(s -> {
            UserDetailVo userDetailVo = new UserDetailVo();
            Terms agg = s.getAggregations().get("dimensionEmotion");
            Terms aggintentionType = s.getAggregations().get("intentionType");
            Terms aggchannelId = s.getAggregations().get("channelId");
            List<ChannelVo> channelVos = new ArrayList<>();
            BigDecimal total = new BigDecimal(0);
            for (Terms.Bucket bucket : aggchannelId.getBuckets()) {
                total = NumberUtil.add(total, new BigDecimal(bucket.getDocCount()));
            }
            userDetailVo.setStatistic(total);
            BigDecimal finalTotal = total;
            AtomicInteger pucon = new AtomicInteger();
            aggchannelId.getBuckets().forEach(e -> {
                ChannelVo channelVo = new ChannelVo();
                channelVo.setChannelId(e.getKeyAsString());
                channelVo.setStatistic(new BigDecimal(e.getDocCount()));
                channelVo.setStatisticP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), finalTotal));
                Terms aggContentId = e.getAggregations().get("contentId");

                pucon.addAndGet(aggContentId.getBuckets().size());

                channelVo.setChannelStr(e.getKeyAsString());
                channelVos.add(channelVo);
            });
            userDetailVo.setPublishCount(new BigDecimal(pucon.get()));
            userDetailVo.setChannelDis(channelVos);

            aggintentionType.getBuckets().forEach(e -> {
                if ("咨询".equals(e.getKeyAsString())) {
                    userDetailVo.setConsult(new BigDecimal(e.getDocCount()));
                } else if ("投诉".equals(e.getKeyAsString())) {
                    userDetailVo.setComplaint(new BigDecimal(e.getDocCount()));
                } else if ("表扬".equals(e.getKeyAsString())) {
                    userDetailVo.setPraise(new BigDecimal(e.getDocCount()));
                } else if ("建议".equals(e.getKeyAsString())) {
                    userDetailVo.setSuggest(new BigDecimal(e.getDocCount()));
                }
            });


            userDetailVo.setTotal(total);
            agg.getBuckets().forEach(e -> {
                if ("正面".equals(e.getKeyAsString())) {
                    userDetailVo.setPositive(new BigDecimal(e.getDocCount()));
                    userDetailVo.setPositiveP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), userDetailVo.getTotal()));
                } else if ("负面".equals(e.getKeyAsString())) {
                    userDetailVo.setNegative(new BigDecimal(e.getDocCount()));
                    userDetailVo.setNegativeP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), userDetailVo.getTotal()));
                } else if ("中性".equals(e.getKeyAsString())) {
                    userDetailVo.setNeutral(new BigDecimal(e.getDocCount()));
                    userDetailVo.setNeutralP(CalculatorUtils.proportion(new BigDecimal(e.getDocCount()), userDetailVo.getTotal()));
                }
            });
            userDetailVo.setNetWorth(CalculatorUtils.getEmotionalNetWorth(userDetailVo.getPositive(), userDetailVo.getNegative()));
            map.put(s.getKeyAsString(), userDetailVo);
        });
        return map;
    }

    @Autowired
    private ElasticsearchRestTemplate restTemplate;

    private void queryUserInrtentions(UserDetailVo userDetail, String userId) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", userId));
        TermsAggregationBuilder intentionType = AggregationBuilders.terms("intentionType").field("intentionType.keyword").size(10);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.addAggregation(intentionType);
        System.out.println("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        Terms agg = pagelist.getAggregations().get("intentionType");
        userDetail.setStatistic(new BigDecimal(pagelist.getTotalHits()));
        agg.getBuckets().forEach(e -> {
            if ("咨询".equals(e.getKeyAsString())) {
                userDetail.setConsult(new BigDecimal(e.getDocCount()));
            } else if ("投诉".equals(e.getKeyAsString())) {
                userDetail.setComplaint(new BigDecimal(e.getDocCount()));
            } else if ("表扬".equals(e.getKeyAsString())) {
                userDetail.setPraise(new BigDecimal(e.getDocCount()));
            }
        });


    }

    @Override
    public Result<?> intentionTrack(IntentionTrackModel model) {
        IPage<SoundContentVo> intentionTrack = sentenceVocService.intentionTrackPage(model);
        return Result.OK(intentionTrack);
    }

    @Override
    public Result<?> userIntentionAndTrajectory(UserIntentionTrajectoryModel model) {
        List<IntentionChannelStatisticVo> ics = sentenceVocService.intentionChannelStatistic(model);
        return Result.OK(ics);
    }

    @Override
    public Result<?> userDataTrack(IntentionTrackModel model) {
        IPage<Map> datatra = sentenceVocService.userDataTrackPus(model);
        return Result.OK(datatra);
    }

    @Override
    public Result<?> userDataTrackRepair(IntentionTrackModel model) {
        IPage<UserRepairDetailVo> datatra = sentenceVocService.userDataTrackRepairPus(model);
        return Result.OK(datatra);
    }

    @Override
    public Result<?> businessTagDetailTrend(LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取网点风险信息
            DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        } else if (StrUtil.isNotBlank(model.getRiskId())) {
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        return Result.OK(emotionUserDiService.trendChangeLabelNew(model));
    }

    @Override
    public Result<?> allTagsTrend(LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }
        return Result.OK(emotionUserDiService.allTagsTrend(model));
    }

    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;

    @Override
    public Result<?> positiveNegativeTrend(LabelDetailFilterModel model) {
        List<EmotionProportionVo> objectLists = emotionUserDiMapper.allPositiveNegativeTrend(model);
        objectLists.forEach(obj -> {
            obj.setNeutralP(BigDecimal.ZERO);

            obj.setPositiveP(CalculatorUtils.proportion(obj.getPositive(), obj.getTotal()));
            obj.setNegativeP(CalculatorUtils.proportion(obj.getNegative(), obj.getTotal()));


            obj.setPositiveT(obj.getPositiveP());
            obj.setNegativeT(obj.getNegativeP());

            obj.setNegativeTR(NumberUtil.sub(obj.getNegativeT(), CalculatorUtils.proportion(obj.getNegativeR(), obj.getTotalR())));
            obj.setPositiveTR(NumberUtil.sub(obj.getPositiveT(), CalculatorUtils.proportion(obj.getPositiveR(), obj.getTotalR())));
        });
        if (CollUtil.isNotEmpty(objectLists)) {
            if (model.getDateUnit() == -1 && DateUtil.between(DateUtil.parseDate(model.getStartDate()), DateUtil.parse(model.getEndDate()), DateUnit.DAY) > 0) {
                objectLists.sort(Comparator.comparing(EmotionProportionVo::getDateStr));
                Result.OK(objectLists);
            }
            objectLists.remove(0);
        }
        return Result.OK(objectLists);
    }

    @Override
    public Result<?> allPositiveNegativeTrend(LabelDetailFilterModel model) {
        if (model.getDateUnit() == -1 && DateUtils.getDays(model.getEndDate(), model.getStartDate()) + 1 <= 1) {
            model.setStartDate(DateUtils.addDay(model.getStartDate(), -5));
        }
        List<SvwDate> dates = model.getDateTimes();
        int index = 12;
        if (model.getDateUnit() != -1)//处理自定义时间是全部显示
        {
            if (dates.size() >= 13) {
                dates = dates.subList(dates.size() - (index + 1), dates.size());
            }
        }
        model.setDateList(dates);
        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());

        List<EmotionProportionVo> objectLists = emotionUserDiMapper.allPositiveNegativeTrend(model);

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    EmotionProportionVo vo = new EmotionProportionVo();
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(EmotionProportionVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            EmotionProportionVo obj = objectLists.get(i);  //当前
            EmotionProportionVo preObj = objectLists.get(i - 1);   //前一个
            obj.setNeutralP(BigDecimal.ZERO);

            obj.setPositiveP(CalculatorUtils.proportion(obj.getPositive(), obj.getTotal()));
            obj.setNegativeP(CalculatorUtils.proportion(obj.getNegative(), obj.getTotal()));

            obj.setPositiveR(CalculatorUtils.ringRatio(obj.getPositive(), preObj.getPositive()));
            obj.setNegativeR(CalculatorUtils.ringRatio(obj.getNegative(), preObj.getNegative()));

            obj.setPositiveT(obj.getPositiveP());
            obj.setNegativeT(obj.getNegativeP());

            obj.setNegativeTR(NumberUtil.sub(obj.getNegativeT(), CalculatorUtils.proportion(preObj.getNegative(), preObj.getTotal())));
            obj.setPositiveTR(NumberUtil.sub(obj.getPositiveT(), CalculatorUtils.proportion(preObj.getPositive(), preObj.getTotal())));
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (model.getDateUnit() == -1 && objectLists.size() > 1) {
            objectLists.remove(0);
        }
        if (objectLists.size() >= index) {
            objectLists = new ArrayList<>(objectLists.subList(dates.size() - index, dates.size()));
        }
        return Result.OK(objectLists);
    }

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public Result<?> qualityTagDetailTrend(LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        return Result.OK(qualityUserDiService.trendChangeLabel(model));
    }

    @Override
    public Result<?> businessTagDetailTitle(LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getRiskId()) && StrUtil.isNotBlank(model.getDlrName())) {//获取网点风险信息
            DwdVocDealerRisk risk = dealerRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        } else if (StrUtil.isNotBlank(model.getRiskId())) {
            DwdVocRisk risk = vocRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        }


        String code = model.getCode();

        PopUpVo pop = new PopUpVo();


        String labelstr = "";
        if (StrUtil.isNotBlank(model.getEmotionKeyword())) {
            labelstr = model.getEmotionKeyword();

        } else if (StrUtil.isNotBlank(code)) {
            labelstr = CalculatorUtils.getAllTagCode(code);
        }

        if (labelstr != null) {
            pop.setLabelStr(labelstr);
        } else {
            pop.setLabelStr("");
        }
        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());
        Map<String, String> map;
        Map<String, String> mapup = new HashMap<>();
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            map = inpsAnalysisService.userAndStatistic(model);
        } else {
            map = emotionUserDiService.userAndStatistic(model);
            model.SetUpCycle();
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            mapup = emotionUserDiService.userAndStatistic(model);

        }
        if (map != null) {
            if (map.get("intention") == null) {
                pop.setStatistic(new BigDecimal(0));
                pop.setStatisticR(CalculatorUtils.ringRatio(pop.getStatistic(), Objects.nonNull(mapup.get("intention")) ?
                        new BigDecimal(mapup.get("intention")) : new BigDecimal(0)));
            } else {
                pop.setStatistic(new BigDecimal(map.get("intention")));

                pop.setStatisticR(CalculatorUtils.ringRatio(pop.getStatistic(), Objects.nonNull(mapup.get("intention")) ?
                        new BigDecimal(mapup.get("intention")) : new BigDecimal(0)));
            }
            if (map.get("userCount") == null) {
                pop.setUserCount(new BigDecimal(0));
                pop.setUserCountR(CalculatorUtils.ringRatio(pop.getUserCount(), Objects.nonNull(mapup.get("userCount")) ?
                        new BigDecimal(mapup.get("userCount")) : new BigDecimal(0)));
            } else {
                pop.setUserCount(new BigDecimal(map.get("userCount")));

                pop.setUserCountR(CalculatorUtils.ringRatio(pop.getUserCount(), Objects.nonNull(mapup.get("userCount")) ?
                        new BigDecimal(mapup.get("userCount")) : new BigDecimal(0)));
            }

        }

        return Result.OK(pop);
    }

    @Override
    public Result<?> businessTagDetailFocus(LabelDetailFilterModel model) {
        String code = model.getCode();
        /*if (StrUtil.isBlankIfStr(code)&&StrUtil.isBlankIfStr(model.getEmotionKeyword())){
            return Result.error("未指定来源！");
        }*/
        PopUpVo pop = new PopUpVo();
        if (StrUtil.isNotBlank(code)) {
            List<UserLabelVo> aggDistribution;
            BigDecimal total;
            BigDecimal totaluser;
            if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
                aggDistribution = emotionUserDiService.topicTagDistribution(model);
                total = aggDistribution.stream().map(UserLabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                totaluser = aggDistribution.stream().map(UserLabelVo::getUserCount).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                aggDistribution.forEach(e -> {
                    e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), totaluser));
                    e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
                });
                pop.setAggDistribution(aggDistribution);
            } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
                aggDistribution = emotionUserDiService.thirdTagDistribution(model);
                total = aggDistribution.stream().map(UserLabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                totaluser = aggDistribution.stream().map(UserLabelVo::getUserCount).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                aggDistribution.forEach(e -> {
                    e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), totaluser));
                    e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
                });
                pop.setAggDistribution(aggDistribution);
            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
                aggDistribution = emotionUserDiService.secondTagDistribution(model);
                total = aggDistribution.stream().map(UserLabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                totaluser = aggDistribution.stream().map(UserLabelVo::getUserCount).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                aggDistribution.forEach(e -> {
                    e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), totaluser));
                    e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
                });
                pop.setAggDistribution(aggDistribution);
            }

        }
        return Result.OK(pop.getAggDistribution());
    }

    @Override
    public Result<?> qualityTagDetailTitle(LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getRiskId())) {//获取风险信息
            DwdVocQualityRisk risk = qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())) {
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        String code = model.getCode();
        PopUpVo pop = new PopUpVo();

        if (!getQuality(model.getBrandCode())) {
            pop.setStatistic(new BigDecimal(String.valueOf(BigDecimal.ZERO)));
            pop.setUserCount(new BigDecimal(String.valueOf(BigDecimal.ZERO)));
            return Result.OK(pop);
        }


        String labelstr = "";
        if (StrUtil.isNotBlank(model.getEmotionKeyword())) {
            labelstr = model.getEmotionKeyword();
        } else if (StrUtil.isNotBlank(code)) {
            labelstr = CalculatorUtils.getAllTagCode(code);
        }
        if (labelstr != null) {
            pop.setLabelStr(labelstr);
        } else {
            pop.setLabelStr("");
        }
        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
            model.setFirstDimensionCode(null);
        }
        Map<String, Object> map;
        if (StrUtil.isNotBlank(model.getTopic())) {//共性问题
            String topiccode = model.getTopicCode();
            model.setTopicCode(null);
            map = qualityUserDiService.userAndStatisticGenerality(model);
            model.setTopicCode(topiccode);
            pop.setLabelStr(model.getTopic());

        } else {
            map = qualityUserDiService.userAndStatistic(model);
        }
        if (map != null) {
            pop.setStatistic((BigDecimal) map.get("intention"));
            pop.setUserCount((BigDecimal) map.get("userCount"));
        } else {
            pop.setStatistic(new BigDecimal(0));
        }
        return Result.OK(pop);
    }

    @Override
    public Result<?> qualityTagDetailFocus(LabelDetailFilterModel model) {
        String code = model.getCode();
        Result<?> result=  qualityTagDetailTitle(model);
        PopUpVo nuuser = (PopUpVo) result.getResult();
        if (StrUtil.isNotBlank(model.getRiskId())){//获取风险信息
            DwdVocQualityRisk risk=qualityRiskService.getById(model.getRiskId());
            model.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
            if (StrUtil.isNotBlank(risk.getChannelId())){
                model.setChannelIds(Arrays.asList(risk.getChannelId()));
            }
        }
        PopUpVo pop = new PopUpVo();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(pop);
        }

        FaultProblem vo;

        if (StrUtil.isNotBlank(code)) {
            if (code.equals(Constants.Q0001)) {
                model.setFirstDimensionCode(Constants.Q0001);
            }
            List<UserLabelVo> labelVos;
            //4级
            if (StrUtil.isNotBlank(model.getTopicCode())) {
                QueryWrapper<FaultProblem> query = new QueryWrapper<>();
                query.lambda().eq(StrUtil.isNotBlank(model.getTopicCode()), FaultProblem::getCode, model.getTopicCode());
                vo = faultProblemService.getOne(query);
                labelVos = qualityUserDiService.thirdTagDistributionByTopic(model, vo.getName());
                if (labelVos != null) {
                    labelVos.forEach(e -> {
                        e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), nuuser.getStatistic()));
                        e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), nuuser.getUserCount()));
                    });
                }
                if (labelVos.size() >= 10) {
                    pop.setAggDistribution(labelVos.subList(0, 10));
                } else {
                    pop.setAggDistribution(labelVos);
                }
                //3级
            } else if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
                labelVos = qualityUserDiService.topicTagByThird(model);
                if (labelVos != null) {
                    labelVos.forEach(e -> {
                        e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), nuuser.getStatistic()));
                        e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), nuuser.getUserCount()));
                    });
                }
                if (labelVos.size() >= 10) {
                    pop.setAggDistribution(labelVos.subList(0, 10));
                } else {
                    pop.setAggDistribution(labelVos);
                }
                //2级
            } else if (StrUtil.isNotBlank(model.getSecondDimensionCode()) || (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0)) {
                labelVos = qualityUserDiService.thirdDimensionCodeBySecond(model);
                if (labelVos != null) {
                    // 这里不能使用批量方法，因为分母是固定值且有两个不同的字段需要计算
                    labelVos.forEach(e -> {
                        e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), nuuser.getStatistic()));
                        e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), nuuser.getUserCount()));
                    });
                }
                if (labelVos.size() >= 10) {
                    pop.setAggDistribution(labelVos.subList(0, 10));
                } else {
                    pop.setAggDistribution(labelVos);
                }
                //1级
            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
                if (model.getFirstDimensionCode().equals(Constants.Q0001)) {
                    model.setFirstDimensionCode(null);
                    labelVos = qualityUserDiService.fistDimensionCode(model);
                } else {
                    labelVos = qualityUserDiService.secondDimensionCodeByFirst(model);
                }
                if (labelVos != null) {
                    // 这里不能使用批量方法，因为分母是固定值且有两个不同的字段需要计算
                    labelVos.forEach(e -> {
                        e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), nuuser.getStatistic()));
                        e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), nuuser.getUserCount()));
                    });
                }
                if (labelVos.size() >= 10) {
                    pop.setAggDistribution(labelVos.subList(0, 10));
                } else {
                    pop.setAggDistribution(labelVos);
                }
            }

        }
        return Result.OK(pop.getAggDistribution());
    }

    @Autowired
    EsDataSentenceVocService sentenceVocService;

    private Page<SoundContentVo> getSoundList(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        return sentenceVocService.pageQueryList(model, page);
    }

    private Page<SoundContentVo> getSoundListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        return sentenceVocService.pageQueryListExportXls(model, page);
    }

    private Page<SoundContentVo> onClickSoundsList(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        return sentenceVocService.pageQueryAllList(model, page);
    }

    private Page<SoundContentVo> onClickSoundsListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        return sentenceVocService.pageQueryAllListExportXls(model, page);
    }


    public Result<?> asyncAllSourceDataQuery(LabelDetailFilterModel model, Page<ContentVo> page, List<String> esIndexs, HttpServletRequest request) {
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        SpringContextUtils.setRequestURI(request.getRequestURI());
        final String taskId = insertLargeDigitalFileRecord("allSourceDataExportXls", "allSourceDataExportXls");
        SpringContextUtils.getExecutor().execute(() -> {
            try {
                this.exportUserListTask(model, page, esIndexs, taskId);
            } catch (Exception e) {
                log.error("异步导出用户列表失败", e);
            }
        });
        return Result.OK();
    }


    public void exportUserListTask(LabelDetailFilterModel model, Page<ContentVo> contentVoPage, List<String> esIndexs, String taskId) throws Exception {

        Page<ContentVo> userList = sentenceVocService.allSourceDataQuery(model, contentVoPage, esIndexs);
        log.info("待导出的结果总数据量:{}", userList.getTotal());
        String fileName = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_FORMAT.getPattern()) + "-原数据";
        log.info("结果总数据量:{}", fileName);
        largeDigitaFilesService.start(
                fileName,
                taskId,
                userList.getTotal(), page -> {
                    if (page.getCurrent() == 1) {
                        List<ContentVo> records = userList.getRecords();
                        try {
                            ObjectMapper objectMapper = new ObjectMapper();
                            String s = objectMapper.writeValueAsString(records);
                            records = objectMapper.readValue(s, new TypeReference<List<ContentVo>>(){});
                        } catch (Exception e) {
                            log.error("导出错误：{}",e);
                        }
                        log.info("查询数据>>>>{}", records.size());
                        return records;
                    }
                    Page<ContentVo> voPage = new Page<>(page.getCurrent(), page.getSize());
                    Page<ContentVo> data = sentenceVocService.allSourceDataQuery(model, voPage, esIndexs);
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        String s = objectMapper.writeValueAsString(data);
                        data = objectMapper.readValue(s, new TypeReference<Page<ContentVo>>(){});
                    } catch (Exception e) {
                        log.error("导出错误：{}",e);
                    }
                    log.info("查询数据>>>>{}", data.getRecords().size());
                    return data.getRecords();
                }, ContentVo.class, 10000);
    }

    private String insertLargeDigitalFileRecord(String taskType, String taskName) {
        final String taskId = UUID.randomUUID().toString();
        largeDigitaFilesService.insert(LargeDigitaFilesModel.builder()
                .id(taskId)
                .userId(SpringContextUtils.getUserId())
                .taskId(taskId)
                .taskName(taskName)
                .type(taskType)
                .status(null)
                .createTime(LocalDateTime.now())
                .build());
        return taskId;
    }

    @Override
    public LargeDigitaFilesModel getFile(LargeDigitaFilesModel model) {
        final String userId = SpringContextUtils.getUserId();
        model.setUserId(userId);
        LargeDigitaFilesModel file = largeDigitaFilesService.getFile(model);
        return file;
    }
}
