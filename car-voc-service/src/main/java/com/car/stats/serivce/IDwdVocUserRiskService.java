package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @version 1.0.0
 * @ClassName IDwdVocUserRiskService.java
 * @Description TODO
 * @createTime 2022年12月21日 11:09
 * @Copyright voc
 */
public interface IDwdVocUserRiskService extends IService<DwdVocUserRisk> {
    IPage<ComplaintUserVo> complaintUserList(ComplaintUserTopModel model);

    BigDecimal getComplaintUser(FilterCriteriaModel model);

    List<DwdVocUserRisk> riskUserFiltering(VocRiskWarningRules rule, RiskRuleVo vo);

    List<DwdVocUserRisk> riskUserFilteringNew(VocRiskWarningRules riskWarningRules, RiskRuleVo vo);
}
