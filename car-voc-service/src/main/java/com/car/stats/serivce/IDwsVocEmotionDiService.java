package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocEmotionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelSelectModel;
import com.car.stats.vo.EmotionProportionVo;
import com.car.voc.common.Result;

/**
 *
 * @version 1.0.0
 * @ClassName IDwsVocEmotionDiService.java
 * @Description TODO
 * @createTime 2022年10月17日 00:00
 * @Copyright voc
 */
public interface IDwsVocEmotionDiService extends IService<DwsVocEmotionDi> {
    Result<?> soundInsightBriefingValue(FilterCriteriaModel model);

    Result<EmotionProportionVo> focusProportionEmotion(FilterCriteriaModel model);

    Result<?> themeDistribution(FilterCriteriaModel model);
    Result<?> homeThemeDistribution(FilterCriteriaModel model);

    Result<?> themeShare(FilterCriteriaModel model);

    Result<?> hotWords(FilterCriteriaModel model);

    Result<?> soarHotWords(FilterCriteriaModel model);


    Result<?> channelDistribution(FilterCriteriaModel model);

    Result<?> channelTrend(FilterCriteriaModel model);

    Result<?> quantitytop(FilterCriteriaModel model);

    Result<?> analyEmotionMentionEate(LabelSelectModel model);

    Result<?> complaintWebsiteChannelTrend(FilterCriteriaModel model);

    Result<?> channelQuantityTop(FilterCriteriaModel model);

    Result<?> themeShare_new(FilterCriteriaModel model);

    Result<?> channelQuantityTop_new(FilterCriteriaModel model);

}
