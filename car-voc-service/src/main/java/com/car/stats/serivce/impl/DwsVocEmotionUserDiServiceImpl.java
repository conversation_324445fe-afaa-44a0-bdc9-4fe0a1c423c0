package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocEmotionUserDi;
import com.car.stats.mapper.DwsVocEmotionDiMapper;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.mapper.DwsVocIntentionDiMapper;
import com.car.stats.mapper.VocSentenceMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.*;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.UserDetailVo;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.stats.vo.wo.WoRescueVolumeVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.*;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.mapper.VocNPSAnalysisMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.IVocChannelCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName DwsVocEmotionUserDiImpl.java
 * @Description TODO
 * @createTime 2022年10月20日 15:07
 * @Copyright voc
 */
@Service
@Slf4j
public class DwsVocEmotionUserDiServiceImpl extends ServiceImpl<DwsVocEmotionUserDiMapper, DwsVocEmotionUserDi> implements IDwsVocEmotionUserDiService {
    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;

    @Resource
    DwsVocIntentionDiMapper intentionDiMapper;

    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;
    @Resource
    VocSentenceMapper vocSentenceMapper;
    @Autowired
    Executor defExecutor;
    @Override
    public Result<?> regionalDistribution(FilterCriteriaModel model) {
        Map<String, Object> re = new HashMap<>();
        List<RegionUserVo> userVos = emotionUserDiMapper.regionalDistribution(model);
        model.SetUpCycle();
        VocOverBriefingValueVo res = dwsVocEmotionDiMapper.overviewBriefingValue(model);
        Map<String, Long> user = emotionUserDiService.userNum(model);

        BigDecimal sttotal = BigDecimal.ZERO;
        BigDecimal usertotal = BigDecimal.ZERO;
        if (res == null) {
            res = new VocOverBriefingValueVo();
        }
        sttotal = res.getTotalMentions();
        if (user != null) {
            res.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
            usertotal = res.getUsersNum();
        }


        List<RegionUserVo> regionss = new ArrayList<>();
        Set<String> restr = new HashSet<>();
        List<RegionUserVo> regions;
        List<RegionUserVo> regionsUp;
        regions = emotionUserDiMapper.regionUser(model);

        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        regionsUp = emotionUserDiMapper.regionUser(model);
        List<RegionUserVo> userVosUp = emotionUserDiMapper.regionalDistribution(model);


        BigDecimal finalSttotal = sttotal;
        BigDecimal finalUsertotal = usertotal;
        userVos.forEach(e -> {
            RegionUserVo onet = userVosUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(onet) ? null : onet.getStatistic()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
            } else if (model.getDataType().equals(DataEnum.numUsers)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
            }
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalSttotal));
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), finalUsertotal));
        });
        re.put("map", userVos);

        regions.forEach(e -> {
            RegionUserVo onet = regionsUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(onet) ? null : onet.getStatistic()));
            } else {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
            }
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), finalUsertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalSttotal));
            if (!restr.contains(e.getRegionStr())) {
                restr.add(e.getRegionStr());
                regionss.add(e);
            }
        });
        if (model.getDataType().equals(DataEnum.numMention)) {
            regionss.sort(Comparator.comparing(RegionUserVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getStatisticR)
                    .thenComparing(RegionUserVo::getRegionCode)
                    .reversed());
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            regionss.sort(Comparator.comparing(RegionUserVo::getUserNum, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getStatisticR)
                    .thenComparing(RegionUserVo::getRegionCode)
                    .reversed());
        }
        re.put("region", regionss);
        return Result.OK(re);
    }
    @Override
    public Result<?> focusRegionalTop(FilterCriteriaModel model) {
        model.SetUpCycle();
        VocOverBriefingValueVo res = dwsVocEmotionDiMapper.overviewBriefingValue(model);
        Map<String, Long> user = emotionUserDiService.userNum(model);
        BigDecimal sttotal = BigDecimal.ZERO;
        BigDecimal usertotal = BigDecimal.ZERO;
        if (res == null) {
            res = new VocOverBriefingValueVo();
        }
        sttotal = res.getTotalMentions();
        if (user != null) {
            res.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
            usertotal = res.getUsersNum();
        }
        List<RegionUserVo> regionss = new ArrayList<>();
        Set<String> restr = new HashSet<>();
        List<RegionUserVo> regions;
        List<RegionUserVo> regionsUp;
        regions = emotionUserDiMapper.focusRegionalTop(model);

        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        regionsUp = emotionUserDiMapper.focusRegionalTop(model);

        extracted(model, sttotal, usertotal, regions, regionsUp, restr, regionss);
        return Result.OK(regionss);
    }

    private static void extracted(FilterCriteriaModel model, BigDecimal sttotal, BigDecimal usertotal, List<RegionUserVo> regions, List<RegionUserVo> regionsUp, Set<String> restr, List<RegionUserVo> regionss) {
        BigDecimal finalSttotal = sttotal;
        BigDecimal finalUsertotal = usertotal;
        regions.forEach(e -> {
            RegionUserVo onet = regionsUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(onet) ? null : onet.getStatistic()));
            } else {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
            }
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), finalUsertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalSttotal));
            if (!restr.contains(e.getRegionStr())) {
                restr.add(e.getRegionStr());
            }
            regionss.add(e);
        });
        if (model.getDataType().equals(DataEnum.numMention)) {
            regionss.sort(Comparator.comparing(RegionUserVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getStatisticR)
                    .thenComparing(RegionUserVo::getRegionCode)
                    .reversed());
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            regionss.sort(Comparator.comparing(RegionUserVo::getUserNum, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getStatisticR)
                    .thenComparing(RegionUserVo::getRegionCode)
                    .reversed());
        }
    }

    @Override
    public Result<?> focusCommunityTop(FilterCriteriaModel model) {
        model.SetUpCycle();
        VocOverBriefingValueVo res = dwsVocEmotionDiMapper.overviewBriefingValue(model);
        Map<String, Long> user = emotionUserDiService.userNum(model);
        BigDecimal sttotal = BigDecimal.ZERO;
        BigDecimal usertotal = BigDecimal.ZERO;
        if (res == null) {
            res = new VocOverBriefingValueVo();
        }
        sttotal = res.getTotalMentions();
        if (user != null) {
            res.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
            usertotal = res.getUsersNum();
        }
        List<RegionUserVo> regionss = new ArrayList<>();
        Set<String> restr = new HashSet<>();
        List<RegionUserVo> regions;
        List<RegionUserVo> regionsUp;
        regions = emotionUserDiMapper.focusCommunityTop(model);

        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        regionsUp = emotionUserDiMapper.focusCommunityTop(model);
        extracted(model, sttotal, usertotal, regions, regionsUp, restr, regionss);
        return Result.OK(regionss);
    }


    @Override
    public Result<?> provinceMap(FilterCriteriaModel model) {
        List<RegionUserVo> userVos = emotionUserDiMapper.provinceMap(model);
        VocOverBriefingValueVo res = dwsVocEmotionDiMapper.overviewBriefingValue(model);
        userVos.stream().forEach(e->{
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), res.getTotalMentions()));
        });
        return Result.OK(userVos);
    }



    @Override
    public Result<?> rankingProvinces(FilterCriteriaModel model) {
        List<RegionUserVo> userVos = emotionUserDiMapper.regionalDistribution(model);
        model.SetUpCycle();

        VocOverBriefingValueVo res = dwsVocEmotionDiMapper.overviewBriefingValue(model);
        Map<String, Long> user = emotionUserDiService.userNum(model);

        BigDecimal sttotal = BigDecimal.ZERO;
        BigDecimal usertotal = BigDecimal.ZERO;
        if (res == null) {
            res = new VocOverBriefingValueVo();
        }
        sttotal = res.getTotalMentions();
        if (user != null) {
            res.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
            usertotal = res.getUsersNum();
        }

        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<RegionUserVo> userVosUp = emotionUserDiMapper.regionalDistribution(model);

        BigDecimal finalUsertotal = usertotal;
        BigDecimal finalSttotal = sttotal;
        userVos.forEach(e -> {

            RegionUserVo onet = userVosUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(onet) ? null : onet.getStatistic()));
            } else {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null : onet.getUserNum()));
            }
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), finalUsertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalSttotal));
        });
        if (model.getDataType().equals(DataEnum.numMention)) {
            userVos.sort(Comparator.comparing(RegionUserVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getStatisticR)
            .reversed());
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            userVos.sort(Comparator.comparing(RegionUserVo::getUserNum, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getUserNumR)
                    .reversed());
        }


        return Result.OK(userVos);
    }

    @Override
    public Result<?> topVoiceUsers(FilterCriteriaModel model) {

        AtomicReference<BigDecimal> total =new AtomicReference(BigDecimal.ZERO);
        AtomicReference<List<TopVoiceUsersVo>> usersVos =new AtomicReference(new ArrayList<>());
        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);

        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            FilterCriteriaModel target = new FilterCriteriaModel();
            BeanUtil.copyProperties(model, target);

            VocOverBriefingValueVo re = emotionUserDiService.overviewBriefingValue(target);
            if (re == null) {
                re = new VocOverBriefingValueVo();
            }
            total.set(re.getTotalMentions());
            return null;
        })));

        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            FilterCriteriaModel target = new FilterCriteriaModel();
            BeanUtil.copyProperties(model, target);
            target.setRownum(target.isExcel() ? target.getDownLoadUserTop() : null);
            usersVos.set(emotionUserDiMapper.topVoiceUsers(target));
            return null;
        })));

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        /*VocOverBriefingValueVo re = emotionUserDiService.overviewBriefingValue(model);
        if (re == null) {
            re = new VocOverBriefingValueVo();
        }
        total = re.getTotalMentions();*/


       /* model.setRownum(model.isExcel() ? model.getDownLoadUserTop() : null);
        List<TopVoiceUsersVo> usersVos = emotionUserDiMapper.topVoiceUsers(model);*/
        //净情感值（(正面提及量-负面提及量)/(正面提及量+负面提及量)*100%）；

//        BigDecimal finalTotal = total;
        usersVos.get().forEach(e -> {
            e.setNetWorth(CalculatorUtils.getEmotionalNetWorth(e.getPositive(), e.getNegative()));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total.get()));
        });

        usersVos.get().sort(Comparator.comparing(TopVoiceUsersVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(TopVoiceUsersVo::getStatisticP)
                .reversed());

        return Result.OK(usersVos.get());
    }

    @Override
    public Map<String, Long> userNum(FilterCriteriaModel model) {
        Map<String, Long> userscount = emotionUserDiMapper.userNum(model);
        return userscount;
    }

    @Override
    public BigDecimal userDistinctNum(FilterCriteriaModel model) {
        BigDecimal userscount = emotionUserDiMapper.userDistinctNum(model);
        return userscount;
    }

    @Override
    public Map<String, Object> homeBriefingValue(FilterCriteriaModel model) {
        return emotionUserDiMapper.homeBriefingValue(model);
    }

    @Override
    public Map<String, Object> VipUser(FilterCriteriaModel model) {
        return emotionUserDiMapper.VipUser(model);
    }

    @Override
    public Map<String, Object> certifiedOwner(FilterCriteriaModel model) {
        return emotionUserDiMapper.certifiedOwner(model);
    }

    @Override
    public VocOverBriefingValueVo overviewBriefingValue(FilterCriteriaModel model) {
        return emotionUserDiMapper.overviewBriefingValue(model);
    }

    @Override
    public VocOverBriefingValueVo overviewBriefingTotalValue(FilterCriteriaModel model) {
        return emotionUserDiMapper.overviewBriefingTotalValue(model);
    }

    @Override
    public List<ChannelStatisticVo> sourceChannel(FilterCriteriaModel model) {
        return emotionUserDiMapper.sourceChannel(model);
    }

    @Override
    public Result<?> overviewChannelDistribution(FilterCriteriaModel model) {
        model.SetUpCycle();
        List<ChannelUserVo> chvos;
        BigDecimal chtotal;
        BigDecimal usertotal;
        chvos = setChannelAllList(model);
        chtotal = chvos.stream().map(ChannelUserVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        usertotal = chvos.stream().map(ChannelUserVo::getUserCount).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        chvos.forEach(e -> {
            e.setDataSourceStr(channelCategoryService.getById(e.getDataSource()).getName());
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), chtotal));
            e.setUserCountP(CalculatorUtils.proportion(e.getUserCount(), usertotal));
        });
        chvos.sort(Comparator.comparing(ChannelUserVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(ChannelUserVo::getStatisticP)
                .reversed());
        int a = model.isExcel() ? model.getDownloadChannelNum() : 10;
        return Result.OK(chvos.size() > a ? new ArrayList<>(chvos.subList(0, a)) : chvos);


    }

    private List<ChannelUserVo> setChannelAllList(FilterCriteriaModel model) {
        List<ChannelUserVo> objects = new ArrayList<>();
        List<String> datasous = model.getDataSources();
        List<String> channels = model.getChannelIds();
        if (model.isCPoint()) {
            //查询非私域
            model.getDataSources().remove(CommonConstant.privateChannelId);
            objects = emotionUserDiMapper.dataSourceDistributionChUser(model);
            //查询私域
            model.setChannelId(CommonConstant.privateChannelId);
            model.setDataSources(null);
            model.setChannelIds(null);
            objects.addAll(emotionUserDiMapper.channelDistributionChUser(model));
            model.setChannelId(null);
        } else if (model.getChannelId() != null && !"".equals(model.getChannelId())) {
            model.setDataSources(null);
            objects.addAll(emotionUserDiMapper.channelDistributionChUser(model));
        } else {
            objects = emotionUserDiMapper.dataSourceDistributionChUser(model);
            model.setChannelId(CommonConstant.privateChannelId);
            model.setDataSources(null);
            model.setChannelIds(null);
            objects.addAll(emotionUserDiMapper.channelDistributionChUser(model));
            model.setChannelId(CommonConstant.b2cChannelId);
            model.setDataSources(null);
            model.setChannelIds(null);
            objects.addAll(emotionUserDiMapper.channelDistributionChUser(model));
            model.setChannelId(null);
        }

        return objects;

    }

    @Override
    public List<UserLabelVo> secondTagDistribution(LabelDetailFilterModel model) {

        return emotionUserDiMapper.secondTagDistribution(model);
    }

    @Override
    public List<UserLabelVo> thirdTagDistribution(LabelDetailFilterModel model) {
        return emotionUserDiMapper.thirdTagDistribution(model);
    }

    @Autowired
    VocNPSAnalysisMapper vocNPSAnalysisMapper;

    @Override
    public List<HomePurposeTrendVo> trendChangeLabel(LabelDetailFilterModel model) {
        //修改环比计算+时间范围补齐
        List<HomePurposeTrendVo> objectLists = new ArrayList<>();
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        if (model.getDateUnit() == -1) {
            model.setStartDate(model.getDateS());
        }

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());

            List<HomePurposeTrendVo> objects;
            if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
                model.setChannelIds(null);
                objects = vocNPSAnalysisMapper.trendChangeLabelList(model);
            } else {
                objects = emotionUserDiMapper.trendChangeLabelList(model);
            }
            objectLists.addAll(objects);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e ->
                {
//                    HomePurposeTrendVo.builder().dateStr(e).build()
                    HomePurposeTrendVo vo = new HomePurposeTrendVo();
                    vo.setDateStr(e);
                    return vo ;
                })
                .collect(Collectors.toList())
        );

        objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            HomePurposeTrendVo obj = objectLists.get(i);  //当前
            HomePurposeTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            if (days==null||preDays==null) {
                break;
            }
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(preDays));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(preDays));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
//            obj.setUserCountAR(CalculatorUtils.ringRatio(obj.getUserCountA(), preObj.getUserCountA()));
            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
//            obj.setIntentionAR(CalculatorUtils.ringRatio(obj.getIntentionA(), preObj.getIntentionA()));

        }


        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }
        return objectLists;
    }
    @Override
    public List<HomePurposeTrendVo> trendChangeLabelNew(LabelDetailFilterModel model) {
        //修改环比计算+时间范围补齐
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        if (model.getDateUnit() == -1) {
            model.setStartDate(model.getDateS());
        }
        ExecutorService executor = Executors.newWorkStealingPool();
        // 使用并发集合，例如 ConcurrentLinkedDeque
        ConcurrentLinkedDeque<HomePurposeTrendVo> safeFinalObjectLists = new ConcurrentLinkedDeque<>();
        List<CompletableFuture<Void>> futures = dates.stream().map(date -> {
            LabelDetailFilterModel model1=BeanUtil.copyProperties(model,LabelDetailFilterModel.class);
            return CompletableFuture.runAsync(() -> extracted(date, model1, safeFinalObjectLists), executor);
        }).collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executor.shutdown();
        // 将线程安全的集合转换为普通的List，以便后续使用
        List<HomePurposeTrendVo> objectLists = new ArrayList<>(safeFinalObjectLists);
        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    HomePurposeTrendVo vo =new HomePurposeTrendVo();
                    vo.setIntention(BigDecimal.ZERO);
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            HomePurposeTrendVo obj = objectLists.get(i);  //当前
            HomePurposeTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(preDays));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(preDays));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
//            obj.setUserCountAR(CalculatorUtils.ringRatio(obj.getUserCountA(), preObj.getUserCountA()));
            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
//            obj.setIntentionAR(CalculatorUtils.ringRatio(obj.getIntentionA(), preObj.getIntentionA()));
            if(ObjectUtil.isNull(obj.getIntention())) {
                obj.setIntention(BigDecimal.ZERO);
            }
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }
        return objectLists;
    }


    @Override
    public List<HomePurposeTrendVo> allTagsTrend(LabelDetailFilterModel model) {
        List<HomePurposeTrendVo> objectLists = vocSentenceMapper.allTagsTrend(model);
        if (CollUtil.isNotEmpty(objectLists)){
            if(model.getDateUnit() == -1 && DateUtil.between(DateUtil.parseDate(model.getStartDate()),DateUtil.parse(model.getEndDate()), DateUnit.DAY)>0){
                objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr));
                return objectLists;
            }
            objectLists.remove(0);
        }
        return objectLists;
    }

    private void extracted(SvwDate date, LabelDetailFilterModel model1, ConcurrentLinkedDeque<HomePurposeTrendVo> safeFinalObjectLists) {
        model1.setStartDate(date.getStartDate());
        model1.setEndDate(date.getEndDate());
        HomePurposeTrendVo oe = emotionUserDiMapper.trendChangeLabelNew(model1);
        oe.setDateStr(date.getTime());
        // 使用线程安全的集合来存储结果
        safeFinalObjectLists.add(oe);
    }

    @Override
    public Map<String, String> userAndStatistic(LabelDetailFilterModel model) {
        return emotionUserDiMapper.userAndStatistic(model);
    }

    @Autowired
    IDwsVocUserService dwsVocUserService;
    @Autowired
    ViewLabelDetailsService detailsService;

    @Override
    public Page<UserListInfoVo> getUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {

        Page<UserListInfoVo> list;
        long start0 = System.currentTimeMillis();
        if (model.getMediaName1() != null || model.getMediaName2() != null) {
//            list = workorderEmotionUserService.getUserList(model, page);
            list=null;
        } else {
            if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(model.getColumn())&&!"statistic".equalsIgnoreCase(model.getColumn())&&!"emotionWorth".equalsIgnoreCase(model.getColumn())){
                model.setColumn(null);
            }
            if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(model.getOrder())&&!"asc".equalsIgnoreCase(model.getOrder())&&!"desc".equalsIgnoreCase(model.getOrder())){
                model.setOrder(null);
            }

            list = emotionUserDiMapper.getUserList(model, page);
        }
        long start2 = System.currentTimeMillis();
        log.info("从mysql获取用户列表,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(start2 - start0) > 0 ? TimeUnit.MILLISECONDS.toSeconds(start2 - start0) + "秒" : (start2 - start0) + "毫秒");
        Set<String> idList = list.getRecords().stream().map(UserListInfoVo::getUserId).collect(Collectors.toSet());
        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        log.info("从Es获取用户内容数,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (!(model.getMenuName() != null && "feedbackAnalysis".equals(model.getMenuName()))) {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        }
        Map<String, UserDetailVo> userDetailVoMap = detailsService.queryUserEmotionList(model, queryBuilder, idList);
        long end1 = System.currentTimeMillis();
        log.info("从Es获取用户感情净值,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end1 - end) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end1 - end) + "秒" : (end1 - end) + "毫秒");

        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        ExecutorService executor=Executors.newWorkStealingPool();
        List<CompletableFuture<Void>> futures = list.getRecords().stream().map(e->CompletableFuture.runAsync(() -> extracted(e, userDetailVoMap),executor)).collect(Collectors.toList());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executor.shutdown();
        long end2 = System.currentTimeMillis();
        log.info("设置渠道与发布数,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end2 - end1) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end2 - end1) + "秒" : (end2 - end1) + "毫秒");
        if (ObjectUtils.isNotEmpty(model.getColumn())) {
            if ("emotionWorth".equals(model.getColumn())) {
                if ("desc".equals(model.getOrder())) {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getEmotionWorth, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
                } else {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getEmotionWorth, Comparator.nullsFirst(BigDecimal::compareTo)));
                }
            } else if ("statistic".equals(model.getColumn())) {
                if ("desc".equals(model.getOrder())) {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
                } else {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo)));
                }
            }
        }
        return list;
    }
@Override
    public Page<UserListInfoVo> allTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {

        Page<UserListInfoVo> list;
        long start0 = System.currentTimeMillis();
        if (model.getMediaName1() != null || model.getMediaName2() != null) {
//            list = workorderEmotionUserService.getUserList(model, page);
            list=null;
        } else {
            if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(model.getColumn())&&!"statistic".equalsIgnoreCase(model.getColumn())&&!"emotionWorth".equalsIgnoreCase(model.getColumn())){
                model.setColumn(null);
            }
            if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(model.getOrder())&&!"asc".equalsIgnoreCase(model.getOrder())&&!"desc".equalsIgnoreCase(model.getOrder())){
                model.setOrder(null);
            }
            log.info("查询分页参数：{},{}",page.getCurrent(),page.getSize());
            list = emotionUserDiMapper.getallTagUserList(model, page);
            log.info("查询分页数量：{}",list.getRecords().size());
        }
        long start2 = System.currentTimeMillis();
        log.info("从mysql获取用户列表,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(start2 - start0) > 0 ? TimeUnit.MILLISECONDS.toSeconds(start2 - start0) + "秒" : (start2 - start0) + "毫秒");
        Set<String> idList = list.getRecords().stream().map(UserListInfoVo::getUserId).collect(Collectors.toSet());
        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        log.info("从Es获取用户内容数,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();

        Map<String, UserDetailVo> userDetailVoMap = detailsService.queryUserEmotionList(model, queryBuilder, idList);
        long end1 = System.currentTimeMillis();
        log.info("从Es获取用户感情净值,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end1 - end) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end1 - end) + "秒" : (end1 - end) + "毫秒");

        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        ExecutorService executor=Executors.newWorkStealingPool();
        List<CompletableFuture<Void>> futures = list.getRecords().stream().map(e->CompletableFuture.runAsync(() -> extracted(e, userDetailVoMap),executor)).collect(Collectors.toList());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executor.shutdown();
        long end2 = System.currentTimeMillis();
        log.info("设置渠道与发布数,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end2 - end1) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end2 - end1) + "秒" : (end2 - end1) + "毫秒");
        if (ObjectUtils.isNotEmpty(model.getColumn())) {
            if ("emotionWorth".equals(model.getColumn())) {
                if ("desc".equals(model.getOrder())) {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getEmotionWorth, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
                } else {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getEmotionWorth, Comparator.nullsFirst(BigDecimal::compareTo)));
                }
            } else if ("statistic".equals(model.getColumn())) {
                if ("desc".equals(model.getOrder())) {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
                } else {
                    list.getRecords().sort(Comparator.comparing(UserListInfoVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo)));
                }
            }
        }
        return list;
    }

    private void extracted(UserListInfoVo e, Map<String, UserDetailVo> userDetailVoMap) {
        e.setEmotionWorth(CalculatorUtils.getEmotionalNetWorth(e.getPositive(), e.getNegative()));
        UserDetailVo uv = userDetailVoMap.get(e.getUserId());
        if (uv != null) {
            Set<String> chstrs = new HashSet<>();
            for (ChannelVo channelDi : uv.getChannelDis()) {
                if (StrUtil.isNotBlank(channelDi.getChannelId())) {
                    VocChannelCategory chd = channelCategoryService.getByIdCache(channelDi.getChannelId());
                    if (chd != null) {
                        chstrs.add(chd.getName());
                    }
                }
            }
            e.setChannelStr(chstrs);
            e.setPublishCount(uv.getPublishCount());
        }
    }
    @Override
    public List<UserLabelVo> topicTagDistribution(LabelDetailFilterModel model) {
        return emotionUserDiMapper.topicTagDistribution(model);
    }

    @Override
    public List<HighHotWordsVo> hotWordsUser(LabelDetailFilterModel model) {
        return emotionUserDiMapper.hotWordsUser(model);
    }

    public String getUserId() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return loginUser.getId();
    }

    @Override
    public Result<?> intentionUserTrends(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        List<DateUserStatisticVo> objectLists = new ArrayList<>();
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(objectLists);
        }

        Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
            model.getSecondDimensionCodes().retainAll(sounps);
        } else {
            model.setSecondDimensionCodes(new ArrayList<>(sounps));
        }


        for (SvwDate date : new ArrayList<>(dates)) {
            TrendChannelVo cha = new TrendChannelVo();
            cha.setDateStr(date.getTime());   //时间范围
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            Optional<DateUserStatisticVo> one = Optional.ofNullable(emotionUserDiMapper.intentionUserTrends(model));
            if (one.isPresent()) {
                one.get().setDateStr(date.getTime());
                objectLists.add(one.get());
            }
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    DateUserStatisticVo vo =new DateUserStatisticVo();
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(DateUserStatisticVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            DateUserStatisticVo obj = objectLists.get(i);  //当前
            DateUserStatisticVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setStatisticAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getStatistic(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getStatistic(), new BigDecimal(preDays));
                obj.setStatisticA(objAvgDays);
                obj.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setUserNumAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserNum(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getUserNum(), new BigDecimal(preDays));
                obj.setUserNumA(objAvgDays);
                obj.setUserNumAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }

            obj.setStatisticR(CalculatorUtils.ringRatio(obj.getStatistic(), preObj.getStatistic()));
            obj.setUserNumR(CalculatorUtils.ringRatio(obj.getUserNum(), preObj.getUserNum()));
            if(ObjectUtil.isNull(obj.getStatistic())){
                obj.setStatistic(BigDecimal.ZERO);
            }
        }


        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }

        return Result.OK(objectLists);

    }


    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public List<String> riskCarSeries(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskCarSeries(model);
    }

    @Override
    public List<String> riskCarSeriesExport(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskCarSeriesExport(model);
    }

    @Override
    public List<String> riskCarSeriesStr(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskCarSeriesStr(model);
    }

    @Override
    public List<HighHotWordsVo> riskHotWordsOpinion(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskHotWordsOpinion(model);
    }

    @Override
    public BigDecimal riskStatisticTotal(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskStatisticTotal(model);
    }

    @Override
    public VocOverBriefingValueVo riskBriefingValue(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskBriefingValue(model);
    }

    @Override
    public Map<String, BigDecimal> riskAllTotal(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskAllTotal(model);
    }

    @Override
    public Map<String, BigDecimal> riskUserNum(RiskEventInsightModel model) {
        Map<String, BigDecimal> userscount = emotionUserDiMapper.riskUserNum(model);
        return userscount;
    }

    @Override
    public BigDecimal riskUserTotalNum(RiskEventInsightModel model) {
        return emotionUserDiMapper.riskUserTotalNum(model);
    }

    @Override
    public List<String> homeChannelStrs(FilterCriteriaModel model) {
        return emotionUserDiMapper.homeChannelStrs(model);
    }

    @Override
    public Result<?> complaintWebsiteTop(FilterCriteriaModel model) {
        List<ChannelUserVo> userVos = emotionUserDiMapper.complaintWebsiteTop(model);
        model.SetUpCycle();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<ChannelUserVo> userVosUp = emotionUserDiMapper.complaintWebsiteTop(model);
        userVos.forEach(e -> {
            ChannelUserVo oe = userVosUp.stream().filter(d -> e.getDataSource().equals(d.getDataSource())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (oe != null) {
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), oe.getUserNum()));
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), oe.getStatistic()));
            }
        });
        return Result.OK(userVos);
    }

    @Override
    public Result<?> complaintWebsiteThemeTop(FilterCriteriaModel model) {
        List<LabelUserVo> vos = emotionUserDiMapper.complaintWebsiteThemeTop(model);
        model.SetUpCycle();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(vos.stream().map(LabelUserVo::getLabelCode).collect(Collectors.toSet()));

        List<LabelUserVo> vosUp = emotionUserDiMapper.complaintWebsiteThemeTop(model);
        vos.forEach(e -> {
            LabelUserVo oe = vosUp.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (oe != null) {
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), oe.getUserNum()));
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), oe.getStatistic()));
            }
        });
        if (model.getDataType() == DataEnum.numUsers) {
            vos.sort(Comparator.comparing(LabelUserVo::getUserNum, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(LabelUserVo::getUserNumR, Comparator.nullsFirst(BigDecimal::compareTo))
                    .reversed());
        } else {
            vos.sort(Comparator.comparing(LabelUserVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(LabelUserVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo))
                    .reversed());
        }
        return Result.OK(vos);
    }

    @Autowired
    IDwsVocEmotionDiService vocEmotionDiService;

    @Override
    public Result<?> complaintWebsiteTrend(FilterCriteriaModel model) {
//        List<ChannelVo> userVos=emotionUserDiMapper.complaintWebsiteTrend(model);
        return vocEmotionDiService.complaintWebsiteChannelTrend(model);
    }

    @Override
    public Map riskTrend(RiskEventInsightModel model) {

        return baseMapper.riskTrend(model);
    }

    @Override
    public List<Map> listRiskTrend(RiskEventInsightModel model) {

        return baseMapper.listRiskTrend(model);
    }

    @Override
    public List<HighHotWordsVo> lastTagHotWords(LabelDetailFilterModel model) {
        model.setRownum(10);
        List<HighHotWordsVo> hotword = baseMapper.lastTagHotWords(model);
        hotword.stream().forEach(e -> {
            e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
            e.setUserCountP(NumberUtil.round(NumberUtil.mul(e.getUserCountP(), 100), 2, RoundingMode.HALF_UP));
        });
        return hotword;
    }

}
