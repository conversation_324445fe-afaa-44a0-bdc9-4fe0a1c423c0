package com.car.stats.serivce.impl.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.serivce.es.EsDataContentVocService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.ContentVo;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.popvo.ContentUserDetailVo;
import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.Result;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.service.ISysDictService;
import com.car.voc.service.IVocChannelCategoryService;
import com.car.voc.vo.DictVo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.NoSuchIndexException;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName EsDataContentVocServiceImpl.java
 * @Description TODO
 * @createTime 2022年11月02日 13:41
 * @Copyright voc
 */
@Service
@Slf4j
public class EsDataContentVocServiceImpl implements EsDataContentVocService {

    @Autowired
    private ElasticsearchRestTemplate esrest;
    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private EsDataContentVocService esDataContentVocService;

    @Override
    public Result<?> appSoundDetails(String contentId, String channelId, String indexId, String id) {
        boolean vocSentenceRepeated = false;
        String index;
        if (StrUtil.isNotBlank(indexId)) {
            index = indexId;
        } else {
            EsDataSentenceVocs sound = sentenceVocService.getId(id);
            if (sound == null) {
                sound = sentenceVocService.getIdRepeated(id);
                if (sound == null) {
                    return Result.OK();
                } else {
                    vocSentenceRepeated = true;
                    index = sound.getOriginalIndex();
                }
            } else {
                index = sound.getOriginalIndex();
            }
        }

        List<VocChannelCategory> categories = channelCategoryService.cacheList();

        VocChannelCategory conten = categories.stream().filter(e -> e.getId().equals(channelId)).findFirst().orElse(null);
        List<SoundContentVo> sounds;
        if (vocSentenceRepeated) {
            sounds = sentenceVocService.userContentSoundRepeated(contentId, channelId, index, id, categories);
        } else {
            sounds = sentenceVocService.userContentSound(contentId, channelId, index, id, categories);
        }
        ContentUserDetailVo userDetailVo = new ContentUserDetailVo();
        // 根据声音中的内容id去原数据中获取内容
        Map conte = esDataContentVocService.getContentBySound(index, contentId);
        if (sounds != null && sounds.size() > 0) {
            userDetailVo.setUserId(sounds.get(0).getUserId());
            userDetailVo.setUserLevel(sounds.get(0).getUserLevel());
            userDetailVo.setUserType(sounds.get(0).getUserType());
        } else if (id == null && indexId != null) {
            SoundContentVo sounf = new SoundContentVo();
            sounds = new ArrayList<>();
            Map<String, String> keyf = new HashMap<>();
            List<DictVo> prs = sysDictService.queryDictItemsByCode(index.replace("_backup", "").replace("_2", ""));
            prs.forEach(e -> {
                if (keyf != null) {
                    keyf.put(e.getValue(), e.getText());
                }
            });
            ContentVo se = BeanUtil.toBean(conte, ContentVo.class, CopyOptions.create().setFieldMapping(keyf));
            if (index.contains("voc_original_session_info")) {
                se = new ContentVo();
                Map<String, Object> sess = (Map<String, Object>) conte.get("contentBase");
                String usin = (String) sess.get("visitorName");
                se.setCustomerName(usin);
            }
            sounf.setChannelId(channelId == null ? "" : channelId);
            sounf.setChannelStr(conten != null ? conten.getName() : (String) conte.get("medianamecn"));
            sounf.setUserName(se.getCustomerName());
            sounf.setUserId(se.getOneid());
            sounf.setProvince(se.getCustomerProvince());
            sounf.setIsOneId(index.contains("voc_original_subject") ? "0" : "1");
            sounf.setSound("");
            sounds.add(sounf);
        }
        Map re = new HashMap();
        re.put("content", conte);
        re.put("contentType", conten != null ? conten.getFlag() : "");
        re.put("sound", sounds);
        return Result.OK(re);
    }


    private void setQueryContentId(VocChannelCategory esindex, BoolQueryBuilder queryBuilder, String contentId) {
        queryBuilder.must(QueryBuilders.termQuery("id.keyword", contentId));


    }


    @Override
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN, DataDesensitization.DesensitizationType.NAME})
    public Map getContentBySound(String index, String contentId) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
//        setQueryContentId(null,queryBuilder,contentId);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();

        BoolQueryBuilder prbool = new BoolQueryBuilder();
        prbool.should(QueryBuilders.termsQuery("_id", contentId));
        BoolQueryBuilder nobool = new BoolQueryBuilder();
        nobool.should(QueryBuilders.termsQuery("id.keyword", contentId));
        if (index.contains("voc_original_call_in_out")) {
            nobool.should(QueryBuilders.termsQuery("callId.keyword", contentId));
            // Painless 脚本转换 (字符串 -> 数字)
            Script script = new Script(
                    "Long.parseLong(doc['createTime.keyword'].value)"
            );
            searchQuery.withPageable(PageRequest.of(0, 10000));
            searchQuery.withSort(new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER)
                    .order(SortOrder.ASC));
        }
        prbool.should(nobool);
        queryBuilder.must(prbool);

        searchQuery.withQuery(queryBuilder);
        log.info("声音-es查询：" + queryBuilder);
        NativeSearchQuery query = searchQuery.build();
        Map re = new HashMap();
        if (index.contains("voc_original_session_info")) {
            String sessionIdByTraceId = emotionUserDiMapper.getSessionIdByTraceId(contentId);
            List<Map<String, Object>> sessions;
            Map<String, Object> sessionone;
            if (StrUtil.isNotEmpty(sessionIdByTraceId)) {
                sessions = emotionUserDiMapper.sessionListUserIdNew(sessionIdByTraceId);
                sessionone = emotionUserDiMapper.getSessionOneNew(sessionIdByTraceId);
            } else {
                log.info("兼容之前老数据");
                sessions = emotionUserDiMapper.sessionListUserId(contentId);
                sessionone = emotionUserDiMapper.getSessionOne(contentId);
            }
            Map<String, Object> sess = new HashMap<>();
            sess.put("contentBase", sessionone);
            sess.put("contentList", sessions);
            re = sess;
        } else if (index.contains("voc_original_call_in_out")) {
            log.debug("内容-es查询【" + index + "】：" + queryBuilder);
            SearchHits<Map> co = null;

            try {
                co = esrest.search(query, Map.class, IndexCoordinates.of(index));
            } catch (NoSuchIndexException e) {
                e.getStackTrace();
                log.error("未找到索引:{}", index);
                return null;
            }
            List<Map> callInOut = co.getSearchHits().stream().map(SearchHit::getContent).collect(Collectors.toList());
            Map callInOutBase = callInOut.stream().filter(e -> e.get("role") != null && "0".equals(e.get("role"))).findFirst().orElse(new HashMap<>());
            Map role1 = callInOut.stream().filter(e -> e.get("role") != null && "1".equals(e.get("role"))).findFirst().orElse(new HashMap<>());
            callInOutBase.put("sendUserNick", role1.get("customerName"));
            Map<String, Object> sess = new HashMap<>();
            sess.put("contentBase", callInOutBase);
            sess.put("contentList", callInOut);
            re = sess;
        } else {
            log.debug("内容-es查询【" + index + "】：" + queryBuilder);
            SearchHit<Map> co = null;
            try {
                co = esrest.searchOne(query, Map.class, IndexCoordinates.of(index));
            } catch (NoSuchIndexException e) {
                e.getStackTrace();
                log.error("未找到索引:{}", index);
                return null;
            }
            if (co != null) {
                re = co.getContent();

            } else {
                re = null;
            }
        }
        return re;
    }

    @Override
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN, DataDesensitization.DesensitizationType.NAME})
    public Map setContentByContentIds(List<String> contentIds, String index) {


        Map re = new HashMap();
        if (index.contains("voc_original_session_info")) {
//            ExecutorService executor = Executors.newFixedThreadPool(10);
//            for (String contentId : contentIds) {
//                executor.submit(() -> {
//                    String sessionIdByTraceId = emotionUserDiMapper.getSessionIdByTraceId(contentId);
//                    List<Map<String, Object>> sessions;
//                    Map<String, Object> sessionone;
//                    if (StrUtil.isNotEmpty(sessionIdByTraceId)) {
//                        sessions = emotionUserDiMapper.sessionListUserIdNew(sessionIdByTraceId);
//                        sessionone = emotionUserDiMapper.getSessionOneNew(sessionIdByTraceId);
//                    } else {
//                        log.info("兼容之前老数据");
//                        sessions = emotionUserDiMapper.sessionListUserId(contentId);
//                        sessionone = emotionUserDiMapper.getSessionOne(contentId);
//                    }
//                    Map<String, Object> sess = new HashMap<>();
//                    sess.put("contentBase", sessionone);
//                    sess.put("contentList", sessions);
//                    synchronized (contents) {
//                        contents.put(contentId, sess);
//                    }
//                });
//            }
//            executor.shutdown();
//
//            try {
//                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }

            // List<String> sessionIdByTraceIdList = emotionUserDiMapper.getSessionIdByTraceIdList(contentIds);
            List<String> sessionIdByTraceIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(sessionIdByTraceIdList)) {
                log.info("处理通过traceId查询逻辑");

            } else {
                log.info("兼容之前老数据");
                List<Map<String, Object>> mapList = emotionUserDiMapper.sessionListUserIdList(contentIds);
                List<Map<String, Object>> sessionOneList = emotionUserDiMapper.getSessionOneList(contentIds);
                for (String contentId : contentIds) {
                    List<Map<String, Object>> sessionId = mapList.stream().filter(e -> e.get("sessionId").equals(contentId)).collect(Collectors.toList());
                    Map<String, Object> sessionone = sessionOneList.stream().filter(e -> e.get("sessionId").equals(contentId)).findFirst().get();
                    Map<String, Object> sess = new HashMap<>();
                    sess.put("contentBase", sessionone);
                    sess.put("contentList", sessionId);
                    synchronized (re) {
                        re.put(contentId, sess);
                    }
                }
            }


        } else {
            BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
            Pageable pageable = PageRequest.of(0, contentIds.size());
            queryBuilder.must(QueryBuilders.termsQuery("id.keyword", contentIds));
            NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
            searchQuery.withQuery(queryBuilder);
            searchQuery.withPageable(pageable);
            log.info("声音-es查询：" + queryBuilder);
            NativeSearchQuery query = searchQuery.build();
            log.info("内容-es查询【" + index + "】：" + queryBuilder);
            SearchHits<Map> co = esrest.search(query, Map.class, IndexCoordinates.of(index));
            if (co != null) {
                co.getSearchHits().stream().forEach(e -> {
                    Map content = e.getContent();
                    String id = content.get("id") + "";
                    re.putIfAbsent(id, content);
                });
            }
        }
        return re;
    }


}
