package com.car.stats.serivce.impl.wo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.mapper.wo.BusinessOpportunityMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.serivce.wo.BusinessOpportunityService;
import com.car.stats.vo.ChannelVo;
import com.car.stats.vo.ProportionCarSeriesVo;
import com.car.stats.vo.RegionUserVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.stats.vo.wo.*;
import com.car.voc.common.Result;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
* <AUTHOR>
* @description 商机线索
* @createDate 2024-12-05 15:22:07
*/
@Service
public class BusinessOpportuntiyServiceImpl extends ServiceImpl<BusinessOpportunityMapper, WoOriginalData> implements BusinessOpportunityService {

    @Override
    public Result<?> clueDist(FilterCriteriaModel model) {
        List<ChannelVo> list = this.baseMapper.clueDist(model);
        return Result.OK(list);
    }

    @Override
    public Result<?> visitIntent(FilterCriteriaModel model) {
        VisitIntentVo visitIntentVo = new VisitIntentVo();
        visitIntentVo= baseMapper.visitIntent(model);
        return Result.OK(visitIntentVo);
    }

    @Override
    public Result<?> intendedBranchTop(FilterCriteriaModel model) {
        List<BranchesVo> branchesVos = baseMapper.intendedBranchTop(model);
        return Result.OK(branchesVos);
    }

    @Override
    public Result<?> intendedCarTop(FilterCriteriaModel model) {
    List<ProportionCarSeriesVo> subsetVos = baseMapper.intendedCarTop(model);
        // 判断列表是否只有一个元素且labelCode为null
        if (subsetVos != null && subsetVos.size() == 1 && subsetVos.get(0).getCarSeriesName() == null) {
            return Result.OK(new ArrayList<>());
        }
        return Result.OK(subsetVos);
    }

    @Override
    public Page<WoBusinessVo> woOrderList(FilterCriteriaModel model) {
        IPage<WoBusinessVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        return baseMapper.woOrderList(page,model);
    }

    @Override
    public Result<?> carPurchaseDemandTrend(FilterCriteriaModel model) {
        List<BuyCarTypeVo> re = baseMapper.carPurchaseDemandTrend(model);
        if (CollUtil.isNotEmpty(re)) {
            if(model.getDateUnit() == -1 && DateUtil.between(DateUtil.parseDate(model.getStartDate()),DateUtil.parse(model.getEndDate()), DateUnit.DAY)>0){
                re.sort(Comparator.comparing(BuyCarTypeVo::getDateStr));
                return Result.OK(re);
            }
            re.remove(0);
        }
        return Result.OK(re);
    }
}
