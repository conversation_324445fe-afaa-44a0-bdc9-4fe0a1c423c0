package com.car.stats.serivce.wo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.WoBusinessVo;
import com.car.voc.common.Result;

/**
* <AUTHOR>
* @description 商机线索
* @createDate 2024-12-05 15:22:07
*/
public interface BusinessOpportunityService extends IService<WoOriginalData> {

    Result<?> clueDist(FilterCriteriaModel model);

    Result<?> visitIntent(FilterCriteriaModel model);

    Result<?> intendedBranchTop(FilterCriteriaModel model);

    Result<?> intendedCarTop(FilterCriteriaModel model);

    Page<WoBusinessVo> woOrderList(FilterCriteriaModel model);

    Result<?> carPurchaseDemandTrend(FilterCriteriaModel model);
}
