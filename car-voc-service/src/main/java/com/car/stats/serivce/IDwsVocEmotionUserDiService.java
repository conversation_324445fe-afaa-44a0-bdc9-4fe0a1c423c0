package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocEmotionUserDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.ChannelStatisticVo;
import com.car.stats.vo.HighHotWordsVo;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.VocOverBriefingValueVo;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocEmotionUserDiService.java
 * @Description TODO
 * @createTime 2022年10月20日 15:07
 * @Copyright voc 聚焦关注
 */
public interface IDwsVocEmotionUserDiService extends IService<DwsVocEmotionUserDi> {
    Result<?> regionalDistribution(FilterCriteriaModel model);
    Result<?> focusRegionalTop(FilterCriteriaModel model);

    Result<?> rankingProvinces(FilterCriteriaModel model);

    Result<?> topVoiceUsers(FilterCriteriaModel model);

    Map<String, Long> userNum(FilterCriteriaModel model);

    BigDecimal userDistinctNum(FilterCriteriaModel model);

    Map<String, Object> homeBriefingValue(FilterCriteriaModel model);

    Map<String, Object> VipUser(FilterCriteriaModel model);

    Map<String, Object> certifiedOwner(FilterCriteriaModel model);

    VocOverBriefingValueVo overviewBriefingValue(FilterCriteriaModel model);
    VocOverBriefingValueVo overviewBriefingTotalValue(FilterCriteriaModel model);

    List<ChannelStatisticVo> sourceChannel(FilterCriteriaModel model);

    Result<?> overviewChannelDistribution(FilterCriteriaModel model);

    List<UserLabelVo> secondTagDistribution(LabelDetailFilterModel model);

    List<UserLabelVo> thirdTagDistribution(LabelDetailFilterModel model);

    List<HomePurposeTrendVo> trendChangeLabel(LabelDetailFilterModel model);
    List<HomePurposeTrendVo> trendChangeLabelNew(LabelDetailFilterModel model);
    List<HomePurposeTrendVo> allTagsTrend(LabelDetailFilterModel model);

    Map<String, String> userAndStatistic(LabelDetailFilterModel model);

    Page<UserListInfoVo> getUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);
    Page<UserListInfoVo> allTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);

    List<UserLabelVo> topicTagDistribution(LabelDetailFilterModel model);


    List<HighHotWordsVo> hotWordsUser(LabelDetailFilterModel model);

    Result<?>  intentionUserTrends(FilterCriteriaModel model);

    List<String> riskCarSeries(RiskEventInsightModel model);
    List<String> riskCarSeriesExport(RiskEventInsightModel model);

    List<HighHotWordsVo> riskHotWordsOpinion(RiskEventInsightModel model);

    BigDecimal riskStatisticTotal(RiskEventInsightModel model);

    VocOverBriefingValueVo riskBriefingValue(RiskEventInsightModel model);

    Map<String, BigDecimal> riskUserNum(RiskEventInsightModel model);

    BigDecimal riskUserTotalNum(RiskEventInsightModel model);

    List<String> homeChannelStrs(FilterCriteriaModel model);

    Result<?> complaintWebsiteTop(FilterCriteriaModel model);

    Result<?> complaintWebsiteThemeTop(FilterCriteriaModel model);

    Result<?> complaintWebsiteTrend(FilterCriteriaModel model);

    Map riskTrend(RiskEventInsightModel model);

    List<Map> listRiskTrend(RiskEventInsightModel model);

    List<HighHotWordsVo> lastTagHotWords(LabelDetailFilterModel model);

    Map<String, BigDecimal> riskAllTotal(RiskEventInsightModel model1);

    List<String> riskCarSeriesStr(RiskEventInsightModel model1);

    Result<?> provinceMap(FilterCriteriaModel model);

    Result<?> focusCommunityTop(FilterCriteriaModel model);
}
