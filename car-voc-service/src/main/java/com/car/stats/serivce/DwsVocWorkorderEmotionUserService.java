package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocWorkorderEmotionUser;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.WorkOrderFilteModel;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocWorkorderEmotionUserService.java
 * @Description TODO
 * @createTime 2022年11月07日 14:43
 * @Copyright voc
 */
public interface DwsVocWorkorderEmotionUserService extends IService<DwsVocWorkorderEmotionUser> {
    Result<?> workOrderType1();

    Result<?> workOrderUserType1(WorkOrderFilteModel model);

    Result<?> mediaTheme(WorkOrderFilteModel model);

    Map<String, String> userAndStatistic(LabelDetailFilterModel model);

    List<HomePurposeTrendVo> trendChangeLabelList(LabelDetailFilterModel model);

    IPage<UserListInfoVo> getUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);
}
