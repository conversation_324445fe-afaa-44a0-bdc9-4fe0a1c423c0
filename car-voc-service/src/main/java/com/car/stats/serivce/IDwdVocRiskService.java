package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName IDwdVocRiskService.java
 * @Description TODO
 * @createTime 2022年11月25日 12:02
 * @Copyright voc
 */

public interface IDwdVocRiskService extends IService<DwdVocRisk> {


    Result<?> dataAnalysisBriefing(RiskEventInsightModel model, DwdVocRisk risk);

    Result<?> dataAnalysisChannelTrend(RiskEventInsightModel model,DwdVocRisk risk);

    Result<?> emotionIntentionTrend(RiskEventInsightModel model,DwdVocRisk risk);
    Result<?> riskMailData(RiskEventInsightModel model);
    Result<?> riskstatictisTrend(RiskEventInsightModel model);
    Result<?> hotWords(RiskEventInsightModel model);

    Result<?> voiceUserTrend(RiskEventInsightModel model,DwdVocRisk risk);

    Result<?> voiceUserTop(RiskEventInsightModel model);

    IPage<RiskPointAggVo> riskPointAggNew(RiskEventInsightModel model);

    Result<?> dataAnalysisBriefingTrend(RiskEventInsightModel model,DwdVocRisk risk);

    BigDecimal getRiskEvents(FilterCriteriaModel model);

    List<DwdVocEmotionRisk> riskEventFiltering(VocRiskWarningRules rule, RiskRuleVo vo);

    Result<?> dataAnalysisWaringNum(RiskEventInsightModel model, DwdVocRisk risk);

    void riskExport();

    List<DwdVocEmotionRisk> riskEventFilteringNew(VocRiskWarningRules rule, RiskRuleVo vo);

}
