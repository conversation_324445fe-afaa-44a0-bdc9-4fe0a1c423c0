package com.car.stats.serivce.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocQualityUserDi;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.mapper.DwsVocQualityEmotionDiMapper;
import com.car.stats.mapper.DwsVocQualityUserDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.*;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.UserDetailVo;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.BigDecimalUtils;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.SvwDate;
import com.car.voc.entity.SysRole;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.ISysDictItemService;
import com.car.voc.service.IVocChannelCategoryService;
import org.apache.shiro.SecurityUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName DwsVocUserDiServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月19日 13:55
 * @Copyright voc
 */
@Service
public class DwsVocQualityUserDiServiceImpl extends ServiceImpl<DwsVocQualityUserDiMapper, DwsVocQualityUserDi> implements IDwsVocQualityUserDiService {
    @Resource
    DwsVocQualityUserDiMapper dwsVocQualityUserDiMapper;

    @Autowired
    ISysDictItemService iSysDictItemService;
    @Autowired
    StatsCommonService statsCommonService;


    @Autowired
    IDwsVocUserService dwsVocUserService;

    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;


    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;

    @Override
    public Result<?> qualityProblemTrend(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        List<DateUserStatisticVo> objectLists = new ArrayList<>();
        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            DateUserStatisticVo oen = dwsVocQualityUserDiMapper.overviewProblemTrends(model);

            if(ObjectUtil.isNull(oen)){
                oen =  new DateUserStatisticVo();
            }

            if(3 == model.getDateUnit().intValue()){
                oen.setDateStr(date.getTime().replace("-",""));
            }else{
                oen.setDateStr(date.getTime());
            }

            oen.setDateStr(date.getTime());
            objectLists.add(oen);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    DateUserStatisticVo vo =new DateUserStatisticVo();
                    vo.setDateStr(e);
                    return vo ;
                } )
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(DateUserStatisticVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            DateUserStatisticVo obj = objectLists.get(i);  //当前
            DateUserStatisticVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setStatisticAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getStatistic(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getStatistic(), new BigDecimal(preDays));
                obj.setStatisticA(objAvgDays);
                obj.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            if(model.getDateUnit().intValue() == -1) {
                obj.setUserNumAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserNum(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserNum(), new BigDecimal(preDays));
                obj.setUserNumA(objAvgDays);
                obj.setUserNumAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            obj.setUserNumR(CalculatorUtils.ringRatio(obj.getUserNum(),preObj.getUserNum()));
            obj.setStatisticR(CalculatorUtils.ringRatio(obj.getStatistic(), preObj.getStatistic()));
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }

        return Result.OK(objectLists);
    }

    @Resource
    DwsVocQualityEmotionDiMapper qualityEmotionDiMapper;

    @Resource
    DwsVocQualityUserDiMapper qualityUserDiMapper;

    @Override
    public Result<?> regionalDistribution(ProductQualityFilterCriteriaModel model) {


        Map<String, Object> re = new HashMap<>();
        if (!getQuality(model.getBrandCode())) {
            re.put("map", null);
            re.put("region", null);
            return Result.OK(re);
        }
        model.SetUpCycle();

        List<RegionUserVo> userVos = dwsVocQualityUserDiMapper.productRegionalDistribution(model);

        VocOverBriefingValueVo res = qualityEmotionDiMapper.briefingValue(model);
        if (res == null) {
            res = new VocOverBriefingValueVo();
            res.setTotalMentions(BigDecimal.ZERO);
        }
        Map<String, Object> user = qualityUserDiMapper.queryUserNumQuality(model);
        if (user != null) {
            res.setUsersNum(new BigDecimal((Long) user.get("thisUserNum")));
        }


        BigDecimal sttotal = res.getTotalMentions();
        BigDecimal usertotal = res.getUsersNum();


        userVos.forEach(e -> {
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), sttotal));
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), usertotal));
        });
        re.put("map", userVos);

        List<RegionUserVo> regions = dwsVocQualityUserDiMapper.productRegionUser(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<RegionUserVo> regionsUp = dwsVocQualityUserDiMapper.productRegionUser(model);

        model.SetUpCycle();
        regions.forEach(e -> {
            RegionUserVo onet = regionsUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode()))
                    .collect(Collectors.toList()).stream().findFirst().orElse(RegionUserVo.builder().build());
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.getStatistic()));
            } else {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), onet.getUserNum()));

            }
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), usertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), sttotal));
        });

        re.put("region", regions);
        return Result.OK(re);
    }
 @Override
    public Result<?> focusRegionalTop(ProductQualityFilterCriteriaModel model) {
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(null);
        }
        model.SetUpCycle();
        VocOverBriefingValueVo res = qualityEmotionDiMapper.briefingValue(model);
        if (res == null) {
            res = new VocOverBriefingValueVo();
            res.setTotalMentions(BigDecimal.ZERO);
        }
        Map<String, Object> user = qualityUserDiMapper.queryUserNumQuality(model);
        if (user != null) {
            res.setUsersNum(new BigDecimal((Long) user.get("thisUserNum")));
        }

        BigDecimal sttotal = res.getTotalMentions();
        BigDecimal usertotal = res.getUsersNum();

        List<RegionUserVo> regions = dwsVocQualityUserDiMapper.focusRegionalTop(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<RegionUserVo> regionsUp = dwsVocQualityUserDiMapper.focusRegionalTop(model);

        model.SetUpCycle();
        regions.forEach(e -> {
            RegionUserVo onet = regionsUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode()))
                    .collect(Collectors.toList()).stream().findFirst().orElse(RegionUserVo.builder().build());
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.getStatistic()));
            } else {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), onet.getUserNum()));

            }
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), usertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), sttotal));
        });
        return Result.OK(regions);
    }
    @Override
    public Result<?> focusCommunityTop(ProductQualityFilterCriteriaModel model) {
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(null);
        }
        model.SetUpCycle();
        VocOverBriefingValueVo res = qualityEmotionDiMapper.briefingValue(model);
        if (res == null) {
            res = new VocOverBriefingValueVo();
            res.setTotalMentions(BigDecimal.ZERO);
        }
        Map<String, Object> user = qualityUserDiMapper.queryUserNumQuality(model);
        if (user != null) {
            res.setUsersNum(new BigDecimal((Long) user.get("thisUserNum")));
        }

        BigDecimal sttotal = res.getTotalMentions();
        BigDecimal usertotal = res.getUsersNum();

        List<RegionUserVo> regions = dwsVocQualityUserDiMapper.focusCommunityTop(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<RegionUserVo> regionsUp = dwsVocQualityUserDiMapper.focusCommunityTop(model);

        model.SetUpCycle();
        regions.forEach(e -> {
            RegionUserVo onet = regionsUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode()))
                    .collect(Collectors.toList()).stream().findFirst().orElse(RegionUserVo.builder().build());
            if (model.getDataType().equals(DataEnum.numMention)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.getStatistic()));
            } else {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getUserNum(), onet.getUserNum()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), onet.getUserNum()));

            }
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), usertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), sttotal));
        });
        return Result.OK(regions);
    }

    @Autowired
    SysUserRoleMapper sysUserRoleMapper;

    boolean getQuality(String brandCode){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(qualityText)&& wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(brandCode)){
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)){
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Result<?> topQualityUsers(ProductQualityFilterCriteriaModel model) {
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(new ArrayList<>());
        }
        List<TopQualityUsersVo> usersVos = dwsVocQualityUserDiMapper.topQualityUsers(model);

        model.SetUpCycle();
        VocOverBriefingValueVo re = qualityEmotionDiMapper.briefingValue(model);
        if (re == null) {
            re = new VocOverBriefingValueVo();
        }
        BigDecimal countUser = re.getTotalMentions();
        model.setRownum(model.isExcel() ? model.getDownLoadUserTop() : null);
        usersVos.forEach(e -> {
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), countUser));
        });
        return Result.OK(usersVos);
    }

    @Override
    public Result<?> rankingProvinces(ProductQualityFilterCriteriaModel model) {
        List<RegionUserVo> userVos = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(userVos);
        }
        userVos = dwsVocQualityUserDiMapper.productRegionalDistribution(model);
        model.SetUpCycle();
        VocOverBriefingValueVo res = qualityEmotionDiMapper.briefingValue(model);
        if (res == null) {
            res = new VocOverBriefingValueVo();
            res.setTotalMentions(BigDecimal.ZERO);
        }
        Map<String, Object> user = qualityUserDiMapper.queryUserNumQuality(model);
        if (user != null) {
            res.setUsersNum(new BigDecimal((Long) user.get("thisUserNum")));
        }


        BigDecimal sttotal = res.getTotalMentions();
        BigDecimal usertotal = res.getUsersNum();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<RegionUserVo> userVosUp = dwsVocQualityUserDiMapper.productRegionalDistribution(model);


        userVos.forEach(e -> {
            RegionUserVo onet = userVosUp.stream().filter(d -> e.getRegionCode().equals(d.getRegionCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(onet) ? null:  onet.getStatistic()));
            e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), ObjectUtil.isNull(onet) ? null: onet.getUserNum()));
            e.setUserNumP(CalculatorUtils.proportion(e.getUserNum(), usertotal));
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), sttotal));
        });
        if (model.getDataType().equals(DataEnum.numMention)) {
            userVos.sort(Comparator.comparing(RegionUserVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getStatisticR)
                    .reversed());
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            userVos.sort(Comparator.comparing(RegionUserVo::getUserNum, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(RegionUserVo::getUserNumR)
                    .reversed());
        }
        return Result.OK(userVos);
    }
@Override
    public Result<?> provinceMap(ProductQualityFilterCriteriaModel model) {
        List<RegionUserVo> userVos = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(userVos);
        }
        userVos = dwsVocQualityUserDiMapper.provinceMap(model);
        return Result.OK(userVos);
    }

    @Override
    public Result<?> topVoiceUsers(FilterCriteriaModel model) {
        List<TopVoiceUsersVo> usersVos = dwsVocQualityUserDiMapper.topVoiceUsers(model);
        //净情感值（(正面提及量-负面提及量)/(正面提及量+负面提及量)*100%）；
        BigDecimal usertotal = usersVos.stream().map(TopVoiceUsersVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        usersVos.forEach(e -> {
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), usertotal));
        });
        return Result.OK(usersVos);
    }

    @Override
    public List<UserLabelVo> thirdTagDistributionByTopic(LabelDetailFilterModel model, String topicName) {
        return baseMapper.thirdTagDistributionByTopic(model, topicName);
    }

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public LableTrendVo trendChangeLabel(LabelDetailFilterModel model) {
        //修改环比计算+时间范围补齐
        if (!getQuality(model.getBrandCode())) {
            return new LableTrendVo();
        }
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        List<HomePurposeTrendVo> objectLists = new ArrayList<>();
        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            HomePurposeTrendVo oe;
            if (StrUtil.isNotBlank(model.getTopic())) {//共性问题
                model.setTopicCode(null);
                oe = baseMapper.trendChangeLabelGenerality(model);
            } else {
                oe = baseMapper.trendChangeLabel(model);
            }
            oe.setDateStr(date.getTime());
            objectLists.add(oe);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    HomePurposeTrendVo vo =new HomePurposeTrendVo();
                    vo.setIntention(BigDecimal.ZERO);
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            HomePurposeTrendVo obj = objectLists.get(i);  //当前
            HomePurposeTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(preDays));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(preDays));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
//            obj.setUserCountAR(CalculatorUtils.ringRatio(obj.getUserCountA(), preObj.getUserCountA()));
            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
//            obj.setIntentionAR(CalculatorUtils.ringRatio(obj.getIntentionA(), preObj.getIntentionA()));
            if(ObjectUtil.isNull(obj.getIntention())) {
                obj.setIntention(BigDecimal.ZERO);
            }
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }
        LableTrendVo vo = new LableTrendVo();
        vo.setLableTrend(objectLists);
        vo.setRepairTrend(new ArrayList<>());
        return vo;
    }

    /*public List<HomePurposeTrendVo> repairTrendChangeLabel(LabelDetailFilterModel model) {
        List<HomePurposeTrendVo> objectLists = new ArrayList<>();
        final long betweenDays = DateUtil.between(DateUtil.parse(model.getStartDate()), DateUtil.parse(model.getEndDate()), DateUnit.DAY) + 1;
        final int index =  model.getDateUnit() == -1 ? (betweenDays >6 ?  Long.valueOf(betweenDays).intValue() : 6)  : 6;
        List<SvwDate> dates = model.getDateTimes();
        model.setDateList(dates);

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(null);
            model.setEndDate(null);
            if (model.getDateUnit() == 3) {
                model.setYear(date.getYear());
            } else if (model.getDateUnit() == 2) {
                model.setYear(date.getYear());
                model.setSeason(date.getSeason());
            } else if (model.getDateUnit() == 1) {
                model.setYear(date.getYear());
                if (!date.getMonth().startsWith("0") && date.getMonth().length() == 1) {
                    model.setMonth("0" + date.getMonth());
                } else {
                    model.setMonth(date.getMonth());
                }
            } else if (model.getDateUnit() == 0) {
                model.setYear(date.getYear());
                model.setWeek(date.getWeek());
            } else {
                model.setStartDate(date.getStartDate());
                model.setEndDate(date.getEndDate());
            }
            HomePurposeTrendVo oe = new HomePurposeTrendVo();
            List<HomePurposeTrendVo> list = null;
            if (StrUtil.isNotBlank(model.getTopic())) {//共性问题
                model.setTopicCode(null);
                list = baseMapper.repairTrendChangeLabelGenerality(model);
            } else {
                list = baseMapper.repairTrendChangeLabel(model);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                oe.setUserCount(list.stream().filter(o -> o.getBusType().equals("user")).findFirst().orElse(new HomePurposeTrendVo()).getIntention());
                oe.setIntention(list.stream().filter(o -> o.getBusType().equals("tag")).findFirst().orElse(new HomePurposeTrendVo()).getIntention());
            }
            if (oe == null) {
                oe = new HomePurposeTrendVo();
            }
            oe.setDateStr(date.getTime());

            objectLists.add(oe);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    HomePurposeTrendVo vo = new HomePurposeTrendVo();
                    vo.setDateStr(e);
                    return vo ;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            HomePurposeTrendVo obj = objectLists.get(i);  //当前
            HomePurposeTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = DateUtil.between(DateUtil.parse(model.getEndDate()), DateUtil.parse(model.getStartDate()),DateUnit.DAY) +1;
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(days));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(days));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() >= index ) {
            objectLists = new ArrayList<>(objectLists.subList(dates.size() - index, dates.size()));
        }


        return objectLists;
    }*/

    @Override
    public Map<String, Object> userAndStatistic(LabelDetailFilterModel model) {
        return baseMapper.userAndStatistic(model);
    }

    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Autowired
    ViewLabelDetailsService detailsService;

    @Override
    public Page<UserListInfoVo> getUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {

        Page<UserListInfoVo> list = new Page<>();

        if (!getQuality(model.getBrandCode())) {
            return list;
        }
        if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(model.getColumn())&&!"statistic".equalsIgnoreCase(model.getColumn())&&!"emotionWorth".equalsIgnoreCase(model.getColumn())){
            model.setColumn(null);
        }
        if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(model.getOrder())&&!"asc".equalsIgnoreCase(model.getOrder())&&!"desc".equalsIgnoreCase(model.getOrder())){
            model.setOrder(null);
        }
        list = baseMapper.getUserList(model, page);
        Set<String> idList = list.getRecords().stream().map(UserListInfoVo::getUserId).collect(Collectors.toSet());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        Map<String, UserDetailVo> userDetailVoMap = detailsService.queryUserEmotionList(model, queryBuilder, idList);
        ExecutorService executor = Executors.newWorkStealingPool();
        List<CompletableFuture<Void>> futures = list.getRecords().stream().map(e -> CompletableFuture.runAsync(() -> extracted(e, userDetailVoMap), executor)).collect(Collectors.toList());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executor.shutdown();
        return list;
    }

    private void extracted(UserListInfoVo e, Map<String, UserDetailVo> userDetailVoMap) {
        e.setEmotionWorth(CalculatorUtils.getEmotionalNetWorth(e.getPositive(), e.getNegative()));
        UserDetailVo uv = userDetailVoMap.get(e.getUserId());
        if (uv != null) {
            Set<String> chstrs = new HashSet<>();
            for (ChannelVo channelDi : uv.getChannelDis()) {
                if (StrUtil.isNotBlank(channelDi.getChannelId())) {
                    VocChannelCategory chd = channelCategoryService.getByIdCache(channelDi.getChannelId());
                    if (chd != null) {
                        chstrs.add(chd.getName());
                    }
                }
            }
            e.setChannelStr(chstrs);
            e.setPublishCount(uv.getPublishCount());
        }
    }

    @Override
    public List<UserLabelVo> thirdDimensionCodeBySecond(LabelDetailFilterModel model) {
        return baseMapper.thirdDimensionCodeBySecond(model);
    }

    @Override
    public List<UserLabelVo> secondDimensionCodeByFirst(LabelDetailFilterModel model) {
        return baseMapper.secondDimensionCodeByFirst(model);
    }
    @Override
    public List<UserLabelVo> fistDimensionCode(LabelDetailFilterModel model) {
        return baseMapper.fistDimensionCode(model);
    }

    @Override
    public List<UserLabelVo> topicTagByThird(LabelDetailFilterModel model) {
        return baseMapper.topicTagByThird(model);
    }

    @Override
    public Map<String, Object> userAndStatisticGenerality(LabelDetailFilterModel model) {
        return baseMapper.userAndStatisticGenerality(model);
    }

    @Override
    public VocOverBriefingValueVo riskBriefingValue(RiskEventInsightModel model) {
        return baseMapper.riskBriefingValue(model);
    }

    @Override
    public BigDecimal riskStatisticTotal(RiskEventInsightModel model) {
        return baseMapper.riskStatisticTotal(model);
    }

    @Override
    public Map<String, Object> riskUserNum(RiskEventInsightModel model) {
        Map<String, Object> userscount = baseMapper.riskUserNum(model);
        return userscount;
    }

    @Override
    public BigDecimal riskUserTotalNum(RiskEventInsightModel model) {
        return baseMapper.riskUserTotalNum(model);
    }

    @Override
    public List<String> riskCarSeries(RiskEventInsightModel model) {
        return baseMapper.riskCarSeries(model);
    }

    @Override
    public List<HighHotWordsVo> riskHotWordsOpinion(RiskEventInsightModel model) {
        return baseMapper.riskHotWordsOpinion(model);
    }


    @Override
    public List<HighHotWordsVo> lastTagHotWords(LabelDetailFilterModel model) {
        model.setRownum(10);
        List<HighHotWordsVo> hotword = baseMapper.lastTagHotWords(model);
        hotword.stream().forEach(e -> {
            e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
            e.setUserCountP(NumberUtil.round(NumberUtil.mul(e.getUserCountP(), 100), 2, RoundingMode.HALF_UP));
        });
        return hotword;
    }


    @Override
    public Map<String, BigDecimal> riskAllTotal(RiskEventInsightModel model) {
        return baseMapper.riskAllTotal(model);
    }

    @Override
    public List<String> riskCarSeriesStr(RiskEventInsightModel model) {
        return baseMapper.riskCarSeriesStr(model);
    }


}
