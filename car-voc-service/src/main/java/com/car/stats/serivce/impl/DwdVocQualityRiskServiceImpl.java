package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocQualityRiskF;
import com.car.stats.mapper.DwdVocQualityRiskMapper;
import com.car.stats.mapper.DwsVocQualityUserDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.IDwsVocQualityUserDiService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.*;
import com.car.stats.vo.risk.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.*;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.service.*;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName IDwdVocRiskServiceImpl.java
 * @Description TODO
 * @createTime 2022年11月25日 12:02
 * @Copyright voc
 */
@Service
public class DwdVocQualityRiskServiceImpl extends ServiceImpl<DwdVocQualityRiskMapper, DwdVocQualityRisk> implements IDwdVocQualityRiskService {


    @Autowired
    IDwsVocQualityUserDiService qualityUserDiService;


    @Resource
    DwsVocQualityUserDiMapper qualityUserDiMapper;


    @Autowired
    EsDataSentenceVocService sentenceVocService;

    @Autowired
    IVocBusinessTagService tagService;
    @Autowired
    IFaultProblemService faultProblemService;

    @Override
    public Result<?> dataAnalysisBriefing(RiskEventInsightModel model, DwdVocQualityRisk risk) {
        RiskBriefingVo briefingVo = new RiskBriefingVo();
        RiskEventInsightModel model1 = getRiskEventInsightModel(model, risk, briefingVo);
        VocOverBriefingValueVo re = qualityUserDiService.riskBriefingValue(model1);
        String riskType="质量问题风险";
        extractedVocOverBriefing(model, risk, re, briefingVo, model1,riskType);
        return Result.OK(briefingVo);
    }    @Override
    public Result<?> dataAnalysisBriefingRescue(RiskEventInsightModel model, DwdVocQualityRisk risk) {
        RiskBriefingVo briefingVo = new RiskBriefingVo();
        model.setChannelIds(Arrays.asList(risk.getChannelId()));
        RiskEventInsightModel model1 = getRiskEventInsightModel(model, risk, briefingVo);
        VocOverBriefingValueVo re = qualityUserDiService.riskBriefingValue(model1);
        String riskType="救援故障预警";
        extractedVocOverBriefing(model, risk, re, briefingVo, model1,riskType);
        return Result.OK(briefingVo);
    }

    private void extractedVocOverBriefing(RiskEventInsightModel model, DwdVocQualityRisk risk, VocOverBriefingValueVo re, RiskBriefingVo briefingVo, RiskEventInsightModel model1,String riskType) {
        if (re == null) {
            re = new VocOverBriefingValueVo();
        }
        briefingVo.setStatistic(risk.getTotalNum());

        if (re.getTotalMentions() != null && re.getTotalMentions().longValue() > 0 && re.getTotalMentionsUp() == null) {
            briefingVo.setStatisticR(new BigDecimal(1));
        } else {
            briefingVo.setStatisticR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
        }
        briefingVo.setStatisticTotal(qualityUserDiService.riskStatisticTotal(model));
        Map<String, Object> user = qualityUserDiService.riskUserNum(model1);
        if (user != null) {
            briefingVo.setUsersNum(new BigDecimal((Long) user.get("userCount")));
            if (user.get("userCountUp") != null && ((Long) user.get("userCountUp")) == 0) {
                briefingVo.setUsersNumR(new BigDecimal(1));
            } else {
                briefingVo.setUsersNumR(CalculatorUtils.ringRatio(briefingVo.getUsersNum(), new BigDecimal((Long) user.get("userCountUp"))));
            }
        }
        briefingVo.setUserTotalNum(qualityUserDiService.riskUserTotalNum(model));
        briefingVo.setCarSeries(qualityUserDiService.riskCarSeries(model));
        Set<String> aggprom = new HashSet<>();
        aggprom.add(model.getTopicCode());
        briefingVo.setAggProblem(aggprom);
        briefingVo.setHotWords(qualityUserDiService.riskHotWordsOpinion(model));

        //20231012修改读取风险数据库
//        String riskLevel = CalculatorUtils.getRiskLevel(risk.getRiskIndex());
        String riskLevel = vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode(  riskType, risk.getBrandCode(), risk.getRiskIndex());

        briefingVo.setRiskGradeTag(riskLevel);
    }

    private static @NotNull RiskEventInsightModel getRiskEventInsightModel(RiskEventInsightModel model, DwdVocQualityRisk risk, RiskBriefingVo briefingVo) {
        briefingVo.setWarnPeriod(risk.getStatisticType());
        briefingVo.setTimeStr(DateUtils.dateToStr(risk.getPublishDate()));
        briefingVo.setStartDate(briefingVo.getTimeStr() + " 00:00:00");
        briefingVo.setEndDate(briefingVo.getTimeStr() + " 23:59:59");
        briefingVo.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));


        model.setStartDate(briefingVo.getStartDate());
        model.setEndDate(briefingVo.getEndDate());
        model.setEmotion("负面");
        RiskEventInsightModel model1 = BeanUtil.copyProperties(model, RiskEventInsightModel.class);
        model1.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model1.setEndDate(CalculatorUtils.getRiskEndDate(briefingVo.getDateUnit(), risk.getPublishDate()));
        model1.SetUpCycle();
        if (briefingVo.getDateUnit() == -1) {
            model1.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
            model1.setEndDate(DateUtil.formatDate(risk.getPublishDate())+" 23:59:59");
            model1.setEndDateUp(DateUtil.formatDate(DateUtil.offsetDay(risk.getPublishDate(), -1))+" 23:59:59");
            model1.setStartDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(risk.getPublishDate(), -1)));
        }
        model1.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        return model1;
    }

    @Autowired
    IVocChannelCategoryService channelCategoryService;

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public Result<?> emotionIntentionTrend(RiskEventInsightModel model, DwdVocQualityRisk risk) {
        //修改环比计算+时间范围补齐
        List<IntentionEmotionTrendVo> objectLists = new ArrayList<>();
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        if (model.getDateUnit().equals(-1)) {
            model.setStartDate(DateUtil.offsetDay(risk.getPublishDate(), -9).toString());
        }else {
            model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        }
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            IntentionEmotionTrendVo emotionVo = qualityUserDiMapper.emotionIntentionTrend(model);

            if(ObjectUtil.isNull(emotionVo)){
                emotionVo = IntentionEmotionTrendVo.builder().build();
            }

            if(3 == model.getDateUnit().intValue()){
                emotionVo.setDateStr(date.getTime().replace("-",""));
            }else{
                emotionVo.setDateStr(date.getTime());
            }

            emotionVo.setDateStr(date.getTime());
            objectLists.add(emotionVo);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> IntentionEmotionTrendVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(IntentionEmotionTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            IntentionEmotionTrendVo obj = objectLists.get(i);  //当前
            IntentionEmotionTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setPraiseAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getPraise(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getPraise(), new BigDecimal(preDays));
                obj.setPraiseA(objAvgDays);
                obj.setPraiseAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setComplaintAR(BigDecimal.ZERO);
            }else{

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getComplaint(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getComplaint(), new BigDecimal(preDays));
                obj.setComplaintA(objAvgDays);
                obj.setComplaintAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setNegativeAR(BigDecimal.ZERO);
            }else{

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getNegative(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getNegative(), new BigDecimal(preDays));
                obj.setNegativeA(objAvgDays);
                obj.setNegativeAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            BigDecimal total = NumberUtil.add(obj.getPraise(), obj.getComplaint(), obj.getNegative());
            obj.setNegativeP(CalculatorUtils.proportion(obj.getNegative(), total));
            obj.setPraiseP(CalculatorUtils.proportion(obj.getPraise(), total));
            obj.setComplaintP(CalculatorUtils.proportion(obj.getComplaint(), total));


            obj.setNegativeR(CalculatorUtils.ringRatio(obj.getNegative(), preObj.getNegative()));
            obj.setPraiseR(CalculatorUtils.ringRatio(obj.getPraise(), preObj.getPraise()));
            obj.setComplaintR(CalculatorUtils.ringRatio(obj.getComplaint(), preObj.getComplaint()));

            if (ObjectUtil.isNull(obj.getComplaint())) {
                obj.setComplaint(BigDecimal.ZERO);
            }
            if (ObjectUtil.isNull(obj.getPraise())) {
                obj.setPraise(BigDecimal.ZERO);
            }
            if (ObjectUtil.isNull(obj.getNegative())) {
                obj.setNegative(BigDecimal.ZERO);
            }
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            if(s>index){
                objectLists = new ArrayList<>(objectLists.subList( s-index  , objectLists.size()));
            }else if(s<index){
                objectLists = new ArrayList<>(objectLists.subList(  index -s  , objectLists.size()));
            }else {
                objectLists = new ArrayList<>(objectLists.subList(  1  , objectLists.size()));
            }
        }
        return Result.OK(objectLists);

    }

    @Override
    public Result<?> intentionTrend(RiskEventInsightModel model, DwdVocQualityRisk risk) {
        //修改环比计算+时间范围补齐
        List<HomePurposeTrendVo> objectLists = new ArrayList<>();
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        if (model.getDateUnit().equals(-1)) {
            model.setStartDate(DateUtil.offsetDay(risk.getPublishDate(), -9).toString());
        }else {
            model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        }
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            HomePurposeTrendVo emotionVo = qualityUserDiMapper.intentionTrend(model);

            if(ObjectUtil.isNull(emotionVo)){
                emotionVo =new HomePurposeTrendVo();
            }

            if(3 == model.getDateUnit().intValue()){
                emotionVo.setDateStr(date.getTime().replace("-",""));
            }else{
                emotionVo.setDateStr(date.getTime());
            }

            emotionVo.setDateStr(date.getTime());
            objectLists.add(emotionVo);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(
                dateTempSet.stream()
                        .map(e -> new HomePurposeTrendVo(e))
                        .collect(Collectors.toList())
        );
        //时间排序
        objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            HomePurposeTrendVo obj = objectLists.get(i);  //当前
            HomePurposeTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(preDays));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(preDays));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
            if (ObjectUtil.isNull(obj.getIntention())) {
                obj.setIntention(BigDecimal.ZERO);
            }
            if (ObjectUtil.isNull(obj.getUserCount())) {
                obj.setUserCount(BigDecimal.ZERO);
            }
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            if(s>index){
                objectLists = new ArrayList<>(objectLists.subList( s-index  , objectLists.size()));
            }else if(s<index){
                objectLists = new ArrayList<>(objectLists.subList(  index -s  , objectLists.size()));
            }else {
                objectLists = new ArrayList<>(objectLists.subList(  1  , objectLists.size()));
            }
        }
        return Result.OK(objectLists);

    }

    @Override
    public Result<?> regionalDistribution(RiskEventInsightModel model) {
        List<RegionUserVo> regions = qualityUserDiMapper.focusRegionalTopRisk(model);
/*        regions.stream().forEach(e->{
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), re.getTotalMentions()));
        });*/
        return Result.OK(regions);
    }


    @Override
    public Result<?> hotWords(RiskEventInsightModel model) {
        List<HighHotWordsVo> hotWordsVos = qualityUserDiMapper.riskHotWords(model);
        return Result.OK(hotWordsVos);
    }


    @Override
    public Result<?> voiceUserTrend(RiskEventInsightModel model, DwdVocQualityRisk risk) {
        //修改环比计算+时间范围补齐
        List<VoiceUserVo> objectLists = new ArrayList<>();
//        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
//        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));


        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));


        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            VoiceUserVo oen = qualityUserDiMapper.riskVoiceUserTrend(model);
            if (oen.getUserNum() == null || oen.getUserNum().intValue() == 0) {
                oen.setUserNum(BigDecimal.ZERO);
            }
            if (oen == null) {
                oen = new VoiceUserVo();
            }

            oen.setDateStr(date.getTime());
            objectLists.add(oen);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> VoiceUserVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(VoiceUserVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            VoiceUserVo obj = objectLists.get(i);  //当前
            VoiceUserVo preObj = objectLists.get(i - 1);   //前一个

            obj.setUserNumR(CalculatorUtils.ringRatio(obj.getUserNum(), preObj.getUserNum()));
        }


        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            if(s>index){
                objectLists = new ArrayList<>(objectLists.subList( s-index  , objectLists.size()));
            }else if(s<index){
                objectLists = new ArrayList<>(objectLists.subList(  index -s  , objectLists.size()));
            }else {
                objectLists = new ArrayList<>(objectLists.subList(  1  , objectLists.size()));
            }
        }
        BigDecimal sum = objectLists.stream().map(VoiceUserVo::getUserNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        Map<String, Object> re = new HashMap<>();
//        if (total!=null&)
        re.put("average", NumberUtil.round(NumberUtil.div(sum, objectLists.size()), 2, RoundingMode.DOWN));
        re.put("userTrend", objectLists);
        return Result.OK(re);
    }


    @Override
    public Result<?> voiceUserTop(RiskEventInsightModel model) {
        model.setRownum(10000);
        List<VoiceUserTopVo> userTopVos = qualityUserDiMapper.voiceUserTop(model);
        Map<String, List<VoiceUserTopVo>> ches = userTopVos.stream().collect(Collectors.groupingBy(VoiceUserTopVo::getChannelStr));
        List<ChannelUserTopVo> topVos = new ArrayList<>();
        ches.entrySet().forEach(e -> {
            ChannelUserTopVo topVo = new ChannelUserTopVo();
            BigDecimal usertotal = e.getValue().stream().map(VoiceUserTopVo::getUserNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            topVo.setTotal(usertotal);
            topVo.setChannelStr(e.getKey());
            if (e.getValue().size() >= 10) {
                topVo.setData(e.getValue().subList(0, 10));
            } else {
                topVo.setData(e.getValue());
            }
            topVos.add(topVo);
        });
        topVos.sort(Comparator.comparing(ChannelUserTopVo::getTotal, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(ChannelUserTopVo::getChannelStr)
                .reversed());
        return Result.OK(topVos);
    }

    @Autowired
    RedisUtil redisUtil;

    @Override
    public IPage<RiskPointAggVo> riskPointAggNew(RiskEventInsightModel model) {
        Page<RiskPointAggVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        String riskType="质量问题风险";
        VocRiskWarningRules warningRules = vocRiskWarningRulesService.getByBrandCode(model.getBrandCode(), riskType);
        RiskRuleVo vo = getRiskRuleVo(model, warningRules);
        IPage<RiskPointAggVo> pages = baseMapper.riskPointAggNew(page, model, vo);
        extracted(model, pages,riskType);
        return pages;
    }

    @Override
    public IPage<RiskPointAggVo> riskRescueList(RiskEventInsightModel model) {
        Page<RiskPointAggVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        String riskType="救援故障预警";
        VocRiskWarningRules warningRules = vocRiskWarningRulesService.getByBrandCode(model.getBrandCode(), riskType);
        RiskRuleVo vo = getRiskRuleVo(model, warningRules);
        IPage<RiskPointAggVo> pages = baseMapper.riskRescueList(page, model, vo);
        extracted(model, pages,riskType);
        return pages;
    }

    @Override
    public List<DwdVocQualityRiskF> riskRescueFiltering(VocRiskWarningRules riskWarningRules, RiskRuleVo vo) {
        return baseMapper.riskRescueFiltering(riskWarningRules,vo);
    }

    private void extracted(RiskEventInsightModel model, IPage<RiskPointAggVo> pages,String riskType) {
        pages.getRecords().forEach(e -> {
            model.setTopicCode(e.getTopicCode());
            model.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
            model.setStartDate(DateUtil.formatDateTime(e.getPublishDate()));
            model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), e.getPublishDate()));
            if (model.getDateUnit() == -1) {
                model.setEndDateUp(DateUtil.formatDate(DateUtil.offsetDay(e.getPublishDate(), -1))+" 23:59:59");
                model.setStartDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(e.getPublishDate(), -1)));
            }
            model.SetUpCycle();

            e.setStartDate(model.getStartDate());
            e.setEndDate(model.getEndDate());
            e.setDateUnit(model.getDateUnit());

            if (Objects.nonNull(e.getRiskIndex())){
                //20231012修改读取风险数据库
//                String riskLevel = CalculatorUtils.getRiskLevel(e.getRiskIndex());
                String riskLevel = vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode(riskType,e.getBrandCode(),e.getRiskIndex());
                e.setRiskGradeTag(riskLevel);
            }
            model.setCreateDate(e.getCreateTime());
            model.setBrandCode(e.getBrandCode());
            Map<String, BigDecimal> a = qualityUserDiMapper.riskTrend(model);
            if (a != null) {
                if (a.get("totalMentionsUp") == null) {
                    e.setRise(-1);
                    e.setRise(1);
                } else if (a.get("totalMentions") == null) {
                    e.setRise(-1);
                } else {
                    BigDecimal th = a.get("totalMentions");
                    BigDecimal up = a.get("totalMentionsUp");
                    if (up == null) {
                        e.setRise(1);
                    } else if (th == null) {
                        e.setRise(-1);
                    } else {
                        e.setRise(th.compareTo(up));
                    }
                }
            }


        });
    }

    private @Nullable RiskRuleVo getRiskRuleVo(RiskEventInsightModel model, VocRiskWarningRules warningRules) {
        RiskRuleVo vo =vocRiskWarningRulesService.getRiskEmotionRule(warningRules.getId());
        if(ObjectUtils.isNotEmpty(vo)){
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumD()) && !vo.getRiskWordsNumD().equals(BigDecimal.ZERO)){
                model.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumW()) && !vo.getRiskWordsNumW().equals(BigDecimal.ZERO)){
                model.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumM()) && !vo.getRiskWordsNumM().equals(BigDecimal.ZERO)){
                model.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumQ()) && !vo.getRiskWordsNumQ().equals(BigDecimal.ZERO)){
                model.setRiskRuleType("1");
            }
            if(ObjectUtils.isNotEmpty(vo.getRiskWordsNumY()) && !vo.getRiskWordsNumY().equals(BigDecimal.ZERO)){
                model.setRiskRuleType("1");
            }
        }
        return vo;
    }


    @Override
    public List<IntentionTrendVo> qualityUserStatictis(FilterCriteriaModel model, DwdVocQualityRisk quality) {
        return baseMapper.qualityUserStatictis(model,quality);
    }

    @Override
    public Result<?> carSeriesDistribution(RiskEventInsightModel model) {
        List<CarEmotionVo> carDistributions = qualityUserDiMapper.carSeriesDistribution(model);
        return Result.OK(carDistributions);
    }

    @Override
    public Result<?> dataAnalysisBriefingTrend(RiskEventInsightModel model) {

        model.SetUpCycle();
        List<SvwDate> dates = model.getDateTimes();
        model.setDateList(dates.subList(dates.size() >= 8 ? dates.size() - 8 : 0, dates.size()));
        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        List<BriefingTrendVo> objects = baseMapper.dataAnalysisBriefingTrend(model);
        List<BriefingTrendVo> vos = new ArrayList<>();
        final boolean[] flag = {true};
        model.getDateList().forEach(e -> {
                    BriefingTrendVo vo = objects.stream().filter(d -> e.getDateCycle().equals(d.getDateStr())).collect(Collectors.toList()).stream().findFirst().orElse(null);
                    if (vo == null && flag[0]) {
                        return;
                    } else {
                        flag[0] = false;
                        vo.setDateStr(e.getTime());
                    }
                    vos.add(vo);
                }
        );
        return Result.OK(vos);
    }
    @Autowired
    IVocRiskWarningRulesService vocRiskWarningRulesService;

    @Override
    public List<DwdVocQualityRiskF> riskQualiytFiltering(VocRiskWarningRules rule, RiskRuleVo vo) {
        return baseMapper.riskQualiytFiltering(rule, vo);
    }

    @Override
    public Result<?> dataAnalysisWaringNum(RiskEventInsightModel model, DwdVocQualityRisk risk) {

        List<String> list = baseMapper.dataAnalysisWaringNum(risk);
        return Result.OK(list);
    }

    @Override
    public Result<?> dataAnalysisChannelTrend(RiskEventInsightModel model1, DwdVocQualityRisk risk) {
        //修改环比计算+时间范围补齐
        Map<String, Object> charns = new HashMap<>();
        FilterCriteriaModel model = BeanUtil.copyProperties(model1, FilterCriteriaModel.class);
        Set<String> topicts = new HashSet<>();
        topicts.add(model1.getTopicCode());
        model.setTopicCodes(topicts);

        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));

        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        model.setEmotion("负面");

        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        model.setDataSources(null);
        model.setChannelId(null);
        List<ChannelVo> objectLists = qualityUserDiMapper.riskChannelDistribution(model);
        if (CollectionUtils.isEmpty(objectLists)) {
            return Result.OK(charns);
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

       /* //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> ChannelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );*/


        //存放所有ID用于补全展示数据
        final Set<String> channelIdTempSet = objectLists.stream().filter(e -> ObjectUtil.isNotNull(e)).filter(e -> StrUtil.isNotBlank(e.getChannelId()))
                .map(ChannelVo::getChannelId).collect(Collectors.toSet());


        final Map<String, List<ChannelVo>> rsMap = objectLists.stream().collect(Collectors.groupingBy(ChannelVo::getDateStr));
        List<TrendChannelVo> objects = rsMap.keySet().stream().map( dateStr -> {
            TrendChannelVo vo = new TrendChannelVo();
            vo.setDateStr(dateStr);
            vo.setChannelVos(rsMap.get(dateStr));
            return vo;
        }).collect(Collectors.toList());


        final String channelProportionDate = objects.stream()
                .max(Comparator.comparing(TrendChannelVo::getDateStr)).orElse(objects.stream().findFirst().get()).getDateStr();
        final List<ChannelVo> channelVos = objectLists.stream()
                .filter(e -> ObjectUtil.isNotNull(e))
                .filter(e -> ObjectUtil.isNotNull(e.getStatistic()))
                .filter(e -> channelProportionDate.equals(e.getDateStr()))
                .collect(Collectors.toList());

        //补齐日期
        objects.addAll(dateTempSet.stream()
                .map(e -> TrendChannelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //补齐业务对象
        objects.stream().forEach(e -> {
            Set<String> containsIds = e.getChannelVos().stream().map(ChannelVo::getChannelId).collect(Collectors.toSet());
            e.getChannelVos().addAll(
                    channelIdTempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id -> ChannelVo.builder()
                                    .dateStr(e.getDateStr())
                                    .channelId(id).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
            );
        });

        //时间排序
        objects.sort(Comparator.comparing(TrendChannelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));
        //计算
        for(int i = 1; i < objects.size()  ; i++){  //第二条开始处理
            TrendChannelVo obj = objects.get(i);  //当前
            TrendChannelVo preObj = objects.get(i-1);   //前一个
            final BigDecimal total = obj.getChannelVos().stream().map(ChannelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, ChannelVo> map = obj.getChannelVos().stream().collect(Collectors.toMap(ChannelVo::getChannelId, e -> e, (k1, k2) -> k1));
            Map<String, ChannelVo> preMap = preObj.getChannelVos().stream().collect(Collectors.toMap(ChannelVo::getChannelId, e -> e, (k1, k2) -> k1));

            map.keySet().stream().forEach(id ->{
                ChannelVo cur_ = map.get(id);
                ChannelVo pre_ = preMap.get(id);
                final Long days = dateR.get(obj.getDateStr());
                final Long preDays = dateR.get(preObj.getDateStr());
                //设置日均 averagePerDay()
                if(model.getDateUnit().intValue() == -1) {
                    cur_.setStatisticAR(BigDecimal.ZERO);
                }else{
                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_)? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(preDays));
                    cur_.setStatisticA(objAvgDays);
                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
                }

                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), total));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_)? null : pre_.getStatistic()));
//                cur_.setStatisticAR(CalculatorUtils.ringRatio(cur_.getStatisticA(),ObjectUtil.isNull(pre_)? null :  pre_.getStatisticA()));
            });
        }

        channelVos.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        //时间排序
        objects.sort(Comparator.comparing(TrendChannelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));
        objects.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objects.size() > index || model.getDateUnit() == -1) {
            int s = objects.size() >= index ? objects.size() : index;
            if(s>index){
                objects = new ArrayList<>(objects.subList( s-index  , objects.size()));
            }else if(s<index){
                objects = new ArrayList<>(objects.subList(  index -s  , objects.size()));
            }else {
                objects = new ArrayList<>(objects.subList(  1  , objects.size()));
            }
        }
        List<ChannelVo> collect = channelVos.stream().sorted(Comparator.comparing(ChannelVo::getStatistic).reversed()).collect(Collectors.toList());
        charns.put("channelProportion", collect);
        charns.put("channelTrend", objects);
        return Result.OK(charns);
    }

    @Autowired
    IBrandProductManagerService brandProductManagerService;

    @Override
    public void riskQualityExport() {
        QueryWrapper<BrandProductManager> queryWrapper=new QueryWrapper();
        queryWrapper.lambda()
                .eq(BrandProductManager::getPId,0)
        ;
        List<BrandProductManager> bras= brandProductManagerService.list(queryWrapper);
        for (BrandProductManager bra : bras) {

            RiskEventInsightModel model=new RiskEventInsightModel();
            model.setBrandCode(bra.getCode());
            RiskRuleVo vo=   vocRiskWarningRulesService.getRiskEmotionRule(CommonConstant.sys_risk_quality_rule);
            List<RiskExportResultVo> list=baseMapper.riskExport(model,vo);
            ExecutorService executor = Executors.newFixedThreadPool(500);
       /* final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
*/
            list.stream().forEach(e->{

                        executor.submit(new Runnable() {
                            @Override
                            public void run() {
                                e.setTimeStr(DateUtils.dateToStr(e.getPublishDate()));
                                e.setStartDate(e.getTimeStr() + " 00:00:00");
                                e.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                                e.setStatisticTypeStr(CalculatorUtils.getPeriod(e.getStatisticType()));
                                e.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));
                                RiskEventInsightModel model1 =new RiskEventInsightModel();
//                            model1.setAccessToken(token);
                                model1.setStartDate(e.getStartDate());
                                model1.setCreateDate(DateUtil.formatDateTime(e.getCreateTime()));
                                model1.setBrandCode(e.getBrandCode());
                                model1.setEmotion("负面");
                                model1.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                                model1.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));
                                model1.setTopicCode(e.getTopicCode());
                                if (e.getCreateTime()!=null){
                                    model1.setCreateDate(IDateUtils.format(e.getCreateTime()));
                                }
//                            e.setCarSeries(qualityUserDiService.riskCarSeriesExport(model1).toString());
                                Long un=qualityUserDiMapper.riskWarningUserNumExport(model1);
                                e.setUserNum(un);




                                String[] riskne= e.getLabelAllCode().split("#");
                                String nam="";
                                for (String s : riskne) {
                                    nam+=faultProblemService.getNameByCode(s)+"#";

                                }
                                e.setTopicCodeName(nam);

                            }
                        });




                    }
            );
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            String path = "/opt/apps/demo_voc/【"+bra.getName()+"】质量风险.xlsx";
            // 导出文件位置
            // 告诉其输出位置
            ExcelWriter excelWriter = EasyExcel.write(path).build();
            // 创建 sheet 构造器时通过head属性告诉其导出类型的模板
            WriteSheet build = EasyExcel.writerSheet("风险结果").head(RiskExportResultVo.class).build();
            excelWriter.write(list, build);
            excelWriter.finish();

        }

    }

    @Override
    public List<DwdVocQualityRiskF> riskQualiytFilteringNew(VocRiskWarningRules riskWarningRules, RiskRuleVo vo) {
        return baseMapper.riskQualiytFilteringNew(riskWarningRules,vo);
    }


}
