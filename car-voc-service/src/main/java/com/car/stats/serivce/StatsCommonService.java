package com.car.stats.serivce;

import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.vo.ChannelVo;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName StatsCommonService.java
 * @Description TODO
 * @createTime 2022年10月25日 15:44
 * @Copyright voc
 */
public interface StatsCommonService {

    FilterCriteriaModel homeUniformConditions(Integer dayNum);
    FilterCriteriaModel getNoticeWarningDate(FilterCriteriaModel model);

    List<ChannelVo> setChannelAllList(FilterCriteriaModel model);

    List<ChannelVo> setChannelAllList(ProductQualityFilterCriteriaModel model);
}
