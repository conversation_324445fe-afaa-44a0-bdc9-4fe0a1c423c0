package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocQualityEmotionDi;
import com.car.stats.mapper.DwsVocQualityEmotionDiMapper;
import com.car.stats.mapper.DwsVocQualityUserDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.FocucLabelDetailModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.serivce.IDwsVocQualityEmotionDiService;
import com.car.stats.serivce.StatsCommonService;
import com.car.stats.vo.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.BigDecimalUtils;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.SvwDate;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.service.ISysDictItemService;
import com.car.voc.service.IVocChannelCategoryService;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName DwsVocQualityEmotionDiServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月18日 16:53
 * @Copyright voc
 */
@Service
public class DwsVocQualityEmotionDiServiceImpl extends ServiceImpl<DwsVocQualityEmotionDiMapper, DwsVocQualityEmotionDi> implements IDwsVocQualityEmotionDiService {

    @Autowired
    ISysDictItemService iSysDictItemService;
    @Resource
    DwsVocQualityEmotionDiMapper qualityEmotionDiMapper;
    @Autowired
    StatsCommonService statsCommonService;
    @Resource
    DwsVocQualityUserDiMapper qualityUserDiMapper;


    @Override
    public Result<?> qualityProblemTopHome(FilterCriteriaModel model) {
        return qualityProblemTopCom(model);
    }

    @Override
    public Result<?> qualityProblemTop(FilterCriteriaModel model) {
        return qualityProblemTopCom(model);
    }

    Result<?> qualityProblemTopCom(FilterCriteriaModel model) {
        model.SetUpCycle();
        model.setRownum(1000);
        model.setSecondDimensionCodes(null);
        List<QualityLabelVo> tops = qualityEmotionDiMapper.qualityProblemTop(model);
        Map<String, Object> re = new HashMap<>();


        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(tops.stream().map(QualityLabelVo::getLabelCode).collect(Collectors.toSet()));
        List<QualityLabelVo> topsup = qualityEmotionDiMapper.qualityProblemTop(model);

        List<QualityLabelVo> soartop = BeanUtil.copyToList(tops, QualityLabelVo.class);
        List<QualityLabelVo> topr = BeanUtil.copyToList(tops, QualityLabelVo.class);
        List<QualityLabelVo> topupr = BeanUtil.copyToList(topsup, QualityLabelVo.class);

        topr.forEach(e -> {
            List<QualityLabelVo> onet = topupr.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(onet)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
            }
        });
        int index = model.isExcel() ? model.getDownloadTagNum() : 10;
        re.put("mentionTops", new ArrayList<>(topr.subList(0, topr.size() >= index ? index : topr.size())));


        BigDecimal btotal = soartop.stream().map(QualityLabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<QualityLabelVo> avgbefreList;
        if (tops != null && tops.size() > 0) {
            avge = NumberUtil.div(btotal, tops.size());
            avgbefreList = soartop.stream().filter(e -> e.getStatistic().compareTo(avge) > 0).collect(Collectors.toList());
        } else {
            avgbefreList = soartop;
        }

        List<QualityLabelVo> jihe = avgbefreList;
        jihe.forEach(e -> {
            List<QualityLabelVo> onet = topupr.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(onet)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
            }
        });
        jihe = jihe.stream().filter(e -> (e.getStatisticR() != null && e.getStatisticR().floatValue() > 0)).collect(Collectors.toList());
        jihe.sort(Comparator.comparing(QualityLabelVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
        re.put("soarTops", new ArrayList<>(jihe.subList(0, jihe.size() >= index ? index : jihe.size())));

        return Result.OK(re);
    }


    @Override
    public Result<?> overviewProblemDistribution(FilterCriteriaModel model) {
        model.setRownum(1000);
        model.setSecondDimensionCodes(null);
        List<QualityLabelTreeVo> sounts = qualityEmotionDiMapper.secondDistribution(model);
        FocucLabelDetailModel modelt = BeanUtil.copyProperties(model, FocucLabelDetailModel.class);
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(sounts, QualityLabelTreeVo::getStatistic, QualityLabelTreeVo::setStatisticP);

        sounts.forEach(e -> {
            modelt.setSecondDimensionCode(e.getLabelCode());
            List<QualityLabelTreeVo> threets = qualityEmotionDiMapper.threeDistribution(modelt);
            CalculatorUtils.proportion(threets, QualityLabelTreeVo::getStatistic, QualityLabelTreeVo::setStatisticP);
            e.setChildren(threets);

        });
        return Result.OK(sounts);
    }

    @Autowired
    SysUserRoleMapper sysUserRoleMapper;

    /**
     * 根据token查询 用户是否有 质量标签
     */
    boolean getQuality(String brandCode) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(qualityText) && wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Result<?> briefingValue(ProductQualityFilterCriteriaModel model) {
        model.SetUpCycle();
        if (!getQuality(model.getBrandCode())) {
            VocOverBriefingValueVo vu = new VocOverBriefingValueVo();
            vu.setUsersNum(new BigDecimal(String.valueOf(BigDecimal.ZERO)));
            vu.setChannelNum(new BigDecimal(String.valueOf(BigDecimal.ZERO)));
            return Result.OK(vu);
        }

        VocOverBriefingValueVo re = qualityEmotionDiMapper.briefingValue(model);

        if (re == null) {
            re = new VocOverBriefingValueVo();
        }
        re.setTotalMentionsR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
        Map<String, Object> user = qualityUserDiMapper.queryUserNumQuality(model);

        if (user != null) {
            re.setUsersNum(new BigDecimal((Long) user.get("thisUserNum")));
        }
        re.setUsersNumR(CalculatorUtils.ringRatio(re.getUsersNum(), ObjectUtil.isNull(user) ? null : new BigDecimal((Long) user.get("thisUserNumUp"))));

        List<ChannelStatisticVo> chans = qualityEmotionDiMapper.sourceChannel(model);
        /*if(model.isCPoint()||((StrUtil.isNotBlank(model.getChannelId()) && !(model.getChannelId().equals("1372001238165593852")||model.getChannelId().equals("1356178730703224804")||model.getChannelId().equals("1356178730703224803"))))){
            chans = chans.stream().filter(channelStatisticVo -> !(channelStatisticVo.getDataSource().equals("1356178730703224804")||channelStatisticVo.getDataSource().equals("1356178730703224803"))&&model.getChannelIds().contains(channelStatisticVo.getDataSource())).collect(Collectors.toList());
        }*/
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<ChannelStatisticVo> chansup = qualityEmotionDiMapper.sourceChannel(model);
        /*if(model.isCPoint()||((StrUtil.isNotBlank(model.getChannelId()) && !(model.getChannelId().equals("1372001238165593852")||model.getChannelId().equals("1356178730703224804")||model.getChannelId().equals("1356178730703224803"))))){
            chansup = chansup.stream().filter(channelStatisticVo -> !(channelStatisticVo.getDataSource().equals("1356178730703224804")||channelStatisticVo.getDataSource().equals("1356178730703224803"))&&model.getChannelIds().contains(channelStatisticVo.getDataSource())).collect(Collectors.toList());
        }*/
        re.setChannelNum(BigDecimal.valueOf(chans.size()));
        re.setChannelNumR(new BigDecimal(chans.size() - chansup.size()));
        List<String> chStr = new ArrayList<>();
        for (int i = 0; i < chans.size(); i++) {
            chStr.add(chans.get(i).getDataSource());
        }
        re.setChannelTopStr(chStr);
        return Result.OK(re);
    }

    @Override
    public Result<?> soarProblem(ProductQualityFilterCriteriaModel model) {
        Map<String, Object> re = new HashMap<>();
        if (!getQuality(model.getBrandCode())) {
            re.put("soarTops", new ArrayList<>());
            return Result.OK(re);
        }


        model.SetUpCycle();
        model.setRownum(1000);
        List<SoarProblemVo> tops = qualityEmotionDiMapper.soarProblem(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(tops.stream().map(SoarProblemVo::getLabel4Code).collect(Collectors.toSet()));
        List<SoarProblemVo> topsup = qualityEmotionDiMapper.soarProblem(model);

        BigDecimal btotal = tops.stream().map(SoarProblemVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<SoarProblemVo> avgbefre;
        if (tops != null && tops.size() > 0) {
            avge = NumberUtil.div(btotal, tops.size());
            avgbefre = tops.stream().filter(e -> (e.getStatistic().compareTo(avge)) > 0).collect(Collectors.toList());
        } else {
            avgbefre = tops;
        }

        List<SoarProblemVo> jihe = avgbefre;


        jihe.forEach(e -> {
            List<SoarProblemVo> onet = topsup.stream().filter(d -> e.getLabel4Code().equals(d.getLabel4Code())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(onet)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
            }
        });
        jihe = jihe.stream().filter(e -> (e.getStatisticR() != null && e.getStatisticR().floatValue() > 0)).collect(Collectors.toList());
        jihe.sort(Comparator.comparing(SoarProblemVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
        re.put("soarTops", new ArrayList<>(jihe.subList(0, jihe.size() > 20 ? 20 : jihe.size())));
        return Result.OK(re);
    }

    @Override
    public Result<?> problemDistribution(ProductQualityFilterCriteriaModel model) {

        List<LabelVo> list = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(list);
        }
        list = setProDistributionFb(model);
        return Result.OK(list);
    }

    private List<LabelVo> setProDistributionFb(ProductQualityFilterCriteriaModel model) {
        List<LabelVo> list = new ArrayList<>();
        model.setRownum(1000);

        boolean quality = getQuality(model.getBrandCode());
        if (!quality) {
            return list;
        }
        BigDecimal total = null;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            total = qualityUserDiMapper.userNumQuality(model);
        }


        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            list = qualityEmotionDiMapper.groupBy4(model);
        } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            list = qualityEmotionDiMapper.groupBy3(model);
        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            List<QualityLabelTreeVo> listo = qualityEmotionDiMapper.groupBy2(model);
            list = BeanUtil.copyToList(listo, LabelVo.class);
        } else {
            List<QualityLabelTreeVo> listo = qualityEmotionDiMapper.groupBy1(model);
            list = BeanUtil.copyToList(listo, LabelVo.class);
        }
        if (model.getDataType().equals(DataEnum.numMention)) {
            total = list.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }

        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(list, LabelVo::getStatistic, LabelVo::setStatisticP);
        return list;
    }

    private List<LabelVo> setProDistribution(ProductQualityFilterCriteriaModel model) {
        List<LabelVo> objectLists;
        model.setRownum(1000);
        /*List<SvwDate> dates = model.getDateTimes();
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));*/
        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            objectLists = qualityEmotionDiMapper.groupBy4(model);

        } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            objectLists = qualityEmotionDiMapper.groupBy3(model);


        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            List<QualityLabelTreeVo> listo = qualityEmotionDiMapper.groupBy2(model);
            objectLists = BeanUtil.copyToList(listo, LabelVo.class);
        } else {
            List<QualityLabelTreeVo> listo = qualityEmotionDiMapper.groupBy1(model);
            objectLists = BeanUtil.copyToList(listo, LabelVo.class);


        }
        if (CollUtil.isEmpty(objectLists)) {
            return Collections.emptyList();
        }

        final BigDecimal total = objectLists.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);


        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            LabelVo obj = objectLists.get(i);  //当前
            LabelVo preObj = objectLists.get(i - 1);   //前一个
//            final Long days = dateR.get(obj.get());
//            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            /*if (model.getDateUnit().intValue() == -1) {
                obj.setStatisticAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getStatistic(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getStatistic(), new BigDecimal(preDays));
                obj.setStatisticA(objAvgDays);
                obj.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }*/

            obj.setStatisticR(CalculatorUtils.ringRatio(obj.getStatistic(), preObj.getStatistic()));
            obj.setStatisticP(CalculatorUtils.proportion(preObj.getStatistic(), total));
        }

        return objectLists;

    }

    private Result<?> setRepairProDistribution(ProductQualityFilterCriteriaModel model) {
        List<QualityLabelTreeVo> sounts;
        if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            sounts = qualityEmotionDiMapper.repairDistributionProblem3(model);
        } else {
            sounts = qualityEmotionDiMapper.repairDistributionProblem2(model);

        }
        BigDecimal total = sounts.stream().map(QualityLabelTreeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        FocucLabelDetailModel modelt = BeanUtil.copyProperties(model, FocucLabelDetailModel.class);
        sounts.forEach(e -> {
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
            modelt.setSecondDimensionCode(e.getLabelCode());
            List<QualityLabelTreeVo> threets;
            if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
                threets = qualityEmotionDiMapper.repairDistributionProblem3(model);
            } else {
//                threets=qualityEmotionDiMapper.threeDistribution(modelt);
                threets = qualityEmotionDiMapper.repairThreeDistribution(modelt);

            }
            BigDecimal thrtotal = threets.stream().map(QualityLabelTreeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            threets.forEach(a -> {
                a.setStatisticP(CalculatorUtils.proportion(a.getStatistic(), thrtotal));
            });
            e.setChildren(threets);

        });


        if (sounts.size() >= 20) {
            return Result.OK(new ArrayList<>(sounts.subList(0, 20)));

        } else {
            return Result.OK(sounts);

        }
    }


    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public Result<?> problemTrends(ProductQualityFilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        List<TrendLabelVo> objectLists = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(objectLists);
        }


        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        boolean quality = getQuality(model.getBrandCode());
        Set<String> tempSet = new HashSet<>();  //存放所有ID用于补全展示数据

        for (SvwDate date : new ArrayList<>(dates)) {
            TrendLabelVo one = new TrendLabelVo();
            one.setDateStr(date.getTime());

            if (quality) {
                model.setStartDate(date.getStartDate());
                model.setEndDate(date.getEndDate());

                List<LabelVo> objects = setProDistribution(model);
                if (CollUtil.isNotEmpty(objects)) {
                    tempSet.addAll(objects.stream().filter(e -> ObjectUtil.isNotNull(e)).filter(e -> StrUtil.isNotBlank(e.getLabelCode()))
                            .map(LabelVo::getLabelCode).collect(Collectors.toSet()));
                }
                one.setList(Optional.ofNullable(objects).orElse(new ArrayList<>()));
            }

            objectLists.add(one);
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> TrendLabelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //拼装补全完整数据
        objectLists.stream()
                .forEach(e -> {
                    Set<String> containsIds = Optional.ofNullable(e.getList().stream().map(LabelVo::getLabelCode).collect(Collectors.toSet()))
                            .orElse(Collections.EMPTY_SET);
                    if (CollUtil.isEmpty(e.getList())) {
                        e.setList(new ArrayList<>());
                    }
                    e.getList().addAll(
                            tempSet.stream()
                                    .filter(id -> !containsIds.contains(id))   //排除已有的数据
                                    .map(id -> LabelVo.builder().labelCode(id).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
                    );
                });
        //时间排序
        objectLists.sort(Comparator.comparing(TrendLabelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            TrendLabelVo obj = objectLists.get(i);  //当前
            TrendLabelVo preObj = objectLists.get(i - 1);   //前一个
            final BigDecimal total = obj.getList().stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, LabelVo> map = obj.getList().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            Map<String, LabelVo> preMap = preObj.getList().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));

            map.keySet().stream().forEach(id -> {
                LabelVo cur_ = map.get(id);
                LabelVo pre_ = preMap.get(id);
                final Long days = dateR.get(obj.getDateStr());
                final Long preDays = dateR.get(preObj.getDateStr());
                //设置日均 averagePerDay()
                if (model.getDateUnit().intValue() == -1) {
                    cur_.setStatisticAR(BigDecimal.ZERO);
                } else {
                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_) ? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(preDays));
                    cur_.setStatisticA(objAvgDays);
                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
                }

                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), total));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
//                cur_.setStatisticAR(CalculatorUtils.ringRatio(cur_.getStatisticA(), ObjectUtil.isNull(pre_) ? null : pre_.getStatisticA()));
            });
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s >= objectLists.size() ? 1 : s - index, objectLists.size()));
        }
        return Result.OK(objectLists);

        /*model.setRownum(1000);
        List<SvwDate> dates = model.getDateTimes();
        int a = 0;
        boolean quality = getQuality();
        for (SvwDate date : model.getDateUnit() == -1 ? dates : dates.subList(dates.size() >= 6 ? dates.size() - 6 : 0, dates.size())) {
            TrendLabelVo one = new TrendLabelVo();
            one.setDateStr(date.getTime());

            if (quality) {
                model.setStartDate(date.getStartDate());
                model.setEndDate(date.getEndDate());
                List<LabelVo> list = setProDistribution(model);
                if (CollectionUtils.isEmpty(list)) continue;
                int finalA = a;
                list.forEach(e -> {
                    if (trendLabelVos.size() > 0) {
                        LabelVo onet = trendLabelVos.get(finalA - 1).getList().stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
                        if (onet != null) {
                            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.getStatistic()));
                            e.setStatisticAR(CalculatorUtils.ringRatio(e.getStatisticA(), onet.getStatisticA()));
                        }
                    }

                });
                one.setList(list);
            }


            trendLabelVos.add(one);
            a++;
        }
        return Result.OK(trendLabelVos);*/
    }

    @Override
    public Result<?> typicalProblems(ProductQualityFilterCriteriaModel model) {
        model.setRownum(model.isExcel() ? model.getDownloadTagNum() : null);
        List<QualityLabelVo> tops = qualityEmotionDiMapper.typicalProblems(model);
        model.SetUpCycle();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(tops.stream().map(QualityLabelVo::getLabelCode).collect(Collectors.toSet()));


        List<QualityLabelVo> topsUp = qualityEmotionDiMapper.typicalProblems(model);

        tops.forEach(e -> {
            e.setStatisticR(BigDecimal.ZERO);
            e.setStatisticP(BigDecimal.ZERO);
            QualityLabelVo onet = topsUp.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList()).stream().findFirst().orElse(null);
            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), Optional.ofNullable(onet).isPresent() ? onet.getStatistic() : null));
        });


        tops.sort(Comparator.comparing(QualityLabelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(QualityLabelVo::getStatisticR)
                .thenComparing(QualityLabelVo::getLabelCode)
                .reversed());
        return Result.OK(tops);
    }

    @Autowired
    IVocChannelCategoryService channelCategoryService;

    @Override
    public Result<?> channelDistributionProduct(ProductQualityFilterCriteriaModel model) {

        List<ChannelVo> objects = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(objects);
        }


        objects = statsCommonService.setChannelAllList(model);

        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(objects, ChannelVo::getStatistic, ChannelVo::setStatisticP);

        objects.forEach(e -> {
            e.setChannelStr(channelCategoryService.getById(e.getChannelId()).getName());
        });
        List<ChannelVo> collect = objects.stream().sorted(Comparator.comparing(ChannelVo::getStatistic).reversed()).collect(Collectors.toList());
        return Result.OK(collect);
    }


    private List<ChannelVo> setChannelAllList(ProductQualityFilterCriteriaModel model) {

        List<ChannelVo> objects = new ArrayList<>();
        if (model.isCPoint()) {

            model.setChannelId(CommonConstant.privateChannelId);
            model.setDataSources(null);
            model.setChannelIds(null);
            objects.addAll(qualityEmotionDiMapper.channelDistributionProduct(model));
            model.setChannelId(null);
        } else if (model.getChannelId() != null && !"".equals(model.getChannelId())) {
            model.setDataSources(null);
            objects.addAll(qualityEmotionDiMapper.channelDistributionProduct(model));
        } else {
            model.setDataSources(null);
            model.setChannelId(null);
            objects.addAll(qualityEmotionDiMapper.channelDistributionProduct(model));
        }
        return objects;

    }


    @Autowired
    private IFaultProblemService faultProblemService;


    @Override
    public Result<?> commonProblems(ProductQualityFilterCriteriaModel model) {
        Map<String,Object> reu = new HashMap<>();

        if (!getQuality(model.getBrandCode())) {
            reu.put("average", null);
            reu.put("list", new ArrayList<>());
            return Result.OK(reu);
        }

        model.setRownum(model.isExcel() ? model.getDownloadTagNum() : null);
        List<CommonProblemsVo> re = qualityEmotionDiMapper.commonProblems(model);
        VocOverBriefingValueVo res = qualityEmotionDiMapper.briefingTotalValue(model);
        if (res == null) {
            return Result.OK(reu);
        }
        BigDecimal total = res.getTotalMentions();
        re.forEach(e -> {
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
            if (StrUtil.isNotBlank(e.getLabelStr())) {
                e.setNumParts(qualityEmotionDiMapper.commonProblemsNumParts(model, e.getLabelStr()));
            }
        });

        if (re != null && re.size() > 0) {
            reu.put("average", NumberUtil.div(total, re.size()));
        }

        re.sort(Comparator.comparing(CommonProblemsVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(CommonProblemsVo::getStatisticP)
                .thenComparing(CommonProblemsVo::getLabelCode)
                .reversed());

        int index = model.isExcel() ? model.getDownloadTagNum() : 6;
        if (re.size() >= index) {
            reu.put("list", new ArrayList<>(re.subList(0, index)));
        } else {
            reu.put("list", re);
        }
        return Result.OK(reu);
    }

    @Override
    public Result<?> distributionProblemParts(ProductQualityFilterCriteriaModel model) {

        List<QualityLabelTreeVo> sounts = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(sounts);
        }
        model.setRownum(1000);
        BigDecimal total = null;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            total = qualityUserDiMapper.userNumQuality(model);
        }


        if (StrUtil.isNotBlank(model.getProblemDistributionType()) && "maintenance".equals(model.getProblemDistributionType())) {
            return setRepairProDistribution(model);
        } else {

            if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
                sounts = qualityEmotionDiMapper.distributionProblem3(model);
            } else if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
                sounts = qualityEmotionDiMapper.distributionProblemParts2(model);
            } else {
                sounts = qualityEmotionDiMapper.distributionProblemParts(model);
            }
            if (model.getDataType().equals(DataEnum.numMention)) {
                total = sounts.stream().map(QualityLabelTreeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);

            }
            FocucLabelDetailModel modelt = BeanUtil.copyProperties(model, FocucLabelDetailModel.class);
            BigDecimal finalTotal = total;
            sounts.forEach(e -> {
                e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalTotal));
                List<QualityLabelTreeVo> threets;
                if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
                    threets = qualityEmotionDiMapper.distributionProblem4(model);
                } else if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
                    modelt.setSecondDimensionCode(e.getLabelCode());
                    FocucLabelDetailModel mos = BeanUtil.copyProperties(modelt, FocucLabelDetailModel.class);
                    threets = qualityEmotionDiMapper.threeDistribution(mos);
                } else {
                    modelt.setFirstDimensionCode(e.getLabelCode());
                    threets = qualityEmotionDiMapper.secondDistribution(modelt);

                }
                BigDecimal thrtotal = e.getStatistic();
                if (model.getDataType().equals(DataEnum.numMention)) {
                    thrtotal = threets.stream().map(QualityLabelTreeVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);

                }
                BigDecimal finalThrtotal = thrtotal;
                threets.forEach(a -> a.setStatisticP(CalculatorUtils.proportion(a.getStatistic(), finalThrtotal)));
                e.setChildren(threets);

            });
        }
        if (sounts.size() >= 20) {
            return Result.OK(new ArrayList<>(sounts.subList(0, 20)));

        } else {
            return Result.OK(sounts);

        }

    }

    @Override
    public Result<?> severityRatio(ProductQualityFilterCriteriaModel model) {

        List<SeverityRatioVo> list = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(list);
        }
        BigDecimal total = null;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            total = qualityUserDiMapper.userNumQuality(model);
        } else {
            VocOverBriefingValueVo re = qualityEmotionDiMapper.briefingValue(model);
            if (re == null) {
                re = new VocOverBriefingValueVo();
            }
            total = re.getTotalMentions();
        }

        list = qualityEmotionDiMapper.severityRatio(model);

        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(list, SeverityRatioVo::getStatistic, SeverityRatioVo::setStatisticP);
        return Result.OK(list);
    }

    @Override
    public Result<?> componentTrendParts(ProductQualityFilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        List<ComponentTrendPartVo> objectLists = new ArrayList<>();
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(objectLists);
        }
        final String startDate = model.getStartDate();
        final String endtDate = model.getEndDate();

        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            if (model.getDateUnit() == 3) {
                model.setYear(date.getYear());
            } else if (model.getDateUnit() == 2) {
                model.setYear(date.getYear());
                model.setSeason(date.getSeason());
            } else if (model.getDateUnit() == 1) {
                model.setYear(date.getYear());
                if (date.getMonth().length() == 1 && !date.getMonth().startsWith("0")) {
                    model.setMonth("0" + date.getMonth());
                } else {
                    model.setMonth(date.getMonth());
                }
            } else if (model.getDateUnit() == 0) {
                model.setYear(date.getYear());
                model.setWeek(date.getWeek());
            }
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            ComponentTrendPartVo one = qualityEmotionDiMapper.componentTrendParts(model);

            if (ObjectUtil.isNull(one)) {
                one = ComponentTrendPartVo.builder().build();
            }

            if (model.getDateUnit() >= 0) {
                model.setStartDate(null);
                model.setEndDate(null);
            }
            if (3 == model.getDateUnit().intValue()) {
                one.setDateStr(date.getTime().replace("-", ""));
            } else {
                one.setDateStr(date.getTime());
            }

            objectLists.add(one);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> ComponentTrendPartVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(ComponentTrendPartVo::getDateStr, Comparator.nullsFirst(String::compareTo)));
        //计算
        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            ComponentTrendPartVo obj = objectLists.get(i);  //当前
            ComponentTrendPartVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setStatisticAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getStatistic(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getStatistic(), new BigDecimal(preDays));
                obj.setStatisticA(objAvgDays);
                obj.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setUserNumAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserNum(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getUserNum(), new BigDecimal(preDays));
                obj.setUserNumA(objAvgDays);
                obj.setUserNumAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }

            obj.setStatisticR(CalculatorUtils.ringRatio(obj.getStatistic(), preObj.getStatistic()));
            obj.setUserNumR(CalculatorUtils.ringRatio(obj.getUserNum(), preObj.getUserNum()));
//            obj.setStatisticAR(CalculatorUtils.ringRatio(obj.getStatisticA(), preObj.getStatisticA()));
//            obj.setUserNumAR(CalculatorUtils.ringRatio(obj.getUserNumA(), preObj.getUserNumA()));

            if (ObjectUtil.isNull(obj.getStatistic())) {
                obj.setStatistic(BigDecimal.ZERO);

            }
            if (ObjectUtil.isNull(obj.getStatisticA())) {
                obj.setStatisticA(BigDecimal.ZERO);
            }
        }
        objectLists.stream().forEach(e -> {
            if (model.getDateUnit().intValue() == 1 || model.getDateUnit().intValue() == 0) {  //补位  2023-9  ->  2023-09
                final String year = e.getDateStr().split("-")[0];
                final String month = e.getDateStr().split("-")[1];
                e.setDateStr(year.concat("-").concat(month.startsWith("0") ? month.replace("0", "") : month));
            }
        });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s >= objectLists.size() ? 1 : s - index, objectLists.size()));
        }
        return Result.OK(objectLists);
        /*int a=0;
        for (SvwDate date : model.getDateUnit() == -1?dates:dates.subList(st,dates.size())) {

            if(model.getDateUnit() == 3){
                model.setYear(date.getYear());
            }else if(model.getDateUnit() == 2){
                model.setYear(date.getYear());
                model.setSeason(date.getSeason());
            }else if(model.getDateUnit() == 1){
                model.setYear(date.getYear());
                if(date.getMonth().length()==1&&!date.getMonth().startsWith("0")){
                    model.setMonth("0"+date.getMonth());
                }else {
                    model.setMonth(date.getMonth());
                }
            }else if(model.getDateUnit() == 0){
                model.setYear(date.getYear());
                model.setWeek(date.getWeek());
            }
            model.setStartDate(date.getStartDate());model.setEndDate(date.getEndDate());
            ComponentTrendPartVo one=qualityEmotionDiMapper.componentTrendParts(model);
            String  std=model.getStartDate();String end=model.getEndDate();
            if(model.getDateUnit() >=0){
                model.setStartDate(null);model.setEndDate(null);
            }
//            ComponentTrendPartVo one1=qualityEmotionDiMapper.componentTrendRepair(model);
            if (one==null){
                one=new ComponentTrendPartVo();
                continue;
            }else {
                if (one.getStatistic()==null){
                    continue;
                }
            }
           *//* if(one1 != null){
                one.setRepairStatistic(one1.getStatistic());
            }*//*
            one.setDateStr(date.getTime());
            if (model.getDateUnit()!=-1){//设置日均 averagePerDay()
                Long day= DateUtils.getDays(end,std)+1;

                one.setAveragePerDay(model.getDateUnit(),model.getEndDate(),day);
            }
            if (a>0&&trendLabelVos.size()>0){
                one.setStatisticR(CalculatorUtils.ringRatio(one.getStatistic(),trendLabelVos.get(a-1).getStatistic()));
                one.setUserNumR(CalculatorUtils.ringRatio(one.getUserNum(),trendLabelVos.get(a-1).getUserNum()));
                one.setStatisticAR(CalculatorUtils.ringRatio(one.getStatisticA(),trendLabelVos.get(a-1).getStatisticA()));
                one.setUserNumAR(CalculatorUtils.ringRatio(one.getUserNumA(),trendLabelVos.get(a-1).getUserNumA()));
            }
            a++;
            trendLabelVos.add(one);
        }*/


    }

    @Override
    public Result<?> partsSoarComponents(ProductQualityFilterCriteriaModel model) {
        ProductQualityFilterCriteriaModel repairMode = BeanUtil.copyProperties(model, ProductQualityFilterCriteriaModel.class);
        model.SetUpCycle();
        model.setRownum(1000);
        List<LabelVo> thres = qualityEmotionDiMapper.groupBy3(model);
//            thres.stream().forEach(e-> log.debug("本："+e.getLabelStr()+" "+e.getStatistic()));
        model.setThreeDimensionCodes(thres.stream().map(LabelVo::getLabelCode).collect(Collectors.toSet()));
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<LabelVo> thresup = qualityEmotionDiMapper.groupBy3(model);
//            thresup.stream().forEach(e-> log.debug("上："+e.getLabelStr()+" "+e.getStatistic()));
        BigDecimal btotal = thres.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<LabelVo> avgbefre;
        if (thres != null && thres.size() > 0) {
            avge = NumberUtil.div(btotal, thres.size());
            avgbefre = thres.stream().filter(e -> (e.getStatistic().compareTo(avge)) > 0).collect(Collectors.toList());
            if (avgbefre != null && avgbefre.size() <= 20) {
                thres.sort(Comparator.comparing(LabelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
                avgbefre = thres.subList(0, thres.size() / 2);
            }
        } else {
            avgbefre = thres;
        }


//        List<LabelVo> jihe = avgbefre;
        List<LabelVo> jihe = new ArrayList<>();
        //若大于平均值的标签的个数小于等于20，则取大于平均值的标签总个数的一半
        if (ObjectUtil.isNotEmpty(avgbefre) && avgbefre.size() <= 20) {
            jihe = thres.subList(0, thres.size() / 2);
        } else {
            jihe = avgbefre;
        }

        jihe.forEach(e -> {
            List<LabelVo> onet = thresup.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(onet)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
            }
        });
//        List<LabelVo> sdfl=jihe.stream().filter(e->(e.getStatisticR()==null)).collect(Collectors.toList());
        jihe = jihe.stream().filter(e -> (e.getStatisticR() != null && e.getStatisticR().floatValue() > 0)).collect(Collectors.toList());

        jihe.sort(Comparator.comparing(LabelVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(LabelVo::getStatistic)
                .thenComparing(LabelVo::getLabelCode)
                .reversed());
        Map re = new HashMap();
        re.put("mention", new ArrayList<>(jihe.subList(0, jihe.size() > 20 ? 20 : jihe.size())));


        re.put("repair", repairSoarComponents(repairMode));
        return Result.OK(re);
    }

    public List<LabelVo> repairSoarComponents(ProductQualityFilterCriteriaModel model) {
        return new ArrayList<>();
    }

    @Override
    public Result<?> hotWords(FilterCriteriaModel model) {
        return Result.ok(qualityUserDiMapper.hotWords(model));
    }
}
