package com.car.stats.serivce.es;

import lombok.Data;

/**
 *
 * @version 1.0.0
 * @ClassName ResultTotal.java
 * @Description TODO
 * @createTime 2022年12月08日 11:25
 * @Copyright voc
 */
@Data
public class ResultTotal {
    /**
     * 记录条数,聚合后的条数
     */
    private Long recordTotal;
    /**
     * 所有满足条件的总数
     */
    private Long sumTotal;

    public ResultTotal(Long recordTotal, Long sumTotal) {
        this.recordTotal = recordTotal;
        this.sumTotal = sumTotal;
    }
}
