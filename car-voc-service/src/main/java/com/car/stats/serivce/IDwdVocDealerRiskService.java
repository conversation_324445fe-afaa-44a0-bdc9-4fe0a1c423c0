package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;

import java.math.BigDecimal;
import java.util.List;

public interface IDwdVocDealerRiskService extends IService<DwdVocDealerRisk> {
    IPage<RiskPointAggVo> riskPointAggNew(RiskEventInsightModel model);

    Result<?> dataAnalysisBriefing(RiskEventInsightModel model, DwdVocDealerRisk risk);

    Result<?> userIntentionTrend(RiskEventInsightModel model, DwdVocDealerRisk risk);

    Result<?> hotWords(RiskEventInsightModel model);

    Result<?> problemDistribution(RiskEventInsightModel model);

    List<DwdVocDealerRisk> riskBranchesFiltering(VocRiskWarningRules riskWarningRules, RiskRuleVo vo);
}
