package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwdVocSentence;
import com.car.stats.entity.DwsVocEmotionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.LabelSelectModel;
import com.car.stats.vo.EmotionProportionVo;
import com.car.voc.common.Result;

/**
 *
 * @version 1.0.0
 * @ClassName IDwsVocEmotionDiService.java
 * @Description TODO
 * @createTime 2022年10月17日 00:00
 * @Copyright voc
 */
public interface DwdVocSentenceService extends IService<DwdVocSentence> {

    Result<?> emotionalPurification(FilterCriteriaModel model);

    Result<?> mentionsUserChannel(FilterCriteriaModel model);

    Result<?> sourceChannel(FilterCriteriaModel model);

    Result<?> carSeriesTop(FilterCriteriaModel model);

    Result<?> homeLeaderIntentionProp(FilterCriteriaModel model);

    Result<EmotionProportionVo> homeLeaderEmotionProp(FilterCriteriaModel model);

    Result<?> allTagDist(FilterCriteriaModel model);
    Result<?> hotWords(FilterCriteriaModel model);
    Result<?> tagDistribution(FilterCriteriaModel model);

    Result<?> carMachineIntelligent(FilterCriteriaModel model);

    Result<?> allTagsTitle(LabelDetailFilterModel model);

    Result<?> provinceMap(FilterCriteriaModel model);

    Result<?> salesLeadstatisticUsers(FilterCriteriaModel model);

    Result<?> carPurchaseDist(FilterCriteriaModel model);

    Result<?> purchaseConcernsPriority(FilterCriteriaModel model);

    Result<?> homeLeaderRegionalTop(FilterCriteriaModel model);

    Result<?> homeLeaderCommunityTop(FilterCriteriaModel model);
}
