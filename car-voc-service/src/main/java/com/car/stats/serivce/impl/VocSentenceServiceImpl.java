package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwdVocSentence;
import com.car.stats.mapper.VocSentenceMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.serivce.DwdVocSentenceService;
import com.car.stats.vo.*;
import com.car.stats.vo.popvo.PopUpVo;
import com.car.stats.vo.popvo.UserLabelVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.enums.SoundTypeEnum;
import com.car.voc.common.util.BigDecimalUtils;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.dto.BusinessTagImportDto;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.model.BrandProductManagerModel;
import com.car.voc.model.LoginUser;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.impl.BrandProductManagerServiceImpl;
import com.microsoft.schemas.vml.STTrueFalse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VocSentenceServiceImpl extends ServiceImpl<VocSentenceMapper, DwdVocSentence> implements DwdVocSentenceService {
    @Autowired
    IBrandProductManagerService brandProductManagerService;
    @Override
    public Result<?> emotionalPurification(FilterCriteriaModel model) {
        NsrVo df = baseMapper.emotionalPurification(model);
       return Result.ok(df);
    }

    @Override
    public Result<?> mentionsUserChannel(FilterCriteriaModel model) {
        VocOverBriefingValueVo re = baseMapper.mentionsUser(model);
        return Result.OK(re);
    }

    @Override
    public Result<?> sourceChannel(FilterCriteriaModel model) {
        model.setNumCycles(2);
        List<ChannelVo> objects= baseMapper.sourceChannel(model);
        Map<String, List<ChannelVo>> map = objects.stream().collect(Collectors.groupingBy(ChannelVo::getDateStr, LinkedHashMap::new, Collectors.toList()));
        Map<String,Object> result = new HashMap<String,Object>();
        Long chl=0l, chlu=0l;
                int i=0;
        for (Map.Entry<String, List<ChannelVo>> mst : map.entrySet()) {
            i++;
            if (i==1){
                if (mst.getValue() != null && mst.getValue().size() == 1 && StrUtil.isBlankIfStr(mst.getValue().get(0).getChannelId())) {
                    result.put("sourceChannel",null);
                }else{
                    result.put("sourceChannel",mst.getValue());
                }
                chl=mst.getValue().stream().filter(obj -> obj.getChannelId() != null && !obj.getChannelId().isEmpty()).count();
            }else{
                chlu=mst.getValue().stream().filter(obj -> obj.getChannelId() != null && !obj.getChannelId().isEmpty()).count();
            }
        }
        result.put("chnum",chl);
        result.put("chnumr", chl-chlu);
        return Result.OK(result);
    }

    @Override
    public Result<?> carSeriesTop(FilterCriteriaModel model) {
        model.setNumCycles(2);
        if (model.getSoundType() == null) {
            model.setSoundType(SoundTypeEnum.emotion);
        }
        List<BrandProductManagerModel> cars= brandProductManagerService.findAll();
        Map<String, BrandProductManagerModel> vocSentenceMap = cars.stream()
                .collect(Collectors.toMap(BrandProductManagerModel::getCode, vocSentence -> vocSentence));
        List<CarEmotionVo> objectLists = baseMapper.homeLeaderCarDistribution(model);
        BigDecimal total = objectLists.stream().map(CarEmotionVo::getTotal).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        objectLists.stream().forEach(e->{
            e.setTotalP(CalculatorUtils.proportion(e.getTotal(),total));
            e.setShowImg(vocSentenceMap.get(e.getCarSeries()).getShowImg());
        });
        return Result.OK(objectLists);
    }

    @Override
    public Result<?> homeLeaderIntentionProp(FilterCriteriaModel model) {
        IntentionRatioVo re =baseMapper.homeLeaderIntentionProp(model);
        return Result.OK(re);
    }

    @Override
    public Result<EmotionProportionVo> homeLeaderEmotionProp(FilterCriteriaModel model) {
        List<EmotionProportionVo> re = baseMapper.homeLeaderEmotionProp(model);
        EmotionProportionVo re2=new EmotionProportionVo();
        if (CollectionUtil.isNotEmpty(re)) {
            re2 = re.get(0);
            EmotionProportionVo re2u = re.get(1);
            re2.setPositiveT(re2.getPositiveP());
            re2.setNegativeT(re2.getNegativeP());
            re2.setNegativeTR(NumberUtil.sub(re2.getNegativeT(),re2u.getNegativeP()));
            re2.setPositiveTR(NumberUtil.sub(re2.getPositiveT(),re2u.getPositiveP()));
        }
        return Result.OK(re2);
    }
    public String getUserId() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return loginUser.getId();
    }
    @Override
    public Result<?> allTagDist(FilterCriteriaModel model) {
        model.setNumCycles(1);
        BigDecimal total = null;
        List<ThemeDistrVo> distrVos=new ArrayList<>();
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            Set<String> threes = new HashSet<>();
            threes.add(model.getThirdDimensionCode());
            model.setThreeDimensionCodes(threes);
            distrVos=baseMapper.allTag4Dist(model);
        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            if (model.getSecondDimensionCodes().size() > 1) {
                distrVos=baseMapper.allTag3Dist(model);
            } else {
                distrVos=baseMapper.allTag3Dist(model);
            }
        } else if (model.getFirstDimensionCode() != null && !"".equals(model.getFirstDimensionCode())) {
            if (StrUtil.isNotBlank(model.getFirstDimensionCode())&&model.getFirstDimensionCode().equals(Constants.Q0001)) {
                model.setFirstDimensionCode(null);
                model.setTagType(2);
                distrVos=baseMapper.qTag1Dist(model);
            }else {
                distrVos=baseMapper.allTag2Dist(model);
            }

        } else if (model.getFirstDimensionCodes()!=null&&model.getFirstDimensionCodes().size()>0) {
            distrVos=baseMapper.qTag1Dist(model);
        } else {
            List<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
            if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
                model.getSecondDimensionCodes().retainAll(sounps);
                List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
                model.setSecondDimensionCodes(interse);
            }
            model.setSecondDimensionCodes(sounps);
            distrVos = baseMapper.allTag1Dist(model);
        }
        if (model.getDataType().equals(DataEnum.numMention)) {
            total = distrVos.stream().map(ThemeDistrVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }
        // 判断列表是否只有一个元素且labelCode为null
        if (distrVos != null && distrVos.size() == 1 && distrVos.get(0).getStatistic().equals(BigDecimal.ZERO)) {
            return Result.OK(null);
        }
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(distrVos, ThemeDistrVo::getStatistic, ThemeDistrVo::setStatisticP);
        return Result.OK(distrVos);
    }

    @Override
    public Result<?> hotWords(FilterCriteriaModel model) {
        List<HighHotWordsVo> highHotWordsVos = baseMapper.hotWords(model);
        return Result.OK(highHotWordsVos);
    }

    @Override
    public Result<?> tagDistribution(FilterCriteriaModel model) {
        model.setNumCycles(1);
        BigDecimal total = null;
        List<ThemeDistrVo> distrVos=new ArrayList<>();
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            Set<String> threes = new HashSet<>();
            threes.add(model.getThirdDimensionCode());
            model.setThreeDimensionCodes(threes);
            distrVos=baseMapper.allTag4Dist(model);
        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0 || StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            if (model.getSecondDimensionCodes().size() > 1) {
                distrVos=baseMapper.allTag3Dist(model);
            } else {
                distrVos=baseMapper.allTag3Dist(model);
            }
        } else if (model.getFirstDimensionCode() != null && !"".equals(model.getFirstDimensionCode())) {
            if (StrUtil.isNotBlank(model.getFirstDimensionCode())&&model.getFirstDimensionCode().equals(Constants.Q0001)) {
                model.setFirstDimensionCode(null);
                model.setTagType(2);
                distrVos=baseMapper.qTag1Dist(model);
            }else {
                distrVos=baseMapper.allTag2Dist(model);
            }

        } else if (model.getFirstDimensionCodes()!=null&&model.getFirstDimensionCodes().size()>0) {
            distrVos=baseMapper.qTag1Dist(model);
        } else {
            List<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
            if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
                model.getSecondDimensionCodes().retainAll(sounps);
                List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
                model.setSecondDimensionCodes(interse);
            }
            model.setSecondDimensionCodes(sounps);
            distrVos = baseMapper.allTag1Dist(model);
        }
        if (model.getDataType().equals(DataEnum.numMention)) {
            total = distrVos.stream().map(ThemeDistrVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }
        // 判断列表是否只有一个元素且labelCode为null
        if (distrVos != null && distrVos.size() == 1 && distrVos.get(0).getStatistic().equals(BigDecimal.ZERO)) {
            return Result.OK(null);
        }
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(distrVos, ThemeDistrVo::getStatistic, ThemeDistrVo::setStatisticP);
        if (CollectionUtil.isEmpty(distrVos)){
            return Result.OK(new ArrayList<>());
        }else {
            return Result.OK(distrVos);
        }
    }


    @Override
    public Result<?> carMachineIntelligent(FilterCriteriaModel model) {
        List<QualityLabelVo> tops= baseMapper.homeLeaderCarIntelligent(model);
        if (tops != null && tops.size() == 1 && tops.get(0).getLabelCode() == null) {
            return Result.OK(null);
        }
        return Result.OK(tops!=null?tops.stream().limit(20).collect(Collectors.toList()):null);
    }

    @Override
    public Result<?> allTagsTitle(LabelDetailFilterModel model) {
            String code = model.getCode();
            PopUpVo pop = new PopUpVo();
            String labelstr = "";
            if (StrUtil.isNotBlank(model.getEmotionKeyword())) {
                labelstr = model.getEmotionKeyword();

            } else if (StrUtil.isNotBlank(code)) {
                labelstr = CalculatorUtils.getAllTagCode(code);
            }

            if (labelstr != null) {
                pop.setLabelStr(labelstr);
            } else {
                pop.setLabelStr("");
            }
            VocOverBriefingValueVo re= baseMapper.mentionsUser(model);
            pop.setStatistic(re.getTotalMentions());
            pop.setUserCount(re.getUsersNum());
            return Result.OK(pop);
        }

    @Override
    public Result<?> provinceMap(FilterCriteriaModel model) {
        List<RegionUserVo> userVos = baseMapper.provinceMap(model);
        return Result.OK(userVos);
    }

    @Override
    public Result<?> salesLeadstatisticUsers(FilterCriteriaModel model) {
        VocOverBriefingValueVo vo = baseMapper.mentionsUser(model);
        return Result.OK(vo);
    }

    @Override
    public Result<?> carPurchaseDist(FilterCriteriaModel model) {
        List<UserLabelVo> tag2s = baseMapper.carPurchaseDist(model);
        return Result.OK(tag2s);
    }

    @Override
    public Result<?> purchaseConcernsPriority(FilterCriteriaModel model) {
        List<HighHotWordsVo> hotWordsVos = baseMapper.purchaseConcernsPriority(model);
        return Result.OK(hotWordsVos);
    }

    @Override
    public Result<?> homeLeaderRegionalTop(FilterCriteriaModel model) {
        List<RegionUserVo> regions=baseMapper.homeLeaderRegionalTop(model);
        VocOverBriefingValueVo re=baseMapper.mentionsUser(model);
        regions.stream().forEach(e->{
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), re.getTotalMentions()));
        });
        if (regions != null && regions.size() == 1 &&regions.get(0).getStatistic()==null) {
            return Result.OK(null);
        }
        return Result.OK(regions);
    }
    @Override
    public Result<?> homeLeaderCommunityTop(FilterCriteriaModel model) {
        List<RegionUserVo> regions = baseMapper.homeLeaderCommunityTop(model);
        VocOverBriefingValueVo re=baseMapper.mentionsUser(model);
        regions.stream().forEach(e->{
            e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), re.getTotalMentions()));
        });
        if (regions != null && regions.size() == 1 &&regions.get(0).getStatistic()==null) {
            return Result.OK(null);
        }
        return Result.OK(regions);
    }
}
