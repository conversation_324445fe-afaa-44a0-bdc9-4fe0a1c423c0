package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocQualityEmotionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.voc.common.Result;

/**
 *
 * @version 1.0.0
 * @ClassName IDwsVocQualityEmotionDiService.java
 * @Description TODO
 * @createTime 2022年10月18日 16:53
 * @Copyright voc
 */
public interface IDwsVocQualityEmotionDiService extends IService<DwsVocQualityEmotionDi> {
    Result<?> qualityProblemTop(FilterCriteriaModel model);
    Result<?> qualityProblemTopHome(FilterCriteriaModel model);

    Result<?> overviewProblemDistribution(FilterCriteriaModel model);

    Result<?> briefingValue(ProductQualityFilterCriteriaModel model);

    Result<?> soarProblem(ProductQualityFilterCriteriaModel model);

    Result<?> problemDistribution(ProductQualityFilterCriteriaModel model);

    Result<?> problemTrends(ProductQualityFilterCriteriaModel model);

    Result<?> typicalProblems(ProductQualityFilterCriteriaModel model);

    Result<?> channelDistributionProduct(ProductQualityFilterCriteriaModel model);

    Result<?> commonProblems(ProductQualityFilterCriteriaModel model);

    Result<?> distributionProblemParts(ProductQualityFilterCriteriaModel model);

    Result<?> severityRatio(ProductQualityFilterCriteriaModel model);

    Result<?> componentTrendParts(ProductQualityFilterCriteriaModel model);

    Result<?> partsSoarComponents(ProductQualityFilterCriteriaModel model);

    Result<?> hotWords(FilterCriteriaModel model);
}
