package com.car.stats.serivce.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.car.stats.mapper.DwsVocEmotionDiMapper;
import com.car.stats.mapper.DwsVocQualityEmotionDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.serivce.StatsCommonService;
import com.car.stats.vo.ChannelVo;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.SysDictItem;
import com.car.voc.service.ISysDictItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName StatsCommonServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月25日 15:44
 * @Copyright voc
 */
@Service
public class StatsCommonServiceImpl implements StatsCommonService {
    @Autowired
    ISysDictItemService iSysDictItemService;
    @Override
    public FilterCriteriaModel homeUniformConditions(Integer dayNum) {
        QueryWrapper<SysDictItem> rwquery=new QueryWrapper<>();
        rwquery.lambda().eq(SysDictItem::getItemText,"vocHomeTimeRange");
        Integer day= Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
        day=day+1;
        if (dayNum!=null){
            day=dayNum;
        }
        FilterCriteriaModel model =new FilterCriteriaModel();
        DateTime newdate=model.getNewDate();

        String thisStartTime= DateUtil.offsetDay(newdate,-day).toDateStr()+" 00:00:00";
        String thisEndTime=DateUtil.offsetDay(newdate,-1).toDateStr()+" 23:59:59";
        String upStartTime=DateUtil.offsetDay(DateUtil.offsetDay(newdate,-day),-day).toDateStr()+" 00:00:00";
        model=new FilterCriteriaModel(thisStartTime,thisEndTime,upStartTime,thisStartTime);
        model.setDayNum(day);
        return model;
    }
    @Override
    public FilterCriteriaModel getNoticeWarningDate(FilterCriteriaModel model) {
        QueryWrapper<SysDictItem> rwquery=new QueryWrapper<>();
        rwquery.lambda().eq(SysDictItem::getItemText,"defaultTimePeriod");
        Integer type= Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
//        0为周，1为月，2为季,3为半年,4为年度


        String thisStartTime;
        String thisEndTime=new DateTime().toString();
        if (type==0){
            thisStartTime=DateUtil.beginOfWeek(new DateTime()).toString();
        }else if (type==1){
            thisStartTime=DateUtil.beginOfMonth(new DateTime()).toString();
        }else if (type==2){
            thisStartTime=DateUtil.beginOfQuarter(new DateTime()).toString();
        }else if (type==4){
            thisStartTime=DateUtil.beginOfYear(new DateTime()).toString();
        }else {
            thisStartTime=DateUtil.beginOfMonth(new DateTime()).toString();
        }
        model.setStartDate(thisStartTime);
        model.setEndDate(thisEndTime);
        model.setDateUnit(type);
        return model;
    }

    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;
    @Override
    public List<ChannelVo> setChannelAllList(FilterCriteriaModel model) {

        List<ChannelVo> objects = new ArrayList<>();
        if (model.isCPoint()){//查询私域二级
            model.setChannelId(CommonConstant.privateChannelId);
            model.setChannelIds(null);
            model.setDataSources(Arrays.asList(CommonConstant.privateChannelId));
            objects.addAll(dwsVocEmotionDiMapper.channel2Distribution(model));
        }else if (model.getChannelId()!=null && !"".equals(model.getChannelId())&&model.getChannelId().equals(CommonConstant.publicSphereId)&&model.getChannelIds().size()==0){
            //公域二级
            objects.addAll(dwsVocEmotionDiMapper.channel2Distribution(model));
        }else if (model.getChannelIds().size()>0&&StrUtil.isBlankIfStr(model.getChannelId())){
            //公域三级
            model.setDataSources(Arrays.asList(CommonConstant.publicSphereId));
            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));
        }else if (model.getChannelId()!=null && !"".equals(model.getChannelId())){
            model.setDataSources(null);
            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));

        }else {
            model.setDataSources(null);model.setChannelId(null);
            objects.addAll(dwsVocEmotionDiMapper.dataSourceDistribution(model));
        }

        return objects;
    }
    @Resource
    DwsVocQualityEmotionDiMapper qualityEmotionDiMapper;
    @Override
    public List<ChannelVo> setChannelAllList(ProductQualityFilterCriteriaModel model) {

        List<ChannelVo> objects = new ArrayList<>();
        if (model.isCPoint()){//查询私域二级
            model.setChannelId(CommonConstant.privateChannelId);
            model.setDataSources(null);model.setChannelIds(null);
            model.setDataSources(Arrays.asList(CommonConstant.privateChannelId));
            objects.addAll(qualityEmotionDiMapper.channel2DistributionProduct(model));
        }else if (model.getChannelId()!=null && !"".equals(model.getChannelId())&&model.getChannelId().equals(CommonConstant.publicSphereId)&&model.getChannelIds().size()==0){
            //公域二级
            model.setDataSources(null);
            objects.addAll(qualityEmotionDiMapper.channel2DistributionProduct(model));
        }else if (model.getChannelIds().size()>0&&StrUtil.isBlankIfStr(model.getChannelId())){
            //公域三级
            model.setDataSources(Arrays.asList(CommonConstant.publicSphereId));
            objects.addAll(qualityEmotionDiMapper.channelDistributionProduct(model));
        }else if (model.getChannelId()!=null && !"".equals(model.getChannelId())){
            model.setDataSources(null);
            objects.addAll(qualityEmotionDiMapper.channelDistributionProduct(model));
        }else {
            model.setDataSources(null);model.setChannelId(null);
            objects.addAll(qualityEmotionDiMapper.dataSourceDistribution(model));
        }
        return objects;

    }


}
