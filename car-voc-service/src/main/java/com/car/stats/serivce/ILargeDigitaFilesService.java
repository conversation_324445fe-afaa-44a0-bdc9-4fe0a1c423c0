package com.car.stats.serivce;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.stats.model.LargeDigitaFilesModel;

import java.util.List;
import java.util.function.Function;

public interface ILargeDigitaFilesService {


    void start(String fileName, String taskId, long total, Function<IPage, List<?>> rows, Class<?> clazz, int batchSize);

    String getFileUrl(LargeDigitaFilesModel model);

    List<LargeDigitaFilesModel> getFileList(LargeDigitaFilesModel model);

    LargeDigitaFilesModel getFile(LargeDigitaFilesModel model);

    void insert(LargeDigitaFilesModel model);
}
