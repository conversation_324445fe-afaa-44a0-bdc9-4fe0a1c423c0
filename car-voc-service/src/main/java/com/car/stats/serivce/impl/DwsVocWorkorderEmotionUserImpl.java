package com.car.stats.serivce.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocWorkorderEmotionUser;
import com.car.stats.mapper.DwsVocWorkorderEmotionUserMapper;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.WorkOrderFilteModel;
import com.car.stats.serivce.DwsVocWorkorderEmotionUserService;
import com.car.stats.vo.HomePurposeTrendVo;
import com.car.stats.vo.LabelUserVo;
import com.car.stats.vo.WorkOrderUserVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;
import com.car.voc.common.util.CalculatorUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName DwsVocWorkorderEmotionUserImpl.java
 * @Description TODO
 * @createTime 2022年11月07日 14:44
 * @Copyright voc
 */
@Service
public class DwsVocWorkorderEmotionUserImpl extends ServiceImpl<DwsVocWorkorderEmotionUserMapper, DwsVocWorkorderEmotionUser> implements DwsVocWorkorderEmotionUserService {
    @Override
    public Result<?> workOrderType1() {
        QueryWrapper<DwsVocWorkorderEmotionUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().groupBy(DwsVocWorkorderEmotionUser::getMediaFirstCategory);
        queryWrapper.lambda().select(DwsVocWorkorderEmotionUser::getMediaFirstCategory);
        queryWrapper.lambda().isNotNull(DwsVocWorkorderEmotionUser::getMediaFirstCategory);
        return Result.OK(this.baseMapper.selectObjs(queryWrapper));
    }

    @Override
    public Result<?> workOrderUserType1(WorkOrderFilteModel model) {
        model.SetUpCycle();
        List<WorkOrderUserVo> workOrders = baseMapper.workOrderUserType1(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setMediaNames(workOrders.stream().map(WorkOrderUserVo::getMediaName).collect(Collectors.toSet()));
        List<WorkOrderUserVo> workOrdersup = baseMapper.workOrderUserType1(model);
        workOrders.forEach(e -> {
            List<WorkOrderUserVo> onet = workOrdersup.stream().filter(d -> e.getMediaName().equals(d.getMediaName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(onet)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), onet.get(0).getUserNum()));
            }
        });
        return Result.OK(workOrders);
    }

    @Override
    public Result<?> mediaTheme(WorkOrderFilteModel model) {

        model.SetUpCycle();
        List<LabelUserVo> workOrders = baseMapper.mediaTheme1(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setLabelCodes(workOrders.stream().map(LabelUserVo::getLabelCode).collect(Collectors.toSet()));
        List<LabelUserVo> workOrdersup = baseMapper.mediaTheme1(model);
        workOrders.forEach(e -> {
            List<LabelUserVo> onet = workOrdersup.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(onet)) {
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
                e.setUserNumR(CalculatorUtils.ringRatio(e.getUserNum(), onet.get(0).getUserNum()));
            }
        });
        return Result.OK(workOrders);
    }

    @Override
    public Map<String, String> userAndStatistic(LabelDetailFilterModel model) {

        return baseMapper.userAndStatistic(model);
    }

    @Override
    public List<HomePurposeTrendVo> trendChangeLabelList(LabelDetailFilterModel model) {
        return baseMapper.trendChangeLabelList(model);
    }

    @Override
    public IPage<UserListInfoVo> getUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page) {
        return baseMapper.getUserList(model, page);
    }
}
