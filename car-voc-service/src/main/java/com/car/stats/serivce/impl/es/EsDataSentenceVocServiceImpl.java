package com.car.stats.serivce.impl.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.model.*;
import com.car.stats.serivce.IDwdVocUserRiskService;
import com.car.stats.serivce.INPSAnalysisService;
import com.car.stats.serivce.StatsCommonService;
import com.car.stats.serivce.es.EsDataContentVocService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.ContentVo;
import com.car.stats.vo.SoundContentQueryVo;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.SourceTagTypeVo;
import com.car.stats.vo.popvo.ContentUserDetailVo;
import com.car.stats.vo.popvo.IntentionChannelStatisticVo;
import com.car.stats.vo.popvo.UserDetailVo;
import com.car.stats.vo.popvo.UserRepairDetailVo;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.DateStyle;
import com.car.voc.common.util.*;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.SysDictItem;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.TtBalanceLabourMapper;
import com.car.voc.mapper.TtBalanceRepairPartMapper;
import com.car.voc.mapper.VocNPSAnalysisMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.*;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.NewDictVo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.MaxAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.NoSuchIndexException;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilterBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName EsCommonServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月31日 10:01
 * @Copyright voc
 */
@Service
@Log4j2
public class EsDataSentenceVocServiceImpl implements EsDataSentenceVocService {
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private ElasticsearchRestTemplate restTemplate;
    @Autowired
    StatsCommonService commonService;
    @Autowired
    ISysDictItemService iSysDictItemService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @Autowired
    IDwdVocUserRiskService vocUserRiskService;

    @Autowired
    TtBalanceLabourMapper balanceLabourMapper;
    @Autowired
    TtBalanceRepairPartMapper balanceRepairPartMapper;

    @Autowired
    private IBrandProductManagerService brandProductManagerService;
    @Autowired
    IProvinceAreaService provinceAreaService;
    @Autowired
    VocBrandRegionService brandRegionService;
    @Autowired
    EsDataContentVocService contentVocService;
    @Autowired
    IVocBusinessTagService tagService;
    @Autowired
    IFaultProblemService faultProblemService;
    @Autowired
    INPSAnalysisService inpsAnalysisService;
    @Autowired
    VocNPSAnalysisMapper vocNPSAnalysisMapper;
    @Resource(name = "vocSentesExecutor")
    Executor vocSentesExecutor;


    @Value("${configuration.esIndex.voc-sentence}")
    private String vocSentence;
    @Value("${configuration.esIndex.voc-sentence-repeated}")
    private String vocSentenceRepeated;

    @Override
    public Page<SoundContentVo> pageQueryList(LabelDetailFilterModel model, Page<SoundContentVo> page) {


        if (model.getDateUnit() == null) {
            QueryWrapper<SysDictItem> rwquery = new QueryWrapper<>();
            rwquery.lambda().eq(SysDictItem::getItemText, "vocHomeTimeRange");
            Integer day = Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
            FilterCriteriaModel home = commonService.homeUniformConditions(day);
            model.setStartDate(home.getStartDate());
            model.setEndDate(home.getEndDate());
        }
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (model.isRiskPire()) {//无权限
            comNotPermissionQueryBUider(model, queryBuilder);
        } else {
            comQueryBUider(model, queryBuilder);
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        log.error("es查询：" + queryBuilder.toString());
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        searchQuery.withTrackTotalHits(true);
        searchQuery.withSourceFilter(new FetchSourceFilterBuilder().withExcludes("id").build());
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        long total = pagelist.getTotalHits();
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();


        List<SoundContentVo> list = new ArrayList<>();

        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(500);
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                executor.submit(new Runnable() {
                    @Override
                    public void run() {
                        SoundContentVo se = new SoundContentVo(e.getContent());
                        VocChannelCategory category = listchannels.stream().filter(o -> o.getId().equals(e.getContent().getChannelId())).findFirst().orElse(null);
                        VocChannelCategory p2 = listchannels.stream().filter(l -> l.getId().equals(category.getPid())).findFirst().orElse(null);
                        if (p2 != null) {
                            se.setIsOneId("Customer-data".equals(p2.getPid()) ? "1" : "0");
                        }
                        String cstr = "";
                        if (category != null) {
                            cstr = category.getName();
                            se.setSoundType(category.getCode());
                        }
                        if ("voc_original_call_in_out".equals(e.getContent().getOriginalIndex())){
                            se.setCallId(se.getContentId());
                        }
                        if ("voc_original_session_info".equals(e.getContent().getOriginalIndex())){
                            se.setSessionId(se.getContentId());
                        }
                        if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                            se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                        }
                        se.setChannelStr(cstr);
                        se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
                        if (!StrUtil.isBlankIfStr(e.getContent().getBrandCode())) {
                            se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
                        } else {
                            se.setBrandCode("汽车");
                        }
                        synchronized (list) {
                            list.add(se);
                        }

                    }
                });
            });
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        Page<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(total);
        repage.setRecords(list);
        return repage;
    }


    @Override
    public Page<SoundContentVo> pageQueryListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page) {


        if (model.getDateUnit() == null) {
            QueryWrapper<SysDictItem> rwquery = new QueryWrapper<>();
            rwquery.lambda().eq(SysDictItem::getItemText, "vocHomeTimeRange");
            Integer day = Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
            FilterCriteriaModel home = commonService.homeUniformConditions(day);
            model.setStartDate(home.getStartDate());
            model.setEndDate(home.getEndDate());
        }
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (model.isRiskPire()) {//无权限
            comNotPermissionQueryBUider(model, queryBuilder);
        } else {
            comQueryBUider(model, queryBuilder);
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        log.error("es查询：" + queryBuilder);
        long total = 0;
        try {
            total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        searchQuery.withSourceFilter(new FetchSourceFilterBuilder().withExcludes("id").build());
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();
        Map<String, Map> contents = new ConcurrentHashMap<>();
        Map<String, List<EsDataSentenceVocs>> indexs = pagelist.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.groupingBy(EsDataSentenceVocs::getOriginalIndex));
        Map<String, Map<String, Map<String, String>>> indesf = new HashMap<>();
        setContentFielIndex(indexs.keySet().stream().collect(Collectors.toList()), indesf);
        //查询原文数据
        for (String indexName : indexs.keySet()) {
            processIndex(indexName, indexs, contents);
        }
        List<SoundContentVo> list = new ArrayList<>();

        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(500);
            pagelist.stream().collect(Collectors.toList()).forEach(e -> executor.submit(() -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = listchannels.stream().filter(o -> o.getId().equals(e.getContent().getChannelId())).findFirst().orElse(null);
                VocChannelCategory p2 = listchannels.stream().filter(l -> l.getId().equals(category.getPid())).findFirst().orElse(null);
                if (p2 != null) {
                    se.setIsOneId("Customer-data".equals(p2.getPid()) ? "1" : "0");
                }
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }
                se.setChannelStr(cstr);
                se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
                if (!StrUtil.isBlankIfStr(e.getContent().getBrandCode())) {
                    se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
                } else {
                    se.setBrandCode("汽车");
                }
                setContentBySoundExportXls(se, contents, e.getContent(), indesf);
                synchronized (list) {
                    list.add(se);
                }

            }));
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        Page<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(total);
        repage.setRecords(list);
        return repage;
    }

    @Override
    public Page<SoundContentVo> pageQueryAllList(LabelDetailFilterModel model, Page<SoundContentVo> page) {

        if (model.getDateUnit() == null) {
            QueryWrapper<SysDictItem> rwquery = new QueryWrapper<>();
            rwquery.lambda().eq(SysDictItem::getItemText, "vocHomeTimeRange");
            Integer day = Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
            FilterCriteriaModel home = commonService.homeUniformConditions(day);
            model.setStartDate(home.getStartDate());
            model.setEndDate(home.getEndDate());
        }
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (model.isRiskPire()) {//无权限
            comNotPermissionQueryBUider(model, queryBuilder);
        } else {
            comQueryBUider(model, queryBuilder);
        }

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        log.error("es查询：" + queryBuilder.toString());
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        searchQuery.withTrackTotalHits(true);
        searchQuery.withSourceFilter(new FetchSourceFilterBuilder().withExcludes("id").build());
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();


        List<SoundContentVo> list = new ArrayList<>();

        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(500);
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                executor.submit(new Runnable() {
                    @Override
                    public void run() {
                        SoundContentVo se = new SoundContentVo(e.getContent());
                        VocChannelCategory category = listchannels.stream().filter(o -> o.getId().equals(e.getContent().getChannelId())).findFirst().orElse(null);
                        VocChannelCategory p2 = listchannels.stream().filter(l -> l.getId().equals(category.getPid())).findFirst().orElse(null);
                        if (p2 != null) {
                            se.setIsOneId("Customer-data".equals(p2.getPid()) ? "1" : "0");
                        }
                        if ("voc_original_call_in_out".equals(e.getContent().getOriginalIndex())){
                            se.setCallId(se.getContentId());
                        }
                        if ("voc_original_session_info".equals(e.getContent().getOriginalIndex())){
                            se.setSessionId(se.getContentId());
                        }
                        String cstr = "";
                        if (category != null) {
                            cstr = category.getName();
                            se.setSoundType(category.getCode());
                        }
                        if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                            se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                        }
                        se.setChannelStr(cstr);
                        se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
                        if (!StrUtil.isBlankIfStr(e.getContent().getBrandCode())) {
                            se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
                        } else {
                            se.setBrandCode("汽车");
                        }
                        synchronized (list) {
                            list.add(se);
                        }

                    }
                });
            });
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        Page<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(pagelist.getTotalHits());
        repage.setRecords(list);
        return repage;
    }


    @Override
    public Page<SoundContentVo> pageQueryAllListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page) {

        if (model.getDateUnit() == null) {
            QueryWrapper<SysDictItem> rwquery = new QueryWrapper<>();
            rwquery.lambda().eq(SysDictItem::getItemText, "vocHomeTimeRange");
            Integer day = Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
            FilterCriteriaModel home = commonService.homeUniformConditions(day);
            model.setStartDate(home.getStartDate());
            model.setEndDate(home.getEndDate());
        }
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (model.isRiskPire()) {//无权限
            comNotPermissionQueryBUider(model, queryBuilder);
        } else {
            comQueryBUider(model, queryBuilder);
        }

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        log.warn("es查询：" + queryBuilder.toString());
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        searchQuery.withTrackTotalHits(true);
        searchQuery.withSourceFilter(new FetchSourceFilterBuilder().withExcludes("id").build());
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();
        Map<String, Map> contents = new ConcurrentHashMap<>();
        Map<String, List<EsDataSentenceVocs>> indexs = pagelist.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.groupingBy(EsDataSentenceVocs::getOriginalIndex));
        Map<String, Map<String, Map<String, String>>> indesf = new HashMap<>();
        setContentFielIndex(indexs.keySet().stream().collect(Collectors.toList()), indesf);
        //查询原文数据
        for (String indexName : indexs.keySet()) {
            processIndex(indexName, indexs, contents);
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(500);
            pagelist.stream().collect(Collectors.toList()).forEach(e -> executor.submit(() -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = listchannels.stream().filter(o -> o.getId().equals(e.getContent().getChannelId())).findFirst().orElse(null);
                VocChannelCategory p2 = listchannels.stream().filter(l -> l.getId().equals(category.getPid())).findFirst().orElse(null);
                if (p2 != null) {
                    se.setIsOneId("Customer-data".equals(p2.getPid()) ? "1" : "0");
                }
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }
                se.setChannelStr(cstr);
                se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
                if (!StrUtil.isBlankIfStr(e.getContent().getBrandCode())) {
                    se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
                } else {
                    se.setBrandCode("汽车");
                }
                setContentBySoundExportXls(se, contents, e.getContent(), indesf);
                synchronized (list) {
                    list.add(se);
                }

            }));
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        Page<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(pagelist.getTotalHits());
        repage.setRecords(list);
        return repage;
    }


    @Override
    public Page<SoundContentQueryVo> allDataQuery(LabelDetailFilterModel model, Page<SoundContentQueryVo> page) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        log.info("分页参数：{}，{}", pageable.getPageNumber(), pageable.getPageSize());
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        comNotPermissionQueryBUider(model, queryBuilder);
        FieldSortBuilder sort;

        if (StrUtil.isNotBlank(model.getTag()) && "去重声音".equals(model.getTag())) {
            sort = SortBuilders.fieldSort("publishTime.keyword").order(SortOrder.DESC);
        } else {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        }
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.withTrackTotalHits(true);
        log.debug("es查询：" + queryBuilder);
        long total;
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        searchQuery.withSourceFilter(new FetchSourceFilterBuilder().withExcludes("id").build());
        searchQuery.withTrackTotalHits(true);
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist;
        long start = System.currentTimeMillis();
        try {
            if (StrUtil.isNotBlank(model.getTag()) && "去重声音".equals(model.getTag())) {
                pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentenceRepeated));
            } else {
                pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
            }

        } catch (Exception e) {
            log.error("es服务异常", e);
            throw new BootException("es服务异常！");
        }
        long end = System.currentTimeMillis();
        log.info("声音列表查询结束,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");

        total = pagelist.getTotalHits();
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();
        Map<String, VocChannelCategory> chMap = listchannels.stream().collect(Collectors.toMap(VocChannelCategory::getId, person -> person));
        Map<String, Map> contents = new ConcurrentHashMap<>();
        Map<String, List<EsDataSentenceVocs>> indexs = pagelist.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.groupingBy(EsDataSentenceVocs::getOriginalIndex));
        Map<String, Map<String, Map<String, String>>> indesf = new HashMap<>();
        setContentFielIndex(indexs.keySet().stream().collect(Collectors.toList()), indesf);
        List<SoundContentQueryVo> list = Collections.synchronizedList(new ArrayList<>());

        for (String indexName : indexs.keySet()) {
            processIndex(indexName, indexs, contents);
        }
        long end2 = System.currentTimeMillis();
        log.info("声音列表查询所有原文结束,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end2 - end) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end2 - end) + "秒" : (end2 - end) + "毫秒");
        Map<String, String> area = brandRegionService.cacheBrandRegionAll()
                .stream()
                .filter(vr -> !"2".equals(vr.getApplicationType()))
                .collect(Collectors.toMap(
                        vr -> vr.getBrand() + vr.getProvinceCode(),
                        VocBrandRegion::getRegionalName,
                        (existing, replacement) -> replacement
                ));

        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(100);
            for (SearchHit<EsDataSentenceVocs> searchHits : pagelist.getSearchHits()) {
                executor.submit(() -> getSoundContentQueryVo(list, model, searchHits, contents, indesf, chMap, area));
            }
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            long end3 = System.currentTimeMillis();
            log.info("声音列表区域、渠道处理结束,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end3 - end2) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end3 - end2) + "秒" : (end3 - end2) + "毫秒");

        }
        long end4 = System.currentTimeMillis();
        log.info("声音列表处理结束,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end4 - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end4 - start) + "秒" : (end4 - start) + "毫秒");
        Page<SoundContentQueryVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(total);
        List<SoundContentQueryVo> orlist = list.stream().sorted(Comparator.comparing(SoundContentQueryVo::getPublishTime).reversed()).collect(Collectors.toList());
        repage.setRecords(orlist);
        return repage;
    }

    @NotNull
    private void getSoundContentQueryVo(List<SoundContentQueryVo> list, LabelDetailFilterModel model, SearchHit<EsDataSentenceVocs> e, Map<String, Map> contents, Map<String, Map<String, Map<String, String>>> indesf, Map<String, VocChannelCategory> chMap, Map<String, String> areas) {
        SoundContentQueryVo se = new SoundContentQueryVo(e.getContent());
        if (model.isExcel()) {
            se = setContentBySound(se, contents, e.getContent(), indesf);
        }
        VocChannelCategory category = chMap.get(e.getContent().getChannelId());
        if (category != null) {
            se.setChannelStr(category.getName());
            VocChannelCategory ch2 = chMap.get(category.getPid());
            if (ch2 != null) {
                se.setChannelStr2(ch2.getName());
            }
            VocChannelCategory ch1 = chMap.get(ch2.getPid());
            if (ch1 != null) {
                se.setChannelStr1(ch1.getName());
            }
        }
        if ("voc_original_call_in_out".equals(e.getContent().getOriginalIndex())){
            se.setCallId(se.getContentId());
        }
        if ("voc_original_session_info".equals(e.getContent().getOriginalIndex())){
            se.setSessionId(se.getContentId());
        }
        if (StrUtil.isNotBlank(e.getContent().getProvince())) {
            se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
            se.setRegion(areas.get((se.getBrandCode() + e.getContent().getProvince()).replace(" ", "")));
        }
        se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
        if (!StrUtil.isBlankIfStr(e.getContent().getBrandCode())) {
            se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
        } else {
            se.setBrandCode("");
        }
        if ("1".equals(se.getTagType())) {
            se.setFirstDimension(tagService.getNameByCode(se.getFirstDimensionCode()));
        }
        if (StrUtil.isNotBlank(model.getTag()) && "去重声音".equals(model.getTag())) {
            se.setStatus("去重数据");
        } else {
            se.setStatus("应用数据");
        }
        list.add(se);
    }

    private void processIndex(String indexName, Map<String, List<EsDataSentenceVocs>> indexs, Map<String, Map> contents) {
        List<String> condids = indexs.get(indexName).stream().map(EsDataSentenceVocs::getContentId).distinct().collect(Collectors.toList());
        int sized = condids.size();
        int dx = 500;
        int fullChunks = sized / dx;
        List<CompletableFuture<Map>> futureList = new ArrayList<>();
        for (int i = 0; i < fullChunks; i++) {
            int finalI = i;
            CompletableFuture<Map> future = CompletableFuture.supplyAsync(() -> contentVocService.setContentByContentIds(condids.subList(finalI * dx, (finalI + 1) * dx), indexName), vocSentesExecutor);
            futureList.add(future);
        }
        CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).join();
        futureList.stream().forEach(f -> {
            contents.putAll(f.join());
        });
        if (sized % dx != 0) {
            Map map = contentVocService.setContentByContentIds(condids.subList(fullChunks * dx, sized), indexName);
            contents.putAll(map);
        }
    }

    @Override
    public Page<ContentVo> allSourceDataQuery(LabelDetailFilterModel model, Page<ContentVo> page, List<String> indexs) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        Map<String, Map<String, Map<String, String>>> fiel = new HashMap<>();
        setContentFielIndex(indexs, fiel);
        String cresort = "id";
        if (StrUtil.isBlankIfStr(cresort)) {
            cresort = "createTime";
        }
        Set<String> createTimes = new HashSet<>();
        List<VocBrandRegion> brandRegionList = null;
        comSourceQueryBuider(model, queryBuilder, fiel, createTimes, brandRegionList);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        FieldSortBuilder sort = SortBuilders.fieldSort(cresort + ".keyword").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort(cresort + ".keyword").order(SortOrder.fromString(model.getOrder()));
        }
        searchQuery.withQuery(queryBuilder);
        long total;
        searchQuery.withPageable(pageable);
        searchQuery.withTrackTotalHits(true);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        SearchHits<Map> pagelist = null;
        try {
            pagelist = restTemplate.search(query, Map.class, IndexCoordinates.of(String.join(",", indexs)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        total = pagelist.getTotalHits();
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();
        Map<String, VocChannelCategory> chMap = listchannels.stream().collect(Collectors.toMap(VocChannelCategory::getId, person -> person));
        List<NewDictVo> areas = provinceAreaService.newQueryProvinceByAreaCode(model.getBrand());
//        Map<String, String> ars = new HashMap<>();
//        areas.stream().forEach(e -> ars.putAll(Arrays.stream(e.getProvinceTexts().split(",  ")).collect(Collectors.toMap(fruit -> fruit.replace(" ", ""), fruit -> e.getItemText()))));

        List<ContentVo> list = new ArrayList<>();
        Map<String, String> orgIndex = (Map<String, String>) redisUtil.get(CacheConstant.index_channelId_map);
        if (pagelist != null) {

            List<SysDictItem> sysDictItemByItemValueAndDictId = iSysDictItemService.getSysDictItemByItemValueAndDictId(null, CommonConstant.sys_online_business_type_dict_id);
            Map<String, String> sysDictMap = sysDictItemByItemValueAndDictId.stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getItemText));
            ExecutorService executor = Executors.newFixedThreadPool(200);
            pagelist.stream().collect(Collectors.toList()).forEach(e -> executor.submit(() -> {
                try {
                    Map<String, String> fisk = fiel.get(e.getIndex()).get("valueKey");
                    ContentVo se = BeanUtil.toBean(e.getContent(), ContentVo.class, CopyOptions.create().setFieldMapping(fisk));
                    se.setClassSplit(MapUtil.reverse(fisk).get("split"));

                    se.setIndexId(e.getIndex());
                    if (e.getIndex().contains("voc_original_call_in_out")) {
                        se.setContentId(e.getContent().get("callId").toString());
                    } else if(e.getIndex().contains("voc_original_session_info")){
                        se.setSessionId(e.getContent().get("sessionId").toString());
                    }else if (StrUtil.isBlankIfStr(se.getContentId())) {
                        se.setContentId(e.getContent().get("id").toString());
                    }
                    if (e.getIndex().contains("voc_original_rescue")) {
                        se.setClassify1Str(tagService.getNameByCode(se.getClassify1()));
                        se.setClassify2Str(tagService.getNameByCode(se.getClassify2()));
                        se.setClassify3Str(tagService.getNameByCode(se.getClassify3()));
                        se.setClassify4Str(tagService.getNameByCode(se.getClassify4()));
                    }
                    if (e.getIndex().contains("voc_original_session_info") && !StrUtil.isBlankIfStr(e.getContent().get("businessType")) && sysDictMap.containsKey(e.getContent().get("businessType").toString())) {
                        se.setContentType(sysDictMap.get(e.getContent().get("businessType").toString()));
                    }
                    if (e.getIndex().contains("voc_original_public_opinion")) {
                        se.setChannelId(orgIndex.get(e.getIndex()));
                    }
                    if (e.getIndex().contains("voc_original_subject")) {
                        se.setChannelId(orgIndex.get(e.getIndex()));
                        se.setChannelStr((String) e.getContent().get("layout"));
                        se.setChannelStr2((String) e.getContent().get("medianamecn"));
                        se.setChannelStr1("公域数据");
                    } else {
                        VocChannelCategory category = chMap.get(se.getChannelId());
                        if (category != null) {
                            se.setChannelStr(category.getName());
                            VocChannelCategory ch2 = chMap.get(category.getPid());
                            if (ch2 != null) {
                                se.setChannelStr2(ch2.getName());
                            }
                            VocChannelCategory ch1 = chMap.get(ch2.getPid());
                            if (ch1 != null) {
                                se.setChannelStr1(ch1.getName());
                            }
                        }
                    }

                    /*if (StrUtil.isNotBlank(se.getCustomerProvince())) {
                        String ares = ars.get(se.getCustomerProvince().replaceAll("省|市|自治区|维吾尔|壮族|回族", ""));
                        if (ares != null) {
                            StringBuffer re = new StringBuffer(ares);
                            se.setRegion(re.toString());
                        }
                    }*/
                    synchronized (list) {
                        list.add(se);
                    }
                } catch (Exception ex) {
                    log.error("原数据转换出错,原数据：{}", e.getContent().toString());
                    log.error("原数据转换出错：{}", ex.getMessage());
                    ex.printStackTrace();
                }
            }));
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }

        Page<ContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(total);
        List<ContentVo> orlist = list.stream().sorted(Comparator.comparing(ContentVo::getCreateTime).reversed()).collect(Collectors.toList());
        repage.setRecords(orlist);
        return repage;
    }

    @Override
    public SourceTagTypeVo allSourceGroupByTag(LabelDetailFilterModel model, List<String> indexs) {
        Pageable pageable = PageRequest.of(0, 1);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        Map<String, Map<String, Map<String, String>>> fiel = new HashMap<>();
        setContentFielIndex(indexs, fiel);
        List<VocBrandRegion> brandRegionList = null;
        Set<String> createTimes = new HashSet<>();
        comSourceQueryBuider(model, queryBuilder, fiel, createTimes, brandRegionList);
        TermsAggregationBuilder tagType = AggregationBuilders.terms("tagType").field("tag.keyword").size(10);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.withTrackTotalHits(true);
        searchQuery.addAggregation(tagType);
        log.error("es查询：" + queryBuilder.toString());
        searchQuery.withPageable(pageable);
        searchQuery.withTrackTotalHits(true);
        NativeSearchQuery query = searchQuery.build();
        SearchHits<Map> pagelist = null;
        try {
            pagelist = restTemplate.search(query, Map.class, IndexCoordinates.of(String.join(",", indexs)));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        SourceTagTypeVo tagTypeVo = new SourceTagTypeVo();
        tagTypeVo.setTotal(BigDecimal.valueOf(pagelist.getTotalHits()));
        Terms agg = pagelist.getAggregations().get("tagType");
        agg.getBuckets().forEach(e -> {
            if ("有结果".equals(e.getKeyAsString())) {
                tagTypeVo.setMark(BigDecimal.valueOf(e.getDocCount()));
                tagTypeVo.setMarkP(CalculatorUtils.proportion(tagTypeVo.getMark(), tagTypeVo.getTotal()));
            } else if ("无结果".equals(e.getKeyAsString())) {
                tagTypeVo.setUnmark(BigDecimal.valueOf(e.getDocCount()));
                tagTypeVo.setUnmarkP(CalculatorUtils.proportion(tagTypeVo.getUnmark(), tagTypeVo.getTotal()));
            } else if ("去噪".equals(e.getKeyAsString())) {
                tagTypeVo.setDenoise(BigDecimal.valueOf(e.getDocCount()));
                tagTypeVo.setDenoiseP(CalculatorUtils.proportion(tagTypeVo.getDenoise(), tagTypeVo.getTotal()));
            }
        });
        return tagTypeVo;
    }

    private void setContentFielIndex(List<String> indexs, Map<String, Map<String, Map<String, String>>> indes) {


        indexs.stream().forEach(k -> {
            String itemkey = k.replaceAll("_2|_backup", "");
            List<DictVo> prs = sysDictService.queryDictItemsByCode(itemkey);
            Map<String, String> keyValue = new HashMap<>();
            prs.forEach(e -> {
                if (keyValue != null) {
                    keyValue.put(e.getValue(), e.getText());
                }
            });
            Map<String, Map<String, String>> kv = new HashMap<>();
            kv.put("valueKey", keyValue);
            kv.put("keyValue", MapUtil.reverse(keyValue));
            indes.put(k, kv);
        });

    }

    private SoundContentVo setContentBySoundExportXls(SoundContentVo se, Map<String, Map> contents, EsDataSentenceVocs content, Map<String, Map<String, Map<String, String>>> indf) {
        Map conte = contents.get(se.getContentId());

        if (content.getOriginalIndex().contains("voc_original_session_info")) {
            Map ba = (Map) conte.get("contentBase");
            if (ba != null && !StrUtil.isBlankIfStr(conte.get("contentList"))) {
                se.setContent(JSONUtil.toJsonStr(conte.get("contentList")));
            }
            return se;
        }
        if (conte != null) {
            String itemkey = content.getOriginalIndex().replaceAll("_2|_backup", "");
            if (indf.get(itemkey) == null) {
                return se;
            }
            Map<String, String> fisk = indf.get(itemkey).get("valueKey");
            if (fisk == null) {
                return se;
            }
            ContentVo sen = BeanUtil.toBean(conte, ContentVo.class, CopyOptions.create().setFieldMapping(fisk));
            sen.setClassSplit(MapUtil.reverse(fisk).get("split"));
            se.setContent(sen.getContent());
            return se;
        } else {
            return se;
        }
    }


    private SoundContentQueryVo setContentBySound(SoundContentQueryVo se, Map<String, Map> contents, EsDataSentenceVocs content, Map<String, Map<String, Map<String, String>>> indf) {
        Map conte = contents.get(se.getContentId());

        if (content.getOriginalIndex().contains("voc_original_session_info")) {
            Map ba = (Map) conte.get("contentBase");
       /*     if (ba != null && !StrUtil.isBlankIfStr(ba.get("businessType"))) {
                se.setContentType(ba.get("businessType") + "");
            }*/
            if (ba != null && !StrUtil.isBlankIfStr(conte.get("contentList"))) {
                se.setContent(JSONUtil.toJsonStr(conte.get("contentList")));
            }
            return se;
        }
        if (conte != null) {
            String itemkey = content.getOriginalIndex().replaceAll("_2|_backup", "");
            if (indf.get(itemkey) == null) {
                return se;
            }
            Map<String, String> fisk = indf.get(itemkey).get("valueKey");
            if (fisk == null) {
                return se;
            }
            ContentVo sen = BeanUtil.toBean(conte, ContentVo.class, CopyOptions.create().setFieldMapping(fisk));
            sen.setClassSplit(MapUtil.reverse(fisk).get("split"));
            se.setContent(sen.getContent());
            se.setTitle(sen.getTitle());
            se.setClassify1(sen.getClassify1());
            se.setClassify1Str(tagService.getNameByCode(sen.getClassify1()));
            se.setClassify2(sen.getClassify2());
            se.setClassify2Str(tagService.getNameByCode(sen.getClassify2()));
            se.setClassify3(sen.getClassify3());
            se.setClassify3Str(tagService.getNameByCode(sen.getClassify3()));
            se.setClassify4(sen.getClassify4());
            se.setClassify4Str(tagService.getNameByCode(sen.getClassify4()));
            se.setPriCarSeriesName(sen.getModelName());
            se.setContentType(sen.getContentType());
            return se;
        } else {
            return se;
        }
    }


    @Override
    public Page<SoundContentVo> getQualitySoundListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        if (model.getDateUnit() == null) {
            QueryWrapper<SysDictItem> rwquery = new QueryWrapper<>();
            rwquery.lambda().eq(SysDictItem::getItemText, "vocHomeTimeRange");
            Integer day = Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
            FilterCriteriaModel home = commonService.homeUniformConditions(day);
            model.setStartDate(home.getStartDate());
            model.setEndDate(home.getEndDate());
        }
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();

        if (model.isRiskPire()) {
            comNotPermissionQueryBUider(model, queryBuilder);
        } else {
            comQueryBUider(model, queryBuilder);
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        if (!StrUtil.isBlankIfStr(model.getProblemLevel())) {
            queryBuilder.must(QueryBuilders.termQuery("level.keyword", model.getProblemLevel()));
        }

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        log.info("es查询：" + queryBuilder);
        long total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.info("es查询：" + queryBuilder);
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        List<SoundContentVo> list = new ArrayList<>();
        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();
        Map<String, Map> contents = new ConcurrentHashMap<>();
        Map<String, List<EsDataSentenceVocs>> indexs = pagelist.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.groupingBy(EsDataSentenceVocs::getOriginalIndex));
        Map<String, Map<String, Map<String, String>>> indesf = new HashMap<>();
        setContentFielIndex(indexs.keySet().stream().collect(Collectors.toList()), indesf);

        for (String indexName : indexs.keySet()) {
            processIndex(indexName, indexs, contents);
        }

        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(100);
            Set<String> channelIds = pagelist.stream().collect(Collectors.toList()).stream().map(e -> e.getContent().getChannelId()).collect(Collectors.toSet());
            List<VocChannelCategory> vocChannelCategories = channelCategoryService.listByIds(channelIds);
            Map<String, VocChannelCategory> channelCategoryMap = vocChannelCategories.stream().collect(Collectors.toMap(VocChannelCategory::getId, Function.identity()));
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                executor.submit(() -> {
                    SoundContentVo se = new SoundContentVo(e.getContent());
 //                   VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                    VocChannelCategory category = channelCategoryMap.get(e.getContent().getChannelId());
                    VocChannelCategory p2 = listchannels.stream().filter(l -> l.getId().equals(category.getPid())).findFirst().orElse(null);
                    if (p2 != null) {
                        se.setIsOneId("Customer-data".equals(p2.getPid()) ? "1" : "0");
                    }

                    String cstr = "";
                    if (category != null) {
                        cstr = category.getName();
                        se.setSoundType(category.getCode());
                    }
                    if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                        se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                    }
                    se.setChannelStr(cstr);
                    se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
                    se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
                    setContentBySoundExportXls(se, contents, e.getContent(), indesf);
                    list.add(se);
                });
            });
            executor.shutdown();


            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        Page<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(total);
        repage.setRecords(list);
        return repage;
    }


    @Override
    public Page<SoundContentVo> getQualitySoundList(LabelDetailFilterModel model, Page<SoundContentVo> page) {
        if (model.getDateUnit() == null) {
            QueryWrapper<SysDictItem> rwquery = new QueryWrapper<>();
            rwquery.lambda().eq(SysDictItem::getItemText, "vocHomeTimeRange");
            Integer day = Integer.valueOf(iSysDictItemService.getOne(rwquery).getItemValue());
            FilterCriteriaModel home = commonService.homeUniformConditions(day);
            model.setStartDate(home.getStartDate());
            model.setEndDate(home.getEndDate());
        }
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();

        if (model.isRiskPire()) {
            comNotPermissionQueryBUider(model, queryBuilder);
        } else {
            comQueryBUider(model, queryBuilder);
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        if (!StrUtil.isBlankIfStr(model.getProblemLevel())) {
            queryBuilder.must(QueryBuilders.termQuery("level.keyword", model.getProblemLevel()));
        }

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        log.info("es查询：" + queryBuilder.toString());
        long total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.info("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        List<SoundContentVo> list = new ArrayList<>();

        List<VocChannelCategory> listchannels = channelCategoryService.cacheList();

        if (pagelist != null) {
            ExecutorService executor = Executors.newFixedThreadPool(100);
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                executor.submit(new Runnable() {
                    @Override
                    public void run() {
                        SoundContentVo se = new SoundContentVo(e.getContent());
                        VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                        VocChannelCategory p2 = listchannels.stream().filter(l -> l.getId().equals(category.getPid())).findFirst().orElse(null);
                        if (p2 != null) {
                            se.setIsOneId("Customer-data".equals(p2.getPid()) ? "1" : "0");
                        }
                        if ("voc_original_call_in_out".equals(e.getContent().getOriginalIndex())){
                            se.setCallId(se.getContentId());
                        }
                        if ("voc_original_session_info".equals(e.getContent().getOriginalIndex())){
                            se.setSessionId(se.getContentId());
                        }
                        String cstr = "";
                        if (category != null) {
                            cstr = category.getName();
                            se.setSoundType(category.getCode());
                        }
                        if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                            se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                        }
                        se.setChannelStr(cstr);
                        se.setCarSeriesName(brandProductManagerService.getCarNameByCarCode(e.getContent().getCarSeriesCode()));
                        se.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getContent().getBrandCode()));
                        list.add(se);
                    }
                });
            });
            executor.shutdown();


            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        Page<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        repage.setSize(size);
        repage.setTotal(total);
        repage.setRecords(list);
        return repage;
    }

    @Override
    public IPage<SoundContentVo> intentionTrackPage(IntentionTrackModel model) {

        Pageable pageable = PageRequest.of(model.getPageNo() - 1, model.getPageSize());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        comQueryBUider(model, queryBuilder);
        if (!StrUtil.isBlankIfStr(model.getMenuName()) && "productQuality".equals(model.getMenuName())) {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        }
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);

        long total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.info("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        List<SoundContentVo> list = new ArrayList<>();
        pagelist.stream().collect(Collectors.toList()).forEach(e -> {
            SoundContentVo se = new SoundContentVo(e.getContent());
            VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
                se.setSoundType(category.getCode());
            }
            se.setChannelStr(cstr);
            if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
            }

            list.add(se);

        });
        IPage<SoundContentVo> repage = new Page<>();
        repage.setCurrent(model.getPageNo());
        repage.setSize(model.getPageSize());
        repage.setTotal(total);
        repage.setRecords(new ArrayList<>(list));
        return repage;
    }

    @Override
    public List<IntentionChannelStatisticVo> intentionChannelStatistic(UserIntentionTrajectoryModel model) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (StrUtil.isNotBlank(model.getUserId())) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", model.getUserId()));
        }
        if (StrUtil.isNotBlank(model.getIntention())) {
            queryBuilder.must(QueryBuilders.termsQuery("intentionType.keyword", model.getIntention()));
        }
        if (StrUtil.isNotBlank(model.getEmotion())) {
            queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", model.getEmotion()));
        }
        if (!StrUtil.isBlankIfStr(model.getMenuName()) && "productQuality".equals(model.getMenuName())) {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        } else {
//            queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        }


        if (CollectionUtils.isNotEmpty(model.getOtherTag())) {
//            queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode.keyword",model.getOtherTag()));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }


        List<IntentionChannelStatisticVo> vos = new ArrayList<>();
        if (!StrUtil.isBlankIfStr(model.getTagType()) && model.getTagType().equals(1)) {

            TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(1000);
            NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
            searchQuery.withQuery(queryBuilder);
            Pageable pageable = Pageable.ofSize(1);
            searchQuery.withPageable(pageable);
            searchQuery.addAggregation(channelId);
            NativeSearchQuery query = searchQuery.build();
            log.debug("es查询：" + queryBuilder.toString());
            SearchHits<EsDataSentenceVocs> pagelist = null;
            try {
                pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

            } catch (Exception e) {
                log.error("es查询异常：");
                e.printStackTrace();
            }

            Terms agg = pagelist.getAggregations().get("channelId");
            agg.getBuckets().forEach(e -> {
                VocChannelCategory ve = channelCategoryService.getById(e.getKeyAsString());
                String channelStr = "";
                if (ve != null) {
                    channelStr = ve.getName();
                }
                IntentionChannelStatisticVo vo = new IntentionChannelStatisticVo(e.getKeyAsString(), e.getDocCount(), channelStr);
                vos.add(vo);
            });
            BigDecimal total = vos.stream().map(IntentionChannelStatisticVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            IntentionChannelStatisticVo all = new IntentionChannelStatisticVo("", total.longValue(), "全部");
            vos.add(0, all);
            return vos;
        } else {

            setUserPublishCount(model.getUserId(), vos, queryBuilder);

            return vos;
        }

    }

    @Override
    public void setUserPublishCount(String userId, List<IntentionChannelStatisticVo> vos, BoolQueryBuilder queryBuilder) {

        if (StrUtil.isNotBlank(userId)) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", userId));
        }
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(100);
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(1000);
        channelId.subAggregation(contentId);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);
        searchQuery.addAggregation(channelId);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            log.error("es查询报错：");
            e.printStackTrace();
            return;
        }

        List<VocChannelCategory> allchannel = channelCategoryService.cacheList();
        Terms aggChannelId = pagelist.getAggregations().get("channelId");
        for (Terms.Bucket channelids : aggChannelId.getBuckets()) {
            VocChannelCategory ve = allchannel.stream().filter(c -> channelids.getKeyAsString().equals(c.getId())).findFirst().orElse(null);
            String channelStr = "";
            if (ve != null) {
                channelStr = ve.getName();
            }
            Terms aggContentId = channelids.getAggregations().get("contentId");
            IntentionChannelStatisticVo vo = new IntentionChannelStatisticVo(channelids.getKeyAsString(), aggContentId.getBuckets().size(), channelStr);
            vos.add(vo);
        }
        BigDecimal total = vos.stream().map(IntentionChannelStatisticVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        IntentionChannelStatisticVo all = new IntentionChannelStatisticVo("", total.longValue(), "全部");
        vos.add(0, all);


    }

    @Override
    public void setUserPublishCount(ComplaintUserVo riskUser, String userId, List<IntentionChannelStatisticVo> vos, BoolQueryBuilder queryBuilder) {

        if (StrUtil.isNotBlank(userId)) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", userId));
        }
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(100);
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(1000);
        channelId.subAggregation(contentId);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);
        searchQuery.addAggregation(channelId);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            log.error("es查询报错：");
            e.printStackTrace();
            return;
        }

        List<VocChannelCategory> allchannel = channelCategoryService.cacheList();
        Terms aggChannelId = pagelist.getAggregations().get("channelId");
//        riskUser.setChannel(BigDecimal.valueOf(aggChannelId.getBuckets().size()));
        for (Terms.Bucket channelids : aggChannelId.getBuckets()) {
            VocChannelCategory ve = allchannel.stream().filter(c -> channelids.getKeyAsString().equals(c.getId())).findFirst().orElse(null);
            String channelStr = "";
            if (ve != null) {
                channelStr = ve.getName();

            }

            Terms aggContentId = channelids.getAggregations().get("contentId");
            IntentionChannelStatisticVo vo = new IntentionChannelStatisticVo(channelids.getKeyAsString(), aggContentId.getBuckets().size(), channelStr);
            vos.add(vo);
        }
        BigDecimal total = vos.stream().map(IntentionChannelStatisticVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        IntentionChannelStatisticVo all = new IntentionChannelStatisticVo("", total.longValue(), "全部");
        vos.add(0, all);


    }


    @Override
    public void setUserPublishCount(UserDetailVo userdetai, String userId, List<IntentionChannelStatisticVo> vos, BoolQueryBuilder queryBuilder) {

        if (StrUtil.isNotBlank(userId)) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", userId));
        }
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(100);
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(1000);
        channelId.subAggregation(contentId);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);
        searchQuery.addAggregation(channelId);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        } catch (Exception e) {
            log.error("es查询报错：");
            e.printStackTrace();
            return;
        }

        List<VocChannelCategory> allchannel = channelCategoryService.cacheList();
        Terms aggChannelId = pagelist.getAggregations().get("channelId");
        for (Terms.Bucket channelids : aggChannelId.getBuckets()) {
            VocChannelCategory ve = allchannel.stream().filter(c -> channelids.getKeyAsString().equals(c.getId())).findFirst().orElse(null);
            if (userdetai.getIsOneId() == null) {
                VocChannelCategory isone = allchannel.stream().filter(k -> ve.getPid().equals(k.getId())).findFirst().orElse(null);
                if (isone != null && "Customer-data".equals(isone.getPid())) {
                    userdetai.setIsOneId(1);
                }
            }

            String channelStr = "";
            if (ve != null) {
                channelStr = ve.getName();
            }
            Terms aggContentId = channelids.getAggregations().get("contentId");
            IntentionChannelStatisticVo vo = new IntentionChannelStatisticVo(channelids.getKeyAsString(), aggContentId.getBuckets().size(), channelStr);
            vos.add(vo);
        }
        userdetai.setChannelCount(new BigDecimal(aggChannelId.getBuckets().size()));
        BigDecimal total = vos.stream().map(IntentionChannelStatisticVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        IntentionChannelStatisticVo all = new IntentionChannelStatisticVo("", total.longValue(), "全部");
        vos.add(0, all);


    }

    @Override
    public EsDataSentenceVocs queryByFilter(LabelDetailFilterModel model) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
        }
        TermsAggregationBuilder tagType = AggregationBuilders.terms("originalIndex").field("originalIndex.keyword").size(10000);


        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;

        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            log.error("es查询报错：");
            e.printStackTrace();
            return null;
        }
        return pagelist.get().collect(Collectors.toList()).stream().findFirst().orElse(null).getContent();
    }

    @Override
    public List<String> queryIndexByChannelFilter(LabelDetailFilterModel model) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
        }
        TermsAggregationBuilder tagType = AggregationBuilders.terms("originalIndex").field("originalIndex.keyword").size(10000);
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(10000);
        tagType.subAggregation(channelId);
        List<VocChannelCategory> allchan = channelCategoryService.cacheList();
        List<String> prosetp = allchan.stream().map(VocChannelCategory::getId).collect(Collectors.toList());

        if (model.getDataSources() != null && model.getDataSources().size() > 0 && (model.getChannelIds() == null || model.getChannelIds().size() == 0)) {
            List<String> ids = new ArrayList<>();
            try {
                ids = allchan.stream()
                        .filter(c -> allchan.stream()
                                .anyMatch(e -> model.getDataSources().contains(e.getPid()) && e.getId().equals(c.getPid())))
                        .map(VocChannelCategory::getId)
                        .collect(Collectors.toList());

            } catch (Exception e) {
                e.printStackTrace();
            }
            ids.retainAll(prosetp);
            List<String> interse = new ArrayList<>(ids);
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", interse));
        } else if (model.getChannelId() != null && model.getChannelId().equals(CommonConstant.publicSphereId) && model.getChannelIds() == null) {
            List<String> chis = new ArrayList<>();
            List<VocChannelCategory> ch2 = allchan.stream().filter(s -> s.getPid().equals(CommonConstant.publicSphereId)).collect(Collectors.toList());
            for (VocChannelCategory vocChannelCategory : ch2) {
                chis.addAll(allchan.stream().filter(k -> k.getPid().equals(vocChannelCategory.getId())).map(VocChannelCategory::getId).collect(Collectors.toList()));
            }
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", chis));

        } else if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            if (CollectionUtils.isNotEmpty(model.getChannelIds())) {
                queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
            } else if (StrUtil.isNotBlank(model.getChannelId())) {
                QueryWrapper<VocChannelCategory> wrapper = new QueryWrapper<>();
                wrapper.lambda().in(VocChannelCategory::getPid, model.getChannelId());
                wrapper.lambda().select(VocChannelCategory::getId);
                List<Object> ids = channelCategoryService.listObjs(wrapper);
                queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", ids));
            }
        } else if (StrUtil.isNotBlank(model.getChannelId())) {
            QueryWrapper<VocChannelCategory> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(VocChannelCategory::getPid, model.getChannelId());
            wrapper.lambda().select(VocChannelCategory::getId);
            List<Object> ids = channelCategoryService.listObjs(wrapper);
            List<String> chids = allchan.stream().filter(e -> ids.contains(e.getPid())).map(VocChannelCategory::getId).collect(Collectors.toList());
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", chids));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.addAggregation(tagType);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;

        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            log.error("es查询报错：");
            e.printStackTrace();
            return null;
        }
        Terms agg = pagelist.getAggregations().get("originalIndex");
        Map<String, String> orgIndex = (Map<String, String>) redisUtil.get(CacheConstant.index_channelId_map);
        List<String> indess = new ArrayList<>();
        Map<String, String> finalOrgIndex;
        if (orgIndex == null) {
            finalOrgIndex = new HashMap<>();
        } else {
            finalOrgIndex = orgIndex;
        }
        agg.getBuckets().stream().forEach(e -> {
            indess.add(e.getKeyAsString());
            Terms cu = e.getAggregations().get("channelId");
            cu.getBuckets().stream().forEach(c -> {
                finalOrgIndex.put(e.getKeyAsString(), c.getKeyAsString());
//                    finalOrgIndex.put(c.getKeyAsString(),e.getKeyAsString());
            });
        });
        redisUtil.set(CacheConstant.index_channelId_map, finalOrgIndex);
        return indess;
    }

    @Autowired
    RedisUtil redisUtil;

    @Override
    public IPage<Map> userDataTrackPus(IntentionTrackModel model) {

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (StrUtil.isNotBlank(model.getUserId())) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", model.getUserId()));
        } else {
            IPage<Map> ls = new Page<>();
            ls.setRecords(null);
            return ls;
        }
        if (StrUtil.isNotBlank(model.getChannelId())) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelId()));
        }
        if (!StrUtil.isBlankIfStr(model.getMenuName()) && "productQuality".equals(model.getMenuName())) {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        } else {
            queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        }
        if (CollectionUtils.isNotEmpty(model.getOtherTag())) {
//            queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode.keyword",model.getOtherTag()));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }
        Integer from = (model.getPageNo() - 1) * model.getPageSize();
        DateHistogramAggregationBuilder publishTime = AggregationBuilders.dateHistogram("publishTime").field("publishTime")
                .calendarInterval(DateHistogramInterval.DAY).minDocCount(1).order(BucketOrder.key(false))
                .subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", null)
                        .from(from).size(model.getPageSize()));
        TermsAggregationBuilder channelId = AggregationBuilders.terms("channelId").field("channelId.keyword").size(100);
        TermsAggregationBuilder originalIndex = AggregationBuilders.terms("originalIndex").field("originalIndex.keyword").size(100);
        TermsAggregationBuilder contentId = AggregationBuilders.terms("contentId").field("contentId.keyword").size(100).order(BucketOrder.aggregation("max_publishTime", false));
        MaxAggregationBuilder maxPublishTimeAggregation = AggregationBuilders.max("max_publishTime").field("publishTime");
        contentId.subAggregation(maxPublishTimeAggregation);
        originalIndex.subAggregation(contentId);

        channelId.subAggregation(originalIndex);

        long total = setUserDaySum(queryBuilder);


        publishTime.subAggregation(channelId);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);

        searchQuery.addAggregation(publishTime);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.printStackTrace();
        }
        ParsedDateHistogram dateHistogram = pagelist.getAggregations().get("publishTime");
        List<Map> dataTrack = new ArrayList<>();
        List<VocChannelCategory> allchannel = channelCategoryService.cacheList();
        dateHistogram.getBuckets().forEach(e -> {
            Map<String, Object> datacontent = new HashMap<>();
            datacontent.put("dataStr", e.getKeyAsString());
            Terms aggChannelId = e.getAggregations().get("channelId");
            List<Map> daycontents = new ArrayList<>();
            for (Terms.Bucket channelids : aggChannelId.getBuckets()) {
                Map chan;
                VocChannelCategory ve = allchannel.stream().filter(c -> channelids.getKeyAsString().equals(c.getId())).findFirst().orElse(null);
                String channelStr = "";
                String channelId1 = "";
                if (ve != null) {
                    channelStr = ve.getName();
                    channelId1 = channelids.getKeyAsString();
                }
                Terms originalIndex1 = channelids.getAggregations().get("originalIndex");
                List<Map> contents = new ArrayList<>();
                for (Terms.Bucket orgIndex : originalIndex1.getBuckets()) {
                    Terms contentId1 = orgIndex.getAggregations().get("contentId");
                    if (contentId1.getBuckets() != null && contentId1.getBuckets().size() > 0) {
                        TreeSet<String> contentIds = new TreeSet<>();
                        if (orgIndex.getKeyAsString().contains("voc_original_session_info") || orgIndex.getKeyAsString().contains("voc_original_call_in_out")) {//处理在线客服
                            contentId1.getBuckets().stream().forEach(l -> contentIds.add(l.getKeyAsString()));
                            for (String conid : contentIds) {
                                String index = orgIndex.getKeyAsString();
                                Map s = new HashMap();
                                s.put("contentId", conid);
                                contentTypeRelult(s, null, index, ve);
                                List<SoundContentVo> sounds = sentenceVocService.userContentSound(conid, channelids.getKeyAsString(), null, null, allchannel);
                                ContentUserDetailVo userDetailVo = new ContentUserDetailVo();
                                if (sounds != null && sounds.size() > 0) {
                                    userDetailVo.setUserId(sounds.get(0).getUserId());
                                    userDetailVo.setUserLevel(sounds.get(0).getUserLevel());
                                    userDetailVo.setUserType(sounds.get(0).getUserType());
                                }
                                s.put("sound", sounds);
                                contents.add(s);
                            }
                        } else {
                            for (Terms.Bucket contentId2 : contentId1.getBuckets()) {
                                BoolQueryBuilder qu1 = new BoolQueryBuilder();
                                contentComQueryBuilderPus(ve, qu1, contentId2.getKeyAsString());
                                NativeSearchQueryBuilder searchQuery1 = new NativeSearchQueryBuilder();
                                searchQuery1.withQuery(qu1);
                                NativeSearchQuery query1 = searchQuery1.build();
                                String index = orgIndex.getKeyAsString();
                                log.error("查询【" + index + "】文章内容：" + qu1.toString());
                                Map s = new HashMap();
                                s.put("contentId", contentId2.getKeyAsString());
                                contentTypeRelult(s, query1, index, ve);
                                List<SoundContentVo> sounds = sentenceVocService.userContentSoundpus(contentId2.getKeyAsString(), channelids.getKeyAsString(), null, index, allchannel);
                                ContentUserDetailVo userDetailVo = new ContentUserDetailVo();
                                if (sounds != null && sounds.size() > 0) {
                                    userDetailVo.setUserId(sounds.get(0).getUserId());
                                    userDetailVo.setUserLevel(sounds.get(0).getUserLevel());
                                    userDetailVo.setUserType(sounds.get(0).getUserType());
                                }
                                s.put("sound", sounds);
                                contents.add(s);
                            }

                        }

                    }


                }
                for (int i = 0; i < contents.size(); i++) {
                    chan = new HashMap();
                    if (contents.get(i).get("sound") != null) {
                        chan.put("channelStr", ((List<SoundContentVo>) contents.get(i).get("sound")).get(0).getChannelStr());
                        chan.put("channelId", ((List<SoundContentVo>) contents.get(i).get("sound")).get(0).getChannelId());

                    } else {
                        chan.put("channelStr", channelStr);
                        chan.put("channelId", channelId1);
                    }
                    chan.put("sound", contents.get(i).get("sound"));
                    chan.put("content", contents.get(i).get("content"));
                    String finalChannelId = channelId1;
                    chan.put("contentType", allchannel.stream().filter(s -> s.getId().equals(finalChannelId)).findFirst().get().getFlag());

                    daycontents.add(chan);
                }

            }
            datacontent.put("data", daycontents);
            dataTrack.add(datacontent);
        });
        IPage<Map> ls = new Page<>();
        ls.setRecords(dataTrack);
        ls.setSize(10);
        ls.setTotal(total);
        ls.setCurrent(model.getPageNo());
        return ls;
    }

    @Override
    public IPage<UserRepairDetailVo> userDataTrackRepairPus(IntentionTrackModel model) {
        return null;
    }


    private long setUserDaySum(BoolQueryBuilder queryBuilder) {
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1);
        searchQuery.withPageable(pageable);
        DateHistogramAggregationBuilder publishTime = AggregationBuilders.dateHistogram("publishTime").field("publishTime")
                .calendarInterval(DateHistogramInterval.DAY).minDocCount(1).order(BucketOrder.key(false));
        searchQuery.addAggregation(publishTime);
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.printStackTrace();
        }
        ParsedDateHistogram dateHistogram = pagelist.getAggregations().get("publishTime");
        return dateHistogram.getBuckets().size();
    }

    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;

    @Override
    public Result<?> complaintUserSentence(ComplaintUserTopModel model) {
        Pageable pageable = PageRequest.of((model.getPageNo() - 1), model.getPageSize());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        LabelDetailFilterModel models = new LabelDetailFilterModel();
        BeanUtil.copyProperties(model, models);
//        comQueryBUider(models,queryBuilder);
        DwdVocUserRisk userRisk = vocUserRiskService.getById(model.getId());
        queryBuilder.must(QueryBuilders.termsQuery("oneId.keyword", userRisk.getUserId()));
        queryBuilder.must(QueryBuilders.termsQuery("intentionType.keyword", "投诉"));
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        model.setDateUnit(CalculatorUtils.periodStrToNum(userRisk.getStatisticType()));
        model.setStartDate(DateUtil.formatDateTime(userRisk.getPublishDate()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), userRisk.getPublishDate()));
        queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);

        long total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.error("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                    try {
                        if (channelCategoryService.getById(category.getPid()).getPid().equals(CommonConstant.privateChannelId)) {
                            se.setIsOneId("1");
                        } else {
                            se.setIsOneId("0");
                        }
                    } catch (Exception e1) {
                        log.error("渠道查询失败！！！！！！！！");
                    }

                }
                se.setChannelStr(cstr);
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }

                list.add(se);
            });
        }
        IPage<SoundContentVo> repage = new Page<>();
        repage.setCurrent(model.getPageNo());
        repage.setTotal(total);
        repage.setRecords(list);
        return Result.OK(repage);
    }

    private void woOrderEsDateForm(Map content) {
        String[] dates = new String[]{"woCreatedTime", "woActualRespTime", "woActualCompTime", "woHandleTimeLastHis", "woHandleTimeCurHis", "woCarOrderDate", "woExpectCompTime", "woExpectRespTime"};
        for (String date : dates) {
            if (!StrUtil.isBlankIfStr(content.get(date))) {
                content.put(date, DateUtils.parseUTC(content.get(date) + ""));
            }
        }
    }

    private void contentTypeRelult(Map s, NativeSearchQuery query1, String index, VocChannelCategory ve) {
        if (index.contains("voc_original_session_info")) {
            String contentId = s.get("contentId").toString();
            String sessionIdByTraceId = emotionUserDiMapper.getSessionIdByTraceId(contentId);
            List<Map<String, Object>> sessions;
            Map<String, Object> sessionone;
            if (StrUtil.isNotEmpty(sessionIdByTraceId)) {
                sessions = emotionUserDiMapper.sessionListUserIdNew(sessionIdByTraceId);
                sessionone = emotionUserDiMapper.getSessionOneNew(sessionIdByTraceId);
            } else {
                log.info("兼容之前老数据");
                sessions = emotionUserDiMapper.sessionListUserId(contentId);
                sessionone = emotionUserDiMapper.getSessionOne(contentId);
            }
            Map<String, Object> sess = new HashMap<>();
            sess.put("contentBase", sessionone);
            sess.put("contentList", sessions);
            s.put("content", sess);
            return;
        } else if (index.contains("voc_original_call_in_out")) {
            String contentId = s.get("contentId").toString();
            BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            prbool.should(QueryBuilders.termsQuery("_id", contentId));
            BoolQueryBuilder nobool = new BoolQueryBuilder();
            nobool.should(QueryBuilders.termsQuery("id.keyword", contentId));
            nobool.should(QueryBuilders.termsQuery("callId.keyword", contentId));
            prbool.should(nobool);
            queryBuilder.must(prbool);

            NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
            searchQuery.withQuery(queryBuilder);
            log.info("声音-es查询：" + queryBuilder);
            NativeSearchQuery query = searchQuery.build();
            Map re;
            log.debug("内容-es查询【" + index + "】：" + queryBuilder);
            SearchHits<Map> co = null;
            try {
                co = restTemplate.search(query, Map.class, IndexCoordinates.of(index));
            } catch (NoSuchIndexException e) {
                e.getStackTrace();
                log.error("未找到索引:{}", index);
            }
            List<Map> callInOut = co.getSearchHits().stream().map(SearchHit::getContent).collect(Collectors.toList());
            Map callInOutBase = callInOut.stream().filter(e -> e.get("role") != null && "0".equals(e.get("role"))).findFirst().orElse(new HashMap<>());
            Map role1 = callInOut.stream().filter(e -> e.get("role") != null && "1".equals(e.get("role"))).findFirst().orElse(new HashMap<>());
            callInOutBase.put("sendUserNick", role1.get("customerName"));
            Map<String, Object> sess = new HashMap<>();
            sess.put("contentBase", callInOutBase);
            sess.put("contentList", callInOut);
            re = sess;
            s.put("content", re);
            return;
        }
        SearchHit<Map> co = restTemplate.searchOne(query1, Map.class, IndexCoordinates.of(index));
        if (co != null && co.getContent() != null) {
            s.put("content", co.getContent());

        } else {
            s.put("content", "未查询到详情~！");
        }


    }

    private void contentComQueryBuilderPus(VocChannelCategory ve, BoolQueryBuilder qu1, String orgIndex) {
        setQueryContentId(ve, qu1, orgIndex);
    }


    private void setQueryContentId(VocChannelCategory esindex, BoolQueryBuilder queryBuilder, String contentId) {
        queryBuilder.must(QueryBuilders.termQuery("id.keyword", contentId));
    }

    @Override
    public List<SoundContentVo> userContentSound(String contentId, String channelId, String index, String id, List<VocChannelCategory> categories) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();

        if (channelId != null && "1656923607922216961".equals(channelId)) {//处理在线客服
            queryBuilder.must(QueryBuilders.wildcardQuery("contentId.keyword", "*" + contentId));
        } else {
            queryBuilder.must(QueryBuilders.termQuery("contentId.keyword", contentId));
        }
        if (index != null) {
            queryBuilder.must(QueryBuilders.termQuery("originalIndex.keyword", index));
        }
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(CacheConstant.SYS_USER_TAG_OTHER);
        queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode", channelSet.stream().map(String::valueOf).collect(Collectors.toList())));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1000);
        searchQuery.withPageable(pageable);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        List<SoundContentVo> list = new ArrayList<>();
        pagelist.stream().collect(Collectors.toList()).forEach(e -> {
            SoundContentVo se = new SoundContentVo(e.getContent());
            VocChannelCategory category = categories.stream().filter(c -> e.getContent().getChannelId().equals(c.getId())).findFirst().orElse(null);
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
                se.setSoundType(category.getCode());
            }
            se.setChannelStr(cstr);
            se.setChannelId(e.getContent().getChannelId());
            if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));

            }

            list.add(se);
        });
        return list;
    }

    @Override
    public List<SoundContentVo> userContentSoundRepeated(String contentId, String channelId, String index, String id, List<VocChannelCategory> categories) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();

        if (channelId != null && "1656923607922216961".equals(channelId)) {//处理在线客服
            queryBuilder.must(QueryBuilders.wildcardQuery("contentId.keyword", "*" + contentId));
        } else {
            queryBuilder.must(QueryBuilders.termQuery("contentId.keyword", contentId));
        }
        if (index != null) {
            queryBuilder.must(QueryBuilders.termQuery("originalIndex.keyword", index));
        }
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(CacheConstant.SYS_USER_TAG_OTHER);
        queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode", channelSet.stream().map(String::valueOf).collect(Collectors.toList())));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1000);
        searchQuery.withPageable(pageable);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentenceRepeated));
        List<SoundContentVo> list = new ArrayList<>();
        pagelist.stream().collect(Collectors.toList()).forEach(e -> {
            SoundContentVo se = new SoundContentVo(e.getContent());
            VocChannelCategory category = categories.stream().filter(c -> e.getContent().getChannelId().equals(c.getId())).findFirst().orElse(null);
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
                se.setSoundType(category.getCode());
            }
            se.setChannelStr(cstr);
            se.setChannelId(e.getContent().getChannelId());
            if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));

            }

            list.add(se);
        });
        return list;
    }

    @Override
    public List<SoundContentVo> userContentSoundpus(String contentId, String channelId, String contentType1, String index, List<VocChannelCategory> categories) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if ("1656923607922216961".equals(channelId)) {//处理在线客服
            queryBuilder.must(QueryBuilders.wildcardQuery("contentId.keyword", "*" + contentId));
        } else {
            queryBuilder.must(QueryBuilders.termQuery("contentId.keyword", contentId));

        }
        if (index != null) {
            queryBuilder.must(QueryBuilders.termQuery("originalIndex.keyword", index));
        }
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(CacheConstant.SYS_USER_TAG_OTHER);
        queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode", channelSet.stream().map(String::valueOf).collect(Collectors.toList())));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        Pageable pageable = Pageable.ofSize(1000);
        searchQuery.withPageable(pageable);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        List<SoundContentVo> list = new ArrayList<>();
        pagelist.stream().collect(Collectors.toList()).forEach(e -> {
            SoundContentVo se = new SoundContentVo(e.getContent());
            VocChannelCategory category = categories.stream().filter(c -> e.getContent().getChannelId().equals(c.getId())).findFirst().orElse(null);
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
                se.setSoundType(category.getCode());
            }
            se.setChannelStr(cstr);
            se.setChannelId(e.getContent().getChannelId());
            if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));

            }

            list.add(se);
        });
        return list;
    }

    @Override
    public Result<?> riskInfoList(RiskEventInsightModel model) {
        Pageable pageable = PageRequest.of((model.getPageNo() - 1), model.getPageSize());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        LabelDetailFilterModel models = new LabelDetailFilterModel();
        BeanUtil.copyProperties(model, models);
        comQueryBUider(models, queryBuilder);
        queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", "负面"));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        long total = 0;

        try {
            total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常" + e.getStackTrace());
        }


        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.error("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                se.setChannelStr(cstr);
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }

                list.add(se);
            });
        }
        IPage<SoundContentVo> repage = new Page<>();
        repage.setCurrent(model.getPageNo());
        repage.setTotal(total);
        repage.setRecords(list);
        return Result.OK(repage);
    }

    @Override
    public Result<?> riskQualityInfoList(RiskEventInsightModel model) {
        Pageable pageable = PageRequest.of((model.getPageNo() - 1), model.getPageSize());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        LabelDetailFilterModel models = new LabelDetailFilterModel();
        BeanUtil.copyProperties(model, models);
        comQueryBUider(models, queryBuilder);
        queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", "负面"));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        long total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));


        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                se.setChannelStr(cstr);
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }

                list.add(se);
            });
        }
        IPage<SoundContentVo> repage = new Page<>();
        repage.setCurrent(model.getPageNo());
        repage.setTotal(total);
        repage.setRecords(list);
        return Result.OK(repage);
    }

    @Override
    public Map<String, String> getEarlyReleaseRiskPointAgg(RiskEventInsightModel model) {

        Pageable pageable = PageRequest.of(0, 1);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", "负面"));
        queryBuilder.must(QueryBuilders.termsQuery("topicCode.keyword", model.getTopicCode()));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.ASC);


        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                }
                se.setChannelStr(cstr);

                list.add(se);
            });
        }
        Map<String, String> erar = new HashMap<>();
        if (list != null && list.size() > 0) {
            erar.put("channelStr", list.get(0).getChannelStr());
            erar.put("earlyDate", list.get(0).getPublishTime());
        } else {
            erar.put("channelStr", "");
            erar.put("earlyDate", "");
        }

        return erar;
    }

    @Override
    public Result<?> dataAnalysisTimeNode(RiskEventInsightModel model, DwdVocRisk risk) {
        Pageable pageable = PageRequest.of(model.getPageNo() - 1, model.getPageSize());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        LabelDetailFilterModel models = new LabelDetailFilterModel();
        BeanUtil.copyProperties(model, models);
        models.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");
        comQueryBUider(models, queryBuilder);
        queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", "负面"));

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.error("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                se.setChannelStr(cstr);
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }

                list.add(se);
            });
        }
        Map<String, List<SoundContentVo>> grous = list.stream().collect(Collectors.groupingBy(o -> DateUtils.StringToString(o.getPublishTime(), DateStyle.YYYY_MM_DD_EN)));
        List<Map> ens = new ArrayList<>();
        for (Map.Entry<String, List<SoundContentVo>> datestr : grous.entrySet()) {
            Map<String, Object> obd = new HashMap<>();
            obd.put("dateStr", datestr.getKey());
            obd.put("data", datestr.getValue());
            ens.add(obd);
        }
        IPage<Map> repage = new Page<>();
        repage.setCurrent(model.getPageNo());
        repage.setSize(model.getPageSize());
        repage.setTotal(pagelist.getTotalHits());
        repage.setRecords(ens);
        return Result.OK(repage);
    }

    @Override
    public Result<?> dataAnalysisTimeNodeQuality(RiskEventInsightModel model, DwdVocQualityRisk risk) {
        Pageable pageable = PageRequest.of(model.getPageNo() - 1, model.getPageSize());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        LabelDetailFilterModel models = new LabelDetailFilterModel();
        BeanUtil.copyProperties(model, models);
        models.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");
        comQueryBUider(models, queryBuilder);

        queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", "负面"));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));

        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                se.setChannelStr(cstr);
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }

                list.add(se);
            });
        }
        Map<String, List<SoundContentVo>> grous = list.stream().collect(Collectors.groupingBy(o -> DateUtils.StringToString(o.getPublishTime(), DateStyle.YYYY_MM_DD_EN)));
        List<Map> ens = new ArrayList<>();
        for (Map.Entry<String, List<SoundContentVo>> datestr : grous.entrySet()) {
            Map<String, Object> obd = new HashMap<>();
            obd.put("dateStr", datestr.getKey());
            obd.put("data", datestr.getValue());
            ens.add(obd);
        }
        IPage<Map> repage = new Page<>();
        repage.setCurrent(model.getPageNo());
        repage.setSize(model.getPageSize());
        repage.setTotal(pagelist.getTotalHits());
        repage.setRecords(ens);
        return Result.OK(repage);
    }

    @Override
    public EsDataSentenceVocs getId(String id) {
        return restTemplate.get(id, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
    }

    @Override
    public Map<String, String> getContentByIdIndex(String id, String index) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);

//        return restTemplate.searchOne(searchQuery,Map.class,IndexCoordinates.of(index));
        return null;
    }


    @Override
    public EsDataSentenceVocs getIdRepeated(String id) {
        return restTemplate.get(id, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentenceRepeated));
    }

    @Autowired
    IVocChannelCategoryService channelCategoryService;

    @Override
    public Result<?> focusSoundContent(FilterCriteriaModel model, Page<SoundContentVo> page) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of((current - 1), size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        comQueryBUider(model, queryBuilder);
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));

        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        System.out.println("es查询：" + queryBuilder.toString());
        long total = 0;
        try {
            total = restTemplate.count(searchQuery.build(), EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("es服务异常：" + e.getStackTrace());
        }
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        SearchHits<EsDataSentenceVocs> pagelist = null;
        try {
            pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        } catch (Exception e) {
            e.fillInStackTrace();
        }

        List<SoundContentVo> list = new ArrayList<>();
        if (pagelist != null && !pagelist.isEmpty()) {
            pagelist.stream().collect(Collectors.toList()).forEach(e -> {
                SoundContentVo se = new SoundContentVo(e.getContent());
                VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
                String cstr = "";
                if (category != null) {
                    cstr = category.getName();
                    se.setSoundType(category.getCode());
                }
                se.setChannelStr(cstr);
                if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                    se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
                }

                list.add(se);
            });
        }

        IPage<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        page.setSize(size);
        repage.setTotal(total);
        repage.setRecords(list);
        return Result.OK(repage);
    }

    @Override
    public Result<?> productQualitySoundContent(ProductQualityFilterCriteriaModel model, Page<SoundContentVo> page) {
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        Pageable pageable = PageRequest.of(current - 1, size);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        comQualitQueryBUider(model, queryBuilder);
        queryBuilder.must(QueryBuilders.termsQuery("tagType", "2"));
        FieldSortBuilder sort = SortBuilders.fieldSort("publishTime").order(SortOrder.DESC);
        if (StrUtil.isNotBlank(model.getOrder())) {
            sort = SortBuilders.fieldSort("publishTime").order(SortOrder.fromString(model.getOrder()));
        }
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder();
        searchQuery.withQuery(queryBuilder);
        searchQuery.withPageable(pageable);
        searchQuery.withSort(sort);
        NativeSearchQuery query = searchQuery.build();
        log.debug("es查询：" + queryBuilder.toString());
        SearchHits<EsDataSentenceVocs> pagelist = restTemplate.search(query, EsDataSentenceVocs.class, IndexCoordinates.of(vocSentence));
        List<SoundContentVo> list = new ArrayList<>();
        pagelist.stream().collect(Collectors.toList()).forEach(e -> {
            SoundContentVo se = new SoundContentVo(e.getContent());
            VocChannelCategory category = channelCategoryService.getById(e.getContent().getChannelId());
            String cstr = "";
            if (category != null) {
                cstr = category.getName();
                se.setSoundType(category.getCode());
            }
            se.setChannelStr(cstr);
            if (StrUtil.isNotBlank(e.getContent().getProvince())) {
                se.setProvince(sysDictService.queryDictTextByKey("province", e.getContent().getProvince()));
            }

            list.add(se);
        });
        IPage<SoundContentVo> repage = new Page<>();
        repage.setCurrent(current);
        page.setSize(size);
        repage.setTotal(pagelist.getTotalHits());
        repage.setRecords(list);
        return Result.OK(repage);
    }

    private BoolQueryBuilder comQueryBUider(IntentionTrackModel model, BoolQueryBuilder queryBuilder) {
        if (StrUtil.isNotBlank(model.getChannelId())) {
            queryBuilder.must(QueryBuilders.termQuery("channelId.keyword", model.getChannelId()));
        }
        if (StrUtil.isNotBlank(model.getUserId())) {
            queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", model.getUserId()));
        }
        if (StrUtil.isNotBlank(model.getIntention())) {
            queryBuilder.must(QueryBuilders.termQuery("intentionType.keyword", model.getIntention()));
        }
        if (StrUtil.isNotBlank(model.getEmotion())) {
            queryBuilder.must(QueryBuilders.termQuery("dimensionEmotion.keyword", model.getEmotion()));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }


        return queryBuilder;
    }


    /**
     * 构建基础查询条件
     */
    private BoolQueryBuilder buildBaseQueryBuilder(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        // DLR相关查询
        if (StrUtil.isNotBlank(model.getDlrCode())) {
            queryBuilder.must(QueryBuilders.termQuery("dlrCode.keyword", model.getDlrCode()));
        }
        if (StrUtil.isNotBlank(model.getDlrName())) {
            queryBuilder.must(QueryBuilders.termQuery("dlrName.keyword", model.getDlrName()));
        }

        // 用户类型查询
        if (CollectionUtils.isNotEmpty(model.getUserTypes())) {
            queryBuilder.must(QueryBuilders.termsQuery("userType.keyword", model.getUserTypes()));
        }

        // 维度相关查询
        if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("firstDimensionCode.keyword", model.getFirstDimensionCode()));
        }
        if (CollUtil.isNotEmpty(model.getFirstDimensionCodes())) {
            queryBuilder.must(QueryBuilders.termsQuery("firstDimensionCode.keyword", model.getFirstDimensionCodes()));
        }
        if (StrUtil.isNotBlank(model.getEmotionKeyword())) {
            queryBuilder.must(QueryBuilders.termQuery("emotionKeyword.keyword", model.getEmotionKeyword()));
        }

        // 情感相关查询
        if (StrUtil.isNotBlank(model.getEmotion())) {
            if (model.getEmotion().contains(",")) {
                List<String> list = Arrays.asList(model.getEmotion().split(","));
                queryBuilder.must(QueryBuilders.termsQuery("dimensionEmotion.keyword", list));
            } else {
                queryBuilder.must(QueryBuilders.termQuery("dimensionEmotion.keyword", model.getEmotion()));
            }
        }

        // 意图查询
        if (StrUtil.isNotBlank(model.getIntention())) {
            queryBuilder.must(QueryBuilders.termQuery("intentionType.keyword", model.getIntention()));
        }

        // 时间范围查询
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate())
                    .lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }

        // OneId相关查询
        if (StrUtil.isNotBlank(model.getIsOneId())) {
            if ("1".equals(model.getIsOneId())) {
                queryBuilder.must(QueryBuilders.termsQuery("isOneId", model.getIsOneId()));
            } else {
                queryBuilder.mustNot(QueryBuilders.existsQuery("isOneId"));
            }
        }

        // 区域查询
        if (StrUtil.isNotBlank(model.getRegion()) && "0000000".equals(model.getRegion())) {
            queryBuilder.mustNot(QueryBuilders.existsQuery("province.keyword"));
        }

        // 其他标签查询
        if (CollectionUtils.isNotEmpty(model.getOtherTag())) {
            // queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode.keyword",model.getOtherTag()));
        }

        // ID查询
        if (model.getIds() != null && model.getIds().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("_id", model.getIds()));
        }

        // 品牌相关查询
        if (StrUtil.isNotBlank(model.getBrand())) {
            queryBuilder.must(QueryBuilders.termsQuery("brandCode.keyword", model.getBrand()));
        }
        if (!StrUtil.isBlankIfStr(model.getTagType())) {
            queryBuilder.must(QueryBuilders.termQuery("tagType.keyword", model.getTagType()));
        }
        if (StrUtil.isNotBlank(model.getBrandCode())) {
            queryBuilder.must(QueryBuilders.termsQuery("brandCode.keyword", model.getBrandCode()));
        }

        return queryBuilder;
    }

    /**
     * 构建无权限查询条件
     */
    private BoolQueryBuilder comNotPermissionQueryBUider(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        // 调用基础查询构建器
        queryBuilder = buildBaseQueryBuilder(model, queryBuilder);

        // 创建时间查询
        if (StrUtil.isNotBlank(model.getCreateDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("createTime").lte(model.getCreateDate()));
        }

        // Topic相关查询
        if (StrUtil.isNotBlank(model.getTopic())) {
            queryBuilder.must(QueryBuilders.termQuery("topic.keyword", model.getTopic()));
        } else if (StrUtil.isNotBlank(model.getTopicCode())) {
            queryBuilder.must(QueryBuilders.termQuery("topicCode.keyword", model.getTopicCode()));
        } else if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("threeDimensionCode.keyword", model.getThirdDimensionCode()));
        } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCode()));
        } else if ((model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0)) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCodes()));
        }

        // 区域查询
        if (StrUtil.isNotBlank(model.getRegion())) {
            List<String> provinces = new ArrayList<>();
            provinces.addAll(provinceAreaService.queryProvinceByAreaCode(model.getRegion(), model.getBrandCode())
                    .stream().map(DictVo::getValue).collect(Collectors.toList()));
            queryBuilder.must(QueryBuilders.termsQuery("province.keyword", provinces));
        }
        if (model.getProvince() != null && model.getProvince().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("province.keyword", model.getProvince()));
        }

        List<VocBrandRegion> brandRegionList = null;

        if (CollectionUtils.isNotEmpty(model.getDealerRegion2s())) {
            brandRegionList = brandRegionService.cacheBrandRegionAll();
            Set<String> idSet = new HashSet<>(model.getDealerRegion2s());
            List<VocBrandRegion> dealerRegion2s = brandRegionList.stream().filter(b -> idSet.contains(b.getCommunityCode())).collect(Collectors.toList());
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (String s : dealerRegion2s.stream().filter(Objects::nonNull).map(VocBrandRegion::getProvinceCode).collect(Collectors.toSet())) {
                prbool.should(QueryBuilders.termsQuery("province.keyword", s));
            }
            queryBuilder.filter(prbool);
        } else if (CollectionUtils.isNotEmpty(model.getDealerRegion1s())) {
            brandRegionList = brandRegionService.cacheBrandRegionAll();

            Set<String> idSet = new HashSet<>(model.getDealerRegion1s());
            List<VocBrandRegion> dealerRegion2s = brandRegionList.stream().filter(b -> idSet.contains(b.getRegionalCode())).collect(Collectors.toList());
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (String s : dealerRegion2s.stream().filter(Objects::nonNull).map(VocBrandRegion::getProvinceCode).collect(Collectors.toSet())) {
                prbool.should(QueryBuilders.termsQuery("province.keyword", s));
            }
            queryBuilder.filter(prbool);
        } else if (StrUtil.isNotEmpty(model.getRegionType())) {
            brandRegionList = brandRegionService.cacheBrandRegionAll();

            List<VocBrandRegion> dealerRegion2s = brandRegionList.stream().filter(b -> (model.getBrandCode().equals(b.getBrand()) && model.getRegionType().equals(b.getApplicationType()))).collect(Collectors.toList());
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (String s : dealerRegion2s.stream().filter(Objects::nonNull).map(VocBrandRegion::getProvinceCode).collect(Collectors.toSet())) {
                prbool.should(QueryBuilders.termsQuery("province.keyword", s));
            }
            queryBuilder.filter(prbool);
        }


        // 车系查询
        if (model.getCarSeries() != null && model.getCarSeries().size() > 0) {
            List<String> carSeries = model.getCarSeries().stream().collect(Collectors.toList());
            queryBuilder.must(QueryBuilders.termsQuery("carSeriesCode.keyword", carSeries));
        }

        // 渠道查询

        extractedChannelQuery(model, queryBuilder);
        // 用户ID查询
        if (model.getUserIds() != null && model.getUserIds().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("oneId.keyword", model.getUserIds()));
        }

        // 特殊时间查询
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            if (StrUtil.isNotBlank(model.getTag()) && "去重声音".equals(model.getTag())) {
                queryBuilder.must(QueryBuilders.rangeQuery("publishTime.keyword")
                        .gt(model.getStartDate()).lte(model.getEndDate())
                        .includeLower(true).includeUpper(true));
            }
        }

        // 句子查询
        if (StrUtil.isNotBlank(model.getSentence())) {
            queryBuilder.must(QueryBuilders.wildcardQuery("sentence.keyword", "*" + model.getSentence() + "*"));
        }

        return queryBuilder;
    }

    private void extractedChannelQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        List<VocChannelCategory> allchan = channelCategoryService.cacheList();
        List<String> prosetp = allchan.stream().map(VocChannelCategory::getId).collect(Collectors.toList());

        if (model.getDataSources() != null && model.getDataSources().size() > 0 && (model.getChannelIds() == null || model.getChannelIds().size() == 0)) {
            List<String> ids = new ArrayList<>();
            try {
                ids = allchan.stream()
                        .filter(c -> allchan.stream()
                                .anyMatch(e -> model.getDataSources().contains(e.getPid()) && e.getId().equals(c.getPid())))
                        .map(VocChannelCategory::getId)
                        .collect(Collectors.toList());

            } catch (Exception e) {
                e.printStackTrace();
            }
            ids.retainAll(prosetp);
            List<String> interse = new ArrayList<>(ids);
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", interse));
        } else if (model.getChannelId() != null && model.getChannelId().equals(CommonConstant.publicSphereId) && model.getChannelIds() == null) {
            List<String> chis = new ArrayList<>();
            List<VocChannelCategory> ch2 = allchan.stream().filter(s -> s.getPid().equals(CommonConstant.publicSphereId)).collect(Collectors.toList());
            for (VocChannelCategory vocChannelCategory : ch2) {
                chis.addAll(allchan.stream().filter(k -> k.getPid().equals(vocChannelCategory.getId())).map(VocChannelCategory::getId).collect(Collectors.toList()));
            }
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", chis));

        } else if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            if (CollectionUtils.isNotEmpty(model.getChannelIds())) {
                queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
            } else if (StrUtil.isNotBlank(model.getChannelId())) {
                QueryWrapper<VocChannelCategory> wrapper = new QueryWrapper<>();
                wrapper.lambda().in(VocChannelCategory::getPid, model.getChannelId());
                wrapper.lambda().select(VocChannelCategory::getId);
                List<Object> ids = channelCategoryService.listObjs(wrapper);
                queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", ids));
            }
        } else if (StrUtil.isNotBlank(model.getChannelId())) {
            QueryWrapper<VocChannelCategory> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(VocChannelCategory::getPid, model.getChannelId());
            wrapper.lambda().select(VocChannelCategory::getId);
            List<Object> ids = channelCategoryService.listObjs(wrapper);
            List<String> chids = allchan.stream().filter(e -> ids.contains(e.getPid())).map(VocChannelCategory::getId).collect(Collectors.toList());
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", chids));
        } else {
            //无权限不走渠道过滤
//                queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword",prosetp));
        }
    }

    /**
     * 构建带权限的查询条件
     */
    private BoolQueryBuilder comQueryBUider(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        // 调用基础查询构建器
        queryBuilder = buildBaseQueryBuilder(model, queryBuilder);

        // Topic相关查询
        if (StrUtil.isNotBlank(model.getTopic())) {
            List<String> topicCodes = faultProblemService.queryTopicCodesByTopicName(model.getTopic());
            queryBuilder.must(QueryBuilders.termsQuery("topicCode.keyword", topicCodes));
            model.setTopicCode(null);
        }

        // 维度代码查询
        buildDimensionQuery(model, queryBuilder);

        // 工单类型查询
        if (StrUtil.isNotBlank(model.getWoType())) {
            queryBuilder.must(QueryBuilders.termQuery("woType.keyword", model.getWoType()));
        }
        if (CollectionUtil.isNotEmpty(model.getWoTypeList())) {
            queryBuilder.must(QueryBuilders.termsQuery("woType.keyword", model.getWoTypeList()));
        }
        if (StrUtil.isNotBlank(model.getWoFlow())) {
            queryBuilder.must(QueryBuilders.termQuery("needFlow.keyword", model.getWoFlow()));
        }

        // 非风险查询逻辑
        if (!model.isRiskPire()) {
            buildNonRiskQuery(model, queryBuilder);
        }

        return queryBuilder;
    }


    /**
     * 构建维度查询条件
     */
    private void buildDimensionQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        if (StrUtil.isNotBlank(model.getTopicCode())) {
            queryBuilder.must(QueryBuilders.termQuery("topicCode.keyword", model.getTopicCode()));
        } else if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("threeDimensionCode.keyword", model.getThirdDimensionCode()));
        } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCode()));
        } else if ((model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0)
                && !model.isRiskPire()) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCodes()));
        }

        if (model.getSecondDimensionCodesPowers() != null && model.getSecondDimensionCodesPowers().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCodesPowers()));
        }
    }

    /**
     * 构建非风险查询条件
     */
    private void buildNonRiskQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        // 处理省份查询
        if (model.getProvince() != null && model.getProvince().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("province.keyword", model.getProvince()));
        } else {
            buildProvincePermissionQuery(model, queryBuilder);
        }

        // 处理车系权限
        buildCarSeriesQuery(model, queryBuilder);

        // 处理渠道权限
        buildChannelPermissionQuery(model, queryBuilder);
    }

    /**
     * 构建省份权限查询条件
     */
    private void buildProvincePermissionQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        BoolQueryBuilder prbool = new BoolQueryBuilder();
        if (model.getProvinceCodesPowers() != null && model.getProvinceCodesPowers().size() > 0) {
            prbool.should(QueryBuilders.termsQuery("province.keyword", model.getProvinceCodesPowers()));
            BoolQueryBuilder nobool = new BoolQueryBuilder();
            nobool.mustNot(QueryBuilders.existsQuery("province"));
            prbool.should(nobool);
            queryBuilder.must(prbool);
        }
    }

    /**
     * 构建车系查询条件
     */
    private void buildCarSeriesQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        if (model.getCarSeriesPowers() != null && model.getCarSeriesPowers().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("carSeriesCode.keyword", model.getCarSeriesPowers()));
        }
        if (model.getCarSeries() != null && model.getCarSeries().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("carSeriesCode.keyword", model.getCarSeries()));
        }
    }

    /**
     * 构建渠道权限查询条件
     */
    private void buildChannelPermissionQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        List<VocChannelCategory> allchan = channelCategoryService.cacheList();
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_CHANNEL,
                model.getBrandCode(), getUserId()));
        List<String> prosetp = channelSet.stream().map(String::valueOf).collect(Collectors.toList());

        if (model.getDataSources() != null && model.getDataSources().size() > 0
                && (model.getChannelIds() == null || model.getChannelIds().size() == 0)) {
            buildDataSourceQuery(model, queryBuilder, allchan, prosetp);
        } else if (model.getChannelId() != null && model.getChannelId().equals(CommonConstant.publicSphereId)
                && model.getChannelIds() == null) {
            buildPublicSphereQuery(model, queryBuilder, allchan);
        } else if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            buildChannelIdsQuery(model, queryBuilder);
        } else if (StrUtil.isNotBlank(model.getChannelId())) {
            buildSingleChannelQuery(model, queryBuilder, allchan, prosetp);
        } else {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", prosetp));
        }
    }

    /**
     * 构建数据源查询条件
     */
    private void buildDataSourceQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder,
                                      List<VocChannelCategory> allchan, List<String> prosetp) {
        List<String> ids = new ArrayList<>();
        try {
            ids = allchan.stream()
                    .filter(c -> allchan.stream()
                            .anyMatch(e -> model.getDataSources().contains(e.getPid()) && e.getId().equals(c.getPid())))
                    .map(VocChannelCategory::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        ids.retainAll(prosetp);
        List<String> interse = new ArrayList<>(ids);
        queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", interse));
    }

    /**
     * 构建公共领域查询条件
     */
    private void buildPublicSphereQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder,
                                        List<VocChannelCategory> allchan) {
        List<String> chis = new ArrayList<>();
        List<VocChannelCategory> ch2 = allchan.stream()
                .filter(s -> s.getPid().equals(CommonConstant.publicSphereId))
                .collect(Collectors.toList());
        for (VocChannelCategory vocChannelCategory : ch2) {
            chis.addAll(allchan.stream()
                    .filter(k -> k.getPid().equals(vocChannelCategory.getId()))
                    .map(VocChannelCategory::getId)
                    .collect(Collectors.toList()));
        }
        queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", chis));
    }

    /**
     * 构建渠道ID列表查询条件
     */
    private void buildChannelIdsQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder) {
        if (CollectionUtils.isNotEmpty(model.getChannelIds())) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
        } else if (StrUtil.isNotBlank(model.getChannelId())) {
            QueryWrapper<VocChannelCategory> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(VocChannelCategory::getPid, model.getChannelId());
            wrapper.lambda().select(VocChannelCategory::getId);
            List<Object> ids = channelCategoryService.listObjs(wrapper);
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", ids));
        }
    }

    /**
     * 构建单个渠道查询条件
     */
    private void buildSingleChannelQuery(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder,
                                         List<VocChannelCategory> allchan, List<String> prosetp) {
        QueryWrapper<VocChannelCategory> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(VocChannelCategory::getPid, model.getChannelId());
        wrapper.lambda().select(VocChannelCategory::getId);
        List<Object> ids = channelCategoryService.listObjs(wrapper);
        List<String> chids = allchan.stream()
                .filter(e -> ids.contains(e.getPid()))
                .map(VocChannelCategory::getId)
                .collect(Collectors.toList());
        queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword",
                CollectionUtils.intersection(chids, prosetp)));
    }


    public String getUserId() {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            return loginUser.getId();
        } catch (Exception e) {
            log.info("用户ID：{}", SpringContextUtils.getUserId());
            return SpringContextUtils.getUserId();
        }
    }

    private BoolQueryBuilder comSourceQueryBuider(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder, Map<String, Map<String, Map<String, String>>> fields, Set<String> createTimes, List<VocBrandRegion> brandRegionList) {
        brandRegionList = brandRegionService.cacheBrandRegionAll();
        if (!StrUtil.isBlankIfStr(model.getTag())) {
            queryBuilder.must(QueryBuilders.termQuery("tag.keyword", model.getTag()));
        }
        Collection<Map<String, Map<String, String>>> val = fields.values();

        Set<String> provinces = new HashSet<>();
        Set<String> modelNames = new HashSet<>();
        Set<String> brandNames = new HashSet<>();


        for (Map<String, Map<String, String>> stringMapMap : val) {
            String fis = stringMapMap.get("keyValue").get("customerProvince");
            String mon = stringMapMap.get("keyValue").get("modelName");
            String bn = stringMapMap.get("keyValue").get("brandName");
            String crt = stringMapMap.get("keyValue").get("createTime");
            if (StrUtil.isNotBlank(fis)) {
                provinces.add(fis);
            } else {
                provinces.add("customerProvince");
            }
            if (StrUtil.isNotBlank(mon)) {
                modelNames.add(mon);
            } else {
                modelNames.add("modelName");
            }
            if (StrUtil.isNotBlank(bn)) {
                brandNames.add(bn);
            } else {
                brandNames.add("brandName");
            }
            if (StrUtil.isNotBlank(crt)) {
                createTimes.add(crt);
            } else {
                createTimes.add("createTime");
            }
        }

        if (CollectionUtils.isNotEmpty(model.getDealerRegion2s())) {
            Set<String> idSet = new HashSet<>(model.getDealerRegion2s());
            List<VocBrandRegion> dealerRegion2s = brandRegionList.stream().filter(b -> idSet.contains(b.getCommunityCode())).collect(Collectors.toList());
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (String s : dealerRegion2s.stream().filter(Objects::nonNull).map(VocBrandRegion::getProvinceName).collect(Collectors.toSet())) {
                for (String pro : provinces) {
                    prbool.should(QueryBuilders.wildcardQuery(pro + ".keyword", "*" + s + "*"));
                }
            }
            queryBuilder.filter(prbool);
        } else if (CollectionUtils.isNotEmpty(model.getDealerRegion1s())) {
            Set<String> idSet = new HashSet<>(model.getDealerRegion1s());
            List<VocBrandRegion> dealerRegion2s = brandRegionList.stream().filter(b -> idSet.contains(b.getRegionalCode())).collect(Collectors.toList());
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (String s : dealerRegion2s.stream().filter(Objects::nonNull).map(VocBrandRegion::getProvinceName).collect(Collectors.toSet())) {
                for (String pro : provinces) {
                    prbool.should(QueryBuilders.wildcardQuery(pro + ".keyword", "*" + s + "*"));
                }
            }
            queryBuilder.filter(prbool);
        } else if (StrUtil.isNotEmpty(model.getRegionType())) {
            List<VocBrandRegion> dealerRegion2s = brandRegionList.stream().filter(b -> (model.getBrandCode().equals(b.getBrand()) && model.getRegionType().equals(b.getApplicationType()))).collect(Collectors.toList());
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (String s : dealerRegion2s.stream().filter(Objects::nonNull).map(VocBrandRegion::getProvinceName).collect(Collectors.toSet())) {
                for (String pro : provinces) {
                    prbool.should(QueryBuilders.wildcardQuery(pro + ".keyword", "*" + s + "*"));
                }
            }
            queryBuilder.filter(prbool);
        }


        if (model.getCarSeries() != null && model.getCarSeries().size() > 0) {

            QueryWrapper<BrandProductManager> wrapper = new QueryWrapper<>();
            wrapper.lambda().select(BrandProductManager::getName, BrandProductManager::getAlias).in(BrandProductManager::getCode, model.getCarSeries());
            List<BrandProductManager> car = brandProductManagerService.list(wrapper);
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (BrandProductManager carn : car) {
                for (String modelName : modelNames) {
                    if (modelName != null && modelName != "null" && modelName != "") {
                        prbool.should(QueryBuilders.wildcardQuery(modelName + ".keyword", "*" + carn.getName() + "*"));
                        prbool.should(QueryBuilders.wildcardQuery("carSeries.keyword", "*" + carn.getName() + "*"));
                    }
                }
                for (String ca : Arrays.stream(carn.getAlias().split("\\\\")).collect(Collectors.toList())) {
                    for (String modelNamea : modelNames) {
                        if (modelNamea != null && modelNamea != "null" && modelNamea != "") {
                            prbool.should(QueryBuilders.wildcardQuery(modelNamea + ".keyword", "*" + ca + "*"));
                            prbool.should(QueryBuilders.wildcardQuery("carSeries.keyword", "*" + carn.getName() + "*"));
                        }
                    }
                }
            }
            if ((StrUtil.isNotBlank(model.getCarType()) && "all-vehicle-series".equals(model.getCarType())
                    && model.getCarSeries() != null && model.getCarSeries().size() > 1)) {
            } else {
                queryBuilder.filter(prbool);
            }
        }
        if (StrUtil.isNotBlank(model.getBrand())) {

            QueryWrapper<BrandProductManager> wrapper = new QueryWrapper<>();
            wrapper.lambda().select(BrandProductManager::getName, BrandProductManager::getAlias).in(BrandProductManager::getCode, model.getBrand());
            List<BrandProductManager> car = brandProductManagerService.list(wrapper);
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            for (BrandProductManager carn : car) {
                for (String brandName : brandNames) {
                    prbool.should(QueryBuilders.wildcardQuery(brandName + ".keyword", "*" + carn.getName() + "*"));

                }
                for (String ca : Arrays.stream(carn.getAlias().split("\\\\")).collect(Collectors.toList())) {
                    for (String brandName : brandNames) {
                        prbool.should(QueryBuilders.wildcardQuery(brandName + ".keyword", "*" + ca + "*"));
                    }
                }
            }
            queryBuilder.filter(prbool);
        }

        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            BoolQueryBuilder prbool = new BoolQueryBuilder();

            for (String createTime : createTimes) {
                prbool.should(QueryBuilders.rangeQuery(createTime + ".keyword").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
                prbool.should(QueryBuilders.rangeQuery(createTime).gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));

            }
            prbool.should(QueryBuilders.rangeQuery("createTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
            queryBuilder.must(prbool);
        }
        if (model.getIds() != null && model.getIds().size() > 0) {
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            prbool.should(QueryBuilders.termsQuery("_id", model.getIds()));
            prbool.should(QueryBuilders.termsQuery("sessionId.keyword", model.getIds()));
            prbool.should(QueryBuilders.termsQuery("callId.keyword", model.getIds()));
            queryBuilder.must(prbool);
        }
        if (StrUtil.isNotBlank(model.getSourceId())) {
            BoolQueryBuilder prbool = new BoolQueryBuilder();
            prbool.should(QueryBuilders.termsQuery("_id", model.getSourceId()));
            prbool.should(QueryBuilders.termsQuery("sessionId.keyword", model.getSourceId()));
            prbool.should(QueryBuilders.termsQuery("callId.keyword", model.getSourceId()));
            prbool.should(QueryBuilders.termsQuery("id.keyword", model.getSourceId()));
            queryBuilder.must(prbool);
        }
        if (CollectionUtil.isNotEmpty(model.getChannelIds()) && !model.getChannelIds().contains("1372001238136589754")) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
        } else if (CollectionUtil.isNotEmpty(model.getDataSources())) {
            queryBuilder.must(QueryBuilders.termsQuery("vocChannel1.keyword", model.getDataSources()));
        }

        queryBuilder.must(QueryBuilders.existsQuery("tag.keyword"));
        return queryBuilder;
    }


    private BoolQueryBuilder comQueryBUider(FilterCriteriaModel model, BoolQueryBuilder queryBuilder) {
        if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeUpper(true).includeLower(true));
        }
        if (StrUtil.isNotBlank(model.getIntention())) {
            queryBuilder.must(QueryBuilders.termQuery("intentionType.keyword", model.getIntention()));
        }
        if (StrUtil.isNotBlank(model.getEmotion())) {
            queryBuilder.must(QueryBuilders.termQuery("dimensionEmotion.keyword", model.getEmotion()));
        }
        if (StrUtil.isNotBlank(model.getProblemLevel())) {
            queryBuilder.must(QueryBuilders.termQuery("level.keyword", model.getProblemLevel()));
        }

        if ((model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0)) {
            queryBuilder.must(QueryBuilders.termsQuery("secondDimensionCode.keyword", model.getSecondDimensionCodes()));
        }
        if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("firstDimensionCode.keyword", model.getFirstDimensionCode()));
        }
        if (StrUtil.isNotBlank(model.getRegion())) {
            if ("0000000".equals(model.getRegion())) {
                queryBuilder.mustNot(QueryBuilders.existsQuery("province.keyword"));

            } else {
                queryBuilder.must(QueryBuilders.termQuery("areaCode.keyword", model.getRegion()));

            }
        }
        if (model.getProvince() != null && model.getProvince().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("province.keyword", model.getProvince()));
        }


        return queryBuilder;
    }

    private BoolQueryBuilder comQualitQueryBUider(ProductQualityFilterCriteriaModel model, BoolQueryBuilder queryBuilder) {
        if (model.getChannelIds() != null && model.getChannelIds().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("channelId.keyword", model.getChannelIds()));
        }
        if (model.getProvince() != null && model.getProvince().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("province.keyword", model.getProvince()));
        }
        if (model.getCarSeries() != null && model.getCarSeries().size() > 0) {
            queryBuilder.must(QueryBuilders.termsQuery("carSeriesCode.keyword", model.getCarSeries()));
        }
        if (StrUtil.isNotBlank(model.getStartDate()) && StrUtil.isNotBlank(model.getEndDate())) {
            queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(model.getStartDate()).lte(model.getEndDate()).includeLower(true).includeUpper(true));
        }
        if (StrUtil.isNotBlank(model.getIntention())) {
            queryBuilder.must(QueryBuilders.termQuery("intentionType.keyword", model.getIntention()));
        }
        if (StrUtil.isNotBlank(model.getEmotion())) {
            queryBuilder.must(QueryBuilders.termQuery("dimensionEmotion.keyword", model.getEmotion()));
        }
        if (StrUtil.isNotBlank(model.getProblemLevel())) {
            queryBuilder.must(QueryBuilders.termQuery("level.keyword", model.getProblemLevel()));
        }
        if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("secondDimensionCode.keyword", model.getSecondDimensionCode()));
        }
        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            queryBuilder.must(QueryBuilders.termQuery("threeDimensionCode.keyword", model.getThirdDimensionCode()));
        }
        return queryBuilder;
    }
}
