package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocEmotionUserDi;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.mapper.*;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocDealerRiskService;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.IDwdVocRiskService;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.serivce.wo.WoBaseOriginalDataService;
import com.car.stats.vo.*;
import com.car.stats.vo.risk.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.*;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocChannelCategoryService;
import com.car.voc.service.IVocRiskWarningRulesService;
import com.car.voc.vo.ProportionAreaVo;
import com.car.voc.vo.RiskMailDataVo;
import com.car.voc.vo.risk.RiskRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class DwdVocDealerRiskServiceImpl extends ServiceImpl<DwdVocDealerRiskMapper, DwdVocDealerRisk> implements IDwdVocDealerRiskService{

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    IVocRiskWarningRulesService vocRiskWarningRulesService;
    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;
    @Autowired
    WoBaseOriginalDataService originalDataService;
    String riskType = "网点风险预警";
    @Override
    public IPage<RiskPointAggVo> riskPointAggNew(RiskEventInsightModel model) {
        Page<RiskPointAggVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        final String token =  StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);

        VocRiskWarningRules warningRules = vocRiskWarningRulesService.getByBrandCode(model.getBrandCode(), riskType);
        RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(warningRules.getId());

        if (ObjectUtils.isNotEmpty(vo)) {
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumD()) && !vo.getRiskWordsNumD().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumW()) && !vo.getRiskWordsNumW().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumM()) && !vo.getRiskWordsNumM().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumQ()) && !vo.getRiskWordsNumQ().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumY()) && !vo.getRiskWordsNumY().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
        }

        IPage<RiskPointAggVo> pages = baseMapper.riskPointAggNew(page, model, vo);
        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        CopyOnWriteArrayList<RiskPointAggVo> recordsList = CollUtil.newCopyOnWriteArrayList(pages.getRecords());
        for (RiskPointAggVo e : recordsList) {
            RiskEventInsightModel target = new RiskEventInsightModel();
            BeanUtil.copyProperties(model, target);
            target.setAccessToken(token);

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                target.setDlrName(e.getRiskName());
                target.setEmotion("负面");
                target.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                target.setEndDate(CalculatorUtils.getRiskEndDate(target.getDateUnit(), e.getPublishDate()));

                if (target.getDateUnit() == -1) {
                    target.setStartDate(DateUtil.formatDateTime(e.getPublishDate()));
                    target.setEndDate(DateUtil.formatDate(e.getPublishDate()) + " 23:59:59");
                    target.setEndDateUp(DateUtil.formatDate(DateUtil.offsetDay(e.getPublishDate(), -1)) + " 23:59:59");
                    target.setStartDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(e.getPublishDate(), -1)));
                }
                target.SetUpCycle();
                e.setStartDate(target.getStartDate());
                e.setEndDate(target.getEndDate());
                e.setDateUnit(target.getDateUnit());
                if (Objects.nonNull(e.getRiskIndex())) {

                    //20231012修改读取风险数据库
//        String riskLevel = CalculatorUtils.getRiskLevel(e.getRiskIndex());
                    String riskLevel = vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode(riskType, e.getBrandCode(), e.getRiskIndex());
                    e.setRiskGradeTag(riskLevel);
                }
                Map a = emotionUserDiService.riskTrend(target);
                if (a != null) {
                    if (a.get("totalMentionsUp") == null) {
                        e.setRise(-1);
                        e.setRise(1);
                    } else if (a.get("totalMentions") == null) {
                        e.setRise(-1);
                    } else {
                        BigDecimal th = (BigDecimal) a.get("totalMentions");
                        BigDecimal up = (BigDecimal) a.get("totalMentionsUp");
                        if (up == null) {
                            e.setRise(1);
                        } else if (th == null) {
                            e.setRise(-1);
                        } else {
                            e.setRise(th.compareTo(up));
                        }
                    }
                }

                return null;
            })));
        }

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        pages.setRecords(recordsList);
        return pages;
    }

    @Override
    public Result<?> dataAnalysisBriefing(RiskEventInsightModel model, DwdVocDealerRisk risk) {
        RiskBriefingVo briefingVo = new RiskBriefingVo();
        briefingVo.setWarnPeriod(risk.getStatisticType());
        briefingVo.setTimeStr(DateUtils.dateToStr(risk.getPublishDate()));
        briefingVo.setStartDate(briefingVo.getTimeStr() + " 00:00:00");
        briefingVo.setEndDate(briefingVo.getTimeStr() + " 23:59:59");
        briefingVo.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setStartDate(briefingVo.getStartDate());
        model.setEndDate(briefingVo.getEndDate());
        model.setEmotion("负面");
        model.setIntention("投诉");
        model.setDlrName(risk.getDlrName());
        RiskEventInsightModel model1 = BeanUtil.copyProperties(model, RiskEventInsightModel.class);
        model1.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model1.setEndDate(CalculatorUtils.getRiskEndDate(briefingVo.getDateUnit(), risk.getPublishDate()));
        if (briefingVo.getDateUnit() == -1) {
            model1.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
            model1.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");
            model.setEndDateUp(DateUtil.formatDate(DateUtil.offsetDay(risk.getPublishDate(), -1)) + " 23:59:59");
            model.setStartDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(risk.getPublishDate(), -1)));

        }
        VocOverBriefingValueVo re;

        model1.SetUpCycle();
        model1.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));
        ExecutorService executorService = Executors.newFixedThreadPool(8);
        Future<VocOverBriefingValueVo> re1=executorService.submit(()->emotionUserDiService.riskBriefingValue(model1));
        Future<BigDecimal> statTotal=executorService.submit(()->emotionUserDiService.riskStatisticTotal(model));
        Future<Map<String, BigDecimal> > userFuture=executorService.submit(()->emotionUserDiService.riskUserNum(model1));
        Future<BigDecimal> userTotal=executorService.submit(()->emotionUserDiService.riskUserTotalNum(model));
        Future<List<HighHotWordsVo>> hotWordsfu=executorService.submit(()->emotionUserDiService.riskHotWordsOpinion(model1));
        Future<String> riskLeve=executorService.submit(()->vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode(riskType, risk.getBrandCode(), risk.getRiskIndex()));
        QueryWrapper<WoOriginalData> query=new QueryWrapper<>();
        query.lambda().eq(WoOriginalData::getDlrName, risk.getDlrName()).isNotNull(WoOriginalData::getDealerRegion1).isNotNull(WoOriginalData::getDealerRegion2).isNotNull(WoOriginalData::getCustomerProvince).last(" limit 1");
        WoOriginalData woeno= originalDataService.getOne(query);
        if (woeno!=null)
        briefingVo.setRegion(Stream.of(woeno.getDealerRegion1(),
                        woeno.getDealerRegion2(),
                        woeno.getCustomerProvince(),
                        woeno.getCustomerCity())
                .filter(text -> !StringUtils.isEmpty(text))
                .collect(Collectors.joining(" / ")));
        Map<String, BigDecimal> user;
        try {
            re=re1.get();
            if (re == null) {
                re = new VocOverBriefingValueVo();
            }
            briefingVo.setStatistic(risk.getComplainNum());

            if (re.getTotalMentions() != null && re.getTotalMentions().longValue() > 0 && re.getTotalMentionsUp() == null) {
                briefingVo.setStatisticR(new BigDecimal(1));
            } else {
                briefingVo.setStatisticR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
            }
            briefingVo.setStatisticTotal(statTotal.get());

            user=userFuture.get();
            if (user != null) {
                briefingVo.setUsersNum(new BigDecimal(String.valueOf(user.get("userCount"))));
                Long usup = Long.valueOf(String.valueOf(user.get("userCountUp")));
                if (user.get("userCountUp") != null) {
                    briefingVo.setUsersNumR(new BigDecimal(1));
                }
                briefingVo.setUsersNumR(CalculatorUtils.ringRatio(briefingVo.getUsersNum(), ObjectUtil.isNull(usup) ? null : new BigDecimal(usup)));
            }
            briefingVo.setRiskName(risk.getDlrName());
            briefingVo.setUserTotalNum(userTotal.get());
            briefingVo.setHotWords(hotWordsfu.get());
            briefingVo.setRiskGradeTag(riskLeve.get());
            return Result.OK(briefingVo);

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            executorService.shutdown();
        }
        return null;
    }

    @Override
    public Result<?> userIntentionTrend(RiskEventInsightModel model, DwdVocDealerRisk risk) {
        //修改环比计算+时间范围补齐
        List<IntentionEmotionTrendVo> objectLists = new ArrayList<>();
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            IntentionEmotionTrendVo emotionVo = dwsVocEmotionDiMapper.emotionIntentionTrend(model);

            if (ObjectUtil.isNull(emotionVo)) {
                emotionVo = IntentionEmotionTrendVo.builder().build();
            }

            if (3 == model.getDateUnit().intValue()) {
                emotionVo.setDateStr(date.getTime().replace("-", ""));
            } else {
                emotionVo.setDateStr(date.getTime());
            }
            emotionVo.setDateStr(date.getTime());
            objectLists.add(emotionVo);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> IntentionEmotionTrendVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(IntentionEmotionTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            IntentionEmotionTrendVo obj = objectLists.get(i);  //当前
            IntentionEmotionTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());

            if (model.getDateUnit().intValue() == -1) {
                obj.setComplaintAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getComplaint(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getComplaint(), new BigDecimal(preDays));
                obj.setComplaintA(objAvgDays);
                obj.setComplaintAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setUserNumAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserNum(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getUserNum(), new BigDecimal(preDays));
                obj.setUserNumA(objAvgDays);
                obj.setUserNumAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }



            obj.setUserNumR(CalculatorUtils.ringRatio(obj.getUserNum(), preObj.getUserNum()));
            obj.setComplaintR(CalculatorUtils.ringRatio(obj.getComplaint(), preObj.getComplaint()));
            if (ObjectUtil.isNull(obj.getComplaint())) {
                obj.setComplaint(BigDecimal.ZERO);
            }
            if (ObjectUtil.isNull(obj.getUserNum())) {
                obj.setUserNum(BigDecimal.ZERO);
            }
        }
        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            if(model.getDateUnit() == -1){
                if(objectLists.size()<index){
                    objectLists = new ArrayList<>(objectLists.subList(1, objectLists.size()));
                }else {
                    objectLists = new ArrayList<>(objectLists.subList(objectLists.size()-index, objectLists.size()));
                }
            }else {
                int s = objectLists.size() >= index ? objectLists.size() : index;
                objectLists = new ArrayList<>(objectLists.subList(s >= objectLists.size() ? 1 : s - index, objectLists.size()));
            }

        }
        return Result.OK(objectLists);
    }

    @Override
    public Result<?> hotWords(RiskEventInsightModel model) {
        List<HighHotWordsVo> hotWordsVos = dwsVocEmotionDiMapper.riskHotWords(model);
        return Result.OK(hotWordsVos);
    }

    @Override
    public Result<?> problemDistribution(RiskEventInsightModel model) {
        List<ThemeDistrVo> distrVos = new ArrayList<>();
        BigDecimal total = null;
        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            Set<String> threes = new HashSet<>();
            threes.add(model.getThirdDimensionCode());
            model.setThreeDimensionCodes(threes);
            distrVos = dwsVocEmotionDiMapper.topicDistribution(model);

        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            if (model.getSecondDimensionCodes().size() > 1) {
                distrVos = dwsVocEmotionDiMapper.secondDistribution(model);
            } else {
                distrVos = dwsVocEmotionDiMapper.threeDistribution(model);
            }

        } else if (model.getFirstDimensionCode() != null && !"".equals(model.getFirstDimensionCode())) {
            distrVos = dwsVocEmotionDiMapper.secondDistribution(model);
        } else {
            distrVos = dwsVocEmotionDiMapper.themeDistribution(model);
        }
        if (model.getDataType().equals(DataEnum.numMention)) {
            total = distrVos.stream().map(ThemeDistrVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }
        BigDecimal finalTotal = total;
        distrVos.forEach(e -> e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalTotal)));
        return Result.OK(distrVos);
    }

    @Override
    public List<DwdVocDealerRisk> riskBranchesFiltering(VocRiskWarningRules riskWarningRules, RiskRuleVo vo) {
        return baseMapper.riskBranchesFiltering(riskWarningRules, vo);
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }


    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

}
