package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocEmotionRisk;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.mapper.DwdVocRiskMapper;
import com.car.stats.mapper.DwsVocEmotionDiMapper;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.mapper.DwsVocQualityUserDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocDealerRiskService;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.IDwdVocRiskService;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.*;
import com.car.stats.vo.risk.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.util.*;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocChannelCategoryService;
import com.car.voc.service.IVocRiskWarningRulesService;
import com.car.voc.vo.ProportionAreaVo;
import com.car.voc.vo.RiskMailDataVo;
import com.car.voc.vo.risk.RiskRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.spec.RSAPrivateKeySpec;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName IDwdVocRiskServiceImpl.java
 * @Description TODO
 * @createTime 2022年11月25日 12:02
 * @Copyright voc
 */
@Service
@Slf4j
public class DwdVocRiskServiceImpl extends ServiceImpl<DwdVocRiskMapper, DwdVocRisk> implements IDwdVocRiskService {
    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;

    @Autowired
    IVocBusinessTagService tagService;
    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;
    @Autowired
    IDwdVocDealerRiskService dealerRiskService;
    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Autowired
    Executor defExecutor;


    @Override
    public Result<?> dataAnalysisBriefing(RiskEventInsightModel model, DwdVocRisk risk) {
        RiskBriefingVo briefingVo = new RiskBriefingVo();
        briefingVo.setWarnPeriod(risk.getStatisticType());
        briefingVo.setTimeStr(DateUtils.dateToStr(risk.getPublishDate()));

        briefingVo.setStartDate(briefingVo.getTimeStr() + " 00:00:00");
        briefingVo.setEndDate(briefingVo.getTimeStr() + " 23:59:59");
        briefingVo.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));

        model.setStartDate(briefingVo.getStartDate());
        model.setEndDate(briefingVo.getEndDate());

        model.setEmotion("负面");
        RiskEventInsightModel model1 = BeanUtil.copyProperties(model, RiskEventInsightModel.class);
        model1.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model1.setEndDate(CalculatorUtils.getRiskEndDate(briefingVo.getDateUnit(), risk.getPublishDate()));
        if (briefingVo.getDateUnit() == -1) {
            model1.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
            model1.setEndDate(DateUtil.formatDate(risk.getPublishDate()) + " 23:59:59");

            model.setEndDateUp(DateUtil.formatDate(DateUtil.offsetDay(risk.getPublishDate(), -1)) + " 23:59:59");
            model.setStartDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(risk.getPublishDate(), -1)));

        }
        model1.SetUpCycle();
        model1.setCreateDate(DateUtil.formatDateTime(risk.getCreateTime()));

        ExecutorService executorService = Executors.newFixedThreadPool(8);
        Future<VocOverBriefingValueVo> re1=executorService.submit(()->emotionUserDiService.riskBriefingValue(model1));
        Future<BigDecimal> statTotal=executorService.submit(()->emotionUserDiService.riskStatisticTotal(model));
        Future<Map<String, BigDecimal> > userFuture=executorService.submit(()->emotionUserDiService.riskUserNum(model1));
        Future<BigDecimal> userTotal=executorService.submit(()->emotionUserDiService.riskUserTotalNum(model));
        Future<List<String>> riskCarSerseis=executorService.submit(()->emotionUserDiService.riskCarSeries(model1));
        Future<List<HighHotWordsVo>> hotWordsfu=executorService.submit(()->emotionUserDiService.riskHotWordsOpinion(model1));
        Future<String> riskLeve=executorService.submit(()->vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode("风险事件洞察", risk.getBrandCode(), risk.getRiskIndex()));

        VocOverBriefingValueVo re;
        Map<String, BigDecimal> user;
        try {
           re=re1.get();
            if (re == null) {
                re = new VocOverBriefingValueVo();
            }
            briefingVo.setStatistic(risk.getNegativeNum());

            if (re.getTotalMentions() != null && re.getTotalMentions().longValue() > 0 && re.getTotalMentionsUp() == null) {
                briefingVo.setStatisticR(new BigDecimal(1));
            } else {
                briefingVo.setStatisticR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
            }
            briefingVo.setStatisticTotal(statTotal.get());

            user=userFuture.get();
            if (user != null) {
                briefingVo.setUsersNum(new BigDecimal(String.valueOf(user.get("userCount"))));
                Long usup = Long.valueOf(String.valueOf(user.get("userCountUp")));
                if (user.get("userCountUp") != null) {
                    briefingVo.setUsersNumR(new BigDecimal(1));
                }
                briefingVo.setUsersNumR(CalculatorUtils.ringRatio(briefingVo.getUsersNum(), ObjectUtil.isNull(usup) ? null : new BigDecimal(usup)));
            }
            briefingVo.setUserTotalNum(userTotal.get());
            briefingVo.setCarSeries(riskCarSerseis.get());
            Set<String> aggprom = new HashSet<>();
            aggprom.add(model.getTopicCode());
            briefingVo.setAggProblem(aggprom);
            briefingVo.setHotWords(hotWordsfu.get());
            briefingVo.setRiskGradeTag(riskLeve.get());
            return Result.OK(briefingVo);

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            executorService.shutdown();
        }
        return null;
    }


    @Override
    public Result<?> dataAnalysisChannelTrend(RiskEventInsightModel model1, DwdVocRisk risk) {
        //修改环比计算+时间范围补齐
        Map<String, Object> charns = new HashMap<>();
        FilterCriteriaModel model = BeanUtil.copyProperties(model1, FilterCriteriaModel.class);
        Set<String> topicts = new HashSet<>();
        topicts.add(model1.getTopicCode());
        model.setTopicCodes(topicts);
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        model.setDataSources(null);
        model.setChannelId(null);
        List<ChannelVo> objects = dwsVocEmotionDiMapper.riskChannelDistribution(model);
        if (CollectionUtils.isEmpty(objects)) {
            return Result.OK(charns);
        }
//转日期格式
        final Set<String> containsDates = objects.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objects.addAll(dateTempSet.stream()
                .map(e -> {
                    ChannelVo vo = new ChannelVo();
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //存放所有ID用于补全展示数据
        final Set<String> channelIdTempSet = objects.stream().filter(e -> ObjectUtil.isNotNull(e)).filter(e -> StrUtil.isNotBlank(e.getChannelId()))
                .map(ChannelVo::getChannelId).collect(Collectors.toSet());
        final Map<String, List<ChannelVo>> rsMap = objects.stream().collect(Collectors.groupingBy(ChannelVo::getDateStr));


        //问题周期/时间
        List<TrendChannelVo> objectLists = rsMap.keySet().stream().map(dateStr -> {
            TrendChannelVo vo = new TrendChannelVo();
            vo.setDateStr(dateStr);
            vo.setChannelVos(rsMap.get(dateStr));
            return vo;
        }).collect(Collectors.toList());

        final String channelProportionDate = objectLists.stream()
                .max(Comparator.comparing(TrendChannelVo::getDateStr)).orElse(objectLists.stream().findFirst().get()).getDateStr();


        final List<ChannelVo> channelVos = objects.stream()
                .filter(e -> ObjectUtil.isNotNull(e))
                .filter(e -> ObjectUtil.isNotNull(e.getStatistic()))
                .filter(e -> channelProportionDate.equals(e.getDateStr()))
                .collect(Collectors.toList());

        //补齐业务对象
        objectLists.stream().forEach(e -> {
            Set<String> containsIds = e.getChannelVos().stream().map(ChannelVo::getChannelId).collect(Collectors.toSet());
            e.getChannelVos().addAll(
                    channelIdTempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id -> ChannelVo.builder()
                                    .dateStr(e.getDateStr())
                                    .channelId(id).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
            );
        });

        //时间排序
        objectLists.sort(Comparator.comparing(TrendChannelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        //计算
        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            TrendChannelVo obj = objectLists.get(i);  //当前
            TrendChannelVo preObj = objectLists.get(i - 1);   //前一个
            final BigDecimal total = obj.getChannelVos().stream().map(ChannelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, ChannelVo> map = obj.getChannelVos().stream().collect(Collectors.toMap(ChannelVo::getChannelId, e -> e, (k1, k2) -> k1));
            Map<String, ChannelVo> preMap = preObj.getChannelVos().stream().collect(Collectors.toMap(ChannelVo::getChannelId, e -> e, (k1, k2) -> k1));

            map.keySet().stream().forEach(id -> {
                ChannelVo cur_ = map.get(id);
                ChannelVo pre_ = preMap.get(id);
                final Long days = dateR.get(obj.getDateStr());
                final Long preDays = dateR.get(preObj.getDateStr());
                //设置日均 averagePerDay()
                if(model.getDateUnit().intValue() == -1) {
                    cur_.setStatisticAR(BigDecimal.ZERO);
                }else{
                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_)? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(preDays));
                    cur_.setStatisticA(objAvgDays);
                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
                }


                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), total));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
            });
        }


        //时间排序
        objectLists.sort(Comparator.comparing(TrendChannelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        channelVos.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            if(s>index){
                objectLists = new ArrayList<>(objectLists.subList( s-index  , objectLists.size()));
            }else if(s<index){
                objectLists = new ArrayList<>(objectLists.subList(  index -s  , objectLists.size()));
            }else {
                objectLists = new ArrayList<>(objectLists.subList(  1  , objectLists.size()));
            }
        }
        List<ChannelVo> collect = channelVos.stream().sorted(Comparator.comparing(ChannelVo::getStatistic).reversed()).collect(Collectors.toList());
        charns.put("channelProportion", collect);
        charns.put("channelTrend", objectLists);
        return Result.OK(charns);

    }

    private List<ChannelVo> setRiskChannelAllListl(FilterCriteriaModel model, List<String> datasous, List<String> channels) {
        List<ChannelVo> objects = new ArrayList<>();

        model.setDataSources(null);
        model.setChannelId(null);
        objects.addAll(dwsVocEmotionDiMapper.riskChannelDistribution(model));

        return objects;
    }

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }
    @Autowired
    IDwdVocQualityRiskService qualityRiskService;
    @Resource
    DwsVocQualityUserDiMapper qualityUserDiMapper;
    @Override
    public Result<?> riskstatictisTrend(RiskEventInsightModel model1) {
        FilterCriteriaModel model= new FilterCriteriaModel();


        if(model1.getRiskType()== 1){
            DwdVocRisk risk=this.getById(model1.getRiskId());
            if (risk==null){
                return Result.error("未找到问题！");
            }
            Set<String> topicts=new HashSet<>();topicts.add(risk.getTopicCode());
            model.setTopicCodes(topicts);
            model.setBrandCode(risk.getBrandCode());
            setModelDate(model,risk.getPublishDate(),risk.getStatisticType());
            model.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(risk.getStatisticType()),risk.getPublishDate()));
//            model.setEndDate(DateUtil.formatDate(risk.getPublishDate())+" 00:00:00");
            return eventMailStaticUser(model);
        }else if (model1.getRiskType()==2||model1.getRiskType()==4){
            DwdVocQualityRisk quality = qualityRiskService.getById(model1.getRiskId());
            if (quality==null){
                return Result.error("未找到问题！");
            }
            Set<String> topicts=new HashSet<>();topicts.add(quality.getTopicCode());
            model.setTopicCodes(topicts);
            model.setBrandCode(quality.getBrandCode());
            setModelDate(model,quality.getPublishDate(),quality.getStatisticType());
            model.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(quality.getStatisticType()),quality.getPublishDate()));
//            model.setEndDate(DateUtil.formatDate(quality.getPublishDate())+" 00:00:00");
            return qualityMailStaticUser(model,quality);
        }else if(model1.getRiskType()== 5){
            DwdVocDealerRisk  risk=dealerRiskService.getById(model1.getRiskId());
            if (risk==null){
                return Result.error("未找到问题！");
            }
            model.setIntention("投诉");
            model.setEmotion("负面");
            model.setBrandCode(risk.getBrandCode());
            setModelDate(model,risk.getPublishDate(),risk.getStatisticType());
            model.setEndDate(CalculatorUtils.getRiskEndDate(CalculatorUtils.periodStrToNum(risk.getStatisticType()),risk.getPublishDate()));
//            model.setEndDate(DateUtil.formatDate(risk.getPublishDate())+" 00:00:00");
             model.setDlrName(risk.getDlrName());
            return eventMailStaticUser(model);
        }else{
            List<IntentionTrendVo> trendVos =new ArrayList<>();
            return Result.OK(trendVos);
        }
    }



    private Result<?> qualityMailStaticUser(FilterCriteriaModel model,  DwdVocQualityRisk quality){
        List<IntentionTrendVo> trendVos = qualityRiskService.qualityUserStatictis(model,quality);
        BigDecimal total = trendVos.stream().map(IntentionTrendVo::getTotal).reduce(BigDecimal.ZERO,BigDecimalUtils::sum);
        BigDecimal userAverage = NumberUtil.div(trendVos.stream().map(IntentionTrendVo::getUserCount).reduce(BigDecimal.ZERO, BigDecimalUtils::sum), new BigDecimal(trendVos.size()));
        BigDecimal average = NumberUtil.round(NumberUtil.div(total,trendVos.size()),2, RoundingMode.DOWN);
        List<IntentionTrendVo> finalTrendVos = trendVos;
        trendVos.stream().forEach(s->{
            s.setUserCountP(CalculatorUtils.proportion(NumberUtil.sub(s.getUserCount(), userAverage), userAverage));
            s.setTotalP(CalculatorUtils.proportion(NumberUtil.sub(s.getTotal(), average), average));
            s.setAverage(average);
            String before =  model.getDateUnit() ==-1?DateUtil.format(DateUtil.parseDate(DateUtils.getBeforeTime(s.getDataStr()+" 00:00:00", model.getDateUnit())),Constants.YYYY_MM_DD):DateUtils.getBeforeTime(s.getDataStr(), model.getDateUnit());
            IntentionTrendVo beVo = finalTrendVos.stream().filter(vo->vo.getDataStr().equals(before)).findFirst().orElse(null);
            if(beVo == null) return;
            s.setUserCountR(CalculatorUtils.ringRatio(s.getUserCount(),beVo.getUserCount()));
            s.setTotalR(CalculatorUtils.ringRatio(s.getTotal(),beVo.getTotal()));
        });
        return Result.OK(trendVos);
    }
    @Resource
    DwsVocEmotionUserDiMapper userDiMapper;
    private Result<?> eventMailStaticUser(FilterCriteriaModel model){
        List<IntentionTrendVo> trendVos = userDiMapper.eventUserStatictis(model);
        BigDecimal total = trendVos.stream().map(IntentionTrendVo::getTotal).reduce(BigDecimal.ZERO,BigDecimalUtils::sum);
        BigDecimal userAverage = NumberUtil.div(trendVos.stream().map(IntentionTrendVo::getUserCount).reduce(BigDecimal.ZERO, BigDecimalUtils::sum), new BigDecimal(trendVos.size()));
        BigDecimal average = NumberUtil.round(NumberUtil.div(total,trendVos.size()),2, RoundingMode.DOWN);

        List<IntentionTrendVo> finalTrendVos = trendVos;
        trendVos.stream().forEach(s->{
            s.setUserCountP(CalculatorUtils.proportion(NumberUtil.sub(s.getUserCount(), userAverage), userAverage));
            s.setTotalP(CalculatorUtils.proportion(NumberUtil.sub(s.getTotal(), average), average));
            s.setAverage(average);
            String before =  model.getDateUnit() ==-1?DateUtil.format(DateUtil.parseDate(DateUtils.getBeforeTime(s.getDataStr()+" 00:00:00", model.getDateUnit())),Constants.YYYY_MM_DD):DateUtils.getBeforeTime(s.getDataStr(), model.getDateUnit());
            IntentionTrendVo beVo = finalTrendVos.stream().filter(vo->vo.getDataStr().equals(before)).findFirst().orElse(null);
            if(beVo == null) return;
            s.setUserCountR(CalculatorUtils.ringRatio(s.getUserCount(),beVo.getUserCount()));
            s.setTotalR(CalculatorUtils.ringRatio(s.getTotal(),beVo.getTotal()));
        });
        return Result.OK(trendVos);
    }
    @Override
    public Result<?> riskMailData(RiskEventInsightModel model1) {
        FilterCriteriaModel model= new FilterCriteriaModel();

        if(model1.getRiskType()== 1){
            DwdVocRisk event = this.getById(model1.getRiskId());
            Set<String> topicts=new HashSet<>();topicts.add(event.getTopicCode());
            model.setTopicCodes(topicts);
            model.setBrandCode(event.getBrandCode());
            model.setDateUnit(CalculatorUtils.periodStrToNum(event.getStatisticType()));
            model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),event.getPublishDate()));
            model.setStartDate(DateUtil.formatDate(event.getPublishDate())+" 00:00:00");
            model.setEmotion("负面");
            return eventMailData(model,event.getTopicCode());
        }else if (model1.getRiskType()== 2|| model1.getRiskType()== 4){
            DwdVocQualityRisk quality = qualityRiskService.getById(model1.getRiskId());
            Set<String> topicts=new HashSet<>();topicts.add(quality.getTopicCode());
            model.setTopicCodes(topicts);
            model.setBrandCode(quality.getBrandCode());
            model.setDateUnit(CalculatorUtils.periodStrToNum(quality.getStatisticType()));
            model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),quality.getPublishDate()));
            model.setStartDate(DateUtil.formatDate(quality.getPublishDate())+" 00:00:00");
            model.setEmotion("负面");
            if (model1.getRiskType().equals(4)){
                model.setChannelIds(Arrays.asList(quality.getChannelId()));
            }
            return qualityMailData(model,quality.getTopicCode(),quality);
        }else if (model1.getRiskType()== 5){
            DwdVocDealerRisk  dealerRisk = dealerRiskService.getById(model1.getRiskId());
            model.setBrandCode(dealerRisk.getBrandCode());
            model.setDateUnit(CalculatorUtils.periodStrToNum(dealerRisk.getStatisticType()));
            model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),dealerRisk.getPublishDate()));
            model.setStartDate(DateUtil.formatDate(dealerRisk.getPublishDate())+" 00:00:00");
            model.setEmotion("负面");
            model.setIntention("投诉");
            return dealerMailData(model,dealerRisk);
        }else {
            RiskMailDataVo mailDataVo = new RiskMailDataVo();
            return Result.OK(mailDataVo);
        }
    }

    private Result<?> dealerMailData(FilterCriteriaModel model, DwdVocDealerRisk dealerRisk) {
        model.setDlrName(dealerRisk.getDlrName());
        model.setBrandCode(dealerRisk.getBrandCode());
        model.setIntention("投诉");
        FilterCriteriaModel model1=BeanUtil.copyProperties(model,FilterCriteriaModel.class);
        ExecutorService executor = Executors.newFixedThreadPool(4);
        CompletableFuture<List<String>> warningVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.getWaringList(dealerRisk.getDlrName()),executor);
        CompletableFuture<List<ProportionCarSeriesVo>> carSeriesVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.riskCarSeriesProption(model),executor);
        CompletableFuture<List<ProportionAreaVo>> areaVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.riskAreaProption(model),executor);
        CompletableFuture<List<ChannelVo>> channelVos = CompletableFuture.supplyAsync(() -> setRiskChannelAllListl(model1,null,null),executor);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(warningVos, carSeriesVos,areaVos,channelVos);
        try {
            allOf.get(); // Wait for all futures to complete
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            executor.shutdown();
        }
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(areaVos.join(), ProportionAreaVo::getStatistic, ProportionAreaVo::setStatisticP);
        CalculatorUtils.proportion(carSeriesVos.join(), ProportionCarSeriesVo::getStatistic, ProportionCarSeriesVo::setStatisticP);
        CalculatorUtils.proportion(channelVos.join(), ChannelVo::getStatistic, ChannelVo::setStatisticP);
        if(areaVos.join().size()>10){
            areaVos.join().sort(Comparator.comparing(ProportionAreaVo::getStatistic).reversed());
        }
        if(carSeriesVos.join().size()>10){
            carSeriesVos.join().sort(Comparator.comparing(ProportionCarSeriesVo::getStatistic).reversed());
        }
        if(channelVos.join().size()>10){
            channelVos.join().sort(Comparator.comparing(ChannelVo::getStatistic).reversed());
        }
        RiskMailDataVo mailDataVo = new RiskMailDataVo();
        mailDataVo.setAreaVos(areaVos.join().size()>10?new ArrayList<>(areaVos.join().subList(0,10)):areaVos.join());
        mailDataVo.setWarningCount(warningVos.join().size()+1);
        mailDataVo.setCarSeriesVos(carSeriesVos.join().size()>10?new ArrayList<>(carSeriesVos.join().subList(0,10)):carSeriesVos.join());
        mailDataVo.setChannelVos(channelVos.join().size()>10?new ArrayList<>(channelVos.join().subList(0,10)):channelVos.join());
        return Result.OK(mailDataVo);
    }

    private Result<?> qualityMailData(FilterCriteriaModel model,String topicCode, DwdVocQualityRisk quality){
        FilterCriteriaModel model1=BeanUtil.copyProperties(model,FilterCriteriaModel.class);
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CompletableFuture<List<String>> warningVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.getWaringList(topicCode),executor);
        CompletableFuture<List<ProportionCarSeriesVo>> carSeriesVos = CompletableFuture.supplyAsync(() -> qualityUserDiMapper.riskCarSeriesProption(model,quality),executor);
        CompletableFuture<List<ProportionAreaVo>> areaVos = CompletableFuture.supplyAsync(() -> qualityUserDiMapper.riskAreaProption(model,quality),executor);
        CompletableFuture<List<ChannelVo>> channelVos = CompletableFuture.supplyAsync(() -> {
            List<ChannelVo> objects;
            List<String> list = new ArrayList<>();
            objects=qualityUserDiMapper.riskDataSourceDistribution(model1,quality);
            return objects;
        },executor);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(warningVos, carSeriesVos,areaVos,channelVos);
        try {
            allOf.get(); // Wait for all futures to complete
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            executor.shutdown();
        }
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(areaVos.join(), ProportionAreaVo::getStatistic, ProportionAreaVo::setStatisticP);
        CalculatorUtils.proportion(carSeriesVos.join(), ProportionCarSeriesVo::getStatistic, ProportionCarSeriesVo::setStatisticP);
        CalculatorUtils.proportion(channelVos.join(), ChannelVo::getStatistic, ChannelVo::setStatisticP);
        if(areaVos.join().size()>10){
            areaVos.join().sort(Comparator.comparing(ProportionAreaVo::getStatistic).reversed());
        }
        if(carSeriesVos.join().size()>10){
            carSeriesVos.join().sort(Comparator.comparing(ProportionCarSeriesVo::getStatistic).reversed());
        }
        if(channelVos.join().size()>10){
            channelVos.join().sort(Comparator.comparing(ChannelVo::getStatistic).reversed());
        }
        RiskMailDataVo mailDataVo = new RiskMailDataVo();
        mailDataVo.setAreaVos(areaVos.join().size()>10?new ArrayList<>(areaVos.join().subList(0,10)):areaVos.join());
        mailDataVo.setWarningCount(warningVos.join().size()+1);
        mailDataVo.setCarSeriesVos(carSeriesVos.join().size()>10?new ArrayList<>(carSeriesVos.join().subList(0,10)):carSeriesVos.join());
        mailDataVo.setChannelVos(channelVos.join().size()>10?new ArrayList<>(channelVos.join().subList(0,10)):channelVos.join());

        return Result.OK(mailDataVo);
    }
    private Result<?> eventMailData(FilterCriteriaModel model,String topicCode){
        String path = SpringContextUtils.getHttpServletRequest().getRequestURI();
        FilterCriteriaModel model1=BeanUtil.copyProperties(model,FilterCriteriaModel.class);
        ExecutorService executor = Executors.newFixedThreadPool(4);
        CompletableFuture<List<String>> warningVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.getWaringList(topicCode),executor);
        CompletableFuture<List<ProportionCarSeriesVo>> carSeriesVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.riskCarSeriesProption(model),executor);
        CompletableFuture<List<ProportionAreaVo>> areaVos = CompletableFuture.supplyAsync(() -> dwsVocEmotionDiMapper.riskAreaProption(model),executor);
        CompletableFuture<List<ChannelVo>> channelVos = CompletableFuture.supplyAsync(() -> setRiskChannelAllListl(model1,null,null),executor);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(warningVos, carSeriesVos,areaVos,channelVos);
        try {
            allOf.get(); // Wait for all futures to complete
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            executor.shutdown();
        }
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(areaVos.join(), ProportionAreaVo::getStatistic, ProportionAreaVo::setStatisticP);
        CalculatorUtils.proportion(carSeriesVos.join(), ProportionCarSeriesVo::getStatistic, ProportionCarSeriesVo::setStatisticP);
        CalculatorUtils.proportion(channelVos.join(), ChannelVo::getStatistic, ChannelVo::setStatisticP);
        if(areaVos.join().size()>10){
            areaVos.join().sort(Comparator.comparing(ProportionAreaVo::getStatistic).reversed());
        }
        if(carSeriesVos.join().size()>10){
            carSeriesVos.join().sort(Comparator.comparing(ProportionCarSeriesVo::getStatistic).reversed());
        }
        if(channelVos.join().size()>10){
            channelVos.join().sort(Comparator.comparing(ChannelVo::getStatistic).reversed());
        }
        RiskMailDataVo mailDataVo = new RiskMailDataVo();
        mailDataVo.setAreaVos(areaVos.join().size()>10?new ArrayList<>(areaVos.join().subList(0,10)):areaVos.join());
        mailDataVo.setWarningCount(warningVos.join().size()+1);
        mailDataVo.setCarSeriesVos(carSeriesVos.join().size()>10?new ArrayList<>(carSeriesVos.join().subList(0,10)):carSeriesVos.join());
        mailDataVo.setChannelVos(channelVos.join().size()>10?new ArrayList<>(channelVos.join().subList(0,10)):channelVos.join());
        return Result.OK(mailDataVo);
    }

    @Override
    public Result<?> emotionIntentionTrend(RiskEventInsightModel model, DwdVocRisk risk) {
        //修改环比计算+时间范围补齐
        List<IntentionEmotionTrendVo> objectLists = new ArrayList<>();
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
//        if (model.getDateUnit()!=-1) {
//            model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
//        }
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            IntentionEmotionTrendVo emotionVo = dwsVocEmotionDiMapper.emotionIntentionTrend(model);

            if (ObjectUtil.isNull(emotionVo)) {
                emotionVo = IntentionEmotionTrendVo.builder().build();
            }

            if (3 == model.getDateUnit().intValue()) {
                emotionVo.setDateStr(date.getTime().replace("-", ""));
            } else {
                emotionVo.setDateStr(date.getTime());
            }
            emotionVo.setDateStr(date.getTime());
            objectLists.add(emotionVo);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> IntentionEmotionTrendVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(IntentionEmotionTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            IntentionEmotionTrendVo obj = objectLists.get(i);  //当前
            IntentionEmotionTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setPraiseAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getPraise(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getPraise(), new BigDecimal(preDays));
                obj.setPraiseA(objAvgDays);
                obj.setPraiseAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setComplaintAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getComplaint(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getComplaint(), new BigDecimal(preDays));
                obj.setComplaintA(objAvgDays);
                obj.setComplaintAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setNegativeAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getNegative(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getNegative(), new BigDecimal(preDays));
                obj.setNegativeA(objAvgDays);
                obj.setNegativeAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }


            BigDecimal total = NumberUtil.add(obj.getPraise(), obj.getComplaint(), obj.getNegative());
            obj.setNegativeP(CalculatorUtils.proportion(obj.getNegative(), total));
            obj.setPraiseP(CalculatorUtils.proportion(obj.getPraise(), total));
            obj.setComplaintP(CalculatorUtils.proportion(obj.getComplaint(), total));


            obj.setNegativeR(CalculatorUtils.ringRatio(obj.getNegative(), preObj.getNegative()));
            obj.setPraiseR(CalculatorUtils.ringRatio(obj.getPraise(), preObj.getPraise()));
            obj.setComplaintR(CalculatorUtils.ringRatio(obj.getComplaint(), preObj.getComplaint()));
//            obj.setNegativeAR(CalculatorUtils.ringRatio(obj.getNegativeA(), preObj.getNegativeA()));
//            obj.setPraiseAR(CalculatorUtils.ringRatio(obj.getpraiseA(), preObj.getpraiseA()));
//            obj.setComplaintAR(CalculatorUtils.ringRatio(obj.getComplaintA(), preObj.getComplaintA()));

            if (ObjectUtil.isNull(obj.getComplaint())) {
                obj.setComplaint(BigDecimal.ZERO);
            }
            if (ObjectUtil.isNull(obj.getPraise())) {
                obj.setPraise(BigDecimal.ZERO);
            }
            if (ObjectUtil.isNull(obj.getNegative())) {
                obj.setNegative(BigDecimal.ZERO);
            }

        }
        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            if(model.getDateUnit() == -1){
                if(objectLists.size()<index){
                    objectLists = new ArrayList<>(objectLists.subList(1, objectLists.size()));
                }else {
                    objectLists = new ArrayList<>(objectLists.subList(objectLists.size()-index, objectLists.size()));
                }
            }else {
                int s = objectLists.size() >= index ? objectLists.size() : index;
                objectLists = new ArrayList<>(objectLists.subList(s >= objectLists.size() ? 1 : s - index, objectLists.size()));
            }

        }
        return Result.OK(objectLists);
    }

    @Override
    public Result<?> hotWords(RiskEventInsightModel model) {
        List<HighHotWordsVo> hotWordsVos = dwsVocEmotionDiMapper.riskHotWords(model);
        return Result.OK(hotWordsVos);
    }

    @Override
    public Result<?> voiceUserTrend(RiskEventInsightModel model, DwdVocRisk risk) {
        //修改环比计算+时间范围补齐
        List<VoiceUserVo> objectLists = new ArrayList<>();
//        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
//        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
//        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
//        if (model.getDateUnit().equals(-1)) {
//            model.setStartDate(DateUtil.offsetDay(risk.getPublishDate(), -9).toString());
//        }
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(), risk.getPublishDate()));
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));

        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        for (SvwDate date : new ArrayList<>(dates)) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            VoiceUserVo oen = dwsVocEmotionDiMapper.riskVoiceUserTrend(model);
            if (oen == null) {
                oen = new VoiceUserVo();
            }

            if (oen.getUserNum() == null || oen.getUserNum().intValue() == 0) {
                oen.setUserNum(BigDecimal.ZERO);
            }

            oen.setDateStr(date.getTime());
            objectLists.add(oen);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> VoiceUserVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(VoiceUserVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            VoiceUserVo obj = objectLists.get(i);  //当前
            VoiceUserVo preObj = objectLists.get(i - 1);   //前一个

            obj.setUserNumR(CalculatorUtils.ringRatio(obj.getUserNum(), preObj.getUserNum()));
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            if(s>index){
                objectLists = new ArrayList<>(objectLists.subList( s-index  , objectLists.size()));
            }else if(s<index){
                objectLists = new ArrayList<>(objectLists.subList(  index -s  , objectLists.size()));
            }else {
                objectLists = new ArrayList<>(objectLists.subList(  1  , objectLists.size()));
            }
        }
        BigDecimal sum = objectLists.stream().map(VoiceUserVo::getUserNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        Map<String, Object> re = new HashMap<>();
        re.put("average", NumberUtil.round(NumberUtil.div(sum, objectLists.size()), 2, RoundingMode.DOWN));
        re.put("userTrend", objectLists);
        return Result.OK(re);
    }

    @Override
    public Result<?> voiceUserTop(RiskEventInsightModel model) {
        model.setRownum(10000);
        List<VoiceUserTopVo> userTopVos = dwsVocEmotionDiMapper.voiceUserTop(model);
        Map<String, List<VoiceUserTopVo>> ches = userTopVos.stream().collect(Collectors.groupingBy(VoiceUserTopVo::getChannelStr));
        List<ChannelUserTopVo> topVos = new ArrayList<>();
        ches.entrySet().forEach(e -> {
            ChannelUserTopVo topVo = new ChannelUserTopVo();
            BigDecimal usertotal = e.getValue().stream().map(VoiceUserTopVo::getUserNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            e.setValue(e.getValue().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(VoiceUserTopVo::getUserId))), ArrayList::new)));
            e.setValue(e.getValue().stream().sorted(Comparator.comparing(VoiceUserTopVo::getUserNum).reversed()).collect(Collectors.toList()));
            if (e.getValue().size() >= 10) {
                usertotal = e.getValue().subList(0, 10).stream().map(VoiceUserTopVo::getUserNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                topVo.setData(e.getValue().subList(0, 10));
            } else {
                topVo.setData(e.getValue());
            }

            topVo.setTotal(usertotal);
            topVo.setChannelStr(e.getKey());
            topVos.add(topVo);
        });
        topVos.sort(Comparator.comparing(ChannelUserTopVo::getTotal, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(ChannelUserTopVo::getChannelStr)
                .reversed());
        return Result.OK(topVos);
    }

    @Autowired
    RedisUtil redisUtil;

    @Override
    public IPage<RiskPointAggVo> riskPointAggNew(RiskEventInsightModel model) {
        Page<RiskPointAggVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        final String token =  StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);

        VocRiskWarningRules warningRules = vocRiskWarningRulesService.getByBrandCode(model.getBrandCode(), "风险事件洞察");


        RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(warningRules.getId());

        if (ObjectUtils.isNotEmpty(vo)) {
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumD()) && !vo.getRiskWordsNumD().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumW()) && !vo.getRiskWordsNumW().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumM()) && !vo.getRiskWordsNumM().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumQ()) && !vo.getRiskWordsNumQ().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumY()) && !vo.getRiskWordsNumY().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
        }

        IPage<RiskPointAggVo> pages = baseMapper.riskPointAggNew(page, model, vo);

        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        CopyOnWriteArrayList<RiskPointAggVo> recordsList = CollUtil.newCopyOnWriteArrayList(pages.getRecords());


        for (RiskPointAggVo e : recordsList) {
            RiskEventInsightModel target = new RiskEventInsightModel();
            BeanUtil.copyProperties(model, target);
            target.setAccessToken(token);

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                target.setTopicCode(e.getTopicCode());
                target.setEmotion("负面");
                target.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                target.setEndDate(CalculatorUtils.getRiskEndDate(target.getDateUnit(), e.getPublishDate()));

                if (target.getDateUnit() == -1) {
                    target.setStartDate(DateUtil.formatDateTime(e.getPublishDate()));
                    target.setEndDate(DateUtil.formatDate(e.getPublishDate()) + " 23:59:59");
                    target.setEndDateUp(DateUtil.formatDate(DateUtil.offsetDay(e.getPublishDate(), -1)) + " 23:59:59");
                    target.setStartDateUp(DateUtil.formatDateTime(DateUtil.offsetDay(e.getPublishDate(), -1)));
                }
                target.SetUpCycle();
                e.setStartDate(target.getStartDate());
                e.setEndDate(target.getEndDate());
                e.setDateUnit(target.getDateUnit());
                if (Objects.nonNull(e.getRiskIndex())) {

                    //20231012修改读取风险数据库
//        String riskLevel = CalculatorUtils.getRiskLevel(e.getRiskIndex());
                    String riskLevel = vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode("风险事件洞察", e.getBrandCode(), e.getRiskIndex());
                    e.setRiskGradeTag(riskLevel);
                }
                Map a = emotionUserDiService.riskTrend(target);
                if (a != null) {
                    if (a.get("totalMentionsUp") == null) {
                        e.setRise(-1);
                        e.setRise(1);
                    } else if (a.get("totalMentions") == null) {
                        e.setRise(-1);
                    } else {
                        BigDecimal th = (BigDecimal) a.get("totalMentions");
                        BigDecimal up = (BigDecimal) a.get("totalMentionsUp");
                        if (up == null) {
                            e.setRise(1);
                        } else if (th == null) {
                            e.setRise(-1);
                        } else {
                            e.setRise(th.compareTo(up));
                        }
                    }
                }

                return null;
            })));
        }

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        pages.setRecords(recordsList);
//        pages.getRecords().forEach(e -> );
        return pages;
    }

    @Override
    public Result<?> dataAnalysisBriefingTrend(RiskEventInsightModel model, DwdVocRisk risk) {

        model.SetUpCycle();
        model.setDateUnit(-1);
        model.setEndDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setStartDate(DateUtils.addDay(model.getStartDate(), -9));

        List<SvwDate> dates = model.getDateTimes();
        model.setDateList(dates.subList(dates.size() >= 8 ? dates.size() - 8 : 0, dates.size()));
        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        List<BriefingTrendVo> objects = baseMapper.dataAnalysisBriefingTrend(model);
        List<BriefingTrendVo> vos = new ArrayList<>();
        final boolean[] flag = {true};
        model.getDateList().forEach(e -> {
                    BriefingTrendVo vo = objects.stream().filter(d -> e.getDateCycle().equals(d.getDateStr())).collect(Collectors.toList()).stream().findFirst().orElse(null);
                    if (vo == null && flag[0]) {
                        return;
                    } else {
                        flag[0] = false;
                        vo.setDateStr(e.getTime());
                    }
                    vos.add(vo);
                }
        );
        return Result.OK(vos);
    }

    @Autowired
    IVocRiskWarningRulesService vocRiskWarningRulesService;

    @Override
    public BigDecimal getRiskEvents(FilterCriteriaModel model) {

        RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(CommonConstant.sys_risk_emotion_rule);


//        RiskRuleVo vo = (RiskRuleVo) redisUtil.get(CacheConstant.SYS_RISK_RULE + CommonConstant.sys_risk_emotion_rule);
        RiskEventInsightModel model1 = BeanUtil.copyProperties(model, RiskEventInsightModel.class);
        return baseMapper.getRiskEvents(model1, vo);
    }

    @Override
    public List<DwdVocEmotionRisk> riskEventFiltering(VocRiskWarningRules rule, RiskRuleVo vo) {

        return baseMapper.riskEventFiltering(rule, vo);
    }

    @Override
    public Result<?> dataAnalysisWaringNum(RiskEventInsightModel model, DwdVocRisk risk) {
        List<String> list = baseMapper.dataAnalysisWaringNum(risk);
        return Result.OK(list);
    }

    @Autowired
    IBrandProductManagerService brandProductManagerService;


    @Override
    public void riskExport() {

        QueryWrapper<BrandProductManager> queryWrapper=new QueryWrapper();
        queryWrapper.lambda()
                .eq(BrandProductManager::getPId,0)
        ;
        List<BrandProductManager> bras= brandProductManagerService.list(queryWrapper);
        for (BrandProductManager bra : bras) {

            RiskEventInsightModel model = new RiskEventInsightModel();
            RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(CommonConstant.sys_risk_emotion_rule);
            List<RiskExportResultVo> list = baseMapper.riskExport(model, vo);
            ExecutorService executor = Executors.newFixedThreadPool(10);
        /*final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);

        model.setAccessToken(token);*/
            list.stream().forEach(e -> {

                        executor.submit(new Runnable() {
                            @Override
                            public void run() {
                                e.setTimeStr(DateUtils.dateToStr(e.getPublishDate()));
                                e.setStartDate(e.getTimeStr() + " 00:00:00");
                                e.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                                e.setStatisticTypeStr(CalculatorUtils.getPeriod(e.getStatisticType()));
                                e.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));
                                RiskEventInsightModel model1 = new RiskEventInsightModel();
//                            model1.setAccessToken(token);
                                model1.setStartDate(e.getStartDate());
                                model1.setEmotion("负面");
                                model1.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                                model1.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));
                                model1.setTopicCode(e.getTopicCode());
//                            e.setCarSeries(emotionUserDiService.riskCarSeriesExport(model1).toString());

//                            Long un=riskWarningUserNumExport(model1);
//                            e.setUserNum(un);


                                String[] riskne = e.getLabelAllCode().split("#");
                                String nam = "";
                                for (String s : riskne) {
                                    nam += tagService.getNameByCode(s) + "#";

                                }
                                e.setTopicCodeName(nam);

                            }
                        });


                    }
            );
            executor.shutdown();
            try {
                executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            String path = "/opt/apps/demo_voc/业务风险.xlsx";
            // 导出文件位置
            // 告诉其输出位置
            ExcelWriter excelWriter = EasyExcel.write(path).build();
            // 创建 sheet 构造器时通过head属性告诉其导出类型的模板
            WriteSheet build = EasyExcel.writerSheet("风险结果").head(RiskExportResultVo.class).build();
            excelWriter.write(list, build);
            excelWriter.finish();
        }


        RiskEventInsightModel model = new RiskEventInsightModel();
        RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(CommonConstant.sys_risk_emotion_rule);
        List<RiskExportResultVo> list = baseMapper.riskExport(model, vo);
        ExecutorService executor = Executors.newFixedThreadPool(10);
        /*final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);

        model.setAccessToken(token);*/
        list.stream().forEach(e -> {

                    executor.submit(new Runnable() {
                        @Override
                        public void run() {
                            e.setTimeStr(DateUtils.dateToStr(e.getPublishDate()));
                            e.setStartDate(e.getTimeStr() + " 00:00:00");
                            e.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                            e.setStatisticTypeStr(CalculatorUtils.getPeriod(e.getStatisticType()));
                            e.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));
                            RiskEventInsightModel model1 = new RiskEventInsightModel();
//                            model1.setAccessToken(token);
                            model1.setStartDate(e.getStartDate());
                            model1.setEmotion("负面");
                            model1.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                            model1.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));
                            model1.setTopicCode(e.getTopicCode());
//                            e.setCarSeries(emotionUserDiService.riskCarSeriesExport(model1).toString());

//                            Long un=riskWarningUserNumExport(model1);
//                            e.setUserNum(un);


                            String[] riskne = e.getLabelAllCode().split("#");
                            String nam = "";
                            for (String s : riskne) {
                                nam += tagService.getNameByCode(s) + "#";

                            }
                            e.setTopicCodeName(nam);

                        }
                    });


                }
        );
        executor.shutdown();
        try {
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS); // 等待所有任务完成
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String path = "/opt/apps/demo_voc/业务风险.xlsx";
        // 导出文件位置
        // 告诉其输出位置
        ExcelWriter excelWriter = EasyExcel.write(path).build();
        // 创建 sheet 构造器时通过head属性告诉其导出类型的模板
        WriteSheet build = EasyExcel.writerSheet("风险结果").head(RiskExportResultVo.class).build();
        excelWriter.write(list, build);
        excelWriter.finish();
    }

    @Override
    public List<DwdVocEmotionRisk> riskEventFilteringNew(VocRiskWarningRules rule, RiskRuleVo vo) {
        return baseMapper.riskEventFilteringNew(rule, vo);
    }

    private void setModelDate(FilterCriteriaModel model, Date publishTime, String statisticType) {
        if (statisticType.equals("d")) {
            model.setStartDate(IDateUtils.format(DateUtils.addDay(publishTime, -14)));
            model.setEndDate(DateUtils.formatTime(DateUtils.addDay(publishTime, +14)));
            model.setDateUnit(-1);
        } else if (statisticType.equals("w")) {
            Date start = DateUtil.offsetWeek(publishTime, -14);
            Date end = DateUtil.offsetWeek(publishTime, 1);
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(end));
        } else if (statisticType.equals("m")) {
            Date start = DateUtil.offsetMonth(publishTime, -14);
            Date end = DateUtil.offsetMonth(publishTime, 14);
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(end));
        } else if (statisticType.equals("q")) {
            Date start = DateUtil.offsetMonth(publishTime, -14 * 3);
            Date end = DateUtil.offsetMonth(publishTime, 14 * 3);
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(end));
        } else if(statisticType.equals("y")){
            Date start = DateUtil.offsetMonth(publishTime, -14 * 12);
            Date end = DateUtil.offsetMonth(publishTime, 14 * 12);
            model.setStartDate(DateUtils.formatTime(start));
            model.setEndDate(DateUtils.formatTime(end));
        }
        model.setDateUnit(CalculatorUtils.periodStrToNum(statisticType));

    }
}
