package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocEmotionDi;
import com.car.stats.entity.DwsVocEmotionUserDi;
import com.car.stats.mapper.DwsVocEmotionDiMapper;
import com.car.stats.mapper.DwsVocEmotionUserDiMapper;
import com.car.stats.mapper.DwsVocIntentionDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelSelectModel;
import com.car.stats.serivce.IDwsVocEmotionDiService;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.StatsCommonService;
import com.car.stats.vo.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.*;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.model.LoginUser;
import com.car.voc.service.ISysDictService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocChannelCategoryService;
import com.car.voc.vo.VocBusinessTagVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName DwsVocEmotionDiServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月17日 00:01
 * @Copyright voc
 */
@Service
@Slf4j
public class DwsVocEmotionDiServiceImpl extends ServiceImpl<DwsVocEmotionDiMapper, DwsVocEmotionDi> implements IDwsVocEmotionDiService {
    @Autowired
    ISysDictService dictService;
    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;
    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Resource
    DwsVocIntentionDiMapper intentionDiMapper;

    @Override
    public Result<?> soundInsightBriefingValue(FilterCriteriaModel model) {
        model.SetUpCycle();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        AtomicReference<Map<String, Long>> userFuture = new AtomicReference();
        AtomicReference<List<ChannelStatisticVo>> chansFuture = new AtomicReference();
        AtomicReference<VocOverBriefingValueVo> re1 = new AtomicReference();
        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            userFuture.set(emotionUserDiService.userNum(model));
            return null;
        })));
        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            chansFuture.set(dwsVocEmotionDiMapper.sourceChannel(model));
            return null;
        })));
        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            re1.set(dwsVocEmotionDiMapper.overviewBriefingValue(model));
            return null;
        })));

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        VocOverBriefingValueVo re;
        try {
            re = re1.get();
            if (ObjectUtil.isNull(re)) {
                re = new VocOverBriefingValueVo();
            }
            re.setTotalMentionsR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
            Map<String, Long> user = userFuture.get();
            if (user != null) {
                re.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
            }
            re.setUsersNumR(CalculatorUtils.ringRatio(re.getUsersNum(), ObjectUtil.isNull(user) ? null : BigDecimal.valueOf(user.get("userCountUp"))));
            List<ChannelStatisticVo> chans = chansFuture.get();
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            final List<ChannelStatisticVo> chansup = dwsVocEmotionDiMapper.sourceChannel(model);
            re.setChannelNum(BigDecimal.valueOf(chans.size()));
            re.setChannelNumR(new BigDecimal(chans.size() - chansup.size()));
            List<String> chStr = new ArrayList<>();
            for (int i = 0; i < chans.size(); i++) {
                chStr.add(chans.get(i).getDataSource());
            }
            re.setChannelTopStr(chStr);

            return Result.OK(re);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.error("操作失败！");
    }


    @Override
    public Result<EmotionProportionVo> focusProportionEmotion(FilterCriteriaModel model) {
        model.SetUpCycle();
        EmotionProportionVo re = dwsVocEmotionDiMapper.focusProportionEmotion(model);
        if (re == null) {
            re = new EmotionProportionVo();
        }
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        EmotionProportionVo reup = dwsVocEmotionDiMapper.focusProportionEmotion(model);
        if (reup == null) {
            reup = new EmotionProportionVo();
        }

        re.setNegativeP(CalculatorUtils.proportion(re.getNegative(), re.getTotal()));
        re.setNeutralP(CalculatorUtils.proportion(re.getNeutral(), re.getTotal()));
        re.setPositiveP(CalculatorUtils.proportion(re.getPositive(), re.getTotal()));

        re.setPositiveT(re.getPositiveP());
        re.setNegativeT(re.getNegativeP());
        re.setPositiveR(CalculatorUtils.ringRatio(re.getPositive(), reup.getPositive()));
        re.setNegativeR(CalculatorUtils.ringRatio(re.getNegative(), reup.getNegative()));
        re.setNeutralR(CalculatorUtils.ringRatio(re.getNeutral(), reup.getNeutral()));
        re.setNegativeTR(NumberUtil.sub(re.getNegativeT(), CalculatorUtils.proportion(reup.getNegative(), reup.getTotal())));
        re.setPositiveTR(NumberUtil.sub(re.getPositiveT(), CalculatorUtils.proportion(reup.getPositive(), reup.getTotal())));
        return Result.OK(re);
    }

    public String getUserId() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return loginUser.getId();
    }

    @Override
    public Result<?> themeDistribution(FilterCriteriaModel model) {

        List<ThemeDistrVo> objectsList = new ArrayList<>();

        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(objectsList);
        }


//        List<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
//        model.getSecondDimensionCodes().retainAll(sounps);
        List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
        model.setSecondDimensionCodes(interse);
//        if(model.getDateUnit() == -1){
//            model.setStartDate(DateUtil.parseDate(model.getEndDate()).toDateStr());
//        }

        if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            objectsList = dwsVocEmotionDiMapper.threeDistribution(model);
        } else if (model.getFirstDimensionCode() != null && !"".equals(model.getFirstDimensionCode())) {
            objectsList = dwsVocEmotionDiMapper.secondDistribution(model);
        } else {
            channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
            Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
                model.getSecondDimensionCodes().retainAll(sounps);
            } else {
                model.setSecondDimensionCodes(new ArrayList<>(sounps));
            }

            objectsList = dwsVocEmotionDiMapper.themeDistribution(model);
        }


        Optional<BigDecimal> userNum = Optional.of(BigDecimal.ZERO);
        if (model.getDataType().equals(DataEnum.numMention)) {
            VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(model);
            if (ObjectUtil.isNotNull(re)) {
                userNum = Optional.ofNullable(re.getTotalMentions());
            }
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            final Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(model);
            if (ObjectUtil.isNotNull(user) && user.containsKey("thisUserNum")) {
                userNum = Optional.of(user.get("thisUserNum"));
            }
        }

        final Optional<BigDecimal> finalUserNum = userNum;
        // 使用新的批量占比计算方法 - 这里需要特殊处理，因为分母是固定值
        BigDecimal userNumValue = finalUserNum.get();
        CalculatorUtils.proportion(objectsList, ThemeDistrVo::getStatistic, ThemeDistrVo::setStatisticP, userNumValue);

//        distrVos.forEach(e -> e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalTotal)));
        return Result.OK(objectsList);
    }

    @Override
    public Result<?> homeThemeDistribution(FilterCriteriaModel model) {
        List<ThemeDistrVo> distrVos = new ArrayList<>();
        FilterCriteriaModel usmodel = BeanUtil.copyProperties(model, FilterCriteriaModel.class);
        BigDecimal total = null;
        if (model.getDataType().equals(DataEnum.numUsers)) {
//            usmodel.setFirstDimensionCode(null);
//            usmodel.setThirdDimensionCode(null);
            total = emotionUserDiService.userDistinctNum(usmodel);
        }

        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(distrVos);
        }

        /*if (model.getDateUnit() == -1) {
            model.setStartDate(DateUtil.parseDate(model.getEndDate()).toDateStr());
        }*/

        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            Set<String> threes = new HashSet<>();
            threes.add(model.getThirdDimensionCode());
            model.setThreeDimensionCodes(threes);
            distrVos = dwsVocEmotionDiMapper.topicDistribution(model);

        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            if (model.getSecondDimensionCodes().size() > 1) {
                distrVos = dwsVocEmotionDiMapper.secondDistribution(model);
            } else {
                distrVos = dwsVocEmotionDiMapper.threeDistribution(model);
            }

        } else if (model.getFirstDimensionCode() != null && !"".equals(model.getFirstDimensionCode())) {
            distrVos = dwsVocEmotionDiMapper.secondDistribution(model);
        } else {
            List<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
            if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
                model.getSecondDimensionCodes().retainAll(sounps);
                List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
                model.setSecondDimensionCodes(interse);
            }
            model.setSecondDimensionCodes(sounps);
            distrVos = dwsVocEmotionDiMapper.themeDistribution(model);
        }
        if (model.getDataType().equals(DataEnum.numMention)) {
            total = distrVos.stream().map(ThemeDistrVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }
        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(distrVos, ThemeDistrVo::getStatistic, ThemeDistrVo::setStatisticP, total);
        return Result.OK(distrVos);
    }

    @Override
    public Result<?> themeShare_new(FilterCriteriaModel model) {
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        List<TrendLabelVo> objectLists = new ArrayList<>();
        List<LabelVo> list;
        if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            list = dwsVocEmotionDiMapper.threeThemeShare_new(model);
        } else if (model.getFirstDimensionCode() != null && !"".equals(model.getFirstDimensionCode())) {
            list = dwsVocEmotionDiMapper.secondThemeShare_new(model);
        } else {
            list = dwsVocEmotionDiMapper.themeShare_new(model);
        }
        Map<String, List<LabelVo>> grou = list.stream()
                .collect(Collectors.groupingBy(LabelVo::getDateStr, LinkedHashMap::new, Collectors.toList()));
        boolean a = true;
        for (Map.Entry<String, List<LabelVo>> date : grou.entrySet()) {
            if (a) {
                a = false;
                continue;
            }
            TrendLabelVo ar = new TrendLabelVo();
            BigDecimal total = date.getValue().stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            date.getValue().stream().forEach(e -> {
                e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), e.getStatisticUp()));
            });
            String dateStr;
            if (model.getDateUnit() != -1) {
                dateStr = date.getKey().replace("-0", "-");
            } else {
                dateStr = date.getKey();
            }
            ar.setDateStr(dateStr);
            ar.setList(date.getValue());
            objectLists.add(ar);
        }


        return Result.OK(objectLists);

    }

    @Override
    public Result<?> themeShare(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐

        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));


        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        final Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(Collections.EMPTY_LIST);
        }

//        Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
//        if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
//            model.getSecondDimensionCodes().retainAll(sounps);
//        } else {
//            model.setSecondDimensionCodes(new ArrayList<>(sounps));
//        }

//        Optional<BigDecimal> userNum = Optional.of(BigDecimal.ZERO);
//        if (model.getDataType().equals(DataEnum.numMention)) {
//            VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(model);
//            if (ObjectUtil.isNotNull(re)) {
//                userNum = Optional.ofNullable(re.getTotalMentions());
//            }
//        } else if (model.getDataType().equals(DataEnum.numUsers)) {
//            final Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(model);
//            if (ObjectUtil.isNotNull(user) && user.containsKey("thisUserNum")) {
//                userNum = Optional.of(user.get("thisUserNum"));
//            }
//        }

        List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
        model.setSecondDimensionCodes(interse);

        FilterCriteriaModel m = new FilterCriteriaModel();
        BeanUtil.copyProperties(model, m);

        if (ObjectUtil.isEmpty(m.getSecondDimensionCodes())) {
            Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(m.getSecondDimensionCodes())) {
                m.getSecondDimensionCodes().retainAll(sounps);
            } else {
                m.setSecondDimensionCodes(new ArrayList<>(sounps));
            }
        }

        List<TrendLabelVo> objectLists = new CopyOnWriteArrayList();
        Set<String> tempSet = new CopyOnWriteArraySet<>();  //存放所有ID用于补全展示数据
        Map<String, BigDecimal> dateP = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        List<TrendLabelVo> finalObjectLists = objectLists;
        final String token = SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        dates.stream().forEach(e -> {
            m.setAccessToken(token);
            FilterCriteriaModel m1 = new FilterCriteriaModel();
            BeanUtil.copyProperties(m, m1);
            m1.setStartDate(e.getStartDate());
            m1.setEndDate(e.getEndDate());
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {

                Optional<BigDecimal> num = Optional.of(BigDecimal.ZERO);
                if (m1.getDataType().equals(DataEnum.numMention)) {
                    final VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(m1);
                    if (ObjectUtil.isNotNull(re)) {
                        num = Optional.ofNullable(re.getTotalMentions());
                    }
                } else if (m1.getDataType().equals(DataEnum.numUsers)) {
                    final Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(m1);
                    if (ObjectUtil.isNotNull(user) && user.containsKey("thisUserNum")) {
                        num = Optional.ofNullable(user.get("thisUserNum"));
                    }
                }
                dateP.put(this.coveringDate(e.getTime()), num.get());
                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                List<LabelVo> objects;
                if (m1.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
                    objects = dwsVocEmotionDiMapper.threeThemeShare(m1);
                } else if (m1.getFirstDimensionCode() != null && !"".equals(m1.getFirstDimensionCode())) {
                    objects = dwsVocEmotionDiMapper.secondThemeShare(m1);
                } else {
                    Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(m1.getSecondDimensionCodes())) {
                        m1.getSecondDimensionCodes().retainAll(sounps);
                    } else {
                        m1.setSecondDimensionCodes(new ArrayList<>(sounps));
                    }
                    objects = dwsVocEmotionDiMapper.themeShare(m1);
                }

                if (CollUtil.isNotEmpty(objects)) {
                    tempSet.addAll(objects.stream().filter(e1 -> ObjectUtil.isNotNull(e1)).filter(e1 -> StrUtil.isNotBlank(e1.getLabelCode()))
                            .map(LabelVo::getLabelCode).collect(Collectors.toSet()));
                }

                TrendLabelVo one = new TrendLabelVo();
                one.setDateStr(e.getTime());
                one.setList(objects);
                finalObjectLists.add(one);
                return null;
            })));
        });

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    /*for (SvwDate date : new ArrayList<>(dates)) {
        FilterCriteriaModel criteriaModel = new FilterCriteriaModel();
        BeanUtil.copyProperties(model,criteriaModel);
        criteriaModel.setStartDate(date.getStartDate());
        criteriaModel.setEndDate(date.getEndDate());
        List<LabelVo> objects;

        if (criteriaModel.getSecondDimensionCodes() != criteriaModel && model.getSecondDimensionCodes().size() > 0) {
            objects = dwsVocEmotionDiMapper.threeThemeShare(criteriaModel);
        } else if (criteriaModel.getFirstDimensionCode() != null && !criteriaModel.getFirstDimensionCode().equals("")) {
            objects = dwsVocEmotionDiMapper.secondThemeShare(criteriaModel);
        } else {
            channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, criteriaModel.getBrandCode(), getUserId()));
            Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(criteriaModel.getSecondDimensionCodes())) {
                criteriaModel.getSecondDimensionCodes().retainAll(sounps);
            } else {
                criteriaModel.setSecondDimensionCodes(new ArrayList<>(sounps));
            }
            objects = dwsVocEmotionDiMapper.themeShare(criteriaModel);
        }

        if (CollUtil.isNotEmpty(objects)) {
            tempSet.addAll(objects.stream().filter(e -> ObjectUtil.isNotNull(e)).filter(e -> StrUtil.isNotBlank(e.getLabelCode()))
                    .map(LabelVo::getLabelCode).collect(Collectors.toSet()));
        }

        TrendLabelVo one = new TrendLabelVo();
        one.setDateStr(date.getTime());
        one.setList(objects);
        objectLists.add(one);
    }*/
        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> TrendLabelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );


        //拼装补全完整数据
        objectLists.stream().forEach(e -> {
            Set<String> containsIds = e.getList().stream().map(LabelVo::getLabelCode).collect(Collectors.toSet());
            if (CollUtil.isEmpty(e.getList())) {
                e.setList(new ArrayList<>());
            }
            e.getList().addAll(
                    tempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id -> LabelVo.builder().labelCode(id).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
            );
        });

//时间排序
        objectLists.sort(Comparator.comparing(TrendLabelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));


//        if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
//            objectsList = dwsVocEmotionDiMapper.threeDistribution(model);
//        } else if (model.getFirstDimensionCode() != null && !model.getFirstDimensionCode().equals("")) {
//            objectsList = dwsVocEmotionDiMapper.secondDistribution(model);
//        } else {
//            channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
//            Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
//            if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
//                model.getSecondDimensionCodes().retainAll(sounps);
//            } else {
//                model.setSecondDimensionCodes(new ArrayList<>(sounps));
//            }
//
//            objectsList = dwsVocEmotionDiMapper.themeDistribution(model);
//        }

//        final Optional<BigDecimal> finalUserNum = userNum;
        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            TrendLabelVo obj = objectLists.get(i);  //当前
            TrendLabelVo preObj = objectLists.get(i - 1);   //前一个
//            final BigDecimal total = obj.getList().stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, LabelVo> map = obj.getList().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            Map<String, LabelVo> preMap = preObj.getList().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));

            map.keySet().stream().forEach(id -> {
                LabelVo cur_ = map.get(id);
                LabelVo pre_ = preMap.get(id);
                final Long days = dateR.get(obj.getDateStr());
                final Long preDays = dateR.get(preObj.getDateStr());
                //设置日均 averagePerDay()
                if (model.getDateUnit().intValue() == -1) {
                    cur_.setStatisticA(BigDecimal.ZERO);
                } else {
                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_) ? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(preDays));
                    cur_.setStatisticA(objAvgDays);
                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
                }


                //占比
                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), dateP.get(obj.getDateStr())));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
//                cur_.setStatisticAR(CalculatorUtils.ringRatio(cur_.getStatisticA(),ObjectUtil.isNull(pre_)? null :  pre_.getStatisticA()));
            });
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s >= objectLists.size() ? 1 : s - index, objectLists.size()));
        }
        /*if(CollUtil.isNotEmpty(objectLists) ){
            Map<String, Object> map = new HashMap<>();
            map.put("themeShare",objectLists);
            map.put("themeDistribution",objectLists.get(objectLists.size()-1));
            return Result.OK(map);
        }*/

        return Result.OK(objectLists);

    }

    @Override
    public Result<?> hotWords(FilterCriteriaModel model) {
        model.setRownum(model.isExcel() ? model.getDownloadHotWordsNum() : null);
        List<HighHotWordsVo> hotWordsVos = dwsVocEmotionDiMapper.highFrequencyWords(model);
       /* if (model.getDataType().equals(DataEnum.numMention)) {
            hotWordsVos.sort(Comparator.comparing(HighHotWordsVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(HighHotWordsVo::getKeyword)
                    .reversed());
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            hotWordsVos.sort(Comparator.comparing(HighHotWordsVo::getUserCount, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(HighHotWordsVo::getKeyword)
                    .reversed());
        }*/

        return Result.OK(hotWordsVos);
    }

    @Override
    public Result<?> soarHotWords(FilterCriteriaModel model) {
        model.SetUpCycle();
        model.setRownum(1000);
        List<HighHotWordsVo> hotWordsVos = dwsVocEmotionDiMapper.highFrequencyWords(model);
        model.setHotWords(hotWordsVos.stream().map(HighHotWordsVo::getKeyword).collect(Collectors.toSet()));
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<HighHotWordsVo> hotWordsVosUp = dwsVocEmotionDiMapper.highFrequencyWords(model);

        BigDecimal btotal = hotWordsVos.stream().map(HighHotWordsVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<HighHotWordsVo> avgbefre;
        if (StrUtil.isNotBlank(model.getEmotion()) && "正面".equals(model.getEmotion())) {
            avgbefre = hotWordsVos;
        } else if (hotWordsVos != null && hotWordsVos.size() > 0) {
            avge = NumberUtil.div(btotal, hotWordsVos.size());
            avgbefre = hotWordsVos.stream().filter(e -> (e.getStatistic().compareTo(avge) > 0)).collect(Collectors.toList());
        } else {
            avgbefre = hotWordsVos;
        }


        List<HighHotWordsVo> jihe = new ArrayList<>();
        if (StrUtil.isNotBlank(model.getEmotion()) && "正面".equals(model.getEmotion())) {
            jihe = avgbefre;
            jihe = jihe.stream().peek(e -> e.setSoaringRate(BigDecimal.ZERO))
                    .sorted(Comparator.comparing(HighHotWordsVo::getStatistic).reversed()).collect(Collectors.toList());
        } else {
            //若大于平均值的标签的个数小于等于20，则取大于平均值的标签总个数的一半
            if (ObjectUtil.isNotEmpty(avgbefre) && avgbefre.size() <= 20) {
                jihe = hotWordsVos.subList(0, hotWordsVos.size() / 2);
            } else {
                jihe = avgbefre;
            }
            jihe.forEach(e -> {
                List<HighHotWordsVo> onet = hotWordsVosUp.stream().filter(d -> e.getKeyword().equals(d.getKeyword())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(onet)) {
                    e.setSoaringRate(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
                }

            });
            jihe = jihe.stream().filter(e -> e.getSoaringRate().floatValue() > 0)
                    .sorted(Comparator.comparing(HighHotWordsVo::getStatistic).reversed()).collect(Collectors.toList());
        }

        if (jihe.size() >= 20) {
            return Result.OK(new ArrayList<>(jihe.subList(0, 20)));
        } else {
            return Result.OK(jihe);
        }
    }

    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Autowired
    StatsCommonService statsCommonService;
    @Autowired
    RedisUtil redisUtil;

    @Override
    public Result<?> channelDistribution(FilterCriteriaModel model) {
        List<ChannelVo> objects = statsCommonService.setChannelAllList(model);
        BigDecimal total;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            model.SetUpCycle();
            final String token =
                    StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                            SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
            model.setAccessToken(token);
            ExecutorService executorService = Executors.newFixedThreadPool(1);
            Future<Map<String, Long>> userFuture = executorService.submit(() -> emotionUserDiService.userNum(model));
            VocOverBriefingValueVo re = new VocOverBriefingValueVo();
            try {
                Map<String, Long> user = userFuture.get();
                if (user != null) {
                    re.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
                }

            } catch (Exception e) {
                e.printStackTrace();
                // Handle exceptions
            } finally {
                executorService.shutdown();
            }
            total = re.getUsersNum();
        } else {
            total = objects.stream().map(ChannelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }

        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(objects, ChannelVo::getStatistic, ChannelVo::setStatisticP, total);
        objects.sort(Comparator.comparing(ChannelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(ChannelVo::getStatisticR)
                .reversed());
        redisUtil.set(CacheConstant.channelTop, objects);
        return Result.OK(objects);
    }

    @Override
    public Result<?> channelTrend(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        List<TrendChannelVo> objectLists = new ArrayList<>();

        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }

        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        Set<String> channelIdTempSet = new HashSet<>();  //存放所有ID用于补全展示数据
        for (SvwDate date : new ArrayList<>(dates)) {
            TrendChannelVo cha = new TrendChannelVo();
            cha.setDateStr(date.getTime());   //时间范围
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            List<ChannelVo> objects = Optional.ofNullable(statsCommonService.setChannelAllList(model)).orElse(new ArrayList<>());
            if (CollUtil.isNotEmpty(objects)) {
                channelIdTempSet.addAll(objects.stream().filter(e -> ObjectUtil.isNotNull(e)).filter(e -> StrUtil.isNotBlank(e.getChannelId()))
                        .map(ChannelVo::getChannelId).collect(Collectors.toSet()));
            }
            cha.setChannelVos(objects);
            objectLists.add(cha);
        }

        //拼装补全完整数据
        objectLists.stream().forEach(e -> {
            Set<String> containsIds = e.getChannelVos().stream().map(ChannelVo::getChannelId).collect(Collectors.toSet());
            if (CollUtil.isEmpty(e.getChannelVos())) {
                e.setChannelVos(new ArrayList<>());
            }
            e.getChannelVos().addAll(
                    channelIdTempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id -> ChannelVo.builder().channelId(id).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
            );
        });

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> TrendChannelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //补齐业务对象
        objectLists.stream().forEach(e -> {
            Set<String> containsIds = e.getChannelVos().stream().map(ChannelVo::getChannelId).collect(Collectors.toSet());
            e.getChannelVos().addAll(
                    channelIdTempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id -> ChannelVo.builder()
                                    .dateStr(e.getDateStr())
                                    .channelId(id).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
            );
        });

        //时间排序
        objectLists.sort(Comparator.comparing(TrendChannelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            TrendChannelVo obj = objectLists.get(i);  //当前
            TrendChannelVo preObj = objectLists.get(i - 1);   //前一个
            final BigDecimal total = obj.getChannelVos().stream().map(ChannelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, ChannelVo> map = obj.getChannelVos().stream().collect(Collectors.toMap(ChannelVo::getChannelId, e -> e, (k1, k2) -> k1));
            Map<String, ChannelVo> preMap = preObj.getChannelVos().stream().collect(Collectors.toMap(ChannelVo::getChannelId, e -> e, (k1, k2) -> k1));
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());

            map.keySet().stream().forEach(id -> {
                ChannelVo cur_ = map.get(id);
                ChannelVo pre_ = preMap.get(id);

                //设置日均 averagePerDay()
                if (model.getDateUnit().intValue() == -1) {
                    cur_.setStatisticAR(BigDecimal.ZERO);
                } else {
//                    final Long days = DateUtil.between(DateUtil.parse(model.getEndDate()), DateUtil.parse(model.getStartDate()), DateUnit.DAY) + 1;
                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_) ? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(preDays));
                    cur_.setStatisticA(objAvgDays);
                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
                }

                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), total));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
//                cur_.setStatisticAR(CalculatorUtils.ringRatio(cur_.getStatisticA(),ObjectUtil.isNull(pre_)? null :  pre_.getStatisticA()));
            });

        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });

        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(s >= objectLists.size() ? 1 : s - index, objectLists.size()));
        }
        return Result.OK(objectLists);
    }

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    private List<ChannelVo> setChannelAllList(FilterCriteriaModel model, List<String> datasous, List<String> channels) {
        List<ChannelVo> objects = new ArrayList<>();
        if (model.isCPoint()) {
            //查询私域
            model.setChannelId(CommonConstant.privateChannelId);
            model.setDataSources(null);
            model.setChannelIds(null);
            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));
            model.setChannelId(null);
        } else if (model.getChannelId() != null && !"".equals(model.getChannelId())) {
            model.setDataSources(null);
            model.setChannelIds(null);
            List<ChannelVo> list = dwsVocEmotionDiMapper.channelDistribution(model);
            list = list.stream().filter(s -> channels.contains(s.getChannelId())).collect(Collectors.toList());
            objects.addAll(list);
        } else {
            model.setDataSources(null);
            model.setChannelId(null);
            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));
        }

        return objects;
    }

    private List<ChannelVo> setChannelAllList(FilterCriteriaModel model) {

        List<ChannelVo> objects = new ArrayList<>();
        if (model.isCPoint()) {
            //查询私域
            model.setChannelId(CommonConstant.privateChannelId);
            model.setChannelIds(null);
//            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));
            model.setDataSources(Arrays.asList(CommonConstant.privateChannelId));
            objects.addAll(dwsVocEmotionDiMapper.channel2Distribution(model));


        } else if (model.getChannelId() != null && !"".equals(model.getChannelId())) {
            model.setDataSources(null);
            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));

        } else {
            model.setDataSources(null);
            model.setChannelId(null);
            objects.addAll(dwsVocEmotionDiMapper.channelDistribution(model));
        }

        return objects;
    }


    @Resource
    DwsVocEmotionUserDiMapper emotionUserDiMapper;

    @Autowired
    private IVocBusinessTagService vocBusinessTagService;

    @Override
    public Result<?> quantitytop(FilterCriteriaModel model) {
        model.SetUpCycle();
        QueryWrapper<VocChannelCategory> qw = new QueryWrapper<>();
        if ((model.getChannelIds() != null && model.getChannelIds().size() > 0)) {
            qw.lambda().in((model.getChannelIds() != null && model.getChannelIds().size() > 0), VocChannelCategory::getId, model.getChannelIds());
        } else if (model.getDataSources() != null && model.getDataSources().size() > 0) {
            qw.lambda().in(VocChannelCategory::getId, model.getDataSources());
        } else {
            qw.lambda().eq(VocChannelCategory::getPid, "0");
        }
        qw.lambda().orderByDesc(VocChannelCategory::getOrderBy);
        List<VocChannelCategory> channels = channelCategoryService.list(qw);
        List<VocChannelCategory> allChan = channelCategoryService.list();

//        model.setChannelIds(channels);
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        List<JSONObject> reslist = new ArrayList<>();
        List<String> chheaders = new ArrayList<>();
        List<VocChannelCategory> forQuerys = new ArrayList<>();

        for (VocChannelCategory channel : channels) {
            if (model.isCPoint()) {
                List<VocChannelCategory> sysj = allChan.stream().filter(k -> k.getPid().equals(channel.getId())).collect(Collectors.toList());
                sysj = sysj.stream().filter(s -> model.getChannelIds().contains(s.getId())).collect(Collectors.toList());
                sysj.stream().forEach(e -> e.setFlag("二"));
                forQuerys.addAll(sysj);
            } else if (StrUtil.isNotBlank(model.getChannelId())) {
                if (model.getChannelIds().contains(channel.getId())) {
                    channel.setFlag("二");
                    forQuerys.add(channel);
                }
            } else {
                List<VocChannelCategory> sysj = allChan.stream().filter(k -> k.getId().equals(channel.getId())).collect(Collectors.toList());
                sysj = sysj.stream().filter(s -> model.getChannelIds().contains(s.getId())).collect(Collectors.toList());
                sysj.stream().forEach(e -> e.setFlag("二"));
                forQuerys.addAll(sysj);
            }

        }
        for (VocChannelCategory chan : forQuerys) {
            String channel = chan.getId();
            chheaders.add(channel);

            model.setStartDate(model.getDateS());
            model.setEndDate(model.getDateE());
            List<MentiontopVo> list = new ArrayList<>();
            List<String> chs = new ArrayList<>();
            if ("二".equals(chan.getFlag())) {
                chs.add(channel);
                model.setChannelIds(chs);
                model.setDataSources(null);
                model.setTopicCodes(null);
                System.out.println("ssyy" + System.currentTimeMillis());
                list.addAll(dwsVocEmotionDiMapper.quantitytop2(model));
                System.out.println("ssyy" + System.currentTimeMillis());
            } else {
                chs.add(channel);
                model.setDataSources(chs);
                model.setChannelIds(null);
                model.setTopicCodes(null);
                System.out.println("yyss" + System.currentTimeMillis());
                list.addAll(dwsVocEmotionDiMapper.quantitytop2(model));
                System.out.println("yyss" + System.currentTimeMillis());
            }

            BigDecimal total = dwsVocEmotionDiMapper.sumQuantityTop(model);
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            model.setTopicCodes(list.stream().map(MentiontopVo::getTopicCode).collect(Collectors.toSet()));
            List<MentiontopVo> uplist = new ArrayList<>();
            if ("二".equals(chan.getFlag())) {
                chs.add(channel);
                model.setChannelIds(chs);
                model.setDataSources(null);
                uplist.addAll(dwsVocEmotionDiMapper.quantitytop2(model));
            } else {
                chs.add(channel);
                model.setDataSources(chs);
                model.setChannelIds(null);
                uplist.addAll(dwsVocEmotionDiMapper.quantitytop2(model));
            }

            AtomicReference<Integer> s = new AtomicReference<>(1);
            list.forEach(e -> {
                MentiontopVo up = uplist.stream().filter(c -> e.getTopicCode().equals(c.getTopicCode())).findFirst().orElse(null);
                e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), total));
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(up) ? null : up.getStatistic()));
                JSONObject exf = reslist.stream().filter(c -> e.getTopicCode().equals(c.getStr("topicCode"))).findFirst().orElse(null);
                if (exf == null) {
                    JSONObject object = JSONUtil.parseObj(e);
//                    object.putOpt(ve.getName(),new BigDecimal[]{new BigDecimal(s.get()),e.getStatisticP(),e.getStatisticR()});
                    object.putOpt(channel, new BigDecimal[]{new BigDecimal(s.get()), e.getStatisticP(), e.getStatisticR(), e.getStatistic()});
                    reslist.add(object);
                } else {
                    reslist.forEach(k -> {
                        if (k.getStr("topicCode").equals(e.getTopicCode())) {
//                            k.putOpt(ve.getName(),new BigDecimal[]{new BigDecimal(s.get()),e.getStatisticP(),e.getStatisticR()});
                            k.putOpt(channel, new BigDecimal[]{new BigDecimal(s.get()), e.getStatisticP(), e.getStatisticR(), e.getStatistic()});
                        }
                    });
                }
                s.getAndSet(s.get() + 1);
            });
        }
        List<VocBusinessTagVo> ls;
        QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocBusinessTag::getPid, "0");
        wrapper.lambda().orderByAsc(VocBusinessTag::getOrderBy);
        List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
        ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
        Map<String, List<JSONObject>> chesUp = reslist.stream().collect(Collectors.groupingBy(item -> item.getStr("firstDimensionCode")));
        List<JSONObject> reslistend = new ArrayList<>();
        for (VocBusinessTagVo l : ls) {
            List<JSONObject> df = chesUp.get(l.getTagCode());
            if (df != null) {
                df.sort(Comparator.comparingInt(o -> o.getInt("orderBy")));
                Map<String, List<JSONObject>> secondMap = df.stream().collect(Collectors.groupingBy(item -> item.getStr("secondDimensionCode")));
                secondMap.keySet().stream().forEach(s -> {
                    Map<String, List<JSONObject>> thirdMap = secondMap.get(s).stream().collect(Collectors.groupingBy(item -> item.getStr("threeDimensionCode")));
                    thirdMap.keySet().stream().forEach(t -> {
                        reslistend.addAll(thirdMap.get(t));
                    });
                });
//                reslistend.addAll(df);
            }

        }
        chheaders = chheaders.stream().filter(s -> reslistend.stream().filter(m -> m.containsKey(s)).findAny().orElse(null) != null).collect(Collectors.toList());
        Map<String, Object> re = new HashMap<>();
        re.put("data", reslistend);
        List<ChannelVo> topch = (List<ChannelVo>) redisUtil.get(CacheConstant.channelTop);
        List<String> chtop = new ArrayList<>();

        List<String> finalChheaders = chheaders;
        topch.stream().forEach(l -> {
            String vo = finalChheaders.stream().filter(s -> s.equals(l.getChannelId())).findFirst().orElse(null);
            if (vo != null) {
                chtop.add(vo);
            }
        });
        re.put("header", chtop);
        return Result.OK(re);
    }

    public Result<?> quantitytop444(FilterCriteriaModel model) {
        model.SetUpCycle();
        QueryWrapper<DwsVocEmotionUserDi> qw = new QueryWrapper<>();
        if ((model.getChannelIds() != null && model.getChannelIds().size() > 0)) {
            qw.lambda().in((model.getChannelIds() != null && model.getChannelIds().size() > 0), DwsVocEmotionUserDi::getChannelId, model.getChannelIds());
        }
        if (model.getDataSources() != null && model.getDataSources().size() > 0) {
            qw.lambda().in(DwsVocEmotionUserDi::getDataSource, model.getDataSources());

        } else {
            qw.lambda().eq(DwsVocEmotionUserDi::getDataSource, CommonConstant.privateChannelId);
        }
        qw.lambda().select(DwsVocEmotionUserDi::getChannelId);
        qw.lambda().groupBy(DwsVocEmotionUserDi::getChannelId);
        List<String> channels = (List<String>) (List) emotionUserDiMapper.selectObjs(qw);
        model.setChannelIds(channels);
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        List<JSONObject> reslist = new ArrayList<>();
        List<String> chheaders = new ArrayList<>();
        for (String channel : channels) {
//            VocChannelCategory ve=categories.stream().filter(c->channel.equals(c.getId())).findFirst().orElse(null);
            chheaders.add(channel);
            List<String> chs = new ArrayList<>();
            chs.add(channel);
            model.setChannelIds(chs);

            model.setStartDate(model.getDateS());
            model.setEndDate(model.getDateE());
            List<MentiontopVo> list = dwsVocEmotionDiMapper.quantitytop2(model);

            BigDecimal total = dwsVocEmotionDiMapper.sumQuantityTop(model);
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            model.setTopicCodes(list.stream().map(MentiontopVo::getTopicCode).collect(Collectors.toSet()));
            List<MentiontopVo> uplist = dwsVocEmotionDiMapper.quantitytop2(model);
            AtomicReference<Integer> s = new AtomicReference<>(1);
            // 使用新的批量占比计算方法
            CalculatorUtils.proportion(list, MentiontopVo::getStatistic, MentiontopVo::setStatisticP);

            // 处理环比计算
            list.forEach(e -> {
                MentiontopVo up = uplist.stream().filter(c -> e.getTopicCode().equals(c.getTopicCode())).findFirst().orElse(null);
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(up) ? null : up.getStatistic()));
                JSONObject exf = reslist.stream().filter(c -> e.getTopicCode().equals(c.getStr("topicCode"))).findFirst().orElse(null);
                if (exf == null) {
                    JSONObject object = JSONUtil.parseObj(e);
//                    object.putOpt(ve.getName(),new BigDecimal[]{new BigDecimal(s.get()),e.getStatisticP(),e.getStatisticR()});
                    object.putOpt(channel, new BigDecimal[]{new BigDecimal(s.get()), e.getStatisticP(), e.getStatisticR()});
                    reslist.add(object);
                } else {
                    reslist.forEach(k -> {
                        if (k.getStr("topicCode").equals(e.getTopicCode())) {
//                            k.putOpt(ve.getName(),new BigDecimal[]{new BigDecimal(s.get()),e.getStatisticP(),e.getStatisticR()});
                            k.putOpt(channel, new BigDecimal[]{new BigDecimal(s.get()), e.getStatisticP(), e.getStatisticR()});
                        }
                    });
                }
                s.getAndSet(s.get() + 1);
            });
        }
        List<VocBusinessTagVo> ls;
        QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocBusinessTag::getPid, "0");
        wrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
        List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
        ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
        Map<String, List<JSONObject>> chesUp = reslist.stream().collect(Collectors.groupingBy(item -> item.getStr("firstDimensionCode")));
        List<JSONObject> reslistend = new ArrayList<>();
        for (VocBusinessTagVo l : ls) {
            List<JSONObject> df = chesUp.get(l.getTagCode());
            if (df != null) {
//                Collections.sort(df,(JSONObject o1, JSONObject o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.get("secondDimension"),o2.get("secondDimension")));
                reslistend.addAll(df);
            }

        }
        Map<String, Object> re = new HashMap<>();
        re.put("data", reslistend);
        re.put("header", chheaders);
        return Result.OK(re);
    }

    public Result<?> quantitytop11(FilterCriteriaModel model) {
        model.SetUpCycle();
        QueryWrapper<DwsVocEmotionUserDi> qw = new QueryWrapper<>();
        if ((model.getChannelIds() != null && model.getChannelIds().size() > 0)) {
            qw.lambda().in((model.getChannelIds() != null && model.getChannelIds().size() > 0), DwsVocEmotionUserDi::getChannelId, model.getChannelIds());
        }
        if (model.getDataSources() != null && model.getDataSources().size() > 0) {
            qw.lambda().in(DwsVocEmotionUserDi::getDataSource, model.getDataSources());
        } else {
            qw.lambda().eq(DwsVocEmotionUserDi::getDataSource, CommonConstant.privateChannelId);
        }
        qw.lambda().select(DwsVocEmotionUserDi::getChannelId);
        qw.lambda().groupBy(DwsVocEmotionUserDi::getChannelId);
        List<String> channels = (List<String>) (List) emotionUserDiMapper.selectObjs(qw);
        model.setChannelIds(channels);
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        List<JSONObject> reslist = new ArrayList<>();
        List<String> chheaders = new ArrayList<>();
        for (String channel : channels) {
//            VocChannelCategory ve=categories.stream().filter(c->channel.equals(c.getId())).findFirst().orElse(null);
            chheaders.add(channel);
            List<String> chs = new ArrayList<>();
            chs.add(channel);
            model.setChannelIds(chs);
            model.setStartDate(model.getDateS());
            model.setEndDate(model.getDateE());
            List<MentiontopVo> list = dwsVocEmotionDiMapper.quantitytop2(model);
            BigDecimal total = dwsVocEmotionDiMapper.sumQuantityTop(model);
            model.setStartDate(model.getStartDateUp());
            model.setEndDate(model.getEndDateUp());
            model.setTopicCodes(list.stream().map(MentiontopVo::getTopicCode).collect(Collectors.toSet()));
            List<MentiontopVo> uplist = dwsVocEmotionDiMapper.quantitytop2(model);
            AtomicReference<Integer> s = new AtomicReference<>(1);
            // 使用新的批量占比计算方法
            CalculatorUtils.proportion(list, MentiontopVo::getStatistic, MentiontopVo::setStatisticP);

            // 处理环比计算
            list.forEach(e -> {
                MentiontopVo up = uplist.stream().filter(c -> e.getTopicCode().equals(c.getTopicCode())).findFirst().orElse(null);
                e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), ObjectUtil.isNull(up) ? null : up.getStatistic()));
                JSONObject exf = reslist.stream().filter(c -> e.getTopicCode().equals(c.getStr("topicCode"))).findFirst().orElse(null);
                if (exf == null) {
                    JSONObject object = JSONUtil.parseObj(e);
//                    object.putOpt(ve.getName(),new BigDecimal[]{new BigDecimal(s.get()),e.getStatisticP(),e.getStatisticR()});
                    object.putOpt(channel, new BigDecimal[]{new BigDecimal(s.get()), e.getStatisticP(), e.getStatisticR()});
                    reslist.add(object);
                } else {
                    reslist.forEach(k -> {
                        if (k.getStr("topicCode").equals(e.getTopicCode())) {
//                            k.putOpt(ve.getName(),new BigDecimal[]{new BigDecimal(s.get()),e.getStatisticP(),e.getStatisticR()});
                            k.putOpt(channel, new BigDecimal[]{new BigDecimal(s.get()), e.getStatisticP(), e.getStatisticR()});
                        }
                    });
                }
                s.getAndSet(s.get() + 1);
            });
        }
        List<VocBusinessTagVo> ls = null;
        QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocBusinessTag::getPid, "0");
        wrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
        List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
        ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
        Map<String, List<JSONObject>> chesUp = reslist.stream().collect(Collectors.groupingBy(item -> item.getStr("firstDimensionCode")));
        List<JSONObject> reslistend = new ArrayList<>();
        for (VocBusinessTagVo l : ls) {
            List<JSONObject> df = chesUp.get(l.getTagCode());
            if (df != null) {
//                Collections.sort(df,(JSONObject o1, JSONObject o2)-> Collator.getInstance(Locale.CHINESE).compare(o1.get("secondDimension"),o2.get("secondDimension")));
                reslistend.addAll(df);
            }

        }
        Map<String, Object> re = new HashMap<>();
        re.put("data", reslistend);
        re.put("header", chheaders);
        return Result.OK(re);
    }


    @Override
    public Result<?> analyEmotionMentionEate(LabelSelectModel model) {
        List<AnalyEmotionEateVo> list;
        if (StrUtil.isNotBlank(model.getThirdDimensionCode())) {
            list = dwsVocEmotionDiMapper.analyEmotionMentionEate4(model);
        } else if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0 && StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            list = dwsVocEmotionDiMapper.analyEmotionMentionEate3(model);
        } else if (StrUtil.isNotBlank(model.getSecondDimensionCode())) {
            list = dwsVocEmotionDiMapper.analyEmotionMentionEate3(model);
        } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            list = dwsVocEmotionDiMapper.analyEmotionMentionEate2(model);
        } else {
            list = dwsVocEmotionDiMapper.analyEmotionMentionEate(model);
        }
//        BigDecimal tagtotal = list.stream().map(AnalyEmotionEateVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        final BigDecimal totalPositive = list.stream().map(AnalyEmotionEateVo::getPositive).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal totalNegative = list.stream().map(AnalyEmotionEateVo::getNegative).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal tagtotal = totalPositive.add(totalNegative);
        list.forEach(e -> {
            e.setStatisticR(CalculatorUtils.proportion(e.getStatistic(), tagtotal));  //占比
            e.setPositiveP(CalculatorUtils.proportion(e.getPositive(), totalPositive));
            e.setNegativeP(CalculatorUtils.proportion(e.getNegative(), totalNegative));
            e.setPositiveR(CalculatorUtils.proportion(e.getPositive(), e.getStatistic()));
            e.setNegativeR(CalculatorUtils.proportion(e.getNegative(), e.getStatistic()));
        });
        Map<String, Object> re = new HashMap<>();
//        BigDecimal tjl = list.stream().map(AnalyEmotionEateVo::getStatisticR).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        final BigDecimal tm = list.stream().map(AnalyEmotionEateVo::getPositiveR).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        final BigDecimal fm = list.stream().map(AnalyEmotionEateVo::getNegativeR).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        if (list.size() > 0) {
//            re.put("mentionAverage", NumberUtil.round(NumberUtil.div(tjl, list.size()), 2));
            re.put("positiveAverage", NumberUtil.round(NumberUtil.div(tm, list.size()), 2));
            re.put("negativeAverage", NumberUtil.round(NumberUtil.div(fm, list.size()), 2));
        } else {
            re.put("positiveAverage", 0);
            re.put("negativeAverage", 0);
        }

        final Map<String, List<AnalyEmotionEateVo>> tmMap = list.stream()
                .filter(e -> ObjectUtil.isNotNull(e.getPositive()))
                .collect(Collectors.groupingBy(AnalyEmotionEateVo::getLabelCode));
        final Map<String, List<AnalyEmotionEateVo>> fmMap = list.stream()
                .filter(e -> ObjectUtil.isNotNull(e.getNegative()))
                .collect(Collectors.groupingBy(AnalyEmotionEateVo::getLabelCode));
        if (CollUtil.isNotEmpty(tmMap)) {
            re.put("positiveMentionA", NumberUtil.round(NumberUtil.div(1, tmMap.size()) * 100, 2));
        } else {
            re.put("positiveMentionA", 0);
        }
        if (CollUtil.isNotEmpty(fmMap)) {
            re.put("negativeMentionA", NumberUtil.round(NumberUtil.div(1, fmMap.size()) * 100, 2));
        } else {
            re.put("negativeMentionA", 0);
        }

        list.sort(Comparator.comparing(AnalyEmotionEateVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(AnalyEmotionEateVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo))
                .reversed());

        re.put("list", list);
        return Result.OK(re);
    }

    @Override
    public Result<?> complaintWebsiteChannelTrend(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        List<TrendChannelVo> objects = new ArrayList<>();
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if (model.getDateUnit() != -1) {
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList(si > 0 ? si : 0, dates.size());
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));


        model.setStartDate(dates.get(0).getStartDate());
        model.setEndDate(dates.get(dates.size() - 1).getEndDate());
        List<ChannelVo> objectLists = dwsVocEmotionDiMapper.complaintWebsiteChannelTrend(model);
        Map<String, List<ChannelVo>> maps = objectLists.stream().filter(s -> s.getDateStr() != null && !"".equals(s.getDateStr())).collect(Collectors.groupingBy(ChannelVo::getDateStr));
        List<String> mapKey = model.getMapKey(dates);
        if (model.getDateUnit() == 3) {
            mapKey = mapKey.stream().sorted(Comparator.naturalOrder()).map(String::valueOf).collect(Collectors.toList());
        } else {
            mapKey = mapKey.stream().sorted(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    Date d1 = IDateUtils.parse(IDateUtils.convertTime(o1), Constants.YYYY_MM_DD_HH_MM_SS);
                    Date d2 = IDateUtils.parse(IDateUtils.convertTime(o2), Constants.YYYY_MM_DD_HH_MM_SS);
                    return d1.compareTo(d2);
                }
            }).collect(Collectors.toList());
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> ChannelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(ChannelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        final BigDecimal total = objectLists.stream().map(ChannelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        //计算
        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            ChannelVo obj = objectLists.get(i);  //当前
            ChannelVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setStatisticAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getStatistic(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getStatistic(), new BigDecimal(preDays));
                obj.setStatisticA(objAvgDays);
                obj.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }


            obj.setStatisticP(CalculatorUtils.proportion(obj.getStatistic(), total));
            obj.setChannelStr(channelCategoryService.getById(obj.getChannelId()).getName());
//            obj.setStatisticR(CalculatorUtils.ringRatio(obj.getStatistic(),  eq.get(0).getStatistic()));
        }

        return Result.OK(objects);

    }

    private List<VocChannelCategory> getChannelCategories(FilterCriteriaModel model, List<VocChannelCategory> chall) {
        if (model.isCPoint()) {
            return chall.stream().filter(e -> CommonConstant.privateChannelId.equals(e.getPid())).collect(Collectors.toList());
        } else if (model.getDataSources() == null && StrUtil.isBlankIfStr(model.getChannelId())) {
            return chall.stream().filter(e -> (CommonConstant.privateChannelId.equals(e.getPid()) || e.getPid().equals(CommonConstant.publicSphereId))).collect(Collectors.toList());
        } else if (model.getChannelIds() == null && model.getChannelId().equals(CommonConstant.publicSphereId)) {
            return chall.stream().filter(e -> e.getPid().equals(model.getChannelId())).collect(Collectors.toList());
        } else if (model.getChannelIds().size() > 0 && StrUtil.isBlankIfStr(model.getChannelId())) {
            return chall.stream().filter(e -> model.getChannelIds().contains(e.getId())).collect(Collectors.toList());
        } else if (model.getChannelIds().size() > 0 && !StrUtil.isBlankIfStr(model.getChannelId())) {
            return chall.stream().filter(e -> model.getChannelIds().contains(e.getId())).collect(Collectors.toList());
        } else if (StrUtil.isNotBlank(model.getChannelId())) {
            return chall.stream().filter(e -> e.getPid().equals(model.getChannelId())).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public Result<?> channelQuantityTop(FilterCriteriaModel model1) {
        //修改环比计算+时间范围补齐
//        List<LinkedHashMap<String, Object>> maps = Collections.synchronizedList(new ArrayList<>());
        List<VocChannelCategory> chall = channelCategoryService.cacheList();
//        ExecutorService executor = Executors.newFixedThreadPool(50); // 创建一个线程池
        List<VocChannelCategory> ch2 = getChannelCategories(model1, chall);


        List<SvwDate> dates = model1.getDateTimes();
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()),
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        final List<ChannelVo> channelAllList = statsCommonService.setChannelAllList(model1);
        if (ObjectUtil.isNull(channelAllList)) {
            return Result.OK(Collections.EMPTY_LIST);
        }
        //[渠道，提及量]、[渠道，用户量]
        final Map<String, BigDecimal> totalMap = channelAllList.stream()
                .collect(Collectors.toMap(ChannelVo::getChannelId, ChannelVo::getStatistic, (k1, k2) -> k1));


        final Set<String> channelIds = channelAllList.stream().map(ChannelVo::getChannelId).collect(Collectors.toSet());

        List<ChannelQuantityTopVo> objectLists = new CopyOnWriteArrayList<>();
        List<ChannelQuantityTopVo> objectListsPre = new CopyOnWriteArrayList<>();
        final String token = SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model1.setAccessToken(token);

        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        ch2.stream().filter(e -> channelIds.contains(e.getId()))
                .forEach(vocChannelCategory -> {

                    futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                        FilterCriteriaModel target = new FilterCriteriaModel();
                        BeanUtils.copyProperties(model1, target);// 使用Spring的BeanUtils.copyProperties()方法来复制对象，保证线程安全
                        target.setAccessToken(token);

                        if (target.isCPoint()) {
                            target.setChannelIds(chall.stream().filter(e -> e.getPid().equals(vocChannelCategory.getId())).map(VocChannelCategory::getId).collect(Collectors.toList()));
                        } else if (target.getDataSources() == null && StrUtil.isBlankIfStr(target.getChannelId())) {
//                            target.setDataSources(Arrays.asList(new String[]{vocChannelCategory.getId()}));
                        } else if (target.getChannelIds() == null && target.getChannelId().equals(CommonConstant.publicSphereId)) {
                            List<VocChannelCategory> ch3s = new ArrayList<>();
                            ch3s.addAll(chall.stream().filter(l -> l.getPid().equals(vocChannelCategory.getId())).collect(Collectors.toList()));
                            target.setChannelIds(ch3s.stream().map(VocChannelCategory::getId).collect(Collectors.toList()));
                            target.setChannelId(null);
                        } else if (target.getChannelIds().size() > 0 && StrUtil.isBlankIfStr(target.getChannelId())) {
                            target.setChannelIds(Arrays.asList(new String[]{vocChannelCategory.getId()}));

                        } else if (target.getChannelIds().size() > 0 && !StrUtil.isBlankIfStr(target.getChannelId())) {
                            target.setChannelIds(Arrays.asList(new String[]{vocChannelCategory.getId()}));

                        } else if (StrUtil.isNotBlank(target.getChannelId())) {
                            target.setChannelIds(Arrays.asList(new String[]{vocChannelCategory.getId()}));
                        }
                        if (StrUtil.isBlankIfStr(target.getChannelId()) && target.getChannelIds().size() == 0) {
                            List<VocChannelCategory> ch3s = new ArrayList<>();
                            ch3s.addAll(chall.stream().filter(l -> l.getPid().equals(vocChannelCategory.getId())).collect(Collectors.toList()));
                            target.setChannelIds(ch3s.stream().map(VocChannelCategory::getId).collect(Collectors.toList()));
                            target.setChannelId(null);
                        }

                        ChannelQuantityTopVo vo = new ChannelQuantityTopVo();
                        vo.setChannelId(vocChannelCategory.getId());
                        target.setRownum(10000);
                        List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(target);
                        vo.setLableTop(vos);
                        final Set<String> labelCodeList = vos.stream().map(LabelVo::getLabelCode).collect(Collectors.toSet());
                        objectLists.add(vo);

                        ChannelQuantityTopVo voPre = new ChannelQuantityTopVo();
                        voPre.setChannelId(vocChannelCategory.getId());
                        target.SetUpCycle();
                        target.setStartDate(target.getStartDateUp());
                        target.setEndDate(target.getEndDateUp());
                        target.setTopicCodes(labelCodeList);
                        List<LabelVo> vosPre = dwsVocEmotionDiMapper.channelQuantityTop(target);
                        voPre.setLableTop(vosPre);
                        objectListsPre.add(voPre);
                        return null;
                    })));
                });


        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        final Map<String, ChannelQuantityTopVo> preVoMap = objectListsPre.stream().collect(Collectors.toMap(ChannelQuantityTopVo::getChannelId, e -> e, (k1, k2) -> k1));


        objectLists.stream().forEach(obj -> {
            final ChannelQuantityTopVo preObj = preVoMap.get(obj.getChannelId());   //上一周期

            final BigDecimal total = obj.getLableTop().stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, LabelVo> map = obj.getLableTop().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            Map<String, LabelVo> preMap = preObj.getLableTop().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            obj.setTotal(total);
            final BigDecimal totalDiv = totalMap.get(obj.getChannelId());
            map.keySet().stream().forEach(id -> {
                LabelVo cur_ = map.get(id);
                LabelVo pre_ = preMap.get(id);


                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), totalDiv));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
            });
        });
        objectLists.stream().forEach(e -> {
            List<LabelVo> list = e.getLableTop();
            list.sort(Comparator.comparing(LabelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(LabelVo::getStatisticP, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(LabelVo::getLabelCode, Comparator.nullsFirst(String::compareTo))
                    .reversed());
            e.setLableTop(CollUtil.sub(list, 0, 10));
        });

        objectLists.sort(Comparator.comparing(ChannelQuantityTopVo::getTotal, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());

        return Result.OK(objectLists);


    }

    @Override
    public Result<?> channelQuantityTop_new(FilterCriteriaModel model1) {
        List<VocChannelCategory> chall = channelCategoryService.cacheList();

        List<VocChannelCategory> ch2 = getChannelCategories(model1, chall);

        List<SvwDate> dates = model1.getDateTimes();


        final List<ChannelVo> channelAllList = statsCommonService.setChannelAllList(model1);
        if (ObjectUtil.isNull(channelAllList)) {
            return Result.OK(Collections.EMPTY_LIST);
        }
        //[渠道，提及量]、[渠道，用户量]
        final Map<String, BigDecimal> totalMap = channelAllList.stream()
                .collect(Collectors.toMap(ChannelVo::getChannelId, ChannelVo::getStatistic, (k1, k2) -> k1));


        final Set<String> channelIds = channelAllList.stream().map(ChannelVo::getChannelId).collect(Collectors.toSet());

        List<ChannelQuantityTopVo> objectLists = new CopyOnWriteArrayList<>();
        List<ChannelQuantityTopVo> objectListsPre = new CopyOnWriteArrayList<>();
        final String token = SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model1.setAccessToken(token);

        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        ch2.stream().filter(e -> channelIds.contains(e.getId()))
                .forEach(vocChannelCategory -> {

                    futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                        FilterCriteriaModel target = new FilterCriteriaModel();
                        BeanUtils.copyProperties(model1, target);// 使用Spring的BeanUtils.copyProperties()方法来复制对象，保证线程安全
                        target.setAccessToken(token);

                        if (target.isCPoint()) {
                            target.setChannelIds(chall.stream().filter(e -> e.getPid().equals(vocChannelCategory.getId())).map(VocChannelCategory::getId).collect(Collectors.toList()));
                        } else if (target.getDataSources() == null && StrUtil.isBlankIfStr(target.getChannelId())) {
                            target.setDataSources(Arrays.asList(new String[]{vocChannelCategory.getId()}));
                        } else if (target.getChannelIds() == null && target.getChannelId().equals(CommonConstant.publicSphereId)) {
                            List<VocChannelCategory> ch3s = new ArrayList<>();
                            ch3s.addAll(chall.stream().filter(l -> l.getPid().equals(vocChannelCategory.getId())).collect(Collectors.toList()));
                            target.setChannelIds(ch3s.stream().map(VocChannelCategory::getId).collect(Collectors.toList()));
                            target.setChannelId(null);
                        } else if (target.getChannelIds().size() > 0 && StrUtil.isBlankIfStr(target.getChannelId())) {
                            target.setChannelIds(Arrays.asList(new String[]{vocChannelCategory.getId()}));

                        } else if (target.getChannelIds().size() > 0 && !StrUtil.isBlankIfStr(target.getChannelId())) {
                            target.setChannelIds(Arrays.asList(new String[]{vocChannelCategory.getId()}));

                        } else if (StrUtil.isNotBlank(target.getChannelId())) {
                            target.setChannelIds(Arrays.asList(new String[]{vocChannelCategory.getId()}));

                        }

                        ChannelQuantityTopVo vo = new ChannelQuantityTopVo();
                        vo.setChannelId(vocChannelCategory.getId());
                        target.setRownum(10000);
                        List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(target);
                        vo.setLableTop(vos);
                        final Set<String> labelCodeList = vos.stream().map(LabelVo::getLabelCode).collect(Collectors.toSet());
                        objectLists.add(vo);

                        ChannelQuantityTopVo voPre = new ChannelQuantityTopVo();
                        voPre.setChannelId(vocChannelCategory.getId());
                        target.SetUpCycle();
                        target.setStartDate(target.getStartDateUp());
                        target.setEndDate(target.getEndDateUp());
                        target.setTopicCodes(labelCodeList);
                        List<LabelVo> vosPre = dwsVocEmotionDiMapper.channelQuantityTop(target);
                        voPre.setLableTop(vosPre);
                        objectListsPre.add(voPre);
                        return null;
                    })));
                });


        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        final Map<String, ChannelQuantityTopVo> preVoMap = objectListsPre.stream().collect(Collectors.toMap(ChannelQuantityTopVo::getChannelId, e -> e, (k1, k2) -> k1));


        objectLists.stream().forEach(obj -> {
            final ChannelQuantityTopVo preObj = preVoMap.get(obj.getChannelId());   //上一周期

            final BigDecimal total = obj.getLableTop().stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, LabelVo> map = obj.getLableTop().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            Map<String, LabelVo> preMap = preObj.getLableTop().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            obj.setTotal(total);
            final BigDecimal totalDiv = totalMap.get(obj.getChannelId());
            map.keySet().stream().forEach(id -> {
                LabelVo cur_ = map.get(id);
                LabelVo pre_ = preMap.get(id);


                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), totalDiv));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
            });
        });
        objectLists.stream().forEach(e -> {
            List<LabelVo> list = e.getLableTop();
            list.sort(Comparator.comparing(LabelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(LabelVo::getStatisticP, Comparator.nullsFirst(BigDecimal::compareTo))
                    .thenComparing(LabelVo::getLabelCode, Comparator.nullsFirst(String::compareTo))
                    .reversed());
            e.setLableTop(CollUtil.sub(list, 0, 10));
        });

        objectLists.sort(Comparator.comparing(ChannelQuantityTopVo::getTotal, Comparator.nullsFirst(BigDecimal::compareTo)).reversed());

        return Result.OK(objectLists);


    }


    public Result<?> channelQuantityTop_old(FilterCriteriaModel model) {
        List<LinkedHashMap<String, Object>> maps = new ArrayList<>();
        List<VocChannelCategory> chall = channelCategoryService.cacheList();
        List<VocChannelCategory> ch2;
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        ExecutorService executor = Executors.newFixedThreadPool(10); // 创建一个线程池

        if (model.isCPoint()) {//默认选择私域数据
            ch2 = chall.stream().filter(e -> CommonConstant.privateChannelId.equals(e.getPid())).collect(Collectors.toList());
            for (VocChannelCategory vocChannelCategory : ch2) {
                LinkedHashMap<String, Object> chtop = new LinkedHashMap<>();
                chtop.put("channelId", vocChannelCategory.getId());
                model.setChannelIds(chall.stream().filter(e -> e.getPid().equals(vocChannelCategory.getId())).map(VocChannelCategory::getId).collect(Collectors.toList()));
                model.SetUpCycle();
                List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(model);

                if (vos.size() <= 0) {
                    continue;
                }
                vos.stream().forEach(e -> {
                    e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
                    e.setStatisticR(NumberUtil.round(NumberUtil.mul(e.getStatisticR(), 100), 2, RoundingMode.HALF_UP));
                });
                chtop.put("lableTop", vos);

                maps.add(chtop);
            }

        } else if (model.getDataSources() == null && StrUtil.isBlankIfStr(model.getChannelId())) {//全部时
            ch2 = chall.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
            for (VocChannelCategory chv : ch2) {
                LinkedHashMap<String, Object> chtop = new LinkedHashMap<>();
                chtop.put("channelId", chv.getId());
                model.setDataSources(Arrays.asList(new String[]{chv.getId()}));
                model.SetUpCycle();
                List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(model);
                if (vos.size() <= 0) {
                    continue;
                }
                vos.stream().forEach(e -> {
                    e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
                    e.setStatisticR(NumberUtil.round(NumberUtil.mul(e.getStatisticR(), 100), 2, RoundingMode.HALF_UP));
                });
                chtop.put("lableTop", vos);

                maps.add(chtop);
            }
        } else if (model.getChannelIds() == null && model.getChannelId().equals(CommonConstant.publicSphereId)) {//公域时二级
            ch2 = chall.stream().filter(e -> e.getPid().equals(model.getChannelId())).collect(Collectors.toList());
//            List<LabelVo> vos1=dwsVocEmotionDiMapper.channelQuantityTop2(model);
            for (VocChannelCategory voc : ch2) {
                List<VocChannelCategory> ch3s = new ArrayList<>();
                ch3s.addAll(chall.stream().filter(l -> l.getPid().equals(voc.getId())).collect(Collectors.toList()));
                LinkedHashMap<String, Object> chtop = new LinkedHashMap<>();
                chtop.put("channelId", voc.getId());
                model.setChannelIds(ch3s.stream().map(VocChannelCategory::getId).collect(Collectors.toList()));
                model.SetUpCycle();
                model.setChannelId(null);
                List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(model);
                if (vos.size() <= 0) {
                    continue;
                }
                vos.stream().forEach(e -> {
                    e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
                    e.setStatisticR(NumberUtil.round(NumberUtil.mul(e.getStatisticR(), 100), 2, RoundingMode.HALF_UP));
                });
                chtop.put("lableTop", vos);

                maps.add(chtop);
            }

        } else if (model.getChannelIds().size() > 0 && StrUtil.isBlankIfStr(model.getChannelId())) {//公域时三级
            int s = 0;
            for (String channelId : model.getChannelIds()) {
                s++;
                if (s >= 9) {
                    break;
                }
                LinkedHashMap<String, Object> chtop = new LinkedHashMap<>();
                chtop.put("channelId", channelId);
                model.setChannelIds(Arrays.asList(new String[]{channelId}));
                model.SetUpCycle();

                executor.execute(() -> {
                    List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(model);
                    if (vos.size() > 0) {
                        vos.stream().forEach(e -> {
                            e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
                            e.setStatisticR(NumberUtil.round(NumberUtil.mul(e.getStatisticR(), 100), 2, RoundingMode.HALF_UP));
                        });
                        chtop.put("lableTop", vos);
                        maps.add(chtop);
                    }
                });

            }
        } else if (model.getChannelIds().size() > 0 && !StrUtil.isBlankIfStr(model.getChannelId())) {
            for (String channelId : model.getChannelIds()) {
                LinkedHashMap<String, Object> chtop = new LinkedHashMap<>();
                chtop.put("channelId", channelId);
                model.setChannelIds(Arrays.asList(new String[]{channelId}));
                model.SetUpCycle();
                List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(model);
                if (vos.size() <= 0) {
                    continue;
                }
                vos.stream().forEach(e -> {
                    e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
                    e.setStatisticR(NumberUtil.round(NumberUtil.mul(e.getStatisticR(), 100), 2, RoundingMode.HALF_UP));
                });
                chtop.put("lableTop", vos);

                maps.add(chtop);
            }
        } else if (StrUtil.isNotBlank(model.getChannelId())) {
            ch2 = chall.stream().filter(e -> e.getPid().equals(model.getChannelId())).collect(Collectors.toList());
            for (VocChannelCategory chv : ch2) {
                LinkedHashMap<String, Object> chtop = new LinkedHashMap<>();
                chtop.put("channelId", chv.getId());
                model.setChannelIds(Arrays.asList(new String[]{chv.getId()}));
                model.SetUpCycle();
                List<LabelVo> vos = dwsVocEmotionDiMapper.channelQuantityTop(model);
                if (vos.size() <= 0) {
                    continue;
                }
                vos.stream().forEach(e -> {
                    e.setStatisticP(NumberUtil.round(NumberUtil.mul(e.getStatisticP(), 100), 2, RoundingMode.HALF_UP));
                    e.setStatisticR(NumberUtil.round(NumberUtil.mul(e.getStatisticR(), 100), 2, RoundingMode.HALF_UP));
                });
                chtop.put("lableTop", vos);

                maps.add(chtop);
            }
        }

        executor.shutdown(); // 关闭线程池
        while (!executor.isTerminated()) {
            // 等待所有任务完成
        }

        Comparator<LinkedHashMap<String, Object>> comparator = new Comparator<LinkedHashMap<String, Object>>() {
            @Override
            public int compare(LinkedHashMap<String, Object> channel1, LinkedHashMap<String, Object> channel2) {
                List<LabelVo> vos1 = (List<LabelVo>) channel1.get("lableTop");
                List<LabelVo> vos2 = (List<LabelVo>) channel2.get("lableTop");

                BigDecimal sum1 = vos1.stream()
                        .map(LabelVo::getStatistic)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sum2 = vos2.stream()
                        .map(LabelVo::getStatistic)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                return sum2.compareTo(sum1); // 从大到小排序
            }
        };

        maps.sort(comparator);


        return Result.OK(maps);


    }


}
