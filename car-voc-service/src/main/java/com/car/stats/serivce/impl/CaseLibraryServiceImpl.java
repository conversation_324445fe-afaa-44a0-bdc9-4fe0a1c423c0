package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.CaseLibrary;
import com.car.stats.entity.CaseUserLikes;
import com.car.stats.mapper.CaseLibraryMapper;
import com.car.stats.mapper.CaseUserLikesMapper;
import com.car.stats.model.CaseLibraryModel;
import com.car.stats.serivce.ICaseLibraryService;
import com.car.stats.vo.CaseLibraryListVo;
import com.car.stats.vo.CaseReportVo;
import com.car.voc.common.Result;
import com.car.voc.entity.*;
import com.car.voc.exception.BootException;
import com.car.voc.mapper.BrandProductManagerMapper;
import com.car.voc.mapper.SysCaseClassifyMapper;
import com.car.voc.mapper.SysUserDepartMapper;
import com.car.voc.mapper.SysUserMapper;
import com.car.voc.service.CommonService;
import com.car.voc.service.impl.SysUserDepartServiceImpl;
import com.car.voc.vo.SysCaseClassifyListVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * @Description 案例库 服务实现类
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
@Service
@Slf4j
public class CaseLibraryServiceImpl extends ServiceImpl<CaseLibraryMapper, CaseLibrary> implements ICaseLibraryService {

    @Autowired
    private CaseLibraryMapper caseLibraryMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserDepartMapper sysUserDepartMapper;

    @Autowired
    private SysCaseClassifyMapper sysCaseClassifyMapper;

    @Autowired
    private CaseUserLikesMapper caseUserLikesMapper;

    @Autowired
    private BrandProductManagerMapper brandProductManagerMapper;


    @Override
    public Result<IPage<CaseLibraryListVo>> queryPageList(CaseLibraryModel caseLibraryModel, Integer pageNo, Integer pageSize, HttpServletRequest req,String type) {
        CaseLibraryListVo caseLibraryListVo = new CaseLibraryListVo();
        BeanUtils.copyProperties(caseLibraryModel,caseLibraryListVo);

        Result<IPage<CaseLibraryListVo>> result = new Result<IPage<CaseLibraryListVo>>();

        QueryWrapper<CaseLibrary> queryWrapper =new QueryWrapper<>();
        Page<CaseLibrary> page = new Page<CaseLibrary>(pageNo, pageSize);
        //我的分享默认登陆人查询 type = 1
        if("1".equals(type)){
            String username = CommonService.getUserNameByToken(req);
            SysUser sysUser = sysUserMapper.getUserByName(username);
            queryWrapper.lambda().eq(CaseLibrary::getCreateBy,sysUser.getRealname());
        }



        //时间排序，0升序，1降序
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getDateSort())){
            if("0".equals(caseLibraryModel.getDateSort())){
                queryWrapper.lambda().orderByAsc(CaseLibrary::getUpdateTime);
            }else if("1".equals(caseLibraryModel.getDateSort())){
                queryWrapper.lambda().orderByDesc(CaseLibrary::getUpdateTime);
            }
        }else{
            //默认创建时间降序
            queryWrapper.lambda().orderByDesc(CaseLibrary::getUpdateTime);
        }

        //关键词 主题/简述/总结
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getKeyword())){
//            queryWrapper.lambda().like(CaseLibrary::getName,caseLibraryModel.getKeyword())
//                    .or().like(CaseLibrary::getSketch,caseLibraryModel.getKeyword())
//                    .or().like(CaseLibrary::getContent,caseLibraryModel.getKeyword());

            queryWrapper.and(Wrapper -> Wrapper.like("name", caseLibraryModel.getKeyword()).or().like("sketch", caseLibraryModel.getKeyword()).like("content", caseLibraryModel.getKeyword()));
        }
        //具体案例分类的案例
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getCaseClassifyId())){
            queryWrapper.lambda().eq(CaseLibrary::getCaseClassifyId,caseLibraryModel.getCaseClassifyId());
        }
        //案例列表：通过审核  时间升降序/分类/关键词搜索
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getStatus())){
            queryWrapper.lambda().eq(CaseLibrary::getStatus,caseLibraryModel.getStatus());
        }
        //推荐
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getRecommend())){
            queryWrapper.lambda().eq(CaseLibrary::getRecommend,caseLibraryModel.getRecommend());
        }

        IPage<CaseLibrary> pageList = this.page(page, queryWrapper);
        IPage<CaseLibraryListVo> listVoIPage = new Page<>();
        List<CaseLibraryListVo> listVos;
        listVos= BeanUtil.copyToList(pageList.getRecords(),CaseLibraryListVo.class);

        for (CaseLibraryListVo caseLibraryVo:listVos) {
            if(ObjectUtils.isNotEmpty(caseLibraryVo.getCaseClassifyId())){
                SysCaseClassify sysCaseClassify = sysCaseClassifyMapper.selectById(caseLibraryVo.getCaseClassifyId());
                if(ObjectUtils.isNotEmpty(sysCaseClassify)){
                    caseLibraryVo.setCaseClassifyName(ObjectUtils.isNotEmpty(sysCaseClassify.getName()) ? sysCaseClassify.getName() : null);
                }
            }
        }

        listVoIPage.setRecords(listVos);
        listVoIPage.setTotal(pageList.getTotal());
        listVoIPage.setCurrent(pageList.getCurrent());
        listVoIPage.setPages(pageList.getPages());
        listVoIPage.setSize(pageList.getSize());
        listVoIPage.setCurrent(pageList.getCurrent());
        result.setSuccess(true);
        result.setResult(listVoIPage);
        log.info("案例库列表返回：{}",listVoIPage.toString());
        return result;
    }

    @Override
    public Result<IPage<SysCaseClassifyListVo>> caseClassifyQueryPageList(CaseLibraryModel caseLibraryModel, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysCaseClassifyListVo>> result = new Result<IPage<SysCaseClassifyListVo>>();
        QueryWrapper<SysCaseClassify> queryWrapper =new QueryWrapper<>();
        Page<SysCaseClassify> page = new Page<SysCaseClassify>(pageNo, pageSize);
//        queryWrapper.lambda().orderByDesc(SysCaseClassify::getSort);
        queryWrapper.lambda().orderByAsc(SysCaseClassify::getSort);

        queryWrapper.lambda().orderByDesc(SysCaseClassify::getUpdateTime);

//        List<SysCaseClassifyListVo> caseBrowseNumList = sysCaseClassifyMapper.getCaseBrowseNumList(pageNo,pageSize);

        IPage<SysCaseClassify> pageList = sysCaseClassifyMapper.selectPage(page, queryWrapper);
        IPage<SysCaseClassifyListVo> listVoIPage = new Page<>();
        List<SysCaseClassifyListVo> listVos;
        listVos= BeanUtil.copyToList(pageList.getRecords(),SysCaseClassifyListVo.class);
        for (SysCaseClassifyListVo vo:listVos) {
            SysCaseClassifyListVo cVo = sysCaseClassifyMapper.getCaseBrowseNum(vo.getId(),caseLibraryModel.getStatus());
            if(ObjectUtils.isEmpty(cVo)){
                vo.setCaseNum(0);
            }else{
                vo.setCaseNum(ObjectUtils.isEmpty(cVo.getCaseNum()) ? 0 : cVo.getCaseNum());
            }

        }
        listVoIPage.setRecords(listVos);
        listVoIPage.setTotal(pageList.getTotal());
        listVoIPage.setCurrent(pageList.getCurrent());
        listVoIPage.setPages(pageList.getPages());
        listVoIPage.setSize(pageList.getSize());
        listVoIPage.setCurrent(pageList.getCurrent());
        result.setSuccess(true);
        result.setResult(listVoIPage);
        log.info("案例库案例分类列表返回：{}",listVoIPage.toString());

        return result;
    }

    @Override
    public Result<CaseLibrary> add(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        Result<CaseLibrary> result = new Result<CaseLibrary>();
        CaseLibrary caseLibrary = new CaseLibrary();
        BeanUtils.copyProperties(caseLibraryModel,caseLibrary);
        try {
            //设置创建时间
            Date date = new Date();
            caseLibrary.setCreateTime(date);
            //从token中获取登录用户名
            String username = CommonService.getUserNameByToken(req);

            caseLibrary.setUpdateTime(date);


            SysUser sysUser = sysUserMapper.getUserByName(username);
            if(ObjectUtils.isNotEmpty(sysUser)){
                SysDepart depart = sysUserDepartMapper.getDepartInfoByUserId(sysUser.getId());
                caseLibrary.setDepartmentId(depart.getId());
                caseLibrary.setDepartmentName(depart.getDepartName());

                caseLibrary.setCreateBy(sysUser.getRealname());
                caseLibrary.setUpdateBy(sysUser.getRealname());
            }else{
                log.error("案例库添加未查询到用户:{}",username);
            }

            if(ObjectUtils.isNotEmpty(caseLibraryModel.getBrandId())){
                BrandProductManager brandProductManager = brandProductManagerMapper.queryById(caseLibraryModel.getBrandId());
                caseLibrary.setBrandName(brandProductManager.getName());
            }
            if(ObjectUtils.isNotEmpty(caseLibraryModel.getPBrandId())){
                BrandProductManager pBrandProductManager = brandProductManagerMapper.queryById(caseLibraryModel.getPBrandId());
                caseLibrary.setPBrandName(pBrandProductManager.getName());
            }

            if(ObjectUtils.isNotEmpty(caseLibraryModel.getBrandIds())){
                String brandIds = StringUtils.join(",", caseLibraryModel.getBrandIds());
                caseLibrary.setBrandIds(brandIds);
            }


            this.save(caseLibrary);
            result.success("案例库添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("案例库操作失败");
        }
        return result;
    }

    @Override
    public Result<CaseLibrary> edit(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        Result<CaseLibrary> result = new Result<CaseLibrary>();
        CaseLibrary caseLibrary = new CaseLibrary();
        BeanUtils.copyProperties(caseLibraryModel,caseLibrary);
        try {
            caseLibrary = getById(caseLibrary.getId());
            if(ObjectUtils.isEmpty(caseLibrary)) {
                result.error500("案例编辑未找到对应实体");
            }else {
                CaseLibrary caseClassify = new CaseLibrary();
                BeanUtils.copyProperties(caseLibraryModel,caseClassify);
                caseClassify.setUpdateTime(new Date());
                String username = CommonService.getUserNameByToken(req);
                SysUser sysUser = sysUserMapper.getUserByName(username);
                if(ObjectUtils.isNotEmpty(sysUser)){
                    caseClassify.setUpdateBy(sysUser.getRealname());
//                    SysDepart depart = sysUserDepartMapper.getDepartInfoByUserId(sysUser.getId());
//                    caseClassify.setDepartmentId(depart.getId());
//                    caseClassify.setDepartmentName(depart.getDepartName());
                }else{
                    log.error("案例库编辑未查询到用户:{}",username);
                }
                if(ObjectUtils.isNotEmpty(caseLibraryModel.getBrandId())){
                    BrandProductManager brandProductManager = brandProductManagerMapper.queryById(caseLibraryModel.getBrandId());
                    caseClassify.setBrandName(brandProductManager.getName());
                }
                if(ObjectUtils.isNotEmpty(caseLibraryModel.getPBrandId())){
                    BrandProductManager pBrandProductManager = brandProductManagerMapper.queryById(caseLibraryModel.getPBrandId());
                    caseClassify.setPBrandName(pBrandProductManager.getName());
                }
                if(ObjectUtils.isNotEmpty(caseLibraryModel.getBrandIds())){
                    String brandIds = StringUtils.join(",", caseLibraryModel.getBrandIds());
                    caseClassify.setBrandIds(brandIds);
                }

                //审核未通过再次编辑改为待审核状态
                if(caseLibrary.getStatus() == 2){
                    caseClassify.setStatus(0);
                }
                //审核已通过 不显示驳回原因
                if(caseLibrary.getStatus() == 1){
                    caseClassify.setReason(null);
                }

                this.updateById(caseClassify);
                result.success("案例编辑修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("案例编辑操作失败");
        }
        return result;
    }

    @Override
    public Boolean deleteCaseLibraryById(String id, HttpServletRequest req) {
        return this.removeById(id);
    }

    @Override
    public CaseLibraryListVo getCaseLibraryById(String id) {
        CaseLibraryListVo caseLibraryListVo = new CaseLibraryListVo();
        CaseLibrary caseLibrary = this.getById(id);
        BeanUtils.copyProperties(caseLibrary,caseLibraryListVo);

        if(ObjectUtils.isNotEmpty(caseLibraryListVo.getCaseClassifyId())){
            SysCaseClassify sysCaseClassify = sysCaseClassifyMapper.selectById(caseLibraryListVo.getCaseClassifyId());
            if(ObjectUtils.isNotEmpty(sysCaseClassify)){
                caseLibraryListVo.setCaseClassifyName(ObjectUtils.isNotEmpty(sysCaseClassify.getName()) ? sysCaseClassify.getName() : null);
            }
        }

        return caseLibraryListVo;
    }

    @Override
    public Boolean getUserLikes(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        String username = CommonService.getUserNameByToken(req);
        SysUser sysUser = sysUserMapper.getUserByName(username);
        QueryWrapper<CaseUserLikes> queryWrapper =new QueryWrapper<>();
        queryWrapper.lambda().eq(CaseUserLikes::getCaseId,caseLibraryModel.getId());
        queryWrapper.lambda().eq(CaseUserLikes::getUserId,sysUser.getId());
        queryWrapper.lambda().eq(CaseUserLikes::getType,0);
        Integer integer = caseUserLikesMapper.selectCount(queryWrapper);
        if(integer > 0){
            return true;
        }
        return false;
    }

    @Override
    public Boolean likesAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        String username = CommonService.getUserNameByToken(req);
        SysUser sysUser = sysUserMapper.getUserByName(username);
        Boolean userLikes = this.getUserLikes(caseLibraryModel, req);
        if(userLikes){
            throw new BootException("用户已给此案例点赞：" + username);
        }

        CaseUserLikes caseUserLikes = new CaseUserLikes();
        caseUserLikes.setCaseId(caseLibraryModel.getId());
        caseUserLikes.setUserId(sysUser.getId());
        Date date = new Date();
        caseUserLikes.setCreateTime(date);
        caseUserLikes.setType(0);
        int insert = caseUserLikesMapper.insert(caseUserLikes);
        if(insert > 0){
            CaseLibrary caseLibraryById = this.getById(caseLibraryModel.getId());
            caseLibraryById.setLikeCount(caseLibraryById.getLikeCount() + 1);
            return this.updateById(caseLibraryById);
        }
        return false;
    }

    @Override
    public Boolean browseAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        String username = CommonService.getUserNameByToken(req);
        SysUser sysUser = sysUserMapper.getUserByName(username);

        CaseUserLikes caseUserLikes = new CaseUserLikes();
        caseUserLikes.setCaseId(caseLibraryModel.getId());
        caseUserLikes.setUserId(sysUser.getId());
        Date date = new Date();
        caseUserLikes.setCreateTime(date);
        caseUserLikes.setType(1);
        int insert = caseUserLikesMapper.insert(caseUserLikes);
        if(insert > 0){
            CaseLibrary caseLibraryById = this.getById(caseLibraryModel.getId());
            caseLibraryById.setBrowse(caseLibraryById.getBrowse() + 1);
            return this.updateById(caseLibraryById);
        }
        return false;
    }

    @Override
    public Boolean downloadAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        String username = CommonService.getUserNameByToken(req);
        SysUser sysUser = sysUserMapper.getUserByName(username);

        CaseUserLikes caseUserLikes = new CaseUserLikes();
        caseUserLikes.setCaseId(caseLibraryModel.getId());
        caseUserLikes.setUserId(sysUser.getId());
        Date date = new Date();
        caseUserLikes.setCreateTime(date);
        caseUserLikes.setType(2);
        int insert = caseUserLikesMapper.insert(caseUserLikes);
        if(insert > 0){
            CaseLibrary caseLibraryById = this.getById(caseLibraryModel.getId());
            caseLibraryById.setDownload(caseLibraryById.getDownload() + 1);
            return this.updateById(caseLibraryById);
        }
        return false;
    }

    @Override
    public Result<CaseReportVo> queryReportList(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        Result<CaseReportVo> result = new Result<CaseReportVo>();
        CaseReportVo caseReportVo = new CaseReportVo();

        //案例数量
        Integer caseNum = caseLibraryMapper.getCaseNum(caseLibraryModel);
        caseReportVo.setCaseNum(ObjectUtils.isNotEmpty(caseNum) ? caseNum : 0);

        //分享部门
        Integer depNum = caseLibraryMapper.getDepNum(caseLibraryModel);
        caseReportVo.setDepNum(ObjectUtils.isNotEmpty(depNum) ? depNum : 0);

        //分享人数
        Integer peopleNum = caseLibraryMapper.getPeopleNum(caseLibraryModel);
        caseReportVo.setPeopleNum(ObjectUtils.isNotEmpty(peopleNum) ? peopleNum : 0);

        //最新分享时间
        String updateTime = caseLibraryMapper.getDescCreateTime(caseLibraryModel);
        caseReportVo.setCreateTime(updateTime);

        result.setResult(caseReportVo);
        result.setSuccess(true);

        return result;
    }

    @Override
    public Result<IPage<CaseLibraryListVo>> queryPageCaseExaminelist(CaseLibraryModel caseLibraryModel, Integer pageNo, Integer pageSize, HttpServletRequest req) {
        CaseLibraryListVo caseLibraryListVo = new CaseLibraryListVo();
        BeanUtils.copyProperties(caseLibraryModel,caseLibraryListVo);

        Result<IPage<CaseLibraryListVo>> result = new Result<IPage<CaseLibraryListVo>>();

        QueryWrapper<CaseLibrary> queryWrapper =new QueryWrapper<>();
        Page<CaseLibrary> page = new Page<CaseLibrary>(pageNo, pageSize);

        //具体案例分类的案例
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getCaseClassifyId())){
            queryWrapper.lambda().eq(CaseLibrary::getCaseClassifyId,caseLibraryModel.getCaseClassifyId());
        }
        //案例列表：通过审核  时间升降序/分类/关键词搜索
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getStatus())){
            queryWrapper.lambda().eq(CaseLibrary::getStatus,caseLibraryModel.getStatus());
        }
        //时间排序，0升序，1降序
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getDateSort())){
            if("0".equals(caseLibraryModel.getDateSort())){
                queryWrapper.lambda().orderByAsc(CaseLibrary::getUpdateTime);
            }else if("1".equals(caseLibraryModel.getDateSort())){
                queryWrapper.lambda().orderByDesc(CaseLibrary::getUpdateTime);
            }
        }else{
            //默认创建时间降序
            queryWrapper.lambda().orderByDesc(CaseLibrary::getUpdateTime);
        }

        //推荐
        if(ObjectUtils.isNotEmpty(caseLibraryModel.getRecommend())){
            queryWrapper.lambda().eq(CaseLibrary::getRecommend,caseLibraryModel.getRecommend());
        }

        IPage<CaseLibrary> pageList = this.page(page, queryWrapper);
        IPage<CaseLibraryListVo> listVoIPage = new Page<>();
        List<CaseLibraryListVo> listVos;
        listVos= BeanUtil.copyToList(pageList.getRecords(),CaseLibraryListVo.class);

        for (CaseLibraryListVo caseLibraryVo:listVos) {
            if(ObjectUtils.isNotEmpty(caseLibraryVo.getCaseClassifyId())){
                SysCaseClassify sysCaseClassify = sysCaseClassifyMapper.selectById(caseLibraryVo.getCaseClassifyId());
                caseLibraryVo.setCaseClassifyName(ObjectUtils.isNotEmpty(sysCaseClassify.getName()) ? sysCaseClassify.getName() : null);
            }
        }

        listVoIPage.setRecords(listVos);
        listVoIPage.setTotal(pageList.getTotal());
        listVoIPage.setCurrent(pageList.getCurrent());
        listVoIPage.setPages(pageList.getPages());
        listVoIPage.setSize(pageList.getSize());
        listVoIPage.setCurrent(pageList.getCurrent());
        result.setSuccess(true);
        result.setResult(listVoIPage);
        log.info("案例审核列表返回：{}",listVoIPage.toString());
        return result;
    }

    @Override
    public Result<?> caseBatchReview(List<CaseLibraryModel> caseLibraryModel, HttpServletRequest req) {
        try{
            String username = CommonService.getUserNameByToken(req);
            for (CaseLibraryModel clm:caseLibraryModel) {
                clm.setUpdateBy(username);
                Boolean updateExamine = sysCaseClassifyMapper.updateExamine(clm);
                if(updateExamine){
                    log.info("批量审核已通过：{}",clm.getId(),clm.getStatus(),clm.getReason());
                }else{
                    log.error("批量审核异常：{},{},{}",clm.getId(),clm.getStatus(),clm.getReason());
                }
            }
        }catch (Exception e){
            log.error("批量审核异常：{},{},{}", JSON.toJSONString(caseLibraryModel));
        }

        return null;
    }

    @Override
    public Integer getCaseClassifyId(String id) {
        QueryWrapper<CaseLibrary> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CaseLibrary::getCaseClassifyId,id);
        Integer integer = caseLibraryMapper.selectCount(wrapper);
        return integer;
    }

    @Override
    public Boolean cancelLikesAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
        String username = CommonService.getUserNameByToken(req);
        SysUser sysUser = sysUserMapper.getUserByName(username);
        Boolean userLikes = this.getUserLikes(caseLibraryModel, req);
        if(userLikes){
            QueryWrapper<CaseUserLikes> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(CaseUserLikes::getCaseId,caseLibraryModel.getId())
                    .eq(CaseUserLikes::getUserId,sysUser.getId())
                    .eq(CaseUserLikes::getType,0);
            int delete = caseUserLikesMapper.delete(wrapper);
            if(delete > 0){
                CaseLibrary caseLibraryById = this.getById(caseLibraryModel.getId());
                caseLibraryById.setLikeCount(caseLibraryById.getLikeCount() - 1);
                return this.updateById(caseLibraryById);
            }
        }else{
            throw new BootException("用户未给此案例点赞：" + username);
        }
        return false;
    }

}
