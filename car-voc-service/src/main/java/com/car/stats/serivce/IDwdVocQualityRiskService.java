package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocQualityRiskF;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.vo.IntentionTrendVo;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.vo.risk.RiskRuleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName IDwdVocRiskService.java
 * @Description TODO
 * @createTime 2022年11月25日 12:02
 * @Copyright voc
 */

public interface IDwdVocQualityRiskService extends IService<DwdVocQualityRisk> {


    Result<?> dataAnalysisBriefing(RiskEventInsightModel model, DwdVocQualityRisk risk);
    Result<?> dataAnalysisBriefingRescue(RiskEventInsightModel model, DwdVocQualityRisk risk);


    Result<?> emotionIntentionTrend(RiskEventInsightModel model,DwdVocQualityRisk risk);

    Result<?> hotWords(RiskEventInsightModel model);

    Result<?> voiceUserTrend(RiskEventInsightModel model,DwdVocQualityRisk risk);

    Result<?> voiceUserTop(RiskEventInsightModel model);

    IPage<RiskPointAggVo> riskPointAggNew(RiskEventInsightModel model);

    Result<?> dataAnalysisBriefingTrend(RiskEventInsightModel model);

    List<DwdVocQualityRiskF> riskQualiytFiltering(VocRiskWarningRules rule, RiskRuleVo vo);

    Result<?> dataAnalysisWaringNum(RiskEventInsightModel model, DwdVocQualityRisk risk);

    Result<?> dataAnalysisChannelTrend(RiskEventInsightModel model, DwdVocQualityRisk risk);

    void riskQualityExport();

    List<DwdVocQualityRiskF> riskQualiytFilteringNew(VocRiskWarningRules riskWarningRules, RiskRuleVo vo);

    List<IntentionTrendVo> qualityUserStatictis(@Param("model") FilterCriteriaModel model, @Param("risk")  DwdVocQualityRisk quality);

    Result<?> carSeriesDistribution(RiskEventInsightModel model);

    Result<?> intentionTrend(RiskEventInsightModel model, DwdVocQualityRisk risk);

    Result<?> regionalDistribution(RiskEventInsightModel model);

    IPage<RiskPointAggVo> riskRescueList(RiskEventInsightModel model);

    List<DwdVocQualityRiskF> riskRescueFiltering(VocRiskWarningRules riskWarningRules, RiskRuleVo vo);
}
