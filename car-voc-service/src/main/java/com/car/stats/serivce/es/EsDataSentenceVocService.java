package com.car.stats.serivce.es;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.es.EsDataSentenceVocs;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.*;
import com.car.stats.vo.ContentVo;
import com.car.stats.vo.SoundContentQueryVo;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.SourceTagTypeVo;
import com.car.stats.vo.popvo.IntentionChannelStatisticVo;
import com.car.stats.vo.popvo.UserDetailVo;
import com.car.stats.vo.popvo.UserRepairDetailVo;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.voc.common.Result;
import com.car.voc.entity.VocChannelCategory;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName ElasticRepositoryService.java
 * @Description TODO
 * @createTime 2022年10月31日 10:30
 * @Copyright voc
 */
public interface EsDataSentenceVocService {
    Page<SoundContentVo> pageQueryList(LabelDetailFilterModel model, Page<SoundContentVo> page);

    Page<SoundContentVo> pageQueryListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page);
    Page<SoundContentVo> pageQueryAllList(LabelDetailFilterModel model, Page<SoundContentVo> page);

    Page<SoundContentVo> pageQueryAllListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page);
    Page<SoundContentQueryVo> allDataQuery(LabelDetailFilterModel model, Page<SoundContentQueryVo> page);

    Result<?> focusSoundContent(FilterCriteriaModel model, Page<SoundContentVo> page);
    Result<?> productQualitySoundContent(ProductQualityFilterCriteriaModel model, Page<SoundContentVo> page);

    Page<SoundContentVo> getQualitySoundList(LabelDetailFilterModel model, Page<SoundContentVo> page);

    Page<SoundContentVo> getQualitySoundListExportXls(LabelDetailFilterModel model, Page<SoundContentVo> page);

    IPage<SoundContentVo> intentionTrackPage(IntentionTrackModel model);

    List<IntentionChannelStatisticVo> intentionChannelStatistic(UserIntentionTrajectoryModel model);

    List<SoundContentVo> userContentSound(String contentId, String channelId, String index, String id, List<VocChannelCategory> categories);
    List<SoundContentVo> userContentSoundRepeated(String contentId, String channelId, String index, String id, List<VocChannelCategory> categories);
    List<SoundContentVo> userContentSoundpus(String contentId, String channelId, String contentType, String index, List<VocChannelCategory> categories);

    Result<?> riskInfoList(RiskEventInsightModel model);

    Map<String, String> getEarlyReleaseRiskPointAgg(RiskEventInsightModel model);

    Result<?> dataAnalysisTimeNode(RiskEventInsightModel model, DwdVocRisk risk);

    EsDataSentenceVocs getId(String id);
    EsDataSentenceVocs getIdRepeated(String id);

    IPage<Map> userDataTrackPus(IntentionTrackModel model);
    IPage<UserRepairDetailVo> userDataTrackRepairPus(IntentionTrackModel model);

    Result<?> complaintUserSentence(ComplaintUserTopModel model);

    Result<?> riskQualityInfoList(RiskEventInsightModel model);

    Result<?> dataAnalysisTimeNodeQuality(RiskEventInsightModel model, DwdVocQualityRisk risk);

   void setUserPublishCount(String userId, List<IntentionChannelStatisticVo> vos, BoolQueryBuilder queryBuilder);
   void setUserPublishCount(ComplaintUserVo riskUser, String userId, List<IntentionChannelStatisticVo> vos, BoolQueryBuilder queryBuilder);
   void setUserPublishCount(UserDetailVo userdetai,String userId, List<IntentionChannelStatisticVo> vos, BoolQueryBuilder queryBuilder);

    EsDataSentenceVocs queryByFilter(LabelDetailFilterModel model);
    List<String> queryIndexByChannelFilter(LabelDetailFilterModel model);

    Page<ContentVo> allSourceDataQuery(LabelDetailFilterModel model,Page<ContentVo> page,List<String> sentence);

    SourceTagTypeVo allSourceGroupByTag(LabelDetailFilterModel model, List<String> indexs);

    Map<String, String> getContentByIdIndex(String id, String index);

}
