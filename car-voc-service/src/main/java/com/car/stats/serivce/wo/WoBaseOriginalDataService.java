package com.car.stats.serivce.wo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.VocOverBriefingValueVo;
import com.car.stats.vo.popvo.PopUpVo;
import com.car.stats.vo.wo.WoLastDealVo;
import com.car.voc.common.Result;

/**
 * <AUTHOR>
 * @description 针对表【t317_csv_rescue_wo_i_d(救援工单)】的数据库操作Service
 * @createDate 2024-12-05 15:22:07
 */
public interface WoBaseOriginalDataService extends IService<WoOriginalData> {

    StatisticVo workOrderNumber(FilterCriteriaModel model);

    Result<?> provinceMap(FilterCriteriaModel model);
    Result<?> workOrderSatisfaction(FilterCriteriaModel model);

    Result<?> regionalTop(FilterCriteriaModel model);

    Result<?> communityTop(FilterCriteriaModel model);

    Result<?> woTypeProportion(FilterCriteriaModel model);
    Result<?> woStatusDistribution(FilterCriteriaModel model);
    Page<WoLastDealVo> woList(FilterCriteriaModel model);
    Result<?> woReginDistribution(FilterCriteriaModel model);
    Result<?> woTrend(FilterCriteriaModel model);

    PopUpVo woTitle(FilterCriteriaModel model);
}
