package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.DwsVocIntentionDi;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.DateChannelVo;
import com.car.stats.vo.HomeIntentionReportVo;
import com.car.voc.common.Result;

import java.util.List;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName IDwsVocIntentionDiService.java
 * @Description TODO
 * @createTime 2022年10月10日 16:27
 * @Copyright voc
 */
public interface IDwsVocIntentionDiService extends IService<DwsVocIntentionDi> {



    Result<List<DateChannelVo>> homeChannelTrends1(FilterCriteriaModel model);

    Result<?> soundInsightBriefingValue(FilterCriteriaModel model);

    Result<?> themeDistribution(FilterCriteriaModel model);


    Result<?> hotWords(FilterCriteriaModel model);

    Result<?> themeShare(FilterCriteriaModel model);

    Result<?> manyQuestion(FilterCriteriaModel model);

    Result<?> soarQuestion(FilterCriteriaModel model);

    Result<?> soarHotWords(FilterCriteriaModel model);

    Result<?> quantityTop(FilterCriteriaModel model);

    Result<?> overviewIntentionAnalysis(FilterCriteriaModel model);

    Result<?> channelDistribution(FilterCriteriaModel model);

    Result<HomeIntentionReportVo> homeIntentionBriefingConsult(FilterCriteriaModel model);
    Result<HomeIntentionReportVo> homeIntentionUserNum(FilterCriteriaModel model);
    Result<?> homeIntentionTrend(FilterCriteriaModel model);
    Result<?> homeIntentionTop(FilterCriteriaModel model);

    Result<HomeIntentionReportVo> homeIntentionBriefingComplaint(FilterCriteriaModel model);


    Result<Map> emotionValue(FilterCriteriaModel model);
}
