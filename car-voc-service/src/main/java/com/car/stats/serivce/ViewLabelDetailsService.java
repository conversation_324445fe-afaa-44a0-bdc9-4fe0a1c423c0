package com.car.stats.serivce;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.IntentionTrackModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.LargeDigitaFilesModel;
import com.car.stats.model.UserIntentionTrajectoryModel;
import com.car.stats.vo.ContentVo;
import com.car.stats.vo.EmotionProportionVo;
import com.car.stats.vo.SoundContentQueryVo;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.popvo.UserDetailVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;
import org.elasticsearch.index.query.BoolQueryBuilder;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName ViewLabelDetailsService.java
 * @Description TODO
 * @createTime 2022年10月30日 20:28
 * @Copyright voc
 */
public interface ViewLabelDetailsService {
    Result<?> businessTagDetail(LabelDetailFilterModel model, Page<SoundContentVo> page);
    Result<?> businessTagDetailNew(LabelDetailFilterModel model, Page<SoundContentVo> page,HttpServletRequest request);

    Result<?> businessTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);

    Result<?> businessTagUserListNew(LabelDetailFilterModel model, Page<UserListInfoVo> page,HttpServletRequest request);

    Result<?> soundContentDetails(String contentId);

    Result<?> qualityTagDetail(LabelDetailFilterModel model, Page<SoundContentVo> page);

    Result<?> qualityTagDetailNew(LabelDetailFilterModel model, Page<SoundContentVo> page,HttpServletRequest request);

    Result<?> qualityTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);

    Result<?> qualityTagUserListNew(LabelDetailFilterModel model, Page<UserListInfoVo> page,HttpServletRequest request);

    Result<?> soundDetails(String contentId,String channelId,String indexId,String id );

    Result<?> userDetails(LabelDetailFilterModel model);

    Result<?> intentionTrack(IntentionTrackModel model);

    Result<?> userIntentionAndTrajectory(UserIntentionTrajectoryModel model);

    Result<?> userDataTrack(IntentionTrackModel model);
    Result<?> userDataTrackRepair(IntentionTrackModel model);

    Result<?> businessTagDetailTrend(LabelDetailFilterModel model);
    Result<?> allTagsTrend(LabelDetailFilterModel model);
    Result<?> positiveNegativeTrend(LabelDetailFilterModel model);
    Result<?> allPositiveNegativeTrend(LabelDetailFilterModel model);

    Result<?> qualityTagDetailTrend(LabelDetailFilterModel model);

    Result<?> businessTagDetailTitle(LabelDetailFilterModel model);

    Result<?> qualityTagDetailTitle(LabelDetailFilterModel model);

    Result<?> businessTagDetailFocus(LabelDetailFilterModel model);

    Result<?> qualityTagDetailFocus(LabelDetailFilterModel model);

    void  queryUserEmotion(EmotionProportionVo emotionProp, LabelDetailFilterModel model, UserDetailVo userDetailVo);
    void  queryUserEmotionBusiness(EmotionProportionVo emotionProp, UserListInfoVo user, UserDetailVo userDetailVo,LabelDetailFilterModel model);
    void  queryUserEmotionQuality(EmotionProportionVo emotionProp, UserListInfoVo user, UserDetailVo userDetailVo,LabelDetailFilterModel model);

    Result<?> lastTagHotWords(LabelDetailFilterModel model);
    Map<String,UserDetailVo> queryUserEmotionList(LabelDetailFilterModel model, BoolQueryBuilder queryBuilder, Set<String> idList);

    Result<?> allDataQuery(LabelDetailFilterModel model, Page<SoundContentQueryVo> page);

    Result<?> allDataQueryNew(LabelDetailFilterModel model, Page<SoundContentQueryVo> page);

    Result<?> allSourceDataQuery(LabelDetailFilterModel model, Page<ContentVo> page);

    Result<?> allSourceGroupByTag(LabelDetailFilterModel model);

    Result<?> allTagsClickSoundsList(LabelDetailFilterModel model, Page<SoundContentVo> page);

    Result<?> allTagsClickSoundsListNew(LabelDetailFilterModel model, Page<SoundContentVo> page, HttpServletRequest request);

    Result<?> allTagUserList(LabelDetailFilterModel model, Page<UserListInfoVo> page);

    Result<?> allTagUserListNew(LabelDetailFilterModel model, Page<UserListInfoVo> page,HttpServletRequest request);

    Result<?> asyncAllSourceDataQuery(LabelDetailFilterModel model, Page<ContentVo> page, List<String> esIndexs,HttpServletRequest request);

    LargeDigitaFilesModel getFile(LargeDigitaFilesModel model);

}
