package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.car.stats.entity.CaseLibrary;
import com.car.stats.model.CaseLibraryModel;
import com.car.stats.vo.CaseLibraryListVo;
import com.car.stats.vo.CaseReportVo;
import com.car.voc.common.Result;
import com.car.voc.vo.SysCaseClassifyListVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * TODO
 *
 * @Description 案例库 服务实现类
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
public interface ICaseLibraryService extends IService<CaseLibrary> {

    Result<IPage<CaseLibraryListVo>> queryPageList(CaseLibraryModel caseLibraryModel, Integer pageNo, Integer pageSize, HttpServletRequest req,String type);

    Result<CaseLibrary> add(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Result<CaseLibrary> edit(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Boolean deleteCaseLibraryById(String id, HttpServletRequest req);

    CaseLibraryListVo getCaseLibraryById(String id);

    Result<IPage<SysCaseClassifyListVo>> caseClassifyQueryPageList(CaseLibraryModel caseLibraryModel, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Boolean getUserLikes(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Boolean likesAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Boolean browseAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Boolean downloadAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Result<CaseReportVo> queryReportList(CaseLibraryModel caseLibraryModel, HttpServletRequest req);

    Result<IPage<CaseLibraryListVo>> queryPageCaseExaminelist(CaseLibraryModel caseLibraryModel, Integer pageNo, Integer pageSize, HttpServletRequest req);

    Result<?> caseBatchReview(List<CaseLibraryModel> caseLibraryModel, HttpServletRequest req);

    Integer getCaseClassifyId(String id);

    Boolean cancelLikesAdd(CaseLibraryModel caseLibraryModel, HttpServletRequest req);
}
