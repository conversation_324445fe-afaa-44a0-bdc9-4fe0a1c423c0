package com.car.stats.serivce.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.DwsVocUser;
import com.car.stats.mapper.DwsVocUserMapper;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.serivce.IDwsVocUserService;
import com.car.stats.vo.UserChannelPubNumVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 *
 * @version 1.0.0
 * @ClassName DwsVocUserServiceImpl.java
 * @Description TODO
 * @createTime 2022年11月02日 10:08
 * @Copyright voc
 */
@Service
public class DwsVocUserServiceImpl extends ServiceImpl<DwsVocUserMapper, DwsVocUser> implements IDwsVocUserService {
    @Override
    public List<UserChannelPubNumVo> userChannelPubNum(LabelDetailFilterModel model) {
        return baseMapper.userChannelPubNum(model);
    }
    @Override
    public List<UserChannelPubNumVo> userChannelPubNumUserIds(Set<String> idList) {
        return baseMapper.userChannelPubNumUserIds(idList);
    }


}
