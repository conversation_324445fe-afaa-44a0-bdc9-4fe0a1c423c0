package com.car.stats.serivce;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;
import com.car.voc.model.NPSFilterCriteriaModel;
import com.car.voc.vo.NPSDataVo;
import com.car.voc.vo.risk.RiskAllTypesVo;

import javax.xml.bind.UnmarshallerHandler;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @version 1.0.0
 * @ClassName NPSAnalysisServiceImpl.java
 * @Description TODO
 * @createTime 2023年07月31日 13:22
 * @Copyright voc
 */
public interface INPSAnalysisService {
    Result<?> participantScore(NPSFilterCriteriaModel model);

    Result<?> participantProportion(NPSFilterCriteriaModel model);

    Result<?> analysisTrend(NPSFilterCriteriaModel model);

    Result<?> carSeriesDistribution(NPSFilterCriteriaModel model);

    Result<?> carSeriesEmotionTrend(NPSFilterCriteriaModel model);

    Result<?> focusAnalysisHotWords(NPSFilterCriteriaModel model);

    Result<?> focusDistribution(NPSFilterCriteriaModel model);

    Result<?> intentionAnalysisProportion(NPSFilterCriteriaModel model);

    Result<?> intentionAnalysisTrend(NPSFilterCriteriaModel model);

    IPage<NPSDataVo> list(Page<NPSDataVo> page, NPSFilterCriteriaModel model);

    Result<?>  exportToXls(NPSFilterCriteriaModel model, Page<NPSDataVo> page);

   Map<String, String> userAndStatistic(LabelDetailFilterModel model);
}
