package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.AtomicDouble;
import com.car.stats.entity.DwsVocIntentionDi;
import com.car.stats.mapper.DwsVocEmotionDiMapper;
import com.car.stats.mapper.DwsVocIntentionDiMapper;
import com.car.stats.mapper.DwsVocQualityUserDiMapper;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.IDwsVocIntentionDiService;
import com.car.stats.serivce.StatsCommonService;
import com.car.stats.vo.*;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.enums.IntentionEnum;
import com.car.voc.common.util.*;
import com.car.voc.model.LoginUser;
import com.car.voc.service.ISysDictItemService;
import com.car.voc.service.ISysDictService;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocChannelCategoryService;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName DwsVocIntentionDiServiceImpl.java
 * @Description TODO
 * @createTime 2022年10月10日 16:28
 * @Copyright voc
 */
@Service
public class DwsVocIntentionDiServiceImpl extends ServiceImpl<DwsVocIntentionDiMapper, DwsVocIntentionDi> implements IDwsVocIntentionDiService {
    @Autowired
    ISysDictItemService iSysDictItemService;
    @Resource
    DwsVocIntentionDiMapper intentionDiMapper;
    @Resource
    DwsVocQualityUserDiMapper intentionUserDiMapper;
    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @Autowired
    StatsCommonService statsCommonService;

    @Autowired
    ISysDictItemService dictItemService;
    @Autowired
    ISysDictService dictService;
    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Resource
    DwsVocEmotionDiMapper dwsVocEmotionDiMapper;

    @Autowired
    Executor defExecutor;
    @Override
    public Result<List<DateChannelVo>> homeChannelTrends1(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));

        model.setStartDate(dates.get(0).getStartDate());
        model.setEndDate(dates.get(dates.size() - 1).getEndDate());
        List<ChannelStatisticVo> objectLists = intentionDiMapper.homeChannelTrends1(model);

        if (CollUtil.isEmpty(objectLists)) {
            return Result.OK(Collections.emptyList());
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

       /* //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> ChannelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );*/

        //存放所有ID用于补全展示数据
        final Set<String> channelIdTempSet = objectLists.stream().filter(e -> ObjectUtil.isNotNull(e)).filter(e -> StrUtil.isNotBlank(e.getDataSource()))
                .map(ChannelStatisticVo::getDataSource).collect(Collectors.toSet());

        final Map<String, List<ChannelStatisticVo>> rsMap = objectLists.stream().collect(Collectors.groupingBy(ChannelStatisticVo::getDateStr));
        List<DateChannelVo> objects = rsMap.keySet().stream().map(dateStr -> {
            DateChannelVo vo = new DateChannelVo();
            vo.setDateTime(dateStr);
            vo.setChannels(rsMap.get(dateStr));
            return vo;
        }).collect(Collectors.toList());


        final String channelProportionDate = objects.stream()
                .max(Comparator.comparing(DateChannelVo::getDateTime)).orElse(objects.stream().findFirst().get()).getDateTime();
        final List<ChannelStatisticVo> channelVos = objectLists.stream()
                .filter(e -> ObjectUtil.isNotNull(e))
                .filter(e -> ObjectUtil.isNotNull(e.getStatistic()))
                .filter(e -> channelProportionDate.equals(e.getDateStr()))
                .collect(Collectors.toList());

        //补齐日期
        objects.addAll(dateTempSet.stream()
                .map(e -> {
                    DateChannelVo vo = new DateChannelVo();
                    vo.setDateTime(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );


        //补齐业务对象
        objects.stream().forEach(e -> {
            Set<String> containsIds = e.getChannels().stream().map(ChannelStatisticVo::getDataSource).collect(Collectors.toSet());
            e.getChannels().addAll(
                    channelIdTempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id ->
                                    {
                                        ChannelStatisticVo vo = new ChannelStatisticVo();
                                        vo.setDateStr(e.getDateTime());
                                        vo.setDataSource(id);
                                        return vo;
                                    }
                            ).collect(Collectors.toList())
            );
        });

        //时间排序
        objects.sort(Comparator.comparing(DateChannelVo::getDateTime, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objects.size(); i++) {  //第二条开始处理
            DateChannelVo obj = objects.get(i);  //当前
            DateChannelVo preObj = objects.get(i - 1);   //前一个
            final BigDecimal total = obj.getChannels().stream().map(ChannelStatisticVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, ChannelStatisticVo> map = obj.getChannels().stream().collect(Collectors.toMap(ChannelStatisticVo::getDataSource, e -> e, (k1, k2) -> k1));
            Map<String, ChannelStatisticVo> preMap = preObj.getChannels().stream().collect(Collectors.toMap(ChannelStatisticVo::getDataSource, e -> e, (k1, k2) -> k1));

            obj.setTotal(total);
            obj.setTotalP(CalculatorUtils.ringRatio(obj.getTotal(), preObj.getTotal()));
            map.keySet().stream().forEach(id -> {
                ChannelStatisticVo cur_ = map.get(id);
                ChannelStatisticVo pre_ = preMap.get(id);

//                //设置日均 averagePerDay()
//                if(model.getDateUnit().intValue() == -1) {
//                    cur_.setStatisticR(BigDecimal.ZERO);
//                }else{
//                    final Long days = DateUtil.between(DateUtil.parse(model.getEndDate()), DateUtil.parse(model.getStartDate()),DateUnit.DAY) +1;
//                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
//                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_)? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(days));
//                    cur_.setStatisticA(objAvgDays);
//                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
//                }

                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
            });
        }


        channelVos.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        //时间排序
        objects.sort(Comparator.comparing(DateChannelVo::getDateTime, Comparator.nullsFirst(String::compareTo)));
        objects.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateTime(e.getDateTime().replaceAll("-", "/"));
                    } else {
                        e.setDateTime(this.coveringDate2(e.getDateTime()));
                    }
                });
        if (objects.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objects = new ArrayList<>(objects.subList(  s >= objects.size()  ? 1 : s - index  , objects.size()));
        }


        return Result.OK(objects);


        /*model.setStartDate(dates.get(0).getStartDate());
        model.setEndDate(dates.get(dates.size() - 1).getEndDate());
        List<ChannelStatisticVo> list = intentionDiMapper.homeChannelTrends1(model);
        Map<String, List<ChannelStatisticVo>> maps = list.stream().filter(s -> s.getDateStr() != null && !"".equals(s.getDateStr())).collect(Collectors.groupingBy(ChannelStatisticVo::getDateStr));
        List<String> mapKey = model.getMapKey(dates);

        List<DateChannelVo> objects = new ArrayList<>();

        for (SvwDate date : new ArrayList<>(dates)) {
            DateChannelVo cha = new DateChannelVo();
            List<ChannelStatisticVo> ones = maps.get(date.getTimeM());
            if (CollectionUtils.isEmpty(ones)) continue;

            if (ones == null || ones.size() <= 0) {
                ones = new ArrayList<>();
                ones.add(new ChannelStatisticVo());
            } else {
                BigDecimal total = ones.stream().map(ChannelStatisticVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
                cha.setTotal(total);
                if (model.getDateUnit() != -1) {//设置日均 averagePerDay()
                    Long day = DateUtils.getDays(date.getEndDate(), date.getStartDate()) + 1;
                    cha.setAveragePerDay(model.getDateUnit(), date.getEndDate(), day);
                }

                if (a[0] > 0) {
                    DateChannelVo upq = objects.get(a[0] - 1);
                    cha.setTotalP(CalculatorUtils.ringRatio(cha.getTotal(), upq.getTotal()));
                    ones.forEach(e -> {
                        if (model.getDateUnit() != -1) {//设置日均 averagePerDay()
                            Long day = DateUtils.getDays(date.getEndDate(), date.getStartDate()) + 1;

                            e.setAveragePerDay(model.getDateUnit(), date.getEndDate(), day);
                        }
                        List<ChannelStatisticVo> eq = objects.get(a[0] - 1).getChannels().stream().filter(d -> e.getDataSource().equals(d.getDataSource())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(eq)) {
                            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), eq.get(0).getStatistic()));
                        }
                    });
                }
            }

            cha.setDateTime(model.getDateUnit() == -1 ? IDateUtils.convertTime(date.getTime(), Constants.YYYYMMDD, false) :
                    (model.getDateUnit() == 1 ? IDateUtils.convertTime(date.getTime(), Constants.YYYYMMDD) : date.getTime()));
            cha.setChannels(ones);
            objects.add(cha);
            a[0]++;
        }
        int index = 13;
        if (objects.size() >= index && model.getDateUnit() > -1) {
            objects.remove(0);
        }
        return Result.OK(objects);*/
    }


    @Override
    public Result<?> soundInsightBriefingValue(FilterCriteriaModel model) {
        model.SetUpCycle();
        VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(model);
        if (re == null) {
            re = new VocOverBriefingValueVo();
            re.setTotalMentions(new BigDecimal(0));
        }
        re.setTotalMentionsR(CalculatorUtils.ringRatio(re.getTotalMentions(), re.getTotalMentionsUp()));
        Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(model);
        if (user != null) {
            re.setUsersNum(user.get("thisUserNum"));
        }
        re.setUsersNumR(CalculatorUtils.ringRatio(re.getUsersNum(), ObjectUtil.isNull(user) ? null : user.get("thisUserNumUp")));

        List<ChannelStatisticVo> chans = intentionDiMapper.sourceChannel(model);

        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<ChannelStatisticVo> chansup = intentionDiMapper.sourceChannel(model);

        if (chans != null && chansup != null) {
            re.setChannelNum(BigDecimal.valueOf(chans.size()));
            re.setChannelNumR(new BigDecimal(chans.size() - chansup.size()));
        }

        List<String> chStr = new ArrayList<>();
        for (int i = 0; i < chans.size(); i++) {
            chStr.add(chans.get(i).getDataSource());
        }
        re.setChannelTopStr(chStr);
        return Result.OK(re);
    }

    @Autowired
    RedisUtil redisUtil;

    public String getUserId() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return loginUser.getId();
    }

    @Override
    public Result<?> themeDistribution(FilterCriteriaModel model) {
        List<LabelVo> distrVos = new ArrayList<>();
        /*BigDecimal total = null;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            total = emotionUserDiService.userDistinctNum(model);
        }*/
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(distrVos);
        }

//        if(model.getDateUnit() == -1){
//            model.setStartDate(DateUtil.parseDate(model.getEndDate()).toDateStr());
//        }

        if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
            distrVos = intentionDiMapper.threeDistribution(model);
        } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            distrVos = intentionDiMapper.secondThemeShare(model);
        } else {
            List<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
            if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
                model.getSecondDimensionCodes().retainAll(sounps);
                List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
                model.setSecondDimensionCodes(interse);
            }
            model.setSecondDimensionCodes(sounps);

            distrVos = intentionDiMapper.themeDistribution(model);
        }

        Optional<BigDecimal> userNum = Optional.of(BigDecimal.ZERO);
        if (model.getDataType().equals(DataEnum.numMention)) {
            VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(model);
            if (ObjectUtil.isNotNull(re)) {
                userNum = Optional.ofNullable(re.getTotalMentions());
            }
        } else if (model.getDataType().equals(DataEnum.numUsers)) {
            final Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(model);
            if (ObjectUtil.isNotNull(user) && user.containsKey("thisUserNum")) {
                userNum = Optional.of(user.get("thisUserNum"));
            }
        }
        /*if (model.getDataType().equals(DataEnum.numMention)) {
            total = distrVos.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }
        BigDecimal finalTotal = total;*/

        final Optional<BigDecimal> finalUserNum = userNum;
        CalculatorUtils.proportion(distrVos, LabelVo::getStatistic, LabelVo::setStatisticP, finalUserNum.get());
        return Result.OK(distrVos);
    }


    @Override
    public Result<?> hotWords(FilterCriteriaModel model) {
        model.setRownum(model.isExcel() ? model.getDownloadHotWordsNum() : null);
        List<HighHotWordsVo> hotWordsVos =  dwsVocEmotionDiMapper.highFrequencyWords(model);
        return Result.OK(hotWordsVos);
    }

    @Override
    public Result<?> themeShare(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        List<TrendLabelVo> objectLists = new ArrayList<>();

        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(objectLists);
        }
//        Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
//        if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
//            model.getSecondDimensionCodes().retainAll(sounps);
//        } else {
//            model.setSecondDimensionCodes(new ArrayList<>(sounps));
//        }

//        Optional<BigDecimal> userNum = Optional.of(BigDecimal.ZERO);
//        if (model.getDataType().equals(DataEnum.numMention)) {
//            VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(model);
//            if (ObjectUtil.isNotNull(re)) {
//                userNum = Optional.ofNullable(re.getTotalMentions());
//            }
//        } else if (model.getDataType().equals(DataEnum.numUsers)) {
//            final Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(model);
//            if (ObjectUtil.isNotNull(user) && user.containsKey("thisUserNum")) {
//                userNum = Optional.of(user.get("thisUserNum"));
//            }
//        }


        int switch_ = 1;
        if (StrUtil.isBlank(model.getFirstDimensionCode()) && CollUtil.isEmpty(model.getSecondDimensionCodes())) {
            switch_ = 1;
        } else if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && CollUtil.isEmpty(model.getSecondDimensionCodes())) {
            switch_ = 2;
        } else if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
            switch_ = 3;
        }

        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));


        Map<String,BigDecimal> dateP = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        dates.stream().forEach(e->{

            FilterCriteriaModel m = new FilterCriteriaModel();
            BeanUtil.copyProperties(model,m);
            m.setStartDate(e.getStartDate());
            m.setEndDate(e.getEndDate());
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                Optional<BigDecimal> num = Optional.of(BigDecimal.ZERO);
                if (m.getDataType().equals(DataEnum.numMention)) {
                    final VocOverBriefingValueVo re = intentionDiMapper.overviewBriefingValue(m);
                    if (ObjectUtil.isNotNull(re)) {
                         num = Optional.ofNullable(re.getTotalMentions());
                    }
                } else if (m.getDataType().equals(DataEnum.numUsers)) {
                    final Map<String, BigDecimal> user = intentionDiMapper.queryUserNum(m);
                    if (ObjectUtil.isNotNull(user) && user.containsKey("thisUserNum")) {
                        num = Optional.ofNullable(user.get("thisUserNum"));
                    }
                }

                dateP.put( this.coveringDate(e.getTime()),num.get());
                return null;
            })));
        });

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        Set<String> tempSet = new HashSet<>();  //存放所有ID用于补全展示数据
        Map<String, String> tempMap = new HashMap<>();
        for (SvwDate date : new ArrayList<>(dates)) {
            TrendLabelVo one = new TrendLabelVo();
            List<LabelVo> objects;

            FilterCriteriaModel criteriaModel = new FilterCriteriaModel();
            BeanUtil.copyProperties(model,criteriaModel);
            criteriaModel.setStartDate(date.getStartDate());
            criteriaModel.setEndDate(date.getEndDate());

            if (switch_ == 3) {
                objects = intentionDiMapper.threeDistribution(criteriaModel);
//            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            } else if (switch_ == 2) {
                objects = intentionDiMapper.secondThemeShare(criteriaModel);
            } else {
                channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, criteriaModel.getBrandCode(), getUserId()));
                Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(criteriaModel.getSecondDimensionCodes())) {
                    criteriaModel.getSecondDimensionCodes().retainAll(sounps);
                } else {
                    criteriaModel.setSecondDimensionCodes(new ArrayList<>(sounps));
                }
                objects = intentionDiMapper.themeShare(criteriaModel);
            }
            one.setDateStr(date.getTime());
            one.setList(objects);

            if (CollUtil.isNotEmpty(objects)) {
                Map<String, String> collect = objects.stream().filter(e -> ObjectUtil.isNotNull(e))
                        .filter(e -> StrUtil.isNotBlank(e.getLabelCode()) && StrUtil.isNotBlank(e.getLabelStr()))
                        .collect(Collectors.toMap(LabelVo::getLabelCode, LabelVo::getLabelStr));
                tempSet.addAll(collect.keySet());
                tempMap.putAll(collect);
            }

            objectLists.add(one);
        }


        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> TrendLabelVo.builder().dateStr(e).build())
                .collect(Collectors.toList())
        );

        //拼装补全完整数据
        objectLists.stream().forEach(e -> {
            Set<String> containsIds = e.getList().stream().map(LabelVo::getLabelCode).collect(Collectors.toSet());
            if(CollUtil.isEmpty(e.getList())){
                e.setList(new ArrayList<>());
            }
            e.getList().addAll(
                    tempSet.stream()
                            .filter(id -> !containsIds.contains(id))   //排除已有的数据
                            .map(id -> LabelVo.builder().labelCode(id).labelStr(tempMap.get(id)).statistic(BigDecimal.ZERO).build()).collect(Collectors.toList())
            );
        });
        //时间排序
        objectLists.sort(Comparator.comparing(TrendLabelVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            TrendLabelVo obj = objectLists.get(i);  //当前
            TrendLabelVo preObj = objectLists.get(i - 1);   //前一个
//            final BigDecimal total = obj.getList().stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
            Map<String, LabelVo> map = obj.getList().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));
            Map<String, LabelVo> preMap = preObj.getList().stream().collect(Collectors.toMap(LabelVo::getLabelCode, e -> e, (k1, k2) -> k1));

//            final Optional<BigDecimal> finalUserNum = userNum;
            map.keySet().stream().forEach(id -> {
                LabelVo cur_ = map.get(id);
                LabelVo pre_ = preMap.get(id);
                final Long days = dateR.get(obj.getDateStr());
                final Long preDays = dateR.get(preObj.getDateStr());

                //设置日均 averagePerDay()
                if (model.getDateUnit().intValue() == -1) {
                    cur_.setStatisticAR(BigDecimal.ZERO);
                } else {
                    final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(cur_.getStatistic(), new BigDecimal(days));
                    final BigDecimal preObjAvgDays = ObjectUtil.isNull(pre_) ? null : CalculatorUtils.avgePerDayNum(pre_.getStatistic(), new BigDecimal(preDays));
                    cur_.setStatisticA(objAvgDays);
                    cur_.setStatisticAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
                }

                cur_.setStatisticP(CalculatorUtils.proportion(cur_.getStatistic(), dateP.get(obj.getDateStr())));
                cur_.setStatisticR(CalculatorUtils.ringRatio(cur_.getStatistic(), ObjectUtil.isNull(pre_) ? null : pre_.getStatistic()));
//                cur_.setStatisticAR(CalculatorUtils.ringRatio(cur_.getStatisticA(),ObjectUtil.isNull(pre_)? null :  pre_.getStatisticA()));

            });
        }

        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }

        /*if(CollUtil.isNotEmpty(objectLists) ){
            Map<String, Object> map = new HashMap<>();
            map.put("themeShare",objectLists);
            map.put("themeDistribution",objectLists.get(objectLists.size()-1));
            return Result.OK(map);
        }*/

        return Result.OK(objectLists);/*



        int index = 7;

        for (SvwDate date : model.getDateUnit() == -1 ? dates : dates.subList(dates.size() >= 7 ? dates.size() - 7 : 0, dates.size())) {
            LabelVo cha = new LabelVo();
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            if (switch_ == 3) {
                reda = intentionDiMapper.threeDistribution(model);
//            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            } else if (switch_ == 2) {
                reda = intentionDiMapper.secondThemeShare(model);
            } else {
                reda = intentionDiMapper.themeShare(model);
            }
        }


        int a = 0;
        for (SvwDate date : model.getDateUnit() == -1 ? dates : dates.subList(dates.size() >= 7 ? dates.size() - 7 : 0, dates.size())) {
            model.setStartDate(date.getStartDate());
            model.setEndDate(date.getEndDate());
            System.out.println(model.getStartDate() + "   " + model.getEndDate());
            List<LabelVo> reda;
            if (switch_ == 3) {
                reda = intentionDiMapper.threeDistribution(model);
//            } else if (StrUtil.isNotBlank(model.getFirstDimensionCode())) {
            } else if (switch_ == 2) {
                reda = intentionDiMapper.secondThemeShare(model);
            } else {
                *//*List<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toList());
                if (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0) {
                    model.getSecondDimensionCodes().retainAll(sounps);
                    List<String> interse = new ArrayList<>(model.getSecondDimensionCodes());
                    model.setSecondDimensionCodes(interse);
                }
                model.setSecondDimensionCodes(sounps);*//*


                reda = intentionDiMapper.themeShare(model);
            }
            if (CollectionUtils.isEmpty(reda)) continue;
//            BigDecimal finalTotal ;
//            if (model.getDataType().equals(DataEnum.numMention)){
            BigDecimal finalTotal = reda.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
//            }
            int finalA = a;
//            BigDecimal finalTotal = total;
            reda.forEach(e -> {
                e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalTotal));
                if (finalA > 0) {
                    List<LabelVo> redup = list.get(finalA - 1).getList();
                    LabelVo labelVo = redup.stream().filter(c -> e.getLabelCode().equals(c.getLabelCode())).findFirst().orElse(null);
                    if (labelVo != null) {
                        e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), labelVo.getStatistic()));
                    }
                }
            });
            TrendLabelVo re = new TrendLabelVo();
            re.setDateStr(date.getTime());
            re.setList(reda);
            list.add(re);
            a++;
        }
        if (list.size() >= 7 && model.getDateUnit() > -1) {
            list.remove(0);
        }

        List<TrendLabelVo> rs = CollUtil.sort(list, new Comparator<Object>() {
            public int compare(Object o1, Object o2) {
                try {
                    if (ObjectUtil.isEmpty(o1) || ObjectUtil.isEmpty(o2)) {
                        return 0;
                    }
                    // || StrUtil.isBlank(o1.getDateStr()) || StrUtil.isBlank(o2.getDateStr())
                    Object o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(o1, "dateStr")).orElse(null);
                    Object o2Str = Optional.ofNullable(ReflectUtil.getFieldValue(o2, "dateStr")).orElse(null);

                    if (ObjectUtil.isNull(o1Str) && ObjectUtil.isNull(o2Str)) {
                        return DateUtil.parse(String.valueOf(o1Str), "yyyy/MM/dd")
                                .compareTo(DateUtil.parse(String.valueOf(o2Str), "yyyy/MM/dd"));
                    }
                    return 0;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });

        return Result.OK(rs);*/
    }

    @Autowired
    IVocBusinessTagService tagService;

    @Override
    public Result<?> manyQuestion(FilterCriteriaModel model) {
        model.SetUpCycle();
        List<LabelVo> mqnylist = intentionDiMapper.manyQuestion(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(mqnylist.stream().map(LabelVo::getLabelCode).collect(Collectors.toSet()));
        List<LabelVo> mqnylistUp = intentionDiMapper.manyQuestion(model);
        mqnylist.forEach(e -> {
            LabelVo onet = mqnylistUp.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList())
                    .stream().findFirst().orElse(LabelVo.builder().build());
            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.getStatistic()));
            e.setLabelAllStr(tagService.getAllName(e.getLabelCode()));
        });


        mqnylist.sort(Comparator.comparing(LabelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(LabelVo::getStatisticR)
                .thenComparing(LabelVo::getLabelCode)
                .reversed());
        return Result.OK(mqnylist);
    }

    @Override
    public Result<?> soarQuestion(FilterCriteriaModel model) {
        model.SetUpCycle();
        model.setRownum(1000);
        List<LabelVo> mqnylist = intentionDiMapper.manyQuestion(model);
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(mqnylist.stream().map(LabelVo::getLabelCode).collect(Collectors.toSet()));
        List<LabelVo> mqnylistUp = intentionDiMapper.manyQuestion(model);

        BigDecimal mqtotal = mqnylist.stream().map(LabelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<LabelVo> avgbefreList;

        if (mqnylist != null && mqnylist.size() > 0) {
            avge = NumberUtil.div(mqtotal, mqnylist.size());
            avgbefreList = mqnylist.stream().filter(e -> e.getStatistic().compareTo(avge) > 0).collect(Collectors.toList());
        } else {
            avgbefreList = mqnylist;
        }

        List<LabelVo> jihe = new ArrayList<>();
        //若大于平均值的标签的个数小于等于20，则取大于平均值的标签总个数的一半
        if(ObjectUtil.isNotEmpty(avgbefreList)&&avgbefreList.size()<=20){
            jihe = mqnylist.subList(0,mqnylist.size()/2);
        }else {
            jihe = avgbefreList;
        }


        jihe.forEach(e -> {
            LabelVo onet = mqnylistUp.stream().filter(d -> e.getLabelCode().equals(d.getLabelCode())).collect(Collectors.toList())
                    .stream().findFirst().orElse(LabelVo.builder().build());
            e.setStatisticR(CalculatorUtils.ringRatio(e.getStatistic(), onet.getStatistic()));
        });
        jihe = jihe.stream().filter(e -> (e.getStatisticR() != null && e.getStatisticR().floatValue() > 0)).collect(Collectors.toList());
        jihe.sort(Comparator.comparing(LabelVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo))
                        .thenComparing(LabelVo::getStatistic)
                        .thenComparing(LabelVo::getLabelCode)
                .reversed());
        if (jihe.size() >= 20) {
            return Result.OK(new ArrayList<>(jihe.subList(0, 20)));
        } else {
            return Result.OK(jihe);
        }

    }

    @Override
    public Result<?> soarHotWords(FilterCriteriaModel model) {
        model.SetUpCycle();
        model.setRownum(1000);
        List<HighHotWordsVo> hotWordsVos = dwsVocEmotionDiMapper.highFrequencyWords(model);
        model.setHotWords(hotWordsVos.stream().map(HighHotWordsVo::getKeyword).collect(Collectors.toSet()));
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        List<HighHotWordsVo> hotWordsVosUp = dwsVocEmotionDiMapper.highFrequencyWords(model);


        BigDecimal btotal = hotWordsVos.stream().map(HighHotWordsVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<HighHotWordsVo> avgbefre;
        if (hotWordsVos != null && hotWordsVos.size() > 0) {
            avge = NumberUtil.div(btotal, hotWordsVos.size());
            avgbefre = hotWordsVos.stream().filter(e -> (e.getStatistic().compareTo(avge)) > 0).collect(Collectors.toList());
        } else {
            avgbefre = hotWordsVos;
        }
        List<HighHotWordsVo> jihe = avgbefre;
        jihe.forEach(e -> {
            List<HighHotWordsVo> onet = hotWordsVosUp.stream().filter(d -> e.getKeyword().equals(d.getKeyword())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(onet)) {
                e.setSoaringRate(CalculatorUtils.ringRatio(e.getStatistic(), onet.get(0).getStatistic()));
            }
        });
        jihe = jihe.stream().filter(e -> e.getSoaringRate().floatValue() > 0).collect(Collectors.toList());
        jihe.sort(Comparator.comparing(HighHotWordsVo::getSoaringRate, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(HighHotWordsVo::getStatistic)
                .thenComparing(HighHotWordsVo::getKeyword)
                .reversed());

        if (jihe.size() >= 20) {
            return Result.OK(new ArrayList<>(jihe.subList(0, 20)));
        } else {
            return Result.OK(jihe);
        }

    }

    @Override
    public Result<?> quantityTop(FilterCriteriaModel model) {
        BigDecimal total = null;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            total = emotionUserDiService.userDistinctNum(model);
        } else {
            VocOverBriefingValueVo re = emotionUserDiService.overviewBriefingValue(model);
            if (re == null) {
                re = new VocOverBriefingValueVo();
            }
            total = re.getTotalMentions();
        }


        int index = model.isExcel() ? model.getDownloadTagNum() : 20;
        List<LabelVo> tops = intentionDiMapper.quantityTop(model);
        BigDecimal finalTotal = total;
        tops.stream().forEach(e -> e.setStatisticP(CalculatorUtils.proportion(e.getStatistic(), finalTotal)));

        tops.sort(Comparator.comparing(LabelVo::getStatistic, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(LabelVo::getStatisticR, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(LabelVo::getLabelCode, Comparator.nullsFirst(String::compareTo))
                .reversed());

        if (tops.size() >= index) {
            return Result.OK(new ArrayList<>(tops.subList(0, index)));
        } else {
            return Result.OK(tops);
        }
    }

    @Override
    public Result<?> overviewIntentionAnalysis(FilterCriteriaModel model) {
        model.SetUpCycle();
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        List<HomeIntentionReportVo> reportVos = new ArrayList<>();
        Map<String, BigDecimal> cb = intentionDiMapper.purposeCb(model);
        if (cb == null) {
            return Result.OK(reportVos);
        }
        if (model.getDataType().equals(DataEnum.numMention)) {
            cb.put("总数", ((BigDecimal) cb.get("其他")).add((BigDecimal) cb.get("咨询")).add((BigDecimal) cb.get("投诉")).add((BigDecimal) cb.get("表扬")));
        }
        BigDecimal cs = new BigDecimal(0);
        if (cb != null) {
            cs = (BigDecimal) cb.get("总数");
        }
        for (IntentionEnum value : IntentionEnum.values()) {
            if ("其他".equals(value.getName())) {
                break;
            }
            HomeIntentionReportVo vo = new HomeIntentionReportVo();
            vo.setPurpose(value.getName());
            if (cb == null || cb.get(value.getName()) == null) {
                cb = new HashMap<>();
                cb.put(value.getName(), new BigDecimal(0));
            }
            vo.setIntention((BigDecimal) cb.get(value.getName()));
            vo.setIntentionP(CalculatorUtils.proportion((BigDecimal) cb.get(value.getName()), cs));
            model.setIntention(value.getName());
            setIntentionCards(vo, model);
            reportVos.add(vo);
        }
        return Result.OK(reportVos);
    }

    @Override
    public Result<?> channelDistribution(FilterCriteriaModel model) {
        List<ChannelVo> objects = statsCommonService.setChannelAllList(model);
        BigDecimal total;
        if (model.getDataType().equals(DataEnum.numUsers)) {
            model.SetUpCycle();
            final String token =
                    StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                            SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
            model.setAccessToken(token);
            ExecutorService executorService = Executors.newFixedThreadPool(1);
            Future<Map<String, Long>> userFuture = executorService.submit(() -> emotionUserDiService.userNum(model));
            VocOverBriefingValueVo re = new VocOverBriefingValueVo();
            try {
                Map<String, Long> user = userFuture.get();
                if (user != null) {
                    re.setUsersNum(BigDecimal.valueOf(user.get("userCount")));
                }
            } catch (Exception e) {
                e.printStackTrace();
                // Handle exceptions
            } finally {
                executorService.shutdown();
            }
            total = re.getUsersNum();
        } else {
            total = objects.stream().map(ChannelVo::getStatistic).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        }

        // 使用新的批量占比计算方法
        CalculatorUtils.proportion(objects, ChannelVo::getStatistic, ChannelVo::setStatisticP, total);
        List<ChannelVo> collect = objects.stream().sorted(Comparator.comparing(ChannelVo::getStatistic).reversed()).collect(Collectors.toList());
        return Result.OK(collect);
    }


    @Override
    public Result<HomeIntentionReportVo> homeIntentionBriefingConsult(FilterCriteriaModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        HomeIntentionReportVo vo = new HomeIntentionReportVo();
        Map<String, BigDecimal> cb = intentionDiMapper.purposeCb(model);
        if (cb == null) {
            return Result.OK(vo);
        }
        cb.put("总数", ((BigDecimal) cb.get("其他")).add((BigDecimal) cb.get("咨询")).add((BigDecimal) cb.get("投诉")).add((BigDecimal) cb.get("表扬")));
        if (cb != null) {
            BigDecimal cs = (BigDecimal) cb.get("总数");
            String value = "咨询";
            vo.setPurpose(value);
            vo.setIntention((BigDecimal) cb.get(value));
            vo.setIntentionP(CalculatorUtils.proportion((BigDecimal) cb.get(value), cs));
            model.setIntention(value);
            model.setEndDate(model.getDateE());
            model.setStartDate(model.getDateS());
            setIntentionCards(vo, model);
        }
        return Result.OK(vo);
    }

    @Override
    public Result<HomeIntentionReportVo> homeIntentionUserNum(FilterCriteriaModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        HomeIntentionReportVo reportVo = new HomeIntentionReportVo();

        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());

        //===================1.查询提及量、用户数的环比=====开始===================
        Map<String, BigDecimal> cb = intentionDiMapper.purposeTjl(model, reportVo);
        Map<String, BigDecimal> us = intentionDiMapper.queryUserNum(model);
        if (cb != null) {
            reportVo.setIntention(cb.get("totalMentions"));
            reportVo.setIntentionR(CalculatorUtils.ringRatio(reportVo.getIntention(), cb.get("totalMentionsUp")));
            reportVo.setUserCount(us.get("thisUserNum"));
            reportVo.setUserCountR(CalculatorUtils.ringRatio(reportVo.getUserCount(), us.get("thisUserNumUp")));
        } else {
            reportVo.setUserCount(new BigDecimal(0));
        }
        //===================1.查询提及量、用户数的环比=====结束===================

        return Result.OK(reportVo);
    }


    @Override
    public Result<?> homeIntentionTrend(FilterCriteriaModel model) {
        //修改环比计算+时间范围补齐
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        HomeIntentionReportVo reportVo = new HomeIntentionReportVo();

        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());


        //===================2.查询提及量与用户趋势=====开始===================
        final int index = 6;
        List<SvwDate> dates = model.getDateTimesByCustom(index);
        if(model.getDateUnit()!=-1){
            int si = dates.size() >= index ? dates.size() - index - 1 : 0;
            dates = dates.subList( si > 0 ? si : 0, dates.size()) ;
        }
        model.setDateList(dates);
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));
        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        List<HomePurposeTrendVo> objectLists = intentionUserDiMapper.intentionNumAndUserNumList(model);
        if (CollUtil.isEmpty(objectLists)) {
            return Result.OK(Collections.EMPTY_LIST);
        }

        //转日期格式
        final Set<String> containsDates = objectLists.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objectLists.addAll(dateTempSet.stream()
                .map(e -> {
                    HomePurposeTrendVo vo = new HomePurposeTrendVo();
                    vo.setDateStr(e);
                    return vo;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objectLists.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));

        for (int i = 1; i < objectLists.size(); i++) {  //第二条开始处理
            HomePurposeTrendVo obj = objectLists.get(i);  //当前
            HomePurposeTrendVo preObj = objectLists.get(i - 1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            if (days== null || preDays == null){
                continue;
            }
            //设置日均 averagePerDay()
            if (model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
            } else {
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(preDays));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }
            if (model.getDateUnit().intValue() == -1) {
                obj.setUserCountAR(BigDecimal.ZERO);
            } else {

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj) ? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(preDays));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays, preObjAvgDays));
            }

            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
//            obj.setIntentionAR(CalculatorUtils.ringRatio(obj.getIntentionA(), preObj.getIntentionA()));
//            obj.setUserCountAR(CalculatorUtils.ringRatio(obj.getUserCountA(), preObj.getUserCountA()));

        }
        objectLists.stream()
                .forEach(e -> {
                    if (model.getDateUnit().intValue() == -1) {
                        e.setDateStr(e.getDateStr().replaceAll("-", "/"));
                    } else {
                        e.setDateStr(this.coveringDate2(e.getDateStr()));
                    }
                });
        if (objectLists.size() > index || model.getDateUnit() == -1) {
            int s = objectLists.size() >= index ? objectLists.size() : index;
            objectLists = new ArrayList<>(objectLists.subList(  s >= objectLists.size()  ? 1 : s - index  , objectLists.size()));
        }
        reportVo.setPurposeTrends(objectLists);


        //===================2.查询提及量与用户趋势=====结束===================

        return Result.OK(reportVo.getPurposeTrends());
    }

    /**
     * @param o1Str
     * @return
     */
    private String coveringDate2(String o1Str) {
        if (StrUtil.isNotBlank(o1Str)) {
            String str = o1Str;
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.startsWith("0") ? s_index.replace("0", "") : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.startsWith("0") ? t_index.replace("0", "") : t_index);
                }
            }
            return str;
        }

        return null;
    }

    /**
     * 补位
     *
     * @param o1Str
     * @return
     */
    private String coveringDate(String o1Str) {

//        objectLists.stream().forEach(e -> {
//        Optional<Object> o1Str = Optional.ofNullable(ReflectUtil.getFieldValue(obj, attName));

        if (StrUtil.isNotBlank(o1Str)) {
            String str = String.valueOf(o1Str).replaceAll("/", "-");
            final String[] arr = str.split("-");
            if (arr.length >= 1) {
                //补位  2023-9  ->  2023-09
                final String f_index = arr[0];
                str = f_index;
                if (arr.length >= 2) {
                    final String s_index = arr[1];
                    str = str.concat("-").concat(s_index.length() < 2 ? "0".concat(s_index) : s_index);
                }
                if (arr.length >= 3) {
                    final String t_index = arr[2];
                    str = str.concat("-").concat(t_index.length() < 2 ? "0".concat(t_index) : t_index);
                }
            }
            return str;
        }

        return null;
    }

    @Override
    public Result<?> homeIntentionTop(FilterCriteriaModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        HomeIntentionReportVo reportVo = new HomeIntentionReportVo();

        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());


        //===================3.查询提及top与飙升top=====开始===================
        model.setRownum(1000);
        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());
        List<TopTopicVo> tjtopsThis = intentionDiMapper.mentionTop(model, reportVo);
        final Set<String> codes = tjtopsThis.stream().map(TopTopicVo::getLabelCode).collect(Collectors.toSet());
        model.SetUpCycle();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(codes);
        List<TopTopicVo> tjtopsUp = intentionDiMapper.mentionTop(model, reportVo);


        tjtopsThis.forEach(tjtopsThi -> {
            List<TopTopicVo> upRef = tjtopsUp.stream().filter(s -> s.getLabelCode().equals(tjtopsThi.getLabelCode())).collect(Collectors.toList());
            if (CollUtil.isEmpty(upRef)) {
                tjtopsThi.setPercentage(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), null));
                tjtopsThi.setRingRatio(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), null));
            } else {
                tjtopsThi.setPercentage(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
                tjtopsThi.setRingRatio(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
            }
        });
        int indext = model.isExcel() ? model.getDownloadTagNum() : 10;
        List<TopTopicVo> mtop = tjtopsThis.stream().sorted(Comparator.comparing(TopTopicVo::getReferenceNum, Comparator.nullsLast(BigDecimal::compareTo)).reversed()).collect(Collectors.toList());
        List<TopTopicVo> mttp=new ArrayList<>(mtop.subList(0, tjtopsThis.size() >= indext ? indext : tjtopsThis.size()));
        reportVo.setMentionTop(BeanUtil.copyToList(mttp,TopTopicVo.class));

//        List<TopTopicVo> thisvoList = BeanUtil.copyToList(tjtopsThis, TopTopicVo.class);
//        List<TopTopicVo> upvoList = BeanUtil.copyToList(tjtopsUp, TopTopicVo.class);
//        List<TopTopicVo> voList;


        BigDecimal btotal = tjtopsThis.stream().map(TopTopicVo::getReferenceNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<TopTopicVo> avgbefre;
        if (tjtopsThis != null && tjtopsThis.size() > 0) {
            avge = NumberUtil.div(btotal, tjtopsThis.size());
            avgbefre = tjtopsThis.stream().filter(e -> (e.getReferenceNum().compareTo(avge)) > 0).collect(Collectors.toList());
        } else {
            avgbefre = tjtopsThis;
        }

        List<TopTopicVo> jihe = new ArrayList<>();
        //若大于平均值的标签的个数小于等于20，则取大于平均值的标签总个数的一半
        if(ObjectUtil.isNotEmpty(avgbefre)&&avgbefre.size()<=20){
            jihe = tjtopsThis.subList(0,tjtopsThis.size()/2);
        }else {
            jihe = avgbefre;
        }

//        List<TopTopicVo> jihe = CollUtil.newCopyOnWriteArrayList(avgbefre);
        jihe.forEach(tjtopsThi -> {
            List<TopTopicVo> upRef = tjtopsUp.stream().filter(s -> s.getLabelCode().equals(tjtopsThi.getLabelCode())).collect(Collectors.toList());
            if (CollUtil.isEmpty(upRef)) {
                tjtopsThi.setPercentage(BigDecimal.ZERO);
                tjtopsThi.setRingRatio(BigDecimal.ZERO);
            } else {
                tjtopsThi.setPercentage(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
                tjtopsThi.setRingRatio(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
            }
        });
        if (jihe != null && jihe.size() > 0) {
            jihe = jihe.stream().filter(e -> (e.getRingRatio() != null && e.getRingRatio().floatValue() > 0)).collect(Collectors.toList());
            List<TopTopicVo> sotop = jihe.stream().sorted(Comparator.comparing(TopTopicVo::getRingRatio, Comparator.nullsFirst(BigDecimal::compareTo)).reversed()).collect(Collectors.toList());
            List<TopTopicVo> srtp=new ArrayList<>(sotop.subList(0, sotop.size() >= indext ? indext : sotop.size()));
            reportVo.setSoarTop(BeanUtil.copyToList(srtp,TopTopicVo.class));
        }

        //===================3.查询提及top与飙升top=====结束===================
        if (reportVo.getSoarTop() == null) {
            reportVo.setSoarTop(new ArrayList<>());
        }
        return Result.OK(reportVo);
    }

    @Override
    public Result<HomeIntentionReportVo> homeIntentionBriefingComplaint(FilterCriteriaModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        HomeIntentionReportVo vo = new HomeIntentionReportVo();
        Map<String, BigDecimal> cb = intentionDiMapper.purposeCb(model);
        if (cb == null) {
            return Result.OK(vo);
        }
        cb.put("总数", ((BigDecimal) cb.get("其他")).add((BigDecimal) cb.get("咨询")).add((BigDecimal) cb.get("投诉")).add((BigDecimal) cb.get("表扬")));
        if (cb != null) {
            BigDecimal cs = (BigDecimal) cb.get("总数");
            String value = "投诉";
            vo.setPurpose(value);
            vo.setIntention((BigDecimal) cb.get(value));
            vo.setIntentionP(CalculatorUtils.proportion((BigDecimal) cb.get(value), cs));
            model.setIntention(value);
            model.setEndDate(model.getDateE());
            model.setStartDate(model.getDateS());
            setIntentionCards(vo, model);
        }
        return Result.OK(vo);
    }

    @Override
    public Result<Map> emotionValue(FilterCriteriaModel model) {
        EmotionProportionVo re = dwsVocEmotionDiMapper.focusProportionEmotion(model);
        Map<String, Object> ev = new HashMap<>();
        if (re != null && re.getPositive() != null) {
            ev.put("emotionValue", CalculatorUtils.getEmotionalNetWorth(re.getPositive(), re.getNegative()));
        } else {
            ev.put("emotionValue", null);
        }
        return Result.OK(ev);
    }

    @Async
    public void setIntentionCards(HomeIntentionReportVo reportVo, FilterCriteriaModel model) {

        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());


        //===================1.查询提及量、用户数的环比=====开始===================
        Map<String, BigDecimal> cb = intentionDiMapper.purposeTjl(model, reportVo);
        Map<String, BigDecimal> us = intentionDiMapper.queryUserNum(model);
        if (cb != null) {
            reportVo.setIntention(cb.get("totalMentions"));
            reportVo.setIntentionR(CalculatorUtils.ringRatio(reportVo.getIntention(), cb.get("totalMentionsUp")));
            reportVo.setUserCount(us.get("thisUserNum"));
            reportVo.setUserCountR(CalculatorUtils.ringRatio(reportVo.getUserCount(), us.get("thisUserNumUp")));
        } else {
            reportVo.setUserCount(new BigDecimal(0));
        }
        //===================1.查询提及量、用户数的环比=====结束===================


        //===================2.查询提及量与用户趋势=====开始===================
        List<SvwDate> dates = model.getDateTimes();

        int a = 0;
        model.setDateList(model.getDateUnit() == -1 ? dates : dates.subList(dates.size() >= 7 ? dates.size() - 7 : 0, dates.size()));
        int index = model.isExcel() ? model.getDownloadNum() : 6;
        final Map<String, Long> dateR = dates.stream().collect(Collectors.toMap(e -> this.coveringDate(e.getTime()) ,
                e -> DateUtil.between(DateUtil.parse(e.getEndDate()), DateUtil.parse(e.getStartDate()), DateUnit.DAY) + 1,
                (k1, k2) -> k1));
        model.setStartDate(model.getDateList().get(0).getStartDate());
        model.setEndDate(model.getDateList().get(model.getDateList().size() - 1).getEndDate());
        List<HomePurposeTrendVo> objects = intentionUserDiMapper.intentionNumAndUserNumList(model);

        //转日期格式
        final Set<String> containsDates = objects.stream()
                .map(e -> {
                    e.setDateStr(this.coveringDate(e.getDateStr()));
                    return e.getDateStr();
                })
                .collect(Collectors.toSet());
        //时间范围
        final Map<String, SvwDate> dateMap = dates.stream()
                .collect(Collectors.toMap(e -> this.coveringDate(e.getTime()), e -> e, (k1, k2) -> k1));

        final Set<String> allDates = dateMap.keySet();
        final Set<String> dateTempSet = allDates.stream().filter(e -> !containsDates.contains(e)).collect(Collectors.toSet());

        //补齐日期
        objects.addAll(dateTempSet.stream()
                .map(e -> {
                    HomePurposeTrendVo vo = new HomePurposeTrendVo();
                    vo.setDateStr(e);
                    return vo ;
                })
                .collect(Collectors.toList())
        );

        //时间排序
        objects.sort(Comparator.comparing(HomePurposeTrendVo::getDateStr, Comparator.nullsFirst(String::compareTo)));


        for(int i = 1; i < objects.size()  ; i++){  //第二条开始处理
            HomePurposeTrendVo obj = objects.get(i);  //当前
            HomePurposeTrendVo preObj = objects.get(i-1);   //前一个
            final Long days = dateR.get(obj.getDateStr());
            final Long preDays = dateR.get(preObj.getDateStr());
            //设置日均 averagePerDay()
            if(model.getDateUnit().intValue() == -1) {
                obj.setIntentionAR(BigDecimal.ZERO);
            }else{
                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getIntention(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getIntention(), new BigDecimal(preDays));
                obj.setIntentionA(objAvgDays);
                obj.setIntentionAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }
            if(model.getDateUnit().intValue() == -1) {
                obj.setUserCountAR(BigDecimal.ZERO);
            }else{

                final BigDecimal objAvgDays = CalculatorUtils.avgePerDayNum(obj.getUserCount(), new BigDecimal(days));
                final BigDecimal preObjAvgDays = ObjectUtil.isNull(preObj)? null : CalculatorUtils.avgePerDayNum(preObj.getUserCount(), new BigDecimal(preDays));
                obj.setUserCountA(objAvgDays);
                obj.setUserCountAR(CalculatorUtils.ringRatio(objAvgDays,preObjAvgDays));
            }

            obj.setUserCountR(CalculatorUtils.ringRatio(obj.getUserCount(), preObj.getUserCount()));
//            obj.setUserCountAR(CalculatorUtils.ringRatio(obj.getUserCountA(), preObj.getUserCountA()));
            obj.setIntentionR(CalculatorUtils.ringRatio(obj.getIntention(), preObj.getIntention()));
//            obj.setIntentionAR(CalculatorUtils.ringRatio(obj.getIntentionA(), preObj.getIntentionA()));

        }


        if (objects.size() >= index && model.getDateUnit() > -1) {
            objects.remove(0);
        }

        reportVo.setPurposeTrends(model.getDateUnit() == -1 ? objects : objects.subList(objects.size() >= 6 ? objects.size() - 6 : 0, objects.size()));


        //===================2.查询提及量与用户趋势=====结束===================


        //===================3.查询提及top与飙升top=====开始===================
        model.setRownum(1000);
        model.setStartDate(model.getDateS());
        model.setEndDate(model.getDateE());
        List<TopTopicVo> tjtopsThis = intentionDiMapper.mentionTop(model, reportVo);
        Set<String> codes = tjtopsThis.stream().map(TopTopicVo::getLabelCode).collect(Collectors.toSet());
        model.SetUpCycle();
        model.setStartDate(model.getStartDateUp());
        model.setEndDate(model.getEndDateUp());
        model.setTopicCodes(codes);
        List<TopTopicVo> tjtopsUp = intentionDiMapper.mentionTop(model, reportVo);


        tjtopsThis.forEach(tjtopsThi -> {
            List<TopTopicVo> upRef = tjtopsUp.stream().filter(s -> s.getLabelCode().equals(tjtopsThi.getLabelCode())).collect(Collectors.toList());
            if (upRef != null && upRef.size() > 0 && upRef.get(0) != null && upRef.get(0).getReferenceNum() != null && !upRef.get(0).getReferenceNum().equals(new BigDecimal(0))) {
                tjtopsThi.setPercentage(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
                tjtopsThi.setRingRatio(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
            }
        });
        int indext = model.isExcel() ? model.getDownloadTagNum() : 10;
        List<TopTopicVo> mtop = tjtopsThis.stream().sorted(Comparator.comparing(TopTopicVo::getReferenceNum, Comparator.nullsLast(BigDecimal::compareTo)).reversed()).collect(Collectors.toList());
        reportVo.setMentionTop(mtop.subList(0, tjtopsThis.size() >= indext ? indext : tjtopsThis.size()));

        BigDecimal btotal = tjtopsThis.stream().map(TopTopicVo::getReferenceNum).reduce(BigDecimal.ZERO, BigDecimalUtils::sum);
        BigDecimal avge;
        List<TopTopicVo> avgbefre;
        if (tjtopsThis != null && tjtopsThis.size() > 0) {
            avge = NumberUtil.div(btotal, tjtopsThis.size());
            avgbefre = tjtopsThis.stream().filter(e -> (e.getReferenceNum().compareTo(avge)) > 0).collect(Collectors.toList());
        } else {
            avgbefre = tjtopsThis;
        }

        List<TopTopicVo> jihe = CollUtil.newCopyOnWriteArrayList(avgbefre);
        jihe.forEach(tjtopsThi -> {
            List<TopTopicVo> upRef = tjtopsUp.stream().filter(s -> s.getLabelCode().equals(tjtopsThi.getLabelCode())).collect(Collectors.toList());
            if (upRef != null && upRef.size() > 0 && upRef.get(0) != null && upRef.get(0).getReferenceNum() != null && !upRef.get(0).getReferenceNum().equals(new BigDecimal(0))) {
                tjtopsThi.setRingRatio(CalculatorUtils.ringRatio(tjtopsThi.getReferenceNum(), upRef.get(0).getReferenceNum()));
            }
        });
        if (jihe != null && jihe.size() > 0) {
            jihe = jihe.stream().filter(e -> (e.getRingRatio() != null && e.getRingRatio().floatValue() > 0)).collect(Collectors.toList());
            List<TopTopicVo> sotop = jihe.stream().sorted(Comparator.comparing(TopTopicVo::getRingRatio, Comparator.nullsFirst(BigDecimal::compareTo)).reversed()).collect(Collectors.toList());
            reportVo.setSoarTop(sotop.subList(0, sotop.size() >= indext ? indext : sotop.size()));
        }

        //===================3.查询提及top与飙升top=====结束===================

    }


}
