package com.car.stats.serivce.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.stats.entity.risk.DwdVocUserRisk;
import com.car.stats.mapper.DwdVocUserRiskMapper;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocUserRiskService;
import com.car.stats.serivce.IDwsVocUserService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.popvo.IntentionChannelStatisticVo;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.service.IVocRiskWarningRulesService;
import com.car.voc.vo.risk.RiskRuleVo;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * @version 1.0.0
 * @ClassName DwdVocUserRiskServiceImpl.java
 * @Description TODO
 * @createTime 2022年12月21日 11:11
 * @Copyright voc
 */

@Service
public class DwdVocUserRiskServiceImpl extends ServiceImpl<DwdVocUserRiskMapper, DwdVocUserRisk> implements IDwdVocUserRiskService {

    @Autowired
    IDwsVocUserService dwsVocUserService;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    EsDataSentenceVocService sentenceVocService;

    @Autowired
    Executor defExecutor;
    @Override
    public IPage<ComplaintUserVo> complaintUserList(ComplaintUserTopModel model) {
        Page<ComplaintUserVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        final String token = StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken():
                SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        VocRiskWarningRules warningRules = vocRiskWarningRulesService.getByBrandCode(model.getBrandCode(), "高频投诉用户");


        RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(warningRules.getId());


        if (ObjectUtils.isNotEmpty(vo)) {
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumD()) && !vo.getRiskWordsNumD().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumW()) && !vo.getRiskWordsNumW().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumM()) && !vo.getRiskWordsNumM().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumQ()) && !vo.getRiskWordsNumQ().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
            if (ObjectUtils.isNotEmpty(vo.getRiskWordsNumY()) && !vo.getRiskWordsNumY().equals(BigDecimal.ZERO)) {
                model.setRiskRuleType("1");
            }
        }

        IPage<ComplaintUserVo> pages = baseMapper.complaintUserList(page, model, vo);


        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        CopyOnWriteArrayList<ComplaintUserVo> recordsList = CollUtil.newCopyOnWriteArrayList(pages.getRecords());

        model.setAccessToken(token);
        recordsList.stream().forEach(e -> {
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {

                ComplaintUserTopModel target = new ComplaintUserTopModel();
                BeanUtils.copyProperties(model, target);// 使用Spring的BeanUtils.copyProperties()方法来复制对象，保证线程安全
                e.setDateUnit(CalculatorUtils.periodStrToNum(e.getStatisticType()));
                e.setStartDate(DateUtil.formatDateTime(e.getPublishDate()));
                e.setEndDate(CalculatorUtils.getRiskEndDate(e.getDateUnit(), e.getPublishDate()));


                BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
                if (StrUtil.isNotBlank(target.getStartDate()) && StrUtil.isNotBlank(target.getEndDate())) {
                    queryBuilder.must(QueryBuilders.rangeQuery("publishTime").gt(target.getStartDate()).lte(target.getEndDate()).includeLower(true).includeUpper(true));
                }
                RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
                Set<Object> channelSet = redisUtil.sGet(CacheConstant.SYS_USER_TAG_OTHER);
                queryBuilder.mustNot(QueryBuilders.termsQuery("topicCode.keyword", channelSet));

                if (StrUtil.isNotBlank(e.getUserId())) {
                    queryBuilder.must(QueryBuilders.termQuery("oneId.keyword", e.getUserId()));
                }

                if (Objects.nonNull(e.getRiskIndex())) {
                    //20231012修改读取风险数据库
                    String riskLevel = vocRiskWarningRulesService.getWarnRuleDetailListByIdBrandCode("高频投诉用户", e.getBrandCode(), e.getRiskIndex());

                    e.setRiskGradeTag(riskLevel);
                }
                queryBuilder.must(QueryBuilders.termsQuery("tagType", "1"));
                queryBuilder.must(QueryBuilders.termsQuery("intentionType.keyword", "投诉"));

                List<IntentionChannelStatisticVo> vos = new ArrayList<>();

                sentenceVocService.setUserPublishCount(e, e.getUserId(), vos, queryBuilder);
                e.setSoundNum(vos.stream().filter(s -> "全部".equals(s.getChannelStr())).findFirst().orElse(null).getStatistic());
                return null;
            })));
        });
        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            e.printStackTrace();
        }
        pages.setRecords(recordsList);

        return pages;
    }

    @Autowired
    IVocRiskWarningRulesService vocRiskWarningRulesService;

    @Override
    public BigDecimal getComplaintUser(FilterCriteriaModel model) {
        RiskRuleVo vo = vocRiskWarningRulesService.getRiskEmotionRule(CommonConstant.sys_risk_user_rule);
        return baseMapper.getComplaintUser(model, vo);
    }

    @Override
    public List<DwdVocUserRisk> riskUserFiltering(VocRiskWarningRules rule, RiskRuleVo vo) {
        return baseMapper.riskUserFiltering(rule, vo);
    }

    @Override
    public List<DwdVocUserRisk> riskUserFilteringNew(VocRiskWarningRules riskWarningRules, RiskRuleVo vo) {
        return baseMapper.riskUserFilteringNew(riskWarningRules, vo);
    }
}
