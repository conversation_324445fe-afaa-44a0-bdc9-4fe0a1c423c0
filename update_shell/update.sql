ALTER TABLE voc_dongfeng.voc_fault_problem ADD tag_scope varchar(100) DEFAULT 0 NULL COMMENT '标签作用域：0:全部 1:VOC 2:CC';
ALTER TABLE voc_dongfeng.voc_fault_problem ADD order_type varchar(100) NULL COMMENT '工单类型 1:咨询 2:投诉 3:建议 4:救援 5:商机';

UPDATE voc_business_tag a
    LEFT JOIN (
        SELECT DISTINCT pid
        FROM voc_business_tag
        WHERE pid IS NOT NULL AND pid != ''
    ) b ON a.id = b.pid
SET a.has_child = CASE
                      WHEN b.pid IS NOT NULL THEN '1'
                      ELSE '0'
    END;


UPDATE voc_fault_problem  a
    LEFT JOIN (
        SELECT DISTINCT pid
        FROM voc_fault_problem
        WHERE pid IS NOT NULL AND pid != ''
    ) b ON a.id = b.pid
SET a.has_child = CASE
                      WHEN b.pid IS NOT NULL THEN '1'
                      ELSE '0'
    END;
