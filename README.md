# VOC 系统

车辆全生命周期客户之声（VOC）管理系统，基于Spring Boot的模块化架构，集成工作流引擎与大数据分析能力。

## 功能全景图
### 1. 工单中心
- **核心控制器**：<mcsymbol name="LastDealWorkOrderController" filename="LastDealWorkOrderController.java" path="d:\IdeaProjects\voc-demo-api\car-voc-app\src\main\java\com\car\voc\stats\controller\LastDealWorkOrderController.java" startline="23" type="class"></mcsymbol>
- 功能特性：
  - 工单全生命周期管理（新建→分配→处理→关闭）
  - 语义分析（NLP）驱动的客户情绪识别
  - 多维度工单统计（类型/状态/处理时效）

### 2. 案例知识库
- **核心控制器**：<mcsymbol name="CaseLibraryController" filename="CaseLibraryController.java" path="d:\IdeaProjects\voc-demo-api\car-voc-case\src\main\java\com\car\voc\cases\CaseLibraryController.java" startline="75" type="class"></mcsymbol>
- 功能特性：
  - 案例多维检索（品牌/车型/故障类型）
  - 案例关联分析（相似问题解决方案推荐）
  - 知识沉淀机制（优秀案例标星收藏）

### 3. 质量预警系统
- **流程控制**：<mcsymbol name="VocRiskProessRecipientMapper" filename="VocRiskProessRecipientMapper.java" path="d:\IdeaProjects\voc-demo-api\car-voc-service\src\main\java\com\car\voc\mapper\VocRiskProessRecipientMapper.java" startline="20" type="class"></mcsymbol>
- 核心能力：
  - 风险规则引擎（阈值触发预警）
  - 审核工作流（多级审批链条）
  - 预警分发策略（钉钉/邮件通知）

### 4. 数据分析中心
- **核心服务**：<mcsymbol name="EsDataSentenceVocService" filename="EsDataSentenceVocService.java" path="d:\IdeaProjects\voc-demo-api\car-voc-service\src\main\java\com\car\stats\serivce\es\EsDataSentenceVocService.java" startline="32" type="interface"></mcsymbol>
- 分析维度：
  - 质量问题分布热力图
  - 部件故障趋势分析
  - 服务质量KPI统计

### 5. 系统管理
- **基础服务**：<mcsymbol name="ModelGroupController" filename="ModelGroupController.java" path="d:\IdeaProjects\voc-demo-api\car-voc-admin\src\main\java\com\car\voc\admin\controller\ModelGroupController.java" startline="43" type="class"></mcsymbol>
- 管理功能：
  - 组织机构权限管理（RBAC模型）
  - 车型组数据维护
  - 系统参数配置

## 技术架构
### 分层架构

### 模块划分
```bash
car-voc-admin    # 管理后台核心模块
car-voc-service  # 业务服务实现
car-voc-core     # 公共组件库
car-voc-app      # 移动端接口
car-voc-case     # 案例库服务
car-voc-report   # 报表服务
car-voc-sound    # 声音聆听

## 核心配置
### 多环境配置
```yaml
spring:
  profiles:
    active: dev # 环境切换（dev/test/prod）

# 加密配置（Jasypt）
jasypt.encryptor:
  password: ENC(加密密钥) 
 ```

### 数据源配置
```yaml
spring:
  datasource:
    druid:
      url: ************************************
      username: ENC(加密账号)
      password: ENC(加密密码)
  redis:
    host: 127.0.0.1
    port: 6379
 ```

## 接口规范
### 基础路径
```text
/api       # 主接口路径
/actuator  # 健康检查
/prometheus # 监控指标
 ```

### 示例接口 模块 端点 方法 说明 工单管理

/complaints/tagDistribution

POST

工单标签分布统计 案例库

/case/reportList

GET

案例多维分析报表 质量预警

/risk/process

PUT

预警流程配置 系统管理

/admin/modelGroup

POST

车型组数据维护
Swagger在线文档

## 部署指南
### 本地启动
```bash
mvn clean install
cd car-voc-admin
mvn spring-boot:run -Dspring-boot.run.profiles=dev
 ```

### Docker部署

# 构建镜像
```bash
docker build -t voc-admin:latest -f k8s/Dockerfile .
 ```
# 运行容器
```bash
docker run -p 6017:6017 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e MYSQL_HOST=voc-mysql \
  voc-admin:latest
 ```


## 监控维护
### 健康检查端点
```text
/actuator/health        # 服务健康状态
/actuator/metrics       # JVM指标监控
/actuator/prometheus    # Prometheus格式指标
 ```

### 日志配置
```xml
<!-- 日志分级存储 -->
<appender name="ERROR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
        <level>ERROR</level>
    </filter>
    <!-- 日志路径配置 -->
</appender>
 ```


## 贡献指南
1. 分支策略

  - main ：生产环境代码
  - dev ：主要开发分支
  - feature/* ：特性开发分支
2. 代码规范
```bash
# 提交前检查
mvn checkstyle:check
mvn pmd:check
 ```

3. 数据库变更
```text
car-voc-admin/src/main/resources/db/migration # Flyway迁移脚本目录
 ```
