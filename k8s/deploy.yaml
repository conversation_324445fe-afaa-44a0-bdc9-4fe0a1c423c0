kind: Deployment
apiVersion: apps/v1
metadata:
  name: 'K8S_APP_NAME'
  namespace: 'NAMESPACE'
  labels:
    app: 'K8S_APP_NAME'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: 'K8S_APP_NAME'
  template:
    metadata:
      labels:
        app: 'K8S_APP_NAME'
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: volume
          emptyDir: {}
        - name: logs
          hostPath:
            path: /data/logs/projects
            type: ''
        - name: volume-bootstrap
          configMap:
            name: K8S_APP_NAME
            items:
              - key: K8S_APP_NAME_bootstrap.properties
                path: bootstrap.properties
            defaultMode: 420
        - name: volume-logback
          configMap:
            name: logback-spring-config
            items:
              - key: logback-spring.xml
                path: logback-spring.xml
            defaultMode: 420
      imagePullSecrets:
      - name: harbor
      initContainers:
        - name: container-jar
          image: 'REGISTRY/HARBOR_NAMESPACE/K8S_APP_NAME:TAG'
          command:
            - /bin/sh
            - '-c'
          args:
            - mv /tmp/*.jar /app/app.jar
          ports:
            - name: http-8080
              containerPort: 8080
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: volume
              mountPath: /app
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      containers:
        - name: container-jdk
          image: ************:30002/library/jdk-8u332:dragonwell-8.11.12_jdk8u332-ga
          ports:
            - name: http-8080
              containerPort: 8080
              protocol: TCP
          env:
            - name: JAVA_OPTS
              valueFrom:
                configMapKeyRef:
                  name: K8S_APP_NAME
                  key: K8S_APP_NAME_java-opts
            - name: NACOS_SERVER_ADDR_URLS
              valueFrom:
                configMapKeyRef:
                  name: nacos-url
                  key: nacos-server-urls

          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: volume
              mountPath: /app
            - name: logs
              mountPath: /applogs
            - name: volume-bootstrap
              readOnly: true
              mountPath: /bootstrap.properties
              subPath: bootstrap.properties
            - name: volume-logback
              readOnly: true
              mountPath: /logback-spring.xml
              subPath: logback-spring.xml
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 3
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 10
          readinessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 3
            periodSeconds: 5
            successThreshold: 3
            failureThreshold: 10
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
---
kind: Service
apiVersion: v1
metadata:
  name: 'K8S_APP_NAME'
  namespace: 'NAMESPACE'
  labels:
    app: 'K8S_APP_NAME'
spec:
  ports:
    - name: http-8080
      protocol: TCP
      port: 8080
      targetPort: 8080
  selector:
    app: 'K8S_APP_NAME'
  type: ClusterIP
