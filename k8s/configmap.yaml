kind: ConfigMap
apiVersion: v1
metadata:
  name: 'K8S_APP_NAME'
  namespace: 'NAMESPACE'
data:
  K8S_APP_NAME_java-opts: |-
    -javaagent:/agent/skywalking-agent.jar 
    -Dskywalking.agent.service_name=K8S_APP_NAME
    -Dspring.profiles.active=PROFILE
    -Dskywalking.collector.backend_service=skywalking-oap-dev.default-dev:11800
    -Dlogging.config=/logback-spring.xml
    -Dspring.cloud.nacos.config.enabled=true
    -Dspring.cloud.nacos.discovery.enabled=true
    -Dserver.port=8080
    -Dfile.encoding=UTF-8
    -server 
    -Xmx2024m -Xms1024m 
    -Xmn256m 
    -XX:+UseG1GC 
    -XX:GCTimeRatio=99 
    -XX:MaxGCPauseMillis=20 
    -XX:MetaspaceSize=256m 
    -XX:MaxMetaspaceSize=256m 
    -XX:+PrintGC 
    -XX:+PrintTenuringDistribution 
    -XX:+PrintGCTimeStamps 
    -XX:ParallelGCThreads=16
    -Xloggc:/applogs/gc.log
  K8S_APP_NAME_bootstrap.properties: >-
    spring.application.name=K8S_APP_NAME

    spring.cloud.nacos.config.server-addr=${NACOS_SERVER_ADDR_URLS:172.16.80.22:31061}

    spring.cloud.nacos.config.file-extension=yml

    spring.cloud.nacos.config.group=${NACOS_CONFIG_GROUP:DEFAULT_GROUP}

    spring.cloud.nacos.config.namespace=${NACOS_CONFIG_NAMESPACE:dev}

    spring.cloud.nacos.config.extension-configs[0].data-id=common-nacos.yml

    spring.cloud.nacos.config.extension-configs[1].data-id=common.yml

    spring.cloud.nacos.config.extension-configs[2].data-id=sentinel.yml

    spring.cloud.nacos.config.extension-configs[3].data-id=common-mysql.yml

    spring.cloud.nacos.config.extension-configs[4].data-id=common-redis.yml

    spring.cloud.nacos.config.extension-configs[5].data-id=common-es.yml
