package com.car.voc.common.sync;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
@Data
public class GetCCApiTokenService {
    @Value("${configuration.tokenCheckToken.baseUrl}")
    String baseUrl;
    @Value("${configuration.tokenCheckToken.hybridBaseUrl}")
    String hybridBaseUrl;
    @Value("${configuration.tokenCheckToken.bffBaseUrl}")
    String bffBaseUrl;
    @Value("${configuration.tokenCheckToken.getUserInfoPath}")
    String getUserInfoPath;
    @Value("${configuration.tokenCheckToken.clientId}")
    String clientId;
    @Value("${configuration.tokenCheckToken.clientSecret}")
    String clientSecret;



    public  String GetToken() {
//        String url = "https://cc.dfes.com.cn/api/oauth2/oauth/token";
//        String url = baseUrl+"/api/oauth2/oauth/token";
        String url = baseUrl+"/oauth2/oauth/token";
        String requestBody = "grant_type=client_credentials&client_id="+clientId+"&client_secret="+clientSecret;
        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/x-www-form-urlencoded");
        request.body(requestBody);
        HttpResponse response = request.execute();
       log.info("获取token返回结果：{}",JSONUtil.parseObj(response.body()));
        return JSONUtil.parseObj(response.body()).getStr("access_token");
    }
    public JsonNode fromCCGetRoles() {
        CloseableHttpClient client = HttpClients.createDefault();
//        hybridBaseUrl="https://cc.dfes.com.cn/api/hybrid/vc/";
        HttpPost post = new HttpPost(hybridBaseUrl+"/authority/role/page");
        post.setHeader("Authorization", "Bearer " + GetToken());
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Cookie", "47975a629103793c3147b787ed00d5db=79f5bec0fa8d21b299521adb7bf9b51d");
        String jsonBody = "{\"page\": {\"currentPage\": 1, \"pageSize\": 400}, \"search\": {\"keyword\":\"voc\"}}";
        JsonNode roleList = null;
        try {
            post.setEntity(new StringEntity(jsonBody));
            CloseableHttpResponse response = client.execute(post);
            ObjectMapper mapper = new ObjectMapper();
            roleList = mapper.readTree(response.getEntity().getContent());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return roleList;
    }

    public JsonNode queryUsersByRoleId(String roleId) {
        CloseableHttpClient client = HttpClients.createDefault();
//        bffBaseUrl="https://cc.dfes.com.cn/api/bff/graphql";
        HttpPost post = new HttpPost(bffBaseUrl);
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", "Bearer " + GetToken());

        String graphqlQuery = String.format(
                "{\"query\":\"{\\n pageEmployee(\\n page: {currentPage: 1, pageSize: 500}\\n search: {roleIds: [\\\"%s\\\"]}\\n ) {\\n list {\\n phone\\n email\\n nickName\\n name\\n id\\n account\\n organization {     orgName      id      orgCode  description  }  roleObjects {      createTime      editable      id      isDefault     name      enabled      remark    } }\\n }\\n}\"}",
                roleId
        );
        try {
            post.setEntity(new StringEntity(graphqlQuery));
        }catch (Exception exception ){
            exception.printStackTrace();
        }
        JsonNode userList = null;
        try (CloseableHttpResponse response = client.execute(post)) {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(response.getEntity().getContent());
             userList = root.path("data").path("pageEmployee").path("list");
        }catch (Exception e){
            e.printStackTrace();
        }
        return userList;
    }
}
