package com.car.voc.common.enums;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionEnum.java
 * @Description TODO
 * @createTime 2022年10月11日 13:46
 * @Copyright voc
 */
public enum EmotionEnum {


    positive("positive", "正面"),
    negative("negative", "负面"),
    neutral("neutral", "中性");
        // 成员变量
        private String nameE;
        private String name;
        // 构造方法 ,赋值给成员变量
        private EmotionEnum(String nameE, String name) {
            this.name = name;
            this.nameE = nameE;
        }

    public String getNameE() {
        return nameE;
    }

    public void setNameE(String nameE) {
        this.nameE = nameE;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
