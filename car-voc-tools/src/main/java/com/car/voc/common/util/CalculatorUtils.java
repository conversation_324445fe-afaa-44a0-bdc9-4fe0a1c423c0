package com.car.voc.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.car.voc.common.constant.Constants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * @version 1.0.0
 * @ClassName CalculatorUtils.java
 * @Description TODO
 * @createTime 2022年10月12日 16:57
 * @Copyright voc
 */
public class CalculatorUtils {
    /**
     * @Description 环比率计算
     * @createTime 2021年07月06日
     * @Copyright voc
     */
    public static BigDecimal ringRatio(BigDecimal current, BigDecimal previous) {
        final BigDecimal cur = Optional.ofNullable(current).orElse(BigDecimal.ZERO);
        final BigDecimal pre = Optional.ofNullable(previous).orElse(BigDecimal.ZERO);

        if (cur.compareTo(pre) == 0) { // //上周期0 ，本周期 0 或 上周期1 ，本周期 1  = 0.00
            return BigDecimal.valueOf(0);
        } else if (pre.compareTo(BigDecimal.ZERO) == 0 && cur.compareTo(BigDecimal.ZERO) != 0) { //上周期0 ，本周期 1  = 无穷大
            return BigDecimal.valueOf(Constants.INFINITY_VALUE);
        } else {
            return NumberUtil.round(NumberUtil.mul(NumberUtil.div(NumberUtil.sub(current, previous), previous), 100), 2, RoundingMode.HALF_UP);
        }
    }








    public static void main(String[] asrg) {
        int i = 1;
        System.out.println(i++ + ": " + ringRatio(null, null));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(0), BigDecimal.valueOf(0)));
        System.out.println(i++ + ": " + ringRatio(null, BigDecimal.valueOf(0)));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(0), null));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(1), BigDecimal.valueOf(1)));
        System.out.println(i++ + ": " + ringRatio(null, BigDecimal.valueOf(1)));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(1), null));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(0), BigDecimal.valueOf(1)));
        System.out.println(i++ + ": " + ringRatio(null, BigDecimal.valueOf(1)));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(0), null));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(1), BigDecimal.valueOf(0)));
        System.out.println(i++ + ": " + ringRatio(null, BigDecimal.valueOf(0)));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(10), BigDecimal.valueOf(5)));
        System.out.println(i++ + ": " + ringRatio(BigDecimal.valueOf(5), BigDecimal.valueOf(10)));
    }

    public static String getAllTagCode(String labelCode) {
        if (labelCode.contains("Q100")) {
            return getAllTagCodeQ(labelCode);
        } else if (labelCode.contains("B1")) {
            String level = labelCode.substring(2);
            int a = level.length() / 3;
            List<String> set = new ArrayList<>();
            for (int i = 0; i < a; i++) {
                String s = "B1" + level.substring(0, (i + 1) * 3);
                set.add(s);
            }
            return String.join("#", set);
        } else {
            return labelCode;
        }

    }

    public static String getAllTagCodeQ(String labelCode) {
        String level = labelCode.substring(2);
        int a = level.length() / 3;
        List<String> set = new ArrayList<>();
        for (int i = 0; i < a; i++) {
            String s = "Q1" + level.substring(0, (i + 1) * 3);
            set.add(s);
        }
        return String.join("#", set);
    }



    /**
     * @Description 占比计算
     * @createTime 2021年07月06日
     * @Copyright voc
     */

    public static BigDecimal proportion(BigDecimal current, BigDecimal total) {

        if (current != null && total != null && !(total.compareTo(new BigDecimal(0)) == 0)) {
            if (current.compareTo(new BigDecimal(0)) == 0) {
                return BigDecimal.ZERO;
            }
            BigDecimal pro = NumberUtil.div(current, total);
            if (pro.compareTo(new BigDecimal(0.0001)) == -1 && pro.compareTo(new BigDecimal(0)) == 1) {
                return NumberUtil.round(new BigDecimal(0.01), 2, RoundingMode.DOWN);
            }
            return NumberUtil.round(NumberUtil.mul(pro, 100), 2, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
    }


    public static <T> void proportion(List<T> list,
                                      Function<T, BigDecimal> valueExtractor,
                                      BiConsumer<T, BigDecimal> setValueProportion) {
        proportion(list, valueExtractor, setValueProportion, null);
    }

    /**
     * 计算占比，并用最大余额法把总和调为 1.0000（100%）
     *
     * @param list               待计算集合
     * @param valueExtractor     读取占比基准值
     * @param setValueProportion 写入占比字段
     * @param <T>                元素类型
     */
    public static <T> void proportion(List<T> list,
                                      Function<T, BigDecimal> valueExtractor,
                                      BiConsumer<T, BigDecimal> setValueProportion,
                                      BigDecimal total) {
        if (list == null || list.isEmpty()) {
            return;
        }
        /* ---------- 1. 计算总值 ---------- */
        if (total == null) {
            total = list.stream()
                    .map(valueExtractor)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            list.forEach(e -> setValueProportion.accept(e, BigDecimal.ZERO));
            return;
        }

        /* 2. 四舍五入到 4 位小数并收集余数 */
        class Item {
            final T obj;
            final BigDecimal raw;      // 原始比例（高精度）
            BigDecimal rounded;        // 四舍五入后

            Item(T obj, BigDecimal raw, BigDecimal rounded) {
                this.obj = obj;
                this.raw = raw;
                this.rounded = rounded;
            }
        }

        List<Item> items = new ArrayList<>(list.size());
        BigDecimal sumRounded = BigDecimal.ZERO;

        for (T t : list) {
            BigDecimal raw = valueExtractor.apply(t)
                    .divide(total, 10, RoundingMode.HALF_UP);
            BigDecimal rounded = raw.setScale(4, RoundingMode.HALF_UP);
            items.add(new Item(t, raw, rounded));
            sumRounded = sumRounded.add(rounded);
        }

        /* 3. 最大余额法补差 */
        BigDecimal ONE = BigDecimal.ONE.setScale(4, RoundingMode.UNNECESSARY);
        // 需要补的差值
        BigDecimal delta = ONE.subtract(sumRounded);
        // 单位：0.0001
        int diff = delta.movePointRight(4).intValue();

        if (diff != 0) {
            // 按余数从大到小排序
            Collections.sort(items, new Comparator<Item>() {
                @Override
                public int compare(Item o1, Item o2) {
                    BigDecimal r1 = o1.raw.subtract(o1.rounded);
                    BigDecimal r2 = o2.raw.subtract(o2.rounded);
                    return r2.compareTo(r1); // 降序
                }
            });

            BigDecimal step = BigDecimal.valueOf(0.0001);
            for (int i = 0; i < Math.abs(diff); i++) {
                Item it = items.get(i);
                it.rounded = it.rounded.add(diff > 0 ? step : step.negate());
            }
        }

        /* 4. 写回对象 */
        for (Item it : items) {
            BigDecimal round = NumberUtil.round(NumberUtil.mul(it.rounded, 100), 2, RoundingMode.HALF_UP);
            setValueProportion.accept(it.obj, round);
        }
    }

    public static BigDecimal avgePerDayNum(BigDecimal total, BigDecimal day) {
        if (total != null && day != null) {
            BigDecimal pro = NumberUtil.div(total, day);
            return NumberUtil.round(pro, 2, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
    }

    public static BigDecimal getPositiveRate(BigDecimal positive, BigDecimal negative) {
        if (positive != null && negative != null && !(NumberUtil.add(positive, negative).equals(BigDecimal.ZERO))) {
            return NumberUtil.round(NumberUtil.mul(NumberUtil.div(positive, NumberUtil.add(positive, negative)), 100), 2, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }

    public static BigDecimal getNegativeRate(BigDecimal negative, BigDecimal positive) {
        if (positive != null && negative != null && !(NumberUtil.add(positive, negative).equals(BigDecimal.ZERO))) {
            return NumberUtil.round(NumberUtil.mul(NumberUtil.div(negative, NumberUtil.add(positive, negative)), 100), 2, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }

    //        //净情感值（(正面提及量-负面提及量)/(正面提及量+负面提及量)*100%）；
    public static BigDecimal getEmotionalNetWorth(BigDecimal positive, BigDecimal negative) {
        if (positive == null) {
            positive = new BigDecimal(0);
        }
        if (negative == null) {
            negative = new BigDecimal(0);
        }
        if (positive != null && negative != null && !(NumberUtil.add(positive, negative)).equals(BigDecimal.ZERO)) {
            return NumberUtil.round(NumberUtil.mul(NumberUtil.div(NumberUtil.sub(positive, negative), NumberUtil.add(positive, negative)), 100), 2, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }

    public static String getPeriod(String statisticType) {
        switch (statisticType) {
            case "d":
                return "日";
            case "w":
                return "周";
            case "m":
                return "月";
            case "q":
                return "季";
            default:
                return "年";
        }
    }

    public static Integer periodStrToNum(String statisticType) {
        switch (statisticType) {
            case "d":
                return -1;
            case "w":
                return 0;
            case "m":
                return 1;
            case "q":
                return 2;
            default:
                return 3;
        }
    }

    public static String periodNumToStr(Integer dateUnit) {
        switch (dateUnit) {
            case -1:
                return "d";
            case 0:
                return "w";
            case 1:
                return "m";
            case 2:
                return "q";
            default:
                return "y";
        }
    }

    public static Integer downloadNumByDateUnit(Integer dateUnit) {
        switch (dateUnit) {
            case -1:
                return 16;
            case 0:
                return 20;
            case 1:
                return 12;
            case 2:
                return 5;
            default:
                return 3;
        }
    }

    public static String getRiskEndDate(Integer dateUnit, Date publishDate) {
        String en = " 23:59:59";
        if (dateUnit != null) {
            switch (dateUnit) {
                case -1:

                    return DateUtil.formatDate(publishDate) + en;
                case 0:
                    return DateUtil.endOfWeek(publishDate).toDateStr() + en;
                case 1:
                    return DateUtil.endOfMonth(publishDate).toDateStr() + en;
                case 2:
                    return DateUtil.endOfQuarter(publishDate).toDateStr() + en;
                default:
                    return DateUtil.endOfYear(publishDate).toDateStr() + en;
            }
        } else {
            return null;
        }


    }

    public static String getIsOne(String isOneId) {
        if (isOneId == null) {
            return "否";
        } else if ("1".equals(isOneId)) {
            return "是";
        } else {
            return "否";
        }
    }


}
