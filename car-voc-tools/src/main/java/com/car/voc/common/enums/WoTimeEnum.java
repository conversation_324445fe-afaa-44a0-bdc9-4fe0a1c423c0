package com.car.voc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WoTimeEnum {
    /*
    时间筛选：显示全部时间筛选工单
    24小时内：显示24小时内派发的工单(含24小时)
    48小时内：显示48小时内派发的工单(含48小时)
    72小时内：显示72小时内派发的工单(含72小时)
    96小时内：显示96小时内派发的工单(含96小时)
    120小时内：显示120小时内派发的工单(含120小时)
    大于120小时：显示大于120小时派发的工单(不含120小时)
     */
    LAST_24_HOURS("LAST_24_HOURS"),
    LAST_48_HOURS("LAST_48_HOURS"),
    LAST_72_HOURS("LAST_72_HOURS"),
    LAST_96_HOURS("LAST_96_HOURS"),
    LAST_120_HOURS("LAST_120_HOURS"),
    OVER_120_HOURS("OVER_120_HOURS");

    private final String value;
}
