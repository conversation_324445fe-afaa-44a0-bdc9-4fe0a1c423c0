/*
 *
 *  *  Copyright (c) 2019-2020, 冷冷 (<EMAIL>).
 *  *  <p>
 *  *  Licensed under the GNU Lesser General Public License 3.0 (the "License");
 *  *  you may not use this file except in compliance with the License.
 *  *  You may obtain a copy of the License at
 *  *  <p>
 *  * https://www.gnu.org/licenses/lgpl.html
 *  *  <p>
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  * See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */

package com.car.voc.common.constant;

/**
 *
 * @date 2019年05月18日
 * 服务名称
 */
public interface ServiceNameConstants {

    /**
     * 系统管理 admin
     */
    String SYSTEM_SERVICE = "voc-system";

    /**
     * gateway通过header传递根路径 basePath
     */
    String X_GATEWAY_BASE_PATH = "X_GATEWAY_BASE_PATH";

}
