package com.car.voc.common.util;

import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.car.voc.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 *
 * @Desc JWT工具类
 **/
@Slf4j
public class JwtUtil {

    public static final long EXPIRE_TIME = 6*24;


    /**
     * 校验token是否正确
     *
     * @param token  密钥
     * @param secret 用户的密码
     * @return 是否正确
     */
    public static boolean verify(String token, String username, String secret) {
        try {
            // 根据密码生成JWT效验器
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim("username", username).build();
            // 效验TOKEN
            DecodedJWT jwt = verifier.verify(token);

            return true;
        } catch (Exception exception) {
            return false;
        }
    }



    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的用户名
     */
    public static String getUsername(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            String username = jwt.getClaim("username").asString();
//            return jwt.getClaim("username").asString();
            try {
                username = Sm4Util.decryptEcb(CommonConstant.aes_key, username);
            } catch (Exception e) {
                log.error("解密失败");
            }
            return username;
        } catch (JWTDecodeException e) {
            return null;
        }
    }

    public static Boolean checkToken(String token){
     try {
         Algorithm algorithm = Algorithm.HMAC256(CommonConstant.tokenKey);
         JWTVerifier verifier = JWT.require(algorithm).build();
         DecodedJWT verify = verifier.verify(token);
         String username = verify.getClaim("username").asString();
         if(StrUtil.isBlank(username)){
             throw new RuntimeException();
         }
         return true;
     }catch (Exception e){
         return false;
     }
    }

    /**
     * 生成签名
     *
     * @param username 用户名
     * @param secret   用户的密码
     * @return 加密的token
     */
    public static String sign(String username, String secret) {
        LocalDateTime localDateTime = LocalDateTime.now().plusHours(EXPIRE_TIME);
        Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
//        Algorithm algorithm = Algorithm.HMAC256(secret);
        Algorithm algorithm = Algorithm.HMAC256(CommonConstant.tokenKey);
        String claim = "";
        try {
            claim = Sm4Util.encryptEcb(CommonConstant.aes_key, username);
        } catch (Exception e) {
            log.error("加密失败");
            claim = username;
        }
        // 附带username信息
//        return JWT.create().withClaim("username", username).withExpiresAt(date).sign(algorithm);
        return JWT.create().withClaim("username", claim).withExpiresAt(date).sign(algorithm);

    }


    public static void main(String[] args) throws Exception {
        LocalDateTime localDateTime = LocalDateTime.now().plusHours(6 * 24);
        System.out.println(localDateTime);
        Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        System.out.println(date);
        LocalDateTime localDateTime1 = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        System.out.println(localDateTime1);
//        String userName = "gwm-admin";
//        String s = Sm4Util.encryptEcb(CommonConstant.aes_key,userName);
//        System.out.println(s);
//        Algorithm algorithm = Algorithm.HMAC256(CommonConstant.tokenKey);
//        Date date = new Date(System.currentTimeMillis() + 60 * 1000);
//        String sign = JWT.create().withClaim("username", userName).withExpiresAt(date).sign(algorithm);
//        System.out.println(sign);
//        JWTVerifier verifier = JWT.require(algorithm).build();
//        verifier.verify(sign);
//        DecodedJWT verify = verifier.verify("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MDQ5NjIxMDEsInVzZXJuYW1lIjoiZ3dtLWFkbWluIn0.PskfisgFzOJuEsFAYk6V4aEyfP84Q_8ZN8bWFL-fwS0");
//        DecodedJWT jwt = JWT.decode("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MDQ5NTk4NDQsInVzZXJuYW1lIjoiSUdmelV3KzR4cG1xRGRYeGlGUk1mUT09In0.Q7aBuqSRQkQ5x3MjL5MnWVx4PJNTcgHM2xyw-IgH-6U");
//        System.out.println(jwt);
//        String username = jwt.getClaim("username").asString();
//        System.out.println(username);
//        String s1 = Sm4Util.decryptEcb(CommonConstant.aes_key, username);
//        System.out.println(s1);
    }



    /**
     * 从session中获取变量
     *
     * @param key
     * @return
     */
    public static String getSessionData(String key) {
        //${myVar}%
        //得到${} 后面的值
        String moshi = "";
        if (key.indexOf("}") != -1) {
            moshi = key.substring(key.indexOf("}") + 1);
        }
        String returnValue = null;
        if (key.contains("#{")) {
            key = key.substring(2, key.indexOf("}"));
        }
        if (StrUtil.isNotEmpty(key)) {
            HttpSession session = SpringContextUtils.getHttpServletRequest().getSession();
            returnValue = (String) session.getAttribute(key);
        }
        //结果加上${} 后面的值
        if (returnValue != null) {
            returnValue = returnValue + moshi;
        }
        return returnValue;
    }


}
