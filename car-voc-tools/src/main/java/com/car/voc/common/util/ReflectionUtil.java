package com.car.voc.common.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 反射工具类
 * 专门处理继承层次结构中的字段和注解获取
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class ReflectionUtil {
    
    /**
     * 获取类的所有字段，包括父类字段
     * 递归获取继承层次结构中的所有字段，避免重复字段
     * 
     * @param clazz 目标类
     * @return 所有字段列表
     */
    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        // 递归获取当前类及所有父类的字段
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            for (Field field : declaredFields) {
                // 避免重复添加同名字段（子类覆盖父类字段的情况）
                if (!containsFieldWithName(fields, field.getName())) {
                    fields.add(field);
                    log.trace("添加字段: {}.{}", currentClass.getSimpleName(), field.getName());
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        
        log.debug("类 {} 及其父类共获取到 {} 个字段", clazz.getSimpleName(), fields.size());
        return fields;
    }
    
    /**
     * 获取带有指定注解的所有字段
     * 
     * @param clazz 目标类
     * @param annotationClass 注解类型
     * @return 带有指定注解的字段列表
     */
    public static List<Field> getFieldsWithAnnotation(Class<?> clazz, Class<? extends Annotation> annotationClass) {
        List<Field> allFields = getAllFields(clazz);
        return allFields.stream()
                .filter(field -> field.isAnnotationPresent(annotationClass))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取带有多个注解中任意一个的字段
     * 
     * @param clazz 目标类
     * @param annotationClasses 注解类型数组
     * @return 带有指定注解的字段列表
     */
    @SafeVarargs
    public static List<Field> getFieldsWithAnyAnnotation(Class<?> clazz, Class<? extends Annotation>... annotationClasses) {
        List<Field> allFields = getAllFields(clazz);
        return allFields.stream()
                .filter(field -> Arrays.stream(annotationClasses)
                        .anyMatch(field::isAnnotationPresent))
                .collect(Collectors.toList());
    }
    
    /**
     * 检查字段列表中是否已包含指定名称的字段
     * 
     * @param fields 字段列表
     * @param fieldName 字段名称
     * @return 是否包含
     */
    private static boolean containsFieldWithName(List<Field> fields, String fieldName) {
        return fields.stream().anyMatch(field -> field.getName().equals(fieldName));
    }
    
    /**
     * 获取字段的值
     * 
     * @param obj 对象实例
     * @param field 字段
     * @return 字段值
     * @throws IllegalAccessException 访问异常
     */
    public static Object getFieldValue(Object obj, Field field) throws IllegalAccessException {
        field.setAccessible(true);
        return field.get(obj);
    }
    
    /**
     * 设置字段的值
     * 
     * @param obj 对象实例
     * @param field 字段
     * @param value 新值
     * @throws IllegalAccessException 访问异常
     */
    public static void setFieldValue(Object obj, Field field, Object value) throws IllegalAccessException {
        field.setAccessible(true);
        field.set(obj, value);
    }
    
    /**
     * 判断是否为基本类型或常用包装类型
     * 
     * @param clazz 类型
     * @return 是否为基本类型
     */
    public static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() || 
               clazz == Boolean.class || clazz == Character.class ||
               clazz == Byte.class || clazz == Short.class ||
               clazz == Integer.class || clazz == Long.class ||
               clazz == Float.class || clazz == Double.class ||
               clazz == String.class || 
               clazz == java.util.Date.class || 
               clazz == java.time.LocalDate.class || 
               clazz == java.time.LocalDateTime.class ||
               clazz == java.time.LocalTime.class ||
               clazz == java.math.BigDecimal.class ||
               clazz == java.math.BigInteger.class ||
               clazz.isEnum();
    }
    
    /**
     * 判断是否为集合类型
     * 
     * @param clazz 类型
     * @return 是否为集合类型
     */
    public static boolean isCollectionType(Class<?> clazz) {
        return java.util.Collection.class.isAssignableFrom(clazz) ||
               java.util.Map.class.isAssignableFrom(clazz) ||
               clazz.isArray();
    }
    
    /**
     * 判断是否为项目内的业务类
     * 
     * @param clazz 类型
     * @return 是否为业务类
     */
    public static boolean isBusinessClass(Class<?> clazz) {
        String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
        return packageName.startsWith("com.car.") && 
               !isPrimitiveOrWrapper(clazz) && 
               !isCollectionType(clazz);
    }
    
    /**
     * 获取类的继承层次结构
     * 
     * @param clazz 目标类
     * @return 继承层次结构列表（从子类到父类）
     */
    public static List<Class<?>> getClassHierarchy(Class<?> clazz) {
        List<Class<?>> hierarchy = new ArrayList<>();
        Class<?> currentClass = clazz;
        
        while (currentClass != null && currentClass != Object.class) {
            hierarchy.add(currentClass);
            currentClass = currentClass.getSuperclass();
        }
        
        return hierarchy;
    }
    
    /**
     * 打印类的字段信息（用于调试）
     * 
     * @param clazz 目标类
     */
    public static void printFieldInfo(Class<?> clazz) {
        if (!log.isDebugEnabled()) {
            return;
        }
        
        log.debug("=== 类 {} 的字段信息 ===", clazz.getSimpleName());
        List<Class<?>> hierarchy = getClassHierarchy(clazz);
        
        for (Class<?> currentClass : hierarchy) {
            log.debug("类: {}", currentClass.getSimpleName());
            Field[] fields = currentClass.getDeclaredFields();
            for (Field field : fields) {
                log.debug("  字段: {} (类型: {})", field.getName(), field.getType().getSimpleName());
                Annotation[] annotations = field.getAnnotations();
                for (Annotation annotation : annotations) {
                    log.debug("    注解: {}", annotation.annotationType().getSimpleName());
                }
            }
        }
        log.debug("=== 字段信息结束 ===");
    }
}
