package com.car.voc.common.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Repeatable;
import java.net.MalformedURLException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *
 * @version 1.0.0
 * @ClassName DingtalkMessageUtils.java
 * @Description TODO
 * @createTime 2023年05月12日 13:55
 * @Copyright voc
 */
@Data
@Log4j2
public class SendMessageUtils {

    //开放平台key
    private static  String  appKey ;
    //开放平台秘钥
    private static String appSecret ;
    //调用地址(发送工作通知)
    private static  String workNoticesUrl ;


    //短信账号
    private static  String  loginName ;
    private static  String  spCode ;
    //短信账号密码
    private static String password ;
    //短信Url
    private static String msgUrl ;


    public static void setAppKey(String appKey) {
        SendMessageUtils.appKey = appKey;
    }

    public static void setAppSecret(String appSecret) {
        SendMessageUtils.appSecret = appSecret;
    }

    public static void setWorkNoticesUrl(String workNoticesUrl) {
        SendMessageUtils.workNoticesUrl = workNoticesUrl;
    }

    public static void setLoginName(String loginName) {
        SendMessageUtils.loginName = loginName;
    }
    public static void setSpCode(String spCode) {
        SendMessageUtils.spCode =spCode;
    }

    public static void setPassword(String password) {
        SendMessageUtils.password = password;
    }

    public static void setMsgUrl(String msgUrl) {
        SendMessageUtils.msgUrl = msgUrl;
    }

    public static void sendDingtalk(String userName, String title, String table, String last){

       // 利用开放平台工具包获取签名
       Map<String, String> headMaps = null;
       try {
           headMaps = HmacSignUtil.createSignHeader(appKey, appSecret, workNoticesUrl, "POST");
       } catch (NoSuchAlgorithmException e) {
           e.printStackTrace();
       } catch (InvalidKeyException e) {
           e.printStackTrace();
       } catch (MalformedURLException e) {
           e.printStackTrace();
       }
       //创建okhttp客户端
       OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(5L, TimeUnit.SECONDS)
               .readTimeout(3L, TimeUnit.SECONDS).build();
       //设置请求头,http传输方式为json
       MediaType mediaType = MediaType.parse("application/json");
       JSONObject mass=new JSONObject();

//       mass.putOpt("userList", ArrayUtil.addAll(new String[]{userName}));
        mass.putOpt("userList", ArrayUtil.addAll(new String[]{"1100011000","1100011001","1100011002"}));
       mass.putOpt("messagetype",0);
       mass.putOpt("msgtype",6);
       mass.putOpt("title","VOC 风险警示－1个风险点");

       String title1="\n" +
               "标题：VOC洞察风险预警提醒  \n" +
               "内容：**OAT运营科-赵四**：您好！  \n" +
               "\n" +
               "     VOC管理平台风险预警有新的派发任务，此任务须尽快处理，以触达客户的响应；请及时进行处理。 \n";

       String table1="  \n" +
               "| **风险名称**  | **车机黑屏**                                       |\n" +
               "|-----------|------------------------------------------------|\n" +
               "| **分析周期** &emsp;&emsp;&emsp; | 2022\\-9\\-1至2022\\-9\\-30                         |\n" +
               "| **负面提及量**  &emsp;&emsp;&emsp;       | 23,539条，高于月均56\\.23%，环比上月飙升225\\.12%             |\n" +
               "| **发声用户** &emsp;&emsp;&emsp;       | 13,539位，高于月均67\\.23%，环比上月飙升205\\.12%             |\n" +
               "| **涉及车型** &emsp;&emsp;&emsp;       | 摩卡DHT\\(589\\)、拿铁DHT\\(487\\)、蓝山 \\(156\\)等N个车系      |\n" +
               "| **观点热词**  &emsp;&emsp;&emsp;     | 安全隐患\\(234\\)、提及媒体\\(211\\)、重大危险\\(178\\)、退换车\\(123\\) |\n" +
               "";


       mass.putOpt("text",title+table+last);





       RequestBody body = RequestBody.create(mediaType, mass.toString());
       //添加请求头参数
       Request request = new Request.Builder()
               //请求地址
               .url(workNoticesUrl)
               //请求方法
               .method("POST", body)
               //添加开放平台应用key
               .addHeader("X-HMAC-ACCESS-KEY", headMaps.get("X-HMAC-ACCESS-KEY"))
               //添加开放签名算法
               .addHeader("X-HMAC-ALGORITHM", headMaps.get("X-HMAC-ALGORITHM"))
               //添加开放请求时间
               .addHeader("Date", headMaps.get("Date"))
               //添加开放签名
               .addHeader("X-HMAC-SIGNATURE", headMaps.get("X-HMAC-SIGNATURE"))
               .addHeader("Content-Type", "application/json")
               .build();
       //执行请求
       Response response = null;
       try {
           response = client.newCall(request).execute();
       } catch (Exception e) {
           e.printStackTrace();
       }
       log.debug("钉钉信息：response===" + response);
       String returnBody = null;
       try {
           returnBody = response.body().string();
       } catch (IOException e) {
           e.printStackTrace();
       }
       log.debug("returnBody===" + returnBody);



   }



    private static void extracted() {
        //开放平台key
        String appKey = "T9S9773056B5";
        //开放平台秘钥
        String appSecret = "7d8b36451e2d46b8ba2e53507e3691db";
        //调用地址(发送工作通知)
        String url = "https://gwapi.gwm.cn/sandbox/msg/dingding/workNotices";
        // 利用开放平台工具包获取签名
        Map<String, String> headMaps = null;
        try {
            headMaps = HmacSignUtil.createSignHeader(appKey, appSecret, url, "POST");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        //创建okhttp客户端
        OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(5L, TimeUnit.SECONDS)
                .readTimeout(3L, TimeUnit.SECONDS).build();
        //设置请求头,http传输方式为json
        MediaType mediaType = MediaType.parse("application/json");
        //创建请求体body
        // 发送普通文本工作通知   messagetype默认0 msgtype消息类型 0文本消息  content 消息内容 userList消息接收人,修改成实际接收人
//        RequestBody body = RequestBody.create(mediaType, "{\r\n\"messagetype\":0,\r\n\"msgtype\":\"0\",\r\n\"content\": \"VOC 风险警示－1 个风险点。\",\r\n\"userList\":[\"GWxxx\"]\r\n}");
//        RequestBody body = RequestBody.create(mediaType, "{\r\n\"messagetype\":0,\r\n\"msgtype\":\"0\",\r\n\"content\": \"VOC【测试】 风险警示－1 个风险点。\",\r\n\"userList\":[\"1100011000\"]\r\n}");
        JSONObject mass=new JSONObject();

        mass.putOpt("userList", ArrayUtil.addAll(new String[]{"1100011000","1100011001","1100011002"}));
        mass.putOpt("messagetype",0);
        mass.putOpt("msgtype",6);
        mass.putOpt("title","VOC 风险警示－1个风险点");

        String title="\n" +
                "标题：VOC洞察风险预警提醒  \n" +
                "内容：**OAT运营科-赵四**：您好！  \n" +
                "\n" +
                "     VOC管理平台风险预警有新的派发任务，此任务须尽快处理，以触达客户的响应；请及时进行处理。 \n";
        String last="  \n[登陆VOC管理平台查阅详情>>>](http://10.250.11.23/#/riskControl/followUp) " +
                "；\n" +
                "此预警任务由**WEY用户运营部-张三**确认派发，如有问题请尽快联系VOC运营组协助处置！\n";
        String table="  \n" +
                "| **风险名称**  | **车机黑屏**                                       |\n" +
                "|-----------|------------------------------------------------|\n" +
                "| **分析周期** &emsp;&emsp;&emsp; | 2022\\-9\\-1至2022\\-9\\-30                         |\n" +
                "| **负面提及量**  &emsp;&emsp;&emsp;       | 23,539条，高于月均56\\.23%，环比上月飙升225\\.12%             |\n" +
                "| **发声用户** &emsp;&emsp;&emsp;       | 13,539位，高于月均67\\.23%，环比上月飙升205\\.12%             |\n" +
                "| **涉及车型** &emsp;&emsp;&emsp;       | 摩卡DHT\\(589\\)、拿铁DHT\\(487\\)、蓝山 \\(156\\)等N个车系      |\n" +
                "| **观点热词**  &emsp;&emsp;&emsp;     | 安全隐患\\(234\\)、提及媒体\\(211\\)、重大危险\\(178\\)、退换车\\(123\\) |\n" +
                "";


//        mass.putOpt("text",title+table+last);
        mass.putOpt("text","\n" +
                "标题：VOC洞察风险预警提醒(测试)  \n" +
                "内容：**品牌数字化团队-admin**：您好！  \n" +
                "     VOC管理平台风险预警有新的派发任务，此任务须尽快处理，以触达客户的响应；请及时进行处理。 \n" +
                "\n" +
                "-----------------------------------------------------------\n" +
                "风险名称:&emsp; **产品体验-指示灯含义**\n" +
                "\n" +
                "-----------------------------------------------------------\n" +
                "分析周期:&emsp; **2023-05-12** \n" +
                "\n" +
                "-----------------------------------------------------------\n" +
                "负面提及量:&emsp; **205条,高于日均11.45%,环比上日飙升6733.33%** \n" +
                "\n" +
                "-----------------------------------------------------------\n" +
                "发声用户:&emsp; **185条,高于日均19.64%** \n" +
                "\n" +
                "-----------------------------------------------------------\n" +
                "涉及车型:&emsp; **VV6(67)、VV5(60)、VV7(46)、拿铁 DHT-PHEV(12)、拿铁 DHT(8)、P8(4)、玛奇朵(4)、蓝山(3)、摩卡(1)等9个车系** \n" +
                "\n" +
                "-----------------------------------------------------------\n" +
                "观点热词:&emsp; **APP不显示车况信息(66)、APP登录异常(27)、APP车辆信息不显示(4)、APP登录(3)、后台数据出现异常(3)、APP异常(2)、APP车控功能消失(2)、手机APP不显示电量(1)、app功能异常(1)、App车辆不见了(1)** \n" +
                "\n" +
                "-----------------------------------------------------------\n" +

                "  \n" +
                "请登录用户声音VOC管理平台查阅;\n" +
                "此预警任务由**品牌数字化团队-admin**确认派发，如有问题请尽快联系VOC运营组协助处置！\n");


        RequestBody body = RequestBody.create(mediaType, mass.toString());
        //添加请求头参数
        Request request = new Request.Builder()
                //请求地址
                .url(url)
                //请求方法
                .method("POST", body)
                //添加开放平台应用key
                .addHeader("X-HMAC-ACCESS-KEY", headMaps.get("X-HMAC-ACCESS-KEY"))
                //添加开放签名算法
                .addHeader("X-HMAC-ALGORITHM", headMaps.get("X-HMAC-ALGORITHM"))
                //添加开放请求时间
                .addHeader("Date", headMaps.get("Date"))
                //添加开放签名
                .addHeader("X-HMAC-SIGNATURE", headMaps.get("X-HMAC-SIGNATURE"))
                .addHeader("Content-Type", "application/json")
                .build();
        //执行请求
        Response response = null;
        try {
            response = client.newCall(request).execute();
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.debug("response===" + response);
        String returnBody = null;
        try {
            returnBody = response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.debug("returnBody===" + returnBody);
    }

    public static void main(String[] args) {
//        extracted();
        sendMessage("15314208524","你有一项编号为1234的事务需要处理。");

    }
    public static void sendMessage(String phoneNumber, String content) {
        // 短信接口参数
        String spCode = "200487";
        String loginName = "df_zlqc";
        String password = "938f4564fafb3883df87693756699ad4";
        String url = "https://api.ums86.com:9600/sms/Api/Send.do";

        try {
            // 将内容转换为GBK编码
            String gbkContent = new String(content.getBytes("GBK"), "GBK");

            // 构建请求参数
//            String requestBody = String.format("SpCode=%s&LoginName=%s&Password=%s&MessageContent=%s&UserNumber=%s&templateId=1",
            String requestBody = String.format("SpCode=%s&LoginName=%s&Password=%s&MessageContent=%s&UserNumber=%s",
                    spCode, loginName, password, gbkContent, phoneNumber);

            // 创建OkHttp客户端
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(5, TimeUnit.SECONDS)
                    .readTimeout(3, TimeUnit.SECONDS)
                    .build();

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(
                            MediaType.parse("application/x-www-form-urlencoded;charset=GBK"),
                            requestBody))
                    .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=GBK")
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("发送短信失败: " + response);
                    return;
                }

                String responseBody = new String(response.body().bytes(), "GBK");
                log.debug("短信发送响应: {}", responseBody);
            }

        } catch (UnsupportedEncodingException e) {
            log.error("编码转换失败", e);
        } catch (IOException e) {
            log.error("发送短信请求失败", e);
        } catch (Exception e) {
            log.error("发送短信异常", e);
        }
    }

}
