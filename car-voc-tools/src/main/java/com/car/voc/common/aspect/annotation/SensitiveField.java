package com.car.voc.common.aspect.annotation;

import com.car.voc.common.enums.SensitiveFieldTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @创建者: fanrong
 * @创建时间: 2024/1/3 15:54
 * @描述:
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface SensitiveField {
    SensitiveFieldTypeEnum type();
}
