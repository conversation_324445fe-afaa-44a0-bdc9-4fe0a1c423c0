package com.car.voc.common.util;

import cn.hutool.core.util.RandomUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RandomTimeGenerator.java
 * @Description TODO
 * @createTime 2023年11月08日 15:34
 * @Copyright voc
 */
public class RandomTimeGenerator {


    public static void main(String[] args) {


        for (int i = 0; i < 69; i++) {
            System.out.println(RandomUtil.randomNumbers(19));
        }

        /*for (int i = 0; i < 500; i++) {
            String startTime = "2023-11-08 14:00:00";
            String endTime = "2023-11-08 18:00:00";

            try {
                Date randomTime = generateRandomTime(startTime, endTime);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                System.out.println(sdf.format(randomTime));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }*/
     /*   String startTime = "2023-11-08 09:00:00";
        String endTime = "2023-11-08 18:00:00";

        try {
            Date randomTime = generateRandomTime(startTime, endTime);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println(sdf.format(randomTime));
        } catch (ParseException e) {
            e.printStackTrace();
        }*/
    }

    public static Date generateRandomTime(String startTime, String endTime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startDate = sdf.parse(startTime);
        Date endDate = sdf.parse(endTime);

        long startMillis = startDate.getTime();
        long endMillis = endDate.getTime();
        long randomMillis = ThreadLocalRandom.current().nextLong(startMillis, endMillis + 1);

        Date randomTime = new Date(randomMillis);

        // 设置小时范围限制
        int hour = randomTime.getHours();
        if ((hour < 9 || hour >= 11) && (hour < 14 || hour >= 18)) {
            randomTime = generateRandomTime(startTime, endTime);
        }

        return randomTime;
    }
}
