package com.car.voc.common.enums;

/**
 *
 * @version 1.0.0
 * @ClassName RiskStateEnum.java
 * @Description TODO
 * @createTime 2022年10月11日 13:46
 * @Copyright voc
 */
public enum RiskProcessStatusEnum {

//    处理状态0：未处理，1：已处理，2：处理中,3:已撤回
    RiskState_0(0, "未处理"),
    RiskState_1(1, "已处理"),
    RiskState_2(2, "处理中"),
    RiskState_3(3, "已撤回");

    private int value;
    private String text;

    private RiskProcessStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    /**
     * value的获取.
     * @return int
     */
    public int getValue() {
        return value;
    }

    /**
     * text的获取.
     * @return String
     */
    public String getText() {
        return text;
    }

    public static RiskProcessStatusEnum getByStatus(Integer value){
        if(value != null){
            for(RiskProcessStatusEnum o : values()){
                if(o.getValue() == value.intValue()){
                    return o;
                }
            }
        }
        return null;
    }
}
