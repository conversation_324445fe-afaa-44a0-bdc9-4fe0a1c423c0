package com.car.voc.common.util;
import org.apache.commons.lang3.StringUtils;
import java.util.regex.Pattern;

/**
 * SQL注入防护工具类
 */
public class SqlInjectionUtils {

    // SQL注入风险字符
    private static final String SQL_INJECTION_CHARS = "'\"\\;#&|";

    // SQL注入风险关键字
    private static final String[] SQL_INJECTION_KEYWORDS = {
            "and", "or", "delete", "drop", "truncate", "insert", "select",
            "update", "declare", "alter", "cast", "exec", "--", "union",
            "xp_cmdshell", "table", "from", "where", "database", "having",
            "like", "sleep", "delay"
    };

    // 简单的SQL注入检测正则
    private static final Pattern SQL_PATTERN = Pattern.compile(
            "('.+--)|(--)|(\\|)|(%7C)|(%27)|(%22)|(%3D)|(=)|(/\\*(?:.|[\\n\\r])*?\\*/)|"
                    + "(\\b(select|update|union|and|or|delete|insert|truncate|char|into|substr|ascii|declare|exec|count|master|into|drop|execute)\\b)",
            Pattern.CASE_INSENSITIVE);

    /**
     * 检查输入是否存在SQL注入风险
     *
     * @param value 被检查的字符串
     * @return true=安全; false=可能存在注入风险
     */
    public static boolean checkSqlInjection(String value) {
        if (StringUtils.isBlank(value)) {
            return true;
        }

        // 转换为小写
        String lowerValue = value.toLowerCase();

        // 检查是否包含注入关键字
        for (String keyword : SQL_INJECTION_KEYWORDS) {
            if (lowerValue.contains(keyword)) {
                return false;
            }
        }

        // 检查是否包含特殊字符
        for (char c : SQL_INJECTION_CHARS.toCharArray()) {
            if (value.contains(String.valueOf(c))) {
                return false;
            }
        }

        // 使用正则进行匹配
        return !SQL_PATTERN.matcher(value).find();
    }

    /**
     * 清理SQL注入风险字符
     *
     * @param value 需要清理的字符串
     * @return 清理后的字符串
     */
    public static String cleanSqlInject(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 转义特殊字符
        value = value.replaceAll("'", "''");

        // 转换为小写进行关键字检查
        String lowerValue = value.toLowerCase();

        // 替换注入关键字
        for (String keyword : SQL_INJECTION_KEYWORDS) {
            if (lowerValue.contains(keyword)) {
                value = value.replaceAll("(?i)" + keyword, "");
            }
        }

        // 清理特殊字符
        for (char c : SQL_INJECTION_CHARS.toCharArray()) {
            value = value.replace(String.valueOf(c), "");
        }

        return value;
    }

    /**
     * 对SQL语句进行转义
     *
     * @param value 需要转义的值
     * @return 转义后的值
     */
    public static String escapeSql(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 替换SQL转义字符
        return value.replaceAll("['\"\\\\]", "\\\\$0");
    }

    /**
     * 安全的like查询值处理
     *
     * @param value 需要进行like查询的值
     * @return 处理后的值
     */
    public static String safeLike(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 清理特殊字符
        String safe = cleanSqlInject(value);

        // 转义like特殊字符
        return safe.replaceAll("[%_\\\\]", "\\\\$0");
    }
}