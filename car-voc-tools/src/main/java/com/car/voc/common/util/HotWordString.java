package com.car.voc.common.util;

import java.util.regex.Pattern;

/**
 * 
 * @version 1.0.0
 * @ClassName HotWordString.java
 * @Description TODO
 * @createTime 2023年05月26日 16:39
 * @Copyright voc
 */
public class HotWordString {

    /**
     * 检查字符串中是否含有特殊字符
     * @param str 字符串
     * @return true：含有特殊字符；false：不含有特殊字符
     */
    public static boolean hasSpecialCharacter(String str) {
        if(str == null || str.length() == 0) {
            return false;
        }
//        String regex ="[^\\u4E00-\\u9FA5a-zA-Z0-9#、《》，。：；*？￥……(\\s\\p{Punct}]";
        String regex ="[^\\u4E00-\\u9FA5a-zA-Z0-9#、《》，。：；￥*？……(\\s\\p{Punct}+]";
//        String regex ="\\p{Punct}+";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(str).find();
    }

    public static void main(String[] args) {
        System.out.println(hasSpecial<PERSON>haracter("*不~含有特殊字符 4S店￥()!`？&……~`》《、，：；'"));
    }


    //        String regex = "[^a-zA-Z0-9\u4e00-\u9fa5]";   // 匹配除数字、字母、汉字外的特殊字符
//        String regex = "[^\\u4e00-\\u9fa5a-zA-Z0-9#*\\s\\p{Punct}]";// 匹配除中文、数字、字母、标点符号、#、*、空格外的特殊字符
/*
    *//**
     * 检查字符串中是否含有非汉字字符
     * @param str 字符串
     * @return true：含有非汉字字符；false：不含有非汉字字符
     *//*
    public static boolean hasNonChineseCharacter(String str) {
        if(str == null || str.length() == 0) {
            return false;
        }
        for(int i = 0; i < str.length(); i++) {
            if(!isChineseCharacter(str.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    *//**
     * 判断字符是否为汉字
     * @param c 字符
     * @return true：是汉字；false：不是汉字
     *//*
    public static boolean isChineseCharacter(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }*/



}
