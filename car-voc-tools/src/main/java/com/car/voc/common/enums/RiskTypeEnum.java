package com.car.voc.common.enums;

/**
 *
 * @version 1.0.0
 * @ClassName IntentionEnum.java
 * @Description TODO
 * @createTime 2022年10月11日 13:46
 * @Copyright voc
 */
public enum RiskTypeEnum {
    vocRiskEvent("1", "风险事件洞察"),
    VocRiskQuality("2", "质量问题风险"),
    VocRiskUser("3", "高频投诉用户"),
    VocRiskRescue("4", "救援故障预警"),
    VocRiskBranches("5", "网点风险预警");
        // 成员变量
        private String type;
        private String name;
        // 构造方法 ,赋值给成员变量
        private RiskTypeEnum(String type, String name) {
            this.type = type;
            this.name = name;
        }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
