package com.car.voc.common.oss;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.model.SetBucketCORSRequest;
import com.car.voc.common.util.SendMessageUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.common.auth.*;

import java.util.ArrayList;

@Configuration
public class OssBootConfiguration {

    @Value("${configuration.oss.endpoint}")
    private String endpoint;
    @Value("${configuration.oss.accessKey}")
    private String accessKeyId;
    @Value("${configuration.oss.secretKey}")
    private String accessKeySecret;
    @Value("${configuration.oss.bucketName}")
    private String bucketName;
    @Value("${configuration.oss.staticDomain}")
    private String staticDomain;


    /**
     * @Description 钉钉信息推送
     * @Copyright voc
     */

    @Value("${configuration.dingTalk.appKey}")
    private String appKey;
    @Value("${configuration.dingTalk.appSecret}")
    private String appSecret;
    @Value("${configuration.dingTalk.workNoticesUrl}")
    private String workNoticesUrl;



    //短信账号
    @Value("${configuration.msg.loginName}")
    private   String  loginName ;
    //短信账号
    @Value("${configuration.msg.spCode}")
    private   String  spCode ;
    //短信账号密码
    @Value("${configuration.msg.password}")
    private  String password ;
    //短信Url
    @Value("${configuration.msg.msgUrl}")
    private  String msgUrl ;



    @Bean
    public void initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);

        /**
         * @Description 钉钉信息推送
         * @Copyright voc
         */
        SendMessageUtils.setAppKey(appKey);
        SendMessageUtils.setAppSecret(appSecret);
        SendMessageUtils.setWorkNoticesUrl(workNoticesUrl);



        /**
         * @Description 短信发送
         * @Copyright voc
         */

        SendMessageUtils.setLoginName(loginName);
        SendMessageUtils.setSpCode(spCode);
        SendMessageUtils.setPassword(password);
        SendMessageUtils.setMsgUrl(msgUrl);
    }



        public static void main(String[] args) throws Exception {
            // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
            String endpoint = "https://oss-cn-beijing.aliyuncs.com";
            // 强烈建议不要把访问凭证保存到工程代码里，否则可能导致访问凭证泄露，威胁您账号下所有资源的安全。本代码示例以从环境变量中获取访问凭证为例。运行本代码示例之前，请先配置环境变量。
            CredentialsProvider credentialsProvider1 =
                    CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider("beijing","","","acs:ram::1668227353285930:role/ramosstest");
//                    CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                    .accessKeyId("")
                    .accessKeySecret("")
                    .build());
            EnvironmentVariableCredentialsProvider credentialsProvider = CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();
            DefaultCredentialProvider provider1 = new DefaultCredentialProvider("","");


            // 填写Bucket名称，例如examplebucket。
            String bucketName = "gwm-voc";

            // 创建OSSClient实例。
            OSS ossClient = new OSSClientBuilder().build(endpoint, provider1);

            try {
                SetBucketCORSRequest request = new SetBucketCORSRequest(bucketName);

                // 跨域资源共享规则的容器，每个存储空间最多允许10条规则。
                ArrayList<SetBucketCORSRequest.CORSRule> putCorsRules = new ArrayList<SetBucketCORSRequest.CORSRule>();

                SetBucketCORSRequest.CORSRule corRule = new SetBucketCORSRequest.CORSRule();

                ArrayList<String> allowedOrigin = new ArrayList<String>();
                // 指定允许跨域请求的来源。
                allowedOrigin.add( "http://localhost:8080,http://************:9998");

                ArrayList<String> allowedMethod = new ArrayList<String>();
                // 指定允许的跨域请求方法(GET/PUT/DELETE/POST/HEAD)。
                allowedMethod.add("GET");

                ArrayList<String> allowedHeader = new ArrayList<String>();
                // 是否允许预取指令（OPTIONS）中Access-Control-Request-Headers头中指定的Header。
                allowedHeader.add("*");

                ArrayList<String> exposedHeader = new ArrayList<String>();
                // 指定允许用户从应用程序中访问的响应头。
                // AllowedOrigins和AllowedMethods最多支持一个星号（*）通配符。星号（*）表示允许所有的域来源或者操作。
                corRule.setAllowedMethods(allowedMethod);
                corRule.setAllowedOrigins(allowedOrigin);
                // AllowedHeaders和ExposeHeaders不支持通配符。
                corRule.setAllowedHeaders(allowedHeader);
                corRule.setExposeHeaders(exposedHeader);
                // 指定浏览器对特定资源的预取（OPTIONS）请求返回结果的缓存时间，单位为秒。
                corRule.setMaxAgeSeconds(10);

                // 最多允许10条规则。
                putCorsRules.add(corRule);
                // 已存在的规则将被覆盖。
                request.setCorsRules(putCorsRules);
                // 指定是否返回Vary: Origin头。指定为TRUE，表示不管发送的是否为跨域请求或跨域请求是否成功，均会返回Vary: Origin头。指定为False，表示任何情况下都不会返回Vary: Origin头。
                // request.setResponseVary(Boolean.TRUE);
                ossClient.setBucketCORS(request);
            } catch (OSSException oe) {
                System.out.println("Caught an OSSException, which means your request made it to OSS, "
                        + "but was rejected with an error response for some reason.");
                System.out.println("Error Message:" + oe.getErrorMessage());
                System.out.println("Error Code:" + oe.getErrorCode());
                System.out.println("Request ID:" + oe.getRequestId());
                System.out.println("Host ID:" + oe.getHostId());
            } catch (ClientException ce) {
                System.out.println("Caught an ClientException, which means the client encountered "
                        + "a serious internal problem while trying to communicate with OSS, "
                        + "such as not being able to access the network.");
                System.out.println("Error Message:" + ce.getMessage());
            } finally {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
        }



}
