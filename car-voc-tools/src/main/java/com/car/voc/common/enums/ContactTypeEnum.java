package com.car.voc.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * cc渠道来源枚举
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ContactTypeEnum {


    CHAT("CHAT", "在线"),
    CHAT_ARCHIVE("CHAT_ARCHIVE", "聊天归档"),

    CTI("CTI", "电话"),
    CTI_IN("CTI_IN", "电话咨询(来电)"),
    CTI_OUT("CTI_OUT", "电话咨询(去电)"),

    WORK_ORDER("WORK_ORDER", "工单"),

    EMAIL("EMAIL", "邮件"),
    FOLLOW("FOLLOW", "跟进计划"),


    SMART_EXHALATION("SMART_EXHALATION", "智能外呼"),
    SMS_SINGLE("SMS_SINGLE", "单条短信"),
    SMS_BATCH("SMS_BATCH", "群发短信"),

    MA_SMS("MA_SMS", "MA短信"),
    MA_CTI("MA_CTI", "MA外呼"),
    MA_IN("MA_IN", "MA进线"),

    OUT_API("OUT_API", "外部接口"),
    FAX("FAX", "传真"),
    ;

    private final String code;
    private final String desc;


    /**
     * 根据code获取枚举
     */
    public static ContactTypeEnum getByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据code获取描述
     */
    public static String getDescByCode(String code) {
        ContactTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : "";
    }

    /**
     * 判断是否为有效的联系类型
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有code
     */
    public static List<String> getAllCodes() {
        return Arrays.stream(values())
                .map(ContactTypeEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有描述
     */
    public static List<String> getAllDescs() {
        return Arrays.stream(values())
                .map(ContactTypeEnum::getDesc)
                .collect(Collectors.toList());
    }

    /**
     * 转换为Map
     */
    public static Map<String, String> toMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(
                        ContactTypeEnum::getCode,
                        ContactTypeEnum::getDesc,
                        (k1, k2) -> k1,
                        LinkedHashMap::new
                ));
    }
}
