package com.car.voc.common.constant;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * Create by joe on 2018/9/26
 */
public interface RegionLatitudeAndLongitude {

    // 替换原来的长字符串定义
    public static final String LONG_STRING = readLongStringFromFile();

    static String readLongStringFromFile() {
        try {
            // 使用类路径读取资源文件
            InputStream is = RegionLatitudeAndLongitude.class.getClassLoader().getResourceAsStream("long.string.txt");
            if (is == null) {
                throw new RuntimeException("读取文件失败: long.string.txt 未找到");
            }
            byte[] buffer = new byte[is.available()];
            int length = is.read(buffer);
            return new String(buffer, 0, length, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("读取文件失败: src/main/resources/long.string.txt", e);
        }
    }
}
