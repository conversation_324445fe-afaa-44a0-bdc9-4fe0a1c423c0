package com.car.voc.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.ServiceNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.Executor;

@Component
@Slf4j
public class SpringContextUtils implements ApplicationContextAware {

    public static TransmittableThreadLocal<HttpServletRequest> userThreadLocal = new TransmittableThreadLocal<>();;
    public static TransmittableThreadLocal<String> userIdThreadLocal = new TransmittableThreadLocal<>();
    public static TransmittableThreadLocal<Object> userInfoThreadLocal = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<String> requestURIHolder = new TransmittableThreadLocal<>();

    private static Executor executor;


    /**
     * 上下文对象实例
     */
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextUtils.applicationContext = applicationContext;
    }


    public static Executor getExecutor() {
        return executor;
    }

    public static void setExecutor(Executor executor) {
        SpringContextUtils.executor = executor;
    }



    public static void setRequestURI(String uri) {
        requestURIHolder.set(uri);
    }

    public static String getRequestURI() {
        return requestURIHolder.get();
    }

    /**
     * 获取applicationContext
     *
     * @return
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 获取HttpServletRequest
     */
    public static HttpServletRequest getHttpServletRequest() {
//        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return userThreadLocal.get();
    }

    public static String getUserId(){
        if(ObjectUtil.isNotNull(userIdThreadLocal.get())){
            return userIdThreadLocal.get();
        }
        return null;
    }

    public static void setUserId(String userId){
        userIdThreadLocal.set(userId);
    }

    public static Object getUser(){
        if (ObjectUtil.isNotNull(userInfoThreadLocal.get())){
            return userInfoThreadLocal.get();
        }
        return null;
    }

    public static void setUser(Object user){
        userInfoThreadLocal.set(user);
    }


    public static void setHttpServletRequest(ServletRequest servletRequest){
        userThreadLocal.set((HttpServletRequest) servletRequest);
    }

    public static String getToken(){
        if(ObjectUtil.isNotNull(userThreadLocal.get())){
            final String accessToken = userThreadLocal.get().getHeader(CommonConstant.X_ACCESS_TOKEN);
            log.debug("getToken {}", accessToken);
            return accessToken;
        }
        return null;
    }
    /**
     * 获取HttpServletResponse
     */
    public static HttpServletResponse getHttpServletResponse() {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        return response;
    }
    /**
     * 获取项目根路径 basePath
     */
    public static String getDomain() {
        HttpServletRequest request = getHttpServletRequest();
        StringBuffer url = request.getRequestURL();
        String basePath = request.getHeader(ServiceNameConstants.X_GATEWAY_BASE_PATH);
        if (StrUtil.isNotEmpty(basePath)) {
            return basePath;
        } else {
            return url.delete(url.length() - request.getRequestURI().length(), url.length()).toString();
        }
    }

    public static String getOrigin() {
        HttpServletRequest request = getHttpServletRequest();
        return request.getHeader("Origin");
    }

    /**
     * 通过name获取 Bean.
     *
     * @param name
     * @return
     */
    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    /**
     * 通过class获取Bean.
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    /**
     * 通过name,以及Clazz返回指定的Bean
     *
     * @param name
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        return getApplicationContext().getBean(name, clazz);
    }
}
