package com.car.voc.common.enums;

/**
 *
 * @version 1.0.0
 * @ClassName RiskStateEnum.java
 * @Description TODO
 * @createTime 2022年10月11日 13:46
 * @Copyright voc
 */
public enum RiskStateEnum {

//    状态(0:待审核,1:已审核,2:待处理,3:已处理,4:处理中,5:已取消,6已撤回，7已完成未全部完成)
    RiskState_0(0, "待审核"),
    RiskState_1(1, "已审核"),
    RiskState_2(2, "待处理"),
    RiskState_3(3, "已处理"),
    RiskState_4(4, "处理中"),
    RiskState_5(5, "已取消"),
    RiskState_6(6, "已撤回"),
    RiskState_7(7, "已完成未全部完成");

    private int value;
    private String text;

    private RiskStateEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    /**
     * value的获取.
     * @return int
     */
    public int getValue() {
        return value;
    }

    /**
     * text的获取.
     * @return String
     */
    public String getText() {
        return text;
    }

    public static RiskStateEnum getByStatus(Integer value){
        if(value != null){
            for(RiskStateEnum o : values()){
                if(o.getValue() == value.intValue()){
                    return o;
                }
            }
        }
        return null;
    }
}
