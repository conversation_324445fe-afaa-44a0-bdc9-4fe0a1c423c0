package com.car.voc.common.constant;

/**
 * @date: 2019-06-14
 * @description: 缓存常量
 */
public interface CacheConstant {

    public static final long expire=86400;

    /**
     * 字典信息缓存
     */
    public static final String SYS_DICT_CACHE = "sys:cache:dict";
    /**
     * 表字典信息缓存
     */
    public static final String SYS_DICT_TABLE_CACHE = "sys:cache:dictTable";
    public static final String SYS_DICT_TABLE_BY_KEYS_CACHE = SYS_DICT_TABLE_CACHE + "ByKeys";

    /**
     * 用户权限缓存
     */
    public static final String SYS_USER_CHANNEL = "sys:cache:userChannel_";
    public static final String SYS_USER_SERIES = "sys:cache:userSeries_";
    public static final String SYS_USER_TAG = "sys:cache:userTag_";
    public static final String SYS_USER_AREA = "sys:cache:userArea_";
    public static final String SYS_USER_PERMISSION = "sys:cache:userPermission_";
    public static final String SYS_USER_TAG_OTHER = "sys:cache:tagOther";
    public static final String SYS_REVIEWER_PROCESS = "sys:rwviewer:process_";
    public static final String SYS_HANDLE_PROCESS = "sys:handle:process_";
    /**
     * 新(用户权限缓存)
     */
    String SYS_USER_QUALITY_TAG = "sys:cache:quality:userTag_";
    String SYS_USER_BRANDCODE_CHANNEL = "sys:cache:brandCode:userChannel_%s_%s";
    String SYS_USER_BRANDCODE_SERIES = "sys:cache:brandCode:userSeries_%s_%s";
    String SYS_USER_BRANDCODE_SERIES_ALL = "sys:cache:brandCode:seriesAll_%s";
    String SYS_USER_BRANDCODE_TAG = "sys:cache:brandCode:userTag_%s_%s";
    String SYS_USER_BRANDCODE = "sys:cache:brandCode_%s";
    String SYS_USER_BRANDCODE_AREA = "sys:cache:brandCode:userArea_%s_%s";
    String SYS_USER_BRANDCODE_PROVINCE = "sys:cache:brandCode:useProvince_%s_%s";
    public static final String SYS_AREA_BRANDCODE_PROVINCE_ALL = "sys:cache:brandCode:area_province_all_%s:";
    public static final String SYS_AREA_PROVINCE_ALL = "sys:cache:area_province_all:";
    public static final String SYS_FAULT_PROBLEM_ALL = "sys:cache:tag:fault_problem_all:";
    public static final String SYS_BUSINESS_TAG_ALL = "sys:cache:tag:business_tag_all:";

    /**
     * 渠道分类
     */
    public static final String SYS_CHANNEL = "sys:cache:channel_id_";
    //区域省份
    public static final String SYS_AREA_PROVINCE = "sys:cache:area_province:";
    /**
     * 数据权限配置缓存
     */
    public static final String SYS_DATA_PERMISSIONS_CACHE = "sys:cache:permission:datarules";

    /**
     * 缓存用户信息
     */
    public static final String SYS_USERS_CACHE = "sys:cache:user";
    /**
     * 缓存用户信息
     */
    public static final String SYS_USERS_CACHE_INFO = "sys:cache:userInfo";

    /**
     * 全部部门信息缓存
     */
    public static final String SYS_DEPARTS_CACHE = "sys:cache:depart:alldata";


    /**
     * 全部部门ids缓存
     */
    public static final String SYS_DEPART_IDS_CACHE = "sys:cache:depart:allids";


    /**
     * 测试缓存key
     */
    public static final String TEST_DEMO_CACHE = "test:demo";

    /**
     * 字典信息缓存
     */
    public static final String SYS_DYNAMICDB_CACHE = "sys:cache:dbconnect:dynamic:";

    /**
     * gateway路由缓存
     */
    public static final String GATEWAY_ROUTES = "sys:cache:cloud:gateway_routes";


    /**
     * gateway路由 reload key
     */
    public static final String ROUTE_JVM_RELOAD_TOPIC = "gateway_jvm_route_reload_topic";

    /**
     * TODO 冗余代码 待删除
     * 插件商城排行榜
     */
    public static final String PLUGIN_MALL_RANKING = "pluginMall::rankingList";
    /**
     * TODO 冗余代码 待删除
     * 插件商城排行榜
     */
    public static final String PLUGIN_MALL_PAGE_LIST = "pluginMall::queryPageList";


    /**
     * online列表页配置信息缓存key
     */
    public static final String ONLINE_LIST = "sys:cache:online:list";

    /**
     * online表单页配置信息缓存key
     */
    public static final String ONLINE_FORM = "sys:cache:online:form";




    public static final String SYS_FAULT_PROBLEM_CACHE = "sys:cache:dictionary:fault_problem:code:";
    public static final String SYS_FAULT_PROBLEM2_CACHES = "sys:cache:dictionary:fault_problems:codes:";
    public static final String SYS_CAR_SCENE_CACHE = "sys:cache:dictionary:car_scene:code:";
    public static final String SYS_INDUSTRY_THESAURUS_CACHE = "sys:cache:dictionary:industry_thesaurus:code:";
    public static final String SYS_BUSINESS_TAG_CACHE = "sys:cache:dictionary:business_tag:code:";
    public static final String SYS_BUSINESS__ALL_TAG_CACHE = "sys:cache:dictionary:business_tag:codeAll:";

    public static String REDIS_SITE_CONTENT_TYPE_MAP="site_content_type:";
    public static String REDIS_SITEBIZ_TYPE_MAP="site_biz_type:";
    public static String channel_ManagerModel="Channel_ManagerModel:";
    public static String OTHER_TAGS = "sys:cache:otherTagCode:";
    String risk_warning_rules = "risk:warning:rules:";
    String SYS_CHANNEL_Id = "sys:cache:channel:";
    String internationalList = "sys:cache:international";
    String channelTop = "sys:cache:channel:top";
    String SYS_RISK_RULE = "sys:cache:risk:rule:";
    String sys_disable_tag_code = "sys:cache:disable:tag:code:";
    String sys_disable_quality_code = "sys:cache:disable:quality:code:";
    String sys_disable_car_code = "sys:cache:disable:car:code:";
    String SYS_CAR_CODE_NAME = "sys:car:code:name";
    String SYS_USER_API_PERMISSION = "sys:user:permission:";
    String SYS_CHANNEL_ALL = "sys:channel:all";
    String sys_province_area = "sys:province:area:";
    String sys_area_brand_province = "sys:area:brand:province:";
    String index_channelId_map = "sys:index:channel:map";
}

