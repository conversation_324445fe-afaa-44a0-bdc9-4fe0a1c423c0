package com.car.voc.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.car.voc.common.enums.DateStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *
 * @date ：Created in 2020/12/23
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@Data
public class SvwDate {
    String year;
    String weekYear;
    String month;
    String day;
    String week;
    String season;
    String startDate;
    String startDateStr;
    String endDate;
    String endDateStr;
    String halfYear;
    String time;
    String timeFilter;
    @ApiModelProperty(value = "日期单位(0为周，1为月，2为季，3为年度，-1为自定义)",example = "1")
    Integer dateUnit;
    String dateStr;

    public SvwDate(){

    }

    public SvwDate(Date startDate, Date endDate){

//        this.year = String.valueOf(DateUtils.getYear(startDate));
        this.year = String.valueOf(DateUtils.getYear(endDate));
        this.startDate = DateUtils.dateToStrLong(startDate);
        this.startDateStr = DateUtils.dateToChStr(startDate);
        if (this.dateUnit!=null&&this.dateUnit!=-1){//
            Date T_2=DateUtil.offsetDay(new Date(),-1);
            this.endDate = DateUtils.dateToStr(endDate.compareTo(T_2)>0?T_2:endDate)+" 23:59:59";
        }else {
            this.endDate= DateUtils.dateToStrLong(endDate);
        }
        Date T_2=DateUtil.offsetDay(new Date(),-1);
        this.endDate = DateUtils.dateToStr(endDate.compareTo(T_2)>0?T_2:endDate)+" 23:59:59";

        /*if (T_2.compareTo(startDate)<0){
            this.endDate= DateUtils.dateToStr(startDate)+" 23:59:59";
        }else {
            this.endDate = DateUtils.dateToStr(endDate.compareTo(T_2)>0?T_2:endDate)+" 23:59:59";
        }
*/
        this.endDateStr = DateUtils.dateToChStr(endDate);
        Integer m= DateUtils.getMonth(startDate)+1;
    }


    public SvwDate(Date startDate, Date endDate,int between){

//        this.year = String.valueOf(DateUtils.getYear(startDate));
        this.year = String.valueOf(DateUtils.getYear(endDate));
        this.startDate = DateUtils.dateToStrLong(startDate);
        this.startDateStr = DateUtils.dateToChStr(startDate);
        if (this.dateUnit!=null&&this.dateUnit!=-1){//
            Date T_2=DateUtil.offsetDay(new Date(),between);
            this.endDate = DateUtils.dateToStr(endDate.compareTo(T_2)>0?T_2:endDate)+" 23:59:59";
        }else {
            this.endDate= DateUtils.dateToStrLong(endDate);
        }
        Date T_2=DateUtil.offsetDay(new Date(),between);
        this.endDate = DateUtils.dateToStr(endDate.compareTo(T_2)>0?T_2:endDate)+" 23:59:59";

        /*if (T_2.compareTo(startDate)<0){
            this.endDate= DateUtils.dateToStr(startDate)+" 23:59:59";
        }else {
            this.endDate = DateUtils.dateToStr(endDate.compareTo(T_2)>0?T_2:endDate)+" 23:59:59";
        }
*/
        this.endDateStr = DateUtils.dateToChStr(endDate);
        Integer m= DateUtils.getMonth(startDate)+1;
    }





    public String getTime() {
        String s="";
        if (StrUtil.isNotBlank(month)){
            s=month+"";
        }else if (StrUtil.isNotBlank(week)){
            s=week+"";
        }else if (StrUtil.isNotBlank(season)){
            s=season+"";
        }else if (!StrUtil.isBlankIfStr(dateUnit)&&this.dateUnit.equals(-1)){
            String datet=DateUtils.StringToString(this.getEndDate(), DateStyle.YYYY_MM_DD_EN);
        return datet;
        }

        return year+"-"+s;
    }
    public String getTimeM() {
        String s="";
        if (StrUtil.isNotBlank(month)){
            if(month.length() == 1){
                s="0"+month+"";
            }else {
                s=month+"";
            }
        }else if (StrUtil.isNotBlank(week)){
            s=week+"";
        }else if (StrUtil.isNotBlank(season)){
            s=season+"";
        }else if (!StrUtil.isBlankIfStr(dateUnit)&&this.dateUnit.equals(-1)){
            String datet=DateUtils.StringToString(this.getEndDate(), DateStyle.YYYY_MM_DD)+" 00:00:00";
            return datet;
        }
        if(StrUtil.isBlank(s)){
            return year;
        }

        return year+"-"+s;
    }


    public String getTimeFilter() {
        String s="";
        if (StrUtil.isNotBlank(month)){
            s=month+"月";
        }else if (StrUtil.isNotBlank(week)){
            s=week+"周";
        }else if (StrUtil.isNotBlank(season)){
            s=season+"季";
        }else if (!StrUtil.isBlankIfStr(dateUnit)&&this.dateUnit.equals(-1)){
            String datet=DateUtils.StringToString(this.getEndDate(),DateStyle.YYYY_MM_DD_EN);
        return datet;
        }
        if(StrUtil.isBlank(s)){
            return year;
        }

        return year+"年"+s;
    }
    public String getDateCycle() {
        if (this.dateUnit==0){
            return year+"-"+(Integer.parseInt(week) <=9?"0"+week:week);
        }else if (this.dateUnit==1){
            return year+"-"+(Integer.parseInt(month) <=9?"0"+month:month);
        }else if (this.dateUnit==2){
            return year+"-"+season;
        }else if (this.dateUnit==3){
            return year;
        }else if (this.dateUnit==-1){
            String datet=DateUtils.StringToString(this.getEndDate(),DateStyle.YYYY_MM_DD);
            return datet;
        }else {
            return DateUtils.StringToString(this.getEndDate(),DateStyle.YYYY_MM_DD);
        }
    }
}
