package com.car.voc.common.enums;

/**
 *
 * @version 1.0.0
 * @ClassName SoundType.java
 * @Description TODO
 * @createTime 2022年10月14日 13:48
 * @Copyright voc
 */
public enum SoundTypeEnum {
    emotion("emotion", "情感"),
    intention("intention", "意图");
    private String nameStr;
    private String name;
    SoundTypeEnum(String name, String nameStr){
        this.name=name;
        this.nameStr=nameStr;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
