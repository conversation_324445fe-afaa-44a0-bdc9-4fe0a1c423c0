package com.car.voc.common.oss;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/4
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
public class OssStorageInfo {
    public static void main(String[] args) throws Exception {
       /* String accessKeyId = StringUtils.trim(System.getenv("OSS_ACCESS_KEY_ID"));
        String secretAccessKey = StringUtils.trim(System.getenv("OSS_ACCESS_KEY_SECRET"));
        String sessionToken = StringUtils.trim(System.getenv("OSS_SESSION_TOKEN"));*/
        System.setProperty("OSS_ACCESS_KEY_ID", "");
        System.setProperty("OSS_ACCESS_KEY_SECRET", "");
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "https://oss-cn-beijing.aliyuncs.com";
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        EnvironmentVariableCredentialsProvider credentialsProvider = CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();
        // 填写Bucket名称，例如examplebucket。
        String bucketName = "gwm-voc";
        // 指定前缀，例如exampledir/object。如果您希望遍历主目录下的文件夹，则将此值置空。
        String keyPrefix = "exampledir/object";
        Credentials credentials = new DefaultCredentials("", "");
        credentialsProvider.setCredentials(credentials);
        DefaultCredentialProvider provider1 = new DefaultCredentialProvider("", "");
    }
/*

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, provider1);

        try {
            BucketStat stat = ossClient.getBucketStat(bucketName);
            // 获取Bucket的总存储量，单位为字节。
            System.out.println("获取Bucket的总存储量，单位为字节:"+stat.getStorageSize());
            // 获取Bucket中总的Object数量。
            System.out.println("获取Bucket中总的Object数量:"+stat.getObjectCount());
            // 获取Bucket中已经初始化但还未完成（Complete）或者还未中止（Abort）的Multipart Upload数量。
            System.out.println(stat.getMultipartUploadCount());
            // 获取标准存储类型Object的存储量，单位为字节。
            System.out.println("获取标准存储类型Object的存储量，单位为字节"+stat.getStandardStorage());
            // 获取标准存储类型的Object的数量。
            System.out.println(stat.getStandardObjectCount());
            // 获取Bucket中Live Channel的数量。
            System.out.println(stat.getLiveChannelCount());
            // 此次调用获取到的存储信息的时间点，格式为时间戳，单位为秒。
            System.out.println(stat.getLastModifiedTime());
            // 获取低频存储类型Object的计费存储量，单位为字节。
            System.out.println(stat.getInfrequentAccessStorage());
            // 获取低频存储类型Object的实际存储量，单位为字节。
            System.out.println(stat.getInfrequentAccessRealStorage());
            // 获取低频存储类型的Object数量。
            System.out.println(stat.getInfrequentAccessObjectCount());
            // 获取归档存储类型Object的计费存储量，单位为字节。
            System.out.println(stat.getArchiveStorage());
            // 获取归档存储类型Object的实际存储量，单位为字节。
            System.out.println(stat.getArchiveRealStorage());
            // 获取归档存储类型的Object数量。
            System.out.println(stat.getArchiveObjectCount());
            // 获取冷归档存储类型Object的计费存储量，单位为字节。
            System.out.println(stat.getColdArchiveStorage());
            // 获取冷归档存储类型Object的实际存储量，单位为字节。
            System.out.println(stat.getColdArchiveRealStorage());
            // 获取冷归档存储类型的Object数量。
            System.out.println(stat.getColdArchiveObjectCount());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }



    // 获取某个存储空间下指定目录（文件夹）下的文件大小。
    private static long calculateFolderLength(OSS ossClient, String bucketName, String folder) {
        long size = 0L;
        ObjectListing objectListing = null;
        do {
            // MaxKey默认值为100，最大值为1000。
            ListObjectsRequest request = new ListObjectsRequest(bucketName).withPrefix(folder).withMaxKeys(1000);
            if (objectListing != null) {
                request.setMarker(objectListing.getNextMarker());
            }
            objectListing = ossClient.listObjects(request);
            List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
            for (OSSObjectSummary s : sums) {
                size += s.getSize();
            }
        } while (objectListing.isTruncated());
        return size;
    }*/
}