package com.car.voc.stats.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.serivce.*;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.serivce.wo.BusinessOpportunityService;
import com.car.stats.serivce.wo.WoBaseOriginalDataService;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.WoBusinessVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * voc-商机线索
 * @version 1.0.0
 * @ClassName BusinessOpportuntiyController.java
 * @Description 商机线索
 * @createTime 2023年06月06日 10:21
 * @Copyright voc
 */
@Api(tags="voc-声音洞察-商机线索")
@RestController
@RequestMapping("/stats/businessOpportuntiy")
@Slf4j
public class BusinessOpportuntiyController {
    @Autowired
    BusinessOpportunityService businessOpportunityService;
    @Autowired
    IDwsVocIntentionDiService iDwsVocIntentionDiService;

    @Autowired
    EsDataSentenceVocService sentenceVocService;

    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;

    @Autowired
    DwdVocSentenceService dwdVocSentenceService;
    @Autowired
    WoBaseOriginalDataService woBaseOriginalDataService;
    @AutoLog(value = "业务声音洞察-商机线索")
    @ApiOperation(value="线索量")
    @PostMapping(value = "/leadVolume")
    public Result<?> leadVolume(@RequestBody FilterCriteriaModel model) {
        StatisticVo re=woBaseOriginalDataService.workOrderNumber(model);
        return Result.OK(re);
    }
    @ApiOperation(value="提及量、用户数")
    @PostMapping(value = "/statisticUsers")
    public Result<?> statisticUsers(@RequestBody FilterCriteriaModel model) {
        model.setNumCycles(2);
        model.setTagType(1);
        return dwdVocSentenceService.salesLeadstatisticUsers(model);
    }

    @ApiOperation(value="来源渠道")
    @PostMapping(value = "/sourceChannel")
    public Result<?> sourceChannel(@RequestBody FilterCriteriaModel model) {
        model.setTagType(1);
        return dwdVocSentenceService.sourceChannel(model);
    }

    @ApiOperation(value="线索来源分布")
    @PostMapping(value = "/clueDist")
    public Result<?> clueDist(@RequestBody FilterCriteriaModel model) {
        return businessOpportunityService.clueDist(model);
    }
    @ApiOperation(value="到店意愿占比")
    @PostMapping(value = "/visitIntent")
    public Result<?> visitIntent(@RequestBody FilterCriteriaModel model) {
        return businessOpportunityService.visitIntent(model);
    }
    @ApiOperation(value="购车需求趋势")
    @PostMapping(value = "/carPurchaseDemandTrend")
    public Result<?> carPurchaseDemandTrend(@RequestBody FilterCriteriaModel model) {
        return businessOpportunityService.carPurchaseDemandTrend(model);
    }

    @ApiOperation(value = "省份地图")
    @PostMapping(value = "/provinceMap")
    public Result<?> provinceMap(@RequestBody FilterCriteriaModel model) {
        return woBaseOriginalDataService.provinceMap(model);
    }


    @ApiOperation(value = "大区排行")
    @PostMapping(value = "/regionalTop")
    public Result<?> regionalTop(@RequestBody FilterCriteriaModel model) {
        return woBaseOriginalDataService.regionalTop(model);
    }
    @ApiOperation(value = "小区排行")
    @PostMapping(value = "/communityTop")
    public Result<?> communityTop(@RequestBody FilterCriteriaModel model) {
        return woBaseOriginalDataService.communityTop(model);
    }
    @ApiOperation(value = "意向网点排行")
    @PostMapping(value = "/intendedBranchTop")
    public Result<?> intendedBranchTop(@RequestBody FilterCriteriaModel model) {
        return businessOpportunityService.intendedBranchTop(model);
    }

    @ApiOperation(value = "意向车系排行")
    @PostMapping(value = "/intendedCarTop")
    public Result<?> intendedCarTop(@RequestBody FilterCriteriaModel model) {
        return businessOpportunityService.intendedCarTop(model);
    }

    @ApiOperation(value="购车意向分布")
    @PostMapping(value = "/carPurchaseDist")
    public Result<?> carPurchaseDist(@RequestBody FilterCriteriaModel model) {
        model.setTagType(1);
        model.setEmotion("正面");
        return dwdVocSentenceService.carPurchaseDist(model);
    }
    @ApiOperation(value="购车关注热点")
    @PostMapping(value = "/purchaseConcernsPriority")
    public Result<?> purchaseConcernsPriority(@RequestBody FilterCriteriaModel model) {
        model.setTagType(1);
        model.setEmotion("正面");
        return dwdVocSentenceService.purchaseConcernsPriority(model);
    }
    @ApiOperation(value="商机工单列表")
    @PostMapping(value = "/woOrderList")
    public Result<?> woOrderList(@RequestBody FilterCriteriaModel model) {
        Page<WoBusinessVo> result = businessOpportunityService.woOrderList(model);
        return Result.OK(result);
    }


}
