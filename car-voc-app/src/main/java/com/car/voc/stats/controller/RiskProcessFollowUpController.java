package com.car.voc.stats.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.vo.risk.RiskDataDryingVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.entity.*;
import com.car.voc.model.LoginUser;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.service.ISysDepartService;
import com.car.voc.service.ISysUserService;
import com.car.voc.service.IVocRiskAllTypesService;
import com.car.voc.service.IVocRiskHandlingRecordService;
import com.car.voc.task.VocRiskEventJob;
import com.car.voc.task.service.VocRiskJobService;
import com.car.voc.vo.FaultProblemTreeVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.risk.RiskAllTypesVo;
import com.car.voc.vo.risk.RiskBrandCodeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.http.HttpUtil.downloadFile;


/**
 * voc-风险处理跟进
 * @version 1.0.0
 * @ClassName RiskWarningRecordController.java
 * @Description 风险审核
 * @createTime 2023年02月07日 11:29
 * @Copyright voc
 */

@Api(tags = "voc-风险处理跟进")
@RestController
@RequestMapping("/stats/riskHandle")
@Slf4j
public class RiskProcessFollowUpController {
    @Value("${voc.mail.url}")
    private String mailServer;  //邮件服务器地址
    @Value("${voc.mail.link}")
    private String link;  //邮件服务器地址
    @Value("${voc.mail.username}")
    private String user;//登录用户名
    @Value("${voc.mail.password}")
    private String password;//登录密码
    @Value("${voc.mail.path}")
    private String path;//登录密码
    @Value("${voc.mail.storepassword}")
    private String storepassword;//登录密码
    @Value("${voc.mail.picturePath}")
    private String picturePath;//邮件图片保存地址
    @Autowired
    IVocRiskAllTypesService riskAllTypesService;

    @Autowired
    ISysUserService userService;
    @Autowired
    ISysDepartService departService;

    @Autowired
    private IVocRiskHandlingRecordService riskHandlingRecordService;

    @Resource
    private ISysDepartService sysDepartService;

    @Resource
    VocRiskJobService eventJobService;
    @Resource
    VocRiskJobService userJobService;

    @Autowired
    private ISysUserService sysUserService;


    @AutoLog(value = "任务处理派发")
    @ApiOperation(value = "任务处理派发")
    @PostMapping(value = "/taskProcessingList")
    public Result<?> riskList(@RequestBody RiskAllTypesModel model) {
        Page<RiskAllTypesVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        IPage<RiskAllTypesVo> voIPage = riskAllTypesService.riskList1(page, model);
        return Result.OK(voIPage);
    }

    @ApiOperation(value = "任务处理派发")
    @AutoLog(value = "任务处理派发")
    @PostMapping(value = "/riskList")
    public Result<?> taskProcessingList(@RequestBody RiskAllTypesModel model) {
        IPage<RiskAllTypesVo> voIPage = riskAllTypesService.taskProcessingList( model);
        return Result.OK(voIPage);
    }

    @AutoLog(value = "特别关注-问题处理跟进查询")
    @ApiOperation(value = "风险处理跟进列表(tagType区分审核或处理)")
    @PostMapping(value = "/list")
    public Result<?> auditList(@RequestBody RiskAllTypesModel model) {
        Page<RiskAllTypesVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        IPage<RiskAllTypesVo> voIPage = riskAllTypesService.auditList(page, model);
        return Result.OK(voIPage);
    }

    @ApiOperation(value = "根据ids获取风险记录详情")
    @GetMapping(value = "/riskDetails")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "风险记录ids多个以逗号分开", required = true, dataType = "String"),
    })
    public Result<List<RiskAllTypesVo>> riskRecordDetails(String ids) {
        return Result.OK(riskAllTypesService.riskRecordDetails(ids));
    }

    @ApiOperation("风险确认审核接口")
    @RequestMapping(value = "/confirmReview", method = RequestMethod.POST)
    public Result<?> confirmReview(@RequestBody RiskAllTypesModel model) {
        model.setMailServer(mailServer);
        model.setPath(path);
        model.setPicturePath(picturePath);
        model.setPassword(password);
        model.setStroepassword(storepassword);
        model.setUser(user);
        return riskAllTypesService.confirmReview(model);
    }

    @ApiOperation("风险确认处理接口")
    @RequestMapping(value = "/confirmProcessing", method = RequestMethod.POST)
    public Result<?> confirmProcessing(@RequestBody VocRiskHandlingRecord model) {
        return riskAllTypesService.confirmProcessing(model);
    }

    @ApiOperation("风险处理完成接口")
    @RequestMapping(value = "/confirmHandle", method = RequestMethod.POST)
    public Result<?> confirmHandle(@RequestBody VocRiskHandlingRecord model) {
        return riskAllTypesService.confirmHandle(model);
    }

    @ApiOperation("风险处理取消接口")
    @RequestMapping(value = "/confirmCancel", method = RequestMethod.POST)
    public Result<?> confirmCancel(@RequestBody VocRiskHandlingRecord model) {
        return riskAllTypesService.confirmCancel(model);
    }


    @ApiOperation("确认不构成风险接口")
    @RequestMapping(value = "/noRisk", method = RequestMethod.PUT)
    public Result<?> noRisk(String riskId) {
        UpdateWrapper<VocRiskAllTypes> update = new UpdateWrapper<>();

        update.lambda().eq(VocRiskAllTypes::getId, riskId)
                .set(VocRiskAllTypes::isIfRisk, 0)
                .set(VocRiskAllTypes::getAuditUserId, getUserInfo().getId())
                .set(VocRiskAllTypes::getCancelTime, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN))
                .set(VocRiskAllTypes::getAuditDepartId, departService.getDepartByUserId(getUserInfo().getId()).getId())
                .set(VocRiskAllTypes::getRiskState, 5);
        ;
        return Result.OK(riskAllTypesService.update(update));
    }

    LoginUser getUserInfo() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return sysUser;
    }

    @ApiOperation(value = "风险数据晾晒")
    @GetMapping(value = "/dataDrying")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<RiskDataDryingVo> dataDrying(String id) {
        return Result.OK(riskAllTypesService.dataDrying(id));
    }


    @ApiOperation(value = "风险数据晾晒-对比")
    @GetMapping(value = "/dataDryingContrast")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> dataDryingContrast(String id) {
        return Result.OK(riskAllTypesService.dataDryingContrast(id));
    }

    @ApiOperation(value = "风险数据晾晒-情感及意图变化")
    @GetMapping(value = "/changeEmotionIntention")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> changeEmotionIntention(String id) {
        return Result.OK(riskAllTypesService.changeEmotionIntention(id));
    }


    @ApiOperation(value = "风险数据晾晒-发声渠道变化")
    @GetMapping(value = "/changeChannel")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> changeChannel(String id) {
        return Result.OK(riskAllTypesService.changeChannel(id));
    }

    @ApiOperation(value = "风险数据晾晒-日趋势")
    @GetMapping(value = "/trendDate")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> trendDate(String id) {
        return Result.OK(riskAllTypesService.trendDate(id));
    }

    @ApiOperation(value = "风险数据晾晒-高频热词")
    @GetMapping(value = "/hotWords")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> hotWords(String id) {
        return Result.OK(riskAllTypesService.hotWords(id));
    }


    @ApiOperation(value = "邮件-文字内容")
    @GetMapping(value = "/riskMailTextContent")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> riskMailTextContent(String id) {
        return Result.OK(riskAllTypesService.riskMailTextContent(id));
    }

    @ApiOperation(value = "处理记录")
    @GetMapping(value = "/processingRecords")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "风险记录id", required = true, dataType = "String"),
    })
    public Result<?> processingRecords(String id) {

        return Result.OK(riskHandlingRecordService.selectProcessingRecords(id));
    }


    @ApiOperation("查询所有部门")
    @RequestMapping(value = "/alllist", method = RequestMethod.GET)
    public Result<List<SysDepart>> alllist(@RequestParam(value = "brandCode", required = false) String brandCode) {
        Result<List<SysDepart>> result = new Result<>();
        QueryWrapper<SysDepart> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysDepart::getDelFlag, 0);
        wrapper.lambda().eq(SysDepart::getStatus, 1);
        if (!StringUtils.isEmpty(brandCode)) {
            wrapper.lambda().like(SysDepart::getBrandCode, brandCode);
        }
        List<SysDepart> pageList = sysDepartService.list(wrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    @ApiOperation(value = "根据部门id查询用户信息")
    @RequestMapping(value = "/queryUserByDepId", method = RequestMethod.GET)
    public Result<List<SysUser>> queryUserByDepId(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "realname", required = false) String realname) {
        return sysUserService.queryUserByDepId(id, realname);

    }


    @ApiOperation("获取品牌")
    @RequestMapping(value = "/getRiskBrandCodes", method = RequestMethod.GET)
    public Result<List<RiskBrandCodeVo>> getRiskBrandCodes() {
        return Result.OK(riskAllTypesService.getRiskBrandCodes());
    }


    @ApiOperation(value = "获取业务标签", notes = "获取业务标签")
    @RequestMapping(value = "/riskAllTopics", method = RequestMethod.GET)
    public Result<List<VocBusinessTagVo>> riskAllTopics(@RequestParam(name = "brandCode") String brandCode) {
        return Result.OK(riskAllTypesService.riskAllTopics(brandCode));
    }


    @ApiOperation(value = "获取质量标签", notes = "获取质量标签")
    @RequestMapping(value = "/riskAllQuality", method = RequestMethod.GET)
    public Result<List<FaultProblemTreeVo>> riskAllQuality(@RequestParam(name = "brandCode") String brandCode) {
        return Result.OK(riskAllTypesService.riskAllQuality(brandCode));
    }


}
