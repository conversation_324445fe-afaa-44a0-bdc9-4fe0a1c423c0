package com.car.voc.stats.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocDealerRisk;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocDealerRiskService;
import com.car.stats.serivce.IDwdVocRiskService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.IDateUtils;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.mapper.SysUserRoleMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * voc-网点风险预警
 * @version 1.0.0
 * @ClassName RiskBranchesController.java
 * @Description 网点风险预警
 * @createTime 2022年11月23日 11:42
 * @Copyright voc
 */
@Api(tags="voc-网点风险预警")
@RestController
@RequestMapping("/stats/riskBranches")
@Slf4j
public class RiskBranchesController {
    @Autowired
    SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    IDwdVocDealerRiskService vocDealerRiskService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @AutoLog(value = "网点风险预警")
    @ApiOperation(value="风险点聚集")
    @PostMapping(value = "/riskPointAgg")
    public Result<?> riskPointAgg(@RequestBody RiskEventInsightModel model) {
        model.SetUpCycle();
        IPage<RiskPointAggVo> aggVoIPage;
        model.setOrderByType("num");
        aggVoIPage=vocDealerRiskService.riskPointAggNew(model);
        return Result.OK(aggVoIPage);
    }
    @ApiOperation(value="数据分析-简报")
    @PostMapping(value = "/dataAnalysis-briefing")
    public Result<?> dataAnalysisBriefing(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocDealerRisk risk=vocDealerRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        setChannels(model,risk);
        return vocDealerRiskService.dataAnalysisBriefing(model,risk);

    }


    @ApiOperation(value="数据分析-用户及意图走势")
    @PostMapping(value = "/dataAnalysis-userIntentionTrend")
    public Result<?> emotionIntentionTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocDealerRisk risk=vocDealerRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        setChannels(model,risk);
        return vocDealerRiskService.userIntentionTrend(model, risk);
    }
    @ApiOperation(value="数据分析-不满关注点")
    @PostMapping(value = "/dataAnalysis-hotWords")
    public Result<?> hotWords(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocDealerRisk risk=vocDealerRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        setChannels(model,risk);
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),risk.getPublishDate()));
        return vocDealerRiskService.hotWords(model);
    }

    @ApiOperation(value = "数据分析-网点问题分布")
    @PostMapping(value = "/dataAnalysis-problemDistribution")
    public Result<?> problemDistribution(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocDealerRisk risk=vocDealerRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        setChannels(model,risk);
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),risk.getPublishDate()));
        return vocDealerRiskService.problemDistribution(model);
    }


    private void setChannels(RiskEventInsightModel model,DwdVocDealerRisk risk) {
        model.setEmotion("负面");
        model.setIntention("投诉");
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setDlrName(risk.getDlrName());


    }



}
