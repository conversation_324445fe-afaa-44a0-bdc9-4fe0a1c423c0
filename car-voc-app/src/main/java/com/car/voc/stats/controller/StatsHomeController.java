package com.car.voc.stats.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelDetailFilterModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.*;
import com.car.stats.strksService.IDwdVocSentenceService;
import com.car.stats.vo.DateChannelVo;
import com.car.stats.vo.DateUserStatisticVo;
import com.car.stats.vo.EmotionProportionVo;
import com.car.stats.vo.HomeRiskWarningVo;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.enums.DataEnum;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.model.risk.RiskAllTypesModel;
import com.car.voc.service.IVocRiskAllTypesService;
import com.car.voc.service.IVocRiskHandlingRecordService;
import com.car.voc.vo.ProblemOverviewVo;
import com.car.voc.vo.risk.RiskAllTypesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.car.voc.service.CommonService.getUserId;

/**
 * voc-总览
 */
@Api(tags = "voc-总览")
@RestController
@RequestMapping("/stats/home")
@Slf4j
public class StatsHomeController {


    @Autowired
    IDwsVocIntentionDiService intentionDiService;
    @Autowired
    IDwsVocQualityEmotionDiService qualityEmotionDiService;
    @Autowired
    IDwsVocQualityUserDiService dwsVocUserDiService;
    @Autowired
    IDwsVocEmotionDiService vocEmotionDiService;
    @Autowired
    IDwsVocIntentionService iDwsVocIntentionService;
    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    IDwdVocQualityRiskService qualityRiskService;
    @Autowired
    IDwdVocUserRiskService vocUserRiskService;
    @Autowired
    IVocRiskAllTypesService riskAllTypesService;
    @Autowired
    IVocRiskHandlingRecordService recordService;
    @Autowired
    StatsCommonService commonService;

    @Autowired
    Executor defExecutor;
    @Autowired
    IDwsVocEmotionUserDiService dwsVocEmotionUserDiService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    IDwdVocSentenceService vocSentenceService;
    @Autowired
    DwdVocSentenceService dwdVocSentenceService;

    @AutoLog(value = "VOC总览")
    @ApiOperation(value = "首页简报数值")
    @PostMapping(value = "/briefingValue")
//    @Cacheable(cacheNames = "voc:api:home:briefingValue", keyGenerator = "myCacheKeyGenerator")
    public Result<?> homeBriefingValue(@RequestBody FilterCriteriaModel model) {
        return vocEmotionDiService.soundInsightBriefingValue(model);
    }

    @ApiOperation(value = "首页简报-净情感值")
    @PostMapping(value = "/emotionValue")
    @Cacheable(cacheNames = "voc:api:home:emotionValue", keyGenerator = "myCacheKeyGenerator")
    public Result<Map> emotionValue(@RequestBody FilterCriteriaModel model) {
        model.setDataType(DataEnum.numMention);
        return intentionDiService.emotionValue(model);
    }

    @ApiOperation(value = "总览提及量(情感)趋势")
    @PostMapping(value = "/emotionTrend")
    @Cacheable(cacheNames = "voc:api:home:emotionTrend", keyGenerator = "myCacheKeyGenerator")
    public Result<?> emotionTrend(@RequestBody FilterCriteriaModel model) {
        return iDwsVocIntentionService.emotionTrend(model);
    }

    @ApiOperation(value = "车系分布")
    @PostMapping(value = "/carSeriesDistribution")
    @Cacheable(cacheNames = "voc:api:home:carSeriesDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> carSeriesDistribution(@RequestBody FilterCriteriaModel model) {
        return iDwsVocIntentionService.carSeriesDistribution(model);
    }


    @ApiOperation(value = "首页意图占比")
    @PostMapping(value = "/overviewIntentionRatio")
    @Cacheable(cacheNames = "voc:api:home:overviewIntentionRatio", keyGenerator = "myCacheKeyGenerator")
    public Result<?> overviewIntentionRatio(@RequestBody FilterCriteriaModel model) {
        return iDwsVocIntentionService.overviewIntentionRatio(model);
    }

    @ApiOperation(value = "情感占比")
    @PostMapping(value = "/proportionEmotion")
    @Cacheable(cacheNames = "voc:api:home:proportionEmotion", keyGenerator = "myCacheKeyGenerator")
    public Result<EmotionProportionVo> homeProportionEmotion(@RequestBody FilterCriteriaModel model) {
        return vocEmotionDiService.focusProportionEmotion(model);
    }

    @ApiOperation(value = "首页意图用户数")
    @PostMapping(value = "/homeIntentionUserNum")
    @Cacheable(cacheNames = "voc:api:home:homeIntentionUserNum", keyGenerator = "myCacheKeyGenerator")
    public Result<?> homeIntentionUserNum(@RequestBody FilterCriteriaModel model) {
        model.SetUpCycle();
        if (StrUtil.isBlankIfStr(model.getIntention())) {
            return Result.error("意图(intention)不能为空!");
        } else {
            return intentionDiService.homeIntentionUserNum(model);
        }
    }

    @ApiOperation(value = "首页意图趋势")
    @PostMapping(value = "/homeIntentionTrend")
    @Cacheable(cacheNames = "voc:api:home:homeIntentionTrend", keyGenerator = "myCacheKeyGenerator")
    public Result<?> homeIntentionTrend(@RequestBody FilterCriteriaModel model) {
        if (StrUtil.isBlankIfStr(model.getIntention())) {
            return Result.error("意图(intention)不能为空!");
        } else {
            LabelDetailFilterModel model1 = BeanUtil.copyProperties(model, LabelDetailFilterModel.class);
            model1.setNumCycles(7);
            model1.setTagType(1);
            return Result.OK(dwsVocEmotionUserDiService.allTagsTrend(model1));
        }
    }

    @ApiOperation(value = "首页意图Top")
    @PostMapping(value = "/homeIntentionTop")
    @Cacheable(cacheNames = "voc:api:home:homeIntentionTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> homeIntentionTop(@RequestBody FilterCriteriaModel model) {
        model.SetUpCycle();
        if (StrUtil.isBlankIfStr(model.getIntention())) {
            return Result.error("意图(intention)不能为空!");
        } else {
            return intentionDiService.homeIntentionTop(model);
        }
    }


    @ApiOperation(value = "首页渠道趋势")
    @PostMapping(value = "/homeChannelTrends")
    @Cacheable(cacheNames = "voc:api:home:homeChannelTrends", keyGenerator = "myCacheKeyGenerator")
    public Result<List<DateChannelVo>> homeChannelTrends1(@RequestBody FilterCriteriaModel model) {
        model.SetUpCycle();
        return intentionDiService.homeChannelTrends1(model);
    }

    @ApiOperation(value = "首页质量问题top")
    @PostMapping(value = "/qualityProblemTop")
    @Cacheable(cacheNames = "voc:api:home:qualityProblemTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> qualityProblemTop(@RequestBody FilterCriteriaModel model) {
        model.SetUpCycle();
        if ((StrUtil.isNotBlank(model.getFirstDimensionCode()) && !"B1001".equals(model.getFirstDimensionCode())) || (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0 && !model.getSecondDimensionCodes().contains("B1002006"))) {
            Map<String, Object> re = new HashMap<>();
            re.put("mentionTops", new ArrayList<>());
            re.put("soarTops", new ArrayList<>());
            return Result.OK(re);
        }
        model.setFirstDimensionCode(null);
        model.setSecondDimensionCodes(null);
        return qualityEmotionDiService.qualityProblemTopHome(model);
    }

    @ApiOperation(value = "首页质量问题趋势")
    @PostMapping(value = "/qualityProblemTrend")
    @Cacheable(cacheNames = "voc:api:home:qualityProblemTrend", keyGenerator = "myCacheKeyGenerator")
    public Result<?> qualityProblemTrend(@RequestBody FilterCriteriaModel model) {
        model.SetUpCycle();
        if ((StrUtil.isNotBlank(model.getFirstDimensionCode()) && !"B1001".equals(model.getFirstDimensionCode())) || (model.getSecondDimensionCodes() != null && model.getSecondDimensionCodes().size() > 0 && !model.getSecondDimensionCodes().contains("B1002006"))) {
            List<DateUserStatisticVo> relist = new ArrayList<>();
            return Result.OK(relist);
        }

        model.setFirstDimensionCode(null);
        model.setSecondDimensionCodes(null);
        return dwsVocUserDiService.qualityProblemTrend(model);
    }

    boolean getQuality(String brandCode) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(qualityText) && wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @ApiOperation(value = "风险警示")
    @PostMapping(value = "/riskWarning")
    public Result<?> riskWarning(@RequestBody FilterCriteriaModel model) {
        if (StrUtil.isBlankIfStr(model.getStartDate()) && StrUtil.isBlankIfStr(model.getEndDate())) {
            commonService.getNoticeWarningDate(model);
        }
        model.SetUpCycle();
        HomeRiskWarningVo re = new HomeRiskWarningVo();
        //业务风险洞察
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
                model.getSecondDimensionCodes().retainAll(sounps);
            } else {
                model.setSecondDimensionCodes(new ArrayList<>(sounps));
            }
        }

        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);

        AtomicReference<IPage<RiskPointAggVo>> aggEventsVoIPage = new AtomicReference<>();
        AtomicReference<IPage<RiskPointAggVo>> aggEventsVoIPageUp = new AtomicReference<>();
        if (!(channelSet != null && channelSet.size() > 0)) {
            re.setRiskEvents(BigDecimal.valueOf(0));
        } else {
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                RiskEventInsightModel riskEventModel = new RiskEventInsightModel();
                BeanUtil.copyProperties(model, riskEventModel);
                riskEventModel.setPageSize(10);
                riskEventModel.setAccessToken(token);
                aggEventsVoIPage.set(vocRiskService.riskPointAggNew(riskEventModel));

                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                RiskEventInsightModel riskEventModelUp = new RiskEventInsightModel();
                BeanUtil.copyProperties(model, riskEventModelUp);
                riskEventModelUp.setAccessToken(token);
                //周期变化量
                riskEventModelUp.setStartDate(riskEventModelUp.getStartDateUp());
                riskEventModelUp.setEndDate(riskEventModelUp.getEndDateUp());
                aggEventsVoIPageUp.set(vocRiskService.riskPointAggNew(riskEventModelUp));

                return null;
            })));
        }

        //质量风险
//        IPage<RiskPointAggVo> aggQualityVoIPage = new Page<>();
        AtomicReference<IPage<RiskPointAggVo>> aggQualityVoIPage = new AtomicReference<>();
        AtomicReference<IPage<RiskPointAggVo>> aggQualityVoIPageAdd = new AtomicReference<>();

        if (!getQuality(model.getBrandCode())) {
            re.setQualityRisk(BigDecimal.valueOf(0));
        } else {

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                RiskEventInsightModel riskQualityModel1 = new RiskEventInsightModel();
                BeanUtil.copyProperties(model, riskQualityModel1);
                riskQualityModel1.setPageSize(10);
                aggQualityVoIPage.set(qualityRiskService.riskPointAggNew(riskQualityModel1));


                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                RiskEventInsightModel riskQualityModel2 = new RiskEventInsightModel();
                BeanUtil.copyProperties(model, riskQualityModel2);
                riskQualityModel2.setStartDate(model.getStartDateUp());
                riskQualityModel2.setEndDate(model.getEndDateUp());
                riskQualityModel2.setTopicCode(null);
                aggQualityVoIPageAdd.set(qualityRiskService.riskPointAggNew(riskQualityModel2));


                return null;
            })));
        }
        //投诉用户
//        IPage<ComplaintUserVo> userVoIPage = new Page<>();
        AtomicReference<IPage<ComplaintUserVo>> userVoIPage = new AtomicReference<>();
        AtomicReference<IPage<ComplaintUserVo>> userVoIPageAdd = new AtomicReference<>();

        if (!(channelSet != null && channelSet.size() > 0)) {
            re.setComplaintUser(BigDecimal.valueOf(0));
        } else {
            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                ComplaintUserTopModel complainUserModel = new ComplaintUserTopModel();
                BeanUtil.copyProperties(model, complainUserModel);
                complainUserModel.setPageSize(10);
                userVoIPage.set(vocUserRiskService.complaintUserList(complainUserModel));

                return null;
            })));

            futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {

                ComplaintUserTopModel complainUserModel2 = new ComplaintUserTopModel();
                BeanUtil.copyProperties(model, complainUserModel2);
                complainUserModel2.setStartDate(model.getStartDateUp());
                complainUserModel2.setEndDate(model.getEndDateUp());
                userVoIPageAdd.set(vocUserRiskService.complaintUserList(complainUserModel2));

                return null;
            })));
        }

        AtomicReference<BigDecimal> toBeReviewed = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> toBeConfirmed = new AtomicReference<>(BigDecimal.ZERO);
        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            toBeReviewed.set(riskAllTypesService.getToBeReviewed(model));

            return null;
        })));

        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            toBeConfirmed.set(recordService.getToBeConfirmed(model));

            return null;
        })));

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            return Result.OK(re);
//            throw new RuntimeException(e);
        }

        re.setToBeReviewed(toBeReviewed.get());
        re.setToBeConfirmed(toBeConfirmed.get());
       /* re.setToBeReviewed(riskAllTypesService.getToBeReviewed(model));
        re.setToBeConfirmed(recordService.getToBeConfirmed(model));*/

        if (ObjectUtil.isNotNull(userVoIPage.get())) {
            re.setComplaintUser(BigDecimal.valueOf(userVoIPage.get().getTotal()));
        }
        if (ObjectUtil.isNotNull(userVoIPageAdd.get())) {
            re.setComplaintUserAdd(NumberUtil.sub(re.getComplaintUser(), userVoIPageAdd.get().getTotal()));
        }
        if (ObjectUtil.isNotNull(aggQualityVoIPage.get())) {
            re.setQualityRisk(BigDecimal.valueOf(aggQualityVoIPage.get().getTotal()));
        }

        if (ObjectUtil.isNotNull(aggQualityVoIPageAdd.get())) {
            re.setQualityRiskAdd(NumberUtil.sub(re.getQualityRisk(), aggQualityVoIPageAdd.get().getTotal()));
        }
        if (ObjectUtil.isNotNull(aggEventsVoIPage.get())) {
            re.setRiskEvents(BigDecimal.valueOf(aggEventsVoIPage.get().getTotal()));
            if (ObjectUtil.isNotNull(aggEventsVoIPageUp.get())) {
                re.setRiskEventsAdd(BigDecimal.valueOf(aggEventsVoIPage.get().getTotal()).subtract(BigDecimal.valueOf(aggEventsVoIPageUp.get().getTotal())));
            }
        }

        return Result.OK(re);
    }

    @ApiOperation(value = "区域分布及占比")
    @PostMapping(value = "/regionalDistribution")
    @Cacheable(cacheNames = "voc:api:home:regionalDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalDistribution(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.regionalDistribution(model);
    }

    @ApiOperation(value = "省份排行")
    @PostMapping(value = "/rankingProvinces")
    @Cacheable(cacheNames = "voc:api:home:rankingProvinces", keyGenerator = "myCacheKeyGenerator")
    public Result<?> rankingProvinces(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.rankingProvinces(model);
    }

    @ApiOperation(value = "标签分布")
    @PostMapping(value = "/themeDistribution")
    @Cacheable(cacheNames = "voc:api:home:themeDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> themeDistribution(@RequestBody FilterCriteriaModel model) {
        return vocEmotionDiService.homeThemeDistribution(model);
    }

    @ApiOperation(value = "问题概览")
    @PostMapping(value = "/problemOverview2")
    public Result<?> problemOverview(@RequestBody FilterCriteriaModel model) {
        // 获取 token 逻辑简化
        String token = Optional.ofNullable(model.getAccessToken())
                .orElse(SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN));
        model.setAccessToken(token);

        // 构建基础查询模型
        RiskAllTypesModel baseModel = buildRiskAllTypesModel(model);

        // 并行执行两个查询
        IPage<RiskAllTypesVo> riskListFuture = queryRiskList(baseModel);
        IPage<RiskAllTypesVo> riskListByDepartFuture = queryRiskListByDepart(baseModel);

        Map<String, String> riskLevelDis = calculateRiskLevelDistribution(riskListFuture.getRecords());
        List<ProblemOverviewVo> riskDep = processRiskDepartmentData(riskListByDepartFuture.getRecords());
        Map<String, Object> result = new HashMap<>();
        // 获取结果
        result.put("riskLevelDis", riskLevelDis);
        result.put("riskDep", riskDep);
        return Result.OK(result);
    }

    @ApiOperation(value = "问题概览")
    @PostMapping(value = "/problemOverview")
    public Result<?> problemOverviewLevel(@RequestBody FilterCriteriaModel model) {
        List<RiskAllTypesVo> problemOverviewLevel = riskAllTypesService.problemOverviewLevel(model);
        List<RiskAllTypesVo> problemOverviewDepart = riskAllTypesService.problemOverviewDepart(model);
        Map<String, Integer> riskLevelDis = problemOverviewLevel.stream()
                .collect(Collectors.toMap(RiskAllTypesVo::getRiskLevel, RiskAllTypesVo::getRiskLevelNum));
       /* Map<String, Integer> riskLevelDis = problemOverviewLevel.stream()
                .collect(Collectors.toMap(
                        RiskAllTypesVo::getRiskLevel,
                        RiskAllTypesVo::getRiskLevelNum,
                        (existingValue, newValue) -> existingValue + newValue
                ));*/
        List<ProblemOverviewVo> riskDep = processRiskDepartmentData(problemOverviewDepart);
        Map<String, Object> over = new HashMap<>();
        over.put("riskLevelDis", riskLevelDis);
        over.put("riskDep", riskDep);
        return Result.OK(over);
    }

    // 提取的辅助方法
    private RiskAllTypesModel buildRiskAllTypesModel(FilterCriteriaModel model) {
        RiskAllTypesModel riskModel = new RiskAllTypesModel();
        riskModel.setPageNo(1);
        riskModel.setPageSize(999999);
        riskModel.setStartDate(model.getStartDate());
        riskModel.setEndDate(model.getEndDate());
        riskModel.setBrandCode(model.getBrandCode());
        return riskModel;
    }

    private IPage<RiskAllTypesVo> queryRiskList(RiskAllTypesModel model) {
        RiskAllTypesModel target = new RiskAllTypesModel();
        BeanUtils.copyProperties(model, target);
        return riskAllTypesService.riskList(new Page<>(target.getPageNo(), target.getPageSize()), target);
    }

    private IPage<RiskAllTypesVo> queryRiskListByDepart(RiskAllTypesModel model) {
        RiskAllTypesModel target = new RiskAllTypesModel();
        BeanUtils.copyProperties(model, target);
        target.setGroupByName("rw.departName");
        return riskAllTypesService.riskList(new Page<>(target.getPageNo(), target.getPageSize()), target);
    }

    private Map<String, String> calculateRiskLevelDistribution(List<RiskAllTypesVo> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyMap();
        }

        Map<String, String> distribution = new HashMap<>();
        String[] riskLevels = {"S", "A", "B", "C", "D"};

        for (String level : riskLevels) {
            long count = records.stream()
                    .filter(r -> (level.equals(r.getRiskLevel()) && StringUtils.isBlank(r.getNewRiskLevel()))
                            || (StringUtils.isNotBlank(r.getNewRiskLevel()) && level.equals(r.getNewRiskLevel())))
                    .count();
            distribution.put(level, count > 0 ? String.valueOf(count) : null);
        }

        return distribution;
    }

    private List<ProblemOverviewVo> processRiskDepartmentData(List<RiskAllTypesVo> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyList();
        }

        return records.stream()
                .map(this::convertToProblemOverview)
                .sorted(Comparator.comparing(ProblemOverviewVo::getTotal,
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    private ProblemOverviewVo convertToProblemOverview(RiskAllTypesVo riskVo) {
        ProblemOverviewVo overview = new ProblemOverviewVo();
        overview.setDepartId(riskVo.getDepartId());
        overview.setDepartName(riskVo.getDepartName());
        overview.setTotal(riskVo.getTotal());
        overview.setBindType(riskVo.getBindType());
        return overview;
    }

    @ApiOperation(value = "问题概览1")
    @PostMapping(value = "/problemOverview1")
    public Result<?> problemOverview1(@RequestBody FilterCriteriaModel model) {
        Map<String, Object> over = new HashMap<>();
        Map<String, String> mapList = new HashMap<>();
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);

        List<CompletableFuture<Void>> futureList = new CopyOnWriteArrayList<>();
        AtomicReference<IPage<RiskAllTypesVo>> riskAllTypeList = new AtomicReference<>();
        AtomicReference<IPage<RiskAllTypesVo>> riskAllTypeList2 = new AtomicReference<>();
        RiskAllTypesModel riskAllTypesModel = new RiskAllTypesModel();
        riskAllTypesModel.setPageNo(1);
        riskAllTypesModel.setPageSize(999999);
        riskAllTypesModel.setStartDate(model.getStartDate());
        riskAllTypesModel.setEndDate(model.getEndDate());
        riskAllTypesModel.setBrandCode(model.getBrandCode());

        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            RiskAllTypesModel target = new RiskAllTypesModel();
            BeanUtils.copyProperties(riskAllTypesModel, target);
            Page<RiskAllTypesVo> page = new Page<>(target.getPageNo(), target.getPageSize());
            riskAllTypeList.set(riskAllTypesService.riskList(page, target));

            return null;
        })));
        futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
            RiskAllTypesModel target = new RiskAllTypesModel();
            BeanUtils.copyProperties(riskAllTypesModel, target);
            target.setGroupByName("rw.departName");
            Page<RiskAllTypesVo> page = new Page<>(target.getPageNo(), target.getPageSize());
            riskAllTypeList2.set(riskAllTypesService.riskList(page, target));

            return null;
        })));

        try {
            CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(2, TimeUnit.MINUTES);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<RiskAllTypesVo> S = new ArrayList<>();
        List<RiskAllTypesVo> A = new ArrayList<>();
        List<RiskAllTypesVo> B = new ArrayList<>();
        List<RiskAllTypesVo> C = new ArrayList<>();
        List<RiskAllTypesVo> D = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(riskAllTypeList.get().getRecords())) {
            List<RiskAllTypesVo> records = riskAllTypeList.get().getRecords();
            S = records.stream().filter(r -> ("S".equals(r.getRiskLevel()) && StringUtils.isBlank(r.getNewRiskLevel()) || (StringUtils.isNotBlank(r.getNewRiskLevel()) && "S".equals(r.getNewRiskLevel())))).collect(Collectors.toList());
            A = records.stream().filter(r -> ("A".equals(r.getRiskLevel()) && StringUtils.isBlank(r.getNewRiskLevel()) || (StringUtils.isNotBlank(r.getNewRiskLevel()) && "A".equals(r.getNewRiskLevel())))).collect(Collectors.toList());
            B = records.stream().filter(r -> ("B".equals(r.getRiskLevel()) && StringUtils.isBlank(r.getNewRiskLevel()) || (StringUtils.isNotBlank(r.getNewRiskLevel()) && "B".equals(r.getNewRiskLevel())))).collect(Collectors.toList());
            C = records.stream().filter(r -> ("C".equals(r.getRiskLevel()) && StringUtils.isBlank(r.getNewRiskLevel()) || (StringUtils.isNotBlank(r.getNewRiskLevel()) && "C".equals(r.getNewRiskLevel())))).collect(Collectors.toList());
            D = records.stream().filter(r -> ("D".equals(r.getRiskLevel()) && StringUtils.isBlank(r.getNewRiskLevel()) || (StringUtils.isNotBlank(r.getNewRiskLevel()) && "D".equals(r.getNewRiskLevel())))).collect(Collectors.toList());
        }
        mapList.put("S", ObjectUtils.isNotEmpty(S) ? S.size() + "" : null);
        mapList.put("A", ObjectUtils.isNotEmpty(A) ? A.size() + "" : null);
        mapList.put("B", ObjectUtils.isNotEmpty(B) ? B.size() + "" : null);
        mapList.put("C", ObjectUtils.isNotEmpty(C) ? C.size() + "" : null);
        mapList.put("D", ObjectUtils.isNotEmpty(D) ? D.size() + "" : null);
        over.put("riskLevelDis", mapList);
        List<ProblemOverviewVo> problemOverviewVoList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(riskAllTypeList2.get().getRecords())) {
            List<RiskAllTypesVo> records = riskAllTypeList2.get().getRecords();
            for (RiskAllTypesVo riskAllTypesVo : records) {
                ProblemOverviewVo problemOverviewVo = new ProblemOverviewVo();
                problemOverviewVo.setDepartId(riskAllTypesVo.getDepartId());
                problemOverviewVo.setDepartName(riskAllTypesVo.getDepartName());
                problemOverviewVo.setTotal(riskAllTypesVo.getTotal());
                problemOverviewVoList.add(problemOverviewVo);
            }
        }
        // 使用 Comparator.nullsLast 处理 null 值，并按 getTotal 降序排序
        List<ProblemOverviewVo> sortedList = problemOverviewVoList.stream()
                .sorted(Comparator.comparing(ProblemOverviewVo::getTotal, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        over.put("riskDep", sortedList);

        return Result.OK(over);
    }


    @ApiOperation(value = "处理进度")
    @PostMapping(value = "/processingProgress2")
    public Result<?> processingProgress(@RequestBody FilterCriteriaModel model) {
        Map<String, Object> progr = riskAllTypesService.processingProgress(model);
        return Result.OK(progr);
    }

    @ApiOperation(value = "处理进度")
    @PostMapping(value = "/processingProgress")
    public Result<?> processingProgress2(@RequestBody FilterCriteriaModel model) {
        RiskAllTypesVo progr = riskAllTypesService.processingProgress2(model);
        Map<String, Object> map = new HashMap<>();
        map.put("complete", progr.getComplete());
        map.put("total", progr.getTotal());
        return Result.OK(map);
    }

    @ApiOperation(value = "任务列表Top")
    @PostMapping(value = "/taskListTop2")
    public Result<?> taskListTop2(@RequestBody FilterCriteriaModel model) {
        List<RiskAllTypesVo> voIPage = riskAllTypesService.taskListTop(model);
        return Result.OK(voIPage);
    }

    @ApiOperation(value = "任务列表Top")
    @PostMapping(value = "/taskListTop")
    public Result<?> taskListTop(@RequestBody FilterCriteriaModel model) {
        RiskAllTypesModel riskAllTypesModel = new RiskAllTypesModel();
        BeanUtil.copyProperties(model, riskAllTypesModel);
        IPage<RiskAllTypesVo> voIPage = riskAllTypesService.taskProcessingList(riskAllTypesModel);
        return Result.OK(voIPage.getRecords());
    }


}
