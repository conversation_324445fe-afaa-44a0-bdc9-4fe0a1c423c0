package com.car.voc.stats.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.serivce.DwdVocSentenceService;
import com.car.stats.serivce.IDwsVocQualityEmotionDiService;
import com.car.stats.serivce.wo.VehicleRescueService;
import com.car.stats.serivce.wo.WoBaseOriginalDataService;
import com.car.stats.vo.ProportionCarSeriesVo;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.*;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wiremock.org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 车辆救援
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "voc-声音洞察-车辆救援")
@RestController
@AllArgsConstructor
@RequestMapping("/stats/vehicleRescue")
public class VehicleRescueController {

    private final DwdVocSentenceService dwdVocSentenceService;

    private final SysUserRoleMapper sysUserRoleMapper;

    private final IDwsVocQualityEmotionDiService qualityEmotionDiService;

    private final WoBaseOriginalDataService woService;

    private final VehicleRescueService vehicleRescueService;

    private boolean getQuality(String brandCode) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (StringUtils.isNotEmpty(qualityText) && StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }


    @AutoLog(value = "VOC简报")
    @ApiOperation(value = "提及量，用户数")
    @PostMapping(value = "/statisticUsers")
    public Result<?> statisticUsers(@RequestBody ProductQualityFilterCriteriaModel model) {
        FilterCriteriaModel filterModel = BeanUtil.copyProperties(model, FilterCriteriaModel.class);
        return dwdVocSentenceService.mentionsUserChannel(filterModel);
    }

    @AutoLog(value = "VOC简报-工单数量")
    @ApiOperation(value = "工单数量-救援量")
    @PostMapping(value = "/workOrderNumber")
    public Result<StatisticVo> workOrderNumber(@RequestBody FilterCriteriaModel model) {
        StatisticVo res = woService.workOrderNumber(model);
        return Result.OK(res);
    }

    /**
     * 满意率统计：满意工单数/总工单数
     *
     * @param model
     * @return
     */
    @AutoLog(value = "VOC简报-满意率")
    @ApiOperation(value = "满意率")
    @PostMapping(value = "/customerSatisfaction")
    public Result<StatisticVo> customerSatisfaction(@RequestBody FilterCriteriaModel model) {
        StatisticVo res = vehicleRescueService.customerSatisfaction(model);
        return Result.OK(res);
    }

    @AutoLog(value = "VOC简报-救援状态")
    @ApiOperation(value = "救援状态")
    @PostMapping(value = "/rescueStatus")
    public Result<WoRescueVolumeVo> rescueStatus(@RequestBody FilterCriteriaModel model) {
        WoRescueVolumeVo res = vehicleRescueService.rescueStatus(model);
        return Result.OK(res);
    }

    @ApiOperation(value = "用户类型")
    @PostMapping(value = "/customerType")
    public Result<List<WoCustomerTypeProportionVo>> customerType(@RequestBody FilterCriteriaModel model) {
        List<WoCustomerTypeProportionVo> res = vehicleRescueService.customerType(model);
        return Result.OK(res);
    }

    @ApiOperation(value = "接收点")
    @PostMapping(value = "/receiver")
    public Result<List<WoReceiverVo>> receiver(@RequestBody FilterCriteriaModel model) {
        List<WoReceiverVo> res = vehicleRescueService.receiver(model);
        return Result.OK(res);
    }

    @ApiOperation(value = "救援状态趋势")
    @PostMapping(value = "/rescueStatusTrend")
    public Result<List<WoRescueVolumeVo>> rescueStatusTrend(@RequestBody FilterCriteriaModel model) {
        List<WoRescueVolumeVo> res = vehicleRescueService.rescueStatusTrend(model);
        return Result.OK(res);
    }

    @ApiOperation(value = "飙升问题")
    @PostMapping(value = "/soarProblem")
    public Result<?> soarProblem(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.soarProblem(model);
    }

    @ApiOperation(value = "典型问题")
    @PostMapping(value = "/typicalProblems")
    public Result<?> typicalProblems(@RequestBody ProductQualityFilterCriteriaModel model) {
        if (!getQuality(model.getBrandCode())) {
            return Result.OK(new ArrayList<>());
        }
        return qualityEmotionDiService.typicalProblems(model);
    }

    @ApiOperation(value = "共性问题")
    @PostMapping(value = "/commonProblems")
    public Result<?> commonProblems(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.commonProblems(model);
    }

/*    @ApiOperation(value = "问题故障分布")
    @PostMapping(value = "/distributionProblemParts")
    public Result<?> distributionProblemParts(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.distributionProblemParts(model);
    }*/
    @ApiOperation(value = "救援问题分布")
    @PostMapping(value = "/distributionProblemParts")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> tagDistribution(@RequestBody FilterCriteriaModel model) {
        return dwdVocSentenceService.tagDistribution(model);
    }


    @ApiOperation(value = "高频故障热点")
    @PostMapping(value = "/hotWords")
    public Result<?> hotWords(@RequestBody FilterCriteriaModel model) {
        model.setEmotion("负面");
        return qualityEmotionDiService.hotWords(model);
    }

    @ApiOperation(value = "省份地图")
    @PostMapping(value = "/provinceMap")
    public Result<?> provinceMap(@RequestBody FilterCriteriaModel model) {
        return woService.provinceMap(model);
    }

    @ApiOperation(value = "大区排行")
    @PostMapping(value = "/regionalTop")
    public Result<?> regionalTop(@RequestBody FilterCriteriaModel model) {
        return woService.regionalTop(model);
    }

    @ApiOperation(value = "小区排行")
    @PostMapping(value = "/communityTop")
    public Result<?> communityTop(@RequestBody FilterCriteriaModel model) {
        return woService.communityTop(model);
    }

    @ApiOperation(value = "车系排行")
    @PostMapping(value = "/carTop")
    public Result<List<ProportionCarSeriesVo>> carSeriesTop(@RequestBody FilterCriteriaModel model) {
        model.setRownum(20);
        return Result.OK(vehicleRescueService.carSeriesTop(model));
    }

    @ApiOperation(value = "车型排行")
    @PostMapping(value = "/carModelTop")
    public Result<List<ProportionCarSeriesVo>> carModelTop(@RequestBody FilterCriteriaModel model) {
        model.setRownum(20);
        return Result.OK(vehicleRescueService.carModelTop(model));
    }

    @ApiOperation(value = "网点排行")
    @PostMapping(value = "/outletsTop")
    public Result<List<BranchesVo>> outletsTop(@RequestBody FilterCriteriaModel model) {
        model.setRownum(30);
        return Result.OK(vehicleRescueService.outletsTop(model));
    }

    @ApiOperation(value = "工单列表")
    @PostMapping(value = "/workOrderDetailsList")
    public Result<Page<WoRescueVo>> workOrderDetailsList(@RequestBody FilterCriteriaModel model) {
        Page<WoRescueVo> result = vehicleRescueService.workOrderDetailsList(model);
        return Result.OK(result);
    }

}
