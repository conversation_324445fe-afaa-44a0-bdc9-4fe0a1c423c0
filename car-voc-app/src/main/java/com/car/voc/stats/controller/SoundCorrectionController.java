package com.car.voc.stats.controller;

import com.car.stats.serivce.IVocSoundCorrectionService;
import com.car.voc.common.Result;
import com.car.voc.entity.VocSoundCorrection;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * voc-纠错表
 * @version 1.0.0
 * @ClassName SoundCorrectionController.java
 * @Description TODO
 * @createTime 2023年03月03日 14:11
 * @Copyright voc
 */
@Api(tags="voc-纠错表")
@RestController
@RequestMapping("/voc/correction")
public class SoundCorrectionController {

    @Autowired
    IVocSoundCorrectionService correctionService;


    @ApiOperation(value="我要纠错")
    @PostMapping(value = "/errorCorrection")
    public Result<?> errorCorrection(@RequestBody VocSoundCorrection model) {
        model.setCreateTime(new Date());
        model.setUserid(getUserInfo().getId());
        model.setUsername(getUserInfo().getUsername());
        correctionService.save(model);
        return Result.OK(model);
    }


    LoginUser getUserInfo(){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return sysUser;
    }


}
