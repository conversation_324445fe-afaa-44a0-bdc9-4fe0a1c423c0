package com.car.voc.stats.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.ComplaintUserTopModel;
import com.car.stats.serivce.IDwdVocUserRiskService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.risk.ComplaintUserVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * voc-高频用户投诉
 * @version 1.0.0
 * @Description
 * @createTime 2022年11月23日 11:42
 * @Copyright voc
 */

@Api(tags="voc-高频用户投诉")
@RestController
@RequestMapping("/stats/complaintsUsersTop")
@Slf4j
public class RiskUsersTopController {

    @Autowired
    IDwdVocUserRiskService vocUserRiskService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @AutoLog(value = "业务声音洞察-投诉用户排行")
    @ApiOperation(value="投诉用户")
    @PostMapping(value = "/complaintUser")
    public Result<?> complaintUser(@RequestBody ComplaintUserTopModel model) {
        model.SetUpCycle();
        IPage<ComplaintUserVo> userVoIPage=new Page<>();
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, model.getBrandCode(), getUserId()));
        if (CollUtil.isEmpty(channelSet)) {
            return Result.OK(userVoIPage);
        }

        /*Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG,model.getBrandCode(),getUserId()));
        if (!(channelSet!=null&&channelSet.size()>0)){
            return Result.OK(userVoIPage);
        }*/
        userVoIPage=vocUserRiskService.complaintUserList(model);
        return Result.OK(userVoIPage);
    }


    public String getUserId(){
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return loginUser.getId();
    }


    @ApiOperation(value="信息列表")
    @PostMapping(value = "/riskInfoList")
    public Result<?> riskInfoList(@RequestBody ComplaintUserTopModel model) {
        if (!StrUtil.isNotBlank(model.getId())){
            return Result.error("id不能为空");
        }
        return sentenceVocService.complaintUserSentence(model);
    }



}
