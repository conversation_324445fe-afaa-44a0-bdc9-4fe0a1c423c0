package com.car.voc.stats.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.LabelSelectModel;
import com.car.stats.serivce.IDwsVocEmotionDiService;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.strksService.IDwdVocSentenceService;
import com.car.stats.vo.EmotionProportionVo;
import com.car.stats.vo.SoundContentVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * voc-声音洞察-聚焦关注
 * @version 1.0.0
 * @ClassName FocusSoundInsightController.java
 * @Description TODO
 * @createTime 2022年10月16日 23:49
 * @Copyright voc
 */
@Api(tags="voc-声音洞察-聚焦关注")
@RestController
@RequestMapping("/stats/focus")
@Slf4j
public class SoundInsightFocusController {
    @Autowired
    IDwsVocEmotionDiService iDwsVocEmotionDiService;
    @Autowired
    IDwsVocEmotionUserDiService dwsVocEmotionUserDiService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @Autowired
    IDwdVocSentenceService vocSentenceService;
    @AutoLog(value = "业务声音洞察-聚焦关注")
    @ApiOperation(value="简报数值")
    @PostMapping(value = "/briefingValue")
//    @Cacheable(cacheNames = "voc:api:focus:briefingValue", keyGenerator = "myCacheKeyGenerator")
    public Result<?> overviewBriefingValue(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.soundInsightBriefingValue(model);
    }
    @ApiOperation(value="区域分布及占比")
    @PostMapping(value = "/regionalDistribution")
    @Cacheable(cacheNames = "voc:api:focus:regionalDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalDistribution(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.regionalDistribution(model);
    }
    @ApiOperation(value = "大区排行")
    @PostMapping(value = "/regionalTop")
    @Cacheable(cacheNames = "voc:api:focus:regionalTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalTop(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.focusRegionalTop(model);
    }
    @ApiOperation(value = "小区排行")
    @PostMapping(value = "/communityTop")
    @Cacheable(cacheNames = "voc:api:focus:communityTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> communityTop(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.focusCommunityTop(model);
    }


    @ApiOperation(value = "省份地图")
    @PostMapping(value = "/provinceMap")
    @Cacheable(cacheNames = "voc:api:focus:provinceMap", keyGenerator = "myCacheKeyGenerator")
    public Result<?> provinceMap(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.provinceMap(model);
    }
    @ApiOperation(value="省份排行")
    @PostMapping(value = "/rankingProvinces")
    @Cacheable(cacheNames = "voc:api:focus:rankingProvinces", keyGenerator = "myCacheKeyGenerator")
    public Result<?> rankingProvinces(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.rankingProvinces(model);
    }
    @ApiOperation(value="渠道分布")
    @PostMapping(value = "/channelDistribution")
    @Cacheable(cacheNames = "voc:api:focus:channelDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> channelDistribution(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.channelDistribution(model);
    }

    @ApiOperation(value="渠道趋势")
    @PostMapping(value = "/channelTrend")
    @Cacheable(cacheNames = "voc:api:focus:channelTrend", keyGenerator = "myCacheKeyGenerator")
    public Result<?> channelTrend(@RequestBody FilterCriteriaModel model) {

        return iDwsVocEmotionDiService.channelTrend(model);
//        return vocSentenceService.channelTrend(model);
    }
    @ApiOperation(value="情感占比")
    @PostMapping(value = "/proportionEmotion")
    @Cacheable(cacheNames = "voc:api:focus:proportionEmotion", keyGenerator = "myCacheKeyGenerator")
    public Result<EmotionProportionVo> homeProportionEmotion(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.focusProportionEmotion(model);
    }
    @ApiOperation(value="标签分布")
    @PostMapping(value = "/themeDistribution")
    @Cacheable(cacheNames = "voc:api:focus:themeDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> themeDistribution(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.themeDistribution(model);
    }
    @ApiOperation(value="标签份额")
    @PostMapping(value = "/themeShare")
    @Cacheable(cacheNames = "voc:api:focus:themeShare", keyGenerator = "myCacheKeyGenerator")
    public Result<?> themeShare(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.themeShare(model);
    }
    @ApiOperation(value="渠道提及量top")
    @PostMapping(value = "/channelQuantityTop")
    @Cacheable(cacheNames = "voc:api:focus:channelQuantityTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> channelQuantityTop(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.channelQuantityTop(model);
    }
    @ApiOperation(value="渠道提及量top_new")
    @PostMapping(value = "/channelQuantityTop_new")
    @Cacheable(cacheNames = "voc:api:focus:channelQuantityTop_new", keyGenerator = "myCacheKeyGenerator")
    public Result<?> channelQuantityTopNew(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.channelQuantityTop_new(model);
    }

    @ApiOperation(value="声音内容")
    @PostMapping(value = "/soundContent")
    @Cacheable(cacheNames = "voc:api:focus:soundContent", keyGenerator = "myCacheKeyGenerator")
    public Result<?> soundContent(@RequestBody FilterCriteriaModel model) {
        Page<SoundContentVo> page=new Page<>(model.getPageNo(), model.getPageSize());
        return sentenceVocService.focusSoundContent(model,page);
    }

    @ApiOperation(value="Top发声用户")
    @PostMapping(value = "/topVoiceUsers")
//    @Cacheable(cacheNames = "voc:api:focus:topVoiceUsers", keyGenerator = "myCacheKeyGenerator")
    public Result<?> topVoiceUsers(@RequestBody FilterCriteriaModel model) {
        return dwsVocEmotionUserDiService.topVoiceUsers(model);
    }

    @ApiOperation(value="高频热词")
    @PostMapping(value = "/hotWords")
    @Cacheable(cacheNames = "voc:api:focus:hotWords", keyGenerator = "myCacheKeyGenerator")
    public Result<?> hotWords(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.hotWords(model);
    }
    @ApiOperation(value="飙升热词")
    @PostMapping(value = "/soarHotWords")
    @Cacheable(cacheNames = "voc:api:focus:soarHotWords", keyGenerator = "myCacheKeyGenerator")
    public Result<?> soarHotWords(@RequestBody FilterCriteriaModel model) {
        return iDwsVocEmotionDiService.soarHotWords(model);
    }
    @ApiOperation(value="情感与提及率分析")
    @PostMapping(value = "/analyEmotionMentionEate")
    @Cacheable(cacheNames = "voc:api:focus:analyEmotionMentionEate", keyGenerator = "myCacheKeyGenerator")
    public Result<?> analyEmotionMentionEate(@RequestBody LabelSelectModel model) {
        return iDwsVocEmotionDiService.analyEmotionMentionEate(model);
    }


}
