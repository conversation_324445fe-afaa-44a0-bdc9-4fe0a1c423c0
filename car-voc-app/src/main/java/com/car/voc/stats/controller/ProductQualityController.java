package com.car.voc.stats.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.serivce.IDwsVocQualityEmotionDiService;
import com.car.stats.serivce.IDwsVocQualityUserDiService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.SoundContentVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wiremock.org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * voc-声音洞察-产品质量
 * @version 1.0.0
 * @ClassName FocusSoundInsightController.java
 * @Description 声音洞察-产品质量
 * @createTime 2022年10月16日 23:49
 * @Copyright voc
 */
@Api(tags="voc-声音洞察-产品质量")
@RestController
@RequestMapping("/stats/productQuality")
@Slf4j
public class ProductQualityController {

    @Autowired
    IDwsVocQualityEmotionDiService qualityEmotionDiService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;



    @Autowired
    IDwsVocQualityUserDiService iDwsVocQualityUserDiService;
    @AutoLog(value = "质量声音洞察-产品质量")
    @ApiOperation(value="简报数值")
    @PostMapping(value = "/briefingValue")
//    @Cacheable(cacheNames = "voc:api:productQuality:briefingValue", keyGenerator = "myCacheKeyGenerator")
    public Result<?> briefingValue(@RequestBody ProductQualityFilterCriteriaModel model) {
        model.setEmotion(null);
        return qualityEmotionDiService.briefingValue(model);
    }

    @ApiOperation(value="Top反馈用户")
    @PostMapping(value = "/topVoiceUsers")
    @Cacheable(cacheNames = "voc:api:productQuality:topVoiceUsers", keyGenerator = "myCacheKeyGenerator")
    public Result<?> topQualityUsers(@RequestBody ProductQualityFilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocQualityUserDiService.topQualityUsers(model);
    }

    @ApiOperation(value="区域分布及占比")
    @PostMapping(value = "/regionalDistribution")
    @Cacheable(cacheNames = "voc:api:productQuality:regionalDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalDistribution(@RequestBody ProductQualityFilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocQualityUserDiService.regionalDistribution(model);
    }

    @ApiOperation(value="省份排行")
    @PostMapping(value = "/rankingProvinces")
    @Cacheable(cacheNames = "voc:api:productQuality:rankingProvinces", keyGenerator = "myCacheKeyGenerator")
    public Result<?> rankingProvinces(@RequestBody ProductQualityFilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocQualityUserDiService.rankingProvinces(model);
    }

    @ApiOperation(value = "大区排行")
    @PostMapping(value = "/regionalTop")
    @Cacheable(cacheNames = "voc:api:productQuality:regionalTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalTop(@RequestBody ProductQualityFilterCriteriaModel model) {
        return iDwsVocQualityUserDiService.focusRegionalTop(model);
    }
    @ApiOperation(value = "小区排行")
    @PostMapping(value = "/communityTop")
    @Cacheable(cacheNames = "voc:api:productQuality:communityTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> communityTop(@RequestBody ProductQualityFilterCriteriaModel model) {
        return iDwsVocQualityUserDiService.focusCommunityTop(model);
    }
    @ApiOperation(value = "省份地图")
    @PostMapping(value = "/provinceMap")
    @Cacheable(cacheNames = "voc:api:productQuality:provinceMap", keyGenerator = "myCacheKeyGenerator")
    public Result<?> provinceMap(@RequestBody ProductQualityFilterCriteriaModel model) {
        return iDwsVocQualityUserDiService.rankingProvinces(model);
    }

    @ApiOperation(value="渠道分布")
    @PostMapping(value = "/channelDistribution")
    @Cacheable(cacheNames = "voc:api:productQuality:channelDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> channelDistribution(@RequestBody ProductQualityFilterCriteriaModel model) {
        model.setEmotion(null);
        return qualityEmotionDiService.channelDistributionProduct(model);
    }

    @ApiOperation(value="严重性占比")
    @PostMapping(value = "/severityRatio")
    @Cacheable(cacheNames = "voc:api:productQuality:severityRatio", keyGenerator = "myCacheKeyGenerator")
    public Result<?> severityRatio(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.severityRatio(model);
    }

    @ApiOperation(value="飙升问题")
    @PostMapping(value = "/soarProblem")
    @Cacheable(cacheNames = "voc:api:productQuality:soarProblem", keyGenerator = "myCacheKeyGenerator")
    public Result<?> soarProblem(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.soarProblem(model);
    }
    @ApiOperation(value="问题分布")
    @PostMapping(value = "/problemDistribution")
    @Cacheable(cacheNames = "voc:api:productQuality:problemDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> problemDistribution(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.problemDistribution(model);
    }
    @ApiOperation(value="问题趋势")
    @PostMapping(value = "/problemTrends")
    @Cacheable(cacheNames = "voc:api:productQuality:problemTrends", keyGenerator = "myCacheKeyGenerator")
    public Result<?> problemTrends(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.problemTrends(model);
    }
    @ApiOperation(value="典型问题")
    @PostMapping(value = "/typicalProblems")
    @Cacheable(cacheNames = "voc:api:productQuality:typicalProblems", keyGenerator = "myCacheKeyGenerator")
    public Result<?> typicalProblems(@RequestBody ProductQualityFilterCriteriaModel model) {
        if (!getQuality(model.getBrandCode())){
            return Result.OK(new ArrayList<>());
        }

        if (!model.getFirstDimensionCode().contains("Q")) {
            if ((StrUtil.isNotBlank(model.getFirstDimensionCode())&&!"B1001".equals(model.getFirstDimensionCode()))||(model.getSecondDimensionCodes()!=null&&model.getSecondDimensionCodes().size()>0&&!model.getSecondDimensionCodes().contains("B1002006"))) {
            return Result.OK(new ArrayList<>());
            }
        }
        if (!StrUtil.isBlankIfStr(model.getFirstDimensionCode())&&model.getFirstDimensionCode().contains("B")){
            model.setFirstDimensionCode(null);
            model.setSecondDimensionCodes(null);
        }
            return qualityEmotionDiService.typicalProblems(model);
    }
    @ApiOperation(value="共性问题")
    @PostMapping(value = "/commonProblems")
    @Cacheable(cacheNames = "voc:api:productQuality:commonProblems", keyGenerator = "myCacheKeyGenerator")
    public Result<?> commonProblems(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.commonProblems(model);
    }

    @ApiOperation(value="声音内容")
    @PostMapping(value = "/soundContent")
    public Result<?> soundContent(@RequestBody ProductQualityFilterCriteriaModel model) {
        model.setEmotion(null);
        Page<SoundContentVo> page=new Page<>(model.getPageNo(), model.getPageSize());
        return sentenceVocService.productQualitySoundContent(model,page);
    }

    @ApiOperation(value="趋势对比")
    @PostMapping(value = "/componentTrendParts")
    @Cacheable(cacheNames = "voc:api:productQuality:componentTrendParts", keyGenerator = "myCacheKeyGenerator")
    public Result<?> componentTrendParts(@RequestBody ProductQualityFilterCriteriaModel model) {
        if (!model.getFirstDimensionCode().contains("Q")) {
            if ((StrUtil.isNotBlank(model.getFirstDimensionCode())&&!"B1001".equals(model.getFirstDimensionCode()))||(model.getSecondDimensionCodes()!=null&&model.getSecondDimensionCodes().size()>0)) {
        return Result.OK(new ArrayList<>());
        }
      }
        if (!StrUtil.isBlankIfStr(model.getFirstDimensionCode())&&model.getFirstDimensionCode().contains("B")){
            model.setFirstDimensionCode(null);
            model.setSecondDimensionCodes(null);
        }
            return qualityEmotionDiService.componentTrendParts(model);
    }
    @ApiOperation(value="问题部件分布")
    @PostMapping(value = "/distributionProblemParts")
    @Cacheable(cacheNames = "voc:api:productQuality:distributionProblemParts", keyGenerator = "myCacheKeyGenerator")
    public Result<?> distributionProblemParts(@RequestBody ProductQualityFilterCriteriaModel model) {
        return qualityEmotionDiService.distributionProblemParts(model);
    }
    @ApiOperation(value="飙升部件")
    @PostMapping(value = "/partsSoarComponents")
    @Cacheable(cacheNames = "voc:api:productQuality:partsSoarComponents", keyGenerator = "myCacheKeyGenerator")
    public Result<?> partsSoarComponents(@RequestBody ProductQualityFilterCriteriaModel model) {
        Map<String,Object> re=new HashMap<>();
        if (!getQuality(model.getBrandCode())){
            re.put("mention",new ArrayList<>());
            re.put("repair",new ArrayList<>());
            return Result.OK(re);
        }

        if (!model.getFirstDimensionCode().contains("Q")) {
            if ((StrUtil.isNotBlank(model.getFirstDimensionCode())&&!"B1001".equals(model.getFirstDimensionCode()))||(model.getSecondDimensionCodes()!=null&&model.getSecondDimensionCodes().size()>0)){
            re.put("mention",new ArrayList<>());
            re.put("repair",new ArrayList<>());
            return Result.OK(re);
            }
        }

        if (!StrUtil.isBlankIfStr(model.getFirstDimensionCode())&&model.getFirstDimensionCode().contains("B")){
            model.setFirstDimensionCode(null);
            model.setSecondDimensionCodes(null);
        }
        return qualityEmotionDiService.partsSoarComponents(model);
    }



    @Autowired
    SysUserRoleMapper sysUserRoleMapper;
    boolean getQuality(String brandCode){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (StringUtils.isNotEmpty(qualityText)&&StringUtils.isNotEmpty(brandCode)){
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)){
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }
}
