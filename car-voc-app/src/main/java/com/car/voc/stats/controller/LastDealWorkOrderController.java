package com.car.voc.stats.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.wo.WoOriginalData;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.ProductQualityFilterCriteriaModel;
import com.car.stats.serivce.DwdVocSentenceService;
import com.car.stats.serivce.wo.WoBaseOriginalDataService;
import com.car.stats.vo.StatisticVo;
import com.car.stats.vo.wo.WoLastDealVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * voc-后处理工单
 * @version 1.0.0
 * @ClassName LastDealWorkOrderController.java
 * @Description 后处理工单
 * @createTime 2022年10月16日 23:49
 * @Copyright voc
 */
@Api(tags = "voc-后处理工单")
@RestController
@RequestMapping("/last/order")
@Slf4j
public class LastDealWorkOrderController {
    @Autowired
    DwdVocSentenceService dwdVocSentenceService;
    @Autowired
    WoBaseOriginalDataService woBaseOriginalDataService;
    @ApiOperation(value = "工单处理分布")
    @PostMapping(value = "/tagDistribution")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> tagDistribution(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return dwdVocSentenceService.tagDistribution(model);
    }

    @ApiOperation(value = "处理不满意热点")
    @PostMapping(value = "/hotWords")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> hotWords(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return dwdVocSentenceService.hotWords(model);
    }
    @ApiOperation(value = "工单类型占比")
    @PostMapping(value = "/woTypeProportion")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> woTypeProportion(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.woTypeProportion(model);
    }

    @ApiOperation(value = "工单状态分布")
    @PostMapping(value = "/woStatusDistribution")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> woStatusDistribution(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.woStatusDistribution(model);
    }

    @ApiOperation(value = "后处理工单趋势")
    @PostMapping(value = "/woTrend")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> woTrend(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.woTrend(model);
    }

    @ApiOperation(value = "区域分布占比")
    @PostMapping(value = "/woReginDistribution")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> woReginDistribution(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.woReginDistribution(model);
    }
    @ApiOperation(value = "后处理工单列表")
    @PostMapping(value = "/woList")
//    @Cacheable(cacheNames = "voc:api:home:tagDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> woList(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        Page<WoLastDealVo> woOriginalDataPage = woBaseOriginalDataService.woList(model);
        return Result.OK(woOriginalDataPage);
    }

    @AutoLog(value = "VOC简报")
    @ApiOperation(value = "提及量，用户数")
    @PostMapping(value = "/statisticUsers")
    public Result<?> statisticUsers(@RequestBody ProductQualityFilterCriteriaModel model) {
        FilterCriteriaModel filterModel = BeanUtil.copyProperties(model, FilterCriteriaModel.class);
        filterModel.setWoFlow("1");
        return dwdVocSentenceService.mentionsUserChannel(filterModel);
    }

    @AutoLog(value = "VOC简报-工单数量")
    @ApiOperation(value = "工单数量")
    @PostMapping(value = "/workOrderNumber")
    public Result<StatisticVo> workOrderNumber(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        StatisticVo res = woBaseOriginalDataService.workOrderNumber(model);
        return Result.OK(res);
    }

    @AutoLog(value = "VOC简报-满意率")
    @ApiOperation(value = "满意率")
    @PostMapping(value = "/workOrderSatisfaction")
    public Result<?> workOrderSatisfaction(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.workOrderSatisfaction(model);
    }

    @ApiOperation(value = "省份地图")
    @PostMapping(value = "/provinceMap")
    public Result<?> provinceMap(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.provinceMap(model);
    }

    @ApiOperation(value = "大区排行")
    @PostMapping(value = "/regionalTop")
    public Result<?> regionalTop(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.regionalTop(model);
    }

    @ApiOperation(value = "小区排行")
    @PostMapping(value = "/communityTop")
    public Result<?> communityTop(@RequestBody FilterCriteriaModel model) {
        model.setWoFlow("1");
        return woBaseOriginalDataService.communityTop(model);
    }
}
