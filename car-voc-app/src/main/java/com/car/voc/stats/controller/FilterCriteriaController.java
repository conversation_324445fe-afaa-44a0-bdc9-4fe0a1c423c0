package com.car.voc.stats.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.enums.DateStyle;
import com.car.voc.common.util.DateUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SvwDate;
import com.car.voc.dto.BrandProductManagerDto;
import com.car.voc.entity.*;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import com.car.voc.service.*;
import com.car.voc.vo.*;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * voc-筛选条件
 * @version 1.0.0
 * @ClassName FilterCriteriaController.java
 * @Description 筛选条件
 * @createTime 2022年10月10日 18:08
 * @Copyright voc
 */
@Api(tags = "voc-筛选条件")
@RestController
@RequestMapping("/stats/filterCriteria")
@Slf4j
public class FilterCriteriaController {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IVocBusinessTagService vocBusinessTagService;
    @Autowired
    IVocChannelCategoryService channelCategoryService;
    @Resource
    ISysRoleService sysRoleService;

    @Resource
    private ISysDepartService sysDepartService;

    @ApiOperation(value = "voc获取日期集合", notes = "voc获取日期集合")
    @GetMapping(value = "/getTime")
    @ApiModelProperty(value = "类型（0为周，1为月，2为季,3为年）", required = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型（0为周，1为月，2为季,3为年）", defaultValue = "1"),
    })
    public Result<?> getTime(int type) {
        Date T_2 = DateUtil.offsetDay(new Date(), -1);

        Date s = DateUtils.StringToDate(DateUtils.getDate(T_2), DateStyle.YYYY_MM_DD);
        List<SvwDate> list = DateUtils.GetTime(s, s, type);
        return Result.OK(type == 0 ? list : list.stream().filter(e -> Integer.valueOf(e.getYear()) >= 2022).collect(Collectors.toList()));
    }

    @ApiOperation(value = "voc获取日期集合", notes = "voc获取日期集合")
    @GetMapping(value = "/getTimeNorm")
    @ApiModelProperty(value = "类型（0为周，1为月，2为季,3为年）", required = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型（0为周，1为月，2为季,3为年）", defaultValue = "1"),
    })
    public Result<?> getTimeNew(int type) {
        Date s = DateUtils.StringToDate(DateUtils.getDate(new Date()), DateStyle.YYYY_MM_DD);
        List<SvwDate> list = DateUtils.GetTime(s, s, type, 0);
        return Result.OK(type == 0 ? list : list.stream().filter(e -> Integer.valueOf(e.getYear()) >= 2022).collect(Collectors.toList()));
    }

    @ApiOperation("查询所有部门")
    @RequestMapping(value = "/departAllList", method = RequestMethod.GET)
    public Result<List<SysDepart>> alllist(@RequestParam(value = "brandCode", required = false) String brandCode) {
        QueryWrapper<SysDepart> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysDepart::getDelFlag, 0);
        wrapper.lambda().eq(SysDepart::getStatus, 1);
        if (StrUtil.isNotBlank(brandCode)) {
            wrapper.lambda().like(SysDepart::getBrandCode, brandCode);
        }
        List<SysDepart> pageList = sysDepartService.list(wrapper);
        return Result.OK(pageList);
    }


    @ApiOperation(value = "全部渠道", notes = "全部渠道")
    @RequestMapping(value = "/allChannels", method = RequestMethod.GET)
    public Result<?> allChannels(@RequestParam(name = "brandCode", required = false) String brandCode) {
        return channelCategoryService.getAllChannels(brandCode);
    }


    @ApiOperation(value = "业务、质量标签", notes = "业务、质量标签")
    @RequestMapping(value = "/allTopics", method = RequestMethod.GET)
    public Result<List<VocBusinessTagVo>> allTopics(@RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<List<VocBusinessTagVo>> result = new Result<List<VocBusinessTagVo>>();
        List<VocBusinessTagVo> ls = null;
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            Set<Object> permissionSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, brandCode, loginUser.getId()));
            List<String> roleTagCodes = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());
            List<VocBusinessTagVo> alltags = vocBusinessTagService.getAllTags(roleTagCodes);
            List<VocBusinessTagVo> tag1 = alltags.stream().filter(s -> s.getPid().equals("0")).collect(Collectors.toList());
            Map<String, List<VocBusinessTagVo>> tagMap = alltags.stream()
                    .collect(Collectors.groupingBy(
                            VocBusinessTagVo::getPid,
                            LinkedHashMap::new,  // 使用 LinkedHashMap 保持顺序
                            Collectors.mapping(
                                    tag -> tag,
                                    Collectors.toList()
                            )
                    ));
            List<VocBusinessTagVo> existing = new ArrayList<>();
            tag1.forEach(e -> {
                if (CollectionUtil.isEmpty(tagMap.get(e.getId()))) {
                    existing.add(e);
                } else {
                    List<VocBusinessTagVo> seouns = tagMap.get(e.getId());
                    seouns.sort(Comparator.comparing(VocBusinessTag::getOrderBy));
                    e.setChildes(seouns);
                }
            });
            tag1.removeAll(existing);
            result.setSuccess(true);
            result.setResult(tag1);
            log.info(result.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

        return result;
    }

    @ApiOperation(value = "业务标签", notes = "业务标签")
    @RequestMapping(value = "/topicsBusiness", method = RequestMethod.GET)
    public Result<List<VocBusinessTagVo>> topicsBusiness(@RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<List<VocBusinessTagVo>> result = new Result<List<VocBusinessTagVo>>();
        List<VocBusinessTagVo> ls = null;
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            Set<Object> permissionSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG, brandCode, loginUser.getId()));
            List<String> roleTagCodes = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());
            QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(VocBusinessTag::getPid, "0");
            wrapper.lambda().eq(VocBusinessTag::isEnable, true);
            wrapper.lambda().ne(VocBusinessTag::getTagScope, "2");
            wrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
            List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
            ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
            ls = ls.stream().filter(s -> roleTagCodes.contains(s.getTagCode())).collect(Collectors.toList());
            ls.sort(Comparator.comparing(VocBusinessTag::getOrderBy));

            ls.forEach(e -> {
                QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(VocBusinessTag::getPid, e.getId());
                queryWrapper.lambda().eq(VocBusinessTag::isEnable, true);
                queryWrapper.lambda().ne(VocBusinessTag::getTagScope, "2");
                List<VocBusinessTag> tags2 = vocBusinessTagService.list(queryWrapper);
                tags2 = tags2.stream().filter(s -> roleTagCodes.contains(s.getTagCode())).collect(Collectors.toList());
                List<VocBusinessTagVo> seouns = BeanUtil.copyToList(tags2, VocBusinessTagVo.class);
                seouns.sort(Comparator.comparing(VocBusinessTag::getOrderBy));
                e.setChildes(seouns);
            });
            result.setSuccess(true);
            result.setResult(ls);
            log.info(result.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

        return result;
    }

    @Autowired
    IFaultProblemService faultProblemService;
    @Autowired
    SysUserRoleMapper sysUserRoleMapper;

    boolean getQuality(String brandCode) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (StringUtils.isNotBlank(qualityText)) {
            com.alibaba.fastjson.JSONObject object = com.alibaba.fastjson.JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @ApiOperation(value = "质量标签", notes = "质量标签")
    @RequestMapping(value = "/allQualitys", method = RequestMethod.GET)
    public Result<List<FaultProblemTreeVo>> allQualitys(@RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<List<FaultProblemTreeVo>> result = new Result<List<FaultProblemTreeVo>>();
        List<FaultProblemTreeVo> ls;
        try {
            result.setSuccess(true);

            boolean quality = getQuality(brandCode);
            if (!quality) {
                return result;
            }
            QueryWrapper<FaultProblem> wrapper = new QueryWrapper<>();
            wrapper.last("where pid=0 and enable=1 ");
            List<FaultProblem> tags = faultProblemService.list(wrapper);
            ls = BeanUtil.copyToList(tags, FaultProblemTreeVo.class);
            ls.forEach(e -> {
                QueryWrapper<FaultProblem> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(FaultProblem::getPid, e.getId());
                queryWrapper.lambda().eq(FaultProblem::isEnable, true);
                List<FaultProblem> tags2 = faultProblemService.list(queryWrapper);
                e.setChildes(BeanUtil.copyToList(tags2, FaultProblemTreeVo.class));
            });

            result.setResult(ls);
            log.info(result.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

        return result;
    }

    @Autowired
    IModelGroupService modelGroupService;
    @Autowired
    IModelGroupRelationService modelGroupRelationService;
    @Autowired
    IBrandProductManagerService brandProductManagerService;


    @ApiOperation(value = "全部品牌", notes = "全部品牌")
    @RequestMapping(value = "/allBrands", method = RequestMethod.GET)
    public Result<List<BrandProductManager>> allBrands() {
        QueryWrapper<BrandProductManager> query = new QueryWrapper<>();
        query.lambda().eq(BrandProductManager::getPId, 0);
        List<BrandProductManager> pageList = brandProductManagerService.list(query);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "全部车系", notes = "全部车系根据brandCode")
    @RequestMapping(value = "/allSeriesByBrandCode", method = RequestMethod.GET)
    public Result<List<BrandProductManagerDto>> allSeriesByBrandCode(@RequestParam(name = "brandCode", required = true) String brandCode) {
        Set<BrandProductManagerDto> res = brandProductManagerService.brandSeriesAll(brandCode).stream()
                .filter(s -> !s.getCode().equals(brandCode))
                .collect(Collectors.toSet());
        return Result.OK(new ArrayList<>(res));
    }


    @ApiOperation(value = "全部车系", notes = "全部车系")
    @RequestMapping(value = "/allSeries", method = RequestMethod.GET)
    public Result<List<Object>> allSeries(@RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<List<Object>> result = new Result<>();
        List<Object> json = new ArrayList<>();
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            Set<Object> userSeriesPermissions = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_SERIES, brandCode, loginUser.getId()));
            if (CollectionUtils.isEmpty(userSeriesPermissions)) {
                result.setSuccess(true);
                result.setResult(json);
                return result;
            }
            List<String> allowedSeriesCodes = userSeriesPermissions.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            if (StrUtil.isBlank(brandCode)) {
                brandCode = "0";
            }
            QueryWrapper<BrandProductManager> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .orderByAsc(BrandProductManager::getSortNo)
                    .ne(BrandProductManager::getPId, 0)
                    .eq(BrandProductManager::getEnable, 1)
                    .in(BrandProductManager::getCode, allowedSeriesCodes);
            List<BrandProductManager> brandProductManagers = brandProductManagerService.list(wrapper);

            Optional<BrandProductManager> otherManager = brandProductManagers.stream()
                    .filter(m -> "其他".equals(m.getName()))
                    .findFirst();
            if (otherManager.isPresent()) {
                brandProductManagers.remove(otherManager.get());
                brandProductManagers.add(otherManager.get());
            }

            JSONObject modelGroupJson = new JSONObject();
            modelGroupJson.putOpt("childes", brandProductManagers);
            modelGroupJson.putOpt("name", "全部车系");
            modelGroupJson.putOpt("code", "all-vehicle-series");
            json.add(modelGroupJson);

            List<DictVo> carclass = sysDictService.queryDictItemsByCode("car_type");
            List<DictVo> energytypes = sysDictService.queryDictItemsByCode("energy_type");

            List<CarSeriesCategoryVo> carTypeCategories = buildCategoryList("car_type", carclass, brandProductManagers, allowedSeriesCodes);
            List<CarSeriesCategoryVo> energyTypeCategories = buildCategoryList("energy_type", energytypes, brandProductManagers, allowedSeriesCodes);

            JSONObject carClassificationJson = new JSONObject();
            carClassificationJson.putOpt("childes", carTypeCategories);
            carClassificationJson.putOpt("typeLabel", true);
            carClassificationJson.putOpt("name", "车辆类型");
            json.add(carClassificationJson);

            JSONObject energyTypesClassJson = new JSONObject();
            energyTypesClassJson.putOpt("childes", energyTypeCategories);
            energyTypesClassJson.putOpt("typeLabel", true);
            energyTypesClassJson.putOpt("name", "能源类型");
            json.add(energyTypesClassJson);

            result.setSuccess(true);
            result.setResult(json);
            log.info("操作成功: {}", result);
        } catch (Exception e) {
            log.error("操作失败", e);
            result.error500("操作失败");
            return result;
        }
        return result;
    }

    private List<CarSeriesCategoryVo> buildCategoryList(String type, List<DictVo> dictVos, List<BrandProductManager> brandProductManagers, List<String> allowedSeriesCodes) {
        List<CarSeriesCategoryVo> categories = new ArrayList<>();
        for (DictVo dictVo : dictVos) {
            CarSeriesCategoryVo categoryVo = new CarSeriesCategoryVo();
            categoryVo.setName(dictVo.getText());
            categoryVo.setCode(dictVo.getValue());
            List<BrandProductManager> filteredBrandProductManagers = brandProductManagers.stream()
                    .filter(m -> "car_type".equals(type) ? dictVo.getText().equals(m.getCarType()) : dictVo.getText().equals(m.getEnergyType()))
                    .collect(Collectors.toList());

            List<CarSeriesCategoryVo> categoryChildes = BeanUtil.copyToList(filteredBrandProductManagers, CarSeriesCategoryVo.class);

            if (!allowedSeriesCodes.isEmpty()) {
                categoryChildes = categoryChildes.stream()
                        .filter(c -> allowedSeriesCodes.contains(c.getCode()))
                        .collect(Collectors.toList());
            }

            categoryVo.setChildes(categoryChildes);

            if (!categoryChildes.isEmpty()) {
                categories.add(categoryVo);
            }
        }
        return categories;
    }


    @ApiOperation(value = "全部用户", notes = "全部用户")
    @RequestMapping(value = "/allUsers", method = RequestMethod.GET)
    public Result<List<DictVo>> allUsers() {
        Result<List<DictVo>> result = new Result<List<DictVo>>();
        List<DictVo> ls = new ArrayList<>();
        try {
            List<DictVo> cpd = sysDictService.queryDictItemsLanguageByCode("all_users");
            DictVo vo = new DictVo();
            vo.setText("CDP_user");
            vo.setValue("1");
            vo.setChildes(cpd);
            ls.add(vo);
            DictVo nvo = new DictVo();
            nvo.setValue("0");
            nvo.setText("not_CDP_user");
            ls.add(nvo);
            result.setSuccess(true);
            result.setResult(ls);
            log.info(result.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

        return result;
    }

    @Resource
    IProvinceAreaService IProvinceAreaService;
    @Resource
    VocBrandRegionService vocBrandRegionService;

    @ApiOperation(value = "全部域区", notes = "全部域区")
    @RequestMapping(value = "/allDomains", method = RequestMethod.GET)
    public Result<?> allDomains(@RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<?> result = new Result<>();
        List<DictVo> ls = new ArrayList<>();
        List<RegionalDictVo> ls1;
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            SysRole sysUserRole = sysRoleService.getRoleByUserId(loginUser.getId());
            ls1 = vocBrandRegionService.queryBrandRegion(brandCode, sysUserRole.getId());
            Map<String, List<RegionalDictVo>> map = ls1.stream().collect(Collectors.groupingBy(RegionalDictVo::getType));
            if (map.size() > 1) {
                for (String s : map.keySet()) {

                    if ("1".equals(s)) {
                        List<RegionalDictVo> rels = vocBrandRegionService.setChildes(2, map.get(s));
                        if (CollectionUtil.isNotEmpty(rels)) {
                            DictVo dictVo = new DictVo();
                            dictVo.setText("销售区域");
                            dictVo.setValue("Sales_territory");
                            dictVo.setChildes(rels);
                            ls.add(dictVo);
                        }

                    } else if ("2".equals(s)) {
                        List<RegionalDictVo> rels = vocBrandRegionService.setChildes(2, map.get(s));
                        if (CollectionUtil.isNotEmpty(rels)) {
                            DictVo dictVo = new DictVo();
                            dictVo.setText("售后区域");
                            dictVo.setValue("Aftermarket_area");
                            dictVo.setChildes(rels);
                            ls.add(dictVo);
                        }
                    }
                }
                return Result.OK(ls);
            } else {
                return Result.OK(vocBrandRegionService.setChildes(3, ls1));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

    }


    @ApiOperation(value = "业务/质量标签", notes = "业务/质量标签")
    @RequestMapping(value = "/labelManagement", method = RequestMethod.GET)
    public Result<?> getLabelManagement(@RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<List<DictVo>> result = new Result<List<DictVo>>();
        List<DictVo> ls = new ArrayList<>();
        try {

            DictVo dictVo = new DictVo();
            Result<List<VocBusinessTagVo>> listResult = allTopics(brandCode);
            if (ObjectUtils.isNotEmpty(listResult.getResult())) {
                dictVo.setText("业务标签");
                dictVo.setValue("1");
                ls.add(dictVo);
            }
            DictVo qualityDictVo = new DictVo();
            boolean quality = getQuality(brandCode);
            if (quality) {
                qualityDictVo.setText("质量问题");
                qualityDictVo.setValue("2");
                ls.add(qualityDictVo);
            }
            result.setSuccess(true);
            result.setResult(ls);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

        return result;
    }

    @Autowired
    VocProjectGroupService vocProjectGroupService;

    /**
     * 项目组和人员下拉框
     */
    @ApiOperation(value = "项目组和人员下拉框", notes = "项目组和人员下拉框")
    @RequestMapping(value = "/projectGroupAndMember", method = RequestMethod.GET)
    public Result<List<VocProjectGroup>> projectGroupAndMember(@RequestParam(name = "brandCode", required = true) String brandCode) {
        List<VocProjectGroup> res = vocProjectGroupService.projectGroupAndMember(brandCode).stream()
                .filter(VocProjectGroup::getStatus)
                .collect(Collectors.toList());
        return Result.OK(res);
    }

    @ApiOperation(value = "项目组人员下拉框", notes = "项目组和人员下拉框")
    @RequestMapping(value = "/projectMembers", method = RequestMethod.GET)
    public Result<List<VocProjectGroupMember>> projectMembers(@RequestParam(name = "projectId") String projectId) {
        List<VocProjectGroupMember> res = vocProjectGroupService.projectMembers(projectId);
        return Result.OK(res);
    }

    /**
     * 项目组和人员下拉框
     */
    @ApiOperation(value = "部门和项目组", notes = "部门和项目组")
    @RequestMapping(value = "/departAndProjectGroup", method = RequestMethod.GET)
    public Result<List<DictVo>> departAndProjectGroup(@RequestParam(name = "brandCode", required = true) String brandCode) {
        QueryWrapper<SysDepart> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysDepart::getDelFlag, 0)
                .eq(SysDepart::getStatus, 1)
                .like(SysDepart::getBrandCode, brandCode);
        List<SysDepart> list = sysDepartService.list(queryWrapper);
        List<DictVo> depart = list.stream().map(s -> {
            DictVo dictVo = new DictVo();
            dictVo.setText(s.getDepartName());
            dictVo.setValue(s.getId());
            return dictVo;
        }).collect(Collectors.toList());
        QueryWrapper<VocProjectGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(VocProjectGroup::getDelFlag, 0)
                .eq(VocProjectGroup::getStatus, 1)
                .like(VocProjectGroup::getBrandCode, brandCode);
        List<VocProjectGroup> projectGroups = vocProjectGroupService.list(wrapper);
        List<DictVo> projects = projectGroups.stream().map(s -> {
            DictVo dictVo = new DictVo();
            dictVo.setText(s.getProjectName());
            dictVo.setValue(s.getId());
            return dictVo;
        }).collect(Collectors.toList());
        List<DictVo> map = new ArrayList<>();
        map.add(new DictVo("1", "部门", depart));
        map.add(new DictVo("2", "项目组", projects));
        return Result.OK(map);
    }

    @ApiOperation(value = "部门和项目组和人员", notes = "部门和项目组")
    @RequestMapping(value = "/departAndProjectAndMember", method = RequestMethod.GET)
    public Result<List<DictVo>> departAndProjectAndMember(@RequestParam(name = "brandCode", required = true) String brandCode) {
        List<DictVo> result = new ArrayList<>();
        // 构建部门选择树
        DictVo departRoot = new DictVo();
        departRoot.setValue("1");
        departRoot.setText("部门");
        departRoot.setChildes(sysDepartService.buildDepartTree(brandCode));
        result.add(departRoot);
        // 构建项目组选择树
        DictVo projectRoot = new DictVo();
        projectRoot.setValue("2");
        projectRoot.setText("项目组");
        projectRoot.setChildes(vocProjectGroupService.buildProjectTree(brandCode));
        result.add(projectRoot);
        return Result.OK(result);
    }
}
