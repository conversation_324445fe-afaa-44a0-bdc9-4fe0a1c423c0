package com.car.voc.stats.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.*;
import com.car.stats.serivce.DwdVocSentenceService;
import com.car.stats.serivce.ViewLabelDetailsService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.serivce.wo.BusinessOpportunityService;
import com.car.stats.serivce.wo.VehicleRescueService;
import com.car.stats.serivce.wo.WoBaseOriginalDataService;
import com.car.stats.vo.ContentVo;
import com.car.stats.vo.SoundContentQueryVo;
import com.car.stats.vo.SoundContentVo;
import com.car.stats.vo.popvo.PopUpVo;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.stats.vo.wo.WoBusinessVo;
import com.car.stats.vo.wo.WoLastDealVo;
import com.car.stats.vo.wo.WoOrderBaseVo;
import com.car.stats.vo.wo.WoRescueVo;
import com.car.voc.cach.BaseDataCache;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.constant.Constants;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.dto.TagCacheDto;
import com.car.voc.service.ISysDictService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.SpreadsheetVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * voc-公共查看标签详情
 * @version 1.0.0
 * @ClassName ViewLabelDetailsController.java
 * @Description 公共查看标签详情
 * @createTime 2022年10月30日 20:27
 * @Copyright voc
 */
@Api(tags = "voc-公共查看标签详情")
@RestController
@RequestMapping("/stats/labelDetails")
@Slf4j
public class ViewLabelDetailsController {
    @Autowired
    ViewLabelDetailsService labelDetailsService;
    @Autowired
    DwdVocSentenceService dwdVocSentenceService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private VehicleRescueService vehicleRescueService;
    @Autowired
    private BusinessOpportunityService businessOpportunityService;

    @Autowired
    private WoBaseOriginalDataService woBaseOriginalDataService;


    @ApiOperation(value = "运营管理(原数据查询)")
    @PostMapping(value = "/allSourceDataQuery")
    public Result<?> allSourceDataQuery(@RequestBody LabelDetailFilterModel model) {
        Page<ContentVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        model.setExcel(true);
        model.setMenuName("allDataQuery");
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.allSourceDataQuery(model, page);
    }

//    @ApiOperation(value = "运营管理(原数据查询)导出")
//    @PostMapping(value = "/allSourceDataExportXls")
//    public void allSourceDataExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        if (model.getIds() != null && model.getIds().size() > 0) {
//            model.setPageNo(1);
//        }
//        String size = sysDictService.queryDictValueByKey(CommonConstant.sys_download_limit, CommonConstant.original_download_volume);
//        Page<ContentVo> page = new Page<>(0, StrUtil.isBlankIfStr(size) ? 50000 : Long.parseLong(size));
//        List<String> esIndexs = sentenceVocService.queryIndexByChannelFilter(model);
//        IPage<ContentVo> pas = sentenceVocService.allSourceDataQuery(model, page, esIndexs);
//        excelExportXls(pas.getRecords(), "原数据", response, ContentVo.class);
//
//    }


    @ApiOperation(value = "运营管理(原数据查询)导出")
    @PostMapping(value = "/allSourceDataExportXls")
    public Result<?> allSourceDataExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        if (model.getIds() != null && model.getIds().size() > 0) {
            model.setPageNo(1);
        }
        Page<ContentVo> page = new Page<>(0, 10000);
        List<String> esIndexs = sentenceVocService.queryIndexByChannelFilter(model);
        labelDetailsService.asyncAllSourceDataQuery(model, page, esIndexs, request);
        return Result.OK();
    }


    @ApiOperation(value = "运营管理(原数据各项占比)")
    @PostMapping(value = "/allSourceGroupByTag")
    public Result<?> allSourceGroupByTag(@RequestBody LabelDetailFilterModel model) {
        model.setExcel(true);
        model.setMenuName("allDataQuery");
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        model.setTag(null);
        return labelDetailsService.allSourceGroupByTag(model);
    }


    @ApiOperation(value = "运营管理(片段数据查询)")
    @PostMapping(value = "/allDataQuery")
    public Result<?> allDataQuery(@RequestBody LabelDetailFilterModel model) {
        Page<SoundContentQueryVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        model.setExcel(true);
        model.setMenuName("allDataQuery");
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.allDataQuery(model, page);
    }


    @ApiOperation(value = "运营管理(片段数据查询)导出")
    @PostMapping(value = "/allDataQueryExportXls")
    public Result<?> allDataQueryExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        if (model.getIds() != null && model.getIds().size() > 0) {
            model.setPageNo(1);
        }
        Page<SoundContentQueryVo> page = new Page<>(0, 10000);
        model.setExcel(true);
        model.setMenuName("allDataQuery");
        labelDetailsService.allDataQueryNew(model, page);
        return Result.OK();
    }

    @ApiOperation(value = "业务指标详情(声音列表)")
    @PostMapping(value = "/businessTagDetail")
    public Result<?> businessTagDetail(@RequestBody LabelDetailFilterModel model, HttpServletRequest request) {
        Page<SoundContentVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.businessTagDetail(model, page);
    }

    @ApiOperation(value = "提及量及所有标签点击-(声音列表)")
    @PostMapping(value = "/allTagsClickSoundsList")
    public Result<?> allTagsClickSoundsList(@RequestBody LabelDetailFilterModel model, HttpServletRequest request) {
        Page<SoundContentVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.allTagsClickSoundsList(model, page);
    }

//    @ApiOperation(value = "业务指标详情(声音列表)导出")
//    @PostMapping(value = "/businessTagDetailExportXls")
//    public void businessTagDetailExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        String size = sysDictService.queryDictValueByKey(CommonConstant.sys_download_limit, CommonConstant.sound_download_volume);
//        Page<SoundContentVo> page = new Page<>(0, StrUtil.isBlankIfStr(size) ? 50000 : Long.parseLong(size));
//        JSONObject jsonObject = JSONObject.parseObject(labelDetailsService.businessTagDetail(model, page).getResult().toString());
//        Object o1 = JSONObject.parseObject(jsonObject.get("soundList").toString()).get("records");
//        List<SoundContentVo> vos = JSONArray.parseArray(o1.toString(), SoundContentVo.class);
//        excelExportXls(vos, "声音列表", response, SoundContentVo.class);
//    }

    @ApiOperation(value = "业务指标详情(声音列表)导出")
    @PostMapping(value = "/businessTagDetailExportXls")
    public Result<?> businessTagDetailExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        Page<SoundContentVo> page = new Page<>(0, 10000);
        labelDetailsService.businessTagDetailNew(model, page, request);
        return Result.OK();

    }

//    @ApiOperation(value = "提及量及所有标签点击(声音列表)导出")
//    @PostMapping(value = "/allTagsClickSoundsListExportXls")
//    public void allTagsClickSoundsListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        String size = sysDictService.queryDictValueByKey(CommonConstant.sys_download_limit, CommonConstant.sound_download_volume);
//        Page<SoundContentVo> page = new Page<>(0, StrUtil.isBlankIfStr(size) ? 50000 : Long.parseLong(size));
//        JSONObject jsonObject = JSONObject.parseObject(labelDetailsService.allTagsClickSoundsList(model, page).getResult().toString());
//        Object o1 = JSONObject.parseObject(jsonObject.get("soundList").toString()).get("records");
//        List<SoundContentVo> vos = JSONArray.parseArray(o1.toString(), SoundContentVo.class);
//        excelExportXls(vos, "声音列表", response, SoundContentVo.class);
//    }


    @ApiOperation(value = "提及量及所有标签点击(声音列表)导出")
    @PostMapping(value = "/allTagsClickSoundsListExportXls")
    public Result<?> allTagsClickSoundsListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        Page<SoundContentVo> page = new Page<>(0, 10000);
        SpringContextUtils.setHttpServletRequest(request);
        labelDetailsService.allTagsClickSoundsListNew(model, page, request);
        return Result.OK();
    }


    @ApiOperation(value = "voc总览领导层所有标签-(提及量--用户数)")
    @PostMapping(value = "/allTags-title")
    public Result<?> allTagsTitle(@RequestBody LabelDetailFilterModel model) {
        return dwdVocSentenceService.allTagsTitle(model);
    }

    @ApiOperation(value = "业务指标详情(提及量--用户数)")
    @PostMapping(value = "/businessTagDetail-title")
    public Result<?> businessTagDetailTitle(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.businessTagDetailTitle(model);
    }

    @ApiOperation(value = "业务指标详情(聚焦分布)")
    @PostMapping(value = "/businessTagDetail-focus")
    public Result<?> businessTagDetailFocus(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.businessTagDetailFocus(model);
    }

    @ApiOperation(value = "业务指标详情(趋势变化)")
    @PostMapping(value = "/businessTagDetail-trend")
    public Result<?> businessTagDetailTrend(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.businessTagDetailTrend(model);
    }

    @ApiOperation(value = "所有标签(趋势变化)")
    @PostMapping(value = "/allTags-trend")
    public Result<?> allTagsTrend(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        model.setNumCycles(13);
        return labelDetailsService.allTagsTrend(model);
    }

    @ApiOperation(value = "好评/差评率(趋势变化)")
    @PostMapping(value = "/positiveNegative-trend")
    public Result<?> positiveNegativeTrend(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        model.setNumCycles(13);
        model.setTagType(1);
        return labelDetailsService.positiveNegativeTrend(model);
    }

    @ApiOperation(value = "好评/差评率(趋势变化)")
    @PostMapping(value = "/allpositiveNegative-trend")
    public Result<?> allPositiveNegativeTrend(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.allPositiveNegativeTrend(model);
    }


    @ApiOperation(value = "业务及产品末级标签热词")
    @PostMapping(value = "/lastTagHotWords")
    public Result<?> lastTagHotWords(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        return labelDetailsService.lastTagHotWords(model);
    }


    @ApiOperation(value = "业务指标详情(用户列表)")
    @PostMapping(value = "/businessTagUserList")
    public Result<?> businessTagUserList(@RequestBody LabelDetailFilterModel model) {
        Page<UserListInfoVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        return labelDetailsService.businessTagUserList(model, page);
    }

    @ApiOperation(value = "提及量、所有标签点击-(用户列表)")
    @PostMapping(value = "/allTagUserList")
    public Result<?> allTagUserList(@RequestBody LabelDetailFilterModel model) {
        Page<UserListInfoVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        return labelDetailsService.allTagUserList(model, page);
    }


//    @ApiOperation(value = "所有标签-(用户列表)导出")
//    @PostMapping(value = "/allTagUserListExportXls")
//    public void allTagUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        long start = System.currentTimeMillis();
//        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
//            model.setProvinceCodes(model.getProvince());
//            model.setProvince(null);
//            model.setRegion(null);
//        }
//        Page<UserListInfoVo> page = new Page<>(0, 5000);
//        JSONObject jsonObject = JSONObject.parseObject(String.valueOf(labelDetailsService.allTagUserList(model, page).getResult()));
//        List<UserListInfoVo> records = JSONArray.parseArray(jsonObject.get("records").toString(), UserListInfoVo.class);
//        long end1 = System.currentTimeMillis();
//        log.info("用户列表获取数据,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end1 - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end1 - start) + "秒" : (end1 - start) + "毫秒");
//        excelExportXls(records,
//                "用户列表",
//                response, UserListInfoVo.class);
//        long end = System.currentTimeMillis();
//        log.info("用户列表,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");
//    }


    @ApiOperation(value = "所有标签-(用户列表)导出")
    @PostMapping(value = "/allTagUserListExportXls")
    public Result<?> allTagUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        long start = System.currentTimeMillis();
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        Page<UserListInfoVo> page = new Page<>(0, 3000);
        labelDetailsService.allTagUserListNew(model, page, request);
        return Result.OK();
    }

//    @ApiOperation(value = "业务指标详情(用户列表)导出")
//    @PostMapping(value = "/businessTagUserListExportXls")
//    public void businessTagUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        long start = System.currentTimeMillis();
//        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
//            model.setProvinceCodes(model.getProvince());
//            model.setProvince(null);
//            model.setRegion(null);
//        }
//        Page<UserListInfoVo> page = new Page<>(0, 5000);
//        JSONObject jsonObject = JSONObject.parseObject(String.valueOf(labelDetailsService.businessTagUserList(model, page).getResult()));
//        List<UserListInfoVo> records = JSONArray.parseArray(jsonObject.get("records").toString(), UserListInfoVo.class);
//        long end1 = System.currentTimeMillis();
//        log.info("业务用户列表获取数据,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end1 - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end1 - start) + "秒" : (end1 - start) + "毫秒");
//        excelExportXls(records,
//                "用户列表",
//                response, UserListInfoVo.class);
//        long end = System.currentTimeMillis();
//        log.info("业务用户列表,总耗时:{}", TimeUnit.MILLISECONDS.toSeconds(end - start) > 0 ? TimeUnit.MILLISECONDS.toSeconds(end - start) + "秒" : (end - start) + "毫秒");
//    }


    @ApiOperation(value = "业务指标详情(用户列表)导出")
    @PostMapping(value = "/businessTagUserListExportXls")
    public Result<?> businessTagUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        Page<UserListInfoVo> page = new Page<>(0, 3000);
        labelDetailsService.businessTagUserListNew(model, page, request);
        return Result.OK();
    }

    @ApiOperation(value = "质量指标详情(声音列表)")
    @PostMapping(value = "/qualityTagDetail")
    public Result<?> qualityTagDetail(@RequestBody LabelDetailFilterModel model) {
        Page<SoundContentVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
            model.setFirstDimensionCode(null);
        }
        return labelDetailsService.qualityTagDetail(model, page);
    }


//    @ApiOperation(value = "质量指标详情(质量声音列表)导出")
//    @PostMapping(value = "/qualityTagDetailExportXls")
//    public void qualityTagDetailExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
//            model.setFirstDimensionCode(null);
//        }
//        String size = sysDictService.queryDictValueByKey(CommonConstant.sys_download_limit, CommonConstant.sound_download_volume);
//        Page<SoundContentVo> page = new Page<>(0, StrUtil.isBlankIfStr(size) ? 50000 : Long.parseLong(size));
//        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
//            model.setProvinceCodes(model.getProvince());
//            model.setProvince(null);
//            model.setRegion(null);
//        }
//        JSONObject jsonObject = JSONObject.parseObject(labelDetailsService.qualityTagDetail(model, page).getResult().toString());
//        Object o1 = JSONObject.parseObject(jsonObject.get("soundList").toString()).get("records");
//        List<SoundContentVo> vos = JSONArray.parseArray(o1.toString(), SoundContentVo.class);
//        excelExportXls(vos,
//                "质量声音列表",
//                response, SoundContentVo.class);
//    }


    @ApiOperation(value = "质量指标详情(质量声音列表)导出")
    @PostMapping(value = "/qualityTagDetailExportXls")
    public Result<?> qualityTagDetailExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
            model.setFirstDimensionCode(null);
        }
        Page<SoundContentVo> page = new Page<>(0, 10000);
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        labelDetailsService.qualityTagDetailNew(model, page, request);
        return Result.OK();
    }

    @ApiOperation(value = "质量指标详情(用户数)")
    @PostMapping(value = "/qualityTagDetail-title")
    public Result<?> qualityTagDetailTitle(@RequestBody LabelDetailFilterModel model) {

        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        return labelDetailsService.qualityTagDetailTitle(model);
    }

    @ApiOperation(value = "质量指标详情(聚焦分布)")
    @PostMapping(value = "/qualityTagDetail-focus")
    public Result<?> qualityTagDetailFocus(@RequestBody LabelDetailFilterModel model) {
        model.setDateS(model.getStartDate());
        model.setDateE(model.getEndDate());

        return labelDetailsService.qualityTagDetailFocus(model);
    }

    @ApiOperation(value = "质量指标详情(趋势变化)")
    @PostMapping(value = "/qualityTagDetail-trend")
    public Result<?> qualityTagDetailTrend(@RequestBody LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
            model.setFirstDimensionCode(null);
        }
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        return labelDetailsService.qualityTagDetailTrend(model);
    }

    @ApiOperation(value = "质量指标详情(用户列表)")
    @PostMapping(value = "/qualityTagUserList")
    public Result<?> qualityTagUserList(@RequestBody LabelDetailFilterModel model) {
        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
            model.setFirstDimensionCode(null);
        }
        Page<UserListInfoVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        if (StrUtil.isNotBlank(model.getTopic())) {
            model.setTopicCode(null);
        }
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        return labelDetailsService.qualityTagUserList(model, page);
    }

//    @ApiOperation(value = "质量指标详情(用户列表)导出")
//    @PostMapping(value = "/qualityTagUserListExportXls")
//    public void qualityTagUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
//        Page<UserListInfoVo> page = new Page<>(0, 5000);
//        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
//            model.setFirstDimensionCode(null);
//        }
//        if (StrUtil.isNotBlank(model.getTopic())) {
//            model.setTopicCode(null);
//        }
//        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
//            model.setProvinceCodes(model.getProvince());
//            model.setProvince(null);
//            model.setRegion(null);
//        }
//        JSONObject jsonObject = JSONObject.parseObject(labelDetailsService.qualityTagUserList(model, page).getResult().toString());
//        List<UserListInfoVo> records = JSONArray.parseArray(jsonObject.get("records").toString(), UserListInfoVo.class);
//        excelExportXls(records,
//                "用户列表",
//                response, UserListInfoVo.class);
//    }

    @ApiOperation(value = "质量指标详情(用户列表)导出")
    @PostMapping(value = "/qualityTagUserListExportXls")
    public Result<?> qualityTagUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) {
        Page<UserListInfoVo> page = new Page<>(0, 3000);
        if (StrUtil.isNotBlank(model.getFirstDimensionCode()) && model.getFirstDimensionCode().equals(Constants.Q0001)) {
            model.setFirstDimensionCode(null);
        }
        if (StrUtil.isNotBlank(model.getTopic())) {
            model.setTopicCode(null);
        }
        if (StrUtil.isNotBlank(model.getMenuName()) && "feedbackAnalysis".equals(model.getMenuName())) {
            model.setProvinceCodes(model.getProvince());
            model.setProvince(null);
            model.setRegion(null);
        }
        labelDetailsService.qualityTagUserListNew(model, page,request);
        return Result.OK();
    }

    @ApiOperation(value = "用户详情")
    @PostMapping(value = "/userDetails")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", defaultValue = "6600000001337564", example = "6600000001337564", required = true, dataType = "String"),
    })
    public Result<?> userDetails(@RequestBody LabelDetailFilterModel model) {
        return labelDetailsService.userDetails(model);
    }

    @ApiOperation(value = "声音详情")
    @GetMapping(value = "/soundDetails")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "声音id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "indexId", value = "原文索引", required = false, dataType = "String"),
            @ApiImplicitParam(name = "contentId", value = "内容Id", defaultValue = "1089676134100590592", example = "1089676134100590592", required = false, dataType = "String"),
            @ApiImplicitParam(name = "channelId", value = "渠道Id", defaultValue = "1356178730703224801", example = "1356178730703224801", required = false, dataType = "String")
    })
    public Result<?> soundDetails(String contentId, String channelId, String id, String indexId) {
        return labelDetailsService.soundDetails(contentId, channelId, indexId, id);
    }

    @ApiOperation(value = "简报数值(用户列表)")
    @PostMapping(value = "/briefingValueUserList")
    public Result<?> briefingValueUserList(@RequestBody LabelDetailFilterModel model) {
        Page<UserListInfoVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        return briefingValue(model, page);

    }

    private Result<?> briefingValue(LabelDetailFilterModel model, Page<UserListInfoVo> page) {

        if (model.getMenuName() != null) {
            if ("dashBoard".equals(model.getMenuName()) || "focus".equals(model.getMenuName())) {
                return labelDetailsService.businessTagUserList(model, page);
            } else if ("userConsultation".equals(model.getMenuName())) {
                model.setIntention("咨询");

                return labelDetailsService.businessTagUserList(model, page);
            } else if ("userComplaints".equals(model.getMenuName())) {
                model.setIntention("投诉");
                return labelDetailsService.businessTagUserList(model, page);
            } else if ("userSuggest".equals(model.getMenuName())) {
                model.setIntention("建议");
                return labelDetailsService.businessTagUserList(model, page);
            } else if ("productQuality".equals(model.getMenuName())) {
                return labelDetailsService.qualityTagUserList(model, page);
            } else {
                return labelDetailsService.businessTagUserList(model, page);

            }
        } else {
            List<String> chs = new ArrayList<>();
            chs.add("Customer-data");
            model.setDataSources(chs);
            return labelDetailsService.businessTagUserList(model, page);
        }
    }

    @ApiOperation(value = "简报数值(用户列表)导出")
    @PostMapping(value = "/briefingValueUserListExportXls")
    public void briefingValueUserListExportXls(@RequestBody LabelDetailFilterModel model, HttpServletResponse response, HttpServletRequest request) throws JsonProcessingException {
        Page<UserListInfoVo> page = new Page<>(0, 50000);
//        JSONObject jsonObject = JSONObject.parseObject(briefingValue(model, page).getResult().toString());
//        List<UserListInfoVo> records = JSONArray.parseArray(jsonObject.get("records").toString(), UserListInfoVo.class);
        Page<UserListInfoVo> o1 = (Page<UserListInfoVo>) briefingValue(model, page).getResult();
        ObjectMapper objectMapper = new ObjectMapper();
        String s = objectMapper.writeValueAsString(o1);
        Page<UserListInfoVo> popUpVo = objectMapper.readValue(s, new TypeReference<Page<UserListInfoVo>>(){});
        List<UserListInfoVo> records = popUpVo.getRecords();
        excelExportXls(records,
                "用户列表",
                response, UserListInfoVo.class);
    }

    @ApiOperation(value = "用户意图跟踪")
    @PostMapping(value = "/intentionTrack")
    public Result<?> intentionTrack(@RequestBody IntentionTrackModel model) {
        return labelDetailsService.intentionTrack(model);
    }

    @ApiOperation(value = "用户数据轨迹")
    @PostMapping(value = "/userDataTrack")
    public Result<?> userDataTrack(@RequestBody IntentionTrackModel model) {
        return labelDetailsService.userDataTrack(model);
    }


    @ApiOperation(value = "用户意图跟踪与数据轨迹统计")
    @PostMapping(value = "/userIntentionAndTrajectory")
    public Result<?> userIntentionAndTrajectory(@RequestBody UserIntentionTrajectoryModel model) {
        return labelDetailsService.userIntentionAndTrajectory(model);
    }

    /**
     * 初始化 cell 内容长度
     * cell 原本内容长度限制 32767  现修改为Integer.MAX_VALUE
     */
    public static void initCellMaxTextLength() {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void excelExportXls(List records, String filename, HttpServletResponse response, Class head) {
        response.setCharacterEncoding("utf-8");
        String fileName;
        // 导出数据前调用
        initCellMaxTextLength();
        try {
            fileName = URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setContentType("application/vnd.ms-excel");

            EasyExcel.write(response.getOutputStream(), head).excelType(ExcelTypeEnum.XLSX).sheet(filename).doWrite(records);

        } catch (IOException e) {
            e.printStackTrace();
        }


    }

    // ===================  工单数据 =========================
    @ApiOperation(value = "工单列表")
    @PostMapping(value = "/workOrderDetailsList")
    public Result<Page<?>> workOrderDetailsList(@RequestBody FilterCriteriaModel model) {
        Page<?> result = new Page<>();
        if (StrUtil.isNotBlank(model.getWoType()) && StrUtil.isBlank(model.getWoFlow())) {
            if ("救援".equals(model.getWoType())) {
                result = vehicleRescueService.workOrderDetailsList(model);
            } else if ("商机".equals(model.getWoType())) {
                result = businessOpportunityService.woOrderList(model);
            }
        } else {
            model.setWoFlow("1");
            result = woBaseOriginalDataService.woList(model);
        }
        return Result.OK(result);
    }

    @ApiOperation(value = "工单列表导出", produces = "application/octet-stream")
    @PostMapping(value = "/workOrderDetailsListExportXls")
    public void qualityTagUserListExportXls(@RequestBody FilterCriteriaModel model, HttpServletResponse response, HttpServletRequest request) throws JsonProcessingException {
        model.setPageSize(50000);
        Page<?> result = new Page<>();
        if (StrUtil.isNotBlank(model.getWoType()) && StrUtil.isBlank(model.getWoFlow())) {
            if ("救援".equals(model.getWoType())) {
                result = vehicleRescueService.workOrderDetailsList(model);
                String s = new ObjectMapper().writeValueAsString(result);
                Page<WoRescueVo> popUpVo = new ObjectMapper().readValue(s, new TypeReference<Page<WoRescueVo>>() {});
                List<?> records = popUpVo.getRecords();
                tagCacheMapping((List<WoOrderBaseVo>) records);
                excelExportXls(records, "车辆救援工单列表", response, WoRescueVo.class);
            } else if ("商机".equals(model.getWoType())) {
                result = businessOpportunityService.woOrderList(model);
                String s = new ObjectMapper().writeValueAsString(result);
                Page<WoBusinessVo> popUpVo = new ObjectMapper().readValue(s, new TypeReference<Page<WoBusinessVo>>() {});
                List<?> records = popUpVo.getRecords();
                tagCacheMapping((List<WoOrderBaseVo>) records);
                excelExportXls(records, "商机线索工单列表", response, WoBusinessVo.class);
            }
        } else {
            model.setWoFlow("1");
            result = woBaseOriginalDataService.woList(model);
            String s = new ObjectMapper().writeValueAsString(result);
            Page<WoLastDealVo> popUpVo = new ObjectMapper().readValue(s, new TypeReference<Page<WoLastDealVo>>() {});
            List<?> records = popUpVo.getRecords();
            tagCacheMapping((List<WoOrderBaseVo>) records);
            excelExportXls(records, "后处理工单列表", response, WoLastDealVo.class);
        }
    }

    private void tagCacheMapping(List<WoOrderBaseVo> records) {
        BaseDataCache cache = (BaseDataCache) SpringContextUtils.getBean("baseDataCache");
        Set<TagCacheDto> tagCacheDtos = cache.tagAll();

        // 转换tagCacheDtos为Map，减少每次查找时的性能消耗
        Map<String, String> codeToNameMap = tagCacheDtos.stream()
                .collect(Collectors.toMap(TagCacheDto::getCode, TagCacheDto::getName));

        // 遍历记录并更新相关字段
        records.forEach(record -> {
            WoOrderBaseVo vo = (WoOrderBaseVo) record;
            vo.setFirstDimensionCode(codeToNameMap.getOrDefault(vo.getFirstDimensionCode(), null));
            vo.setSecondDimensionCode(codeToNameMap.getOrDefault(vo.getSecondDimensionCode(), null));
            vo.setThreeDimensionCode(codeToNameMap.getOrDefault(vo.getThreeDimensionCode(), null));
            vo.setTopicCode(codeToNameMap.getOrDefault(vo.getTopicCode(), null));
        });
    }

    @ApiOperation(value = "标签分布")
    @PostMapping(value = "/allTagDist")
    public Result<?> allTagDist(@RequestBody FilterCriteriaModel model) {
        return dwdVocSentenceService.allTagDist(model);
    }

    @ApiOperation(value = "高频热词")
    @PostMapping(value = "/hotWords")
    public Result<?> hotWords(@RequestBody FilterCriteriaModel model) {
        return dwdVocSentenceService.hotWords(model);
    }

    @ApiOperation(value = "工单数、工单用户数")
    @PostMapping(value = "/woTitle")
    public Result<PopUpVo> woTitle(@RequestBody FilterCriteriaModel model) {
        PopUpVo re = woBaseOriginalDataService.woTitle(model);
        return Result.OK(re);
    }


    @ApiOperation(value = "获取当前用户最新生成的文件记录")
    @PostMapping("/getFile")
    public Result<?> getFile(@RequestBody LargeDigitaFilesModel insDataSourceModel) {
        try {
            LargeDigitaFilesModel file = labelDetailsService.getFile(insDataSourceModel);
            return Result.OK(file);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("请稍后再试");
        }
    }

}
