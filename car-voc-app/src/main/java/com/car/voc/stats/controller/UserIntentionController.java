package com.car.voc.stats.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.serivce.IDwsVocEmotionUserDiService;
import com.car.stats.serivce.IDwsVocIntentionDiService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.SoundContentVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * voc-声音洞察-用户意图
 * @version 1.0.0
 * @ClassName FocusSoundInsightController.java
 * @Description 声音洞察-用户意图
 * @createTime 2022年10月16日 23:49
 * @Copyright voc
 */
@Api(tags="voc-声音洞察-用户意图")
@RestController
    @RequestMapping("/stats/userIntention")
@Slf4j
public class UserIntentionController {
    @Autowired
    IDwsVocIntentionDiService iDwsVocIntentionDiService;

    @Autowired
    EsDataSentenceVocService sentenceVocService;

    @Autowired
    IDwsVocEmotionUserDiService emotionUserDiService;
    @AutoLog(value = "业务声音洞察-用户意图")
    @ApiOperation(value="简报数值")
    @PostMapping(value = "/briefingValue")
//    @Cacheable(cacheNames = "voc:api:userIntention:briefingValue", keyGenerator = "myCacheKeyGenerator")
    public Result<?> briefingValue(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.soundInsightBriefingValue(model);
    }
    @ApiOperation(value="区域分布及占比")
    @PostMapping(value = "/regionalDistribution")
    @Cacheable(cacheNames = "voc:api:userIntention:regionalDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalDistribution(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return emotionUserDiService.regionalDistribution(model);
    }

    @ApiOperation(value = "大区排行")
    @PostMapping(value = "/regionalTop")
    @Cacheable(cacheNames = "voc:api:userIntention:regionalTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> regionalTop(@RequestBody FilterCriteriaModel model) {
        return emotionUserDiService.focusRegionalTop(model);
    }
    @ApiOperation(value = "小区排行")
    @PostMapping(value = "/communityTop")
    @Cacheable(cacheNames = "voc:api:userIntention:communityTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> communityTop(@RequestBody FilterCriteriaModel model) {
        return emotionUserDiService.focusCommunityTop(model);
    }

    @ApiOperation(value="省份排行")
    @PostMapping(value = "/rankingProvinces")
    @Cacheable(cacheNames = "voc:api:userIntention:rankingProvinces", keyGenerator = "myCacheKeyGenerator")
    public Result<?> rankingProvinces(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return emotionUserDiService.rankingProvinces(model);
    }
    @ApiOperation(value = "省份地图")
    @PostMapping(value = "/provinceMap")
    @Cacheable(cacheNames = "voc:api:userIntention:provinceMap", keyGenerator = "myCacheKeyGenerator")
    public Result<?> provinceMap(@RequestBody FilterCriteriaModel model) {
        return emotionUserDiService.provinceMap(model);
    }

    @ApiOperation(value="渠道分布")
    @PostMapping(value = "/channelDistribution")
    @Cacheable(cacheNames = "voc:api:userIntention:channelDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> channelDistribution(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.channelDistribution(model);
    }

    @ApiOperation(value="意图趋势")
    @PostMapping(value = "/intentionTrends")
    @Cacheable(cacheNames = "voc:api:userIntention:intentionTrends", keyGenerator = "myCacheKeyGenerator")
    public Result<?> complainTrends(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return emotionUserDiService.intentionUserTrends(model);
    }
    @ApiOperation(value="主题分布")
    @PostMapping(value = "/themeDistribution")
    @Cacheable(cacheNames = "voc:api:userIntention:themeDistribution", keyGenerator = "myCacheKeyGenerator")
    public Result<?> themeDistribution(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.themeDistribution(model);
    }

    @ApiOperation(value="主题份额")
    @PostMapping(value = "/themeShare")
    @Cacheable(cacheNames = "voc:api:userIntention:themeShare", keyGenerator = "myCacheKeyGenerator")
    public Result<?> themeShare(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.themeShare(model);
    }

    @ApiOperation(value="Top抱怨用户")
    @PostMapping(value = "/topVoiceUsers")
    @Cacheable(cacheNames = "voc:api:userIntention:topVoiceUsers", keyGenerator = "myCacheKeyGenerator")
    public Result<?> topVoiceUsers(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return emotionUserDiService.topVoiceUsers(model);
    }

    @ApiOperation(value="高频热词")
    @PostMapping(value = "/hotWords")
    @Cacheable(cacheNames = "voc:api:userIntention:hotWords", keyGenerator = "myCacheKeyGenerator")
    public Result<?> hotWords(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.hotWords(model);
    }

    @ApiOperation(value="飙升热词")
    @PostMapping(value = "/soarHotWords")
    @Cacheable(cacheNames = "voc:api:userIntention:soarHotWords", keyGenerator = "myCacheKeyGenerator")
    public Result<?> soarHotWords(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.soarHotWords(model);
    }
    @ApiOperation(value="提及量top")
    @PostMapping(value = "/quantityTop")
    @Cacheable(cacheNames = "voc:api:userIntention:quantityTop", keyGenerator = "myCacheKeyGenerator")
    public Result<?> quantityTop(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        return iDwsVocIntentionDiService.quantityTop(model);
    }

    @ApiOperation(value="声音内容")
    @PostMapping(value = "/soundContent")
    public Result<?> soundContent(@RequestBody FilterCriteriaModel model) {
        model.setEmotion(null);
        Page<SoundContentVo> page=new Page<>(model.getPageNo(), model.getPageSize());
        return sentenceVocService.focusSoundContent(model,page);
    }

}
