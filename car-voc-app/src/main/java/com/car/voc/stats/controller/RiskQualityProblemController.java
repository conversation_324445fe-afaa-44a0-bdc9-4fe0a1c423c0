package com.car.voc.stats.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.VocOverBriefingValueVo;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.IDateUtils;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wiremock.org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * voc-质量问题风险
 * @version 1.0.0
 * @ClassName RiskEventInsightController.java
 * @Description
 * @createTime 2022年11月23日 11:42
 * @Copyright voc
 */

@Api(tags="voc-质量问题风险")
@RestController
@RequestMapping("/stats/riskQualityProblem")
@Slf4j
public class RiskQualityProblemController {
    @Autowired
    SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    IDwdVocQualityRiskService vocRiskService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @AutoLog(value = "质量声音洞察-质量风险洞察")
    @ApiOperation(value="风险点聚集")
    @PostMapping(value = "/riskPointAgg")
    public Result<?> riskPointAgg(@RequestBody RiskEventInsightModel model) {
        IPage<RiskPointAggVo> aggVoIPage=new Page<>();
        if (!getQuality(model.getBrandCode())){
            return Result.OK(aggVoIPage);
        }

        model.SetUpCycle();
        aggVoIPage=vocRiskService.riskPointAggNew(model);
        return Result.OK(aggVoIPage);
    }

    boolean getQuality(String brandCode){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (StringUtils.isNotEmpty(qualityText)&&StringUtils.isNotEmpty(brandCode)){
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)){
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @ApiOperation(value="信息列表")
    @PostMapping(value = "/riskInfoList")
    public Result<?> riskInfoList(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        return sentenceVocService.riskQualityInfoList(model);
    }
    @ApiOperation(value="数据分析-简报")
    @PostMapping(value = "/dataAnalysis-briefing")
//    @Cacheable(cacheNames = "voc:api:riskQualityProblem:dataAnalysisBriefing",keyGenerator = "myCacheKeyGenerator")
    public Result<?> dataAnalysisBriefing(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
       DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        model.setChannelId(null);
        return vocRiskService.dataAnalysisBriefing(model, risk);

    }
    @ApiOperation(value="数据分析-简报-趋势")
    @PostMapping(value = "/dataAnalysis-briefing-trend")
    public Result<?> dataAnalysisBriefingTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        return vocRiskService.dataAnalysisBriefingTrend(model);

    }

    @ApiOperation(value="数据分析-发声渠道趋势")
    @PostMapping(value = "/dataAnalysis-channelTrend")
//    @Cacheable(cacheNames = "voc:api:riskQuality:dataAnalysisChannelTrend",keyGenerator = "myCacheKeyGenerator")
    public Result<?> dataAnalysisChannelTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        setChannels(model,risk);
        return vocRiskService.dataAnalysisChannelTrend(model,risk);



    }
    private void setChannels(RiskEventInsightModel model,DwdVocQualityRisk risk) {
        List<String> chans=new ArrayList<>();
        model.setEmotion("负面");

    }
    @ApiOperation(value="数据分析-情感及意图走势")
    @PostMapping(value = "/dataAnalysis-emotionIntentionTrend")
    public Result<?> emotionIntentionTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());

        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        return vocRiskService.emotionIntentionTrend(model, risk);



    }
    @ApiOperation(value="数据分析-高频提及热词")
    @PostMapping(value = "/dataAnalysis-hotWords")
    public Result<?> hotWords(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        model.setEmotion("负面");
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),risk.getPublishDate()));
        model.setBrandCode(risk.getBrandCode());
        return vocRiskService.hotWords(model);
    }
    @ApiOperation(value="数据分析-发声用户趋势")
    @PostMapping(value = "/dataAnalysis-voiceUserTrend")
    public Result<?> voiceUserTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setEmotion("负面");
        model.setBrandCode(risk.getBrandCode());
        return vocRiskService.voiceUserTrend(model,risk);
    }
    @ApiOperation(value="数据分析-发声用户Top")
    @PostMapping(value = "/dataAnalysis-voiceUserTop")
    public Result<?> voiceUserTop(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setEmotion("负面");
        model.setBrandCode(risk.getBrandCode());
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),risk.getPublishDate()));
        return vocRiskService.voiceUserTop(model);



    }
    @ApiOperation(value="数据分析-时间节点")
    @PostMapping(value = "/dataAnalysis-timeNode")
    public Result<?> dataAnalysisTimeNode(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        return sentenceVocService.dataAnalysisTimeNodeQuality(model,risk);


    }

    @ApiOperation(value="数据分析-已警示次数")
    @PostMapping(value = "/dataAnalysis-waringNum")
    public Result<?> dataAnalysisWaringNum(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        return vocRiskService.dataAnalysisWaringNum(model,risk);
    }

}
