package com.car.voc.stats.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocQualityRisk;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.IDateUtils;
import com.car.voc.entity.SysRole;
import com.car.voc.mapper.SysUserRoleMapper;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wiremock.org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * voc-救援故障预警
 */
@Api(tags="voc-救援故障预警")
@RestController
@RequestMapping("/stats/riskRescue")
@Slf4j
public class RiskRescueController {
    @Autowired
    SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    IDwdVocQualityRiskService vocRiskService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @AutoLog(value = "救援故障预警")
    @ApiOperation(value="救援故障预警列表")
    @PostMapping(value = "/riskRescueList")
    public Result<?> riskRescueList(@RequestBody RiskEventInsightModel model) {
        IPage<RiskPointAggVo> aggVoIPage=new Page<>();
        if (!getQuality(model.getBrandCode())){
            return Result.OK(aggVoIPage);
        }
        model.SetUpCycle();
        aggVoIPage=vocRiskService.riskRescueList(model);
        return Result.OK(aggVoIPage);
    }

    boolean getQuality(String brandCode){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<SysRole> sysRoles = sysUserRoleMapper.getRoleInfoListByUserId(sysUser.getId());
        String qualityText = sysRoles.get(0).getQualityText();
        if (StringUtils.isNotEmpty(qualityText)&&StringUtils.isNotEmpty(brandCode)){
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)){
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }

    @ApiOperation(value="数据分析-简报")
    @PostMapping(value = "/dataAnalysis-briefing")
//    @Cacheable(cacheNames = "voc:api:riskRescue:dataAnalysisBriefing",keyGenerator = "myCacheKeyGenerator")
    public Result<?> dataAnalysisBriefing(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
       DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        model.setChannelId(null);
//         vocRiskService.dataAnalysisBriefing(model, risk);
        return vocRiskService.dataAnalysisBriefingRescue(model, risk);
    }

    @ApiOperation(value="数据分析-提及量走势")
    @PostMapping(value = "/dataAnalysis-statisticsTrend")
    public Result<?> statisticsTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        model.setChannelIds(Arrays.asList(risk.getChannelId()));
        return vocRiskService.intentionTrend(model, risk);
    }

    @ApiOperation(value = "数据分析-车系占比")
    @PostMapping(value = "/dataAnalysis-carSeriesDistribution")
    public Result<?> carSeriesDistribution(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        model.setChannelIds(Arrays.asList(risk.getChannelId()));
        return vocRiskService.carSeriesDistribution(model);
    }

    @ApiOperation(value = "数据分析-区域分布")
    @PostMapping(value = "/dataAnalysis-regionalDistribution")
    public Result<?> regionalDistribution(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocQualityRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        model.setChannelIds(Arrays.asList(risk.getChannelId()));
        return vocRiskService.regionalDistribution(model);
    }

}
