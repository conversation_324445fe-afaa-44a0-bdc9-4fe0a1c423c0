package com.car.voc.stats.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.stats.entity.risk.DwdVocRisk;
import com.car.stats.model.RiskEventInsightModel;
import com.car.stats.serivce.IDwdVocRiskService;
import com.car.stats.serivce.es.EsDataSentenceVocService;
import com.car.stats.vo.risk.RiskPointAggVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.CalculatorUtils;
import com.car.voc.common.util.IDateUtils;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * voc-风险事件洞察(风险警示)
 * @version 1.0.0
 * @ClassName RiskEventInsightController.java
 * @Description 风险事件洞察(风险警示)
 * @createTime 2022年11月23日 11:42
 * @Copyright voc
 */

@Api(tags="voc-风险事件洞察(风险警示)")
@RestController
@RequestMapping("/stats/riskEvent")
@Slf4j
public class RiskEventInsightController {

    @Autowired
    IDwdVocRiskService vocRiskService;
    @Autowired
    EsDataSentenceVocService sentenceVocService;
    @AutoLog(value = "业务声音洞察-业务风险洞察")
    @ApiOperation(value="风险点聚集")
    @PostMapping(value = "/riskPointAgg")
    public Result<?> riskPointAgg(@RequestBody RiskEventInsightModel model) {
        model.SetUpCycle();
        IPage<RiskPointAggVo> aggVoIPage=new Page<>();
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        Set<Object> channelSet = redisUtil.sGet(String.format(CacheConstant.SYS_USER_BRANDCODE_TAG,model.getBrandCode(),getUserId()));
        if (CollUtil.isEmpty(channelSet)){
            return Result.OK(aggVoIPage);
        }
        Set<String> sounps = channelSet.stream().map(String::valueOf).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(model.getSecondDimensionCodes())) {
            model.getSecondDimensionCodes().retainAll(sounps);
        } else {
            model.setSecondDimensionCodes(new ArrayList<>(sounps));
        }
        aggVoIPage=vocRiskService.riskPointAggNew(model);

        return Result.OK(aggVoIPage);
    }


    public String getUserId(){
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return loginUser.getId();
    }



    @ApiOperation(value="信息列表")
    @PostMapping(value = "/riskInfoList")
    public Result<?> riskInfoList(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        setChannels(model,risk);
        return sentenceVocService.riskInfoList(model);
    }

    private void setChannels(RiskEventInsightModel model,DwdVocRisk risk) {
        model.setEmotion("负面");
    }

    @ApiOperation(value="数据分析-简报")
    @PostMapping(value = "/dataAnalysis-briefing")
//    @Cacheable(cacheNames = "voc:api:riskEvent:dataAnalysisBriefing",keyGenerator = "myCacheKeyGenerator")
    public Result<?> dataAnalysisBriefing(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        final String token =
                StrUtil.isNotBlank(model.getAccessToken()) ? model.getAccessToken() :
                        SpringContextUtils.getHttpServletRequest().getHeader(CommonConstant.X_ACCESS_TOKEN);
        model.setAccessToken(token);
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        setChannels(model,risk);
        return vocRiskService.dataAnalysisBriefing(model,risk);

    }
    @ApiOperation(value="数据分析-简报-趋势")
    @PostMapping(value = "/dataAnalysis-briefing-trend")
    public Result<?> dataAnalysisBriefingTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        setChannels(model,risk);
        return vocRiskService.dataAnalysisBriefingTrend(model, risk);

    }
    @ApiOperation(value="数据分析-发声渠道趋势")
    @PostMapping(value = "/dataAnalysis-channelTrend")
    public Result<?> dataAnalysisChannelTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        setChannels(model,risk);
        return vocRiskService.dataAnalysisChannelTrend(model,risk);



    }
    @ApiOperation(value="数据分析-情感及意图走势")
    @PostMapping(value = "/dataAnalysis-emotionIntentionTrend")
    public Result<?> emotionIntentionTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        setChannels(model,risk);
        return vocRiskService.emotionIntentionTrend(model, risk);



    }
    @ApiOperation(value="数据分析-高频提及热词")
    @PostMapping(value = "/dataAnalysis-hotWords")
    public Result<?> hotWords(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        model.setEmotion("负面");
        setChannels(model,risk);

        model.setTopicCode(risk.getTopicCode());
        model.setEmotion("负面");
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),risk.getPublishDate()));


        return vocRiskService.hotWords(model);
    }
    @ApiOperation(value="数据分析-发声用户趋势")
    @PostMapping(value = "/dataAnalysis-voiceUserTrend")
    public Result<?> voiceUserTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        if (risk==null){
            return Result.error("未找到问题！");
        }
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setBrandCode(risk.getBrandCode());
        model.setTopicCode(risk.getTopicCode());
        model.setEmotion("负面");
        setChannels(model,risk);
        return vocRiskService.voiceUserTrend(model,risk);
    }
    @ApiOperation(value="数据分析-发声用户Top")
    @PostMapping(value = "/dataAnalysis-voiceUserTop")
    public Result<?> voiceUserTop(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        model.setCreateDate(IDateUtils.format(risk.getCreateTime()));
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        model.setEmotion("负面");
        setChannels(model,risk);
        model.setStartDate(DateUtil.formatDateTime(risk.getPublishDate()));
        model.setDateUnit(CalculatorUtils.periodStrToNum(risk.getStatisticType()));
        model.setEndDate(CalculatorUtils.getRiskEndDate(model.getDateUnit(),risk.getPublishDate()));
        return vocRiskService.voiceUserTop(model);



    }
    @ApiOperation(value="数据分析-时间节点")
    @PostMapping(value = "/dataAnalysis-timeNode")
    public Result<?> dataAnalysisTimeNode(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        setChannels(model,risk);
        return sentenceVocService.dataAnalysisTimeNode(model,risk);


    }



    @ApiOperation(value="数据分析-已警示次数")
    @PostMapping(value = "/dataAnalysis-waringNum")
    public Result<?> dataAnalysisWaringNum(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        DwdVocRisk risk=vocRiskService.getById(model.getRiskId());
        model.setTopicCode(risk.getTopicCode());
        model.setBrandCode(risk.getBrandCode());
        setChannels(model,risk);
        return vocRiskService.dataAnalysisWaringNum(model,risk);
    }

    @ApiOperation(value="数据分析-邮件内容")
    @PostMapping(value = "/dataAnalysis-mailData")
//    @Cacheable(cacheNames = "voc:api:riskEvent:dataAnalysisMailData",keyGenerator = "myCacheKeyGenerator")
    public Result<?> riskMailData(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        model.setEmotion("负面");
        return vocRiskService.riskMailData(model);
    }

    @ApiOperation(value="数据分析-提及量趋势")
    @PostMapping(value = "/dataAnalysis-statictisTrend")
//    @Cacheable(cacheNames = "voc:api:riskEvent:dataAnalysisStatictisTrend",keyGenerator = "myCacheKeyGenerator")
    public Result<?> riskStatictisTrend(@RequestBody RiskEventInsightModel model) {
        if (!StrUtil.isNotBlank(model.getRiskId())){
            return Result.error("风险点Id(riskId)不能为空");
        }
        model.setEmotion("负面");
        return vocRiskService.riskstatictisTrend(model);
    }
}
