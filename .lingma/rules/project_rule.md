你是一个资深的java专家，请在开发中遵循如下规则：
- 严格遵循 **SOLID、DRY、KISS、YAGNI** 原则
- 遵循 **OWASP 安全最佳实践**（如输入验证、SQL注入防护）
- 采用 **分层架构设计**，确保职责分离
- 代码变更需通过 **单元测试覆盖**（测试覆盖率 ≥ 80%）

---

## 二、技术栈规范
### 技术栈要求
- **框架**：Spring Boot 2.5.4 + Java 1.8
- **依赖**：
    - 核心：Spring Web, Spring Data JPA, Lombok, MyBatis Plus 
    - 数据库：MYSql, StarRocks, Redis 或其他关系型数据库驱动
    - 其他：Swagger (SpringDoc), Spring Security (如需权限控制)

---

## 三、应用逻辑设计规范
### 1. 分层架构原则
| 层级          | 职责                                                                 | 约束条件                                                                 |
|---------------|----------------------------------------------------------------------|--------------------------------------------------------------------------|
| **Controller** | 处理 HTTP 请求与响应，定义 API 接口                                 | - 禁止直接操作数据库<br>- 必须通过 Service 层调用                          |
| **Service**    | 业务逻辑实现，事务管理，数据校验                                   | - 必须通过 Repository 访问数据库<br>- 返回 DTO 而非实体类（除非必要）       |
| **Repository** | 数据持久化操作，定义数据库查询逻辑                                 | - 必须继承 `JpaRepository`<br>- 使用 `@EntityGraph` 避免 N+1 查询问题       |
| **Entity**     | 数据库表结构映射对象                                               | - 仅用于数据库交互<br>- 禁止直接返回给前端（需通过 DTO 转换）               |

---

## 四、核心代码规范
### 1. 实体类（Entity）规范
```java
@Entity
@Data // Lombok 注解
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50)
    private String username;

    @Email
    private String email;

    // 关联关系使用懒加载
    @ManyToOne(fetch = FetchType.LAZY)
    private Department department;
}
```

### 2. 数据访问层（Repository）规范
```java
public interface UserRepository extends JpaRepository<User, Long> {
    // 命名查询
    Optional<User> findByUsername(String username);

    // 自定义 JPQL 查询
    @Query("SELECT u FROM User u JOIN FETCH u.department WHERE u.id = :id")
    @EntityGraph(attributePaths = {"department"})
    Optional<User> findUserWithDepartment(@Param("id") Long id);
}
```

### 3. 服务层（Service）规范
```java
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserRepository userRepository;

    @Transactional
    public ApiResponse<UserDTO> createUser(UserDTO dto) {
        // 业务逻辑实现
        User user = User.builder().username(dto.getUsername()).build();
        User savedUser = userRepository.save(user);
        return ApiResponse.success(UserDTO.fromEntity(savedUser));
    }
}
```

### 4. 控制器（RestController）规范
```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<ApiResponse<UserDTO>> createUser(@RequestBody @Valid UserDTO dto) {
        try {
            ApiResponse<UserDTO> response = userService.createUser(dto);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return GlobalExceptionHandler.errorResponseEntity(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
}
```

---

## 五、数据传输对象（DTO）规范
```java
// 使用 record 或 @Data 注解
public record UserDTO(
    @NotBlank String username,
    @Email String email
) {
    public static UserDTO fromEntity(User entity) {
        return new UserDTO(entity.getUsername(), entity.getEmail());
    }
}
```

---

## 六、全局异常处理规范
### 1. 统一响应类（ApiResponse）
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    private String result; // SUCCESS/ERROR
    private String message;
    private T data;

    // 工厂方法
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>("SUCCESS", "操作成功", data);
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>("ERROR", message, null);
    }
}
```

### 2. 全局异常处理器（GlobalExceptionHandler）
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiResponse<?>> handleEntityNotFound(EntityNotFoundException ex) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(ex.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<?>> handleValidationErrors(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));
        return ResponseEntity.badRequest().body(ApiResponse.error(errorMessage));
    }
}
```

---

## 七、安全与性能规范
1. **输入校验**：
    - 使用 `@Valid` 注解 + JSR-303 校验注解（如 `@NotBlank`, `@Size`）
    - 禁止直接拼接 SQL 防止注入攻击
2. **事务管理**：
    - `@Transactional` 注解仅标注在 Service 方法上
    - 避免在循环中频繁提交事务
3. **性能优化**：
    - 使用 `@EntityGraph` 预加载关联关系
    - 避免在循环中执行数据库查询（批量操作优先）

---

## 八、代码风格规范
1. **命名规范**：
    - 类名：`UpperCamelCase`（如 `UserServiceImpl`）
    - 方法/变量名：`lowerCamelCase`（如 `saveUser`）
    - 常量：`UPPER_SNAKE_CASE`（如 `MAX_LOGIN_ATTEMPTS`）
2. **注释规范**：
    - 方法必须添加注释且方法级注释使用 Javadoc 格式
    - 计划待完成的任务需要添加 `// TODO` 标记
    - 存在潜在缺陷的逻辑需要添加 `// FIXME` 标记
3. **代码格式化**：
    - 使用 IntelliJ IDEA 默认的 Spring Boot 风格
    - 禁止手动修改代码缩进（依赖 IDE 自动格式化）

---

## 九、部署规范
1. **部署规范**：
    - 生产环境需禁用 `@EnableAutoConfiguration` 的默认配置
    - 敏感信息通过 `application.properties` 外部化配置
    - 使用 `Spring Profiles` 管理环境差异（如 `dev`, `prod`）

---

## 十、扩展性设计规范
1. **接口优先**：
    - 服务层接口（`UserService`）与实现（`UserServiceImpl`）分离
2. **扩展点预留**：
    - 关键业务逻辑需提供 `Strategy` 或 `Template` 模式支持扩展
3. **日志规范**：
    - 使用 `SLF4J` 记录日志（禁止直接使用 `System.out.println`）
    - 核心操作需记录 `INFO` 级别日志，异常记录 `ERROR` 级别

:D:\work\project\voc\voc-demo-api\.lingma\rules\project_rule.md
**添加规则文件可帮助模型精准理解你的编码偏好，如框架、代码风格等**
**规则文件只对当前工程生效，单文件限制10000字符。如果无需将该文件提交到远程 Git 仓库，请将其添加到 .gitignore**

## 项目结构规范

本项目采用多模块Maven结构，各模块职责如下：
- car-voc-parent: 父项目，管理依赖版本
- car-voc-tools: 公共工具类模块
- car-voc-core: 核心配置模块
- car-voc-service: 业务逻辑层模块
- car-voc-api: 接口定义模块
- car-voc-admin: 后台管理模块
- car-voc-app: 应用模块
- car-voc-report: 报表模块
- car-voc-case: 案例模块
- car-voc-sound: 声音处理模块

## 编码规范

### 命名规范
1. 类名采用大驼峰命名法，如 [VocProjectGroupController](file://D:\work\project\voc\voc-demo-api\car-voc-admin\src\main\java\com\car\voc\admin\controller\VocProjectGroupController.java#L15-L57)
2. 方法名采用小驼峰命名法，如 [saveProjectGroup](file://D:\work\project\voc\voc-demo-api\car-voc-service\src\main\java\com\car\voc\service\VocProjectGroupService.java#L18-L18)
3. 常量全部大写，单词间用下划线分隔，如 [DOC_SEND](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L13-L13)
4. 包名全部小写，采用功能模块划分，如 `com.car.voc.service`

### 实体类规范
1. 使用Lombok注解简化代码，如 `@Data`、`@ApiModel` 等
2. 实体类字段需添加Swagger注解，如 `@ApiModelProperty`
3. 数据库字段映射使用MyBatis Plus注解，如 `@TableId`、`@TableName`

### 控制器规范
1. 控制器类需添加 `@RestController` 和 `@RequestMapping` 注解
2. 使用Swagger注解描述接口，如 `@Api`、`@ApiOperation`
3. 接口返回统一使用 `Result<T>` 对象
4. 参数校验使用Spring Assert工具类

## EntityGraph使用规范

在本项目中，EntityGraph主要用于优化JPA实体间关联关系的加载策略。使用EntityGraph可以：
- 精确控制实体关联属性的加载范围
- 避免N+1查询问题
- 提升数据查询性能

使用要求：
1. 所有Repository接口方法应明确指定EntityGraph策略
2. EntityGraph名称应与实体类属性保持一致
3. 对于复杂嵌套关联，建议分层加载而非一次性加载所有关联

## 业务规则引擎规范

### 填值规则
1. 所有填值规则实现类必须实现 [IFillRuleHandler](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\handler\IFillRuleHandler.java#L11-L20) 接口
2. 规则编码定义在 [FillRuleConstant](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L8-L43) 常量类中
3. 规则通过 [FillRuleUtil.executeRule()](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\util\FillRuleUtil.java#L23-L55) 方法执行
4. 规则参数通过 `JSONObject` 传递

### 规则编码常量
- [DOC_SEND](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L13-L13): 公文发文编码
- [DEPART](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L18-L18): 部门编码
- [CATEGORY](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L23-L23): 分类字典编码
- [BRAND_PRODUCT_MANAGER](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L28-L28): 品牌产品管理
- [BUSINESS_TAG](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L33-L33): 标签编码
- [FAULT_PROBLEM](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L37-L37): VOC故障问题编码
- [THESAURUS](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\constant\FillRuleConstant.java#L41-L41): VOC行业语料词典编码

## 数据库规范

### MyBatis Plus使用
1. Mapper接口继承 `BaseMapper<T>`
2. Service实现类继承 `ServiceImpl<M, T>`
3. XML映射文件与Mapper接口同名，放在对应mapper.xml目录下

### 查询规范
1. 分页查询使用MyBatis Plus的 `Page<T>` 对象
2. 复杂查询在XML中编写SQL，使用 `<if>` 标签处理动态条件
3. 多表关联查询使用LEFT JOIN，避免N+1问题

## 安全规范

### 认证授权
1. 使用JWT进行用户身份验证
2. 用户信息通过 [CommonService.getUserNameByToken()](file://D:\work\project\voc\voc-demo-api\car-voc-service\src\main\java\com\car\voc\service\CommonService.java#L35-L42) 获取
3. 敏感字段使用 [@SensitiveField](file://D:\work\project\voc\voc-demo-api\car-voc-tools\src\main\java\com\car\voc\common\aspect\annotation\SensitiveField.java#L14-L18) 注解标记

### 参数校验
1. 必填参数使用 `Assert.hasText()` 校验
2. 长度限制使用 `Assert.isTrue()` 校验
3. 数值范围在业务逻辑中进行判断

## 异常处理规范

1. 统一使用 `Result<T>` 封装返回结果
2. 业务异常通过 [Result.error()](file://D:\work\project\voc\voc-demo-api\car-voc-core\src\main\java\com\car\voc\common\Result.java#L128-L130) 方法返回
3. 系统异常通过全局异常处理器处理
4. 所有对外接口必须处理异常，不能直接抛出

## 日志规范

1. 使用Slf4j注解进行日志记录
2. 关键业务操作必须记录日志
3. 敏感信息（如密码）不能记录到日志中
4. 异常信息需完整记录堆栈跟踪

## 配置规范

1. 多环境配置通过 `application-{env}.yml` 实现
2. 敏感配置使用Jasypt加密
3. 自定义配置项添加详细注释
4. 配置项命名采用小写字母+短横线分隔

## 接口规范

1. RESTful API设计遵循HTTP动词语义
2. 接口路径采用小写字母+短横线分隔
3. 所有接口添加Swagger文档注解
4. 接口版本通过URL路径管理

## 依赖管理规范

1. 所有依赖版本在父项目 [pom.xml](file://D:\work\project\voc\voc-demo-api\pom.xml) 中统一管理
2. 禁止在子模块中直接指定依赖版本
3. 第三方依赖需评估安全性和稳定性
4. 依赖范围根据实际使用场景选择
