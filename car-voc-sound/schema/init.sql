CREATE TABLE `cc_user_depart` (
                                  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
                                  `depart_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部门id',
                                  `user_name` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
                                  `user_account` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '账号',
                                  `depart_name` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部门名称',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='cc用户部门关联表';

CREATE TABLE `voc_upvote_user` (
                                   `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                   `user_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
                                   `comment_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '评论id',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='点赞用户表';

CREATE TABLE `voc_sound_permission` (
                                        `id` varchar(132) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `parent_id` varchar(132) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `component` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `component_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `redirect` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `menu_type` decimal(11,0) DEFAULT NULL,
                                        `perms` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `perms_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `sort_no` decimal(8,2) DEFAULT NULL,
                                        `always_show` decimal(4,0) DEFAULT NULL,
                                        `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `is_route` decimal(4,0) DEFAULT NULL,
                                        `is_leaf` decimal(4,0) DEFAULT NULL,
                                        `keep_alive` decimal(4,0) DEFAULT NULL,
                                        `hidden` decimal(11,0) DEFAULT NULL,
                                        `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `create_by` varchar(132) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `create_time` datetime DEFAULT NULL,
                                        `update_by` varchar(132) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `update_time` datetime DEFAULT NULL,
                                        `del_flag` decimal(11,0) DEFAULT NULL,
                                        `rule_flag` decimal(11,0) DEFAULT NULL,
                                        `status` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `internal_or_external` decimal(4,0) DEFAULT NULL,
                                        `hide_tab` decimal(11,0) DEFAULT NULL,
                                        `menu_i18n` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                        `api_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='声音聆听权限';

CREATE TABLE `voc_sound_listening` (
                                       `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                       `sound_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '声音名称',
                                       `brand` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '品牌',
                                       `car_series` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '车系',
                                       `first_label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '一级标签',
                                       `second_label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二级标签',
                                       `three_label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '三级标签',
                                       `four_label` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '四级标签',
                                       `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                       `is_mandatory` tinyint(1) DEFAULT NULL COMMENT '是否必听',
                                       `reward_integral` double DEFAULT NULL COMMENT '奖励积分',
                                       `work_order_no` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工单号',
                                       `work_order_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工单类型',
                                       `sound_duration` bigint DEFAULT NULL COMMENT '声音时长',
                                       `valid_start_time` datetime DEFAULT NULL COMMENT '有效期开始时间',
                                       `valid_end_time` datetime DEFAULT NULL COMMENT '有效期结束时间',
                                       `listening_range` text COLLATE utf8mb4_unicode_ci COMMENT '可听范围',
                                       `original_sound` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始声音',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                       `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
                                       `del` int DEFAULT '0' COMMENT '删除标识',
                                       `status` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '状态',
                                       `task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务id',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='声音聆听';

CREATE TABLE `voc_listen_record` (
                                     `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                     `task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务id',
                                     `user_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
                                     `sound_duration` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '音频时长',
                                     `listen_duration` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '聆听时长',
                                     `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '聆听状态',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `del` int DEFAULT '0' COMMENT '删除标识',
                                     `listen_times` int DEFAULT '1' COMMENT '聆听次数',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聆听记录';

CREATE TABLE `voc_listen_setting` (
                                      `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                      `department` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部门',
                                      `account` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '账号',
                                      `permission` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限',
                                      `status` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态',
                                      `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                      `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聆听设置';

CREATE TABLE `voc_favorite` (
                                `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                `task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务id',
                                `user_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `del` int DEFAULT '0' COMMENT '删除标识',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏';

CREATE TABLE `voc_integral` (
                                `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                `task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务id',
                                `user_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
                                `integral` double DEFAULT NULL COMMENT '积分',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分';

CREATE TABLE `voc_comment_setting` (
                                       `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                       `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型名称',
                                       `sort` int DEFAULT NULL COMMENT '类型排序',
                                       `min_input_num` int DEFAULT NULL COMMENT '最少输入字数',
                                       `max_input_num` int DEFAULT NULL COMMENT '最多输入字数',
                                       `like_num` int DEFAULT NULL COMMENT '点赞数',
                                       `reward_integral` double DEFAULT NULL COMMENT '奖励积分',
                                       `status` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '状态',
                                       `del` int DEFAULT '0' COMMENT '删除标识',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                       `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论设置';

CREATE TABLE `voc_comment` (
                               `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                               `task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '声音任务id',
                               `comment_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '评论类型',
                               `comment` text COLLATE utf8mb4_unicode_ci COMMENT '评论内容',
                               `comment_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '评论人',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `reply_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '回复人',
                               `reply` text COLLATE utf8mb4_unicode_ci COMMENT '回复内容',
                               `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
                               `like_count` bigint DEFAULT '0' COMMENT '点赞数',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论';

CREATE TABLE `voc_carousel_image` (
                                      `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
                                      `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片名称',
                                      `image_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片地址',
                                      `link_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '跳转地址',
                                      `sort` int DEFAULT NULL COMMENT '排序',
                                      `status` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '状态',
                                      `del` int DEFAULT '0' COMMENT '删除标识',
                                      `create_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                      `update_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图';

INSERT INTO voc_dongfeng.voc_sound_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, menu_i18n, api_address) VALUES ('c7d6e2e4e2934f2c9385a623fd98c604', '0', '首页', '/listen/dashBoard', null, null, null, 0, null, null, 1.00, 0, 'setting', 1, 0, null, 0, null, null, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, null, 0, 0, 'dashBoard', '/listen/dashBoard');
INSERT INTO voc_dongfeng.voc_sound_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, menu_i18n, api_address) VALUES ('c7d6e2e4e2934f2c9385a623fd98c605', '0', '任务管理', '/listen/task', null, null, null, 0, null, null, 1.00, 0, 'setting', 1, 0, null, 0, null, null, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, null, 0, 0, 'task', '/listen/task');
INSERT INTO voc_dongfeng.voc_sound_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, menu_i18n, api_address) VALUES ('c7d6e2e4e2934f2c9385a623fd98c606', '0', '聆听管理', '/listen/source', null, null, null, 0, null, null, 1.00, 0, 'setting', 1, 0, null, 0, null, null, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, null, 0, 0, 'source', '/listen/source');
INSERT INTO voc_dongfeng.voc_sound_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, menu_i18n, api_address) VALUES ('c7d6e2e4e2934f2c9385a623fd98c607', '0', '评论管理', '/listen/comment', null, null, null, 0, null, null, 1.00, 0, 'setting', 1, 0, null, 0, null, null, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, null, 0, 0, 'comment', '/listen/comment');
INSERT INTO voc_dongfeng.voc_sound_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, menu_i18n, api_address) VALUES ('c7d6e2e4e2934f2c9385a623fd98c608', '0', '轮播图管理', '/listen/carousel', null, null, null, 0, null, null, 1.00, 0, 'setting', 1, 0, null, 0, null, null, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, null, 0, 0, 'carousel', '/listen/carousel');


INSERT INTO voc_dongfeng.sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) VALUES ('68168534ff5065a152bfab275c2211b8', '开启关闭状态', 'on_Off_status', '开启关闭状态', 0, 'admin', '2020-09-26 19:21:14', 'admin', '2019-04-26 19:21:23', 0);
INSERT INTO voc_dongfeng.sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) VALUES ('1909876756488663043', '工单类型', 'work_order_type', '', 0, null, '2025-04-09 15:51:17', null, null, null);
INSERT INTO voc_dongfeng.sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) VALUES ('68168534ff5065a152bfab275c2211b9', '已听未听状态', 'heard_unheard_status', '已听未听状态', 0, 'admin', '2020-09-26 19:21:14', 'admin', '2019-04-26 19:21:23', 0);
INSERT INTO voc_dongfeng.sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) VALUES ('68168534ff5065a152bfab275c2211c0', '回复状态', 'replied_unreplied_status', '回复状态', 0, 'admin', '2020-09-26 19:21:14', 'admin', '2019-04-26 19:21:23', 0);
INSERT INTO voc_dongfeng.sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) VALUES ('68168534ff5065a152bfab275c2211c1', '是否必听', 'mandatory_status', '是否必听', 0, 'admin', '2020-09-26 19:21:14', 'admin', '2019-04-26 19:21:23', 0);



INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a8bb3a01', '68168534ff5065a152bfab275c2211b8', '开启', '1', '开启', 1, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a9cb4b01', '68168534ff5065a152bfab275c2211b8', '关闭', '0', '关闭', 2, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('1909876906934153200', '1909876756488663043', '投诉', '0', '', 0, 1, null, '2025-04-09 15:51:53', null, null, null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('1909876906934153201', '1909876756488663043', '咨询', '1', '', 1, 1, null, '2025-04-09 15:51:53', null, null, null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('1909876906934153202', '1909876756488663043', '建议', '2', '', 2, 1, null, '2025-04-09 15:51:53', null, null, null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('1909876906934153203', '1909876756488663043', '商机', '3', '', 3, 1, null, '2025-04-09 15:51:53', null, null, null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('1909876906934153204', '1909876756488663043', '线索', '4', '', 4, 1, null, '2025-04-09 15:51:53', null, null, null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a9cb4b02', '68168534ff5065a152bfab275c2211b9', '未听', '0', '未听', 1, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a9cb4b03', '68168534ff5065a152bfab275c2211b9', '已听', '1', '已听', 2, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a9cb4b04', '68168534ff5065a152bfab275c2211c0', '未回复', '0', '未回复', 1, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a9cb4b06', '68168534ff5065a152bfab275c2211c1', '必听', '1', '必听', 1, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);
INSERT INTO voc_dongfeng.sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time, item_text_en, item_key, brand_code) VALUES ('2d51376643f220afdeb6d216a9cb4b07', '68168534ff5065a152bfab275c2211c1', '非必听', '0', '非必听', 2, 1, 'admin', '2019-04-26 19:22:01', null, '2000-01-01 00:00:00', null, null, null);


INSERT INTO voc_dongfeng.sys_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external, hide_tab, `system`, menu_i18n, api_address) VALUES ('d7d6e2e4e2934f2c9385b991fc798574', 'd7d6e2e4e2934f2c9385a623fd98c600', '聆听配置', '/operationManagement/listen', 'layouts/RouteView', null, null, 0, null, null, 1.00, 0, 'setting', 1, 1, null, 0, null, null, '2018-12-25 20:34:38', 'admin', '2021-06-15 17:00:08', 0, 0, null, 0, 0, 0, 'listen', null);


alter table voc_listen_record add success_time datetime null comment '聆听完成时间';
update sys_dict_item set item_text = '救援' where id = '1909876906934153204';
alter table voc_listen_record add depart_id varchar(500) null comment '部门id';