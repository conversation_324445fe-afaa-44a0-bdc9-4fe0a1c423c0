package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 15:21
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("cc_user_depart")
public class UserDepartEntity implements Serializable {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 用户id
     */
    private String userId;
    private String userName;
    private String userAccount;
    /**
     * 部门id
     */
    private String departId;
    private String departName;
    private String brand;
}
