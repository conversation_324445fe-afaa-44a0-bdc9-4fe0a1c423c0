package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:34
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "CommentSettingModel", description = "评论设置")
@TableName("voc_comment_setting")
public class CommentSettingEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 类型名称
     */
    private String name;
    /**
     * 类型排序
     */
    private Integer sort;
    /**
     * 最少输入字数
     */
    private Integer minInputNum;
    /**
     * 最多输入字数
     */
    private Integer maxInputNum;
    /**
     * 点赞数
     */
    private Integer likeNum;
    /**
     * 奖励积分
     */
    private BigDecimal rewardIntegral;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 状态
     */
    private String status;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 删除标识
     */
    private Integer del;

    @TableField(exist = false)
    private String statusName;
}
