package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/14 17:43
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("voc_sound_permission")
public class MenuEntity {
    /**
     * id
     */
    private String id;

    /**
     * parent_id
     */
    private String parentId;

    /**
     * name
     */
    private String name;

    /**
     * url
     */
    private String url;

    /**
     * component
     */
    private String component;

    /**
     * component_name
     */
    private String componentName;

    /**
     * redirect
     */
    private String redirect;

    /**
     * menu_type
     */
    private BigDecimal menuType;

    /**
     * perms
     */
    private String perms;

    /**
     * perms_type
     */
    private String permsType;

    /**
     * sort_no
     */
    private BigDecimal sortNo;

    /**
     * always_show
     */
    private BigDecimal alwaysShow;

    /**
     * icon
     */
    private String icon;

    /**
     * is_route
     */
    private BigDecimal isRoute;

    /**
     * is_leaf
     */
    private BigDecimal isLeaf;

    /**
     * keep_alive
     */
    private BigDecimal keepAlive;

    /**
     * hidden
     */
    private BigDecimal hidden;

    /**
     * description
     */
    private String description;

    /**
     * create_by
     */
    private String createBy;

    /**
     * create_time
     */
    private LocalDateTime createTime;

    /**
     * update_by
     */
    private String updateBy;

    /**
     * update_time
     */
    private LocalDateTime updateTime;

    /**
     * del_flag
     */
    private BigDecimal delFlag;

    /**
     * rule_flag
     */
    private BigDecimal ruleFlag;

    /**
     * status
     */
    private String status;

    /**
     * internal_or_external
     */
    private BigDecimal internalOrExternal;

    /**
     * hide_tab
     */
    private BigDecimal hideTab;


    /**
     * menu_i18n
     */
    private String menuI18n;

    /**
     * api_address
     */
    private String apiAddress;
}
