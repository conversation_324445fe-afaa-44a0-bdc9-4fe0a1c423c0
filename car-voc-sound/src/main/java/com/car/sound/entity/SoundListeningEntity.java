package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:10
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("voc_sound_listening")
public class SoundListeningEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 声音名称
     */
    private String soundName;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 车系
     */
    private String carSeries;
    /**
     * 一级标签
     */
    private String firstLabel;
    /**
     * 二级标签
     */
    private String secondLabel;
    /**
     * 三级标签
     */
    private String threeLabel;
    /**
     * 四级标签
     */
    private String fourLabel;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否必听
     */
    private String isMandatory;
    /**
     * 奖励积分
     */
    private BigDecimal rewardIntegral;
    /**
     * 工单号
     */
    private String workOrderNo;
    /**
     * 工单类型
     */
    private String workOrderType;
    /**
     * 声音时长
     */
    private Long soundDuration;
    /**
     * 有效期开始时间
     */
    private LocalDateTime validStartTime;
    /**
     * 有效期结束时间
     */
    private LocalDateTime validEndTime;
    /**
     * 可听范围
     */
    private String listeningRange;
    private Integer level;
    private Boolean isLeaf;
    /**
     * 原始声音
     */
    private String originalSound;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 删除标识
     */
    private Integer del;
    /**
     * 状态
     */
    private String status;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 状态名称
     */
    @TableField(exist = false)
    private String statusName;
    /**
     * 是否收藏
     */
    @TableField(exist = false)
    private Boolean isFavorite;
    /**
     * 聆听时长
     */
    @TableField(exist = false)
    private Long listenDuration;
    /**
     * 是否评论
     */
    @TableField(exist = false)
    private String isComment;
    /**
     * 是否完成
     */
    @TableField(exist = false)
    private String isComplete;
    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String departName;
    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;
    /**
     * 参与时间
     */
    @TableField(exist = false)
    private LocalDateTime joinTime;
    /**
     * 聆听进度
     */
    @TableField(exist = false)
    private BigDecimal listenProgress;
    @TableField(exist = false)
    private String userId;

    /**
     * 聆听人数
     */
    @TableField(exist = false)
    private Integer listenCount;
    /**
     * 聆听人次
     */
    @TableField(exist = false)
    private Integer listenTimes;
    /**
     * 评论人次
     */
    @TableField(exist = false)
    private Integer commentTimes;
    /**
     * 已收藏数
     */
    @TableField(exist = false)
    private Integer favoriteCount;
    /**
     * 已评论数
     */
    @TableField(exist = false)
    private Integer commentCount;
    /**
     * 已完成数
     */
    @TableField(exist = false)
    private Integer completeCount;
    @TableField(exist = false)
    private String listeningRanges;


}
