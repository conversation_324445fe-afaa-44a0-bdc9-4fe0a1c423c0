package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 14:27
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("voc_comment")
public class CommentEntity implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 声音任务id
     */
    private String taskId;
    /**
     * 评论类型
     */
    private String commentType;
    /**
     * 评论内容
     */
    private String comment;
    /**
     * 评论人
     */
    private String commentUser;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 回复人
     */
    private String replyUser;
    /**
     * 回复内容
     */
    private String reply;
    /**
     * 回复时间
     */
    private LocalDateTime replyTime;
    /**
     * 点赞数
     */
    private Long likeCount;
    /**
     * 账号
     */
    @TableField(exist = false)
    private String account;
    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String departName;
    /**
     * 是否回复
     */
    @TableField(exist = false)
    private String isReply;
    /**
     * 评论类型名称
     */
    @TableField(exist = false)
    private String commentTypeName;
    /**
     * 是否点赞
     **/
    @TableField(exist = false)
    private Boolean isUpvote;
}
