package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 14:36
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("voc_favorite")
public class FavoriteEntity implements Serializable {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 删除状态
     */
    private Integer del;
}
