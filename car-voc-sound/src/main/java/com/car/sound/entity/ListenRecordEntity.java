package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 15:35
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("voc_listen_record")
public class ListenRecordEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 部门id
     */
    private String departId;
    /**
     * 音频时长
     */
    private Long soundDuration;
    /**
     * 聆听时长
     */
    private Long listenDuration;
    /**
     * 聆听状态
     */
    private String status;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 删除标识
     */
    private Integer del;
    /**
     * 聆听次数
     */
    private Integer listenTimes;
    /**
     * 聆听完成时间
     */
    private  LocalDateTime successTime;
}
