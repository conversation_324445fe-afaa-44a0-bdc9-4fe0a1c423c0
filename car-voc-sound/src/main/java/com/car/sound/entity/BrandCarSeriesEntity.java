package com.car.sound.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.car.voc.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 品牌产品管理
 * @Date: 2021-04-09
 * @Version: V1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("voc_brand_product_manager")
public class BrandCarSeriesEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * p_id
     */
    private String pId;

    /**
     * code
     */
    private String code;

    /**
     * name
     */
    private String name;

    /**
     * alias
     */
    private String alias;

    /**
     * industry
     */
    private String industry;

    /**
     * car_type
     */
    private String carType;

    /**
     * car_space_type
     */
    private String carSpaceType;

    /**
     * type
     */
    private Double type;

    /**
     * relation_id
     */
    private String relationId;

    /**
     * english_name
     */
    private String englishName;

    /**
     * country
     */
    private String country;

    /**
     * remark
     */
    private String remark;

    /**
     * screen_img
     */
    private String screenImg;

    /**
     * show_img
     */
    private String showImg;

    /**
     * industry_text
     */
    private String industryText;

    /**
     * has_child
     */
    private String hasChild;

    /**
     * sort_no
     */
    private Double sortNo;

    /**
     * energy_type
     */
    private String energyType;

    /**
     * energy_supply_type
     */
    private String energySupplyType;

    /**
     * model_group_id
     */
    private String modelGroupId;

    /**
     * enable
     */
    private Double enable;

    /**
     * create_by
     */
    private String createBy;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * del_flag
     */
    private Double delFlag;

    /**
     * update_by
     */
    private String updateBy;

    /**
     * update_time
     */
    private Date updateTime;
}
