package com.car.sound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.entity.CommentSettingEntity;
import com.car.sound.model.CommentSettingModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:53
 * @描述:
 **/
@Mapper
@Repository
public interface CommentSettingMapper extends BaseMapper<CommentSettingEntity> {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 11:27
     * @描述   分页查询评论设置列表
     * @param page
     * @param model
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.car.sound.entity.CommentSettingEntity>
     **/
    IPage<CommentSettingEntity> getCommentSettingList(IPage<CommentSettingEntity> page, @Param("model") CommentSettingModel model);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 11:27
     * @描述   查询所有评论设置列表
     * @param
     * @return java.util.List<com.car.sound.entity.CommentSettingEntity>
     **/
    List<CommentSettingEntity> getAllCommentSettingList();
    List<CommentSettingEntity> getCommentTypeList();
}
