package com.car.sound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.sound.entity.SoundListeningEntity;
import com.car.sound.entity.UserEntity;
import com.car.sound.vo.UserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 11:44
 * @描述:
 **/
@Mapper
@Repository
public interface UserMapper extends BaseMapper<UserEntity> {

    UserEntity getUserInfoByUserId(@Param("userId") String userId);

    List<UserVo> getDepartUser();
}
