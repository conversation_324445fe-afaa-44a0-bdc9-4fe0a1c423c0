<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.DictMapper">

    <select id="getDictItemByDictCode" resultType="com.car.sound.vo.DictItemVo">
        select
            sdi.item_text as `key`,
            sdi.item_value as value
        from sys_dict_item sdi
            left join sys_dict sd on sdi.dict_id = sd.id
        where sd.dict_code = #{dictCode}
    </select>
</mapper>