package com.car.sound.mapper;

import com.car.sound.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 17:04
 * @描述:
 **/
@Mapper
@Repository
public interface HomePageMapper {
    List<RankingListVo> getDepartRanking(@Param("month")String month);

    List<RankingListVo> getPersonRanking(@Param("month")String month);

    List<LabelVo> getLabelVo();

    List<LabelVo> UpwardFindBusinessLabelHierarchical(@Param("labelCode")String labelCode);
    List<LabelVo> UpwardFindQyLabelHierarchical(@Param("labelCode")String labelCode);

    LabelVo UpwardFindBusinessTopLabelHierarchical(@Param("labelCode")String labelCode);
    LabelVo UpwardFindQYTopLabelHierarchical(@Param("labelCode")String labelCode);

    List<BrandVo> getBrandCarSeriesByCodes(@Param("cods") List<String> cods);
}
