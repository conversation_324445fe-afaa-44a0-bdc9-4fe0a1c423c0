package com.car.sound.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.entity.SoundListeningEntity;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.model.SoundListeningModel;
import com.car.sound.vo.ListenTaskStatisticsVo;
import com.car.sound.vo.WorkNoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 15:17
 * @描述:
 **/
@Mapper
@Repository
public interface SoundListeningMapper extends BaseMapper<SoundListeningEntity> {

    WorkNoVo getSoundByWorkOrderNo(@Param("workOrderNo") String workOrderNo);
    @DS("starrocks1")
    List<WorkNoVo> getSoundByWorkOrderId(@Param("workOrderNoList") List<String> workOrderNo);

    IPage<SoundListeningEntity> getSoundListeningList(IPage<SoundListeningEntity> page,@Param("model") SoundListeningModel model);

    IPage<SoundListeningEntity> getListenTaskList(IPage<SoundListeningEntity> page,@Param("model") ListenTaskModel listenTaskModel);

    ListenTaskStatisticsVo getListenTaskStatistics(@Param("userId")String userId,@Param("departId")String departId);

    ListenTaskStatisticsVo getSoundListenDataStatistics(@Param("model") SoundListeningModel model);

    List<ListenTaskStatisticsVo> getAllSoundListenDataStatistics();

    IPage<SoundListeningEntity> getSoundListenDataList(IPage<SoundListeningEntity> page, @Param("model") SoundListeningModel model);
    Integer exportSoundListenDataSize(@Param("model") SoundListeningModel model);

    List<SoundListeningEntity> getListenTaskInfoData(@Param("model") ListenTaskModel model);

    List<String> getSoundListeningRange(@Param("taskId") String taskId);
}
