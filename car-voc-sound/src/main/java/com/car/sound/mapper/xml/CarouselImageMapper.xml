<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.CarouselImageMapper">
    <resultMap id="BaseResultMap" type="com.car.sound.entity.CarouselImageEntity" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="image_url" property="imageUrl" />
        <result column="link_url" property="linkUrl" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="del" property="del" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                name,
                image_url,
                link_url,
                sort,
                status,
                del,
                create_user,
                update_user,
                create_time,
                update_time
    </sql>
    <select id="getCarouselImageList" resultType="com.car.sound.entity.CarouselImageEntity">
        SELECT
        vci.id,
        vci.name,
        vci.image_url,
        vci.link_url,
        vci.sort,
        vci.status,
        vci.del,
        vci.create_user,
        vci.update_user,
        vci.create_time,
        vci.update_time
            ,dict.item_text as statusName
        FROM voc_carousel_image vci
        left join (
        select sdi.item_text,sdi.item_value from sys_dict sd left join sys_dict_item sdi on sd.id = sdi.dict_id where sd.dict_code = 'on_Off_status'
        ) dict on dict.item_value =  vci.status
        WHERE vci.del = 0
            <if test="model.name != null and model.name != ''">
                AND vci.name LIKE CONCAT('%',#{model.name},'%')
            </if>
            <if test="model.status != null and model.status != ''">
                AND vci.status = #{model.status}
            </if>
        order by vci.sort asc
    </select>

</mapper>