<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.CommentSettingMapper">

    <resultMap id="BaseResultMap" type="com.car.sound.entity.CommentSettingEntity" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="sort" property="sort" />
        <result column="min_input_num" property="minInputNum" />
        <result column="max_input_num" property="maxInputNum" />
        <result column="like_num" property="likeNum" />
        <result column="reward_integral" property="rewardIntegral" />
        <result column="status" property="status" />
        <result column="del" property="del" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                name,
                sort,
                min_input_num,
                max_input_num,
                like_num,
                reward_integral,
                status,
                del,
                create_time,
                update_time,
                create_user,
                update_user
    </sql>

    <select id="getCommentSettingList" resultType="com.car.sound.entity.CommentSettingEntity">
        SELECT
            <include refid="Base_Column_List"/>
            ,dict.item_text as statusName
        FROM voc_comment_setting vcs
        left join (
            select sdi.item_text,sdi.item_value from sys_dict sd left join sys_dict_item sdi on sd.id = sdi.dict_id where sd.dict_code = 'valid_status'
        ) dict on dict.item_value =  vcs.status
        WHERE vcs.del = 0
        <if test="model.name != null and model.name != ''">
            AND vcs.name LIKE CONCAT('%',#{model.name},'%')
        </if>
        <if test="model.status != null and model.status != ''">
            AND vcs.status = #{model.status}
        </if>
        order by vcs.sort asc
    </select>
    <select id="getAllCommentSettingList" resultType="com.car.sound.entity.CommentSettingEntity">
        SELECT
        <include refid="Base_Column_List"/>
        ,dict.item_text as statusName
        FROM voc_comment_setting vcs
        left join (
        select sdi.item_text,sdi.item_value from sys_dict sd left join sys_dict_item sdi on sd.id = sdi.dict_id where sd.dict_code = 'valid_status'
        ) dict on dict.item_value =  vcs.status
        WHERE vcs.del = 0
        and vcs.status = '1'
        order by vcs.sort asc
    </select>
    <select id="getCommentTypeList" resultType="com.car.sound.entity.CommentSettingEntity">
        SELECT
            id,
            name
        FROM voc_comment_setting vcs
        WHERE vcs.del = 0
        and vcs.status = '1'
        order by vcs.sort asc
    </select>
</mapper>