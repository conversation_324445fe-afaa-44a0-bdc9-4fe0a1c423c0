<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.UserMapper">

    <select id="getUserInfoByUserId" resultType="com.car.sound.entity.UserEntity">
        select
            cu.depart_id,
            cu.depart_name,
            cu.user_id as userId,
            cu.user_name as userName
        from cc_user_depart cu
        where  cu.user_id =  #{userId} or cu.user_name =  #{userId}
    </select>

    <select id="getDepartUser" resultType="com.car.sound.vo.UserVo">
        select
            cd.depart_id,
            cd.depart_name,
            cd.user_id as userId,
            cd.user_name as userName
        from cc_user_depart cd
    </select>
</mapper>