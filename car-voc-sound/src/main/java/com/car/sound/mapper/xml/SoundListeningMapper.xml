<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.SoundListeningMapper">

    <resultMap id="BaseResultMap" type="com.car.sound.entity.SoundListeningEntity" >
        <result column="id" property="id" />
        <result column="sound_name" property="soundName" />
        <result column="brand" property="brand" />
        <result column="car_series" property="carSeries" />
        <result column="first_label" property="firstLabel" />
        <result column="second_label" property="secondLabel" />
        <result column="three_label" property="threeLabel" />
        <result column="four_label" property="fourLabel" />
        <result column="remark" property="remark" />
        <result column="is_mandatory" property="isMandatory" />
        <result column="reward_integral" property="rewardIntegral" />
        <result column="work_order_no" property="workOrderNo" />
        <result column="work_order_type" property="workOrderType" />
        <result column="sound_duration" property="soundDuration" />
        <result column="valid_start_time" property="validStartTime" />
        <result column="valid_end_time" property="validEndTime" />
        <result column="listening_range" property="listeningRange" />
        <result column="original_sound" property="originalSound" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del" property="del" />
        <result column="status" property="status" />
        <result column="task_id" property="taskId" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                sound_name,
                brand,
                car_series,
                first_label,
                second_label,
                three_label,
                four_label,
                remark,
                is_mandatory,
                reward_integral,
                work_order_no,
                work_order_type,
                sound_duration,
                valid_start_time,
                valid_end_time,
                listening_range,
                original_sound,
                create_time,
                update_time,
                create_user,
                update_user,
                del,
                status,
                task_id
    </sql>


    <select id="getSoundByWorkOrderNo" resultType="com.car.sound.vo.WorkNoVo">
        select
            any_value(t.asr_url) as url,
            any_value(case wo.wo_type is not null
                          when wo.wo_type='投诉' then '0'
                          when wo.wo_type='咨询' then '1'
                          when wo.wo_type='建议' then '2'
                          when wo.wo_type='商机' then '3'
                          when wo.wo_type='救援' then '4'
                end) as workOrderType,
            '' as soundDuration
        from t317_csv_rescue_wo_i_d_mysql wo
                 left join t317_csv_call_in_out_wo_i_d_mysql  t on workorder_codes like concat('%',wo.wo_num , '%')
        where wo.code = #{workOrderNo}
        group by t.call_id
        limit 1
    </select>


    <select id="getSoundByWorkOrderId" resultType="com.car.sound.vo.WorkNoVo">
        select
            wo_num as workOrderId,code as workOrderNo
        from
            wo_original_data wod
        where
        <if test="workOrderNoList !=null and workOrderNoList.size()>0">
            wod.code in
            <foreach collection="workOrderNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by
            wo_num,code
    </select>


    <select id="getSoundListeningList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"></include>
        ,dict.item_text as statusName,
        GROUP_CONCAT(DISTINCT listening_range ORDER BY listening_range SEPARATOR ',') as  listeningRanges
        from
        voc_sound_listening vsl
        left join (
        select sdi.item_text,sdi.item_value from sys_dict sd left join sys_dict_item sdi on sd.id = sdi.dict_id where sd.dict_code = 'on_Off_status'
        ) dict on dict.item_value =  vsl.status
        where vsl.del = 0
            <if test="model.soundName != null and model.soundName != ''">
                and vsl.sound_name like concat('%',#{model.soundName},'%')
            </if>
            <if test="model.brand != null and model.brand != ''">
                and vsl.brand = #{model.brand}
            </if>
            <if test="model.carSeries != null and model.carSeries != ''">
                and vsl.car_series = #{model.carSeries}
            </if>
            <if test="model.firstLabel != null and model.firstLabel != ''">
                and vsl.first_label = #{model.firstLabel}
            </if>
            <if test="model.secondLabel != null and model.secondLabel != ''">
                and vsl.second_label = #{model.secondLabel}
            </if>
            <if test="model.threeLabel != null and model.threeLabel != ''">
                and vsl.three_label = #{model.threeLabel}
            </if>
            <if test="model.fourLabel != null and model.fourLabel != ''">
                and vsl.four_label = #{model.fourLabel}
            </if>
            <if test="model.startTime != null">
                and vsl.create_time >= #{model.startTime}
            </if>
            <if test="model.endTime != null">
                and vsl.create_time &lt;= #{model.endTime}
            </if>
            <if test="model.carSeriesList !=null and model.carSeriesList.size()>0">
                and vsl.car_series in
                <foreach collection="model.carSeriesList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.labelList !=null and model.labelList.size()>0">
                and vsl.four_label in
                <foreach collection="model.labelList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="model.status !=null and model.status !=''">
                and vsl.status = #{model.status}
            </if>
        group by vsl.task_id
    </select>

    <select id="getListenTaskList" resultType="com.car.sound.entity.SoundListeningEntity">
        select
        vsl.id,
        vsl.sound_name as soundName,
        vsl.brand as brand,
        vsl.car_series as carSeries,
        vsl.first_label,
        vsl.second_label,
        vsl.three_label,
        vsl.four_label,
        vsl.remark as remark,
        vsl.is_mandatory as isMandatory,
        vsl.reward_integral as rewardIntegral,
        vsl.work_order_no as workOrderNo,
        vsl.work_order_type as workOrderType,
        vsl.valid_start_time as validStartTime,
        vsl.valid_end_time as validEndTime,
        vsl.listening_range as listeningRange,
        vsl.original_sound as originalSound,
        vsl.create_time as createTime,
        vsl.sound_duration as soundDuration,
        vsl.del,
        vsl.task_id,
        if(a.ucount is null ,0,a.ucount) as completeCount
        ,if(b.ucount is null ,0,b.ucount) as listenCount
        ,if(c.ucount is null ,0,c.ucount) as listenTimes
        ,if(d.ucount is null ,0,d.ucount) as commentCount
        ,if(e.ucount is null ,0,e.ucount) as commentTimes,
        if(f.ucount is null ,0,f.ucount)  as favoriteCount
        from voc_sound_listening vsl
        left join (select count(1) as ucount,task_id as tid from voc_listen_record where status = '1' group by  task_id) a on a.tid = vsl.task_id
        left join (select count(1) as ucount,task_id as tid from voc_listen_record group by  task_id)  b on b.tid = vsl.task_id
        left join (select sum(listen_times) as ucount,task_id as tid from voc_listen_record  group by task_id)  c on c.tid = vsl.task_id
        left join (select count(1)as ucount, a.tid from (select comment_user,task_id as tid from voc_comment group by task_id,comment_user) a group by a.tid)  d on d.tid = vsl.task_id
        left join (select count(1) as ucount,task_id as tid from voc_comment  group by task_id) e on e.tid = vsl.task_id
        left join (select count(1) as ucount, task_id as tid from voc_favorite where del = 0 group by task_id) f on f.tid = vsl.task_id
        where vsl.del = 0
        <if test="model.userId !=null and model.userId !=''">
            and vsl.status = '1'
        </if>
        <if test="model.departId !=null and model.departId !=''">
                and vsl.listening_range like CONCAT('%',#{model.departId},'%')
        </if>
        <if test="model.workOrderType !=null and model.workOrderType !=''">
            and vsl.work_order_type = #{model.workOrderType}
        </if>
        <if test="model.label !=null and model.label !=''">
            and (vsl.four_label = #{model.label} or vsl.three_label = #{model.label} or vsl.second_label = #{model.label} or vsl.first_label = #{model.label})
        </if>
        <if test="model.brandCode !=null and model.brandCode !=''">
            and vsl.brand = #{model.brandCode}
        </if>
        <if test="model.carSeriesCode !=null and model.carSeriesCode !=''">
            and vsl.car_series = #{model.carSeriesCode}
        </if>
        <if test="model.startTime !=null">
            and vsl.valid_start_time &lt;= #{model.startTime}
        </if>
        group by vsl.task_id
        <if test="model.order !=null and model.order !=''">
            order by ${model.order}
        </if>
        <if test="model.order ==null or model.order ==''">
            order by vsl.create_time desc
        </if>

    </select>
    <select id="getListenTaskStatistics" resultType="com.car.sound.vo.ListenTaskStatisticsVo">
        select
            if((select sum(integral) from voc_integral where user_id = #{userId} group by user_id) is null,0,(select sum(integral) from voc_integral where user_id = #{userId} group by user_id)) as totalIntegral,
            (select count(id) from voc_comment where comment_user = #{userId}) as commentCount,
            (select count(id) from voc_favorite where user_id = #{userId} and del = 0) as favoriteCount,
            (select count(1) from voc_listen_record vlr
             left join voc_sound_listening vsl on vsl.task_id = vlr.task_id
             where vlr.user_id = #{userId}
               and vsl.listening_range = #{departId}
               and vlr.status = '1'
               and vsl.del = 0
               and vsl.status = 1
               and vsl.is_mandatory = 1
             ) as mandatoryCount,
            (select count(1) from voc_listen_record vlr
                left join voc_sound_listening vsl on vsl.task_id = vlr.task_id
             where vlr.user_id = #{userId}
               and vsl.listening_range = #{departId}
               and vlr.status = '1'
               and vsl.del = 0
               and vsl.status = 1
               and vsl.is_mandatory = 0
            ) as notMandatoryCount,
            (select count(id) from voc_sound_listening where listening_range = #{departId} and del = 0 and status = 1 and is_mandatory = 1) as mandatoryTotalCount,
            (select count(id) from voc_sound_listening where listening_range = #{departId} and del = 0 and status = 1 and is_mandatory = 0) as notMandatoryTotalCount
    </select>
    <select id="getSoundListenDataStatistics" resultType="com.car.sound.vo.ListenTaskStatisticsVo">
        select
            (select if(sum(vlr.ucount) is null ,0,sum(vlr.ucount))  from (select count(user_id) as ucount from voc_listen_record where  task_id = #{model.taskId}  and status = '1' group by user_id) as vlr) as completeCount,
            (select if(sum(a.ucount) is null ,0,sum(a.ucount))  from (select count(user_id) as ucount from voc_listen_record where task_id = #{model.taskId} group by user_id) as a) as listenCount,
            (select if(b.ucount is null,0,sum(b.lt))  from (select count(task_id) as ucount,sum(listen_times) as lt from voc_listen_record where task_id = #{model.taskId} group by task_id) as b) as listenTimes,
            (select if(sum(c.ucount) is null,0,sum(c.ucount))  from (select count(1) as ucount from (select comment_user from voc_comment where task_id = #{model.taskId} group by comment_user) a) as c)as commentCount,
            (select if(sum(d.ucount) is null,0,d.ucount)  from (select count(1) as ucount from voc_comment where task_id = #{model.taskId} group by task_id) as d) as commentTimes,
            (select if(sum(e.ucount) is null,0,sum(e.ucount))  from (select count(task_id) as ucount from voc_favorite where task_id = #{model.taskId} and del = 0 group by task_id) as e) as favoriteCount
    </select>
    <select id="getSoundListenDataList" resultType="com.car.sound.entity.SoundListeningEntity">
        select
            a.departName as departName,
            a.userName   as userName,
            a.userId as userId,
            a.isComment   as isComment,
            if(vlr.status is not null, (if(vlr.status = '1', '是', '否')), '否') as isComplete,
            if(vlr.create_time is not null ,vlr.create_time,null) as joinTime
        from (
            select
                cud.depart_name                             as departName,
                cud.user_name                               as userName,
                cud.user_id                                 as userId,
                if(vc.comment_user is not null, '是', '否') as isComment,
                vsl.task_id                                 as taskId
            from voc_sound_listening vsl
            left join cc_user_depart cud on cud.depart_id = vsl.listening_range
            LEFT JOIN voc_comment vc ON vc.comment_user = cud.user_id and vc.task_id = vsl.task_id
            where vsl.task_id = #{model.taskId}
            and vsl.del = '0'
            and cud.id is not null
        <if test="model.listeningRange !=null and model.listeningRange.size()>0">
            and vsl.listening_range in
            <foreach collection="model.listeningRange" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.account !=null and model.account !=''">
            and cud.user_name like CONCAT('%',#{model.account},'%')
        </if>
        group by cud.depart_id,cud.user_id
            ) a
        left join voc_listen_record vlr ON vlr.user_id = a.userId and vlr.task_id = a.taskId
        where 1=1
        <if test="model.startTime !=null">
            and  vlr.create_time >= #{model.startTime}
        </if>
        <if test="model.endTime !=null">
            and  vlr.create_time &lt;= #{model.endTime}
        </if>
        <if test="model.status !=null and model.status !=''">
            and  vlr.status = #{model.status}
        </if>
    </select>
    <select id="getAllSoundListenDataStatistics" resultType="com.car.sound.vo.ListenTaskStatisticsVo">
        select
        (select if(sum(vlr.ucount) is null ,0,sum(vlr.ucount))  from (select count(user_id) as ucount from voc_listen_record where  status = '1' group by user_id) as vlr) as completeCount,
        (select if(sum(a.ucount) is null ,0,sum(a.ucount))  from (select count(user_id) as ucount from voc_listen_record  group by user_id) as a) as listenCount,
        (select if(sum(b.ucount) is null,0,count(b.ucount))  from (select count(task_id) as ucount from voc_listen_record group by task_id) as b) as listenNum,
        (select if(sum(c.ucount) is null,0,sum(c.ucount))  from (select count(comment_user) as ucount from voc_comment group by comment_user) as c)as commentCount,
        (select if(sum(d.ucount) is null,0,sum(d.ucount))  from (select count(task_id) as ucount from voc_comment  group by task_id) as d) as commentNum,
        (select if(sum(e.ucount) is null,0,sum(e.ucount))  from (select count(task_id) as ucount from voc_favorite  group by task_id) as e) as favoriteCount
    </select>
    <select id="exportSoundListenDataSize" resultType="java.lang.Integer">
        select
            count(*)
        from voc_sound_listening vsl
        left join voc_listen_record vlr ON vlr.task_id  = vsl.task_id
        left join cc_user_depart cud on cud.depart_id  = vsl.listening_range
        LEFT JOIN voc_comment vc ON vc.comment_user = cud.user_id
        where
        vsl.task_id = #{model.taskId}
        and vsl.del = '0'
        <if test="model.listeningRange !=null and model.listeningRange.size()>0">
            and vsl.listening_range in
            <foreach collection="model.listeningRange" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="model.account !=null and model.account !=''">
            and cud.user_account like CONCAT('%',#{model.account},'%')
        </if>
        <if test="model.status !=null and model.status !=''">
            and  vlr.status = #{model.status}
        </if>
        <if test="model.startTime !=null">
            and  vlr.create_time >= #{model.startTime}
        </if>
        <if test="model.endTime !=null">
            and  vlr.create_time &lt;= #{model.endTime}
        </if>
        limit 10000
    </select>
    <select id="getListenTaskInfoData" resultType="com.car.sound.entity.SoundListeningEntity">
        select * from voc_listen_record where user_id = #{model.userId} and task_id in
        <foreach collection="model.taskIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSoundListeningRange" resultType="com.car.sound.model.ListeningRangeModel">
        select
            listening_range as id,
            level as  level,
            is_leaf as isLeaf
        from voc_sound_listening
        where task_id = #{taskId}
        and del = '0'
    </select>
</mapper>