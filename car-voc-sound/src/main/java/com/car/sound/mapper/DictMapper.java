package com.car.sound.mapper;

import com.car.sound.vo.DictItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 15:02
 * @描述:
**/
@Mapper
@Repository
public interface DictMapper {
    List<DictItemVo> getDictItemByDictCode(@Param("dictCode") String dictCode);
}
