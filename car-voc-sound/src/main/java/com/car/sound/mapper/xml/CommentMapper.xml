<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.CommentMapper">
    <resultMap id="BaseResultMap" type="com.car.sound.entity.CommentEntity" >
        <result column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="comment_type" property="commentType" />
        <result column="comment" property="comment" />
        <result column="comment_user" property="commentUser" />
        <result column="create_time" property="createTime" />
        <result column="reply_user" property="replyUser" />
        <result column="reply" property="reply" />
        <result column="reply_time" property="replyTime" />
        <result column="like_count" property="likeCount" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                task_id,
                comment_type,
                comment,
                comment_user,
                create_time,
                reply_user,
                reply,
                reply_time,
                like_count
    </sql>
    <update id="saveUpvote">
        update voc_comment set like_count = like_count + 1 where id = #{model.id}
    </update>
    <delete id="cancelUpvote">
        update voc_comment set like_count = like_count - 1 where id = #{model.id}
    </delete>

    <select id="getCommentList" resultType="com.car.sound.entity.CommentEntity">
        select
            vc.id,
            vc.task_id,
            vc.comment_type,
            vc.comment,
            vc.comment_user,
            vc.create_time,
            cud1.user_name as reply_user,
            vc.reply,
            vc.reply_time,
            vc.like_count as likeCount
            ,cud.user_name as account,
            cud.depart_name as departName
        from voc_comment vc
         left join cc_user_depart cud on vc.comment_user = cud.user_id
         left join cc_user_depart cud1 on cud1.user_id = vc.reply_user
        where vc.task_id = #{model.taskId}
        <if test="model.order !=null and model.order !=''">
            order by ${model.order}
        </if>
        <if test="model.order ==null or model.order ==''">
            order by vc.create_time desc
        </if>
    </select>
    <select id="getCommentListByTaskId" resultType="com.car.sound.entity.CommentEntity">
        select
        vc.id,
        cud.user_name as account,
        cud.depart_name as departName,
        vc.comment_type as commentType,
        vcs.name as commentTypeName,
        vc.comment as comment,
        vc.like_count as likeCount,
        vc.create_time as createTime,
        vc.reply as reply,
        vc.reply_time as replyTime,
        vc.reply_user as replyUser,
        if(vc.reply is not null ,true,false) as isReply,
        case
        when vu.id is not null  then true
        else false
        end as isUpvote
        from  voc_comment vc
        left join cc_user_depart cud on cud.user_id = vc.comment_user
        left join voc_comment_setting vcs on vcs.id = vc.comment_type
        left join voc_upvote_user vu on vu.comment_id = vc.id
        where vc.task_id = #{model.taskId}
        <if test="model.commentType != null">
            and vc.comment_type like CONCAT('%',#{model.commentType} ,'%')
        </if>
        <if test="model.account !=null and model.account !=''">
            and cud.user_name like CONCAT('%', #{model.account} ,'%')
        </if>
        <if test="model.listeningRange !=null and model.listeningRange.size()>0">
            and cud.depart_id in
            <foreach item="item" index="index" collection="model.listeningRange" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="model.isReply !=null and model.isReply != '' ">
            <if test="model.isReply == '1'.toString()">
                and vc.reply is not null
            </if>
            <if test="model.isReply == '0'.toString()">
                and vc.reply is null
            </if>
        </if>
        <if test="model.commentStartTime !=null ">
            and vc.create_time >= #{model.commentStartTime}
        </if>
        <if test="model.commentEndTime !=null">
            and vc.create_time &lt;= #{model.commentEndTime}
        </if>
        <if test="model.replyStartTime !=null">
            and vc.reply_time >= #{model.replyStartTime}
        </if>
        <if test="model.replyEndTime !=null">
            and vc.reply_time &lt;= #{model.replyEndTime}
        </if>
        group by vc.id
        order by vc.create_time desc
    </select>
    <select id="getCommentListCountByTaskId" resultType="java.lang.Integer">
        select
            count(*)
        from  voc_comment vc
        left join cc_user_depart  cud on cud.user_id = vc.comment_user
        left join voc_comment_setting vcs on vcs.id = vc.comment_type
        left join voc_upvote_user vu on vu.comment_id = vc.id
        where vc.task_id = #{model.taskId}
        <if test="model.commentType != null">
            and vc.comment_type = #{model.commentType}
        </if>
        <if test="model.account !=null and model.account !=''">
            and cud.user_name like CONCAT('%', #{model.account} ,'%')
        </if>
        <if test="model.listeningRange !=null and model.listeningRange.size()>0">
            and cud.depart_id in
            <foreach item="item" index="index" collection="model.listeningRange" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.isReply !=null and model.isReply != '' ">
            <if test="model.isReply = '1'">
                and vc.reply is not null
            </if>
            <if test="model.isReply = '0'">
                and vc.reply is null
            </if>
        </if>
        <if test="model.commentStartTime !=null and model.commentStartTime != ''">
            and vc.create_time >= #{model.commentStartTime}
        </if>
        <if test="model.commentEndTime !=null and model.commentEndTime != ''">
            and vc.create_time &lt;= #{model.commentEndTime}
        </if>
        <if test="model.replyStartTime !=null and model.replyStartTime != ''">
            and vc.reply_time >= #{model.replyStartTime}
        </if>
        <if test="model.replyEndTime !=null and model.replyEndTime != ''">
            and vc.reply_time &lt;= #{model.replyEndTime}
        </if>
        order by vc.create_time desc
    </select>
</mapper>