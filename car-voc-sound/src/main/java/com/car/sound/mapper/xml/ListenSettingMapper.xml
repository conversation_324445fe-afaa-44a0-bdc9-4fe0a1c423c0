<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.ListenSettingMapper">
    <resultMap id="BaseResultMap" type="com.car.sound.entity.ListenSettingEntity" >
        <result column="id" property="id" />
        <result column="department" property="department" />
        <result column="account" property="account" />
        <result column="permission" property="permission" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                department,
                account,
                permission,
                status,
                remark,
                create_time,
                update_time,
                create_user,
                update_user
    </sql>
    <select id="getListenSettingList" resultType="com.car.sound.entity.ListenSettingEntity">
        select
            vls.id,
            vls.department,
            cd.user_name as name,
            cd.brand as brand,
            vls.permission,
            vls.status,
            vls.remark,
            vls.create_time,
            vls.update_time,
            vls.create_user,
            vls.update_user
            ,cd.depart_name
            ,cd.user_account as account
            ,cd.user_id as userId
        from voc_listen_setting vls
        left join cc_user_depart cd on cd.depart_id = vls.department and cd.user_id = vls.account
        where 1=1
        <if test="model.name !=null and model.name !=''">
        and (cd.user_name like CONCAT('%',#{model.name},'%') or  cd.user_account like CONCAT('%',#{model.name},'%'))
        </if>
        order by vls.create_time desc
    </select>
</mapper>