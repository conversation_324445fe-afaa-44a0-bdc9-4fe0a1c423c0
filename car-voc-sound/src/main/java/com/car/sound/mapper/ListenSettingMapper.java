package com.car.sound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.entity.ListenSettingEntity;
import com.car.sound.model.ListenSettingModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 13:44
 * @描述:
 **/
@Mapper
@Repository
public interface ListenSettingMapper extends BaseMapper<ListenSettingEntity> {

    IPage<ListenSettingEntity> getListenSettingList(IPage<ListenSettingEntity> page, @Param("model")ListenSettingModel settingModel);
}
