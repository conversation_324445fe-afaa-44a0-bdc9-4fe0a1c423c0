package com.car.sound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.entity.CommentEntity;
import com.car.sound.model.CommentModel;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.vo.CommentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 15:49
 * @描述:
 **/
@Mapper
@Repository
public interface CommentMapper extends BaseMapper<CommentEntity> {

    void saveUpvote(@Param("model") CommentModel model);

    IPage<CommentEntity> getCommentList(IPage<CommentEntity> page, @Param("model") ListenTaskModel model);

    IPage<CommentEntity> getCommentListByTaskId(IPage<CommentEntity> page, @Param("model") CommentModel model);

    Integer getCommentListCountByTaskId(@Param("model") CommentModel model);

    void cancelUpvote(@Param("model") CommentModel model);
}
