<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.sound.mapper.HomePageMapper">

    <select id="getDepartRanking" resultType="com.car.sound.vo.RankingListVo">
        select if(ROUND((a.completeCount / b.completeCount) * 100, 0) is null, 0,if(ROUND((a.completeCount / b.completeCount) * 100, 0)>100,100,ROUND((a.completeCount / b.completeCount) * 100, 0))) as completeRate,
        concat(if(a.brand is null,'',a.brand), '-',a.departName) as departName
        from (select if(a.ucount is not null,a.ucount,0 ) as completeCount,cd.depart_id,cd.depart_name as departName,cd.brand as brand from cc_user_depart cd
        left join (select count(vsl.listening_range) as  ucount, vsl.listening_range
        from voc_listen_record vlr
        right join voc_sound_listening vsl
        on vsl.listening_range = vlr.depart_id and vsl.task_id = vlr.task_id
        where vsl.is_mandatory = true
        and vlr.status = '1'
        and vsl.del = 0
        and vsl.valid_end_time >= vlr.success_time
        <if test="month !=null and month !=''">
            and DATE_FORMAT(vlr.success_time, '%Y-%m')= #{month}
            and DATE_FORMAT(vsl.create_time, '%Y-%m')= #{month}
        </if>
        group by vsl.listening_range) as a on a.listening_range = cd.depart_id
        group by cd.depart_id) as a
        left join
        (select count(cd.depart_id) as completeCount,
        cd.depart_name  as departName,
        cd.depart_id
        from voc_sound_listening vsl
        left join cc_user_depart cd on cd.depart_id = vsl.listening_range
        where vsl.is_mandatory = true and  cd.depart_name  is not null
        and vsl.del = 0
        <if test="month !=null and month !=''">
            and DATE_FORMAT(vsl.create_time, '%Y-%m')= #{month}
        </if>
        group by cd.depart_id) as b  on a.depart_id = b.depart_id
        order by completeRate desc
    </select>
    <select id="getPersonRanking" resultType="com.car.sound.vo.RankingListVo">
        select
            sum(integral) as integral,
            cu.user_name as userName,
            vi.user_id as userId
        from voc_integral vi
        left join cc_user_depart cu on cu.user_id = vi.user_id
        where 1=1
        <if test="month !=null and month !=''">
            and DATE_FORMAT(vi.create_time, '%Y-%m')= #{month}
        </if>
        group by vi.user_id, cu.user_name
        order by integral desc
    </select>
    <select id="getLabelVo" resultType="com.car.sound.vo.LabelVo">
        select id,name,pid,tag_code as code from voc_business_tag where enable = 1
        union all
        select id,name,pid,code from voc_fault_problem where enable = 1
    </select>
    <select id="UpwardFindBusinessLabelHierarchical" resultType="com.car.sound.vo.LabelVo">
        WITH RECURSIVE CTE AS (
            SELECT * FROM voc_business_tag
            WHERE tag_code = #{labelCode}
            UNION ALL
            SELECT t.* FROM voc_business_tag t INNER JOIN CTE ON t.id = CTE.pid
        )
        SELECT
            CTE.id,CTE.tag_code as code,CTE.name
        FROM CTE group by id ORDER BY id
    </select>
    <select id="getBrandCarSeriesByCodes" resultType="com.car.sound.vo.BrandVo">
        select code as `label`,  name as `value` from voc_brand_product_manager where code in
        <foreach item="item" collection="cods" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>
    <select id="UpwardFindQYTopLabelHierarchical" resultType="com.car.sound.vo.LabelVo">
        WITH RECURSIVE CTE AS (
            SELECT * FROM voc_fault_problem
            WHERE code = #{labelCode}
            UNION ALL
            SELECT t.* FROM voc_fault_problem t INNER JOIN CTE ON t.id = CTE.pid
        )
        SELECT
            CTE.id,CTE.code,CTE.name
        FROM CTE where pid = 0 group by id ORDER BY id
    </select>
    <select id="UpwardFindQyLabelHierarchical" resultType="com.car.sound.vo.LabelVo">
        WITH RECURSIVE CTE AS (
            SELECT * FROM voc_fault_problem
            WHERE code = #{labelCode}
            UNION ALL
            SELECT t.* FROM voc_fault_problem t INNER JOIN CTE ON t.id = CTE.pid
        )
        SELECT
            CTE.id,CTE.code,CTE.name
        FROM CTE
        group by id ORDER BY id
    </select>
    <select id="UpwardFindBusinessTopLabelHierarchical" resultType="com.car.sound.vo.LabelVo">
        WITH RECURSIVE CTE AS (
            SELECT * FROM voc_business_tag
            WHERE tag_code = #{labelCode}
            UNION ALL
            SELECT t.* FROM voc_business_tag t INNER JOIN CTE ON t.id = CTE.pid
        )
        SELECT
            CTE.id,CTE.tag_code as code,CTE.name
        FROM CTE where pid = 0 group by id ORDER BY id
    </select>
</mapper>