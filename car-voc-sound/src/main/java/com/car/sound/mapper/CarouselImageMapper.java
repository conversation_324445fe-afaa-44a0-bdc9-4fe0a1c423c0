package com.car.sound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.entity.CarouselImageEntity;
import com.car.sound.model.CarouselImageModel;
import com.car.sound.model.CommentSettingModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 13:46
 * @描述:
 **/
@Mapper
@Repository
public interface CarouselImageMapper extends BaseMapper<CarouselImageEntity> {

    IPage<CarouselImageEntity> getCarouselImageList(IPage<CarouselImageEntity> page, @Param("model") CarouselImageModel model);
}
