package com.car.sound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.car.sound.entity.LargeFilesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @创建者: cuick
 * @创建时间: 2024/6/11 13:23
 * @描述:
 **/
@Mapper
@Repository
public interface LargeFileMapper extends BaseMapper<LargeFilesEntity> {

    List<LargeFilesEntity> getFileList(LargeFilesEntity entity);

    LargeFilesEntity getFile(LargeFilesEntity entity);
}
