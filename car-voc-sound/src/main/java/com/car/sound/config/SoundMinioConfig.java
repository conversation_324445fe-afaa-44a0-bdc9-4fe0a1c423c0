package com.car.sound.config;


import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

/**
 * MinIO配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
@Slf4j
public class SoundMinioConfig {

    /**
     * endPoint是一个URL，域名，IPv4或者IPv6地址
     */
    @Value("${minio.oss.endpoint}")
    private String endpoint;

    /**
     * accessKey类似于用户ID，用于唯一标识你的账户
     */
    @Value("${minio.oss.accessKeyId}")
    private String accessKey;

    /**
     * secretKey是你账户的密码
     */
    @Value("${minio.oss.accessKeySecret}")
    private String secretKey;
    @Value("${minio.oss.web_endpoint}")
    private String webEndpoint;
    @Value("${minio.oss.bucketName}")
    private String bucketName;


    @Bean(name = "soundMinioClient")
    public MinioClient getMinioClient() throws Exception {
        // 创建信任所有证书的 OkHttpClient
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .sslSocketFactory(createTrustAllSslSocketFactory(), createTrustAllManager())
                .hostnameVerifier((hostname, session) -> true)
                .build();
        log.info("正在初始化MinIO...");
        MinioClient minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .httpClient(httpClient)
                .build();
        log.info("初始化MinIO成功!");
        return minioClient;
    }

    private X509TrustManager createTrustAllManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {}
            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {}
            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() { return new java.security.cert.X509Certificate[]{}; }
        };
    }

    private SSLSocketFactory createTrustAllSslSocketFactory() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, new TrustManager[]{createTrustAllManager()}, new java.security.SecureRandom());
        return sslContext.getSocketFactory();
    }
}
