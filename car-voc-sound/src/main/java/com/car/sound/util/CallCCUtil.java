package com.car.sound.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.car.sound.vo.CCBrandVo;
import com.car.sound.vo.CCDepartVo;
import com.car.sound.vo.CCUserVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/6/3 17:50
 * @描述:
 **/
@Slf4j
@Service
public class CallCCUtil {
    @Value("${configuration.tokenCheckToken.clientId}")
     String clientId;
    @Value("${configuration.tokenCheckToken.clientSecret}")
     String clientSecret;
    @Value("${configuration.tokenCheckToken.baseUrl}")
     String baseUrl;
    @Value("${configuration.tokenCheckToken.bffBaseUrl}")
     String bffBaseUrl;

    private   String GetToken() {
        String url = baseUrl+"/oauth2/oauth/token";
        String requestBody = "grant_type=client_credentials&client_id="+clientId+"&client_secret="+clientSecret;
        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/x-www-form-urlencoded");
        request.body(requestBody);
        HttpResponse response = request.execute();
        log.info("获取token返回结果：{}", JSONUtil.parseObj(response.body()));
        return JSONUtil.parseObj(response.body()).getStr("access_token");
    }


    /**
     * 获取组织
     * @return
     */
    public  List<CCDepartVo> fromCCGetOrganization(String brand) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(bffBaseUrl);
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", "Bearer " + GetToken());
        post.setHeader("X-Brands", brand);

        String graphqlQuery = "{\n" +
                "    \"query\": \"query  {\\n  searchTiledOrganization(retainEmptyBrand: true) {\\n    id\\n    orgName\\n    parentId\\n  brands {\\n      name\\n    }\\n}\\n  listRootOrganization {\\n    id\\n    orgName\\n  }\\n}\"\n" +
                "}";
        try {
            post.setEntity(new StringEntity(graphqlQuery));
        }catch (Exception exception ){
            exception.printStackTrace();
        }
        List<CCDepartVo> ccDepartVos = new ArrayList<>();
        try (CloseableHttpResponse response = client.execute(post)) {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(response.getEntity().getContent());
            JsonNode top = root.path("data").path("listRootOrganization");
            JsonNode child = root.path("data").path("searchTiledOrganization");

            if(ObjectUtils.isEmpty(top)){
                log.info("暂无部门信息");
            }else{
                top.forEach(e->{
                    try {
                        CCDepartVo ccDepartVo = mapper.treeToValue(e, CCDepartVo.class);
                        ccDepartVos.add(ccDepartVo);
                    } catch (JsonProcessingException ex) {
                        throw new RuntimeException(ex);
                    }
                });
            }
            if(ObjectUtils.isEmpty(child)){
                log.info("暂无子部门信息");
            }else{
                child.forEach(e->{
                    try {
                        CCDepartVo ccDepartVo = mapper.convertValue(e, CCDepartVo.class);
                        JsonNode jsonNode = e.get("brands");
                        if(jsonNode.isArray()){
                            ArrayNode aarrayNode = (ArrayNode) jsonNode;
                            List<CCBrandVo> brandVos = mapper.readValue(
                                        aarrayNode.traverse(),
                                        mapper.getTypeFactory().constructCollectionType(List.class, CCBrandVo.class)
                                );
//                            List<CCBrandVo> brandVos = mapper.convertValue(aarrayNode, new TypeReference<List<CCBrandVo>>() {});
                            ccDepartVo.setBrands(brandVos);
                        }
                        ccDepartVos.add(ccDepartVo);
                    } catch (IOException ex) {
                        throw new RuntimeException(ex);
                    }
                });
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return ccDepartVos;
    }


    public  List<CCUserVo> fromCCGetUser(String brand, String departId) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(bffBaseUrl);
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", "Bearer " + GetToken());
        post.setHeader("X-Brands", brand);

        Integer pageNum = 1;
        Integer pageSize = 500;
        Integer total = 1;
        List<CCUserVo> ccUserVos = new ArrayList<>();
        while (total > ccUserVos.size()){
            String graphqlQuery = String.format("{\n" +
                            "    \"query\": \"query  {\\n  pageEmployee(\\n    page: {currentPage: %s, pageSize: %s}\\n    search: {organizationIdIn: \\\"%s\\\"}\\n  ) {\\n    list {\\n      account\\n      id\\n      name\\n    }\\n    total\\n  }\\n}\"\n" +
                            "}",
                    pageNum,
                    pageSize,
                    departId
            );
            try {
                post.setEntity(new StringEntity(graphqlQuery));
            } catch (Exception exception) {
                exception.printStackTrace();
            }

            try (CloseableHttpResponse response = client.execute(post)) {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode root = mapper.readTree(response.getEntity().getContent());
                JsonNode userList = root.path("data").path("pageEmployee").path("list");
                total = root.path("data").path("pageEmployee").path("total").asInt();
                if (ObjectUtils.isEmpty(userList)) {
                    log.info("暂无用户信息");
                } else {
                    userList.forEach(e -> {
                        try {
                            CCUserVo ccUserVo = mapper.treeToValue(e, CCUserVo.class);
                            ccUserVos.add(ccUserVo);
                        } catch (JsonProcessingException ex) {
                            throw new RuntimeException(ex);
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            pageNum++;
        }
        return ccUserVos;
    }
}
