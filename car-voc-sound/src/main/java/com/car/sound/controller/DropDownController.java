package com.car.sound.controller;

import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.service.DropDownService;
import com.car.sound.vo.BrandVo;
import com.car.sound.vo.LabelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 13:48
 * @描述:
**/
@RestController
@Api(tags = "品牌车系")
@RequestMapping("/sound/dropDown")
@AllArgsConstructor
@Slf4j
public class DropDownController {
    @Autowired
    private DropDownService brandCarSeriesService;

    @ApiOperation(value = "获取品牌车系树", notes = "获取品牌车系树")
    @GetMapping("/getBrandCarSeriesTree")
    public Result<?> getBrandCarSeriesTree() {
        try {
            List<BrandVo> brandCarSeriesTree = brandCarSeriesService.getBrandCarSeriesTree();
            return Result.OK(brandCarSeriesTree);
        }catch (IllegalArgumentException e){
            log.error("获取品牌车系树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取品牌车系树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取品牌车系树失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "获取品牌车系树", notes = "获取品牌车系树")
    @GetMapping("/getBrandCarSeriesTrees")
    public Result<?> getBrandCarSeriesTrees() {
        try {
            List<BrandVo> brandCarSeriesTree = brandCarSeriesService.getBrandCarSeriesTrees();
            return Result.OK(brandCarSeriesTree);
        }catch (IllegalArgumentException e){
            log.error("获取品牌车系树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取品牌车系树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取品牌车系树失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "获取标签树", notes = "获取标签树")
    @GetMapping("/getLabelTree")
    public Result<?> getLabelTree() {
        try {
            List<LabelVo> labelTree = brandCarSeriesService.getLabelTree();
            return Result.OK(labelTree);
        }catch (IllegalArgumentException e){
            log.error("获取标签树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取标签树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取标签树失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }
    
}
