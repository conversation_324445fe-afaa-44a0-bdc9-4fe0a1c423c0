package com.car.sound.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.CommentModel;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.model.SoundListeningModel;
import com.car.sound.service.ListenTaskService;
import com.car.sound.vo.CommentVo;
import com.car.sound.vo.ListenTaskStatisticsVo;
import com.car.sound.vo.ListenTaskVo;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 17:55
 * @描述:
 **/
@RestController
    @Api(tags = "聆听任务")
@RequestMapping("/sound/listenTask")
@AllArgsConstructor
@Slf4j
public class ListenTaskController {
    @Autowired
    private  ListenTaskService listenTaskService;


    @ApiOperation(value = "聆听任务-获取聆听任务列表", notes = "聆听任务-获取聆听任务列表")
    @PostMapping("/getListenTaskList")
    public Result<?> getListenTaskList(@RequestBody ListenTaskModel model) {
        try {
            IPage<ListenTaskVo> listenTaskList = listenTaskService.getListenTaskList(model);
            return Result.OK(listenTaskList);
        }catch (IllegalArgumentException e){
            log.error("获取聆听任务列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取聆听任务列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取聆听任务列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听任务-获取聆听任务统计", notes = "聆听任务-获取聆听任务统计")
    @PostMapping("/getListenTaskStatistics")
    public Result<?> getListenTaskStatistics() {
        try {
            ListenTaskStatisticsVo listenTaskStatistics = listenTaskService.getListenTaskStatistics();
            return Result.OK(listenTaskStatistics);
        }catch (IllegalArgumentException e){
            log.error("获取聆听任务统计失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取聆听任务统计失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取聆听任务统计失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "聆听任务-保存或更新聆听任务", notes = "聆听任务-保存或更新聆听任务")
    @PostMapping("/saveOrUpdateListenTask")
    public Result<?> saveOrUpdateListenTask(@RequestBody ListenTaskModel model) {
        try {
            listenTaskService.saveOrUpdateListenTask(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("保存或更新聆听任务失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("保存或更新聆听任务失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("保存或更新聆听任务失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "聆听任务-保存评论", notes = "聆听任务-保存评论")
    @PostMapping("/saveComment")
    public Result<?> saveComment(@RequestBody CommentModel model) {
        try {
            listenTaskService.saveComment(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("保存评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("保存评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("保存评论失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "聆听任务-点赞评论", notes = "聆听任务-点赞评论")
    @PostMapping("/saveUpvote")
    public Result<?> saveUpvote(@RequestBody CommentModel model) {
        try {
            listenTaskService.saveUpvote(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("点赞评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("点赞评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("点赞评论失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听任务-取消点赞评论", notes = "聆听任务-取消点赞评论")
    @PostMapping("/cancelUpvote")
    public Result<?> cancelUpvote(@RequestBody CommentModel model) {
        try {
            listenTaskService.cancelUpvote(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("取消点赞评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("取消点赞评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("取消点赞评论失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听任务-收藏任务", notes = "聆听任务-收藏任务")
    @PostMapping("/saveFavorite")
    public Result<?> saveFavorite(@RequestBody ListenTaskModel model) {
        try {
            listenTaskService.saveFavorite(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("收藏任务失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("收藏任务失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("收藏任务失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听任务-取消收藏任务", notes = "聆听任务-取消收藏任务")
    @PostMapping("/cancelFavorite")
    public Result<?> cancelFavorite(@RequestBody ListenTaskModel model) {
        try {
            listenTaskService.cancelFavorite(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("取消收藏任务失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("取消收藏任务失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("取消收藏任务失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听任务-根据任务id获取评论列表", notes = "聆听任务-根据任务id获取评论列表")
    @PostMapping("/getCommentList")
    public Result<?> getCommentList(@RequestBody ListenTaskModel model) {
        try {
            IPage<CommentVo> commentList = listenTaskService.getCommentList(model);
            return Result.OK(commentList);
        }catch (IllegalArgumentException e){
            log.error("根据任务id获取评论列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("根据任务id获取评论列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("根据任务id获取评论列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }
}
