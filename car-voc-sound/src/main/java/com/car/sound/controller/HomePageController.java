package com.car.sound.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.ListenSettingModel;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.service.HomePageService;
import com.car.sound.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/19 17:53
 * @描述:
 **/
@RestController
@Api(tags = "首页")
@RequestMapping("/sound/homePage")
@AllArgsConstructor
@Slf4j
public class HomePageController {
    @Autowired
    private HomePageService homePageService;

    @ApiOperation(value = "首页-获取首页聆听任务列表", notes = "首页-获取首页聆听任务列表")
    @PostMapping("/getSoundListeningTaskList")
    public Result<?> getSoundListeningTaskList(@RequestBody ListenTaskModel model) {
        try {
            IPage<ListenTaskVo> soundListeningTaskList = homePageService.getSoundListeningTaskList(model);
            return Result.OK(soundListeningTaskList);
        }catch (IllegalArgumentException e){
            log.error("获取首页聆听任务列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取首页聆听任务列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取首页聆听任务列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "首页-获取首页个人排行榜", notes = "首页-获取首页个人排行榜")
    @PostMapping("/getPersonRanking")
    public Result<?> getPersonRanking() {
        try {
            PersonRankingVo personRanking = homePageService.getPersonRanking();
            return Result.OK(personRanking);
        }catch (IllegalArgumentException e){
            log.error("获取首页个人排行榜失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取首页个人排行榜失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取首页个人排行榜失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "首页-获取首页部门排行榜", notes = "首页-获取首页部门排行榜")
    @PostMapping("/getDepartRanking")
    public Result<?> getDepartRanking() {
        try {
            DepartRankingVo departRanking = homePageService.getDepartRanking();
            return Result.OK(departRanking);
        }catch (IllegalArgumentException e){
            log.error("获取首页部门排行榜失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取首页部门排行榜失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取首页部门排行榜失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "首页-获取首页意图列表", notes = "首页-获取首页意图列表")
    @PostMapping("/getIntentionList")
    public Result<?> getIntentionList(@RequestBody ListenTaskModel model) {
        try {
            List<IntentionVo> intentionList = homePageService.getIntentionList(model);
            return Result.OK(intentionList);
        }catch (IllegalArgumentException e){
            log.error("获取首页意图列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取首页意图列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取首页意图列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "首页-获取首页轮播图列表", notes = "首页-获取首页轮播图列表")
    @PostMapping("/getCarouselImageList")
    public Result<?> getCarouselImageList() {
        try {
            List<CarouselImageVo> carouselImageList = homePageService.getCarouselImageList();
            return Result.OK(carouselImageList);
        }catch (IllegalArgumentException e){
            log.error("获取首页轮播图列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取首页轮播图列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取首页轮播图列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }
}
