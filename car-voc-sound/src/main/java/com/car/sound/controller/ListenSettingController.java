package com.car.sound.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.CarouselImageModel;
import com.car.sound.model.ListenSettingModel;
import com.car.sound.service.ListenSettingService;
import com.car.sound.vo.DepartUserVo;
import com.car.sound.vo.DepartVo;
import com.car.sound.vo.ListenSettingMenuVo;
import com.car.sound.vo.ListenSettingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/14 17:30
 * @描述:
 **/
@RestController
@Api(tags = "聆听设置")
@RequestMapping("/sound/listenSetting")
@AllArgsConstructor
@Slf4j
public class ListenSettingController {
    @Autowired
    private ListenSettingService listenSettingService;

    @ApiOperation(value = "聆听设置-新增聆听设置", notes = "聆听设置-新增聆听设置")
    @PostMapping("/saveListenSetting")
    public Result<?> saveListenSetting(@RequestBody ListenSettingModel settingModel) {
        try {
            listenSettingService.saveListenSetting(settingModel);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("新增聆听设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("新增聆听设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("新增聆听设置失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听设置-修改聆听设置", notes = "聆听设置-修改聆听设置")
    @PostMapping("/updateListenSetting")
    public Result<?> updateListenSetting(@RequestBody ListenSettingModel settingModel) {
        try {
            listenSettingService.updateListenSetting(settingModel);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("新增聆听设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("新增聆听设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("新增聆听设置失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听设置-获取聆听设置列表", notes = "聆听设置-获取聆听设置列表")
    @PostMapping("/getListenSettingList")
    public Result<IPage<ListenSettingVo>> getListenSettingList(@RequestBody ListenSettingModel settingModel) {
        try {
            IPage<ListenSettingVo> listenSettingList = listenSettingService.getListenSettingList(settingModel);
            return Result.OK(listenSettingList);
        }catch (IllegalArgumentException e){
            log.error("获取聆听设置列表失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (BootException e){
            log.error("获取聆听设置列表失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (Exception e) {
            log.error("获取聆听设置列表失败:{}", e);
            return Result.errors("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听设置-获取聆听设置菜单列表", notes = "聆听设置-获取聆听设置菜单列表")
    @GetMapping("/getListenSettingMenuList")
    public Result<?> getListenSettingMenuList() {
        try {
            List<ListenSettingMenuVo> menuVos = listenSettingService.getListenSettingMenuList();
            return Result.OK(menuVos);
        }catch (IllegalArgumentException e){
            log.error("获取聆听设置菜单列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取聆听设置菜单列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取聆听设置菜单列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听设置-获取部门树", notes = "聆听设置-获取部门树")
    @GetMapping("/getDepartTree")
    public Result<?> getDepartTree(@RequestParam("brand") String brand) {
        try {
            List<DepartVo> departVos = listenSettingService.getDepartTree(brand);
            return Result.OK(departVos);
        }catch (IllegalArgumentException e){
            log.error("获取部门树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取部门树失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取部门树失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听设置-根据部门获取用户列表", notes = "聆听设置-根据部门获取用户列表")
    @GetMapping("/getUserListByDepartId")
    public Result<?> getUserListByDepartId(@RequestParam("brand") String brand, @RequestParam("departId") String departId) {
        try {
            List<DepartUserVo> userVoList = listenSettingService.getUserListByDepartId(departId,brand);
            return Result.OK(userVoList);
        }catch (IllegalArgumentException e){
            log.error("根据部门获取用户列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("根据部门获取用户列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("根据部门获取用户列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

}
