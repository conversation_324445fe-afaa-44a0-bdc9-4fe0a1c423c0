package com.car.sound.controller;

import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.TokenVaildModel;
import com.car.sound.service.AuthenticationService;
import com.car.sound.vo.ListenSettingMenuVo;
import com.car.sound.vo.TokenVaildVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 18:41
 * @描述:
 **/
@RestController
@Api(tags = "权限验证")
@RequestMapping("/sound/auth")
@AllArgsConstructor
@Slf4j
public class AuthenticationController {
    @Autowired
    private  AuthenticationService authenticationService;
    @ApiOperation("统一登录token获取")
    @RequestMapping(value = "/getToken", method = RequestMethod.POST)
    public Result<?> getToken(@RequestBody TokenVaildModel checkToken) {
        try {
            TokenVaildVo token = authenticationService.getToken(checkToken);
            return Result.OK(token);
        }catch (IllegalArgumentException e){
            log.error("统一登录token获取失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("统一登录token获取失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("统一登录token获取失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation("获取菜单")
    @RequestMapping(value = "/getMenu", method = RequestMethod.POST)
    public Result<?> getMenu() {
        try {
            List<ListenSettingMenuVo> menuVos = authenticationService.getMenu();
            return Result.OK(menuVos);
        }catch (IllegalArgumentException e){
            log.error("获取菜单失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取菜单失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取菜单失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

}
