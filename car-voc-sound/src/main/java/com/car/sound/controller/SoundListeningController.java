package com.car.sound.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.CommentModel;
import com.car.sound.model.CommentSettingModel;
import com.car.sound.model.SoundListeningModel;
import com.car.sound.service.SoundListeningService;
import com.car.sound.vo.*;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 15:27
 * @描述:
 **/
@RestController
@Api(tags = "聆听声音")
@RequestMapping("/sound/soundListening")
@AllArgsConstructor
@Slf4j
public class SoundListeningController {
    @Autowired
    private SoundListeningService soundListeningService;


    @AutoLog(value = "聆听声音-根据工单号获取声音")
    @ApiOperation(value = "聆听声音-根据工单号获取声音", notes = "聆听声音-根据工单号获取声音")
    @GetMapping("/getSoundByWorkOrderNo")
    public Result<?> getSoundByWorkOrderNo(@RequestParam("workOrderNo") String workOrderNo) {
        try {
            final WorkNoVo workNoVo = soundListeningService.getSoundByWorkOrderNo(workOrderNo);
            return Result.OK(workNoVo);
        }catch (BootException e){
            log.error("根据工单号获取声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("根据工单号获取声音失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }


    @ApiOperation(value = "聆听声音-保存声音", notes = "聆听声音-保存声音")
    @PostMapping("/saveSoundListening")
    public Result<?> saveSoundListening(@RequestBody SoundListeningModel model) {
        try {
            soundListeningService.saveSoundListening(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("保存声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("保存声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("保存声音失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-修改声音", notes = "聆听声音-修改声音")
    @PostMapping("/updateSoundListening")
    public Result<?> updateSoundListening(@RequestBody SoundListeningModel model) {
        try {
            soundListeningService.updateSoundListening(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("修改声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("修改声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("修改声音失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-删除声音", notes = "聆听声音-删除声音")
    @GetMapping("/deleteSoundListening")
    public Result<?> deleteSoundListening(@RequestParam("taskId") String taskId) {
        try {
            soundListeningService.deleteSoundListening(taskId);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("删除声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("删除声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("删除声音失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-根据id获取声音", notes = "聆听声音-根据id获取声音")
    @PostMapping("/getSoundListeningById")
    public Result<?> getSoundListeningById(@RequestBody SoundListeningModel model) {
        try {
            SoundListeningVo soundListeningById = soundListeningService.getSoundListeningById(model);
            return Result.OK(soundListeningById);
        }catch (IllegalArgumentException e){
            log.error("根据id获取声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("根据id获取声音失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("根据id获取声音失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-获取声音列表", notes = "聆听声音-获取声音列表")
    @PostMapping("/getSoundListeningList")
    public Result<?> getSoundListeningList(@RequestBody SoundListeningModel model) {
        try {
            IPage<SoundListeningVo> soundListeningList = soundListeningService.getSoundListeningList(model);
            return Result.OK(soundListeningList);
        }catch (IllegalArgumentException e){
            log.error("获取声音列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取声音列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取声音列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-获取任务数据统计", notes = "聆听声音-获取任务数据统计")
    @PostMapping("/getSoundListenDataStatistics")
    public Result<?> getSoundListenDataStatistics(@RequestBody SoundListeningModel model) {
        try {
            ListenTaskStatisticsVo soundListeningVo = soundListeningService.getSoundListenDataStatistics(model);
            return Result.OK(soundListeningVo);
        }catch (IllegalArgumentException e){
            log.error("获取任务数据统计失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取任务数据统计失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取任务数据统计失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-获取任务数据列表", notes = "聆听声音-获取任务数据列表")
    @PostMapping("/getSoundListenDataList")
    public Result<?> getSoundListenDataList(@RequestBody SoundListeningModel model) {
        try {
            IPage<ListenTaskDataVo> soundListenDataList = soundListeningService.getSoundListenDataList(model);
            return Result.OK(soundListenDataList);
        }catch (IllegalArgumentException e){
            log.error("获取任务数据列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取任务数据列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取任务数据列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-导出任务数据列表", notes = "聆听声音-导出任务数据列表")
    @PostMapping("/exportSoundListenDataList")
    public Result<?> exportSoundListenDataList(@RequestBody SoundListeningModel model) {
        try {
            String fileUrl = soundListeningService.exportSoundListenDataList(model);
            return Result.OK(fileUrl);
        }catch (IllegalArgumentException e){
            log.error("导出任务数据列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("导出任务数据列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("导出任务数据列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-获取评论列表", notes = "聆听声音-获取评论列表")
    @PostMapping("/getCommentList")
    public Result<?> getCommentList(@RequestBody CommentModel model) {
        try {
            IPage<CommentVo> commentList = soundListeningService.getCommentList(model);
            return Result.OK(commentList);
        }catch (IllegalArgumentException e){
            log.error("获取评论列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取评论列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取评论列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-导出评论列表", notes = "聆听声音-导出评论列表")
    @PostMapping("/exportCommentList")
    public Result<?> exportCommentList(@RequestBody CommentModel model) {
        try {
            String fileUrl = soundListeningService.exportCommentList(model);
            return Result.OK(fileUrl);
        }catch (IllegalArgumentException e){
            log.error("导出评论列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("导出评论列表失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("导出评论列表失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-获取评论类型", notes = "聆听声音-获取评论类型")
    @PostMapping("/getCommentTypeList")
    public Result<?> getCommentTypeList() {
        try {
            List<CommentSettingVo> commentList = soundListeningService.getCommentTypeList();
            return Result.OK(commentList);
        }catch (IllegalArgumentException e){
            log.error("获取评论类型失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("获取评论类型失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("获取评论类型失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "聆听声音-回复评论", notes = "聆听声音-回复评论")
    @PostMapping("/replyComment")
    public Result<?> replyComment(@RequestBody CommentModel model) {
        try {
            soundListeningService.replyComment(model);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("回复评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("回复评论失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("回复评论失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

}
