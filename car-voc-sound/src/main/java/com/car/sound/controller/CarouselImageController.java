package com.car.sound.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.CarouselImageModel;
import com.car.sound.service.CarouselImageService;
import com.car.sound.vo.CarouselImageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 14:17
 * @描述:
 **/
@RestController
@Api(tags = "轮播图")
@RequestMapping("/sound/carouselImage")
@AllArgsConstructor
@Slf4j
public class CarouselImageController {
    @Autowired
    private CarouselImageService carouselImageService;

    @ApiOperation(value = "轮播图-新增轮播图", notes = "轮播图-新增轮播图")
    @PostMapping("/saveCarouselImage")
    public Result<?> saveCarouselImage(@RequestBody CarouselImageModel carouselImageModel) {
        try {
            carouselImageService.saveCarouselImage(carouselImageModel);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("新增轮播图失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("新增轮播图失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("新增轮播图失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "轮播图-修改轮播图", notes = "轮播图-修改轮播图")
    @PostMapping("/updateCarouselImage")
    public Result<?> updateCarouselImage(@RequestBody CarouselImageModel carouselImageModel) {
        try {
            carouselImageService.updateCarouselImage(carouselImageModel);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("修改轮播图失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("修改轮播图失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("修改轮播图失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "轮播图-删除轮播图", notes = "轮播图-删除轮播图")
    @GetMapping("/deleteCarouselImage")
    public Result<?> deleteCarouselImage(@RequestParam("id") String id) {
        try {
            carouselImageService.deleteCarouselImage(id);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("删除轮播图失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("删除轮播图失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("删除轮播图失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "轮播图-根据id查询轮播图", notes = "轮播图-根据id查询轮播图")
    @PostMapping("/getCarouselImageById")
    public Result<CarouselImageVo> getCarouselImageById(@RequestBody CarouselImageModel carouselImageModel) {
        try {
            CarouselImageVo carouselImageVo = carouselImageService.getCarouselImageById(carouselImageModel);
            return Result.OK(carouselImageVo);
        }catch (IllegalArgumentException e){
            log.error("根据id查询轮播图失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (BootException e){
            log.error("根据id查询轮播图失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (Exception e) {
            log.error("根据id查询轮播图失败:{}", e);
            return Result.errors("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "轮播图-查询轮播图列表", notes = "轮播图-查询轮播图列表")
    @PostMapping("/getCarouselImageList")
    public Result<IPage<CarouselImageVo>> getCarouselImageList(@RequestBody CarouselImageModel carouselImageModel) {
        try {
            IPage<CarouselImageVo> commentSettingList = carouselImageService.getCarouselImageList(carouselImageModel);
            return Result.OK(commentSettingList);
        }catch (IllegalArgumentException e){
            log.error("查询轮播图列表失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (BootException e){
            log.error("查询轮播图列表失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (Exception e) {
            log.error("查询轮播图列表失败:{}", e);
            return Result.errors("系统内部错误,请联系管理员");
        }
    }
}
