package com.car.sound.controller;

import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.service.DictService;
import com.car.sound.vo.DictItemVo;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 15:06
 * @描述:
 **/
@RestController
@Api(tags = "字典")
@RequestMapping("/sound/dict")
@AllArgsConstructor
@Slf4j
public class DictController {
    @Autowired
    private DictService dictService;

    @ApiOperation(value = "字典-根据dictCode获取item数据", notes = "字典-根据dictCode获取item数据")
    @GetMapping("/getDictItemByDictCode")
    public Result<?> getDictItemByDictCode(@RequestParam("dictCode") String dictCode) {
        try {
            final List<DictItemVo> dictItemVos = dictService.getDictItemByDictCode(dictCode);
            return Result.OK(dictItemVos);
        }catch (IllegalArgumentException e){
            log.error("根据dictCode获取item数据失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("根据dictCode获取item数据失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("根据dictCode获取item数据失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }
}
