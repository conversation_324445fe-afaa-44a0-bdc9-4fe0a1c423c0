package com.car.sound.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.common.Result;
import com.car.sound.exception.BootException;
import com.car.sound.model.CommentSettingModel;
import com.car.sound.service.CommentSettingService;
import com.car.sound.vo.CommentSettingVo;
import com.car.voc.common.aspect.annotation.AutoLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 12:01
 * @描述:
 **/
@RestController
@Api(tags = "评论设置")
@RequestMapping("/sound/commentSetting")
@AllArgsConstructor
@Slf4j
public class CommentSettingController {
    @Autowired
    private CommentSettingService  commentSettingService;

    @ApiOperation(value = "评论设置-新增评论设置", notes = "评论设置-新增评论设置")
    @PostMapping("/saveCommentSetting")
    public Result<?> saveCommentSetting(@RequestBody CommentSettingModel settingModel) {
        try {
            commentSettingService.saveCommentSetting(settingModel);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("新增评论设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("新增评论设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("新增评论设置失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "评论设置-修改评论设置", notes = "评论设置-修改评论设置")
    @PostMapping("/updateCommentSetting")
    public Result<?> updateCommentSetting(@RequestBody CommentSettingModel settingModel) {
        try {
            commentSettingService.updateCommentSetting(settingModel);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("修改评论设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("修改评论设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("修改评论设置失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "评论设置-删除评论设置", notes = "评论设置-删除评论设置")
    @GetMapping("/deleteCommentSetting")
    public Result<?> deleteCommentSetting(@RequestParam("id") String id) {
        try {
            commentSettingService.deleteCommentSetting(id);
            return Result.OK();
        }catch (IllegalArgumentException e){
            log.error("删除评论设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (BootException e){
            log.error("删除评论设置失败:{}",e);
            return Result.error(e.getMessage());
        }catch (Exception e) {
            log.error("删除评论设置失败:{}", e);
            return Result.error("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "评论设置-根据id查询评论设置", notes = "评论设置-根据id查询评论设置")
    @PostMapping("/getCommentSettingById")
    public Result<CommentSettingVo> getCommentSettingById(@RequestBody CommentSettingModel settingModel) {
        try {
            CommentSettingVo commentSettingById = commentSettingService.getCommentSettingById(settingModel);
            return Result.OK(commentSettingById);
        }catch (IllegalArgumentException e){
            log.error("根据id查询评论设置失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (BootException e){
            log.error("根据id查询评论设置失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (Exception e) {
            log.error("根据id查询评论设置失败:{}", e);
            return Result.errors("系统内部错误,请联系管理员");
        }
    }

    @ApiOperation(value = "评论设置-查询评论设置列表", notes = "评论设置-查询评论设置列表")
    @PostMapping("/getCommentSettingList")
    public Result<IPage<CommentSettingVo>> getCommentSettingList(@RequestBody CommentSettingModel settingModel) {
        try {
            IPage<CommentSettingVo> commentSettingList = commentSettingService.getCommentSettingList(settingModel);
            return Result.OK(commentSettingList);
        }catch (IllegalArgumentException e){
            log.error("查询评论设置列表失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (BootException e){
            log.error("查询评论设置列表失败:{}",e);
            return Result.errors(e.getMessage());
        }catch (Exception e) {
            log.error("查询评论设置列表失败:{}", e);
            return Result.errors("系统内部错误,请联系管理员");
        }
    }
}
