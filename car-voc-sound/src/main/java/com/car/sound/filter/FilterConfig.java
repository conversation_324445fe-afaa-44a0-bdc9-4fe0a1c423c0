package com.car.sound.filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 09:54
 * @描述:
 **/
@Configuration
public class FilterConfig {
    @Bean("soundFilter")
    public FilterRegistrationBean<AuthenticationFilter> authenticationFilter() {
        FilterRegistrationBean<AuthenticationFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new AuthenticationFilter());
        registrationBean.addUrlPatterns("/sound/carouselImage/*",
                "/sound/soundListening/*",
                "/sound/commentSetting/*",
                "/sound/listenTask/*",
                "/sound/homePage/*",
                "/sound/dropDown/*",
                "/sound/auth/getMenu");
        return registrationBean;
    }
}
