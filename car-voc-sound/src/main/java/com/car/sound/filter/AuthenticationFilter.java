package com.car.sound.filter;


import cn.hutool.core.util.StrUtil;
import com.car.sound.service.UserService;
import com.car.sound.vo.UserVo;
import com.car.voc.common.util.JwtUtil;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
public class AuthenticationFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        log.info("请求地址：{}", httpRequest.getRequestURI());
        // 检查请求头
        String authHeader = httpRequest.getHeader("X-Access-Token");
        if (StrUtil.isEmpty(authHeader)) {
            // 如果没有授权头或者值不正确，返回403状态码
            httpResponse.sendError(HttpServletResponse.SC_FORBIDDEN, "未携带token");
            return;
        }
        try {
            Boolean checkToken = JwtUtil.checkToken(authHeader);
            if (!checkToken){
                httpResponse.sendError(HttpServletResponse.SC_FORBIDDEN, "token已过期");
            }
        }catch (Exception e){
            log.error("token校验失败:{}",e);
            httpResponse.sendError(HttpServletResponse.SC_FORBIDDEN, "token已过期");
        }

        // 从token中获取用户id
        String userId = JwtUtil.getUsername(authHeader);
        //根据用户id查询用户信息
        UserService userService = SpringContextUtils.getBean(UserService.class);
        final UserVo userVo = userService.getUserInfoByUserId(userId);
        if(ObjectUtils.isEmpty(userVo)){
            httpResponse.sendError(HttpServletResponse.SC_FORBIDDEN, "没有权限,请联系管理员授权");
            return;
        }
        //将用户信息放入ThreadLocal中
        SpringContextUtils.setUser(userVo);
        SpringContextUtils.setUserId(userVo.getUserId());
        // 如果授权成功，继续请求链
        chain.doFilter(request, response);
    }
}
