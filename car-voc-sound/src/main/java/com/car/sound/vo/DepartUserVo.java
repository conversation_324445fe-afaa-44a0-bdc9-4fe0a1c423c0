package com.car.sound.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/23 15:27
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DepartUserVo {
    @ApiModelProperty(value = "用户id")
    private String label;
    @ApiModelProperty(value = "用户名称")
    private String value;
    @ApiModelProperty(value = "用户账号")
    private String account;
}
