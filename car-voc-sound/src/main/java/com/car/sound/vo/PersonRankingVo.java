package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 15:43
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "PersonRankingVo", description = "个人排行榜")
public class PersonRankingVo {
    @ApiModelProperty(value = "本月积分")
    private Integer integral;
    @ApiModelProperty(value = "本月排名")
    private Integer ranking;
    @ApiModelProperty(value = "本月排行")
    private List<RankingListVo> rankingList;
    @ApiModelProperty(value = "累计排行")
    private List<RankingListVo> cumulativeRankingList;
    @ApiModelProperty(value = "累计积分")
    private Integer cumulativeIntegral;
    @ApiModelProperty(value = "累计排名")
    private Integer cumulativeRanking;

}
