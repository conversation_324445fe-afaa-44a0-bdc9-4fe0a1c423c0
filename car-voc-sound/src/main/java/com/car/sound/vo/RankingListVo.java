package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 15:39
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "RankingListVo", description = "排行榜")
public class RankingListVo {
    @ApiModelProperty(value = "部门名称")
    private String departName;
    @ApiModelProperty(value = "完成率")
    private BigDecimal completeRate;
    @ApiModelProperty(value = "用户名称")
    private String userName;
    @ApiModelProperty(value = "用户积分")
    private BigDecimal integral;
    private String userId;
}
