package com.car.sound.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/20 14:18
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WorkNoVo {
    @ApiModelProperty(value = "原始声音url")
    private String url;
    @ApiModelProperty(value = "工单类型")
    private String workOrderType;
    @ApiModelProperty(value = "声音时长")
    private String soundDuration;
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    @ApiModelProperty(value = "工单ID")
    private String workOrderId;
}
