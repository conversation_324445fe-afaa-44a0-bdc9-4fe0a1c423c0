package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 14:03
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "CommentVo", description = "评论")
public class CommentVo {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "评论类型")
    private String commentType;
    @ApiModelProperty(value = "评论类型名称")
    private String commentTypeName;
    @ApiModelProperty(value = "评论内容")
    private String comment;
    @ApiModelProperty(value = "点赞数")
    private Long likeCount;
    @ApiModelProperty(value = "评论时间")
    private String commentTime;
    @ApiModelProperty(value = "评论人")
    private String commentUser;
    @ApiModelProperty(value = "回复人")
    private String replyUser;
    @ApiModelProperty(value = "回复内容")
    private String replyContent;
    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private String replyTime;
    @ApiModelProperty(value = "账号")
    private String account;
    @ApiModelProperty(value = "部门名称")
    private String departName;
    @ApiModelProperty(value = "是否回复")
    private Boolean isReply;
    @ApiModelProperty(value = "是否点赞")
    private Boolean isUpvote;


}
