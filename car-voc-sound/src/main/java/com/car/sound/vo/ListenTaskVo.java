package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 11:06
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ListenTaskVo", description = "聆听任务")
public class ListenTaskVo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 声音名称
     */
    @ApiModelProperty(value = "声音名称")
    private String soundName;

    /**
     * 是否必听
     */
    @ApiModelProperty(value = "是否必听")
    private String isMandatory;
    /**
     * 奖励积分
     */
    @ApiModelProperty(value = "奖励积分")
    private BigDecimal rewardIntegral;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    /**
     * 原始声音
     */
    @ApiModelProperty(value = "原始声音")
    private String originalSound;
    /**
     * 声音时长
     */
    @ApiModelProperty(value = "声音时长")
    private Long soundDuration;
    /**
     * 聆听时长
     */
    @ApiModelProperty(value = "聆听时长")
    private Long listenDuration;
    /**
     * 聆听状态
     */
    @ApiModelProperty(value = "聆听状态")
    private String status;
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;
    /**
     * 是否收藏
     */
    @ApiModelProperty(value = "是否收藏")
    private Boolean isFavorite;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "截止时间")
    private String endTime;

    /**
     * 聆听人数
     */
    @ApiModelProperty(value = "聆听人数")
    private Integer listenCount;
    /**
     * 聆听人次
     */
    @ApiModelProperty(value = "聆听人次")
    private Integer listenTimes;
    /**
     * 评论人次
     */
    @ApiModelProperty(value = "评论人次")
    private Integer commentTimes;
    /**
     * 已收藏数
     */
    @ApiModelProperty(value = "已收藏数")
    private Integer favoriteCount;
    /**
     * 已评论数
     */
    @ApiModelProperty(value = "已评论数/评论人数")
    private Integer commentCount;
    @ApiModelProperty(value = "已完成数/听完人数")
    private Integer completeCount;
    /**
     * 聆听进度
     */
    @ApiModelProperty(value = "聆听进度")
    private BigDecimal listenProgress;
    @ApiModelProperty(value = "评论设置")
    private List<CommentSettingVo> commentSetting;
    @ApiModelProperty(value = "图片地址")
    private String url;
    @ApiModelProperty(value = "任务id")
    private String taskId;
    @ApiModelProperty(value = "提示语")
    private List<String> prompt;
    @ApiModelProperty(value = "工单ID")
    private String workOrderId;
    @ApiModelProperty(value = "是否评论")
    private Boolean isComment;
}
