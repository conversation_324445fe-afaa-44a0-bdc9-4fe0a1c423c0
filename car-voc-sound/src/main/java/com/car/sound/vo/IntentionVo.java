package com.car.sound.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/19 18:59
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IntentionVo {
    @ApiModelProperty(value = "意图id")
    private String id;
    @ApiModelProperty(value = "意图")
    private String intention;
    @ApiModelProperty(value = "任务数量")
    private  Integer taskCount;
    @ApiModelProperty(value = "标签")
    private List<LabelVo> label;
}
