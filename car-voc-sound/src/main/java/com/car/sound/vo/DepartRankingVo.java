package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 15:45
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "DepartRankingVo", description = "部门排行榜")
public class DepartRankingVo {
    @ApiModelProperty(value = "本月排行")
    private List<RankingListVo> rankingList;
    @ApiModelProperty(value = "累计排行")
    private List<RankingListVo> cumulativeRankingList;
}
