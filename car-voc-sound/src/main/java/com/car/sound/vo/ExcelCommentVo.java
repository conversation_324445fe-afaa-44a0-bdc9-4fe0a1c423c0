package com.car.sound.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/30 10:10
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExcelCommentVo {
    @ExcelProperty(value = "账号",order = 1)
    private String account;
    @ExcelProperty(value = "部门名称",order = 2)
    private String departName;
    @ExcelProperty(value = "建议分类",order = 3)
    private String commentTypeName;
    @ExcelProperty(value = "建议内容",order = 4)
    private String comment;
    @ExcelProperty(value = "点赞数",order = 5)
    private Long likeCount;
    @ExcelProperty(value = "提交时间",order = 6)
    private String commentTime;
    @ExcelProperty(value = "是否回复",order = 7)
    private String isReply;
    @ExcelProperty(value = "回复时间",order = 8)
    private String replyTime;



}
