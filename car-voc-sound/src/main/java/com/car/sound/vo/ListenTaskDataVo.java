package com.car.sound.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/14 11:47
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ListenTaskDataVo", description = "聆听任务数据")
public class ListenTaskDataVo {
    @ApiModelProperty(value = "账号")
    @ExcelProperty(value = "账号",order = 1)
    private String userName;
    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门",order = 2)
    private String departName;
    @ApiModelProperty(value = "是否完成")
    @ExcelProperty(value = "是否完成",order = 3)
    private String isComplete;
    @ApiModelProperty(value = "参与时间")
    @ExcelProperty(value = "参与时间",order = 4)
    private String joinTime;
    @ApiModelProperty(value = "是否评论")
    @ExcelProperty(value = "是否评论",order = 5)
    private String isComment;
    @ApiModelProperty(value = "是否收藏")
    @ExcelProperty(value = "是否收藏",order = 6)
    private String isFavorite;
}
