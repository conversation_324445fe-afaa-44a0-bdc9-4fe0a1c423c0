package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:03
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "SoundListeningVo", description = "声音聆听")
public class SoundListeningVo implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 声音名称
     */
    @ApiModelProperty(value = "声音名称")
    private String soundName;
    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    private String carSeries;
    @ApiModelProperty(value = "车系名称")
    private String carSeriesName;
    @ApiModelProperty(value = "品牌名称")
    private String brandName;
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String label;
    @ApiModelProperty(value = "标签名称")
    private String labelName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否必听
     */
    @ApiModelProperty(value = "是否必听")
    private String isMandatory;
    /**
     * 奖励积分
     */
    @ApiModelProperty(value = "奖励积分")
    private BigDecimal rewardIntegral;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    private String validStartTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    private String validEndTime;
    /**
     * 可听范围
     */
    @ApiModelProperty(value = "可听范围")
    private String listeningRange;
    /**
     * 原始声音
     */
    @ApiModelProperty(value = "原始声音")
    private String originalSound;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 聆听人数
     */
    @ApiModelProperty(value = "聆听人数")
    private Integer listenCount;
    /**
     * 聆听人次
     */
    @ApiModelProperty(value = "聆听人次")
    private Integer listenTimes;
    /**
     * 评论人次
     */
    @ApiModelProperty(value = "评论人次")
    private Integer commentTimes;
    /**
     * 已收藏数
     */
    @ApiModelProperty(value = "已收藏数")
    private Integer favoriteCount;
    /**
     * 已评论数
     */
    @ApiModelProperty(value = "已评论数/评论人数")
    private Integer commentCount;
    /**
     * 聆听进度
     */
    @ApiModelProperty(value = "聆听进度")
    private BigDecimal listenProgress;

    private String listeningRanges;

    private String taskId;

    @ApiModelProperty(value = "工单类型")
    private String workOrderType;
    @ApiModelProperty(value = "声音时长")
    private Long soundDuration;
    @ApiModelProperty(value = "提示语")
    private List<String> prompt;
}
