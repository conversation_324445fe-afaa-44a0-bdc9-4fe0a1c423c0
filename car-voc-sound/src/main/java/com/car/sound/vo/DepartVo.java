package com.car.sound.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/19 10:10
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DepartVo {
    @ApiModelProperty(value = "部门id")
    private String label;
    @ApiModelProperty(value = "部门名称")
    private String value;
    private String parentId;
    @ApiModelProperty(value = "品牌")
    private String brand;
    @ApiModelProperty(value = "用户列表")
    private List<DepartUserVo> userList;
    @ApiModelProperty(value = "子部门列表")
    private List<DepartVo> child;
}
