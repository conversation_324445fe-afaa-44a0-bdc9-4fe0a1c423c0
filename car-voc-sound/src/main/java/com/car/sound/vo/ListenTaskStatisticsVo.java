package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 17:10
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ListenTaskStatisticsVo", description = "聆听任务统计")
public class ListenTaskStatisticsVo {
    /**
     * 总积分数
     */
    @ApiModelProperty(value = "积分数")
    private BigDecimal totalIntegral;
    /**
     * 待聆听必听数
     */
    @ApiModelProperty(value = "待聆听必听数")
    private Integer mandatoryWaitListenCount;
    /**
     * 待聆听非必听数
     */
    @ApiModelProperty(value = "待聆听非必听数")
    private Integer nonMandatoryWaitListenCount;
    /**
     * 已完成数
     */
    @ApiModelProperty(value = "已完成数/听完人数")
    private Integer completeCount;
    /**
     * 已收藏数
     */
    @ApiModelProperty(value = "已收藏数")
    private Integer favoriteCount;
    /**
     * 已评论数
     */
    @ApiModelProperty(value = "已评论数/评论人数")
    private Integer commentCount;
    /**
     * 总任务数
     */
    private Integer totalCount;
    /**
     * 聆听人数
     */
    @ApiModelProperty(value = "聆听人数")
    private Integer listenCount;
    /**
     * 聆听人次
     */
    @ApiModelProperty(value = "聆听人次")
    private Integer listenTimes;
    /**
     * 评论人次
     */
    @ApiModelProperty(value = "评论人次")
    private Integer commentTimes;
    /**
     * 已完成的必听数
     */
    private Integer mandatoryCount;
    /**
     * 已完成的非必听数
     */
    private Integer notMandatoryCount;
    /**
     * 必听总数
     */
    private Integer mandatoryTotalCount;
    /**
     * 非必听总数
     */
    private Integer notMandatoryTotalCount;

}
