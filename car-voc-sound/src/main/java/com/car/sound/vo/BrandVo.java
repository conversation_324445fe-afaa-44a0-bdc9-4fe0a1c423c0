package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 11:44
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "BrandVo", description = "品牌")
@EqualsAndHashCode(callSuper = false)
public class BrandVo implements Serializable {
    /**
     * 品牌id
     */
    @ApiModelProperty(value = "品牌id")
    private String id;
    /**
     * 品牌编码
     */
    @ApiModelProperty(value = "品牌编码")
    private String label;
    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String value;
    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    private List<CarSeriesVo> carSeries;
    /**
     * 当前品牌任务数
     */
    @ApiModelProperty(value = "当前品牌任务数")
    private Integer taskCount;
}
