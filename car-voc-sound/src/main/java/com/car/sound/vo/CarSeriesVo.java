package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 11:52
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "CarSeriesVo", description = "车系")
@EqualsAndHashCode(callSuper = false)
public class CarSeriesVo implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 车系编码
     */
    @ApiModelProperty(value = "车系编码")
    private String label;
    /**
     * 车系名称
     */
    @ApiModelProperty(value = "车系名称")
    private String value;
    /**
     * 当前车系任务数
     */
    @ApiModelProperty(value = "当前车系任务数")
    private Integer taskCount;
}
