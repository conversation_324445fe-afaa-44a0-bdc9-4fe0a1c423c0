package com.car.sound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 18:13
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "LabelVo", description = "标签")
public class LabelVo {
    @ApiModelProperty(value = "标签id")
    private String id;
    @ApiModelProperty(value = "标签名称")
    private String name;
    private String pid;
    @ApiModelProperty(value = "标签编码")
    private String code;
    @ApiModelProperty(value = "任务数量")
    private Integer taskCount;
    @ApiModelProperty(value = "子标签")
    private List<LabelVo> child;
}
