package com.car.sound.vo;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:34
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "CommentSettingModel", description = "评论设置")
@EqualsAndHashCode(callSuper = false)
public class CommentSettingVo implements Serializable {
    @ApiModelProperty(value = "主键")
    private String id;
    @ApiModelProperty(value = "类型名称")
    private String name;
    @ApiModelProperty(value = "类型排序")
    private Integer sort;
    @ApiModelProperty(value = "最少输入字数")
    private Integer minInputNum;
    @ApiModelProperty(value = "最多输入字数")
    private Integer maxInputNum;
    @ApiModelProperty(value = "点赞数")
    private Integer likeNum;
    @ApiModelProperty(value = "奖励积分")
    private BigDecimal rewardIntegral;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
