package com.car.sound.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 14:03
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "CommentModel", description = "评论")
public class CommentModel extends Page implements Serializable {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "评论类型")
    private String commentType;
    @ApiModelProperty(value = "评论内容")
    private String comment;
    @ApiModelProperty(value = "点赞数")
    private Long likeCount;
    @ApiModelProperty(value = "评论人")
    private String commentUser;
    @ApiModelProperty(value = "回复人")
    private String replyUser;
    @ApiModelProperty(value = "回复内容")
    private String replyContent;
    @ApiModelProperty(value = "任务id")
    private String taskId;
    @ApiModelProperty(value = "账号")
    private String account;
    @ApiModelProperty(value = "部门名称")
    private String departName;
    @ApiModelProperty(value = "是否回复")
    private String isReply;
    @ApiModelProperty(value = "评论开始时间")
    private LocalDateTime commentStartTime;
    @ApiModelProperty(value = "评论结束时间")
    private LocalDateTime commentEndTime;
    @ApiModelProperty(value = "回复开始时间")
    private LocalDateTime replyStartTime;
    @ApiModelProperty(value = "回复结束时间")
    private LocalDateTime replyEndTime;
    /**
     * 可听范围
     */
    @ApiModelProperty(value = "可听范围")
    private List<String> listeningRange;

}
