package com.car.sound.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:34
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "CommentSettingModel", description = "评论设置")
public class CommentSettingModel extends Page implements Serializable {
    @ApiModelProperty(value = "主键")
    private String id;
    @ApiModelProperty(value = "类型名称")
    private String name;
    @ApiModelProperty(value = "类型排序")
    private Integer sort;
    @ApiModelProperty(value = "最少输入字数")
    private Integer minInputNum;
    @ApiModelProperty(value = "最多输入字数")
    private Integer maxInputNum;
    @ApiModelProperty(value = "点赞数")
    private Integer likeNum;
    @ApiModelProperty(value = "奖励积分")
    private BigDecimal rewardIntegral;
    @ApiModelProperty(value = "状态")
    private String status;
}
