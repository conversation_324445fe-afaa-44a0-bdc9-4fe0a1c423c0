package com.car.sound.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:03
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "SoundListeningModel", description = "声音聆听")
public class SoundListeningModel extends Page implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String taskId;
    /**
     * 声音名称
     */
    @ApiModelProperty(value = "声音名称")
    private String soundName;
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    private String carSeries;
    /**
     * 一级标签
     */
    @ApiModelProperty(value = "一级标签")
    private String firstLabel;
    /**
     * 二级标签
     */
    @ApiModelProperty(value = "二级标签")
    private String secondLabel;
    /**
     * 三级标签
     */
    @ApiModelProperty(value = "三级标签")
    private String threeLabel;
    /**
     * 四级标签
     */
    @ApiModelProperty(value = "四级标签")
    private String fourLabel;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否必听
     */
    @ApiModelProperty(value = "是否必听")
    private String isMandatory;
    /**
     * 奖励积分
     */
    @ApiModelProperty(value = "奖励积分")
    private BigDecimal rewardIntegral;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    @ApiModelProperty(value = "工单类型")
    private String workOrderType;
    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    private LocalDateTime validStartTime;
    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间")
    private LocalDateTime validEndTime;
    /**
     * 可听范围
     */
    @ApiModelProperty(value = "可听范围")
    private List<ListeningRangeModel> listeningRange;
    /**
     * 原始声音
     */
    @ApiModelProperty(value = "原始声音")
    private String originalSound;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "账号")
    private String account;
    @ApiModelProperty(value = "车系集合")
    private List<String> carSeriesList;
    @ApiModelProperty(value = "标签集合")
    private List<String> labelList;
    @ApiModelProperty(value = "声音时长")
    private Long soundDuration;
}
