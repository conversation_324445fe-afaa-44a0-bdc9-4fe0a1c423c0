package com.car.sound.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 13:44
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ListenSettingModel", description = "聆听设置")
public class ListenSettingModel extends Page implements Serializable {
    @ApiModelProperty(value = "主键")
    private String id;
    @ApiModelProperty(value = "部门")
    private String department;
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    @ApiModelProperty(value = "账号")
    private List<UserModel> account;
    @ApiModelProperty(value = "权限")
    private List<String> permission;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "操作人")
    private String operator;
    @ApiModelProperty(value = "品牌")
    private String brand;
    @ApiModelProperty(value = "姓名/账号")
    private String name;

}
