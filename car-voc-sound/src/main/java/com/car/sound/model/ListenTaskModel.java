package com.car.sound.model;

import com.car.sound.annotation.SortField;
import com.car.sound.annotation.SortFieldConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 10:32
 * @描述:
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "ListenTaskModel", description = "聆听任务")
@SortFieldConvert(fields = {
        @SortField(source = "commentType", targer = "comment_type"),//评论类型
        @SortField(source = "endTime", targer = "validEndTime"),//截止时间
        @SortField(source = "label", targer = "label"),//标签
        @SortField(source = "isMandatory", targer = "isMandatory"),//是否必听
        @SortField(source = "listenCount", targer = "listenCount"),//聆听人数
        @SortField(source = "commentCount", targer = "commentCount"),//评论人数
        @SortField(source = "workOrderType", targer = "workOrderType"),//工单类型
        @SortField(source = "listenProgress", targer = "listenProgress"),//聆听进度/是否已听
        @SortField(source = "likeCount", targer = "likeCount")//点赞数
})
public class ListenTaskModel extends Page implements Serializable {
    @ApiModelProperty(value = "声音id")
    private String taskId;
    @ApiModelProperty(value = "工单类型")
    private String workOrderType;
    @ApiModelProperty(value = "声音时长")
    private Long soundDuration;
    @ApiModelProperty(value = "聆听时长")
    private Long listenDuration;
    @ApiModelProperty(value = "评论类型")
    private String commentType;
    @ApiModelProperty(value = "评论内容")
    private String comment;
    @ApiModelProperty(value = "点赞数")
    private Long likeCount;
    @ApiModelProperty(value = "截止时间")
    private LocalDate endTime;
    private LocalDateTime startTime;

    private String departId;
    /**
     * 奖励积分
     */
    @ApiModelProperty(value = "奖励积分")
    private BigDecimal rewardIntegral;

    @ApiModelProperty(value = "标签")
    private String label;
    @ApiModelProperty(value = "品牌编码")
    private String brandCode;
    @ApiModelProperty(value = "车系编码")
    private String carSeriesCode;

    private String userId;
    private List<String> taskIdList;
}
