package com.car.sound.service;

import com.car.sound.vo.BrandVo;
import com.car.sound.vo.LabelVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 12:10
 * @描述:
 **/
public interface DropDownService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/16 12:11
     * @描述 获取品牌车系树
     * @return java.util.List<com.car.sound.vo.BrandVo>
     **/
    List<BrandVo> getBrandCarSeriesTree();

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/16 12:11
     * @描述 获取标签树
     * @return java.util.List<com.car.sound.vo.LabelVo>
     **/
    List<LabelVo> getLabelTree();

    List<BrandVo> getBrandCarSeriesTrees();
}
