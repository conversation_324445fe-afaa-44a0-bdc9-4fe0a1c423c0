package com.car.sound.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.ListenSettingEntity;
import com.car.sound.entity.MenuEntity;
import com.car.sound.entity.UserDepartEntity;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.ListenSettingMapper;
import com.car.sound.mapper.MenuMapper;
import com.car.sound.mapper.UserMapper;
import com.car.sound.model.ListenSettingModel;
import com.car.sound.model.UserModel;
import com.car.sound.service.ListenSettingService;
import com.car.sound.service.UserDepartService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.util.CallCCUtil;
import com.car.sound.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 14:33
 * @描述:
 **/
@Service
@Slf4j
public class ListenSettingServiceImpl extends ServiceImpl<ListenSettingMapper, ListenSettingEntity> implements ListenSettingService {
    @Autowired
    SoundConvertService convertService;
    @Autowired
    UserMapper userMapper;
    @Autowired
    MenuMapper menuMapper;
    @Autowired
    CallCCUtil util;
    @Autowired
    UserDepartService userDepartService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveListenSetting(ListenSettingModel settingModel) {
        Assert.hasLength(settingModel.getDepartment(),  "部门不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(settingModel.getAccount()),  "账号不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(settingModel.getPermission()),  "权限不能为空");
        Assert.hasLength(settingModel.getStatus(),  "状态不能为空");
        log.debug("转换前:[settingModel:{}]",  settingModel);
        List<ListenSettingEntity> settingEntityList = new ArrayList<>();
        List<UserDepartEntity> userDepartEntityList = new ArrayList<>();

        final List<UserModel> account = settingModel.getAccount();
        List<String> userIds = account.stream().map(e -> e.getId()).collect(Collectors.toList());
        QueryWrapper<ListenSettingEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ListenSettingEntity::getAccount, userIds);
        queryWrapper.lambda().select(ListenSettingEntity::getAccount);
        queryWrapper.lambda().groupBy(ListenSettingEntity::getAccount);
        final List<ListenSettingEntity> listenSettingList = this.baseMapper.selectList(queryWrapper);
        if(ObjectUtils.isNotEmpty(listenSettingList)){
            log.info("当前新增授权账号中存在已授权的账号");
            final List<String> collect = listenSettingList.stream().map(e -> e.getAccount()).collect(Collectors.toList());
            //获取已授权的账号
            List<UserModel> authorizedAccount = account.stream().filter(e -> collect.contains(e.getId())).collect(Collectors.toList());
            if(ObjectUtils.isNotEmpty(authorizedAccount)){
                final List<String> accountName = authorizedAccount.stream().map(e -> e.getName()).collect(Collectors.toList());
                throw new BootException("当前授权账号中存在已授权的账号:"+ StrUtil.join(",", accountName));
            }
        }

        account.stream().forEach(e->{
            ListenSettingEntity listenSetting = convertService.listenSettingModelConvertToEntity(settingModel);
            String join = StrUtil.join(",", settingModel.getPermission());
            listenSetting.setPermission(join);
            listenSetting.setCreateTime(LocalDateTime.now());
            listenSetting.setCreateUser(ObjectUtils.isNotEmpty(settingModel.getOperator())?settingModel.getOperator():"");
            listenSetting.setAccount(e.getId());
            settingEntityList.add(listenSetting);
            UserDepartEntity userDepartEntity = UserDepartEntity.builder()
                    .departId(settingModel.getDepartment())
                    .departName(settingModel.getDepartmentName())
                    .brand(ObjectUtils.isNotEmpty(settingModel.getBrand())?settingModel.getBrand():null)
                    .build();
            userDepartEntity.setUserId(e.getId());
            userDepartEntity.setUserName(e.getName());
            userDepartEntity.setUserAccount(e.getAccount());
            userDepartEntityList.add(userDepartEntity);
        });
        log.debug("转换后:[settingEntityList:{}]",  settingEntityList);
        boolean saveBatch = this.saveBatch(settingEntityList);
        if(saveBatch){
            log.info("新增聆听设置成功");
        }else{
            throw new BootException("新增聆听设置失败");
        }
        userDepartService.saveBatchUserDepart(userDepartEntityList);
    }

    @Override
    public void updateListenSetting(ListenSettingModel settingModel) {
        Assert.isTrue(ObjectUtils.isNotEmpty(settingModel.getPermission()),  "权限不能为空");
        Assert.hasLength(settingModel.getStatus(),  "状态不能为空");
        Assert.hasLength(settingModel.getId(),  "id不能为空");
        ListenSettingEntity listenSetting = convertService.listenSettingModelConvertToEntity(settingModel);
        String join = StrUtil.join(",", settingModel.getPermission());
        listenSetting.setPermission(join);
        UpdateWrapper<ListenSettingEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ListenSettingEntity::getId, settingModel.getId());
        listenSetting.setUpdateTime(LocalDateTime.now());
        listenSetting.setUpdateUser(ObjectUtils.isNotEmpty(settingModel.getOperator())?settingModel.getOperator():"");
        boolean update = this.update(listenSetting, updateWrapper);
        if(update){
            log.info("修改聆听设置成功");
        }else{
            throw new BootException("修改聆听设置失败");
        }
    }

    @Override
    public IPage<ListenSettingVo> getListenSettingList(ListenSettingModel settingModel) {
        IPage<ListenSettingEntity> page = new Page<>(settingModel.getPageNum(), settingModel.getPageSize());
        IPage<ListenSettingEntity> listenSettingList = this.baseMapper.getListenSettingList(page, settingModel);
        IPage<ListenSettingVo> pages = new Page<>();
        if (ObjectUtils.isEmpty(listenSettingList.getRecords())) {
            log.info("暂无聆听设置信息");
            return pages;
        }
        pages.setCurrent(listenSettingList.getCurrent());
        pages.setPages(listenSettingList.getPages());
        pages.setSize(listenSettingList.getSize());
        pages.setTotal(listenSettingList.getTotal());
        final List<ListenSettingEntity> records = listenSettingList.getRecords();
        log.debug("转换前:[listenSettingList:{}]",  records);
        final List<ListenSettingMenuVo> listenSettingMenuList = this.getListenSettingMenuList();
        final Map<String, String> menu = listenSettingMenuList.stream().collect(Collectors.toMap(ListenSettingMenuVo::getId, ListenSettingMenuVo::getName));
        List<ListenSettingVo> soundListeningVos = records.stream().map(e->{
            final ListenSettingVo listenSettingVo = convertService.listenSettingEntityConvertToVo(e);
            if(ObjectUtils.isNotEmpty(e.getPermission())){
                String permission = e.getPermission();
                List<String> list = Arrays.stream(permission.split(",")).map(String::trim).filter(s -> ObjectUtils.isNotEmpty(s)).collect(Collectors.toList());
                StringBuffer stringBuffer = new StringBuffer();
                list.stream().forEach(k->{
                    if(ObjectUtils.isNotEmpty(menu)&& menu.containsKey(k)){
                        stringBuffer.append(menu.get(k)+"|");
                    }
                });
                String permissionName = "";
                if(stringBuffer.toString().endsWith("|")){
                    permissionName = stringBuffer.substring(0, stringBuffer.length()-1);
                }
                listenSettingVo.setPermissionName(permissionName);
            }
            listenSettingVo.setOperatorTime(ObjectUtils.isNotEmpty(e.getCreateTime())?e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")):"");
            listenSettingVo.setOperator(ObjectUtils.isNotEmpty(e.getCreateUser())?e.getCreateUser():"");
            listenSettingVo.setDepartmentName(ObjectUtils.isNotEmpty(e.getDepartName())?e.getDepartName():"");
            listenSettingVo.setName(ObjectUtils.isNotEmpty(e.getName())?e.getName():"");
            listenSettingVo.setUserId(ObjectUtils.isNotEmpty(e.getUserId())?e.getUserId():"");
            return listenSettingVo;
        }).collect(Collectors.toList());
        log.debug("转换后:[soundListeningVos:{}]",  soundListeningVos);
        pages.setRecords(soundListeningVos);
        return pages;
    }

    @Override
    public List<ListenSettingMenuVo> getListenSettingMenuList() {
        QueryWrapper<MenuEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().isNotNull(MenuEntity::getParentId);
        List<MenuEntity> menuEntityList = menuMapper.selectList(queryWrapper);
        if(ObjectUtils.isEmpty(menuEntityList)){
            log.info("暂无菜单信息");
            return Collections.emptyList();
        }
        return convertService.menuEntityConvertToVoList(menuEntityList);
    }

    @Override
    public List<DepartVo> getDepartTree(String brand) {
        List<CCDepartVo> jsonNode = util.fromCCGetOrganization("epi,aeolus,nammi");
        if(ObjectUtils.isEmpty(jsonNode)){
            log.info("暂无部门信息");
            return Collections.emptyList();
        }
        List<DepartVo> departVos = jsonNode.stream()
                .map(e -> {
                    final DepartVo departVo = convertService.convertToDepartVo(e);
                    if(ObjectUtils.isNotEmpty(e.getBrands())){
                        departVo.setBrand(e.getBrands().stream().map(CCBrandVo::getName).collect(Collectors.joining(",")));
                    }
                    return departVo;
                })
                .collect(Collectors.toList());
        List<DepartVo> topDepart = departVos.stream().filter(e -> ObjectUtils.isEmpty(e.getParentId())).collect(Collectors.toList());
        Map<String, List<DepartVo>> childMap = departVos.stream().filter(e -> ObjectUtils.isNotEmpty(e.getParentId())).collect(Collectors.groupingBy(DepartVo::getParentId));
        this.departTree(topDepart, childMap);
        return topDepart;
    }

    @Override
    public List<DepartUserVo> getUserListByDepartId(String departId, String brand) {
        List<CCUserVo> ccUserVos = util.fromCCGetUser(brand, departId);
        if(ObjectUtils.isEmpty(ccUserVos)){
            log.info("暂无用户信息");
            return Collections.emptyList();
        }
        return ccUserVos.stream().map(e -> convertService.convertToDepartUserVo(e)).collect(Collectors.toList());
    }


    void departTree(List<DepartVo> topDepart, Map<String, List<DepartVo>> childMap) {
        if (ObjectUtils.isEmpty(topDepart)) {
            return;
        }
        for (DepartVo departVo : topDepart) {
            List<DepartVo> departInfoVos = childMap.get(departVo.getValue());
            this.departTree(departInfoVos, childMap);
            departVo.setChild(departInfoVos);
        }
    }




}
