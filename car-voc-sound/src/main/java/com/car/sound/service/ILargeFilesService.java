package com.car.sound.service;



import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.LargeDigitaFilesModel;

import java.util.List;
import java.util.function.Function;

/**
 * @Title: ILargeDigitaFilesService
 * @Package: com.voc.service.insights.engine.api
 * @Description:
 * @Author: cuick
 * @Date: 2024/12/15 18:39
 * @Version:1.0
 */
public interface ILargeFilesService {

    String getFileUrl(LargeDigitaFilesModel model);

    List<LargeDigitaFilesModel> getFileList(LargeDigitaFilesModel model);

    LargeDigitaFilesModel getFile(LargeDigitaFilesModel model);
    void insert(LargeDigitaFilesModel model);


    void exportSoundList(String fileName, String taskId, long total, Function<IPage, List<?>> data, Class<?> clazz);
}
