package com.car.sound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.CarouselImageEntity;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.CarouselImageMapper;
import com.car.sound.model.CarouselImageModel;
import com.car.sound.service.CarouselImageService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.vo.CarouselImageVo;
import com.car.sound.vo.UserVo;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 13:44
 * @描述:
 **/
@Service
@Slf4j
public class CarouselImageServiceImpl extends ServiceImpl<CarouselImageMapper, CarouselImageEntity> implements CarouselImageService {
    @Autowired
    SoundConvertService convertService;

    @Override
    public void saveCarouselImage(CarouselImageModel model) {
        Assert.hasLength(model.getName(), "名称不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getSort()), "排序不能为空");
        Assert.hasLength(model.getImageUrl(), "图片地址不能为空");
        final UserVo user = (UserVo)SpringContextUtils.getUser();
        log.debug("转换前:[model:{}]",  model);
        CarouselImageEntity carouselImage = convertService.carouselImageModelConvertToEntity(model);
        carouselImage.setCreateTime(LocalDateTime.now());
        carouselImage.setCreateUser(ObjectUtils.isNotEmpty(user)?user.getUserName():"");
        log.debug("转换后:[carouselImage:{}]",  carouselImage);
        boolean save = this.save(carouselImage);
        if(save){
            log.info("保存轮播图成功");
        }else{
            throw new BootException("保存轮播图失败");
        }
    }

    @Override
    public void updateCarouselImage(CarouselImageModel model) {
        Assert.hasLength(model.getName(), "名称不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getSort()), "排序不能为空");
        Assert.hasLength(model.getImageUrl(), "图片地址不能为空");
        Assert.hasLength(model.getId(), "id不能为空");
        final UserVo user = (UserVo)SpringContextUtils.getUser();
        log.debug("转换前:[model:{}]",  model);
        CarouselImageEntity carouselImage = convertService.carouselImageModelConvertToEntity(model);
        carouselImage.setUpdateTime(LocalDateTime.now());
        carouselImage.setUpdateUser(ObjectUtils.isNotEmpty(user)?user.getUserName():"");
        log.debug("转换后:[carouselImage:{}]",  carouselImage);
        QueryWrapper<CarouselImageEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CarouselImageEntity::getId, model.getId());
        boolean update = this.update(carouselImage, queryWrapper);
        if(update){
            log.info("修改轮播图成功");
        }else{
            throw new BootException("修改轮播图失败");
        }
    }

    @Override
    public void deleteCarouselImage(String id) {
        Assert.hasLength(id, "id不能为空");
        final UserVo user = (UserVo)SpringContextUtils.getUser();
        UpdateWrapper<CarouselImageEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarouselImageEntity::getId, id);
        updateWrapper.lambda().set(CarouselImageEntity::getDel, 1);
        updateWrapper.lambda().set(CarouselImageEntity::getUpdateTime, LocalDateTime.now());
        updateWrapper.lambda().set(CarouselImageEntity::getUpdateUser, ObjectUtils.isNotEmpty(user)?user.getUserName():"");
        boolean delete = this.update(updateWrapper);
        if(delete){
            log.info("删除轮播图成功");
        }else{
            throw new BootException("删除轮播图失败");
        }
    }

    @Override
    public CarouselImageVo getCarouselImageById(CarouselImageModel model) {
        Assert.hasLength(model.getId(), "id不能为空");
        QueryWrapper<CarouselImageEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CarouselImageEntity::getId, model.getId());
        queryWrapper.lambda().eq(CarouselImageEntity::getDel, 0);
        CarouselImageEntity carouselImage = this.getOne(queryWrapper);
        if(ObjectUtils.isEmpty(carouselImage)){
            log.info("未查询到轮播图");
            return null;
        }
        return convertService.carouselImageEntityConvertToVo(carouselImage);
    }

    @Override
    public IPage<CarouselImageVo> getCarouselImageList(CarouselImageModel model) {
        IPage<CarouselImageEntity> page = new Page<>(model.getPageNum(), model.getPageSize());
        IPage<CarouselImageEntity> carouselImageList = this.baseMapper.getCarouselImageList(page, model);
        IPage<CarouselImageVo> pages = new Page<>();
        if(ObjectUtils.isEmpty(carouselImageList.getRecords())){
            log.info("暂无轮播图信息");
            return pages;
        }
        pages.setCurrent(carouselImageList.getCurrent());
        pages.setPages(carouselImageList.getPages());
        pages.setSize(carouselImageList.getSize());
        pages.setTotal(carouselImageList.getTotal());
        if(ObjectUtils.isNotEmpty(carouselImageList.getRecords())){
            final List<CarouselImageEntity> records = carouselImageList.getRecords();
            List<CarouselImageVo> carouselImageVos = convertService.carouselImageEntityConvertToVoList(records);
            pages.setRecords(carouselImageVos);
        }
        return pages;
    }
}
