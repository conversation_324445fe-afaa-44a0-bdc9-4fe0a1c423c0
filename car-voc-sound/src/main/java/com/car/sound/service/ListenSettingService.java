package com.car.sound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.ListenSettingModel;
import com.car.sound.vo.DepartUserVo;
import com.car.sound.vo.DepartVo;
import com.car.sound.vo.ListenSettingMenuVo;
import com.car.sound.vo.ListenSettingVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 14:26
 * @描述:
 **/
public interface ListenSettingService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 14:26
     * @描述  新增聆听设置
     * @param settingModel
     * @return void
     **/
    void saveListenSetting(ListenSettingModel settingModel);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 14:26
     * @描述  修改聆听设置
     * @param settingModel
     * @return void
     **/
    void updateListenSetting(ListenSettingModel settingModel);
    /**
     * @param settingModel
     * @return java.util.List<com.car.sound.vo.ListenSettingVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 14:32
     * @描述 获取聆听设置列表
     **/
    IPage<ListenSettingVo> getListenSettingList(ListenSettingModel settingModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 14:32
     * @描述 获取聆听设置菜单列表
     * @return java.util.List<com.car.sound.vo.ListenSettingMenuVo>
     **/
    List<ListenSettingMenuVo> getListenSettingMenuList();
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 14:32
     * @描述 获取部门树
     * @return java.util.List<com.car.sound.vo.DepartVo>
     **/
    List<DepartVo> getDepartTree(String brand);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/6/4 11:01
     * @描述   根据部门获取用户列表
     * @param departId
     * @param brand
     * @return java.util.List<com.car.sound.vo.DepartUserVo>
     **/
    List<DepartUserVo> getUserListByDepartId(String departId, String brand);
}
