package com.car.sound.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.car.sound.entity.ListenSettingEntity;
import com.car.sound.entity.MenuEntity;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.ListenSettingMapper;
import com.car.sound.mapper.MenuMapper;
import com.car.sound.model.TokenVaildModel;
import com.car.sound.service.AuthenticationService;
import com.car.sound.service.UserService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.vo.ListenSettingMenuVo;
import com.car.sound.vo.TokenVaildVo;
import com.car.sound.vo.UserVo;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.JwtUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 18:52
 * @描述:
 **/
@Service
@Slf4j
public class AuthenticationServiceImpl implements AuthenticationService {
    @Value("${configuration.tokenCheckToken.checkTokenValidPath}")
    String checkTokenValidPath;
    @Value("${configuration.tokenCheckToken.clientId}")
    String clientId;
    @Value("${configuration.tokenCheckToken.clientSecret}")
    String clientSecret;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    UserService userService;
    @Autowired
    ListenSettingMapper listenSettingMapper;
    @Autowired
    MenuMapper menuMapper;
    @Autowired
    SoundConvertService convertService;
    @Override
    public TokenVaildVo getToken(TokenVaildModel checkToken) {
        Assert.hasLength(checkToken.getAccessToken(), "accessToken不能为空");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        header.put("Authorization", "Basic " + Base64.encode(clientId + ":" + clientSecret));
        final String accessToken = checkToken.getAccessToken();
        // 创建请求头
        String result = null;
        try {
            //调用cc平台验证token，并获取用户信息
            result = HttpUtil.createPost(checkTokenValidPath).addHeaders(header).body("&token=" + accessToken).execute().body();
        } catch (HttpException e) {
            throw new RuntimeException(e);
        }
        log.info("result:{}", result);
        TokenVaildVo tokenVaildVo = new TokenVaildVo();
        if (JSONUtil.isJson(result)) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            if (jsonObject.get("authenticated") != null && jsonObject.get("authenticated").equals(true)&&jsonObject.containsKey("sub")) {
                final String userId = jsonObject.getStr("sub");
                final UserVo userVo = userService.getUserInfoByUserId(userId);
                if(ObjectUtils.isEmpty(userVo)){
                    throw new BootException("用户未授权！");
                }
                // 生成token
                String token = JwtUtil.sign(userVo.getUserId(), userVo.getUserName());
                // 设置token缓存有效时间 7天
                redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token, JwtUtil.EXPIRE_TIME + 24, TimeUnit.HOURS);
                tokenVaildVo.setUserId(jsonObject.getStr("sub"));
                tokenVaildVo.setAccessToken(token);
            } else {
                throw new BootException("统一平台验证失败！");
            }
        }
        return tokenVaildVo;
    }

    @Override
    public List<ListenSettingMenuVo> getMenu() {
        final String userId = SpringContextUtils.getUserId();
        QueryWrapper<ListenSettingEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ListenSettingEntity::getAccount,userId);
        queryWrapper.lambda().eq(ListenSettingEntity::getStatus,"1");
        final ListenSettingEntity listenSettingList = listenSettingMapper.selectOne(queryWrapper);
        if(ObjectUtils.isEmpty(listenSettingList)||ObjectUtils.isEmpty(listenSettingList.getPermission())){
            log.info("当前用户无菜单权限");
            return Collections.emptyList();
        }
        //菜单权限
        final String permission = listenSettingList.getPermission();
        List<String> collect = Arrays.stream(permission.split(","))
                .map(String::trim)        // 去除每个元素的前后空格
                .filter(s -> !s.isEmpty()) // 过滤空字符串
                .collect(Collectors.toList());
        QueryWrapper<MenuEntity> menuQueryWrapper = new QueryWrapper<>();
        menuQueryWrapper.lambda().in(MenuEntity::getId,collect);
        final List<MenuEntity> menuList = menuMapper.selectList(menuQueryWrapper);
        if(ObjectUtils.isEmpty(menuList)){
            log.info("暂无菜单信息");
            return Collections.emptyList();
        }
        return convertService.menuEntityConvertToVoList(menuList);
    }
}
