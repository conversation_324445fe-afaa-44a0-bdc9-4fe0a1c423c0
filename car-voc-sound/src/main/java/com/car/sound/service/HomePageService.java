package com.car.sound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.vo.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 10:12
 * @描述:
 **/
public interface HomePageService {
    /**
     * 获取首页聆听任务列表
     * @param model
     * @return
     */
    IPage<ListenTaskVo>  getSoundListeningTaskList(ListenTaskModel model);
    /**
     * 获取首页个人排行榜
     * @return
     */
    PersonRankingVo  getPersonRanking();
    /**
     * 获取首页部门排行榜
     * @return
     */
    DepartRankingVo getDepartRanking();
    /**
     * 获取首页意图列表
     * @return
     */
    List<IntentionVo> getIntentionList(ListenTaskModel model);
    /**
     * 获取首页轮播图列表
     * @return
     */
    List<CarouselImageVo> getCarouselImageList();
}
