package com.car.sound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.entity.CarouselImageEntity;
import com.car.sound.entity.SoundListeningEntity;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.CarouselImageMapper;
import com.car.sound.mapper.HomePageMapper;
import com.car.sound.mapper.SoundListeningMapper;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.service.DictService;
import com.car.sound.service.HomePageService;
import com.car.sound.service.ListenTaskService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.vo.*;
import com.car.voc.common.util.SpringContextUtils;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 10:30
 * @描述:
 **/
@Service
@Slf4j
public class HomePageServiceImpl implements HomePageService {

    @Autowired
    private ListenTaskService listenTaskService;
    @Autowired
    HomePageMapper homePageMapper;
    @Autowired
    SoundListeningMapper soundListeningMapper;
    @Autowired
    DictService dictService;
    @Autowired
    CarouselImageMapper carouselImageMapper;
    @Autowired
    SoundConvertService convertService;

    @Override
    public IPage<ListenTaskVo> getSoundListeningTaskList(ListenTaskModel model) {
        return listenTaskService.getListenTaskList(model);
    }

    @Override
    public PersonRankingVo getPersonRanking() {
        PersonRankingVo build = PersonRankingVo.builder().build();
        //获取累计个人排名
        List<RankingListVo> personRanking = homePageMapper.getPersonRanking(null);
        final String userId = SpringContextUtils.getUserId();
        AtomicInteger atomicInteger = new AtomicInteger(1);
        AtomicDouble integral = new AtomicDouble(0);
        if(ObjectUtils.isNotEmpty(userId)){
            for (int i = 0; i < personRanking.size(); i++) {
                RankingListVo e = personRanking.get(i);
                if(userId.equals(e.getUserId())){
                    integral.set(e.getIntegral().doubleValue());
                    build.setRanking(atomicInteger.get());
                    build.setIntegral(BigDecimal.valueOf(integral.get()).compareTo(BigDecimal.ZERO)==0?BigDecimal.ZERO.intValue():BigDecimal.valueOf(integral.get()).intValue());
                }else{
                    atomicInteger.getAndIncrement();
                }
            }
        }
        build.setCumulativeRankingList(ObjectUtils.isNotEmpty(personRanking)?personRanking:Collections.emptyList());
        //获取本月个人排名
        String month = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<RankingListVo> cumulativeRanking = homePageMapper.getPersonRanking(month);
        AtomicInteger cumulativeRankingInteger = new AtomicInteger(1);
        AtomicDouble cumulativeIntegral = new AtomicDouble(0);
        if(ObjectUtils.isNotEmpty(userId)){
            for (int i = 0; i < cumulativeRanking.size(); i++) {
                RankingListVo e = cumulativeRanking.get(i);
                if(userId.equals(e.getUserId())){
                    cumulativeIntegral.set(e.getIntegral().doubleValue());
                    build.setCumulativeIntegral(BigDecimal.valueOf(cumulativeIntegral.get()).intValue());
                    build.setCumulativeRanking(cumulativeRankingInteger.get());
                }else{
                    cumulativeRankingInteger.getAndIncrement();
                }
            }
        }
        build.setRankingList(ObjectUtils.isNotEmpty(cumulativeRanking)?cumulativeRanking:Collections.emptyList());
        return build;
    }

    @Override
    public DepartRankingVo getDepartRanking() {
        //获取累计部门排名
        List<RankingListVo> departRanking = homePageMapper.getDepartRanking(null);
        //获取本月部门排名
        String month = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<RankingListVo> cumulativeRanking = homePageMapper.getDepartRanking(month);
        return DepartRankingVo
                .builder()
                .cumulativeRankingList(ObjectUtils.isNotEmpty(departRanking)?departRanking:Collections.emptyList())
                .rankingList(ObjectUtils.isNotEmpty(cumulativeRanking)?cumulativeRanking:Collections.emptyList())
                .build();
    }

    @Override
    public List<IntentionVo> getIntentionList(ListenTaskModel model) {
        UserVo user = (UserVo)SpringContextUtils.getUser();
        QueryWrapper<SoundListeningEntity> soundListeningEntityQueryWrapper = new QueryWrapper<>();
        soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
        soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getStatus, 1);
        soundListeningEntityQueryWrapper.lambda().le(SoundListeningEntity::getValidStartTime, LocalDateTime.now());
        if(ObjectUtils.isNotEmpty(model.getBrandCode())){
            soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getBrand, model.getBrandCode());
        }
        if(ObjectUtils.isNotEmpty(model.getCarSeriesCode())){
            soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getCarSeries, model.getCarSeriesCode());
        }
        if(ObjectUtils.isNotEmpty(user)){
            soundListeningEntityQueryWrapper.lambda().like(SoundListeningEntity::getListeningRange, user.getDepartId());
        }
        final List<SoundListeningEntity> soundListeningEntities = soundListeningMapper.selectList(soundListeningEntityQueryWrapper);
        //根据工单类型分组
        final Map<String, List<SoundListeningEntity>> collect1 = soundListeningEntities.stream().filter(e -> ObjectUtils.isNotEmpty(e.getWorkOrderType())).collect(Collectors.groupingBy(SoundListeningEntity::getWorkOrderType));
        final List<DictItemVo> dictItemByDictCode = dictService.getDictItemByDictCode("work_order_type");
        if(ObjectUtils.isEmpty(dictItemByDictCode)){
            throw new BootException("工单类型字典为空");
        }
        List<LabelVo> allLabel = new ArrayList<>();
        List<IntentionVo> intentionVos = dictItemByDictCode.stream().map(e -> {
            final String key = e.getKey();
            final String value1 = e.getValue();
            IntentionVo intentionVo = IntentionVo.builder().id(value1).intention(key).taskCount(0).build();
            if (ObjectUtils.isNotEmpty(collect1) && collect1.containsKey(value1)) {
                List<SoundListeningEntity> soundListeningEntities1 = collect1.get(value1);
                Map<String, List<SoundListeningEntity>> collect2 = soundListeningEntities1.stream().collect(Collectors.groupingBy(SoundListeningEntity::getTaskId));
                intentionVo.setTaskCount(ObjectUtils.isNotEmpty(collect2)?collect2.size():0);
                if(ObjectUtils.isNotEmpty(soundListeningEntities1)){
                    final Map<String, List<SoundListeningEntity>> collect = soundListeningEntities1.stream().collect(Collectors.groupingBy(SoundListeningEntity::getFirstLabel));
                    List<LabelVo> labelVos = collect.entrySet().stream().map(k -> {
                        LabelVo labelVo = homePageMapper.UpwardFindBusinessTopLabelHierarchical(k.getKey());
                        final List<SoundListeningEntity> value = k.getValue();
                        if(ObjectUtils.isNotEmpty(labelVo)){
                            Map<String, List<SoundListeningEntity>> collect3 = value.stream().collect(Collectors.groupingBy(SoundListeningEntity::getTaskId));
                            labelVo.setTaskCount(collect3.size());
                        }else {
                            labelVo = homePageMapper.UpwardFindQYTopLabelHierarchical(k.getKey());
                        }
                        labelVo.setTaskCount(value.size());
                        return labelVo;
                    }).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
                    intentionVo.setLabel(labelVos);
                    if(ObjectUtils.isNotEmpty(labelVos)){
                        allLabel.addAll(labelVos);
                    }
                }
            }
            return intentionVo;
        }).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        int sum = intentionVos.stream().mapToInt(IntentionVo::getTaskCount).sum();
        Map<String, LabelVo> allLabelMap = allLabel.stream()
                .collect(Collectors.toMap(
                        LabelVo::getName,
                        label -> label,
                        (existing, replacement) -> {
                            existing.setTaskCount(existing.getTaskCount() + replacement.getTaskCount());
                            return existing;
                        }
                ));
        intentionVos.add(IntentionVo.builder().intention("全部").taskCount(sum).label(new ArrayList<>(allLabelMap.values())).build());
        return intentionVos;
    }

    @Override
    public List<CarouselImageVo> getCarouselImageList() {
        QueryWrapper<CarouselImageEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CarouselImageEntity::getStatus, 1);
        queryWrapper.lambda().eq(CarouselImageEntity::getDel, 0);
        queryWrapper.lambda().orderByAsc(CarouselImageEntity::getSort);
        final List<CarouselImageEntity> carouselImageEntities = carouselImageMapper.selectList(queryWrapper);
        if(ObjectUtils.isEmpty(carouselImageEntities)){
            log.info("未查询到轮播图");
            return Collections.emptyList();
        }
        return convertService.carouselImageEntityConvertToVoList(carouselImageEntities);
    }
}
