package com.car.sound.service.converts;


import com.car.sound.entity.*;
import com.car.sound.model.*;
import com.car.sound.vo.*;
import org.mapstruct.*;

import java.util.List;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SoundConvertService {
    CommentSettingEntity commentSettingModelConvertToEntity(CommentSettingModel settingModel);
    @Mapping(target = "createTime", ignore = true)
    CommentSettingVo commentSettingEntityConvertToVo(CommentSettingEntity commentSetting);

    List<CommentSettingVo> commentSettingEntityConvertToVoList(List<CommentSettingEntity> records);

    CarouselImageEntity carouselImageModelConvertToEntity(CarouselImageModel model);

    CarouselImageVo carouselImageEntityConvertToVo(CarouselImageEntity carouselImage);

    List<CarouselImageVo> carouselImageEntityConvertToVoList(List<CarouselImageEntity> records);

    @Mapping(target = "listeningRange", ignore = true)
    SoundListeningEntity soundListeningModelConvertToEntity(SoundListeningModel model);

    @Mapping(target = "createTime", ignore = true)
    SoundListeningVo soundListeningEntityConvertToVo(SoundListeningEntity soundListening);

    UserVo userEntityConvertToVo(UserEntity one);

    @Mappings({
            @Mapping(target = "permission", ignore = true),
            @Mapping(target = "account", ignore = true),
    })
    ListenSettingEntity listenSettingModelConvertToEntity(ListenSettingModel settingModel);

    @Mapping(target = "operatorTime", ignore = true)
    ListenSettingVo listenSettingEntityConvertToVo(ListenSettingEntity e);

    List<UserVo> userEntityConvertToVoList(List<UserEntity> list);

    ListenTaskVo soundListeningEntityConvertToListenTaskVo(SoundListeningEntity e);
    @Mappings({
            @Mapping(target = "commentTime", ignore = true),
            @Mapping(target = "replyTime", ignore = true)
    })
    CommentVo commentEntityConvertToCommentVo(CommentEntity e);

    CommentEntity commentModelConvertToEntity(CommentModel model);

    ListenTaskDataVo soundListeningEntityConvertToListenTaskDataVo(SoundListeningEntity e);

    List<ListenSettingMenuVo> menuEntityConvertToVoList(List<MenuEntity> menuEntityList);

    ExcelCommentVo commentEntityConvertToEExcelCommentVo(CommentEntity e);

    @Mappings({
            @Mapping(target = "label", source = "orgName"),
            @Mapping(target = "value", source = "id")
    })
    DepartVo convertToDepartVo(CCDepartVo departInfoVos);
    @Mappings({
            @Mapping(target = "label", source = "name"),
            @Mapping(target = "value", source = "id")
    })
    DepartUserVo convertToDepartUserVo(CCUserVo e);
}

