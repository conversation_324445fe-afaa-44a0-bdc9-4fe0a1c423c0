package com.car.sound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.CarouselImageModel;
import com.car.sound.vo.CarouselImageVo;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 13:43
 * @描述:
 **/
public interface CarouselImageService {

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 13:43
     * @描述  新增轮播图
     * @param model
     * @return void
     **/
    void saveCarouselImage(CarouselImageModel model);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 13:43
     * @描述  修改轮播图
     * @param model
     * @return void
     **/
    void updateCarouselImage(CarouselImageModel model);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 13:43
     * @描述  删除轮播图
     * @param id
     * @return void
     **/
    void deleteCarouselImage(String id);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 13:43
     * @描述  根据id查询轮播图
     * @param model
     * @return com.car.sound.vo.CarouselImageVo
     **/
    CarouselImageVo getCarouselImageById(CarouselImageModel model);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 13:43
     * @描述  查询轮播图列表
     * @param model
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.car.sound.vo.CarouselImageVo>
     **/
    IPage<CarouselImageVo> getCarouselImageList(CarouselImageModel model);
}
