package com.car.sound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.CommentModel;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.vo.CommentVo;
import com.car.sound.vo.ListenTaskDataVo;
import com.car.sound.vo.ListenTaskStatisticsVo;
import com.car.sound.vo.ListenTaskVo;

import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 13:46
 * @描述:
 **/
public interface ListenTaskService {
    /**
     * @param model
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.car.sound.vo.ListenTaskVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 13:52
     * @描述 获取聆听任务列表
     **/
    IPage<ListenTaskVo> getListenTaskList(ListenTaskModel model);

    /**
     * @param model
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.car.sound.vo.CommentVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 根据任务id获取评论列表
     **/
    IPage<CommentVo> getCommentList(ListenTaskModel model);


    Map<String, String> getSoundByWorkOrderId(List<String> workOrderNo);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 收藏任务
     **/
    void saveFavorite(ListenTaskModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 取消收藏任务
     **/
    void cancelFavorite(ListenTaskModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 点赞评论
     **/
    void saveUpvote(CommentModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 取消点赞评论
     **/
    void cancelUpvote(CommentModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 保存评论
     **/
    void saveComment(CommentModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 保存或更新聆听任务
     **/
    void saveOrUpdateListenTask(ListenTaskModel model);

    /**
     * @param
     * @return com.car.sound.vo.ListenTaskStatisticsVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 获取聆听任务统计
     **/
    ListenTaskStatisticsVo getListenTaskStatistics();

}
