package com.car.sound.service.impl;

import com.car.sound.mapper.DictMapper;
import com.car.sound.service.DictService;
import com.car.sound.vo.DictItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 15:00
 * @描述:
 **/
@Service
@Slf4j
public class DictServiceImpl implements DictService {
    @Autowired
    DictMapper dictMapper;
    @Override
    public List<DictItemVo> getDictItemByDictCode(String dictCode) {
        Assert.hasLength(dictCode,  "dictCode不能为空");
        return dictMapper.getDictItemByDictCode(dictCode);
    }
}
