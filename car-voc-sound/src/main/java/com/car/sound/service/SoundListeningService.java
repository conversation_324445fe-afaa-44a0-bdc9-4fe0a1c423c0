package com.car.sound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.CommentModel;
import com.car.sound.model.SoundListeningModel;
import com.car.sound.vo.*;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 15:06
 * @描述:
 **/
public interface SoundListeningService {
    /**
     * 根据工单号获取声音
     *
     * @param workOrderNo 工单号
     * @return
     */
    WorkNoVo getSoundByWorkOrderNo(String workOrderNo);

    /**
     * 保存声音
     * @param model
     */
    void saveSoundListening(SoundListeningModel model);

    /**
     * 修改声音
     * @param model
     */
    void updateSoundListening(SoundListeningModel model);
    /**
     * 删除声音
     * @param taskId
     */
    void deleteSoundListening(String taskId);
    /**
     * 根据id获取声音
     * @param model
     * @return
     */
    SoundListeningVo getSoundListeningById(SoundListeningModel model);
    /**
     * 获取声音列表
     * @param model
     * @return
     */
    IPage<SoundListeningVo> getSoundListeningList(SoundListeningModel model);

    /**
     * @param model
     * @return com.car.sound.vo.ListenTaskStatisticsVo
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 获取任务数据统计
     **/
    ListenTaskStatisticsVo getSoundListenDataStatistics(SoundListeningModel model);

    /**
     * @param model
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.car.sound.vo.ListenTaskDataVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 获取任务数据列表
     **/
    IPage<ListenTaskDataVo> getSoundListenDataList(SoundListeningModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 导出任务数据列表
     **/
    String exportSoundListenDataList(SoundListeningModel model);
    /**
     * @param model
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.car.sound.vo.CommentVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 获取评论列表
     **/
    IPage<CommentVo> getCommentList(CommentModel model);
    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 导出评论列表
     **/
    String exportCommentList(CommentModel model);

    /**
     * @param model
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 回复评论
     **/
    void replyComment(CommentModel model);
    /**
     * @return java.util.List<com.car.sound.vo.CommentSettingVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/13 14:09
     * @描述 获取评论类型列表
     **/
    List<CommentSettingVo> getCommentTypeList();
}
