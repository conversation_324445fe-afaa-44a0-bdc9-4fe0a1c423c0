package com.car.sound.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.ttl.TtlWrappers;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.config.SoundMinioConfig;
import com.car.sound.entity.LargeFilesEntity;
import com.car.sound.mapper.LargeFileMapper;
import com.car.sound.model.LargeDigitaFilesModel;
import com.car.sound.service.ILargeFilesService;
import com.car.sound.util.SoundUploadFileService;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.tomcat.util.http.fileupload.FileItem;
import org.apache.tomcat.util.http.fileupload.FileItemFactory;
import org.apache.tomcat.util.http.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
@SuppressWarnings("unchecked")
public class LargeFilesServiceImpl extends ServiceImpl<LargeFileMapper, LargeFilesEntity> implements ILargeFilesService {

    @Value("${files.readBatchSize:10000}")
    int batchSize = 10000;
    @Value("${files.readTimeOut:600}")
    long allOfTimeoutSeconds;
    @Value("${files.maxRows:200000}")
    long maxRows;
    @Autowired
    SoundUploadFileService uploadFileService;
    @Autowired
    SoundMinioConfig minioConfig;


    @Override
    public String getFileUrl(LargeDigitaFilesModel model) {
        Assert.isTrue(StrUtil.isNotBlank(model.getUserId()), "getUserId cannot be empty");
        Assert.isTrue(StrUtil.isNotBlank(model.getType()), "getType cannot be empty");
        QueryWrapper<LargeFilesEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(LargeFilesEntity::getUserId, model.getUserId());
        wrapper.lambda().eq(LargeFilesEntity::getType, model.getType());

        LargeFilesEntity entity = this.baseMapper.selectOne(wrapper);
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        final String url = uploadFileService.getObjectUrl("voc-dev", entity.getFileKey(), 30);
        log.info("getFileUrl {}", url);

        return url;
    }

    @Override
    public List<LargeDigitaFilesModel> getFileList(LargeDigitaFilesModel model) {
        Assert.isTrue(StrUtil.isNotBlank(model.getUserId()), "getUserId cannot be empty");
        LargeFilesEntity entity = new LargeFilesEntity();
        BeanUtils.copyProperties(model, entity);

        List<LargeFilesEntity> list = this.baseMapper.getFileList(entity);

        if (CollUtil.isEmpty(list)) {
            log.info("getFileList empty");
            return CollUtil.newArrayList();
        }
        List<LargeDigitaFilesModel> modelList = new ArrayList<>();
        for (LargeFilesEntity filesEntity : list) {
            LargeDigitaFilesModel filesModel = new LargeDigitaFilesModel();
            BeanUtils.copyProperties(filesEntity, filesModel);
            modelList.add(filesModel);
        }

        modelList = modelList.stream()
                .map(item -> {
                    final String url = uploadFileService.getObjectUrl("voc-dev", item.getFileKey(), 30);
                    item.setFileUrl(url);
                    return item;
                }).collect(Collectors.toList());
        return modelList;
    }

    @Override
    public LargeDigitaFilesModel getFile(LargeDigitaFilesModel model) {
        Assert.isTrue(StrUtil.isNotBlank(model.getUserId()), "getUserId cannot be empty");

        LargeFilesEntity entity = new LargeFilesEntity();
        BeanUtils.copyProperties(model, entity);

        LargeFilesEntity LargeFilesEntity = this.baseMapper.getFile(entity);
        if (ObjUtil.isEmpty(LargeFilesEntity)) {
            log.info("getFile empty");
            return LargeDigitaFilesModel.builder().build();
        }
        LargeDigitaFilesModel largeDigitaFilesModel = new LargeDigitaFilesModel();
        BeanUtils.copyProperties(LargeFilesEntity, largeDigitaFilesModel);

        if (ObjectUtils.isNotEmpty(LargeFilesEntity.getFileKey())) {
            String url = minioConfig.getWebEndpoint().concat("/voc-dev").concat(LargeFilesEntity.getFileKey());
            largeDigitaFilesModel.setFileUrl(url);
        } else {
            log.info("暂未执行完成");
        }
        return largeDigitaFilesModel;
    }

    @Override
    public void insert(LargeDigitaFilesModel model) {
        LargeFilesEntity entity = new LargeFilesEntity();
        BeanUtils.copyProperties(model, entity);
        this.baseMapper.insert(entity);
    }

    @Override
    public void exportSoundList(String fileName, String taskId, long total, Function<IPage, List<?>> data, Class<?> clazz) {
        try {
            Assert.isTrue(StrUtil.isNotBlank(fileName), "fileName cannot be empty");
            IPage page_ = new Page(1, batchSize);
            page_.setTotal(total);
            final long pages = page_.getPages();
            log.info("开始拆解任务，共{}页，每页{}条数据", pages, batchSize);

            FileItemFactory factory = new DiskFileItemFactory(16, null);
            final String tempFileName = taskId.concat(".xlsx");
            FileItem fileItem = factory.createItem("textField", "text/plain", true, taskId.concat(tempFileName));
            log.info("创建临时文件 {}", tempFileName);
            @Cleanup
            OutputStream outputStream = fileItem.getOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream).head(clazz).build();

            if (total <= 0) {
                log.info("空数据集 {}", taskId);
                excelWriter.write(ArrayList::new, EasyExcel.writerSheet("Sheet1").build());

            } else {
                List<CompletableFuture> futureList = new ArrayList<>();
                for (int i = 1; i <= pages; i++) {
                    final int finalI = i;

                    futureList.add(CompletableFuture.supplyAsync(TtlWrappers.wrap(() -> {
                        final List<?> rows = data.apply(new Page(finalI, batchSize));
                        if (CollUtil.isEmpty(rows)) {
                            log.info("查询到数据未空");
                            return null;
                        }
                        synchronized (excelWriter) {
                            excelWriter.write(rows, EasyExcel.writerSheet("Sheet1").build());
                        }
                        return null;
                    })));
                }
                try {
                    //TODO 记录数据-本次附件下载任务
                    CompletableFuture.allOf(futureList.stream().toArray(CompletableFuture[]::new)).get(allOfTimeoutSeconds, java.util.concurrent.TimeUnit.SECONDS);
                    log.info("临时文件写入完成");
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }
            try {
                excelWriter.finish();
                log.info("文件写入开始");

                final String fileKey = this.cleanFileName(fileName).concat(".xlsx");
                @Cleanup
                InputStream is = fileItem.getInputStream();
                uploadFileService.putObject(minioConfig.getBucketName(), fileKey, is);
                fileItem.delete();
                log.info("文件上传完成");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                this.baseMapper.updateById(LargeFilesEntity.builder().id(taskId).status("0").build());
                throw new RuntimeException(e);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    // 定义非法字符的正则表达式
    private static final String ILLEGAL_CHARACTERS_REGEX = "[\\\\/:*?\"<>|]";

    // 定义保留名称
    private static final String[] RESERVED_NAMES = {"CON", "PRN", "AUX", "NUL",
            "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
            "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};

    public String cleanFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "default";
        }

        // 替换非法字符
        String cleanedFileName = fileName.replaceAll(ILLEGAL_CHARACTERS_REGEX, "_");

        // 检查是否为保留名称
        for (String reservedName : RESERVED_NAMES) {
            if (cleanedFileName.equalsIgnoreCase(reservedName)) {
                cleanedFileName = cleanedFileName + "_";
                break;
            }
        }

        return cleanedFileName;
    }
}
