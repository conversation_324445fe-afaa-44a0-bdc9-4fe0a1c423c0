package com.car.sound.service;

import com.car.sound.vo.DictItemVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 14:55
 * @描述:
 **/
public interface DictService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/9 14:59
     * @描述   根据dictCode获取item数据
     * @param dictCode
     * @return java.util.List<com.car.sound.vo.DictItemVo>
     **/
    List<DictItemVo> getDictItemByDictCode(String dictCode);
}
