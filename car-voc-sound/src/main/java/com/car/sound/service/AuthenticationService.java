package com.car.sound.service;

import com.car.sound.model.TokenVaildModel;
import com.car.sound.vo.ListenSettingMenuVo;
import com.car.sound.vo.TokenVaildVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 18:44
 * @描述:
 **/
public interface AuthenticationService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 18:51
     * @描述  获取token
     * @param checkToken
     * @return com.car.sound.vo.TokenVaildVo
     **/
    TokenVaildVo getToken(TokenVaildModel checkToken);

    /**
     * @return java.util.List<com.car.sound.vo.MenuVo>
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/8 18:51
     * @描述 获取菜单
     **/
    List<ListenSettingMenuVo> getMenu();
}
