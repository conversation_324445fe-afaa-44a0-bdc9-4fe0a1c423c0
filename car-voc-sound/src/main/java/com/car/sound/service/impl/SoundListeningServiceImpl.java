package com.car.sound.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.CommentEntity;
import com.car.sound.entity.FavoriteEntity;
import com.car.sound.entity.SoundListeningEntity;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.CommentMapper;
import com.car.sound.mapper.FavoriteMapper;
import com.car.sound.mapper.HomePageMapper;
import com.car.sound.mapper.SoundListeningMapper;
import com.car.sound.model.CommentModel;
import com.car.sound.model.ListeningRangeModel;
import com.car.sound.model.SoundListeningModel;
import com.car.sound.service.CommentSettingService;
import com.car.sound.service.ILargeFilesService;
import com.car.sound.service.SoundListeningService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.util.SoundUploadFileService;
import com.car.sound.vo.*;
import com.car.voc.common.util.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/8 15:16
 * @描述:
 **/
@Service
@Slf4j
public class SoundListeningServiceImpl extends ServiceImpl<SoundListeningMapper, SoundListeningEntity> implements SoundListeningService {
    @Autowired
    SoundConvertService convertService;
    @Autowired
    CommentMapper commentMapper;
    @Autowired
    HomePageMapper homePageMapper;
    @Autowired
    FavoriteMapper favoriteMapper;
    @Autowired
    CommentSettingService commentSettingService;
    @Autowired
    ILargeFilesService largeFilesService;
    @Autowired
    private SoundUploadFileService uploadFileService;
    @Value("#{'${sound.prompt}'.split(',')}")
    private List<String> soundPrompt;

    @Override
    @DS("starrocks1")
    public WorkNoVo getSoundByWorkOrderNo(String workOrderNo) {
        return this.baseMapper.getSoundByWorkOrderNo(workOrderNo);
    }

    @Override
    public void saveSoundListening(SoundListeningModel model) {
        this.checkParam(model);
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getListeningRange()), "可听范围不能为空");
        final String soundName = model.getSoundName();
        QueryWrapper<SoundListeningEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SoundListeningEntity::getSoundName, soundName);
        queryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
        final List<SoundListeningEntity> names = this.getBaseMapper().selectList(queryWrapper);
        if(ObjectUtils.isNotEmpty(names)){
            final SoundListeningEntity soundListeningEntity = names.stream().findAny().get();
            Assert.isTrue(soundListeningEntity.getTaskId().equals(model.getTaskId()), "该名称已存在");
        }
        List<SoundListeningEntity> soundListeningEntities = new ArrayList<>();
        List<ListeningRangeModel> listeningRange = model.getListeningRange();
        UserVo user = (UserVo) SpringContextUtils.getUser();
        final String taskId = IdWorker.getIdStr();
        listeningRange.stream().forEach(e -> {
            SoundListeningEntity soundListening = convertService.soundListeningModelConvertToEntity(model);
            soundListening.setListeningRange(e.getId());
            soundListening.setLevel(e.getLevel());
            soundListening.setIsLeaf(ObjectUtils.isNotEmpty(e.getIsLeaf())?e.getIsLeaf():false);
            soundListening.setCreateTime(LocalDateTime.now());
            soundListening.setCreateUser(ObjectUtils.isNotEmpty(user) ? user.getUserName() : "");
            if (ObjectUtils.isEmpty(model.getTaskId())) {
                soundListening.setTaskId(taskId);
            } else {
                soundListening.setTaskId(model.getTaskId());
            }
            soundListeningEntities.add(soundListening);
        });
        boolean saveBatch = this.saveBatch(soundListeningEntities);
        if (saveBatch) {
            log.info("保存声音聆听成功");
        } else {
            throw new BootException("保存声音聆听失败");
        }
    }

    @Override
    public void updateSoundListening(SoundListeningModel model) {
        this.checkParam(model);
        Assert.hasLength(model.getTaskId(), "声音id不能为空");
        final UserVo user = (UserVo) SpringContextUtils.getUser();
        QueryWrapper<SoundListeningEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SoundListeningEntity::getSoundName, model.getSoundName());
        queryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
        final List<SoundListeningEntity> soundListeningEntity = this.getBaseMapper().selectList(queryWrapper);
        if(ObjectUtils.isNotEmpty(soundListeningEntity)){
            final SoundListeningEntity soundListeningEntity1 = soundListeningEntity.stream().findFirst().get();
            Assert.isTrue(soundListeningEntity1.getTaskId().equals(model.getTaskId()), "该名称已存在");
        }

        if (ObjectUtils.isEmpty(model.getListeningRange())) {
            UpdateWrapper<SoundListeningEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(SoundListeningEntity::getTaskId, model.getTaskId());
            wrapper.lambda().set(SoundListeningEntity::getUpdateTime, LocalDateTime.now());
            wrapper.lambda().set(SoundListeningEntity::getUpdateUser, ObjectUtils.isNotEmpty(user) ? user.getUserName() : "");
            wrapper.lambda().set(SoundListeningEntity::getStatus, model.getStatus());
            boolean update = this.update(wrapper);
            if (update) {
                log.info("修改声音聆听成功");
            } else {
                throw new BootException("修改声音聆听失败");
            }
        } else {
            QueryWrapper<SoundListeningEntity> wrapper = new QueryWrapper();
            wrapper.lambda().eq(SoundListeningEntity::getTaskId, model.getTaskId());
            wrapper.lambda().eq(SoundListeningEntity::getDel, 0);
            final List<SoundListeningEntity> list = this.list(wrapper);
            final String taskId = list.stream().findAny().get().getTaskId();
            final List<String> collect = list.stream().map(e -> e.getListeningRange()).collect(Collectors.toList());
            final List<ListeningRangeModel> listeningRange = model.getListeningRange();
            final Map<String, ListeningRangeModel> collect4 = listeningRange.stream().collect(Collectors.toMap(ListeningRangeModel::getId, Function.identity(), (e1, e2) -> e1));
            List<String> collect2 = listeningRange.stream().map(e -> e.getId()).collect(Collectors.toList());
            //先取交集 对并集进行更新
            Collection<String> intersection = CollectionUtils.intersection(collect, collect2);
            List<SoundListeningEntity> collect1 = list.stream().filter(e -> intersection.contains(e.getListeningRange())).map(e -> {
                final String listeningRange1 = e.getListeningRange();
                BeanUtil.copyProperties(model, e);
                e.setUpdateTime(LocalDateTime.now());
                e.setUpdateUser(ObjectUtils.isNotEmpty(user) ? user.getUserName() : "");
                if(ObjectUtils.isNotEmpty(collect4)&&collect4.containsKey(listeningRange1)){
                    final ListeningRangeModel listeningRangeModel = collect4.get(listeningRange1);
                    e.setListeningRange(listeningRangeModel.getId());
                    e.setLevel(listeningRangeModel.getLevel());
                    e.setIsLeaf(ObjectUtils.isNotEmpty(listeningRangeModel.getIsLeaf())?listeningRangeModel.getIsLeaf():false);
                }
                return e;
            }).collect(Collectors.toList());
            if(ObjectUtils.isNotEmpty(collect1)){
                boolean updateBatch = this.updateBatchById(collect1);
                if (updateBatch) {
                    log.info("批量修改声音聆听成功");
                }else{
                    throw new BootException("批量修改声音聆听失败");
                }
            }

            //将交集从两个集合中剔除，对老数据进行删除，对新数据进行新增
            collect.removeAll(intersection);
            List<ListeningRangeModel> collect3 = listeningRange.stream().filter(item -> !intersection.contains(item.getId())).collect(Collectors.toList());
//            collect2.removeAll(intersection);
            //将老数据删除
            if (ObjectUtils.isNotEmpty(collect)) {
                UpdateWrapper<SoundListeningEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(SoundListeningEntity::getTaskId, model.getTaskId());
                updateWrapper.lambda().in(SoundListeningEntity::getListeningRange, collect);
                updateWrapper.lambda().set(SoundListeningEntity::getDel, 1);
                updateWrapper.lambda().set(SoundListeningEntity::getUpdateTime, LocalDateTime.now());
                updateWrapper.lambda().set(SoundListeningEntity::getUpdateUser, ObjectUtils.isNotEmpty(user) ? user.getUserName() : "");
                boolean delete = this.update(updateWrapper);
                if (delete) {
                    log.info("删除声音聆听成功");
                } else {
                    throw new BootException("删除声音聆听失败");
                }
            }
            //将新数据保存
            if (ObjectUtils.isNotEmpty(collect3)) {
                log.info("聆听任务变更,对新数据进行保存");
                SoundListeningModel soundListening = new SoundListeningModel();
                BeanUtil.copyProperties(model, soundListening);
                soundListening.setListeningRange(collect3);
                soundListening.setTaskId(taskId);
                log.debug("转换后:[soundListening:{}]", soundListening);
                this.saveSoundListening(soundListening);
            }
        }
    }

    @Override
    public void deleteSoundListening(String taskId) {
        Assert.hasLength(taskId, "任务id不能为空");
        UpdateWrapper<SoundListeningEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SoundListeningEntity::getTaskId, taskId);
        updateWrapper.lambda().set(SoundListeningEntity::getDel, 1);
        boolean delete = this.update(updateWrapper);
        if (delete) {
            log.info("删除声音聆听成功");
        } else {
            throw new BootException("删除声音聆听失败");
        }
    }

    @Override
    public SoundListeningVo getSoundListeningById(SoundListeningModel model) {
        Assert.hasLength(model.getTaskId(), "声音id不能为空");
        QueryWrapper<SoundListeningEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SoundListeningEntity::getTaskId, model.getTaskId());
        queryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
        final List<SoundListeningEntity> list = this.list(queryWrapper);
        if (ObjectUtils.isEmpty(list)) {
            log.info("未查询到声音信息");
            return null;
        }

        SoundListeningEntity soundListening = list.stream().findAny().get();
//        final List<String> collect = list.stream().map(e -> e.getListeningRange()).collect(Collectors.toList());
//        String join = StrUtil.join(",", collect);
        SoundListeningVo soundListeningVo = convertService.soundListeningEntityConvertToVo(soundListening);
//        soundListeningVo.setListeningRange(join);
        soundListeningVo.setCreateTime(soundListening.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        soundListeningVo.setValidStartTime(soundListening.getValidStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        soundListeningVo.setValidEndTime(soundListening.getValidEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return soundListeningVo;
    }

    @Override
    public IPage<SoundListeningVo> getSoundListeningList(SoundListeningModel model) {
        IPage<SoundListeningEntity> page = new Page<>(model.getPageNum(), model.getPageSize());
        IPage<SoundListeningEntity> soundListeningList = this.baseMapper.getSoundListeningList(page, model);
        IPage<SoundListeningVo> pages = new Page<>();
        if (ObjectUtils.isEmpty(soundListeningList.getRecords())) {
            log.info("暂无声音信息");
            return pages;
        }
        pages.setCurrent(soundListeningList.getCurrent());
        pages.setPages(soundListeningList.getPages());
        pages.setSize(soundListeningList.getSize());
        pages.setTotal(soundListeningList.getTotal());
        if (ObjectUtils.isNotEmpty(soundListeningList.getRecords())) {
            final List<SoundListeningEntity> records = soundListeningList.getRecords();
            //品牌集合
            final Set<String> brandList = records.stream().map(e -> e.getBrand()).collect(Collectors.toSet());
            //车系集合
            final Set<String> carSeriesList = records.stream().map(e -> e.getCarSeries()).collect(Collectors.toSet());
            List<String> list = new ArrayList<>(brandList);
            list.addAll(carSeriesList);
            final List<BrandVo> brandVos = homePageMapper.getBrandCarSeriesByCodes(list);
            Map<String, String> map = brandVos.stream().collect(Collectors.toMap(BrandVo::getLabel, BrandVo::getValue));
            List<SoundListeningVo> soundListeningVos = records.stream().map(e -> {
                final SoundListeningVo soundListeningVo = convertService.soundListeningEntityConvertToVo(e);
                soundListeningVo.setCreateTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                soundListeningVo.setValidStartTime(e.getValidStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                soundListeningVo.setValidEndTime(e.getValidEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                soundListeningVo.setId(e.getTaskId());
                soundListeningVo.setTaskId(e.getTaskId());
                final List<ListeningRangeModel> soundListeningRange = this.baseMapper.getSoundListeningRange(e.getTaskId());
                soundListeningVo.setListeningRanges(soundListeningRange);
                final String label = e.getFourLabel();
                final List<LabelVo> labelVos = homePageMapper.UpwardFindBusinessLabelHierarchical(label);
                final List<LabelVo> labelVos1 = homePageMapper.UpwardFindQyLabelHierarchical(label);
                StringBuffer stringBuffer = new StringBuffer();
                StringBuffer labelCode = new StringBuffer();
                if (ObjectUtils.isNotEmpty(labelVos)) {
                    labelVos.stream().forEach(k -> {
                        stringBuffer.append(k.getName() + "|");
                        labelCode.append(k.getCode() + "|");
                    });
                } else if (ObjectUtils.isNotEmpty(labelVos1)) {
                    labelVos1.stream().forEach(k -> {
                        stringBuffer.append(k.getName() + "|");
                        labelCode.append(k.getCode() + "|");
                    });
                }
                if (ObjectUtils.isNotEmpty(map) && map.containsKey(e.getBrand())) {
                    String s = map.get(e.getBrand());
                    soundListeningVo.setBrandName(ObjectUtils.isNotEmpty(s) ? s : "");
                }
                if (ObjectUtils.isNotEmpty(map) && map.containsKey(e.getCarSeries())) {
                    String s = map.get(e.getCarSeries());
                    soundListeningVo.setCarSeriesName(ObjectUtils.isNotEmpty(s) ? s : "");
                }
                soundListeningVo.setLabelName(ObjectUtils.isNotEmpty(stringBuffer) ? stringBuffer.substring(0, stringBuffer.length() - 1) : "");
                soundListeningVo.setLabel(ObjectUtils.isNotEmpty(labelCode) ? labelCode.substring(0, labelCode.length() - 1) : "");
                soundListeningVo.setId(e.getTaskId());
                soundListeningVo.setSoundDuration(ObjectUtils.isNotEmpty(e.getSoundDuration()) ? e.getSoundDuration() : 0L);
                soundListeningVo.setPrompt(soundPrompt);
//                List<String> soundListeningRange = this.getBaseMapper().getSoundListeningRange(e.getTaskId());
//                soundListeningVo.setListeningRanges(StrUtil.join(",", soundListeningRange));
//                final String listeningRanges = e.getListeningRanges();
//                if(ObjectUtils.isNotEmpty(listeningRanges)){
//                    String collect = Pattern.compile(",")
//                            .splitAsStream(listeningRanges)       // 惰性分割，避免内存占用
//                            .map(String::trim)
//                            .filter(s -> !s.isEmpty())
//                            .distinct()
//                            .collect(Collectors.joining(","));
//                    soundListeningVo.setListeningRanges(collect);
//                }
                return soundListeningVo;
            }).collect(Collectors.toList());
            pages.setRecords(soundListeningVos);
        }
        return pages;
    }

    @Override
    public ListenTaskStatisticsVo getSoundListenDataStatistics(SoundListeningModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
        return this.baseMapper.getSoundListenDataStatistics(model);
    }

    @Override
    public IPage<ListenTaskDataVo> getSoundListenDataList(SoundListeningModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
        IPage<SoundListeningEntity> page = new Page<>(model.getPageNum(), model.getPageSize());
        IPage<SoundListeningEntity> soundListenDataList = this.baseMapper.getSoundListenDataList(page, model);
        IPage<ListenTaskDataVo> pages = new Page<>();
        if (ObjectUtils.isEmpty(soundListenDataList.getRecords())) {
            log.info("暂无声音信息");
            return pages;
        }
        pages.setCurrent(soundListenDataList.getCurrent());
        pages.setPages(soundListenDataList.getPages());
        pages.setSize(soundListenDataList.getSize());
        pages.setTotal(soundListenDataList.getTotal());
        final List<SoundListeningEntity> records = soundListenDataList.getRecords();
        log.debug("转换前:[records:{}]", records);
        QueryWrapper<FavoriteEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(FavoriteEntity::getTaskId, model.getTaskId());
        wrapper.lambda().eq(FavoriteEntity::getDel, 0);
        final List<FavoriteEntity> favoriteEntities = favoriteMapper.selectList(wrapper);
        final List<String> collect = favoriteEntities.stream().map(e -> e.getUserId()).collect(Collectors.toList());
        List<ListenTaskDataVo> soundListeningVos = records.stream().map(e -> {
            final ListenTaskDataVo soundListeningVo = convertService.soundListeningEntityConvertToListenTaskDataVo(e);
            soundListeningVo.setJoinTime(ObjectUtils.isNotEmpty(e.getJoinTime()) ? e.getJoinTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
            if (ObjectUtils.isNotEmpty(collect) && collect.contains(e.getUserId())) {
                soundListeningVo.setIsFavorite("是");
            } else {
                soundListeningVo.setIsFavorite("否");
            }
            return soundListeningVo;
        }).collect(Collectors.toList());
        pages.setRecords(soundListeningVos);
        log.debug("转换后:[soundListeningVos:{}]", soundListeningVos);
        return pages;
    }


    @Override
    public String exportSoundListenDataList(SoundListeningModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
//        SpringContextUtils.getExecutor().execute(() -> {
//            try {
//                this.asyncDownloadData(model);
//            } catch (Exception e) {
//                log.error("异步导出数据失败", e);
//            }
//        });
        try {
            final String taskId = IdWorker.getIdStr();
            LocalDateTime localDateTime = LocalDateTime.now();
//            String fileName = URLEncoder.encode("声音列表".concat("-").concat(localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))), "UTF-8").replaceAll("\\+", "%20");
            String fileName = "声音列表".concat(localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            this.asyncDownloadData(model, fileName, taskId);
            String objectUrl = uploadFileService.getObjectUrl(fileName.concat(".xlsx"));
            return objectUrl;
        } catch (Exception e) {
            log.error("同步导出声音数据失败", e);
        }
        return null;
    }

    @SneakyThrows
    public void asyncDownloadData(SoundListeningModel model, String fileName, String taskId) {
        final Integer soundListenDataCount = this.baseMapper.exportSoundListenDataSize(model);
        log.info("声音数据->待导出的结果总数据量:{}", soundListenDataCount);
        largeFilesService.exportSoundList(
                fileName,
                taskId,
                soundListenDataCount,
                page -> {
                    SoundListeningModel cloneModel = SoundListeningModel.builder().build();
                    BeanUtil.copyProperties(model, cloneModel);
                    IPage<SoundListeningEntity> pages = new Page<>(page.getCurrent(), page.getSize());
                    IPage<SoundListeningEntity> soundListenDataList = this.baseMapper.getSoundListenDataList(pages, cloneModel);

                    if (ObjectUtils.isEmpty(soundListenDataList.getRecords())) {
                        log.info("暂无声音信息");
                        return null;
                    }
                    final List<SoundListeningEntity> records = soundListenDataList.getRecords();
                    QueryWrapper<FavoriteEntity> wrapper = new QueryWrapper<>();
                    wrapper.lambda().eq(FavoriteEntity::getTaskId, cloneModel.getTaskId());
                    wrapper.lambda().eq(FavoriteEntity::getDel, 0);
                    final List<FavoriteEntity> favoriteEntities = favoriteMapper.selectList(wrapper);
                    final List<String> collect = favoriteEntities.stream().map(e -> e.getUserId()).collect(Collectors.toList());
                    List<ListenTaskDataVo> soundListeningVos = records.stream().map(e -> {
                        final ListenTaskDataVo soundListeningVo = convertService.soundListeningEntityConvertToListenTaskDataVo(e);
                        soundListeningVo.setJoinTime(ObjectUtils.isNotEmpty(e.getJoinTime()) ? e.getJoinTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                        if (ObjectUtils.isNotEmpty(collect) && collect.contains(e.getUserId())) {
                            soundListeningVo.setIsFavorite("是");
                        } else {
                            soundListeningVo.setIsFavorite("否");
                        }
                        return soundListeningVo;
                    }).collect(Collectors.toList());
                    log.info("查询数据>>>>{}", soundListeningVos.size());
                    return soundListeningVos;
                }, ListenTaskDataVo.class);
    }


    @Override
    public IPage<CommentVo> getCommentList(CommentModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
        IPage<CommentEntity> page = new Page<>(model.getPageNum(), model.getPageSize());
        IPage<CommentEntity> commentListByTaskId = commentMapper.getCommentListByTaskId(page, model);
        IPage<CommentVo> pages = new Page<>();
        if (ObjectUtils.isEmpty(commentListByTaskId.getRecords())) {
            log.info("暂无评论信息");
            return pages;
        }
        final List<CommentEntity> records = commentListByTaskId.getRecords();
        final List<CommentSettingVo> commentTypeList = commentSettingService.getCommentTypeList();
        final Map<String, String> collect = commentTypeList.stream().collect(Collectors.toMap(CommentSettingVo::getId, CommentSettingVo::getName));
        log.debug("转换前:[records:{}]", records);
        List<CommentVo> soundListeningVos = records.stream().map(e -> {
            final CommentVo commentVo = convertService.commentEntityConvertToCommentVo(e);
            if (e.getIsReply().equals("0")) {
                commentVo.setIsReply(Boolean.FALSE);
            } else {
                commentVo.setIsReply(Boolean.TRUE);
            }
            if(ObjectUtils.isNotEmpty(commentVo.getCommentType())&&ObjectUtils.isNotEmpty(collect)){
                final String commentType = commentVo.getCommentType();
                String[] split = commentType.split(",");
                StringBuffer commentName= new StringBuffer();
                for (String s : split) {
                    if(collect.containsKey(s)){
                        String s1 = collect.get(s);
                        commentName.append(s1);
                        commentName.append(",");
                    }
                }
                commentVo.setCommentTypeName(ObjectUtils.isNotEmpty(commentName)?commentName.substring(0,commentName.length()-1):"");
            }
            commentVo.setCommentTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            commentVo.setReplyTime(ObjectUtils.isNotEmpty(e.getReplyTime()) ? e.getReplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
            commentVo.setReplyContent(ObjectUtils.isNotEmpty(e.getReply()) ? e.getReply() : "");
            return commentVo;
        }).collect(Collectors.toList());
        pages.setRecords(soundListeningVos);
        log.debug("转换后:[soundListeningVos:{}]", soundListeningVos);
        return pages;
    }

    public static void main(String[] args) {
        String s = "1,2,3";
        StrUtil.replace(s, "1", "5");
        System.out.println(s);
    }

    @Override
    public String exportCommentList(CommentModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
//        SpringContextUtils.getExecutor().execute(() -> {
//            try {
//                this.asyncDownloadCommentData(model);
//            } catch (Exception e) {
//                log.error("异步导出数据失败", e);
//            }
//        });

        try {
            final String taskId = IdWorker.getIdStr();
            LocalDateTime localDateTime = LocalDateTime.now();
            String fileName = "声音列表".concat(localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            this.asyncDownloadCommentData(model, fileName, taskId);
            String objectUrl = uploadFileService.getObjectUrl(fileName.concat(".xlsx"));
            return objectUrl;
        } catch (Exception e) {
            log.error("同步导出评论数据失败", e);
        }
        return null;
    }


    @SneakyThrows
    public void asyncDownloadCommentData(CommentModel model, String fileName, String taskId) {
        final Integer commentListCount = commentMapper.getCommentListCountByTaskId(model);
        log.info("声音数据->待导出的结果总数据量:{}", commentListCount);
        log.info("结果总数据量:{}", fileName);
        largeFilesService.exportSoundList(
                fileName,
                taskId,
                commentListCount,
                page -> {
                    SoundListeningModel cloneModel = SoundListeningModel.builder().build();
                    BeanUtil.copyProperties(model, cloneModel);
                    IPage<CommentEntity> pages = new Page<>(page.getCurrent(), page.getSize());
                    IPage<CommentEntity> commentListByTaskId = commentMapper.getCommentListByTaskId(pages, model);
                    if (ObjectUtils.isEmpty(commentListByTaskId.getRecords())) {
                        log.info("暂无评论信息");
                        return null;
                    }
                    final List<CommentEntity> records = commentListByTaskId.getRecords();
                    List<ExcelCommentVo> soundListeningVos = records.stream().map(e -> {
                        final ExcelCommentVo commentVo = convertService.commentEntityConvertToEExcelCommentVo(e);
                        commentVo.setCommentTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        commentVo.setReplyTime(ObjectUtils.isNotEmpty(e.getReplyTime()) ? e.getReplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                        commentVo.setIsReply(Boolean.FALSE.toString().equals("false") ? "否" : "是");
                        return commentVo;
                    }).collect(Collectors.toList());
                    log.info("查询数据>>>>{}", soundListeningVos.size());
                    return soundListeningVos;
                }, ExcelCommentVo.class);
    }

    @Override
    public void replyComment(CommentModel model) {
        Assert.hasLength(model.getReplyContent(), "回复内容不能为空");
        Assert.hasLength(model.getId(), "评论id不能为空");
        final String userId = SpringContextUtils.getUserId();
        QueryWrapper<CommentEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CommentEntity::getId, model.getId());
        final CommentEntity commentEntity = commentMapper.selectOne(wrapper);
        if (ObjectUtils.isEmpty(commentEntity)) {
            throw new BootException("评论不存在");
        }
        if (ObjectUtils.isNotEmpty(commentEntity.getReply())) {
            throw new BootException("该评论已回复");
        }
        if (commentEntity.getCommentUser().equals(userId)) {
            throw new BootException("不能回复自己的评论");
        }
        commentEntity.setReplyTime(LocalDateTime.now());
        commentEntity.setReplyUser(userId);
        commentEntity.setReply(model.getReplyContent());
        int update = commentMapper.update(commentEntity, wrapper);
        if (update > 0) {
            log.info("回复成功");
        } else {
            throw new BootException("回复失败");
        }
    }

    @Override
    public List<CommentSettingVo> getCommentTypeList() {
        return commentSettingService.getCommentTypeList();
    }

    private void checkParam(SoundListeningModel model) {
        Assert.hasLength(model.getSoundName(), "声音名称不能为空");
        Assert.hasLength(model.getCarSeries(), "车系不能为空");
        Assert.hasLength(model.getOriginalSound(), "原始声音不能为空");
        Assert.hasLength(model.getFirstLabel(), "一级标签不能为空");
        Assert.hasLength(model.getSecondLabel(), "二级标签不能为空");
        Assert.hasLength(model.getThreeLabel(), "三级标签不能为空");
        Assert.hasLength(model.getFourLabel(), "四级标签不能为空");
        Assert.hasLength(model.getWorkOrderNo(), "工单号不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getValidStartTime()), "有效期开始时间不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getValidEndTime()), "有效期结束时间不能为空");
        Assert.isTrue(model.getValidStartTime().isBefore(model.getValidEndTime()), "有效期开始时间不能大于有效期结束时间");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getRewardIntegral()), "奖励积分不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getIsMandatory()), "是否必听不能为空");
        Assert.hasLength(model.getBrand(), "品牌不能为空");
    }
}
