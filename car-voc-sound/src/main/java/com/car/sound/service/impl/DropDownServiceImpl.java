package com.car.sound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.BrandCarSeriesEntity;
import com.car.sound.entity.SoundListeningEntity;
import com.car.sound.mapper.BrandCarSeriesMapper;
import com.car.sound.mapper.HomePageMapper;
import com.car.sound.mapper.SoundListeningMapper;
import com.car.sound.service.DropDownService;
import com.car.sound.vo.BrandVo;
import com.car.sound.vo.CarSeriesVo;
import com.car.sound.vo.LabelVo;
import com.car.sound.vo.UserVo;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/16 12:11
 * @描述:
 **/
@Service
@Slf4j
public class DropDownServiceImpl extends ServiceImpl<BrandCarSeriesMapper, BrandCarSeriesEntity> implements DropDownService {

    @Autowired
    SoundListeningMapper soundListeningMapper;
    @Autowired
    HomePageMapper homePageMapper;

    @Override
    public List<BrandVo> getBrandCarSeriesTree() {
        QueryWrapper<BrandCarSeriesEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BrandCarSeriesEntity::getDelFlag, 0);
        queryWrapper.lambda().eq(BrandCarSeriesEntity::getEnable, 1);
        final List<BrandCarSeriesEntity> list = this.list(queryWrapper);
        if(ObjectUtils.isEmpty(list)){
            log.info("未查询到品牌车系信息");
            return Collections.emptyList();
        }
        UserVo user = (UserVo)SpringContextUtils.getUser();
        QueryWrapper<SoundListeningEntity> soundListeningEntityQueryWrapper = new QueryWrapper<>();
        soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
        soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getStatus, 1);
        if(ObjectUtils.isNotEmpty(user)){
            soundListeningEntityQueryWrapper.lambda().like(SoundListeningEntity::getListeningRange, user.getDepartId());
        }
        final List<SoundListeningEntity> soundListeningEntities = soundListeningMapper.selectList(soundListeningEntityQueryWrapper);
        //根据品牌分组
        Map<String, List<SoundListeningEntity>> soundListeningBrandMap = soundListeningEntities.stream().collect(Collectors.groupingBy(SoundListeningEntity::getBrand));
        //根据车系分组
        Map<String, List<SoundListeningEntity>> soundListeningCarSeriesMap = soundListeningEntities.stream().collect(Collectors.groupingBy(SoundListeningEntity::getCarSeries));
        //获取品牌
        final List<BrandCarSeriesEntity>  brandList = list.stream().filter(e -> "0".equals(e.getPId())).collect(Collectors.toList());
        //获取车系并按照品牌分组
        final Map<String, List<BrandCarSeriesEntity>> carSeriesMap = list.stream().filter(e -> !"0".equals(e.getPId())).collect(Collectors.groupingBy(BrandCarSeriesEntity::getPId));
        List<BrandVo> collect = brandList.stream().map(e -> {
            BrandVo build = BrandVo.builder().id(e.getId()).label(e.getName()).value(e.getCode()).build();
            if(ObjectUtils.isNotEmpty(soundListeningBrandMap)&&soundListeningBrandMap.containsKey(e.getCode())){
                final List<SoundListeningEntity> soundListeningEntities1 = soundListeningBrandMap.get(e.getCode());
                build.setTaskCount(soundListeningEntities1.size());
            }else{
                build.setTaskCount(0);
            }
            //车系
            if(ObjectUtils.isNotEmpty(carSeriesMap)&&carSeriesMap.containsKey(e.getId())){
                final List<BrandCarSeriesEntity> brandCarSeriesEntities = carSeriesMap.get(e.getId());
                List<CarSeriesVo> carSeriesVos = brandCarSeriesEntities.stream().map(k->{
                    CarSeriesVo carSeriesVo = CarSeriesVo.builder().id(k.getId()).label(k.getName()).value(k.getCode()).build();
                    if(ObjectUtils.isNotEmpty(soundListeningCarSeriesMap)&&soundListeningCarSeriesMap.containsKey(k.getCode())){
                        final List<SoundListeningEntity> soundListeningEntities1 = soundListeningCarSeriesMap.get(k.getCode());
                        carSeriesVo.setTaskCount(soundListeningEntities1.size());
                    }else{
                        carSeriesVo.setTaskCount(0);
                    }
                    return carSeriesVo;
                }).filter(k->k.getTaskCount()>0).collect(Collectors.toList());
                build.setCarSeries(carSeriesVos);
            }
            return build;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<LabelVo> getLabelTree() {
        final List<LabelVo> labelVo = homePageMapper.getLabelVo();
        if(ObjectUtils.isEmpty(labelVo)){
            log.info("未查询到标签信息");
            return Collections.emptyList();
        }
        //获取一级标签
        List<LabelVo> topTagLibList = labelVo.stream().filter(e -> "0".equals(e.getPid())).collect(Collectors.toList());
        //根据一级标签分组
        Map<String, List<LabelVo>> labelMap = labelVo.stream().filter(e -> !"0".equals(e.getPid())).collect(Collectors.groupingBy(LabelVo::getPid));
        this.tagLibTree(topTagLibList, labelMap);
        return topTagLibList;
    }

    @Override
    public List<BrandVo> getBrandCarSeriesTrees() {
        QueryWrapper<BrandCarSeriesEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BrandCarSeriesEntity::getDelFlag, 0);
        queryWrapper.lambda().eq(BrandCarSeriesEntity::getEnable, 1);
        final List<BrandCarSeriesEntity> list = this.list(queryWrapper);
        if(ObjectUtils.isEmpty(list)){
            log.info("未查询到品牌车系信息");
            return Collections.emptyList();
        }
        UserVo user = (UserVo)SpringContextUtils.getUser();
        QueryWrapper<SoundListeningEntity> soundListeningEntityQueryWrapper = new QueryWrapper<>();
        soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
        soundListeningEntityQueryWrapper.lambda().eq(SoundListeningEntity::getStatus, 1);
        if(ObjectUtils.isNotEmpty(user)){
            soundListeningEntityQueryWrapper.lambda().like(SoundListeningEntity::getListeningRange, user.getDepartId());
        }
        final List<SoundListeningEntity> soundListeningEntities = soundListeningMapper.selectList(soundListeningEntityQueryWrapper);
        //根据品牌分组
        Map<String, List<SoundListeningEntity>> soundListeningBrandMap = soundListeningEntities.stream().collect(Collectors.groupingBy(SoundListeningEntity::getBrand));
        //根据车系分组
        Map<String, List<SoundListeningEntity>> soundListeningCarSeriesMap = soundListeningEntities.stream().collect(Collectors.groupingBy(SoundListeningEntity::getCarSeries));
        //获取品牌
        final List<BrandCarSeriesEntity>  brandList = list.stream().filter(e -> "0".equals(e.getPId())).collect(Collectors.toList());
        //获取车系并按照品牌分组
        final Map<String, List<BrandCarSeriesEntity>> carSeriesMap = list.stream().filter(e -> !"0".equals(e.getPId())).collect(Collectors.groupingBy(BrandCarSeriesEntity::getPId));
        List<BrandVo> collect = brandList.stream().map(e -> {
            BrandVo build = BrandVo.builder().id(e.getId()).label(e.getName()).value(e.getCode()).build();
            if(ObjectUtils.isNotEmpty(soundListeningBrandMap)&&soundListeningBrandMap.containsKey(e.getCode())){
                final List<SoundListeningEntity> soundListeningEntities1 = soundListeningBrandMap.get(e.getCode());
                build.setTaskCount(soundListeningEntities1.size());
            }else{
                build.setTaskCount(0);
            }
            //车系
            if(ObjectUtils.isNotEmpty(carSeriesMap)&&carSeriesMap.containsKey(e.getId())){
                final List<BrandCarSeriesEntity> brandCarSeriesEntities = carSeriesMap.get(e.getId());
                List<CarSeriesVo> carSeriesVos = brandCarSeriesEntities.stream().map(k->{
                    CarSeriesVo carSeriesVo = CarSeriesVo.builder().id(k.getId()).label(k.getName()).value(k.getCode()).build();
                    if(ObjectUtils.isNotEmpty(soundListeningCarSeriesMap)&&soundListeningCarSeriesMap.containsKey(k.getCode())){
                        final List<SoundListeningEntity> soundListeningEntities1 = soundListeningCarSeriesMap.get(k.getCode());
                        carSeriesVo.setTaskCount(soundListeningEntities1.size());
                    }else{
                        carSeriesVo.setTaskCount(0);
                    }
                    return carSeriesVo;
                }).collect(Collectors.toList());
                build.setCarSeries(carSeriesVos);
            }
            return build;
        }).collect(Collectors.toList());
        return collect;
    }


    void tagLibTree(List<LabelVo> topTagLibList, Map<String, List<LabelVo>> labelMap) {
        if (ObjectUtils.isEmpty(topTagLibList)) {
            return;
        }
        for (LabelVo labelVo : topTagLibList) {
            List<LabelVo> tagLibCategoryVos = labelMap.get(labelVo.getId());
            this.tagLibTree(tagLibCategoryVos, labelMap);
            labelVo.setChild(tagLibCategoryVos);
        }
    }
}
