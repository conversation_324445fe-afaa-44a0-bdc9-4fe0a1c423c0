package com.car.sound.service;

import com.car.sound.vo.UserVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 11:42
 * @描述:
 **/
public interface UserService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/9 11:48
     * @描述  根据用户id查询用户信息
     * @param userId
     * @return com.car.sound.vo.UserVo
     **/
    UserVo getUserInfoByUserId(String userId);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/9 11:48
     * @描述  根据用户id查询用户是否存在
     * @param userId
     * @return java.lang.Boolean
     **/
    boolean checkUserByUserId(String userId);

    List<UserVo> getUserList();
}
