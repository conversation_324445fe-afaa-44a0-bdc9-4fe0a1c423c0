package com.car.sound.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.sound.entity.*;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.*;
import com.car.sound.model.CommentModel;
import com.car.sound.model.ListenTaskModel;
import com.car.sound.model.SoundListeningModel;
import com.car.sound.service.CommentSettingService;
import com.car.sound.service.ListenTaskService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.vo.*;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/13 14:18
 * @描述:
 **/
@Service
@Slf4j
public class ListenTaskServiceImpl implements ListenTaskService {
    @Autowired
    SoundListeningMapper soundListeningMapper;
    @Autowired
    UserDepartMapper userDepartMapper;
    @Autowired
    SoundConvertService convertService;
    @Autowired
    FavoriteMapper favoriteMapper;
    @Autowired
    CommentMapper commentMapper;
    @Autowired
    ListRecordMapper listRecordMapper;
    @Autowired
    IntegralMapper integralMapper;
    @Autowired
    CommentSettingService commentSettingService;
    @Value("${listen.task.imageUrl:null}")
    String imageUrl;
    @Autowired
    UpvoteMapper upvoteMapper;
    @Autowired
    CommentSettingMapper commentSettingMapper;
    @Autowired
    UserMapper userMapper;

    @Value("#{'${sound.prompt}'.split(',')}")
    private List<String> soundPrompt;

    @Override
    public IPage<ListenTaskVo> getListenTaskList(ListenTaskModel model) {
        IPage<SoundListeningEntity> page = new Page<>(model.getPageNum(), model.getPageSize());
        final UserVo user = (UserVo) SpringContextUtils.getUser();
        if (ObjectUtils.isNotEmpty(user)) {
            model.setDepartId(user.getDepartId());
            model.setUserId(user.getUserId());
        }
        model.setStartTime(LocalDateTime.now());
        IPage<SoundListeningEntity> listenTaskList = soundListeningMapper.getListenTaskList(page, model);
        IPage<ListenTaskVo> pages = new Page<>();
        pages.setCurrent(listenTaskList.getCurrent());
        pages.setPages(listenTaskList.getPages());
        pages.setSize(listenTaskList.getSize());
        pages.setTotal(listenTaskList.getTotal());
        final List<CommentSettingVo> allCommentSettingList = commentSettingService.getAllCommentSettingList();
        if (ObjectUtils.isEmpty(listenTaskList.getRecords())) {
            log.info("暂无任务信息");
        } else {
            final List<SoundListeningEntity> records = listenTaskList.getRecords();
            log.debug("转换前:[records:{}]", records);
            List<String> codeList = records.stream().map(SoundListeningEntity::getWorkOrderNo).collect(Collectors.toList());
            List<String> taskIds = records.stream().map(e -> e.getTaskId()).collect(Collectors.toList());
            model.setTaskIdList(taskIds);
            final List<SoundListeningEntity> listenTaskInfoData = soundListeningMapper.getListenTaskInfoData(model);
            final Map<String, SoundListeningEntity> collect = listenTaskInfoData.stream().collect(Collectors.toMap(SoundListeningEntity::getTaskId, e -> e, (k1, k2) -> k1));
            Map<String, String> soundByWorkOrderIdMap = getSoundByWorkOrderId(codeList);
            QueryWrapper<FavoriteEntity> favoriteWrapper = new QueryWrapper<>();
            favoriteWrapper.lambda().in(FavoriteEntity::getTaskId, taskIds);
            favoriteWrapper.lambda().eq(FavoriteEntity::getUserId, user.getUserId());
            favoriteWrapper.lambda().eq(FavoriteEntity::getDel, 0);
            List<FavoriteEntity> favoriteEntities = favoriteMapper.selectList(favoriteWrapper);
            Map<String, FavoriteEntity> collect1 = favoriteEntities.stream().collect(Collectors.toMap(FavoriteEntity::getTaskId, e -> e, (k1, k2) -> k1));
            QueryWrapper<CommentEntity> commentWrapper = new QueryWrapper<>();
            commentWrapper.lambda().in(CommentEntity::getTaskId, taskIds);
            commentWrapper.lambda().eq(CommentEntity::getCommentUser, user.getUserId());
            List<CommentEntity> commentEntities = commentMapper.selectList(commentWrapper);
            final List<String> collect2 = commentEntities.stream().map(e -> e.getTaskId()).collect(Collectors.toList());
            List<ListenTaskVo> soundListeningVos = records.stream().map(e -> {
                final ListenTaskVo soundListeningVo = convertService.soundListeningEntityConvertToListenTaskVo(e);
//                soundListeningVo.setIsMandatory("1".equals(e.getIsMandatory())?true:false);
                soundListeningVo.setEndTime(ObjectUtils.isNotEmpty(e.getValidEndTime()) ? e.getValidEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "");
                //聆听时长
                Long listenDuration = 0L;
                //声音时长
                final Long soundDuration = e.getSoundDuration();
                if(ObjectUtils.isNotEmpty(collect)&&collect.containsKey(e.getTaskId())){
                    SoundListeningEntity soundListeningEntity = collect.get(e.getTaskId());
                    listenDuration = soundListeningEntity.getListenDuration();
                    soundListeningVo.setStatus(soundListeningEntity.getStatus());
                }
                if (listenDuration.longValue() == 0) {
                    soundListeningVo.setListenProgress(BigDecimal.ZERO);
                } else {
                    BigDecimal bigDecimal = new BigDecimal(listenDuration).divide(new BigDecimal(soundDuration), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    soundListeningVo.setListenProgress(bigDecimal.compareTo(new BigDecimal(100))>0?new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP):bigDecimal);
                }
                soundListeningVo.setCommentSetting(allCommentSettingList);
                soundListeningVo.setUrl(imageUrl);
                soundListeningVo.setPrompt(soundPrompt);
                if (soundByWorkOrderIdMap.containsKey(e.getWorkOrderNo())) {
                    soundListeningVo.setWorkOrderId(soundByWorkOrderIdMap.get(e.getWorkOrderNo()));
                }
                if (ObjectUtils.isEmpty(soundListeningVo.getStatus())) {
                    soundListeningVo.setStatus("0");
                }
                if(ObjectUtils.isNotEmpty(collect1)&&collect1.containsKey(e.getTaskId())){
                    soundListeningVo.setIsFavorite(true);
                }else{
                    soundListeningVo.setIsFavorite(false);
                }
               if(ObjectUtils.isNotEmpty(collect2)&&collect2.contains(e.getTaskId())){
                   soundListeningVo.setIsComment(true);
               }else{
                   soundListeningVo.setIsComment(false);
               }
                return soundListeningVo;
            }).collect(Collectors.toList());
            pages.setRecords(soundListeningVos);
            log.debug("转换后:[soundListeningVos:{}]", soundListeningVos);
        }
        return pages;
    }

    @Override
    public Map<String, String> getSoundByWorkOrderId(List<String> workOrderNo) {
        List<WorkNoVo> soundByWorkOrderId = soundListeningMapper.getSoundByWorkOrderId(workOrderNo);
        if (!CollectionUtils.isEmpty(soundByWorkOrderId)) {
            Map<String, String> stringMap = soundByWorkOrderId.stream().collect(Collectors.toMap(WorkNoVo::getWorkOrderNo, WorkNoVo::getWorkOrderId));
            return stringMap;
        }
        return new HashMap<>();
    }

    @Override
    public IPage<CommentVo> getCommentList(ListenTaskModel model) {
        final String userId = SpringContextUtils.getUserId();
        IPage<CommentEntity> page = new Page<>(model.getPageNum(), model.getPageSize());
        IPage<CommentEntity> commentList = commentMapper.getCommentList(page, model);
        IPage<CommentVo> pages = new Page<>();
        pages.setCurrent(commentList.getCurrent());
        pages.setPages(commentList.getPages());
        pages.setSize(commentList.getSize());
        pages.setTotal(commentList.getTotal());
        if (ObjectUtils.isEmpty(commentList.getRecords())) {
            log.info("暂无评论信息");
        } else {
            final List<CommentEntity> records = commentList.getRecords();
            List<String> collect = records.stream().map(e -> e.getId()).collect(Collectors.toList());
            QueryWrapper<UpvoteUserEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(UpvoteUserEntity::getCommentId, collect);
            queryWrapper.lambda().eq(UpvoteUserEntity::getUserId, userId);
            final List<UpvoteUserEntity> upvoteUserList = upvoteMapper.selectList(queryWrapper);
            final Map<String, UpvoteUserEntity> collect1 = upvoteUserList.stream().collect(Collectors.toMap(UpvoteUserEntity::getCommentId, e -> e, (k1, k2) -> k1));
            log.debug("转换前:[records:{}]", records);
            List<CommentVo> soundListeningVos = records.stream().map(e -> {
                final CommentVo commentVo = convertService.commentEntityConvertToCommentVo(e);
                commentVo.setCommentTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                commentVo.setReplyTime(ObjectUtils.isNotEmpty(e.getReplyTime()) ? e.getReplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                commentVo.setReplyContent(ObjectUtils.isNotEmpty(e.getReply()) ? e.getReply() : "");
                if(ObjectUtils.isNotEmpty(collect1)&&collect1.containsKey(e.getId())){
                    commentVo.setIsUpvote(true);
                }else{
                    commentVo.setIsUpvote(false);
                }
                return commentVo;
            }).collect(Collectors.toList());
            pages.setRecords(soundListeningVos);
            log.debug("转换后:[soundListeningVos:{}]", soundListeningVos);
        }
        return pages;
    }

    @Override
    public void saveFavorite(ListenTaskModel model) {
        Assert.hasLength(model.getTaskId(), "声音id不能为空");
        final String userId = SpringContextUtils.getUserId();
        FavoriteEntity build = FavoriteEntity.builder().taskId(model.getTaskId()).userId(userId).createTime(LocalDateTime.now()).build();
        int save = favoriteMapper.insert(build);
        if (save > 0) {
            log.info("收藏成功");
        } else {
            throw new BootException("收藏失败");
        }
    }

    @Override
    public void cancelFavorite(ListenTaskModel model) {
        Assert.hasLength(model.getTaskId(), "声音id不能为空");
        final String userId = SpringContextUtils.getUserId();
        UpdateWrapper<FavoriteEntity> wrapper = new UpdateWrapper();
        wrapper.lambda().eq(FavoriteEntity::getTaskId, model.getTaskId()).eq(FavoriteEntity::getUserId, userId);
        wrapper.lambda().set(FavoriteEntity::getDel, 1);
        int update = favoriteMapper.update(null, wrapper);
        if (update > 0) {
            log.info("取消收藏成功");
        } else {
            throw new BootException("取消收藏失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUpvote(CommentModel model) {
        Assert.hasLength(model.getId(), "评论id不能为空");
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
        final String userId = SpringContextUtils.getUserId();
        QueryWrapper<CommentEntity> commentQuery = new QueryWrapper<>();
        commentQuery.lambda().eq(CommentEntity::getId, model.getId());
        final CommentEntity commentEntity = commentMapper.selectOne(commentQuery);
        if (ObjectUtils.isNotEmpty(commentEntity.getCommentUser()) && commentEntity.getCommentUser().equals(userId)) {
            throw new BootException("不能给自己的评论点赞");
        }

        UpvoteUserEntity build = UpvoteUserEntity.builder().userId(userId).commentId(model.getId()).build();
        int insert = upvoteMapper.insert(build);
        if (insert > 0) {
            log.info("点赞成功");
        } else {
            throw new BootException("点赞失败");
        }
        commentMapper.saveUpvote(model);
        String commentType = commentEntity.getCommentType();
        final String commentUser = commentEntity.getCommentUser();


        QueryWrapper<IntegralEntity> integralEntityQueryWrapper = new QueryWrapper<>();
        integralEntityQueryWrapper.lambda().eq(IntegralEntity::getUserId, commentUser);
        integralEntityQueryWrapper.lambda().eq(IntegralEntity::getTaskId, model.getTaskId());
        integralEntityQueryWrapper.lambda().eq(IntegralEntity::getCommentId, model.getId());
        final Integer integralEntity = integralMapper.selectCount(integralEntityQueryWrapper);
        if(ObjectUtils.isNotEmpty(integralEntity)&&integralEntity.intValue()>0){
            log.info("当前评论:[{}]的评论人:[{}]已奖励过积分，不再给予奖励",model.getId(),commentUser);
        }else {
            List<String> collect = Arrays.stream(commentType.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());
            QueryWrapper<CommentSettingEntity> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.lambda().in(CommentSettingEntity::getId, collect);
            queryWrapper1.lambda().eq(CommentSettingEntity::getDel, 0);
            queryWrapper1.lambda().eq(CommentSettingEntity::getStatus, 1);
            queryWrapper1.lambda().le(CommentSettingEntity::getLikeNum, commentEntity.getLikeCount()+1);
            final List<CommentSettingEntity> commentSettingEntities = commentSettingMapper.selectList(queryWrapper1);
            if (ObjectUtils.isEmpty(commentSettingEntities)) {
                log.info("暂无评论设置信息,不做积分奖励");
            } else {
                BigDecimal reduce = commentSettingEntities.stream().map(e -> e.getRewardIntegral()).reduce(BigDecimal.ZERO, BigDecimal::max);

                log.info("给用户:[{}]奖励积分:[{}]",commentUser, reduce);
                IntegralEntity integral = IntegralEntity.builder()
                        .integral(reduce)
                        .userId(commentUser)
                        .taskId(model.getTaskId())
                        .createTime(LocalDateTime.now())
                        .commentId(model.getId())
                        .build();
                int save = integralMapper.insert(integral);
                if (save > 0) {
                    log.info("新增用户积分成功");
                } else {
                    throw new BootException("新增用户积分失败");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelUpvote(CommentModel model) {
        Assert.hasLength(model.getId(), "评论id不能为空");
        final String userId = SpringContextUtils.getUserId();
        QueryWrapper<UpvoteUserEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UpvoteUserEntity::getCommentId, model.getId()).eq(UpvoteUserEntity::getUserId, userId);
        int delete = upvoteMapper.delete(wrapper);
        if (delete > 0) {
            log.info("取消点赞成功");
        } else {
            throw new BootException("取消点赞失败");
        }
        commentMapper.cancelUpvote(model);
    }

    @Override
    public void saveComment(CommentModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
        Assert.hasLength(model.getComment(), "评论内容不能为空");
        Assert.hasLength(model.getCommentType(), "评论类型不能为空");
        final String userId = SpringContextUtils.getUserId();
        log.debug("转换前:[CommentModel:{}]", model);
        CommentEntity commentEntity = convertService.commentModelConvertToEntity(model);
        commentEntity.setCommentUser(userId);
        commentEntity.setCreateTime(LocalDateTime.now());
        log.debug("转换后:[commentEntity:{}]", commentEntity);
        int save = commentMapper.insert(commentEntity);
        if (save > 0) {
            log.info("评论成功");
        } else {
            throw new BootException("评论失败");
        }
        QueryWrapper<ListenRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ListenRecordEntity::getTaskId, model.getTaskId());
        queryWrapper.lambda().eq(ListenRecordEntity::getUserId, userId);
        queryWrapper.lambda().isNotNull(ListenRecordEntity::getSuccessTime);
        final ListenRecordEntity one = listRecordMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(one)) {
            one.setStatus("1");
            int update = listRecordMapper.update(one, queryWrapper);
            if (update > 0) {
                log.info("更新任务成功");
            } else {
                throw new BootException("更新任务失败");
            }
            QueryWrapper<SoundListeningEntity> soundQueryWrapper = new QueryWrapper<>();
            soundQueryWrapper.lambda().eq(SoundListeningEntity::getTaskId, model.getTaskId());
            soundQueryWrapper.lambda().eq(SoundListeningEntity::getDel, 0);
            soundQueryWrapper.lambda().last("limit 1");
            final SoundListeningEntity soundListeningEntity = soundListeningMapper.selectOne(soundQueryWrapper);
            QueryWrapper<IntegralEntity> integralEntityQueryWrapper = new QueryWrapper<>();
            integralEntityQueryWrapper.lambda().eq(IntegralEntity::getUserId, userId);
            integralEntityQueryWrapper.lambda().eq(IntegralEntity::getTaskId, model.getTaskId());
            final Integer integralCount = integralMapper.selectCount(integralEntityQueryWrapper);
            if(soundListeningEntity.getValidEndTime().toLocalDate().isAfter(one.getSuccessTime().toLocalDate())
                    &&ObjectUtils.isNotEmpty(integralCount)&&integralCount<1) {
                IntegralEntity integral = IntegralEntity.builder()
                        .integral(soundListeningEntity.getRewardIntegral())
                        .userId(userId)
                        .taskId(model.getTaskId())
                        .createTime(LocalDateTime.now())
                        .build();
                int insert = integralMapper.insert(integral);
                if (insert > 0) {
                    log.info("新增用户积分成功");
                } else {
                    throw new BootException("新增用户积分失败");
                }
            }
        }
    }

    @Override
    public void saveOrUpdateListenTask(ListenTaskModel model) {
        Assert.hasLength(model.getTaskId(), "任务id不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getListenDuration()), "聆听时长不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getSoundDuration()), "声音时长不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(model.getRewardIntegral()), "奖励积分不能为空");
        UserVo user = (UserVo)SpringContextUtils.getUser();
        QueryWrapper<ListenRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ListenRecordEntity::getTaskId, model.getTaskId());
        if(ObjectUtils.isNotEmpty(user)){
            queryWrapper.lambda().eq(ListenRecordEntity::getUserId, user.getUserId());
        }
        final ListenRecordEntity one = listRecordMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(one)) {
            final Integer listenTimes = one.getListenTimes();
            log.info("开始更新聆听记录");
            if ("1".equals(one.getStatus()) || model.getListenDuration() < one.getListenDuration()) {
                log.info("仅增加聆听次数");
                UpdateWrapper<ListenRecordEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(ListenRecordEntity::getId, one.getId());
                updateWrapper.lambda().set(ListenRecordEntity::getListenTimes, listenTimes + 1);
                updateWrapper.lambda().set(ListenRecordEntity::getUpdateTime, LocalDateTime.now());
                int update = listRecordMapper.update(one, updateWrapper);
                if (update > 0) {
                    log.info("增加聆听次数成功");
                } else {
                    throw new BootException("增加聆听次数失败");
                }
            } else {
                one.setListenDuration(model.getListenDuration());
                one.setUpdateTime(LocalDateTime.now());
                one.setListenTimes(listenTimes + 1);
                if (model.getListenDuration().longValue() < model.getSoundDuration().longValue()) {
                    //未完成
                    one.setStatus("0");
                } else {
                    //已完成
                    //若已评论过，则标记为已完成
                    QueryWrapper<CommentEntity> queryWrapper1 = new QueryWrapper<>();
                    queryWrapper1.lambda().eq(CommentEntity::getTaskId, model.getTaskId());
                    queryWrapper1.lambda().eq(CommentEntity::getCommentUser, user.getUserId());
                    final Integer commentEntity = commentMapper.selectCount(queryWrapper1);
                    if(ObjectUtils.isNotEmpty(commentEntity)&&commentEntity>0){
                        one.setStatus("1");
                        if(model.getEndTime().isAfter(LocalDateTime.now().toLocalDate())){
                            IntegralEntity integral = IntegralEntity.builder()
                                    .integral(model.getRewardIntegral())
                                    .userId(ObjectUtils.isNotEmpty(user)&&ObjectUtils.isNotEmpty(user.getUserId())?user.getUserId():"")
                                    .taskId(model.getTaskId())
                                    .createTime(LocalDateTime.now())
                                    .build();
                            int insert = integralMapper.insert(integral);
                            if (insert > 0) {
                                log.info("新增用户积分成功");
                            } else {
                                throw new BootException("新增用户积分失败");
                            }
                        }
                    }
                    one.setSuccessTime(LocalDateTime.now());
                }
                int update = listRecordMapper.updateById(one);
                if (update > 0) {
                    log.info("更新聆听记录成功");
                } else {
                    throw new BootException("更新聆听记录失败");
                }
            }
        } else {
            log.info("开始新增聆听记录");
            ListenRecordEntity build = ListenRecordEntity.builder()
                    .taskId(model.getTaskId())
                    .listenDuration(model.getListenDuration())
                    .soundDuration(model.getSoundDuration())
                    .userId(ObjectUtils.isNotEmpty(user)&&ObjectUtils.isNotEmpty(user.getUserId())?user.getUserId():"")
                    .departId(user.getDepartId())
                    .createTime(LocalDateTime.now())
                    .build();
            if (model.getListenDuration().longValue() < model.getSoundDuration().longValue()) {
                //未完成
                build.setStatus("0");
            } else {
                //已完成
                QueryWrapper<CommentEntity> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.lambda().eq(CommentEntity::getTaskId, model.getTaskId());
                queryWrapper1.lambda().eq(CommentEntity::getCommentUser, user.getUserId());
                final Integer commentEntity = commentMapper.selectCount(queryWrapper1);
                if(ObjectUtils.isNotEmpty(commentEntity)&&commentEntity>0){
                    build.setStatus("1");
                    if(model.getEndTime().isAfter(LocalDateTime.now().toLocalDate())) {
                        IntegralEntity integral = IntegralEntity.builder()
                                .integral(model.getRewardIntegral())
                                .userId(ObjectUtils.isNotEmpty(user) && ObjectUtils.isNotEmpty(user.getUserId()) ? user.getUserId() : "")
                                .taskId(model.getTaskId())
                                .createTime(LocalDateTime.now())
                                .build();
                        int insert = integralMapper.insert(integral);
                        if (insert > 0) {
                            log.info("新增用户积分成功");
                        } else {
                            throw new BootException("新增用户积分失败");
                        }
                    }
                }
                build.setSuccessTime(LocalDateTime.now());
            }
            int insert = listRecordMapper.insert(build);
            if (insert > 0) {
                log.info("保存聆听记录成功");
            } else {
                throw new BootException("保存聆听记录失败");
            }
        }
    }

    @Override
    public ListenTaskStatisticsVo getListenTaskStatistics() {
        final String userId = SpringContextUtils.getUserId();
        QueryWrapper<UserDepartEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserDepartEntity::getUserId, userId);
        final UserDepartEntity userDepartEntity = userDepartMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(userDepartEntity)) {
            throw new BootException("用户部门信息不存在");
        }
        ListenTaskStatisticsVo listenTaskStatistics = soundListeningMapper.getListenTaskStatistics(userId, userDepartEntity.getDepartId());
        int mandatory = ObjectUtils.isNotEmpty(listenTaskStatistics.getMandatoryCount()) ? listenTaskStatistics.getMandatoryCount() : 0;
        int notMandatory = ObjectUtils.isNotEmpty(listenTaskStatistics.getNotMandatoryCount()) ? listenTaskStatistics.getNotMandatoryCount() : 0;
        listenTaskStatistics.setCompleteCount(mandatory+notMandatory);
        listenTaskStatistics.setMandatoryWaitListenCount((listenTaskStatistics.getMandatoryTotalCount()- mandatory)>0?(listenTaskStatistics.getMandatoryTotalCount()- mandatory):0);
        listenTaskStatistics.setNonMandatoryWaitListenCount((listenTaskStatistics.getNotMandatoryTotalCount()- notMandatory)>0?(listenTaskStatistics.getNotMandatoryTotalCount()- notMandatory):0);
        return listenTaskStatistics;
    }


}
