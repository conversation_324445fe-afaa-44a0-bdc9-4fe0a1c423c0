package com.car.sound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.UserEntity;
import com.car.sound.mapper.UserMapper;
import com.car.sound.service.UserService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/9 11:45
 * @描述:
 **/
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {
    @Autowired
    SoundConvertService convertService;
    @Override
    public UserVo getUserInfoByUserId(String userId) {
        Assert.hasLength(userId,  "userId不能为空");
        final UserEntity userInfo = this.baseMapper.getUserInfoByUserId(userId);
        if(ObjectUtils.isEmpty(userInfo)){
            log.info("未查询到用户信息");
            return null;
        }
        UserVo userVo = convertService.userEntityConvertToVo(userInfo);
        userVo.setUserName(userInfo.getUserName());
        return userVo;
    }

    @Override
    public boolean checkUserByUserId(String userId) {
        Assert.hasLength(userId,  "userId不能为空");
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getUserId,userId);
        int count = this.count(queryWrapper);
        if(count<=0){
            log.info("未查询到用户信息");
            return false;
        }
        return true;
    }

    @Override
    public List<UserVo> getUserList() {
        List<UserEntity> list = this.list();
        if(ObjectUtils.isEmpty(list)){
            log.info("未查询到用户信息");
            return Collections.emptyList();
        }

        return convertService.userEntityConvertToVoList(list);
    }
}
