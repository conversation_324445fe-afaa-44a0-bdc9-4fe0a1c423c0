package com.car.sound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.sound.model.CommentSettingModel;
import com.car.sound.vo.CommentSettingVo;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:44
 * @描述:
 **/
public interface CommentSettingService {
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 10:46
     * @描述   新增评论设置
     * @param settingModel
     * @return void
     **/
    void saveCommentSetting(CommentSettingModel settingModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 10:46
     * @描述   修改评论设置
     * @param settingModel
     * @return void
     **/
    void updateCommentSetting(CommentSettingModel settingModel);
   /**
    * @创建者/修改者 fanrong
    * @创建/更新日期 2025/5/7 10:47
    * @描述   删除评论设置
    * @param id
    * @return void
    **/
    void deleteCommentSetting(String id);

    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 10:47
     * @描述   根据id查询评论设置
     * @param settingModel
     * @return com.car.sound.vo.CommentSettingVo
     **/
    CommentSettingVo getCommentSettingById(CommentSettingModel settingModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 10:47
     * @描述   查询评论设置列表
     * @param settingModel
     * @return java.util.List<com.car.sound.vo.CommentSettingVo>
     **/
    IPage<CommentSettingVo> getCommentSettingList(CommentSettingModel settingModel);
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 10:47
     * @描述   查询所有评论设置列表
     * @param
     * @return java.util.List<com.car.sound.vo.CommentSettingVo>
     **/
    List<CommentSettingVo> getAllCommentSettingList();
    /**
     * @创建者/修改者 fanrong
     * @创建/更新日期 2025/5/7 10:47
     * @描述   获取评论类型列表
     * @param
     * @return java.util.List<com.car.sound.vo.CommentSettingVo>
     **/
    List<CommentSettingVo> getCommentTypeList();
}
