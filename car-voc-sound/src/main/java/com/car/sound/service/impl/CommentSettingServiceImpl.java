package com.car.sound.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.CommentSettingEntity;
import com.car.sound.exception.BootException;
import com.car.sound.mapper.CommentSettingMapper;
import com.car.sound.model.CommentSettingModel;
import com.car.sound.service.CommentSettingService;
import com.car.sound.service.converts.SoundConvertService;
import com.car.sound.vo.CommentSettingVo;
import com.car.sound.vo.UserVo;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @创建者: fanrong
 * @创建时间: 2025/5/7 10:50
 * @描述:
 **/
@Service
@Slf4j
public class CommentSettingServiceImpl extends ServiceImpl<CommentSettingMapper, CommentSettingEntity> implements CommentSettingService {
    @Autowired
    SoundConvertService convertService;

    @Override
    public void saveCommentSetting(CommentSettingModel settingModel) {
        Assert.hasLength(settingModel.getName(),  "名称不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(settingModel.getSort()),  "排序不能为空");
        UserVo user = (UserVo) SpringContextUtils.getUser();
        log.debug("转换前:[settingModel:{}]",  settingModel);
        CommentSettingEntity commentSetting = convertService.commentSettingModelConvertToEntity(settingModel);
        commentSetting.setCreateTime(LocalDateTime.now());
        commentSetting.setCreateUser(ObjectUtils.isNotEmpty(user)?user.getUserName():"");
        log.debug("转换后:[commentSetting:{}]",  commentSetting);
        boolean save = this.save(commentSetting);
        if(save){
            log.info("新增评论设置成功");
        }else{
            throw new BootException("新增评论设置失败");
        }
    }

    @Override
    public void updateCommentSetting(CommentSettingModel settingModel) {
        Assert.hasLength(settingModel.getName(),  "名称不能为空");
        Assert.isTrue(ObjectUtils.isNotEmpty(settingModel.getSort()),  "排序不能为空");
        Assert.hasLength(settingModel.getId(),  "id不能为空");
        UserVo user = (UserVo) SpringContextUtils.getUser();
        log.debug("转换前:[settingModel:{}]",  settingModel);
        CommentSettingEntity commentSetting = convertService.commentSettingModelConvertToEntity(settingModel);
        commentSetting.setUpdateTime(LocalDateTime.now());
        commentSetting.setUpdateUser(ObjectUtils.isNotEmpty(user)?user.getUserName():"");
        log.debug("转换后:[commentSetting:{}]",  commentSetting);
        QueryWrapper<CommentSettingEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CommentSettingEntity::getId, settingModel.getId());
        boolean update = this.update(commentSetting, queryWrapper);
        if(update){
            log.info("修改评论设置成功");
        }else{
            throw new BootException("修改评论设置失败");
        }
    }

    @Override
    public void deleteCommentSetting(String id) {
        Assert.hasLength(id,  "id不能为空");
        UpdateWrapper<CommentSettingEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CommentSettingEntity::getId, id);
        updateWrapper.lambda().set(CommentSettingEntity::getDel, 1);
        updateWrapper.lambda().set(CommentSettingEntity::getUpdateTime, LocalDateTime.now());
        UserVo user = (UserVo) SpringContextUtils.getUser();
        updateWrapper.lambda().set(CommentSettingEntity::getUpdateUser, ObjectUtils.isNotEmpty(user)?user.getUserName():"");
        boolean delete = this.update(updateWrapper);
        if(delete){
            log.info("删除评论设置成功");
        }else{
            throw new BootException("删除评论设置失败");
        }
    }

    @Override
    public CommentSettingVo getCommentSettingById(CommentSettingModel settingModel) {
        Assert.hasLength(settingModel.getId(),  "id不能为空");
        QueryWrapper<CommentSettingEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CommentSettingEntity::getId, settingModel.getId());
        CommentSettingEntity commentSetting = this.getOne(queryWrapper);
        if(ObjectUtils.isEmpty(commentSetting)){
            log.info("未查询到评论设置");
            return null;
        }
        return convertService.commentSettingEntityConvertToVo(commentSetting);
    }

    @Override
    public IPage<CommentSettingVo> getCommentSettingList(CommentSettingModel settingModel) {
        IPage<CommentSettingEntity> page = new Page<>(settingModel.getPageNum(), settingModel.getPageSize());
        IPage<CommentSettingEntity> commentSettingList = this.baseMapper.getCommentSettingList(page, settingModel);
        IPage<CommentSettingVo> pages = new Page<>();
        if(ObjectUtils.isEmpty(commentSettingList.getRecords())){
            log.info("暂无评论设置信息");
            return pages;
        }
        pages.setCurrent(commentSettingList.getCurrent());
        pages.setPages(commentSettingList.getPages());
        pages.setSize(commentSettingList.getSize());
        pages.setTotal(commentSettingList.getTotal());
        if(ObjectUtils.isNotEmpty(commentSettingList.getRecords())){
            final List<CommentSettingEntity> records = commentSettingList.getRecords();
            List<CommentSettingVo> commentSettingVos = records.stream().map(e->{
                final CommentSettingVo commentSettingVo = convertService.commentSettingEntityConvertToVo(e);
                commentSettingVo.setCreateTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                return commentSettingVo;
            }).collect(Collectors.toList());
            pages.setRecords(commentSettingVos);
        }
        return pages;
    }

    @Override
    public List<CommentSettingVo> getAllCommentSettingList() {
        final List<CommentSettingEntity> commentSettingList = this.baseMapper.getAllCommentSettingList();
        if(ObjectUtils.isEmpty(commentSettingList)){
            log.info("暂无评论设置信息");
            return Collections.emptyList();
        }
        return convertService.commentSettingEntityConvertToVoList(commentSettingList);
    }

    @Override
    public List<CommentSettingVo> getCommentTypeList() {
        final List<CommentSettingEntity> commentSettingList = this.baseMapper.getCommentTypeList();
        if(ObjectUtils.isEmpty(commentSettingList)){
            log.info("暂无评论设置信息");
            return Collections.emptyList();
        }
        return convertService.commentSettingEntityConvertToVoList(commentSettingList);
    }
}
