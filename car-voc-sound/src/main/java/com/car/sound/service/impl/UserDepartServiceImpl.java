package com.car.sound.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.sound.entity.ListenSettingEntity;
import com.car.sound.entity.UserDepartEntity;
import com.car.sound.mapper.UserDepartMapper;
import com.car.sound.service.UserDepartService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @创建者: fanrong
 * @创建时间: 2025/6/4 11:52
 * @描述:
 **/
@Slf4j
@Service
public class UserDepartServiceImpl extends ServiceImpl<UserDepartMapper, UserDepartEntity> implements UserDepartService {
    @Override
    public void saveBatchUserDepart(List<UserDepartEntity> userDepartEntityList) {
        Assert.isTrue(ObjectUtils.isNotEmpty(userDepartEntityList), "userDepartEntityList不能为空");
        boolean saveBatch = this.saveOrUpdateBatch(userDepartEntityList);
        if(saveBatch){
            log.info("批量保存用户部门关联关系成功");
        }else{
            throw new RuntimeException("批量保存用户部门关联关系失败");
        }
    }
}
