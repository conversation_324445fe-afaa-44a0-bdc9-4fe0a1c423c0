package com.car.voc.risk;

import cn.hutool.extra.mail.MailUtil;
import com.car.voc.admin.VocSystemApplication;
import com.car.voc.service.IVocRiskAlertReviewerService;
import com.car.voc.service.IVocRiskHandlingRecordService;
import com.car.voc.task.VocClearAllRiskJob;
import com.car.voc.task.service.VocRiskJobService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.UniqueElements;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 *
 * @version 1.0.0
 * @ClassName IWarningDayStatisticServiceTest.java
 * @Description TODO
 * @createTime 2022年07月07日 13:50
 * @Copyright voc
 */

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VocSystemApplication.class)
@Log4j2
public class VocRiskServiceTest {

    @Autowired
    private VocClearAllRiskJob clearAllRiskJob;
    @Autowired
    private VocRiskJobService riskJobService;
    @Resource
    IVocRiskAlertReviewerService riskAlertReviewerService;
    @Resource
    IVocRiskHandlingRecordService handlingRecordService;
    /**
     * @Description 高频用户投诉警示测试
     *
     * @createTime 2021年07月06日
     * @Copyright voc
     */
    @Test
    public void userRiskUser()  {
        riskJobService.vocRiskUserJob();
    }
    @Test
    public void allDeleteRisks()  {
        clearAllRiskJob.vocClearAllRiskJob();
    }
    /**
     * @Description 风险事件警示测试
     *
     * @createTime 2021年07月06日
     * @Copyright voc
     */
    @Test
    public void eventsRisk() {
        riskJobService.vocRiskEventJob();
    }
    /**
     * @Description 风险质量警示测试
     *
     * @createTime 2021年07月06日
     * @Copyright voc
     */

    @Test
    public void qualityRisk() {
        riskJobService.vocRiskQualityJob();
    }
    @Test
    public void vocRiskRescueRisk() {
        riskJobService.vocRiskRescueJob();
    }
    @Test
    public void vocRiskBranches() {
        riskJobService.vocRiskBranchesJob();
    }

    @Test
    public void allRisk() {
        riskJobService.vocRiskEventJob();
        riskJobService.vocRiskQualityJob();
//        riskJobService.vocRiskUserJob();
    }



    @Test
    public void reviewerSendDingtalk() {
        riskAlertReviewerService.vocRiskReviewerSendDingtalkJob();
    }

    @Test
    public void vocRiskProcessTimeoutRemindJob() {
        handlingRecordService.vocRiskProcessTimeoutRemindJob();
    }






    @Test
    public void mailSendTest() {
        String conet="<img src=''/>";
        MailUtil.sendHtml("<EMAIL>;","图片测试",conet,null );
    }


}
