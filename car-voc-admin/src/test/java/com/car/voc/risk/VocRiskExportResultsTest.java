package com.car.voc.risk;

import cn.hutool.extra.mail.MailUtil;
import com.car.stats.serivce.IDwdVocQualityRiskService;
import com.car.stats.serivce.IDwdVocRiskService;
import com.car.voc.admin.VocSystemApplication;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 * @version 1.0.0
 * @ClassName IWarningDayStatisticServiceTest.java
 * @Description TODO
 * @createTime 2022年07月07日 13:50
 * @Copyright voc
 */

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VocSystemApplication.class)
@Log4j2
public class VocRiskExportResultsTest {


    @Autowired
    IDwdVocRiskService vocRiskService;

    @Autowired
    IDwdVocQualityRiskService vocQualityRiskService;


    @Test
    public void riskExport() {
        vocRiskService.riskExport();
    }
    @Test
    public void riskQualityExport() {
        vocQualityRiskService.riskQualityExport();
    }


}
