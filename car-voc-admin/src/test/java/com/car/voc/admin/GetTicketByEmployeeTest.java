package com.car.voc.admin;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.car.voc.service.ISysRoleService;
import lombok.extern.log4j.Log4j2;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * @version 1.0.0
 * @ClassName GetTicketByEmployeeTest.java
 * @Description TODO
 * @createTime 2023年03月17日 15:08
 * @Copyright voc
 */

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VocSystemApplication.class)
@Log4j2
public class GetTicketByEmployeeTest {

    @Resource
    ISysRoleService sysRoleService;
    @Test
    public  void  getTicket(){
        Map<String,Object> pamrs=new HashMap<>();
        pamrs.put("appKey","6wjo8y15f33tndpk4v1tsmj6l870760j");
        pamrs.put("employeeId","6000000001130003");
        pamrs.put("timestamp", DateUtil.current());
        char[] chars="6wjo8y15f33tndpk4v1tsmj6l870760j".toCharArray();
        Arrays.sort(chars);
        String signature= SecureUtil.md5(StrUtil.join(StrUtil.AT,new String(chars),pamrs.get("timestamp"),"6000000001130003"));
        pamrs.put("signature",signature);
        String url="http://127.0.0.1:6001/api/sys/ticketByEmployeeId";
      String s="";
      s=HttpUtil.post(url, JSON.toJSONString(pamrs) );
      System.out.println("返回："+s);
    }
    @Test
    public  void  getUserInfo(){
        sysRoleService.roleUserSyncJob();
    }

    private static final String ROLE_API_URL = "https://cc.dfes.com.cn/api/hybrid/vc/authority/role/page";
    private static final String BEARER_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI"; // 替换为实际token
    public static String testGetToken() {
        String url = "https://cc-dev.dfes.com.cn/api/oauth2/oauth/token";
        String requestBody = "grant_type=client_credentials&client_id=client_voc&client_secret=t8g1y1t2r0q8k2g0a5i7x0w8i1p1s7e2";

        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/x-www-form-urlencoded");
        request.body(requestBody);
        HttpResponse response = request.execute();
        System.out.println(JSONUtil.parseObj(response.body()));
        return JSONUtil.parseObj(response.body()).getStr("access_token");
    }
    public static String prodGetToken() {
        String url = "https://cc.dfes.com.cn/api/oauth2/oauth/token";
        String requestBody = "grant_type=client_credentials&client_id=client_voc&client_secret=bM0kC4aE0dE2nC0cG8aA2rU0jD3cG6rd";

        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/x-www-form-urlencoded");
        request.body(requestBody);
        HttpResponse response = request.execute();
        System.out.println(JSONUtil.parseObj(response.body()));
        return JSONUtil.parseObj(response.body()).getStr("access_token");
    }


    public static void main(String[] args) {

//    }

//    public  void  getRoles()  {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(ROLE_API_URL);
        post.setHeader("Authorization", "Bearer " + testGetToken());
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Cookie", "47975a629103793c3147b787ed00d5db=79f5bec0fa8d21b299521adb7bf9b51d");
        String jsonBody = "{\"page\": {\"currentPage\": 1, \"pageSize\": 40}, \"search\": {\"keyword\":\"voc\"}}";
        JsonNode roleList = null;

            try {
                post.setEntity(new StringEntity(jsonBody));

                CloseableHttpResponse response = client.execute(post);
                ObjectMapper mapper = new ObjectMapper();
                JsonNode root = mapper.readTree(response.getEntity().getContent());
                System.out.println("返回结果：" + root);

                // 直接使用根节点作为数组（根据你的示例，根节点本身就是数组）
                 roleList = root;

                // 添加数组类型校验
                if (!roleList.isArray()) {
                    System.out.println("返回结果不是数组格式");
                    return;
                }
//[{"createTime":1729566923960,"editable":true,"enabled":true,"id":"efbeb144-0996-4411-a8bc-764027a54ab2","isDefault":false,"name":"VOC(联调专用）","remark":"给VOC开发人员使用"},
// {"createTime":1732102866853,"editable":true,"enabled":true,"id":"b0a2e925-51fa-428a-bc54-9ecacdbd7378","isDefault":false,"name":"卓联-VOC管理人员","remark":"voc-admin"},
// {"createTime":1736427356822,"editable":true,"enabled":true,"id":"d1393e55-8efe-42d0-ab61-34b332cd1e52","isDefault":false,"name":"乘用车-VOC舆情监测人员","remark":"VOC-舆情监测人员"}]
                for (JsonNode role : roleList) {
                    // 添加字段存在性校验
                    if (role.has("name") && role.get("name").asText().toLowerCase().contains("voc")) {
                        String roleId = role.has("id") ? role.get("id").asText() : "未知ID";
                        System.out.println("匹配到角色: " + roleId);
                        queryUsersByRoleId(roleId);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    private static void queryUsersByRoleId(String roleId) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost("https://cc.dfes.com.cn/api/bff/graphql");
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", "Bearer " + prodGetToken());

        String graphqlQuery = String.format(
                "{\"query\":\"{\\n pageEmployee(\\n page: {currentPage: 1, pageSize: 500}\\n search: {roleIds: [\\\"%s\\\"]}\\n ) {\\n list {\\n phone\\n email\\n nickName\\n name\\n id\\n account\\n  organization {     orgName      id      orgCode  description  }  roleObjects {      createTime      editable      id      isDefault     name      enabled      remark    }  }\\n }\\n}\"}",
                roleId
        );
        try {
            post.setEntity(new StringEntity(graphqlQuery));
        }catch (Exception exception ){
            exception.printStackTrace();
        }

        try (CloseableHttpResponse response = client.execute(post)) {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(response.getEntity().getContent());
            JsonNode userList = root.path("data").path("pageEmployee").path("list");
//[{"nickName":"","name":"voc001","id":"9293d4cc-49ac-4e10-a925-e52c4437e8f4","account":"voc001"},
// {"nickName":"","name":"voc002","id":"********-6310-4017-8f0e-2d87dafc175f","account":"voc002"}]
//[{"nickName":"","name":"dfes05097","id":"175ce988-7f2e-4a23-ac4b-ecf034569253","account":"dfes05097"},
// {"nickName":"","name":"dfes22071","id":"2e507ae7-bf18-4269-b126-1d121b48918c","account":"dfes22071"},
// {"nickName":"","name":"voc001","id":"9293d4cc-49ac-4e10-a925-e52c4437e8f4","account":"voc001"},
// {"nickName":"","name":"voc002","id":"********-6310-4017-8f0e-2d87dafc175f","account":"voc002"},
// {"nickName":"","name":"张贝","id":"078d19ae-ea21-4397-8e50-275e745c9073","account":"000161"},
// {"nickName":"","name":"胡婷婷","id":"********-e938-4b4f-bffd-bd8979b562be","account":"000191"},
// {"nickName":"","name":"ZJ","id":"dd0404ed-d004-415b-a5a0-7c860529d726","account":"dfes1001"},
// {"nickName":"","name":"ZXL","id":"56c1086b-16e1-4ff5-abf6-5ab0629688f7","account":"dfes1012"},
// {"nickName":"","name":"WXY","id":"8d08e910-d69a-4cff-8435-d0c7da2e675e","account":"dfes1013"},
// {"nickName":"","name":"大为","id":"5ea725c3-ef0f-4819-bda7-ae1159aad2d4","account":"dfes88418"},
// {"nickName":"","name":"VOC-YQ","id":"c31ac4a0-d2fe-411e-89c9-8cf3b87955b5","account":"VOC-YQ"},
// {"nickName":"许文广","name":"许文广","id":"96ada1ab-dfa2-4bda-9dac-6e37a0fabebf","account":"003845"},
// {"nickName":"","name":"培战","id":"61a74625-bb4d-455d-bde2-f49687eedaaf","account":"107988"},
// {"nickName":"张婷婷","name":"张婷婷","id":"10b4e415-da0d-477e-802e-16f2f204b067","account":"001756"}]


// [{"phone":"","email":"","nickName":"","name":"voc001","id":"9293d4cc-49ac-4e10-a925-e52c4437e8f4","account":"voc001",
// "organization":{"orgName":"400呼入组","id":"06cd1d93-6c41-44cc-a6e3-700e359885f9","orgCode":"2-u","description":""},
// "roleObjects":[
//              {"createTime":null,"editable":true,"id":"52452be7-d3a5-4e94-9077-309582e3c3bd","isDefault":false,"name":"系统管理员（内部）","enabled":true,"remark":"（开发专用）项目实施/维护人员"},
//              {"createTime":null,"editable":true,"id":"b0a2e925-51fa-428a-bc54-9ecacdbd7378","isDefault":false,"name":"卓联-VOC管理人员","enabled":true,"remark":"voc-admin"},
//              {"createTime":null,"editable":true,"id":"efbeb144-0996-4411-a8bc-764027a54ab2","isDefault":false,"name":"VOC(联调专用）","enabled":true,"remark":"给VOC开发人员使用"}]},
//

// {"phone":"","email":"","nickName":"","name":"voc002","id":"********-6310-4017-8f0e-2d87dafc175f","account":"voc002",
// "organization":{"orgName":"奕派客服部","id":"5d76879c-2169-4f3c-bbec-1c29d3af932c","orgCode":"2-t","description":""},
// "roleObjects":[{"createTime":null,"editable":true,"id":"52452be7-d3a5-4e94-9077-309582e3c3bd","isDefault":false,"name":"系统管理员（内部）","enabled":true,"remark":"（开发专用）项目实施/维护人员"},
//                 {"createTime":null,"editable":true,"id":"b0a2e925-51fa-428a-bc54-9ecacdbd7378","isDefault":false,"name":"卓联-VOC管理人员","enabled":true,"remark":"voc-admin"},
//                 {"createTime":null,"editable":true,"id":"efbeb144-0996-4411-a8bc-764027a54ab2","isDefault":false,"name":"VOC(联调专用）","enabled":true,"remark":"给VOC开发人员使用"}]}]
            for (JsonNode user : userList) {
                String userId = user.get("id").asText();
                String account = user.get("account").asText();
                String name = user.get("name").asText();
                String nickName = user.get("nickName").asText();
                System.out.println("用户 ID: " + userId + ", 账号: " + account+ ", 姓名: " + name+ ", 昵称: " + nickName);
                System.out.println("user: " + user);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }



}
