package com.car.voc.admin;

import com.car.voc.component.ElasticsearchClient;
import com.car.voc.entity.SysUser;
import com.car.voc.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.QueryBuilders;
import org.jasypt.util.text.BasicTextEncryptor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VocSystemApplication.class)
@Slf4j
class CommonTest {

    @Autowired
    private ElasticsearchClient elasticsearchClient;
    @Test
    public void esQuery() throws IOException {
        SearchResponse searchResponse=elasticsearchClient.searchWithQueryBuilder("demo_warning_data_vocs","demo_warning_data_vocs_types", QueryBuilders.matchAllQuery());
        log.info(searchResponse.toString());
    }

    @Test
    public void encrypt(){
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        textEncryptor.setPassword("ftvoc2022");

        String userName = textEncryptor.encrypt("dealer");
        String passWord = textEncryptor.encrypt("svw2021#");

        System.out.println("userName==="+userName);
        System.out.println("passWord==="+passWord);


        textEncryptor.decrypt(passWord);
//        textEncryptor.decrypt("0BKi6tkGu82gymmEySYH0cUXaHCHwrt4usFw/iqoopk=");
        System.out.println("passWord==="+textEncryptor);
    }


    public static void main(String[] args) {
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        textEncryptor.setPassword("ftvoc2022");

        String userName = textEncryptor.encrypt("dealer");
        String passWord = textEncryptor.encrypt("svw2021#");

        System.out.println("userName==="+userName);
        System.out.println("passWord==="+passWord);


        textEncryptor.decrypt(passWord);
//        textEncryptor.decrypt("0BKi6tkGu82gymmEySYH0cUXaHCHwrt4usFw/iqoopk=");
        System.out.println("passWord解密==="+textEncryptor);




        String text = "The quick brown fox jumps over the lazy dog";
        System.out.println("Text      = " + text);

        BasicTextEncryptor bte = new BasicTextEncryptor();
        bte.setPassword("ftvoc2022");

        String encrypted = bte.encrypt(text);
        System.out.println("Encrypted = " + encrypted);

        String original = bte.decrypt("0BKi6tkGu82gymmEySYH0cUXaHCHwrt4usFw/iqoopk=");
        System.out.println("Original  = " + original);




    }

    @Autowired
    private ISysUserService sysUserService;

    @Test
    public void userpassword(){//
        SysUser user=new SysUser();
        user.setUsername("GW0200321");
        user.setPassword("GW0200321");
        sysUserService.changePassword(user);
    }


}
