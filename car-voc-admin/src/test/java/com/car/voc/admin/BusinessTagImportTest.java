package com.car.voc.admin;

import com.car.voc.service.IVocBusinessTagService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 *
 * @version 1.0.0
 * @ClassName BusinessTagImportTest.java
 * @Description 业务标签导入
 * @createTime 2023年03月13日 17:15
 * @Copyright voc
 */

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VocSystemApplication.class)
@Log4j2
public class BusinessTagImportTest {
    @Autowired
    private IVocBusinessTagService vocBusinessTagService;

    /**
     * @Description TODO
     *
     * @createTime 2021年07月06日
     * @Copyright voc
     */

    @Test
    public void testImport() {

        //# localAddress 为文件路径 例如 “/test/myNote/test.xlsx
        String localAddress="/opt/apps/dfmc_voc/VOC标签v2.0（含编码）1101.xlsx";
        try {
            File pdfFile = new File(localAddress);
            FileInputStream inputStream = new FileInputStream(pdfFile);
            MultipartFile multipartFile = new MockMultipartFile("file", pdfFile.getName(), "text/plain", IOUtils.toByteArray(inputStream));
            vocBusinessTagService.batchImport(multipartFile);
        } catch (IOException e) {
            e.printStackTrace();
        }



    }
}
