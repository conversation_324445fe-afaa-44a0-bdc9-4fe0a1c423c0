package com.car.voc.admin;

import cn.hutool.core.date.DateUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2024/2/4
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = VocSystemApplication.class)
@Log4j2
public class VocDateDimTest {


    static long sign=3165;

//    @Test
    public static void dimDateInit(){
        Date date= DateUtil.parseDate("2023-01-01");
        Date endDate=DateUtil.parseDate("2024-02-04");
        while(DateUtil.compare(date,endDate)<0){
            DateUtil.dayOfWeekEnum(date);
            int num=DateUtil.weekOfYear(date);
            String dwy;
            if(num>50&&DateUtil.month(date)==1){
                dwy=(DateUtil.year(date)-1)+"";
            }else if(num==1&&DateUtil.month(date)>1){
                dwy=(DateUtil.year(date)+1)+"";
            }else {
                dwy=DateUtil.year(date)+"";
            }
            log.debug("id:{},fromatDate:{},dateYear:{},dateMonth:{},dateWeek:{},dateQuarter:{},dateWeekYear:{}",sign,DateUtil.formatDateTime(date),DateUtil.year(date)+"",DateUtil.format(date, new SimpleDateFormat("MM")),num+"",DateUtil.quarter(date)+"",dwy);
            date=DateUtil.offsetDay(date,1);
            sign++;
        }
    }

    public static void main(String[] args) {
        dimDateInit();
    }
}
