server:
  port: 6017
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /api
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
spring:
  session:
    store-type: redis
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker    一级菜单id
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: false
      filter:
        config:
          enabled: false
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: false
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=false;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: ************************************************************************************************************************************************************************************************************************************
          url: *************************************************************************************************************************************************************************************************************
          username: voc_dongfeng
          password: voc_dongfeng@2024
        starrocks1:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***************************************************************************************************************************************************************************
          username: es_app
          password: Ejfauu1248cNSQHS
          druid:
            # 初始化时建立物理连接的个数
            initial-size: 5
            # 连接池的最小空闲数量
            min-idle: 5
            # 连接池最大连接数量
            max-active: 20
            # 获取连接时最大等待时间，单位毫秒
            max-wait: 60000
            # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
            test-while-idle: true
            # 既作为检测的间隔时间又作为testWhileIdel执行的依据
            time-between-eviction-runs-millis: 60000
            # 销毁线程时检测当前连接的最后活动时间和当前时间差大于该值时，关闭当前连接(配置连接在池中的最小生存时间)
            min-evictable-idle-time-millis: 30000
            # 用来检测数据库连接是否有效的sql 必须是一个查询语句(oracle中为 select 1 from dual)
            validation-query: select 'x'
            # 申请连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
            test-on-borrow: false
            # 归还连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
            test-on-return: false
            # 是否缓存preparedStatement, 也就是PSCache,PSCache对支持游标的数据库性能提升巨大，比如说oracle,在mysql下建议关闭。
            pool-prepared-statements: false

  #redis 配置
  redis:
    database: 0
#    database: 9
#    host: voc-redis-service.dfessubsidiary-call-center-dev.svc.cluster.local
    host: ************
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
#    port: 6379
    port: 31100
    password: ft2024
  elasticsearch:
    rest:
      is-ssl: false
#      uris: voc-elasticsearch-service.dfessubsidiary-call-center-dev.svc.cluster.local
#      port: 9200
      uris: ************
      port: 31102
      username: 'VOC'
      password: 'VOC@2024_127124'
      read-timeout: 120000
      connection-timeout: 120000  # 连接超时时间(ms)
      socket-timeout: 120000  # Socket超时时间(ms)
      max-conn-total: 200    # 最大连接数
      max-conn-per-route: 100   # 每个路由的最大连接数
      keep-alive-minutes: 5    # 连接保活时间(分钟)


#    rest:
#      is-ssl: false
#      uris: 10.250.11.44
#      port: 9200
#      username: ''
#      password: ''
#      read-timeout: 60000
#      connection-timeout: 60000



#
#  elasticsearch:
#    rest:
#      uris: http://192.168.0.205:9200
#      read-timeout: 60000


#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:com/car/voc/mapper/xml/*Mapper.xml,classpath*:com/car/stats/mapper/xml/*Mapper.xml,classpath*:com/car/report/mapper/xml/*Mapper.xml,classpath*:com/car/stats/mapper/xml/wo/*Mapper.xml,classpath*:com/car/sound/mapper/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
      # 逻辑删除字段名
      logic-delete-field: del_flag
      # 逻辑删除值（默认为1）
      logic-delete-value: 1
      # 逻辑未删除值（默认为0）
      logic-not-delete-value: 0
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
    #开启mybatis的二级缓存
    cache-enabled: true


configuration:
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  path:
    #文件上传根目录 设置
    upload: /opt/apps/demo-voc/file/
    #webapp文件路径
    webapp: /opt/apps/webapp
  shiro:
    excludeUrls: /sys/common/upload,/api/sys/common/getOssView,/sso/login/callback,/sys/checkTokenVaild,/third-party/*,/project-group/*,/largeScreenData/*,/sound/**
  thirdPartyToken: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MDkxMTQ3OTUsInVzZXJuYW1lIjoiTFU3M0syVlR6Slc2dS8xdHRpMDQrUT09In0.ikL3KD-b2f1NgqvAG7jESJyfncVt2f_TvPJTai9ys7U
  tokenCheckToken:
    baseUrl: http://crm-oauth2:8080
    hybridBaseUrl: http://crm-hybrid:8080/hybrid/vc
    bffBaseUrl: http://crm-api-bff:8080/bff/graphql
    checkTokenValidPath: http://crm-oauth2:8080/oauth2/oauth/check_token
    getUserInfoPath: http://crm-api-bff:8080/bff/graphql
    dictPath: ${configuration.tokenCheckToken.hybridBaseUrl}/dictionaries-category/groupId-completeTree?_method=get
    logoutPath: ${configuration.tokenCheckToken.baseUrl}/authCenter/GLO?redirectToLogin=true&redirectToUrl=https%3A%2F%2Fclient%2Eexample%2Ecom%2Fcallback
    clientId: client_voc
    clientSecret: t8g1y1t2r0q8k2g0a5i7x0w8i1p1s7e2
    roleId: 1848912218028703746
    departId: 524923408a0a48c3b0d01509cbbc3f9f
    ccDictData: 4347179d-8a69-47df-a531-a5cda15c456f
  dingTalk:
    appKey: xxxx
    appSecret: xxxxx
    workNoticesUrl: xxxxxxx
  #es索引
  esIndex:
    voc-sentence: voc_sentence_2
    voc-sentence-repeated: voc_sentence_repeated_2
  #阿里云oss存储配置
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKey: xxxxxxx
    secretKey: xxxxxxxxxxxxx
    bucketName: dfmc-voc
    staticDomain: ??
  #短信配置
  msg:
    spCode: 200487
    loginName: df_zlqc
    password: 938f4564fafb3883df87693756699ad4
    msgUrl: https://api.ums86.com:9600/sms/Api/Send.do
  #sso统一登录配置
  sso:
    #平台编码（必填）
    platform-code: xxxx
    #token用户验证地址
    SSOExchangeUrl: xxxx
    #test:测试环境，prod:正式环境（默认test）
    env: test
    #是否开始自动拦截，默认false
    auto-interceptor: true
    #自动拦截地址，当auto-interceptor为true时，interceptor-path必填
    interceptor-path: /test
    #是否外网：默认false
    is-wan: true
    #公网模式签名秘钥，公网模式下必填
    access-secret: xxxxxxxxxxxxxxxxxx
    #重定向地址
    rediectUrl: xxxx
    #重定向失败地址
    rediectErrorUrl: xxxx
#swagger
knife4j:
  enable: false
  setting:
    enableFilterMultipartApiMethodType: POST
    enableFilterMultipartApis: true
    enableVersion: true
  production: true


#Mybatis输出sql日志
logging:
  level:
    com.car.modules.*: debug



### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl:
  job:
    admin:
      addresses: http://************:31108/xxl-job-admin/
    accessToken: default_token
    enabled: false
    executor:
      disable: true
      appname: voc-risk-executor-${spring.profiles.active}
      address:
      ip:
      port: 9989
      logpath: /opt/apps/tf-voc/xxl-job/jobhandler
      logretentiondays: 30


report:
  service:
    chrome:
      chromedriver: /opt/apps/tf_voc/api/chrome/chrome-linux64_120.0.6099.62/chromedriver
#      chromedriver: /Users/<USER>/Downloads/chromedriver-mac-x64/chromedriver
    request:
      url: http://************:9998/#/voc/ppt/{}?q={}
#      codes: Q01,G01
    default:
      url: http://************:9998
      sleep: 10000
    active: dev
  default:
    codes:
      - codes: G01,G08,Q01
        width: 403
        height: 300
      - codes: G02,Q02
        width: 603
        height: 300
      - codes: G03,G07,Q03,Q04
        width: 1003
        height: 300
      - codes: G04
        width: 353
        height: 300
      - codes: G05,G06
        width: 653
        height: 300
      - codes: G09,G10,G11
        width: 503
        height: 300
      - codes: G12,G14
        width: 453
        height: 300
      - codes: G13,G15
        width: 553
        height: 300
      - codes: G16,G17,G18,G19,G20,G21,Q05,Q06,Q07,Q08
        width: 1003
        height: 400
  special:
    codes:
      - codes: G02,Q02
        width: 1003
        height: 300
voc:
  mail:
    username: <EMAIL>
    password: DFes@123
    storepassword: 123456
    path: /opt/key/df.jks
    url: https://mail.dfmc.com.cn/EWS/Exchange.asmx
    link: https://cc.dfes.com.cn/api/oauth2/login
    picturePath: /opt/key/1.png
es:
  indexName: voc_sentence_2

large:
  url: https://cc-dev.dfes.com.cn

minio:
  oss:
    accessKeyId: b8QtdZYKRyrxEHbwfbHq
    #accessKeyId: WUDchOzohKBvvzr6YDE7
    accessKeySecret: KzNE8ngfeEebWxZB1Ejsu6NmBmjNrQA1CtBqzZxh
    #accessKeySecret: YB7Z5IsRppmGUSSL19FSkJOHD8fmtp77oautGAqk
    endpoint: https://*************:6069
    # 可以公网访问的域名，将minio返回的 ip 和端口直接替换就可以访问了
    web_endpoint: https://*************:6069/files
    bucketName: voc-dev
    # km 最大支持 5g
    maxSize:  1

sound:
  prompt: "1、为强化全员客户意识，倡导全员聆听反馈，公司中高管及党员要求100%聆听记录并反馈;,2、内部资料仅供内部聆听使用，所有员工严禁进行录音或将其外传;,3、录音全部听完后， 可获得聆听积分,4、聆听结束后，请进行评论，发表评论后可获得评论积分"

listen:
  task:
    imageUrl: "VOC听见缩略图_1749182723075.png"