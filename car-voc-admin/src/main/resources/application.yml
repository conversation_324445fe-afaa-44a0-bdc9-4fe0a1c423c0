spring:
  application:
    name: voc-admin-dfmc
  profiles:
    active: prodlocal

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
logging:
  level:
    springfox: info

#logging.config=classpath: ./logs/logback-spring.xml
#logging.config=file: ./logs/logback-spring.xml
spring.cloud.nacos.config.enabled: false
spring.cloud.nacos.discovery.enabled: false
# 控制台彩色输出
spring.output.ansi.enabled: ALWAYS

jasypt.encryptor:
  #pbe,voc
  enabled: pbe
  #prefix: VOC(
  prefix: PBE(
  suffix: )