package com.car.voc.admin.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.SysUser;
import com.car.voc.model.DepartIdModel;
import com.car.voc.model.SysUserModel;
import com.car.voc.model.UserUpdetePasModel;
import com.car.voc.service.ISysUserService;
import com.car.voc.util.UploadFileService;
import com.car.voc.vo.SysUserListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.UUID;

@Slf4j
@Api(tags = "a-用户管理")
@RestController
@RequestMapping("/sys/user")
public class SysUserController {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private UploadFileService uploadFileService;


    @ApiOperation("上传")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        String string = file.getOriginalFilename();
        uploadFileService.putObject(file, string);
        String objectUrl = uploadFileService.getObjectUrl(string, 7 * 24 * 60 * 60);
        return Result.OK(objectUrl);

    }

    @AutoLog(value = "运营管理-账号管理")
    @ApiOperation(value = "获取用户列表数据-分页列表查询", notes = "获取用户列表数据-分页列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<SysUserListVo>> queryPageList(SysUserModel user, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        return sysUserService.queryPageList(user, pageNo, pageSize, req);

    }

    @ApiOperation("用户添加接口")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Result<SysUser> add(@RequestBody SysUserModel sysUserModel) {

        return sysUserService.add(sysUserModel);

    }

    @ApiOperation("用户编辑接口")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    public Result<SysUser> edit(@RequestBody SysUserModel sysUserModel) {
        return sysUserService.edit(sysUserModel);

    }

    @ApiOperation("删除用户接口")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)

    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.sysUserService.deleteUser(id);
        return Result.OK("删除用户成功");
    }

    @ApiOperation("根据id查询用户")
    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<SysUser> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<SysUser> result = new Result<SysUser>();
        SysUser sysUser = sysUserService.getById(id);
        if (sysUser == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(sysUser);
            result.setSuccess(true);
        }
        return result;
    }

    @ApiOperation("查询用户角色接口")
    @RequestMapping(value = "/queryUserRole", method = RequestMethod.GET)
    public Result<List<String>> queryUserRole(@RequestParam(name = "userid", required = true) String userid) {
        return sysUserService.queryUserRole(userid);

    }


    @ApiOperation(value = "校验用户账号是否唯一接口", notes = "(可以校验其他 需要检验什么就传什么。。。)")
    @RequestMapping(value = "/checkOnlyUser", method = RequestMethod.GET)
    public Result<Boolean> checkOnlyUser(SysUser sysUser) {
        return sysUserService.checkOnlyUser(sysUser);

    }


    @ApiOperation(value = "修改密码", notes = "修改密码")
    @RequestMapping(value = "/changePassword", method = RequestMethod.PUT)
    public Result<?> changePassword(@RequestBody SysUser sysUser) {
        SysUser u = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, sysUser.getId()));
        if (u == null) {
            return Result.error("用户不存在！");
        }
        sysUser.setId(u.getId());
//        sysUser.setUsername(u.getUsername());
//        sysUser.setType(u.getType());
        return sysUserService.changePassword(sysUser);
    }


    @ApiOperation(value = "查询指定用户和部门关联的数据")
    @RequestMapping(value = "/userDepartList", method = RequestMethod.GET)
    public Result<List<DepartIdModel>> getUserDepartsList(@RequestParam(name = "userId", required = true) String userId) {
        return sysUserService.getUserDepartsList(userId);
    }


    @ApiOperation(value = "根据部门id查询用户信息")
    @RequestMapping(value = "/queryUserByDepId", method = RequestMethod.GET)
    public Result<List<SysUser>> queryUserByDepId(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "realname", required = false) String realname) {
        return sysUserService.queryUserByDepId(id, realname);

    }


    @ApiOperation(value = "首页用户重置密码")
    @RequestMapping(value = "/updatePassword", method = RequestMethod.PUT)
    public Result<?> changPassword(@RequestBody UserUpdetePasModel userUpdetePasModel) {

        SysUser user = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, userUpdetePasModel.getUsername()));
        if (user == null) {
            return Result.error("用户不存在！");
        }
        return sysUserService.resetPassword(userUpdetePasModel);
    }


    @ApiOperation(value = "管理员修改密码", notes = "管理员修改密码")
    @RequestMapping(value = "/adminUpdatePassword", method = RequestMethod.PUT)
    public Result<?> adminUpdatePassword(@RequestBody UserUpdetePasModel userUpdetePasModel) {
        /*LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (!loginUser.getUsername().equals("gwm-admin")){
            return Result.error("无权限！");

        }*/
        SysUser user = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, userUpdetePasModel.getUsername()));
        if (user == null) {
            return Result.error("用户不存在！");
        }
        return sysUserService.adminUpdatePassword(userUpdetePasModel);
    }


}
