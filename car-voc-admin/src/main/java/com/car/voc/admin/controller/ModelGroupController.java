package com.car.voc.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.voc.common.Result;
import com.car.voc.entity.ModelGroup;
import com.car.voc.entity.ModelGroupRelation;
import com.car.voc.model.ModelGroupModel;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IModelGroupRelationService;
import com.car.voc.service.IModelGroupService;
import com.car.voc.vo.ModelGroupVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Api(tags="a-车型组管理")
@RestController
@RequestMapping("/voc/modelGroup")
@Slf4j
public class ModelGroupController {
   @Autowired
   private IModelGroupService iModelGroupService;
   @Autowired
    IModelGroupRelationService iModelGroupRelationService;

    @ApiOperation(value = "获取车型组列表数据-分页列表查询", notes = "获取车型组列表数据-分页列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<ModelGroup>> queryPageList(ModelGroupModel modelGroupModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {

        return iModelGroupService.queryPageList(modelGroupModel, pageNo, pageSize, req);

    }

    @ApiOperation(value = "获取所有车型组", notes = "获取所有车型组")
    @RequestMapping(value = "/listall", method = RequestMethod.GET)
    public Result<List<ModelGroupVo>> listall(ModelGroupModel modelGroupModel, HttpServletRequest req) {
        return iModelGroupService.listall(modelGroupModel, req);

    }
    @ApiOperation("车型组添加接口")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Result<ModelGroupVo> add(@RequestBody ModelGroupModel modelGroupModel) {

        return iModelGroupService.add(modelGroupModel);

    }

    @ApiOperation("车型组编辑接口")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    public Result<ModelGroupVo> edit(@RequestBody ModelGroupModel sysUserModel) {
        return iModelGroupService.edit(sysUserModel);

    }

    @ApiOperation("删除车型组接口")
    @RequestMapping(value = "/deleteModelGroup", method = RequestMethod.DELETE)
    public Result<?> deleteModelGroup(@RequestParam(name = "id", required = true) String id) {
        this.iModelGroupService.deleteModelGroup(id);
        return Result.OK("删除车型组成功");
    }
    @ApiOperation("删除车型接口")
    @RequestMapping(value = "/deleteSeries", method = RequestMethod.DELETE)
    public Result<?> deleteSeries(@RequestParam(name = "id", required = true) String id) {
        QueryWrapper<ModelGroupRelation> de=new QueryWrapper<>();
        de.lambda().eq(ModelGroupRelation::getModelGroupId,id);
        iModelGroupRelationService.remove(de);
        this.iModelGroupService.deleteModelGroup(id);
        return Result.OK("删除车型组成功");
    }

    @Autowired
    IBrandProductManagerService brandProductManagerService;

    @ApiOperation("根据id查询车型组")
    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<ModelGroupVo> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<ModelGroupVo> result = new Result<ModelGroupVo>();
        ModelGroup modelGroup = iModelGroupService.getById(id);
        ModelGroupVo modelGroupVo=new ModelGroupVo();
        BeanUtil.copyProperties(modelGroup,modelGroupVo);
        QueryWrapper<ModelGroupRelation> wrapper=new QueryWrapper<>();
        wrapper.lambda().eq(ModelGroupRelation::getModelGroupId,id);
        List<ModelGroupRelation> relations=iModelGroupRelationService.list(wrapper);
        relations.forEach(e->{
            e.setBrandName(brandProductManagerService.getCarNameByCarCode(e.getBrandCode()));
            e.setCarSeriesCode(brandProductManagerService.getCarNameByCarCode(e.getCarSeriesCode()));
        });
        modelGroupVo.setRelations(iModelGroupRelationService.list(wrapper));
        if (modelGroupVo == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(modelGroupVo);
            result.setSuccess(true);
        }
        return result;
    }

}
