package com.car.voc.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.entity.FaultProblem;
import com.car.voc.model.FaultProblemAddModel;
import com.car.voc.model.FaultProblemListQueryModel;
import com.car.voc.model.FaultProblemModel;
import com.car.voc.service.IFaultProblemService;
import com.car.voc.vo.FaultProblemListVo;
import com.car.voc.vo.FaultProblemTreeVo;
import com.car.voc.vo.FaultProblemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;


@Api(tags="a-质量问题管理")
@RestController
@RequestMapping("/faultProblem")
@Slf4j
public class FaultProblemController {
   @Autowired
   private IFaultProblemService faultProblemService;


   //@AutoLog(value = "质量问题体系-分页列表查询")
   @ApiOperation(value="质量问题体系(指标)-分页列表查询", notes="质量问题体系(指标)-分页列表查询")
   @GetMapping(value = "/rootList")
   public Result<?> queryPageList(FaultProblemListQueryModel queryModel,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {

       Page<FaultProblemListVo> page = new Page<>(pageNo, pageSize);
       IPage<FaultProblemListVo> voIPage=faultProblemService.queryByPage(page,queryModel,req);
       return Result.OK(voIPage);
   }


    //@AutoLog(value = "质量问题体系(指标)-树查询")
    @ApiOperation(value="质量问题体系(指标)-树查询", notes="质量问题体系(指标)-树查询")
    @GetMapping(value = "/listTree")
    public Result<?> queryTreeList(FaultProblemModel faultProblemModel,
                                   HttpServletRequest req) {
        QueryWrapper<FaultProblem> queryWrapper = new QueryWrapper<>();
        List<FaultProblemTreeVo> treelist=faultProblemService.queryTreeList(queryWrapper);
        return Result.OK(treelist);
    }




   //@AutoLog(value = "质量问题体系(指标)-获取子数据")
   @ApiOperation(value="质量问题体系(指标)-code获取子数据", notes="质量问题体系(指标)-获取子数据")
   @GetMapping(value = "/childList")
   public Result<?> queryPageList(FaultProblem faultProblem,HttpServletRequest req) {
       QueryWrapper<FaultProblem> queryWrapper = new QueryWrapper<>();

       if (StrUtil.isBlankIfStr(faultProblem.getPid())&& StrUtil.isNotBlank(faultProblem.getCode())&&!"0".equals(faultProblem.getCode())){
           queryWrapper.lambda().eq(FaultProblem::getCode,faultProblem.getCode());
           FaultProblem tag=faultProblemService.getOne(queryWrapper);
           queryWrapper = new QueryWrapper<>();
           queryWrapper.lambda().eq(FaultProblem::getPid,tag.getId());

       }else if (StrUtil.isNotBlank(faultProblem.getPid())){
           queryWrapper.lambda().eq(FaultProblem::getPid,faultProblem.getPid());
       }else {
           queryWrapper.lambda().eq(FaultProblem::getPid,"0");
       }
       List<FaultProblem> list = faultProblemService.list(queryWrapper);
       IPage<FaultProblem> pageList = new Page<>(1, 10, list.size());
       pageList.setRecords(list);
       return Result.OK(pageList);
   }


   //@AutoLog(value = "质量问题体系(指标)-批量获取子数据")
   @ApiOperation(value="质量问题体系(指标)-批量获取子数据", notes="质量问题体系(指标)-批量获取子数据")
   @GetMapping("/getChildListBatch")
   public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
       try {
           QueryWrapper<FaultProblem> queryWrapper = new QueryWrapper<>();
           List<String> parentIdList = Arrays.asList(parentIds.split(","));
           queryWrapper.in("pid", parentIdList);
           List<FaultProblem> list = faultProblemService.list(queryWrapper);
           IPage<FaultProblem> pageList = new Page<>(1, list.size(), list.size());
           pageList.setRecords(list);
           return Result.OK(pageList);
       } catch (Exception e) {
           log.error(e.getMessage(), e);
           return Result.error("批量查询子节点失败：" + e.getMessage());
       }
   }

   //@AutoLog(value = "质量问题体系(指标)-添加")
   @ApiOperation(value="质量问题体系(指标)-添加", notes="质量问题体系(指标)-添加")
   @PostMapping(value = "/add")
   public Result<?> add(@RequestBody FaultProblemAddModel addModel) {
       faultProblemService.add(addModel);
       return Result.OK("添加成功！");
   }

   //@AutoLog(value = "质量问题体系(指标)-编辑")
   @ApiOperation(value="质量问题体系(指标)-编辑", notes="质量问题体系(指标)-编辑")
   @PutMapping(value = "/edit")
   public Result<?> edit(@RequestBody FaultProblemAddModel edit) {
       faultProblemService.edit(edit);
       return Result.OK("编辑成功!");
   }

   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "质量问题体系(指标)-通过id删除")
   @ApiOperation(value="质量问题体系(指标)-通过id删除", notes="质量问题体系(指标)-通过id删除")
   @DeleteMapping(value = "/delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       faultProblemService.delete(id);
       return Result.OK("删除成功!");
   }



   /**
    * 通过id查询
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "质量问题体系(指标)-通过id查询")
   @ApiOperation(value="质量问题体系(指标)-通过id查询", notes="质量问题体系(指标)-通过id查询")
   @GetMapping(value = "/queryById")
   public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
       FaultProblemVo faultProblemVo = faultProblemService.getItemById(id);
       if(faultProblemVo==null) {
           return Result.error("未找到对应数据");
       }
       return Result.OK(faultProblemVo);
   }

    /**
     * 模板导入管理
     */
    @PostMapping("/batchImport")
    @ApiOperation(value = "批量导入", notes = "导入")
    public Result<?> batchImport(MultipartFile file) throws Exception {
//        return faultProblemService.batchImport(file);
        return Result.error("导入失败！！");
    }


}
