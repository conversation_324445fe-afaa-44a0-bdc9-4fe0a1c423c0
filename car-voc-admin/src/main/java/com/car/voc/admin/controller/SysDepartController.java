package com.car.voc.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.SysRole;
import com.car.voc.model.SysDepartModel;
import com.car.voc.service.CommonService;
import com.car.voc.service.ISysDepartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import wiremock.org.eclipse.jetty.util.StringUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;


@Api(tags="a-部门管理")
@RestController
@RequestMapping("/sys/sysDepart")
@Slf4j
public class SysDepartController {

	@Resource
	private ISysDepartService sysDepartService;



	@ApiOperation("分页列表查询")
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public Result<IPage<SysDepart>> queryPageList(SysDepartModel role,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												  @RequestParam(name="searchKeyword", defaultValue="") String searchKeyword,
												  @RequestParam(name="brandCode", defaultValue="") String brandCode,
												  HttpServletRequest req) {
		Result<IPage<SysDepart>> result = new Result<>();
		QueryWrapper<SysDepart> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(SysDepart::getDelFlag,"0");
		if(StrUtil.isNotBlank(role.getSearchKeyword())&&StrUtil.isNotBlank(brandCode)){
			queryWrapper.lambda().like(SysDepart::getBrandCode,brandCode).and(wrapper -> wrapper.like(SysDepart::getDepartName,searchKeyword).or().like(SysDepart::getDepartNameAbbr,searchKeyword));
		}else  if (StrUtil.isNotBlank(role.getSearchKeyword())) {
			queryWrapper.lambda().and(wrapper -> wrapper.like(SysDepart::getDepartName,searchKeyword).or().like(SysDepart::getDepartNameAbbr,searchKeyword));
		}else if (StrUtil.isNotBlank(brandCode)){
			queryWrapper.lambda().like(SysDepart::getBrandCode,brandCode);
		}
		queryWrapper.lambda().orderByDesc(SysDepart::getCreateTime);
		Page<SysDepart> page = new Page<>(pageNo, pageSize);
		IPage<SysDepart> pageList = sysDepartService.page(page, queryWrapper);
		List<SysDepart> records = pageList.getRecords();
		if (CollectionUtils.isNotEmpty(records)){
			for (SysDepart sysDepart:records){
				if (!StringUtils.isEmpty(sysDepart.getBrandCode())) {
					List<String> list = Arrays.asList(sysDepart.getBrandCode().split(","));
					sysDepart.setBrandCodeList(list);
				}
			}
		}
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}

	@ApiOperation("查询所有部门")
	@RequestMapping(value = "/alllist", method = RequestMethod.GET)
	public Result<List<SysDepart>> alllist(@RequestParam(value = "brandCode",required = false) String brandCode) {
		Result<List<SysDepart>> result = new Result<>();
		QueryWrapper<SysDepart> wrapper=new QueryWrapper<>();
		wrapper.lambda().eq(SysDepart::getDelFlag,0);
		wrapper.lambda().eq(SysDepart::getStatus,1);
		if (!StringUtils.isEmpty(brandCode)){
			wrapper.lambda().like(SysDepart::getBrandCode,brandCode);
		}
		List<SysDepart> pageList = sysDepartService.list(wrapper);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}



	//@RequiresRoles({"admin"})
	@ApiOperation("添加部门")
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@CacheEvict(value= {CacheConstant.SYS_DEPARTS_CACHE,CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries=true)
	public Result<SysDepart> add(@RequestBody SysDepartModel sysDepart, HttpServletRequest request) {
		return sysDepartService.add(sysDepart,request);
	}
	@ApiOperation("编辑部门")
	@RequestMapping(value = "/edit", method = RequestMethod.PUT)
	@CacheEvict(value= {CacheConstant.SYS_DEPARTS_CACHE,CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries=true)
	public Result<SysDepart> edit(@RequestBody SysDepartModel sysDepartModel, HttpServletRequest request) {
		SysDepart sysDepart=new SysDepart();
		BeanUtil.copyProperties(sysDepartModel,sysDepart);
		if (CollectionUtils.isNotEmpty(sysDepartModel.getBrandCodeList())) {
			String join = String.join(",", sysDepartModel.getBrandCodeList());
			sysDepart.setBrandCode(join);
		}
		String username = CommonService.getUserNameByToken(request);
		sysDepart.setUpdateBy(username);
		Result<SysDepart> result = new Result<SysDepart>();
		SysDepart sysDepartEntity = sysDepartService.getById(sysDepart.getId());
		if (sysDepartEntity == null) {
			result.error500("未找到对应实体");
		} else {
			boolean ok = sysDepartService.updateDepartDataById(sysDepart, username);
			// TODO 返回false说明什么？
			if (ok) {
				//清除部门树内存
				//FindsDepartsChildrenUtil.clearSysDepartTreeList();
				//FindsDepartsChildrenUtil.clearDepartIdModel();
				result.success("修改成功!");
			}
		}
		return result;
	}
	@ApiOperation("删除部门")
	@RequestMapping(value = "/delete", method = RequestMethod.DELETE)
	@CacheEvict(value= {CacheConstant.SYS_DEPARTS_CACHE,CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries=true)
	public Result<SysDepart> delete(@RequestParam(name="id",required=true) String id) {

		Result<SysDepart> result = new Result<SysDepart>();
		SysDepart sysDepart = sysDepartService.getById(id);
		if(sysDepart==null) {
			result.error500("未找到对应实体");
		}else {
			sysDepart.setDelFlag("1");
			boolean ok = sysDepartService.updateById(sysDepart);
			if(ok) {
				//清除部门树内存
				//FindsDepartsChildrenUtil.clearSysDepartTreeList();
				// FindsDepartsChildrenUtil.clearDepartIdModel();
				result.success("删除成功!");
			}
		}
		return result;
	}


}
