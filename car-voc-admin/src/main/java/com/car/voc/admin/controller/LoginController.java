package com.car.voc.admin.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.JwtUtil;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.SysUser;
import com.car.voc.model.LoginUser;
import com.car.voc.model.SysLoginModel;
import com.car.voc.model.ThirdLoginModel;
import com.car.voc.model.TokenCheckTokenVaildModel;
import com.car.voc.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * @version 1.0.0
 * @ClassName LoginController.java
 * @Description TODO
 * @createTime 2022年09月06日 10:36
 * @Copyright voc
 */
@RestController
@RequestMapping("/sys")
@Api(tags="a-用户登录")
@Slf4j
public class LoginController {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private ISysUserService sysUserService;


    @ApiOperation("登录接口")
    @AutoLog(value = "登录",logType = CommonConstant.LOG_TYPE_1)
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public Result<JSONObject> login(@RequestBody SysLoginModel sysLoginModel) {
        return sysUserService.login(sysLoginModel);
    }
    @ApiOperation("token获取")//http://xx/api/sys/ticketByEmployeeId
//    @AutoLog(value = "token获取登录",logType = CommonConstant.LOG_TYPE_1)
    @RequestMapping(value = "/ticketByEmployeeId", method = RequestMethod.POST)
    public Result<?> ticketByEmployeeId(@RequestBody ThirdLoginModel sysLoginModel) {
        return sysUserService.ticketByEmployeeId(sysLoginModel);
    }

    @ApiOperation("统一登录token获取")
//    @AutoLog(value = "token获取登录",logType = CommonConstant.LOG_TYPE_1)
    @RequestMapping(value = "/checkTokenVaild", method = RequestMethod.POST)
    @AutoLog(value = "登录",logType = CommonConstant.LOG_TYPE_1)
    public Result<?> checkTokenVaild(@RequestBody TokenCheckTokenVaildModel checkTokenVaild, HttpServletRequest request ) {
        return sysUserService.checkTokenVaild(checkTokenVaild,request);
    }



    /**
     * 退出登录
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/logout")
    public Result<Object> logout(HttpServletRequest request, HttpServletResponse response) {
        //用户退出逻辑
        String token = request.getHeader(CommonConstant.X_ACCESS_TOKEN);
        if(StrUtil.isEmpty(token)) {
            return Result.error("退出登录失败！");
        }

       String username=  JwtUtil.getUsername(token);
        LoginUser sysUser=sysUserService.getUserByName(username);
        if(sysUser!=null) {
            log.info(" 用户名:  "+sysUser.getRealname()+",退出成功！ ");
            //清空用户登录Token缓存
            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
            //清空用户登录Shiro权限缓存
            redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
            //清空用户的缓存信息（包括部门信息），例如sys:cache:user::<username>
            redisUtil.del(String.format("%s::%s", CacheConstant.SYS_USERS_CACHE, sysUser.getUsername()));
            redisUtil.del(String.format("%s:%s", CacheConstant.SYS_USERS_CACHE_INFO, sysUser.getId()));
            redisUtil.del(CacheConstant.SYS_USER_API_PERMISSION+sysUser.getId());

         /*   redisUtil.del(CacheConstant.SYS_USER_CHANNEL+sysUser.getId());
            redisUtil.del(CacheConstant.SYS_USER_SERIES+sysUser.getId());
            redisUtil.del(CacheConstant.SYS_USER_TAG+sysUser.getId());
            redisUtil.del(CacheConstant.SYS_USER_PERMISSION+sysUser.getId());
            redisUtil.del(CacheConstant.SYS_USER_TAG_OTHER+sysUser.getId());
            redisUtil.del(CacheConstant.SYS_USER_AREA+sysUser.getId());
*/


            //调用shiro的logout
            SecurityUtils.getSubject().logout();
            return Result.OK("退出登录成功！");
        }else {
            return Result.error("Token无效!");
        }
    }




}
