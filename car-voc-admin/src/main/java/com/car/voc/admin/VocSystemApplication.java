package com.car.voc.admin;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetAddress;

@Slf4j
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.car.voc.*", "com.car.stats.*", "com.car.report.*","com.car.sound.*"})
//@SpringBootApplication(scanBasePackages = {"com.car"})
@ServletComponentScan
//@MapperScan(basePackages = {"com.car.sound.mapper","com.car.voc.mapper",
//        "com.car.stats.mapper", "com.car.stats.strksMapper",
//        "com.car.report.mapper","com.car.stats.mapper.wo"})
@MapperScan(basePackages = {"com.car.*.mapper", "com.car.stats.strksMapper", "com.car.stats.mapper.wo"})
@EnableEncryptableProperties
@EnableCaching
public class VocSystemApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(VocSystemApplication.class);
    }

    public static void main(String[] args) throws IOException {

        ConfigurableApplicationContext application = SpringApplication.run(VocSystemApplication.class, args);
        Environment env = application.getEnvironment();

        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application voc系统 is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "Swagger文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                "----------------------------------------------------------");
    }


    /*@PostConstruct
    public void run() throws Exception{
        SslUtils.ignoreSsl();
    }*/

}
