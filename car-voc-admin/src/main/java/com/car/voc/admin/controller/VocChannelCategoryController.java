package com.car.voc.admin.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.model.ChannelEditSortModel;
import com.car.voc.service.IVocChannelCategoryService;
import com.car.voc.vo.VocChannelCategoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import wiremock.org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName VocChannelCategoryController.java
 * @Description TODO
 * @createTime 2023年05月10日 14:20
 * @Copyright voc
 */
@Api(tags="a-渠道管理")
@RestController
@RequestMapping("/sys/channelCategory")
@Slf4j
public class VocChannelCategoryController {

    @Autowired
    IVocChannelCategoryService channelCategoryService;

    @AutoLog(value = "运营管理-渠道管理")
    @ApiOperation(value="渠道管理(列表)-分页列表查询", notes="渠道管理(列表)-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(VocChannelCategory channelCategory,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        Page<VocChannelCategoryVo> page = new Page<>(pageNo, pageSize);
        IPage<VocChannelCategoryVo> voIPage=channelCategoryService.queryByPage(page,channelCategory,req);
        List<VocChannelCategoryVo> records = voIPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)){
            for (VocChannelCategoryVo vocChannelCategoryVo:records){
                if(StringUtils.isNotEmpty(vocChannelCategoryVo.getBrandCode())) {
                    List<String> list = Arrays.asList(vocChannelCategoryVo.getBrandCode().split(","));
                    vocChannelCategoryVo.setBrandCodeList(list);
                }
            }
        }
        return Result.OK(voIPage);
    }




//    @AutoLog(value = "渠道管理-添加")
    @ApiOperation(value="渠道管理-添加", notes="渠道管理-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody VocChannelCategory model) {
        boolean add = channelCategoryService.add(model);
        if (!add){
            Result<?> result = new Result<>();
            result.error500("渠道名称不能重复");
            return Result.error("渠道名称不能重复");
        }
        return Result.OK("添加成功！");
    }

//    @AutoLog(value = "渠道管理-编辑")
    @ApiOperation(value="渠道管理-编辑", notes="渠道管理-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody VocChannelCategory model) {
        boolean edit = channelCategoryService.edit(model);
        if (!edit){
            Result<?> result = new Result<>();
            result.error500("渠道名称不能重复");
            return Result.error("渠道名称不能重复");
        }
        return Result.OK("编辑成功!");
    }
//    @AutoLog(value = "渠道管理-修改排序")
    @ApiOperation(value="渠道管理-修改排序", notes="渠道管理-修改排序")
    @PutMapping(value = "/editSort")
    public Result<?> editSort(@RequestBody ChannelEditSortModel model) {
        UpdateWrapper<VocChannelCategory> up=new UpdateWrapper<>();
        up.lambda().eq(VocChannelCategory::getId,model.getId()).set(VocChannelCategory::getOrderBy,model.getOrderBy());
        channelCategoryService.update(up);
        return Result.OK("修改成功!");
    }


//    @AutoLog(value = "渠道管理-通过id删除")
    @ApiOperation(value="渠道管理-通过id删除", notes="渠道管理-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        channelCategoryService.delete(id);
        return Result.OK("删除成功!");
    }

}
