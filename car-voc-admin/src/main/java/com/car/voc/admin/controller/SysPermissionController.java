package com.car.voc.admin.controller;

import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.service.ISysPermissionService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@Api(tags="a-菜单权限 前端控制器")
@RestController
@RequestMapping("/sys/permission")
public class SysPermissionController {
    @Resource
    private ISysPermissionService sysPermissionService;

    /**
     * 查询用户拥有的菜单权限和按钮权限
     *
     * @return
     */
    @RequestMapping(value = "/getUserPermissionByToken", method = RequestMethod.GET)
    public Result<?> getUserPermissionByToken() {
        long start = System.currentTimeMillis();
        Result<?> r = sysPermissionService.getUserPermissionByToken();
        long end = System.currentTimeMillis();
        log.debug("getUserPermissionByToken,总耗时:{}毫秒",  (end - start));
        return r;
    }
}
