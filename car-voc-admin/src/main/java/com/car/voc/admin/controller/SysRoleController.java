package com.car.voc.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.SysRole;
import com.car.voc.model.SysRoleModel;
import com.car.voc.service.ISysRoleService;
import com.car.voc.vo.ChannelCategoryVo;
import com.car.voc.vo.auth.RoleAuthTree;
import com.car.voc.vo.auth.RoleAuthVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0.0
 * @ClassName SysRoleController.java
 * @Description TODO
 * @createTime 2022年09月14日 10:21
 * @Copyright voc
 */

@Api(tags = "a-角色管理")
@RestController
@RequestMapping("/sys/role")
@Slf4j
public class SysRoleController {

    @Autowired
    ISysRoleService sysRoleService;


    @AutoLog(value = "运营管理-部门与角色")
    @ApiOperation("分页列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<RoleAuthVo>> queryPageList(SysRoleModel role,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(name = "searchKeyword", defaultValue = "") String searchKeyword,
                                                   @RequestParam(name = "brandCode", defaultValue = "") String brandCode,
                                                   HttpServletRequest req) {
        if (StrUtil.isNotBlank(searchKeyword)) {
            role.setSearchKeyword(searchKeyword);
        }
        if (StringUtils.isNotEmpty(brandCode)) {
            role.setBrandCode(brandCode);
        }
        return sysRoleService.queryPageLit(role, pageNo, pageSize, req);
    }

    @ApiOperation("查询所有可用的角色")
    @RequestMapping(value = "/listAll", method = RequestMethod.GET)
    public Result<List<RoleAuthVo>> queryList() {
        return sysRoleService.queryList();
    }


    @ApiOperation("添加更新角色")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Result<SysRole> saveOrUpdate(@RequestBody SysRoleModel roleModel) {
        return sysRoleService.saveOrUpdate(roleModel);
    }

    @ApiOperation("角色授权功能，查询菜单权限树")
    @RequestMapping(value = "/queryTreeList", method = RequestMethod.GET)
    public Result<Map<String, Object>> queryTreeList(@RequestParam(name = "roleId", required = true) String roleId, HttpServletRequest request) {
        SysRoleModel roleModel = new SysRoleModel();
        roleModel.setId(roleId);
        return sysRoleService.queryTreeList(roleModel, request);
    }


    @ApiOperation("角色删除通过id删除")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) throws Exception {
        sysRoleService.deleteRole(id);
        return Result.OK("删除角色成功");
    }

    @ApiOperation("根据id查询角色")
    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<RoleAuthVo> queryById(@RequestParam(name = "id", required = true) String id) {
        RoleAuthVo role = sysRoleService.queryById(id, "");
        Result<RoleAuthVo> result = new Result<>();
        result.setResult(role);
        result.setSuccess(true);
        return result;
    }


    @ApiOperation("根据品牌ID 获取渠道车系区域")
    @RequestMapping(value = "/getDataByBrandCode", method = RequestMethod.GET)
    public Result<List<RoleAuthVo>> getDataByBrandCode(@RequestParam(name = "brandCode", required = false) String brandCode) {
        List<RoleAuthVo> dataByBrandCodeList = sysRoleService.getDataByBrandCode(brandCode);
        return Result.OK(dataByBrandCodeList);
    }


    @ApiOperation("添加更新角色")
    @RequestMapping(value = "/newSaveOrUpdate", method = RequestMethod.POST)
    public Result<SysRole> newSaveOrUpdate(@RequestBody List<SysRoleModel> roleModelList) {
        return sysRoleService.newSaveOrUpdate(roleModelList);
    }

    @ApiOperation("根据 RoleId 获取编辑回显的数据")
    @RequestMapping(value = "/getListByRoleId", method = RequestMethod.GET)
    public Result<List<RoleAuthVo>> getListByRoleId(@RequestParam(name = "roleId") String roleId) {
        List<RoleAuthVo> listByRoleId = sysRoleService.getListByRoleId(roleId);
        return Result.OK(listByRoleId);
    }


    @AutoLog(value = "渠道管理-获取全部渠道")
    @ApiOperation(value = "渠道管理-获取全部渠道", notes = "渠道管理-获取全部渠道")
    @GetMapping(value = "/allChannel")
    public Result<?> allChannel() {
        List<ChannelCategoryVo> stringListMap = sysRoleService.allChannel();
        return Result.OK(stringListMap);
    }

    @ApiOperation("角色删除通过id删除")
    @RequestMapping(value = "/newDelete", method = RequestMethod.DELETE)
    public Result<?> newDelete(@RequestParam(name = "id") String id) throws Exception {
        sysRoleService.deleteRole(id);
        return Result.OK("删除角色成功");
    }

}
