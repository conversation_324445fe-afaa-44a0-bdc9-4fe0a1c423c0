package com.car.voc.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.VocBusinessTag;
import com.car.voc.model.VocBusinessTagListQueryModel;
import com.car.voc.model.VocBusinessTagModel;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.vo.VocBusinessTagListVo;
import com.car.voc.vo.VocBusinessTagVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;


@Api(tags = "a-业务标签管理")
@RestController
@RequestMapping("/vocBusinessTag/vocBusinessTag")
@Slf4j
public class VocBusinessTagController {
    @Autowired
    private IVocBusinessTagService vocBusinessTagService;

    /**
     * 分页列表查询
     *
     * @param vocBusinessTag
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "运营管理-标签管理")
    @ApiOperation(value = "业务标签体系(指标)-分页列表查询", notes = "业务标签体系(指标)-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(VocBusinessTagListQueryModel vocBusinessTag,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        Page<VocBusinessTagListVo> page = new Page<>(pageNo, pageSize);
        IPage<VocBusinessTagListVo> voIPage = vocBusinessTagService.queryByPage(page, vocBusinessTag, req);
        return Result.OK(voIPage);
    }


    @ApiOperation(value = "业务标签体系(指标)-树查询", notes = "业务标签体系(指标)-树查询")
    @GetMapping(value = "/listTree")
    public Result<?> queryTreeList(VocBusinessTag vocBusinessTag,
                                   HttpServletRequest req) {
        QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
        List<VocBusinessTagVo> treelist = vocBusinessTagService.queryTreeList(queryWrapper);
        return Result.OK(treelist);
    }


    /**
     * 获取子数据
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "业务标签体系(指标)-code获取子数据", notes = "业务标签体系(指标)-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(VocBusinessTag vocBusinessTag, HttpServletRequest req) {
        if (StrUtil.isNotBlank(vocBusinessTag.getCode())) {
            vocBusinessTag.setTagCode(vocBusinessTag.getCode());
        }
        QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isBlankIfStr(vocBusinessTag.getPid()) && (StrUtil.isNotBlank(vocBusinessTag.getTagCode())) && !"0".equals(vocBusinessTag.getTagCode())) {
            queryWrapper.lambda().eq(VocBusinessTag::getTagCode, vocBusinessTag.getTagCode());
            VocBusinessTag tag = vocBusinessTagService.getOne(queryWrapper);
            queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(VocBusinessTag::getPid, tag.getId());

        } else if (StrUtil.isNotBlank(vocBusinessTag.getPid())) {
            queryWrapper.lambda().eq(VocBusinessTag::getPid, vocBusinessTag.getPid());
        } else {
            queryWrapper.lambda().eq(VocBusinessTag::getPid, "0");
        }
        List<VocBusinessTag> list = vocBusinessTagService.list(queryWrapper);
        IPage<VocBusinessTag> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return
     */
    @ApiOperation(value = "业务标签体系(指标)-批量获取子数据", notes = "业务标签体系(指标)-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<VocBusinessTag> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<VocBusinessTag> list = vocBusinessTagService.list(queryWrapper);
            IPage<VocBusinessTag> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }

    //@AutoLog(value = "业务标签体系(指标)-添加")
    @ApiOperation(value = "业务标签体系(指标)-添加", notes = "业务标签体系(指标)-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody VocBusinessTagModel vocBusinessTagModel) {
        vocBusinessTagService.addVocBusinessTag(vocBusinessTagModel);
        return Result.OK("添加成功！");
    }

    //@AutoLog(value = "业务标签体系(指标)-编辑")
    @ApiOperation(value = "业务标签体系(指标)-编辑", notes = "业务标签体系(指标)-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody VocBusinessTagModel vocBusinessTagModel) {
        vocBusinessTagService.updateVocBusinessTag(vocBusinessTagModel);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "业务标签体系(指标)-通过id删除")
    @ApiOperation(value = "业务标签体系(指标)-通过id删除", notes = "业务标签体系(指标)-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        vocBusinessTagService.deleteVocBusinessTag(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    //@AutoLog(value = "业务标签体系(指标)-批量删除")
    @ApiOperation(value = "业务标签体系(指标)-批量删除", notes = "业务标签体系(指标)-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.vocBusinessTagService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "业务标签体系(指标)-通过id查询")
    @ApiOperation(value = "业务标签体系(指标)-通过id查询", notes = "业务标签体系(指标)-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        VocBusinessTag vocBusinessTag = vocBusinessTagService.getById(id);
        if (vocBusinessTag == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(vocBusinessTag);
    }

    /**
     * 模板导入管理
     */
    @PostMapping("/batchImport")
    @ApiOperation(value = "批量导入", notes = "导入")
    public Result<?> batchImport(MultipartFile file) {
//        return vocBusinessTagService.batchImport(file);
        return Result.error("导入失败！！");
    }


}
