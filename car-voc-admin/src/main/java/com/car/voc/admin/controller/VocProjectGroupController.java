package com.car.voc.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.entity.VocProjectGroup;
import com.car.voc.service.CommonService;
import com.car.voc.service.VocProjectGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "a-项目组管理")
@RestController
@RequestMapping("/project-group")
public class VocProjectGroupController {

    @Autowired
    private VocProjectGroupService projectGroupService;

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public Result<?> save(@RequestBody VocProjectGroup projectGroup, HttpServletRequest req) {
        // 校验项目组名称长度
        Assert.hasText(projectGroup.getProjectName(), "项目组名称不能为空");
        Assert.isTrue(projectGroup.getProjectName().length() > 8 && projectGroup.getProjectName().length() < 32, "项目组名称长度需在8~32个字符之间");
        Assert.hasText(projectGroup.getLeaderId(), "项目组负责人不能为空");
        String username = CommonService.getUserNameByToken(req);
        projectGroup.setUpdateBy(username);
        projectGroupService.saveProjectGroup(projectGroup);
        return Result.OK();
    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public Result<?> update(@RequestBody VocProjectGroup projectGroup, HttpServletRequest req) {
        // 校验项目组名称长度
        Assert.hasText(projectGroup.getProjectName(), "项目组名称不能为空");
        Assert.isTrue(projectGroup.getProjectName().length() > 8 && projectGroup.getProjectName().length() < 32, "项目组名称长度需在8~32个字符之间");
        Assert.hasText(projectGroup.getLeaderId(), "项目组负责人不能为空");
        String username = CommonService.getUserNameByToken(req);
        projectGroup.setUpdateBy(username);
        projectGroupService.updateProjectGroup(projectGroup);
        return Result.OK();
    }

    @ApiOperation(value = "项目组列表")
    @GetMapping("/list")
    public Result<Page<VocProjectGroup>> list(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                              String brandCode, String keyword) {
        Page<VocProjectGroup> page = new Page<>(pageNo, pageSize);
        return Result.OK(projectGroupService.pageList(page, brandCode, keyword));
    }
}
