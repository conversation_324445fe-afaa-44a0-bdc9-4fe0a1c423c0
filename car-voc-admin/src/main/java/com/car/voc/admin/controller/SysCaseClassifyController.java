package com.car.voc.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.stats.model.CaseLibraryModel;
import com.car.stats.serivce.ICaseLibraryService;
import com.car.stats.vo.CaseLibraryListVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.SysCaseClassify;
import com.car.voc.exception.BootException;
import com.car.voc.model.SysCaseClassifyModel;
import com.car.voc.service.CommonService;
import com.car.voc.service.ISysCaseClassifyService;
import com.car.voc.vo.SysCaseClassifyListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * TODO
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/7/28 16:37
 **/
@Slf4j
@Api(tags="a-案例分类")
@RestController
@RequestMapping("/sys/case")
public class SysCaseClassifyController {

    @Autowired
    private ISysCaseClassifyService sysCaseClassifyService;

    @Autowired
    private ICaseLibraryService caseLibraryService;

    @AutoLog(value = "运营管理-案例管理")
    @ApiOperation(value = "获取案例分类列表数据-分页列表查询", notes = "获取案例分类数据-分页列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<SysCaseClassifyListVo>> queryPageList(SysCaseClassifyModel caseClassify, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        try{
            return sysCaseClassifyService.queryPageList(caseClassify,pageNo,pageSize,req);
        }catch (Exception e){
            log.error("案例分类查询异常：{}",e.getMessage());
            throw new BootException("案例分类查询异常");
        }
    }

    @ApiOperation("案例分类新增接口")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Result<SysCaseClassify> add(@RequestBody SysCaseClassifyModel caseClassifyModel, HttpServletRequest req) {
        try{
            return sysCaseClassifyService.add(caseClassifyModel,req);
        }catch (Exception e){
            log.error("案例分类新增异常：{}",e.getMessage());
            throw new BootException("案例分类新增异常");
        }
    }

    @ApiOperation("案例分类编辑接口")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    public Result<SysCaseClassify> edit(@RequestBody SysCaseClassifyModel caseClassifyModel, HttpServletRequest req) {
        try{
            return sysCaseClassifyService.edit(caseClassifyModel,req);
        }catch (Exception e){
            log.error("案例分类编辑异常：{}",e.getMessage());
            throw new BootException("案例分类编辑异常");
        }
    }

    @ApiOperation("案例分类删除接口")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id, HttpServletRequest req) {
        try{
            String username = CommonService.getUserNameByToken(req);
            log.info("用户：{}案例分类删除操作，id为:{}",username,id);
            Integer caseClassifyNum = caseLibraryService.getCaseClassifyId(id);
            if(caseClassifyNum > 0){
                return Result.error("该分类下有案例，不能删除！");
            }
            Boolean caseClassifyBoolean = sysCaseClassifyService.deleteCaseClassify(id,req);
            if(caseClassifyBoolean){
                return Result.OK("案例分类删除成功");
            }else{
                return Result.error("案例分类删除失败");
            }
        }catch (Exception e){
            log.error("案例分类删除异常：{}",e.getMessage());
            throw new BootException("案例分类删除异常");
        }
    }

    @ApiOperation("根据id查询案例分类")
    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<SysCaseClassify> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<SysCaseClassify> result = new Result<SysCaseClassify>();
        SysCaseClassify sysCaseClassify = sysCaseClassifyService.getById(id);
        if (sysCaseClassify == null) {
            result.error500("未找到案例分类对应实体");
        } else {
            result.setResult(sysCaseClassify);
            result.setSuccess(true);
        }
        return result;
    }

    @ApiOperation(value = "案例审核列表", notes = "案例审核列表")
    @RequestMapping(value = "/caseExaminelist", method = RequestMethod.GET)
    public Result<IPage<CaseLibraryListVo>> queryPageCaseExaminelist(CaseLibraryModel caseLibraryModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        try{

            return caseLibraryService.queryPageCaseExaminelist(caseLibraryModel,pageNo,pageSize,req);
        }catch (Exception e){
            log.error("案例审核列表查询异常：{}",e.getMessage());
            throw new BootException("案例审核列表查询异常"+e.getMessage());
        }
    }

    @ApiOperation(value = "案例审核-批量审核", notes = "案例审核-批量审核")
    @RequestMapping(value = "/caseBatchReview", method = RequestMethod.POST)
    public Result<?> caseBatchReview(@RequestBody List<CaseLibraryModel> caseLibraryModel, HttpServletRequest req) {
        try{

            caseLibraryService.caseBatchReview(caseLibraryModel,req);
            return Result.OK("案例批量审核成功");
        }catch (Exception e){
            log.error("案例审核批量审核异常：{}",e.getMessage());
            throw new BootException("案例审核批量审核异常"+e.getMessage());
        }
    }
}
