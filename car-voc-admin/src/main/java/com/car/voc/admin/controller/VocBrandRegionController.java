package com.car.voc.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.SysDictItem;
import com.car.voc.entity.VocBrandRegion;
import com.car.voc.service.VocBrandRegionService;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.NewDictVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import wiremock.org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * (VocBrandRegion)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-20 17:03:32
 */
@RestController
@RequestMapping("/sys/vocBrandRegion")
@Api(tags="a-系统区域")
public class VocBrandRegionController {
    /**
     * 服务对象
     */
    @Resource
    private VocBrandRegionService vocBrandRegionService;

    @ApiOperation(value="查询区域省份列表", notes="查询区域省份列表")
    @RequestMapping(value = "/arealist", method = RequestMethod.GET)
    public Result<IPage<VocBrandRegion>> arealist(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                              @RequestParam(name = "brandCode", required = false) String brandCode,
                                              HttpServletRequest req) {
        Result<IPage<VocBrandRegion>> result = new Result<>();
        QueryWrapper<VocBrandRegion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StrUtil.isNotBlank(brandCode),VocBrandRegion::getBrand, brandCode);
        Page<VocBrandRegion> page = new Page<>(pageNo, pageSize);
        IPage<VocBrandRegion> pageList = vocBrandRegionService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

}

