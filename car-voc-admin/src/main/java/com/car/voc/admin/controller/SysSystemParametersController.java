package com.car.voc.admin.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.entity.SysDictItem;
import com.car.voc.service.ISysDictItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 *
 * @date ：Created in 2021/6/23
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@Api(tags="a-默认参数配置")
@RestController
@RequestMapping("/sys/parameters")
public class SysSystemParametersController {
@Autowired
ISysDictItemService sysDictItemService;
    /**
     * @功能：系统参数配置
     * @return
     */
    @ApiOperation(value="系统参数配置", notes="系统参数配置")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<JSONObject> queryPageList(SysDictItem sysDictItem, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletRequest req) {
        return parameters(sysDictItem,pageNo,pageSize,req);
    }

    @AutoLog(value = "运营管理-系统配置")
    @ApiOperation(value="用户获取参数配置", notes="参数配置")
    @RequestMapping(value = "/adminList", method = RequestMethod.GET)
    public Result<JSONObject> adminList(SysDictItem sysDictItem, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletRequest req) {
        return parameters(sysDictItem,pageNo,pageSize,req);
    }



    /**
     * @功能：编辑
     * @param sysDictItem
     * @return
     */
    @ApiOperation(value="系统参数配置编辑", notes="系统参数配置编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT,produces = {"application/json;charset=UTF-8"})
    @CacheEvict(value= CacheConstant.SYS_DICT_CACHE, allEntries=true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sysDictItem", value = "系统参数配置编辑参数： { \"vocHomeTimeRange\": \"7\", \"systemPictureElements\": \"logo.png\", \"downloadDocumentWatermark\": \"0\", \"defaultTimePeriod\": \"0\", \"systemWatermarkDisplay\": \"1\" }"),
    })
    public Result<?> edit(@RequestBody Map<String,Object> sysDictItem) {
        boolean ok=false;
            if (sysDictItem!=null&&sysDictItem.size()>0){
                for (Map.Entry<String, Object> stringObjectEntry : sysDictItem.entrySet()) {
                    UpdateWrapper<SysDictItem> wrapper=new UpdateWrapper();
                    wrapper.lambda().eq(SysDictItem::getItemText,stringObjectEntry.getKey())
                            .set(SysDictItem::getItemValue,stringObjectEntry.getValue());
                     ok= sysDictItemService.update(wrapper);
                     if (!ok) {
                         break;
                     }
                }
            }
        Result<?> result =new Result<>();
        result.setSuccess(true);
        return result;
    }



    public  Result<JSONObject>  parameters(SysDictItem sysDictItem, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                           @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletRequest req){
        Result<JSONObject> result = new Result<JSONObject>();
        sysDictItem.setDictId("1407532004891533314");
        QueryWrapper<SysDictItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysDictItem::getDictId,"1407532004891533314");
        queryWrapper.orderByAsc("sort_order");
        Page<SysDictItem> page = new Page<SysDictItem>(0, 20);
        List<SysDictItem> pageList = sysDictItemService.page(page, queryWrapper).getRecords();
        JSONObject jsonObject=new JSONObject();
        for (SysDictItem dictItem : pageList) {
            jsonObject.putOpt(dictItem.getItemText(),dictItem.getItemValue());
        }
        result.setSuccess(true);
        result.setResult(jsonObject);
        return result;
    }

}
