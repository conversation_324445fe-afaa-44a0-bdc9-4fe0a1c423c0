package com.car.voc.admin.controller.risk;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.RiskKeyword;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.service.IRiskKeywordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Update;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Api(tags="a-风险关键词管理")
@RestController
@RequestMapping("/sys/risk/riskKeyword")
@Slf4j
public class RiskKeywordController {
   @Autowired
   private IRiskKeywordService iRiskKeywordService;

    @ApiOperation(value = "获取风险关键词列表数据-分页列表查询", notes = "获取风险关键词列表数据-分页列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<?> queryPageList(RiskKeyword model, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<RiskKeyword> queryWrapper =new QueryWrapper<>();
        queryWrapper.lambda().like(StrUtil.isNotBlank(model.getSearchKeyword()),RiskKeyword::getRiskKeyword,model.getSearchKeyword());

        queryWrapper.lambda().eq(StrUtil.isNotBlank(model.getClassify()),RiskKeyword::getClassify,model.getClassify());
        queryWrapper.lambda().like(StrUtil.isNotBlank(model.getBrandCode()),RiskKeyword::getBrandCode,model.getBrandCode());
//        queryWrapper.lambda().like(StrUtil.isNotBlank(model.getWeight()),RiskKeyword::getWeight,model.getWeight());

        if(ObjectUtils.isNotEmpty(model.getWeight())){
            queryWrapper.lambda().eq(RiskKeyword::getWeight,model.getWeight());
        }
        queryWrapper.lambda().eq(RiskKeyword::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.lambda().orderByDesc(RiskKeyword::getCreateTime);

        Page<RiskKeyword> page = new Page<>(pageNo, pageSize);
        IPage<RiskKeyword> pageList = iRiskKeywordService.page(page, queryWrapper);
        for (RiskKeyword record : pageList.getRecords()) {
            if(ObjectUtils.isNotEmpty(record.getBrandCode())){
                record.setBrandCodes(Arrays.asList(record.getBrandCode().split(",")));
            }
        }
        return Result.OK(pageList);

    }
    @ApiOperation("风险关键词添加更新接口")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Result<?> add(@RequestBody RiskKeyword model) {

        LambdaQueryWrapper<RiskKeyword> query = new LambdaQueryWrapper<RiskKeyword>().eq(RiskKeyword::getRiskKeyword, model.getRiskKeyword());
        if (!StringUtils.isEmpty(model.getId())){
            query.ne(RiskKeyword::getId,model.getId());
        }
        List<RiskKeyword> brandProductManagers = iRiskKeywordService.list(query);
        if(CollectionUtil.isNotEmpty(brandProductManagers)){
            Result<?> result = new Result<>();
            result.error500("风险关键词名称不能重复");
            return Result.error("风险关键词名称不能重复");
        }
        String brandCodes = StringUtils.join(",", model.getBrandCodes());
        model.setBrandCode(brandCodes);
        model.setCreateTime(new Date());
        iRiskKeywordService.saveOrUpdate(model);
        return Result.OK(model);

    }
    @ApiOperation("删除风险关键词接口")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public Result<?> deleteRiskKeyword(@RequestParam(name = "id", required = true) String id) {
        UpdateWrapper<RiskKeyword> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(RiskKeyword::getId, id)
                .set(RiskKeyword::getDelFlag, CommonConstant.DEL_FLAG_1);
        this.iRiskKeywordService.update(updateWrapper);
        return Result.OK("删除风险关键词成功");
    }


}
