package com.car.voc.admin.controller.risk;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.car.voc.common.Result;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.*;
import com.car.voc.mapper.BrandProductManagerMapper;
import com.car.voc.mapper.SysRoleBusinessTagMapper;
import com.car.voc.mapper.SysRoleMapper;
import com.car.voc.model.risk.RiskAlertReviewerModel;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.service.IVocRiskAlertReviewerService;
import com.car.voc.service.IVocRiskProessRecipientService;
import com.car.voc.vo.VocBrandCodeTagVo;
import com.car.voc.vo.VocBusinessTagVo;
import com.car.voc.vo.risk.ReviewerPermissionVo;
import com.car.voc.vo.risk.RiskAlertReviewerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 * @ClassName VocRiskAlertReviewerController.java
 * @Description TODO
 * @createTime 2023年02月06日 10:38
 * @Copyright voc
 */

@Slf4j
@Api(tags = "a-警示审核人员")
@RestController
@RequestMapping("/sys/risk/reviewer")
public class VocRiskAlertReviewerController {

    @Autowired
    IVocRiskAlertReviewerService riskAlertReviewerService;

    @Autowired
    private IVocBusinessTagService vocBusinessTagService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysRoleBusinessTagMapper sysRoleBusinessTagMapper;


    @ApiOperation("警示审核人员更新接口")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Result<?> saveOrUpdate(@RequestBody RiskAlertReviewerModel reviewerModel) {
        return riskAlertReviewerService.saveOrUpdate2(reviewerModel);

    }

    @ApiOperation("删除警示审核人员接口")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)

    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.riskAlertReviewerService.deleteReviewer(id);
        return Result.OK("删除警示审核人员成功");
    }

    @ApiOperation("根据id查询审核人员信息")
    @RequestMapping(value = "/queryById", method = RequestMethod.GET)
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {

        return riskAlertReviewerService.queryById(id);
    }

    @ApiOperation(value = "获取警示审核人员-分页列表查询", notes = "获取警示审核人员-分页列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<RiskAlertReviewerVo>> queryPageList(VocRiskAlertReviewer user, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        return riskAlertReviewerService.queryPageList(user, pageNo, pageSize, req);

    }

    @ApiOperation(value = "问题审核人员-查询审核人配置权限", notes = "问题审核人员-查询审核人配置权限")
    @RequestMapping(value = "/queryPermissionByUserId", method = RequestMethod.GET)
    public ReviewerPermissionVo queryPermissionByUserId(@RequestParam(name = "userId", required = true) String userId) {
        return riskAlertReviewerService.queryPermissionByUserId(userId);

    }

    @ApiOperation(value = "问题审核人员-查询处理人配置权限", notes = "问题审核人员-查询处理人配置权限")
    @RequestMapping(value = "/queryHandlePermissionByUserId", method = RequestMethod.GET)
    public ReviewerPermissionVo queryHandlePermissionByUserId(@RequestParam(name = "userId", required = true) String userId) {
        return riskAlertReviewerService.queryHandlePermissionByUserId(userId);

    }

    @ApiOperation("审核人员树结构")
    @RequestMapping(value = "/topicsList", method = RequestMethod.GET)
    public Result<List<VocBusinessTagVo>> allTopics(@RequestParam(name = "id", required = false) String id,
                                                    @RequestParam(name = "userId", required = false) String userId,
                                                    @RequestParam(name = "brandCode", required = false) String brandCode) {
        Result<List<VocBusinessTagVo>> result = new Result<List<VocBusinessTagVo>>();
        List<VocBusinessTagVo> ls = null;
        try {
            //根据部门人员id 获取权限
//            Set<Object> permissionSet = redisUtil.sGet(CacheConstant.SYS_USER_TAG + userId);
//            List<String> roleTagCodes = permissionSet.stream().map(String::valueOf).collect(Collectors.toList());

            QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
            wrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
            List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
            ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
//            ls = ls.stream().filter(s -> roleTagCodes.contains(s.getTagCode())).collect(Collectors.toList());
            ls.sort(Comparator.comparing(VocBusinessTag::getOrderBy));


            //用户设置业务标签
//            List<VocRiskProcessRecipientTag> recipientTags = recipientService.selectByIdBrandCode(id, brandCode);


            List<VocBusinessTagVo> resultTag = new ArrayList<>();
            //1. 构建一级节点
            for (VocBusinessTagVo tag : ls) {
                if ("0".equals(tag.getPid())) {
                    resultTag.add(tag);
                }
            }

            // 2、递归获取子节点
            for (VocBusinessTagVo parent : resultTag) {
                parent = recursiveTree(parent, ls);
            }

            result.setSuccess(true);
            result.setResult(resultTag);
            log.info(result.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }

        return result;
    }


    @ApiOperation("获取品牌")
    @RequestMapping(value = "/getBrandCodeList", method = RequestMethod.GET)
    public Result<List<String>> getBrandCodeList(@RequestParam(name = "userId") String userId) {
        return Result.OK(riskAlertReviewerService.getBrandCodeList(userId));
    }


    @ApiOperation("审核人员树结构")
    @RequestMapping(value = "/newTopicsList", method = RequestMethod.GET)
    public Result<VocBrandCodeTagVo> newTopicsList(@RequestParam(name = "userId") String userId,
                                                   @RequestParam(name = "brandCode") String brandCode) {
        Result<VocBrandCodeTagVo> result = new Result<>();
        List<VocBusinessTagVo> ls;
        QueryWrapper<VocBusinessTag> wrapper = new QueryWrapper<>();
        wrapper.lambda().orderByDesc(VocBusinessTag::getOrderBy);
        List<VocBusinessTag> tags = vocBusinessTagService.list(wrapper);
        VocBrandCodeTagVo vocBrandCodeTagVo = new VocBrandCodeTagVo();
        try {
            ls = BeanUtil.copyToList(tags, VocBusinessTagVo.class);
            ls.sort(Comparator.comparing(VocBusinessTag::getOrderBy));
            if (StringUtils.isNotBlank(userId)) {
                SysRole roleByUserId = sysRoleMapper.getRoleByUserId(userId);
                if (ObjectUtils.isNotEmpty(roleByUserId)) {
                    String code = roleByUserId.getBrandCode();
                    String qualityText = roleByUserId.getQualityText();
                    if (StringUtils.isNotBlank(code)) {
                        List<String> brandCodeList = Arrays.asList(code.split(","));
                        if (brandCodeList.contains(brandCode)) {
                            List<SysRoleBusinessTag> sysRoleBusinessTags = sysRoleBusinessTagMapper.listByRoleId(roleByUserId.getId(), brandCode);
                            if (CollectionUtil.isNotEmpty(sysRoleBusinessTags)) {
                                List<String> businessTags = sysRoleBusinessTags.stream().map(SysRoleBusinessTag::getTagCode).collect(Collectors.toList());
                                List<VocBusinessTagVo> resultTag = new ArrayList<>();
                                //1. 构建一级节点
                                for (VocBusinessTagVo tag : ls) {
                                    if ("0".equals(tag.getPid()) && businessTags.contains(tag.getTagCode())) {
                                        resultTag.add(tag);
                                    }
                                }
                                // 2、递归获取子节点
                                for (VocBusinessTagVo parent : resultTag) {
                                    newRecursiveTree(parent, ls, businessTags);
                                }
                                vocBrandCodeTagVo.setBrandCode(brandCode);
                                vocBrandCodeTagVo.setVocBusinessTagVoList(resultTag);
                                vocBrandCodeTagVo.setUserPermission(Boolean.TRUE);
                                vocBrandCodeTagVo.setQualityPermission(getQuality(brandCode, qualityText));
                            }
                        }
                    }
                }
            } else {
                List<VocBusinessTagVo> resultTag = new ArrayList<>();
                //1. 构建一级节点
                for (VocBusinessTagVo tag : ls) {
                    if ("0".equals(tag.getPid())) {
                        resultTag.add(tag);
                    }
                }
                for (VocBusinessTagVo parent : resultTag) {
                    recursiveTree(parent, ls);
                }
                vocBrandCodeTagVo.setBrandCode(brandCode);
                vocBrandCodeTagVo.setVocBusinessTagVoList(resultTag);
                vocBrandCodeTagVo.setUserPermission(Boolean.TRUE);
                vocBrandCodeTagVo.setQualityPermission(Boolean.TRUE);
            }
            result.setSuccess(true);
            result.setResult(vocBrandCodeTagVo);
            log.info(result.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
            return result;
        }
        return result;
    }


    private boolean getQuality(String brandCode, String qualityText) {
        if (wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(qualityText) && wiremock.org.apache.commons.lang3.StringUtils.isNotEmpty(brandCode)) {
            JSONObject object = JSON.parseObject(qualityText);
            if (object.containsKey(brandCode)) {
                Boolean o = (Boolean) object.get(brandCode);
                return o;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 递归
     */
    private void newRecursiveTree(VocBusinessTagVo parent, List<VocBusinessTagVo> list, List<String> codes) {
        List<VocBusinessTagVo> scouts = new ArrayList<>();
        List<String> collect = list.stream().map(VocBusinessTagVo::getTagCode).collect(Collectors.toList());
        for (VocBusinessTagVo ziplocFieldDab : list) {
            if (Objects.equals(parent.getId(), ziplocFieldDab.getPid())) {
                if (codes.contains(ziplocFieldDab.getTagCode())) {
                    newRecursiveTree(ziplocFieldDab, list, collect);
                    scouts.add(ziplocFieldDab);
                    parent.setChildes(scouts);
                }
            }
        }
    }


    /**
     * 递归
     *
     * @return
     */
    private VocBusinessTagVo recursiveTree(VocBusinessTagVo parent, List<VocBusinessTagVo> list) {
        List<VocBusinessTagVo> seouns = new ArrayList<>();
        for (VocBusinessTagVo zcprojectFieldDto : list) {
            if (Objects.equals(parent.getId(), zcprojectFieldDto.getPid())) {
                recursiveTree(zcprojectFieldDto, list);
                seouns.add(zcprojectFieldDto);
                parent.setChildes(seouns);
            }
        }
        return parent;
    }

}
