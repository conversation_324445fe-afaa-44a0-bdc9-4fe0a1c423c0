package com.car.voc.admin.controller.risk;

import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.entity.VocRiskWarningRules;
import com.car.voc.entity.VocRiskWarningRulesDetailed;
import com.car.voc.service.IVocRiskWarningRulesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * @version 1.0.0
 * @ClassName VocRiskWarningRulesController.java
 * @Description TODO
 * @createTime 2023年02月06日 11:55
 * @Copyright voc
 */
@Slf4j
@Api(tags="a-警示规则查询")
@RestController
@RequestMapping("/sys/risk/rarningRules")
public class VocRiskWarningRulesController {
    @Autowired
    IVocRiskWarningRulesService rulesService;

    @AutoLog(value = "运营管理-预警配置")
    @ApiOperation(value = "警示规则查询")
    @RequestMapping(value = "/getWarningRules", method = RequestMethod.GET)
    public Result<?> getWarningRules(VocRiskWarningRules warningRules) {
        return rulesService.getWarningRules(warningRules);
    }



    @ApiOperation("警示规则更新接口")
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public Result<?> saveOrUpdate(@RequestBody VocRiskWarningRules warningRules) {
        return rulesService.saveOrUpdate1(warningRules);

    }

}
