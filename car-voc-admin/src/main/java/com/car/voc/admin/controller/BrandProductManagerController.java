package com.car.voc.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.entity.BrandProductManager;
import com.car.voc.entity.SysCategory;
import com.car.voc.entity.VocChannelCategory;
import com.car.voc.model.BrandProductManagerModel;
import com.car.voc.model.ChannelEditSortModel;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.service.IModelGroupRelationService;
import com.car.voc.service.ISysCategoryService;
import com.car.voc.vo.CarSeriesListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import wiremock.org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Api(tags="a-车型车系管理")
@RestController
@RequestMapping("/voc/brandProductManager")
@Slf4j
public class BrandProductManagerController {
   @Autowired
   private IBrandProductManagerService brandProductManagerService;

   @Autowired
   ISysCategoryService sysCategoryService;
   @Autowired
    RedisUtil redisUtil;

    /**
     * 选择关联品牌/车型/车系
     * @return
     */
    @GetMapping(value = "/queryCategoryList")
    @ApiOperation(value = "选择关联品牌",notes = "选择关联品牌")
   public Result<?> queryCategoryList(){
       Integer type=1;
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("flag",0);
       if(type == 1){
           queryWrapper.eq("pid",0);
           List<SysCategory> list = sysCategoryService.list(queryWrapper);
           return Result.OK(list);
       }
       return Result.error("请求参数异常");
   }

//    @AutoLog(value = "选择关联品牌/车系/车型 —— 非关联")
    @GetMapping(value = "/childList")
    @ApiOperation(value = "根据id或code查询子节点",notes = "根据id或code查询子节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code",value = "父节点code，不传为一级",dataType = "String")
    })
   public Result<?> childList(@RequestParam(name="name",required = false) String name,
                              @RequestParam(name = "pid",required = false)String pid,
                              @RequestParam(name = "code",required = false)String code){
        BrandProductManager brandProductManager = new BrandProductManager();
        brandProductManager.setDelFlag(0);
        if (StrUtil.isNotBlank(code)&&StrUtil.isBlankIfStr(pid)){
            QueryWrapper<BrandProductManager> wrapper=new QueryWrapper<>();
            wrapper.lambda().eq(BrandProductManager::getCode,code);
            wrapper.lambda().eq(BrandProductManager::getDelFlag,0);
            BrandProductManager  manager=brandProductManagerService.getOne(wrapper);
            brandProductManager.setPId(manager.getId());
        }else if (StrUtil.isNotBlank(pid)){
            brandProductManager.setPId(pid);
        }else {
            brandProductManager.setPId("0");
        }
       if(StrUtil.isNotBlank(name)){
           brandProductManager.setName(name);
       }
       Page<BrandProductManager> page = new Page<BrandProductManager>(0, 500);
        Page<BrandProductManager> list = brandProductManagerService.queryByPage(page,brandProductManager);
        return Result.OK(list.getRecords());
   }
    /**
     * 批量查询子节点
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @return 返回 IPage
     * @param parentIds
     * @return
     */
//    @AutoLog(value = "批量获取子数据")
    @ApiOperation(value="批量获取子数据", notes="批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {

            IPage<BrandProductManager> re=	 brandProductManagerService.getChildListBatch(parentIds);


            return Result.OK(re);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }
    /**
    * 查询 一级品牌查询
    *
    * @return
    */
//   @AutoLog(value = "一级品牌查询")
   @ApiOperation(value="一级品牌查询", notes="一级品牌查询")
   @GetMapping(value = "/rootList")
   public Result<?> queryPageList(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {

       QueryWrapper<BrandProductManager> query=new QueryWrapper<>();
       query.lambda().select(BrandProductManager::getId,BrandProductManager::getCode,BrandProductManager::getName);
       query.lambda().eq(BrandProductManager::getPId,0 );
       query.lambda().orderByAsc(BrandProductManager::getSortNo);
       Page<BrandProductManager> page = new Page<>(pageNo, pageSize);
       IPage<BrandProductManager> pageList = brandProductManagerService.page(page, query);
       IPage<CarSeriesListVo> pages=new Page<>();
       pages.setRecords(BeanUtil.copyToList(pageList.getRecords(),CarSeriesListVo.class));

       return Result.OK(pages);
   }



    @AutoLog(value = "运营管理-车系管理")
   @ApiOperation(value="车系列表查询", notes="车系列表查询")
   @GetMapping(value = "/list")
   public Result<?> list(BrandProductManager brandProductManager,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   @RequestParam(name="brandCode",required = false) String  brandCode,
                                  HttpServletRequest req) {

       if(brandProductManager!=null){
           brandProductManager.setPId("0");
       }
       if (StringUtils.isNotEmpty(brandCode)){
           brandProductManager.setBrandCode(brandCode);
       }
//       brandProductManager.setPId("1381890956320113569");
       brandProductManager.setDelFlag(0);

       Page<BrandProductManager> page = new Page<BrandProductManager>(pageNo, pageSize);

       IPage<BrandProductManager> pageList = brandProductManagerService.queryByPage(page, brandProductManager);
       return Result.OK(pageList);
   }


//   @AutoLog(value = "添加")
   @ApiOperation(value="添加", notes="添加")
   @PostMapping(value = "/add")
   public Result<?> add(@RequestBody BrandProductManagerModel brandProductManager) {
       if (brandProductManager.getType().equals(2)&&StrUtil.isBlankIfStr(brandProductManager.getpId())){
           return Result.error("添加车系时pId(品牌id)不能为空！");
       }

     return brandProductManagerService.add(brandProductManager);
   }

   /**
    *  编辑
    *
    * @param brandProductManager
    * @return
    */
//   @AutoLog(value = "品牌产品管理-编辑")
   @ApiOperation(value="品牌产品管理-编辑", notes="品牌产品管理-编辑")
   @PutMapping(value = "/edit")
   public Result<?> edit(@RequestBody BrandProductManagerModel brandProductManager) {
       brandProductManagerService.editBrandProduct(brandProductManager);
       return Result.OK("编辑成功!");
   }
    @Autowired
    IModelGroupRelationService relationService;
   /**
    * 删除
    *
    * @param id
    * @return
    */
//   @AutoLog(value = "删除 —— 根据ID")
   @ApiOperation(value="删除 —— 根据ID", notes="删除 —— 根据ID")
   @DeleteMapping(value = "/delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       UpdateWrapper updateWrapper = new UpdateWrapper();
       updateWrapper.eq("id",id);
       updateWrapper.set("del_flag",1);

       BrandProductManager car =brandProductManagerService.getById(id);

       Set<String> nCarCodes= (Set<String>) redisUtil.get(CacheConstant.sys_disable_car_code);
       if (nCarCodes==null){
           nCarCodes=new HashSet<>();
       }
       nCarCodes.remove(car.getCode());
       redisUtil.set(CacheConstant.sys_disable_car_code,nCarCodes);


     /*  QueryWrapper<ModelGroupRelation> relation =new QueryWrapper<>();
       relation.lambda().eq(ModelGroupRelation::getCarSeriesCode,car.getCode());
       relationService.remove(relation);
*/

       UpdateWrapper cardel = new UpdateWrapper();
       cardel.eq("p_id",id);
       cardel.set("del_flag",1);
       brandProductManagerService.update(updateWrapper);
       //删除对应车系
       brandProductManagerService.update(cardel);



       return Result.OK("删除成功!");
   }

   /**
    *  批量删除
    * @param ids
    * @return
    */
   @DeleteMapping(value = "/deleteBatch")
   public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       this.brandProductManagerService.removeByIds(Arrays.asList(ids.split(",")));

       return Result.OK("批量删除成功!");
   }


    @ApiOperation(value="车系管理-修改排序", notes="车系管理-修改排序")
    @PutMapping(value = "/editSort")
    public Result<?> editSort(@RequestBody BrandProductManagerModel model) {
        UpdateWrapper<BrandProductManager> up=new UpdateWrapper<>();
        up.lambda().eq(BrandProductManager::getId,model.getId()).set(BrandProductManager::getSortNo,model.getOrderBy());
        brandProductManagerService.update(up);
        return Result.OK("修改成功!");
    }


   /**
    * 通过id查询
    * @param id
    * @return
    */
//   @AutoLog(value = "查询 —— 根据ID")
   @ApiOperation(value="查询 —— 根据ID", notes="查询 —— 根据ID")
   @GetMapping(value = "/queryById")
   public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
       BrandProductManager brandProductManager = brandProductManagerService.getById(id);
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("id",brandProductManager.getRelationId());
       if(brandProductManager==null) {
           return Result.error("未找到对应数据");
       }
       return Result.OK(brandProductManager);
   }



}
