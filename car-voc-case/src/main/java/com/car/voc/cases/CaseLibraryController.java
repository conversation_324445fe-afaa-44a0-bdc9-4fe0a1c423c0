package com.car.voc.cases;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.car.stats.entity.CaseLibrary;
import com.car.stats.model.CaseLibraryModel;
import com.car.stats.serivce.ICaseLibraryService;
import com.car.stats.vo.CaseLibraryListVo;
import com.car.stats.vo.CaseReportVo;
import com.car.voc.common.Result;
import com.car.voc.common.aspect.annotation.AutoLog;
import com.car.voc.exception.BootException;
import com.car.voc.service.CommonService;
import com.car.voc.service.IBrandProductManagerService;
import com.car.voc.vo.SysCaseClassifyListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags="案例库")
@RestController
@RequestMapping("/stats/caseLibrary")
@Slf4j
public class CaseLibraryController {

	@Autowired
	private ICaseLibraryService caseLibraryService;

	@Autowired
	private IBrandProductManagerService brandProductManagerService;

	@AutoLog(value = "ToC案例库-案例列表")
	@ApiOperation(value = "案例库-全部案例", notes = "案例库-全部案例")
	@RequestMapping(value = "/allList", method = RequestMethod.GET)
	public Result<IPage<CaseLibraryListVo>> queryPageAllList(CaseLibraryModel caseLibraryModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
														  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
		try{
			return caseLibraryService.queryPageList(caseLibraryModel,pageNo,pageSize,req,"2");
		}catch (Exception e){
			log.error("案例库-我的分享查询异常：{}",e.getMessage());
			throw new BootException("案例库-我的分享查询异常"+e.getMessage());
		}
	}

	@AutoLog(value = "ToC案例库-我的案例")
	@ApiOperation(value = "案例库-我的分享", notes = "案例库-我的分享")
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public Result<IPage<CaseLibraryListVo>> queryPageList(CaseLibraryModel caseLibraryModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
														  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
		try{
			return caseLibraryService.queryPageList(caseLibraryModel,pageNo,pageSize,req,"1");
		}catch (Exception e){
			log.error("案例库-我的分享查询异常：{}",e.getMessage());
			throw new BootException("案例库-我的分享查询异常"+e.getMessage());
		}
	}

	@ApiOperation(value = "案例库-案例分类", notes = "案例库-案例分类")
	@RequestMapping(value = "/caseClassifylist", method = RequestMethod.GET)
	public Result<IPage<SysCaseClassifyListVo>> caseClassifyQueryPageList(CaseLibraryModel caseLibraryModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
														  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
		try{
			return caseLibraryService.caseClassifyQueryPageList(caseLibraryModel,pageNo,pageSize,req);
		}catch (Exception e){
			log.error("案例分类查询异常：{}",e.getMessage());
			throw new BootException("案例分类查询异常"+e.getMessage());
		}
	}

//	@AutoLog(value = "案例库统计查询")
	@ApiOperation(value = "案例库-统计查询", notes = "案例库-统计查询")
	@RequestMapping(value = "/reportList", method = RequestMethod.GET)
	public Result<CaseReportVo> queryReportList(CaseLibraryModel caseLibraryModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
												@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
		try{
			return caseLibraryService.queryReportList(caseLibraryModel,req);
		}catch (Exception e){
			log.error("案例库统计查询：{}",e.getMessage());
			throw new BootException("案例库统计查询"+e.getMessage());
		}
	}

	@ApiOperation("案例库新增接口")
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public Result<CaseLibrary> add(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			return caseLibraryService.add(caseLibraryModel,req);
		}catch (Exception e){
			log.error("案例库新增异常：{}",e.getMessage());
			throw new BootException("案例库新增异常"+e.getMessage());
		}
	}

	@ApiOperation("案例库编辑接口")
	@RequestMapping(value = "/edit", method = RequestMethod.PUT)
	public Result<CaseLibrary> edit(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			return caseLibraryService.edit(caseLibraryModel,req);
		}catch (Exception e){
			log.error("案例库编辑异常：{}",e.getMessage());
			throw new BootException("案例库编辑异常"+e.getMessage());
		}
	}

	@ApiOperation("案例库删除接口")
	@RequestMapping(value = "/delete", method = RequestMethod.DELETE)
	public Result<?> delete(@RequestParam(name = "id", required = true) String id, HttpServletRequest req) {
		try{
			String username = CommonService.getUserNameByToken(req);
			log.info("用户：{}案例库删除操作，id为:{}",username,id);
			Boolean caseLibraryBoolean = caseLibraryService.deleteCaseLibraryById(id,req);
			if(caseLibraryBoolean){
				return Result.OK("案例库删除成功");
			}else{
				return Result.error("案例库删除失败");
			}
		}catch (Exception e){
			log.error("案例库删除异常：{}",e.getMessage());
			throw new BootException("案例库删除异常"+e.getMessage());
		}
	}

	@ApiOperation("根据id查询案例库")
	@RequestMapping(value = "/queryById", method = RequestMethod.GET)
	public Result<CaseLibraryListVo> queryById(@RequestParam(name = "id", required = true) String id) {
		Result<CaseLibraryListVo> result = new Result<CaseLibraryListVo>();
		CaseLibraryListVo caseLibraryListVo = caseLibraryService.getCaseLibraryById(id);
		if (caseLibraryListVo == null) {
			result.error500("未找到案例库对应实体");
		} else {
			result.setResult(caseLibraryListVo);
			result.setSuccess(true);
		}
		return result;
	}

	@ApiOperation("案例库点赞查询接口")
	@RequestMapping(value = "/getUerLikes", method = RequestMethod.POST)
	public Result<?> getUserLikes(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			if(ObjectUtils.isEmpty(caseLibraryModel.getId())){
				throw new BootException("案例id不能为空");
			}
			Boolean uerLikes = caseLibraryService.getUserLikes(caseLibraryModel, req);
			return Result.OK(uerLikes);

		}catch (Exception e){
			log.error("案例点赞查询异常：{}",e.getMessage());
			throw new BootException("案例点赞查询异常"+e.getMessage());
		}
	}

	@ApiOperation("案例库点赞接口")
	@RequestMapping(value = "/likesAdd", method = RequestMethod.POST)
	public Result<?> likesAdd(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			if(ObjectUtils.isEmpty(caseLibraryModel.getId())){
				throw new BootException("案例id不能为空");
			}
			Boolean likeAdd = caseLibraryService.likesAdd(caseLibraryModel,req);
			if(likeAdd){
				return Result.OK("点赞成功");
			}else{
				return Result.error("点赞失败");
			}
		}catch (Exception e){
			log.error("案例点赞异常：{}",e.getMessage());
			throw new BootException("案例点赞异常"+e.getMessage());
		}
	}

	@ApiOperation("案例库取消点赞接口")
	@RequestMapping(value = "/cancelLikesAdd", method = RequestMethod.POST)
	public Result<?> cancelLikesAdd(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			if(ObjectUtils.isEmpty(caseLibraryModel.getId())){
				throw new BootException("案例id不能为空");
			}
			Boolean likeAdd = caseLibraryService.cancelLikesAdd(caseLibraryModel,req);
			if(likeAdd){
				return Result.OK("取消点赞成功");
			}else{
				return Result.error("取消点赞失败");
			}
		}catch (Exception e){
			log.error("案例点赞异常：{}",e.getMessage());
			throw new BootException("案例点赞异常"+e.getMessage());
		}
	}

	@ApiOperation("案例库浏览接口")
	@RequestMapping(value = "/browseAdd", method = RequestMethod.POST)
	public Result<?> browseAdd(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			if(ObjectUtils.isEmpty(caseLibraryModel.getId())){
				throw new BootException("案例id不能为空");
			}
			Boolean browseAdd = caseLibraryService.browseAdd(caseLibraryModel,req);
			if(browseAdd){
				return Result.OK("浏览量+1");
			}else{
				return Result.error("浏览量失败");
			}
		}catch (Exception e){
			log.error("案例浏览量异常：{}",e.getMessage());
			throw new BootException("案例浏览量异常"+e.getMessage());
		}
	}

	@ApiOperation("案例库下载接口")
	@RequestMapping(value = "/downloadAdd", method = RequestMethod.POST)
	public Result<?> downloadAdd(@RequestBody CaseLibraryModel caseLibraryModel, HttpServletRequest req) {
		try{
			if(ObjectUtils.isEmpty(caseLibraryModel.getId())){
				throw new BootException("案例id不能为空");
			}
			Boolean downloadAdd = caseLibraryService.downloadAdd(caseLibraryModel,req);
			if(downloadAdd){
				return Result.OK("下载量+1");
			}else{
				return Result.error("下载量失败");
			}
		}catch (Exception e){
			log.error("案例下载量异常：{}",e.getMessage());
			throw new BootException("案例下载量异常"+e.getMessage());
		}
	}

}
