package com.car.voc.util;

import com.car.voc.annotation.DataDesensitization;
import com.car.voc.serializer.DataDesensitizationSerializer;
import com.fasterxml.jackson.databind.introspect.Annotated;
import com.fasterxml.jackson.databind.introspect.NopAnnotationIntrospector;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据脱敏注解内省器
 * 用于在Jackson序列化过程中识别@DataDesensitization注解并应用相应的序列化器
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class DataDesensitizationIntrospector extends NopAnnotationIntrospector {
    
    @Override
    public Object findSerializer(Annotated am) {
        DataDesensitization annotation = am.getAnnotation(DataDesensitization.class);
        if (annotation != null) {
            log.debug("发现数据脱敏注解，字段: {}, 脱敏类型: {}", am.getName(), annotation.type());
            return DataDesensitizationSerializer.class;
        }
        return super.findSerializer(am);
    }
}
