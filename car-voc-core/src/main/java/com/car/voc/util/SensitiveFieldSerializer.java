package com.car.voc.util;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.car.voc.common.aspect.annotation.SensitiveField;
import com.car.voc.common.enums.SensitiveFieldTypeEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/1/3 15:58
 * @描述:
 **/
@Slf4j
public class SensitiveFieldSerializer extends JsonSerializer<Object> implements ContextualSerializer {

    private final ThreadLocal<SensitiveFieldTypeEnum> type = new ThreadLocal<>();

    @Override
    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        try {
            if (o instanceof List) {
                this.filtration(o);
                List<Object> list = (List) o;
                jsonGenerator.writeObject(o);
            } else if (o instanceof JSONObject ) {
                this.filtration(o);
                jsonGenerator.writeRawValue(String.valueOf(o));
            } else if (o instanceof Map) {
                this.filtration(o);
                jsonGenerator.writeObject(o);
            } else if (o instanceof String) {
                this.filtration(o);
                jsonGenerator.writeString(String.valueOf(o));
            } else if(o instanceof Boolean){
                jsonGenerator.writeObject(o);
            } else {
                this.filtration(o);
                jsonGenerator.writeObject(o);
            }
        } catch (Exception e) {
            log.error("序列化异常:", e);
        }
    }

    private void filtration(Object o) {
        if (o instanceof List) {
            List<Object> list = (List) o;
            list.stream().forEach(e -> {
                Class<?> aClass = e.getClass();
                List<Field> declaredFields = getDeclaredFields(aClass);
                declaredFields.stream()
                        .filter(k -> ObjectUtils.isNotEmpty(k.getAnnotation(SensitiveField.class)))
                        .forEach(k -> {
                            try {
                                k.setAccessible(true);
                                k.set(e, "");
                            } catch (IllegalAccessException ex) {
                            }
                        });
            });
        } else {
            Class<?> aClass = o.getClass();
            List<Field> declaredFields = getDeclaredFields(aClass);
            declaredFields.stream()
                    .filter(k -> ObjectUtils.isNotEmpty(k.getAnnotation(SensitiveField.class)))
                    .forEach(k -> {
                        try {
                            k.setAccessible(true);
                            k.set(o, "");
                        } catch (IllegalAccessException ex) {
                        }
                    });
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        try {
            if (beanProperty != null) {
                // 获取注解的参数
                SensitiveField annotation = beanProperty.getAnnotation(SensitiveField.class);
                if (ObjectUtils.isNotEmpty(annotation)) {
                    SensitiveFieldTypeEnum sensitiveFieldType = annotation.type();

                    // 将注解参数设置到序列化器的全局变量type里
                    type.set(sensitiveFieldType);
                    // 复用该序列器起对象
                }
                return this;
            }
        } catch (Exception e) {

        }
        return serializerProvider.findNullValueSerializer(beanProperty);
    }


    private List<Field> getDeclaredFields(Class<?> aClass) {
        List<Field> fieldList = new ArrayList<>();
        Field[] declaredFields1 = aClass.getDeclaredFields();
        fieldList.addAll(Arrays.asList(declaredFields1));
        List<Class<?>> classes = getAllSuperclass(aClass);
        if (classes != null) {
            for (Class<?> clazz : classes) {
                Field[] declaredFields = clazz.getDeclaredFields();
                fieldList.addAll(Arrays.asList(declaredFields));
            }
        }

        return fieldList;
    }

    private List<Class<?>> getAllSuperclass(Class<?> aClass) {
        List<Class<?>> allClasses = new ArrayList<>();
        Class<?> superclass;
        while ((superclass = aClass.getSuperclass()) != null) {
            allClasses.add(superclass);
            aClass = superclass;
        }
        return allClasses;
    }
}
