package com.car.voc.util;

import cn.hutool.core.util.StrUtil;
import com.car.voc.annotation.DataDesensitization;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据脱敏工具类
 * 提供各种类型的数据脱敏功能，特别是电话号码脱敏
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class DataMaskingUtil {

    /**
     * 中国大陆手机号正则表达式
     * 匹配11位手机号：1[3-9]xxxxxxxxx
     */
    private static final Pattern CHINA_MOBILE_PATTERN = Pattern.compile("1[3-9]\\d{9}");

    /**
     * vin码
     */
    private static final Pattern VIN_PATTERN = Pattern.compile("[A-HJ-NPR-Z0-9]{17}");

    /**
     * 国际手机号正则表达式
     * 匹配+国家代码+号码的格式
     */
    private static final Pattern INTERNATIONAL_MOBILE_PATTERN = Pattern.compile("\\+\\d{1,4}[\\s-]?\\d{6,14}");

    /**
     * 固定电话号码正则表达式
     * 匹配区号+号码的格式：0xx-xxxxxxxx 或 0xxxxxxxxxx
     */
    private static final Pattern LANDLINE_PATTERN = Pattern.compile("0\\d{2,3}[-]?\\d{7,8}");

    /**
     * 400/800客服电话正则表达式
     */
    private static final Pattern SERVICE_PHONE_PATTERN = Pattern.compile("[48]00[-]?\\d{3}[-]?\\d{4}");

    /**
     * 身份证号正则表达式
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("\\d{17}[\\dXx]");

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}");

    /**
     * 银行卡号正则表达式（13-19位数字）
     */
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("\\d{13,19}");

    /**
     * 对文本中的手机号进行脱敏处理
     * 支持中国大陆手机号、国际手机号、固定电话等格式
     *
     * @param text 原始文本
     * @return 脱敏后的文本
     */
    public static String maskPhoneNumbers(String text) {
        return maskPhoneNumbers(text, DataDesensitization.MaskFormat.STANDARD);
    }

    public static String applyDesensitization(DataDesensitization.DesensitizationType type, String value) {
        if (type == null) {
            return value;
        }

        switch (type) {
            case PHONE:
                return DataMaskingUtil.maskPhoneNumbers(value);
            case VIN:
                return DataMaskingUtil.maskVIN(value);
            case ID_CARD:
                return DataMaskingUtil.maskIdCard(value);
            case NAME:
                return DataMaskingUtil.maskName(value);
            case EMAIL:
                return DataMaskingUtil.maskEmail(value);
            case ADDRESS:
                return DataMaskingUtil.maskAddress(value);
            case BANK_CARD:
                return DataMaskingUtil.maskBankCard(value);
            default:
                return value;
        }
    }


    /**
     * 根据脱敏类型应用相应的脱敏规则
     */
    public static Map<String, Object> applyDesensitization(DataDesensitization.DesensitizationType type, Map<String, Object> value) {
        if (type == null) {
            return value;
        }
        Map<String, Object> desensitizedMap = new HashMap<>();
        value.forEach((key, val) -> {
            String desensitizedValue;
            if (val instanceof String) {
                desensitizedValue = applyDesensitization(type, (String) val);
                desensitizedMap.put(key, desensitizedValue);
            } else {
                desensitizedMap.put(key, val);
            }
        });
        return desensitizedMap;
    }

    /**
     * 对文本中的手机号进行脱敏处理（支持指定格式）
     *
     * @param text   原始文本
     * @param format 脱敏格式
     * @return 脱敏后的文本
     */
    public static String maskPhoneNumbers(String text, DataDesensitization.MaskFormat format) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        String result = text;

        try {
            // 处理中国大陆手机号
            result = maskChinaMobileNumbers(result, format);

            // 处理国际手机号
            result = maskInternationalNumbers(result, format);

            // 处理固定电话号码
//            result = maskLandlineNumbers(result, format);

            // 处理400/800客服电话
            result = maskServiceNumbers(result, format);

        } catch (Exception e) {
            log.error("手机号脱敏处理异常", e);
            return text; // 异常时返回原文本
        }

        return result;
    }

    /**
     * 对文本中的手机号进行脱敏处理（支持指定格式）
     *
     * @param text 原始文本
     * @return 脱敏后的文本
     */
    public static String maskVIN(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        String result = text;

        try {
            result = maskChinaVIN(result);
        } catch (Exception e) {
            log.error("VIN脱敏处理异常", e);
            return text; // 异常时返回原文本
        }

        return result;
    }

    /**
     * 脱敏中国大陆手机号
     * 格式：138****5678
     */
    private static String maskChinaMobileNumbers(String text) {
        return maskChinaMobileNumbers(text, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 脱敏中国大陆手机号（支持指定格式）
     */
    private static String maskChinaMobileNumbers(String text, DataDesensitization.MaskFormat format) {
        Matcher matcher = CHINA_MOBILE_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String phone = matcher.group();
            String masked = maskSinglePhone(phone, format);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private static String maskChinaVIN(String text) {
        Matcher matcher = VIN_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String vin = matcher.group();
            String masked = maskSingleVIN(vin);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 脱敏国际手机号
     * 格式：+86 138****5678
     */
    private static String maskInternationalNumbers(String text) {
        return maskInternationalNumbers(text, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 脱敏国际手机号（支持指定格式）
     */
    private static String maskInternationalNumbers(String text, DataDesensitization.MaskFormat format) {
        Matcher matcher = INTERNATIONAL_MOBILE_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String phone = matcher.group();
            String masked = maskInternationalPhone(phone, format);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 脱敏固定电话号码
     * 格式：010-****5678 或 0755****5678
     */
    private static String maskLandlineNumbers(String text) {
        return maskLandlineNumbers(text, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 脱敏固定电话号码（支持指定格式）
     */
    private static String maskLandlineNumbers(String text, DataDesensitization.MaskFormat format) {
        Matcher matcher = LANDLINE_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String phone = matcher.group();
            String masked = maskLandlinePhone(phone, format);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 脱敏400/800客服电话
     * 格式：400-***-5678
     */
    private static String maskServiceNumbers(String text) {
        return maskServiceNumbers(text, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 脱敏400/800客服电话（支持指定格式）
     */
    private static String maskServiceNumbers(String text, DataDesensitization.MaskFormat format) {
        Matcher matcher = SERVICE_PHONE_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String phone = matcher.group();
            String masked = maskServicePhone(phone, format);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 根据格式脱敏单个手机号
     */
    private static String maskSinglePhone(String phone, DataDesensitization.MaskFormat format) {
        if (phone.length() != 11) {
            return phone;
        }

        switch (format) {
            case STANDARD:
                return phone.substring(0, 3) + "****" + phone.substring(7);
            case FULL_HIDE:
                return repeat("*", phone.length());
            case KEEP_TWO:
                return phone.substring(0, 2) + "****" + phone.substring(phone.length() - 2);
            case KEEP_PREFIX:
                return phone.substring(0, 3) + repeat("*", phone.length() - 3);
            default:
                return phone.substring(0, 3) + "****" + phone.substring(7);
        }
    }

    private static String maskSingleVIN(String vin) {
        if (vin.length() != 17) {
            return vin;
        }
        return "***********" + vin.substring(11);
    }

    public static String repeat(String original, int count) {
        if (original == null) {
            return null;
        }
        if (count <= 0) {
            return "";
        }
        final StringBuilder builder = new StringBuilder(original.length() * count);
        for (int i = 0; i < count; i++) {
            builder.append(original);
        }
        return builder.toString();
    }

    /**
     * 处理国际手机号脱敏
     */
    private static String maskInternationalPhone(String phone) {
        return maskInternationalPhone(phone, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 处理国际手机号脱敏（支持指定格式）
     */
    private static String maskInternationalPhone(String phone, DataDesensitization.MaskFormat format) {
        // 移除空格和连字符进行处理
        String cleanPhone = phone.replaceAll("[\\s-]", "");
        if (cleanPhone.length() >= 8) {
            String countryCode = cleanPhone.substring(0, cleanPhone.length() - 7);
            String number = cleanPhone.substring(cleanPhone.length() - 7);
            String maskedNumber = maskSinglePhone(number, format);
            return countryCode + maskedNumber.substring(3); // 保留国家代码
        }
        return phone; // 格式不符合预期时返回原值
    }

    /**
     * 处理固定电话脱敏
     */
    private static String maskLandlinePhone(String phone) {
        return maskLandlinePhone(phone, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 处理固定电话脱敏（支持指定格式）
     */
    private static String maskLandlinePhone(String phone, DataDesensitization.MaskFormat format) {
        if (phone.contains("-")) {
            String[] parts = phone.split("-");
            if (parts.length == 2 && parts[1].length() >= 4) {
                String maskedNumber = applyMaskFormat(parts[1], format);
                return parts[0] + "-" + maskedNumber;
            }
        } else if (phone.length() >= 8) {
            // 区号通常是3-4位
            int areaCodeLength = phone.length() <= 10 ? 3 : 4;
            String areaCode = phone.substring(0, areaCodeLength);
            String number = phone.substring(areaCodeLength);
            if (number.length() >= 4) {
                String maskedNumber = applyMaskFormat(number, format);
                return areaCode + maskedNumber;
            }
        }
        return phone;
    }

    /**
     * 处理400/800客服电话脱敏
     */
    private static String maskServicePhone(String phone) {
        return maskServicePhone(phone, DataDesensitization.MaskFormat.STANDARD);
    }

    /**
     * 处理400/800客服电话脱敏（支持指定格式）
     */
    private static String maskServicePhone(String phone, DataDesensitization.MaskFormat format) {
        String cleanPhone = phone.replaceAll("-", "");
        if (cleanPhone.length() == 10) {
            String prefix = cleanPhone.substring(0, 3);
            String middle = cleanPhone.substring(3, 7);
            String suffix = cleanPhone.substring(7);

            switch (format) {
                case STANDARD:
                    return prefix + "-***-" + suffix;
                case FULL_HIDE:
                    return repeat("*", phone.length());
                case KEEP_TWO:
                    return prefix + "-**" + middle.substring(2) + "-" + suffix;
                case KEEP_PREFIX:
                    return prefix + "-" + repeat("*", 7);
                default:
                    return prefix + "-***-" + suffix;
            }
        }
        return phone;
    }

    /**
     * 通用的脱敏格式应用方法
     */
    private static String applyMaskFormat(String number, DataDesensitization.MaskFormat format) {
        if (number.length() < 4) {
            return number;
        }

        switch (format) {
            case STANDARD:
                return "****" + number.substring(4);
            case FULL_HIDE:
                return repeat("*", number.length());
            case KEEP_TWO:
                return "**" + number.substring(number.length() - 2);
            case KEEP_PREFIX:
                return repeat("*", number.length());
            default:
                return "****" + number.substring(4);
        }
    }

    /**
     * 身份证号脱敏
     * 格式：110101********1234
     */
    public static String maskIdCard(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        Matcher matcher = ID_CARD_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String idCard = matcher.group();
            String masked = idCard.substring(0, 6) + "********" + idCard.substring(14);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 姓名脱敏
     * 格式：张* 或 欧阳*
     */
    public static String maskName(String name) {
        if (StrUtil.isBlank(name) || name.length() <= 1) {
            return name;
        }

        if (name.length() == 2) {
            return name.charAt(0) + "*";
        } else {
            return name.charAt(0) + repeat("*", name.length());
        }
    }

    /**
     * 邮箱脱敏
     * 格式：abc***@example.com
     */
    public static String maskEmail(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        Matcher matcher = EMAIL_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String email = matcher.group();
            String masked = maskSingleEmail(email);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private static String maskSingleEmail(String email) {
        int atIndex = email.indexOf('@');
        if (atIndex > 3) {
            String username = email.substring(0, atIndex);
            String domain = email.substring(atIndex);
            return username.substring(0, 3) + "***" + domain;
        }
        return email;
    }

    /**
     * 银行卡号脱敏
     * 格式：6222********1234
     */
    public static String maskBankCard(String text) {
        if (StrUtil.isBlank(text)) {
            return text;
        }

        Matcher matcher = BANK_CARD_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String cardNumber = matcher.group();
            if (cardNumber.length() >= 8) {
                String masked = cardNumber.substring(0, 4) + "********" + cardNumber.substring(cardNumber.length() - 4);
                matcher.appendReplacement(sb, masked);
            } else {
                matcher.appendReplacement(sb, cardNumber);
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 地址脱敏
     * 格式：北京市***区
     */
    public static String maskAddress(String address) {
        if (StrUtil.isBlank(address) || address.length() <= 6) {
            return address;
        }

        // 保留前3个字符和后3个字符，中间用***替代
        if (address.length() <= 10) {
            return address.substring(0, 3) + "***" + address.substring(address.length() - 3);
        } else {
            return address.substring(0, 6) + "***" + address.substring(address.length() - 6);
        }
    }

    public static void main(String[] args) {
        DataDesensitization.DesensitizationType format = DataDesensitization.DesensitizationType.NAME;
        String s = DataMaskingUtil.applyDesensitization(format, "刘培站");
        System.out.println(s);
    }
}
