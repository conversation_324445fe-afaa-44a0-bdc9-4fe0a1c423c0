package com.car.voc.util;

import com.fasterxml.jackson.databind.introspect.Annotated;
import com.fasterxml.jackson.databind.introspect.NopAnnotationIntrospector;
import com.car.voc.common.aspect.annotation.SensitiveField;
import lombok.extern.slf4j.Slf4j;

/**
 * @创建者: fanrong
 * @创建时间: 2024/1/3 16:02
 * @描述:
 **/
@Slf4j
public class SensitiveFieldAnnotationIntrospector extends NopAnnotationIntrospector {

    @Override
    public Object findSerializer(Annotated am) {
        SensitiveField annotation = am.getAnnotation(SensitiveField.class);
        if (annotation != null) {
            log.debug("----当前序列化使用自定义字段序列化器----");
            return SensitiveFieldSerializer.class;
        }
        return super.findSerializer(am);
    }
}
