package com.car.voc.serializer;


import cn.hutool.json.JSONObject;
import com.car.voc.annotation.DataDesensitization;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.model.LoginUser;
import com.car.voc.util.DataMaskingUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;

import java.io.IOException;


/**
 * 数据脱敏序列化器
 * 用于在JSON序列化时自动对标记了@DataDesensitization注解的字段进行脱敏处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class DataDesensitizationSerializer extends JsonSerializer<Object> implements ContextualSerializer {

    private DataDesensitization.DesensitizationType[] type;
    private boolean enabled = true;
    private String customRule;

    public DataDesensitizationSerializer() {
        log.info("--->> init {}", this.getClass().getSimpleName());
    }


    public DataDesensitizationSerializer(DataDesensitization.DesensitizationType[] type, boolean enabled, String customRule) {
        this.type = type;
        this.enabled = enabled;
        this.customRule = customRule;
    }

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        RedisUtil redisUtil = (RedisUtil) SpringContextUtils.getBean("redisUtil");
        JSONObject json = (JSONObject) redisUtil.get(CacheConstant.SYS_USERS_CACHE_INFO + ":" + loginUser.getId());
        Boolean desensitization = json.getBool("desensitization");
        Boolean desensitizationVin = json.getBool("desensitizationVin");

        // 如果脱敏未启用，直接输出原值
        if (desensitization && desensitizationVin) {
            gen.writeObject(value);
            return;
        }
        try {
            if (value instanceof String) {
                String maskedValue = (String) value;
                for (DataDesensitization.DesensitizationType desensitizationType : type) {
                    if ((desensitizationType.equals(DataDesensitization.DesensitizationType.NAME) ||
                            desensitizationType.equals(DataDesensitization.DesensitizationType.PHONE))
                            && !desensitization) {
                        maskedValue = DataMaskingUtil.applyDesensitization(desensitizationType, maskedValue);
                    } else if (desensitizationType.equals(DataDesensitization.DesensitizationType.VIN) && !desensitizationVin) {
                        maskedValue = DataMaskingUtil.applyDesensitization(desensitizationType, maskedValue);
                    }
                }
                gen.writeString(maskedValue);
            } else {
                gen.writeObject(value);
            }
        } catch (Exception e) {
            log.error("数据脱敏处理异常，字段值: {}, 脱敏类型: {}", value, type, e);
            // 异常时输出原值，确保系统稳定性
            gen.writeObject(value);
        }
    }


    /**
     * 应用自定义脱敏规则
     */


    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        if (property != null) {
            DataDesensitization annotation = property.getAnnotation(DataDesensitization.class);
            if (annotation != null) {

                return new DataDesensitizationSerializer(
                        annotation.type(),
                        annotation.enabled(),
                        annotation.customRule()
                );
            }
        }
        return this;
    }
}
