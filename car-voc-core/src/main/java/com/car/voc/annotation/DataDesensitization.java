package com.car.voc.annotation;


import com.car.voc.serializer.DataDesensitizationSerializer;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.*;

/**
 * 数据脱敏注解
 * 用于标记需要进行数据脱敏处理的字段
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Documented
@JacksonAnnotationsInside
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE})
@JsonSerialize(using = DataDesensitizationSerializer.class)
public @interface DataDesensitization {

    /**
     * 脱敏类型
     */
    DesensitizationType[] type();

    /**
     * 是否启用脱敏（默认启用）
     */
    boolean enabled() default true;

    /**
     * 自定义脱敏规则（当type为CUSTOM时使用）
     */
    String customRule() default "";

    /**
     * 脱敏类型枚举
     */
    enum DesensitizationType {
        /**
         * 手机号脱敏：138****5678
         */
        PHONE("手机号脱敏"),
        /**
         * vin码脱敏：********006509
         */
        VIN("VIN码"),

        /**
         * 身份证号脱敏：110101********1234
         */
        ID_CARD("身份证号脱敏"),

        /**
         * 姓名脱敏：张*
         */
        NAME("姓名脱敏"),

        /**
         * 邮箱脱敏：abc***@example.com
         */
        EMAIL("邮箱脱敏"),

        /**
         * 地址脱敏：北京市***区
         */
        ADDRESS("地址脱敏"),

        /**
         * 银行卡号脱敏：6222********1234
         */
        BANK_CARD("银行卡号脱敏"),

        /**
         * 自定义脱敏规则
         */
        CUSTOM("自定义脱敏");

        private final String description;

        DesensitizationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    enum MaskFormat {
        /**
         * 标准格式：138****5678
         */
        STANDARD,

        /**
         * 完全隐藏：***********
         */
        FULL_HIDE,

        /**
         * 保留前后各2位：13****78
         */
        KEEP_TWO,

        /**
         * 保留前3位：138********
         */
        KEEP_PREFIX
    }
}
