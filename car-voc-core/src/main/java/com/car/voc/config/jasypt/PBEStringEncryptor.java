package com.car.voc.config.jasypt;

import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEByteEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

import java.security.Provider;
import java.security.Security;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomStringEncryptor
 * @Description TODO ckcui
 * @createTime 2023年12月29日 16:55
 * @Copyright voc
 */
@Slf4j
public class PBEStringEncryptor implements StringEncryptor {
//    private final Environment environment;

    static PooledPBEStringEncryptor encryptor;

    public PBEStringEncryptor() {
        log.info("--->> {}", this.getClass().getSimpleName());
        encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword("P@ssw0rd!@#123..");
        config.setAlgorithm(StandardPBEByteEncryptor.DEFAULT_ALGORITHM);
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName(null);
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
    }

    @Override
    public String encrypt(String message) {
        return message;
    }

    @Override
    public String decrypt(String encryptedMessage) {
//        return encryptedMessage;
        return encryptor.decrypt(encryptedMessage);
    }

    public static void main(String[] args) {
//        checkJCE();
        System.out.println();
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword("P@ssw0rd!@#123..");
        config.setAlgorithm(StandardPBEByteEncryptor.DEFAULT_ALGORITHM);
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName(null);
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);

        System.out.println("dev");
        System.out.println(encryptor.encrypt("autotest_voc312@2024."));
        System.out.println(encryptor.decrypt("mxnWNqH00HR4gGOiP3t8sg=="));
        System.out.println();
        System.out.println(encryptor.encrypt("7JeCgXqTuye8Gk4HFRSZZxVK"));
        System.out.println(encryptor.decrypt("YjF9gwSWybAib0VpyfJ6gDAmCItOEsmNxL5yTEWClXZp4D/VPrWP2w=="));
        System.out.println();
        System.out.println(encryptor.encrypt("521d1145027220ec1da08955f5cb0077"));
        System.out.println(encryptor.decrypt("zHtn7tPy7j2vM6uX+8jDWxIr3cU8oHb7sjxOQDC4CR1AvToewUGXmhtK3MdOPnUB"));


        System.out.println();
        System.out.println();
        System.out.println("prod");
        System.out.println(encryptor.encrypt("uBQut4Y2qMHLKkyVSF"));
        System.out.println(encryptor.decrypt("xpFBfI4VUqnsZr7JwUkYWVOVkGlHGnyi2r1iHQjf6Rg="));
        System.out.println(encryptor.encrypt("WHjycUmqs96Zc5gswY"));
        System.out.println(encryptor.decrypt("wJLB5htupSQ+iGQT5YUcAlBht95Bxv1RUaoOCyeVIZw="));
        System.out.println(encryptor.encrypt("521d1145027220ec1da08955f5cb0077"));
        System.out.println(encryptor.decrypt("H5h5mN/JPQZVWnCEoeXABE/eBBzTj8hakIebs+TzPLBffpyBS/zE6bEZKVAVlrR7"));
        System.out.println(encryptor.encrypt("xpMfvRnmFEn6D5kCzh"));
        System.out.println(encryptor.decrypt("EGXM8BBOmm70ABuUeDu2GznI9IKSKKvPpgmmHReQdEE="));
    }


    public static void checkJCE() {
        Provider[] providers =  Security.getProviders();
        for (Provider p : providers){
            System.out.println("提供者名称："+p.getName()+"版本号："+p.getVersion());
            System.out.println();
            System.out.println(p.getInfo());
        }

        System.out.println();
        System.out.println();
        System.out.println("支持的消息摘要名称");
        for (String s:Security.getAlgorithms("messageDigest")){
            System.out.println("算法名称:"+s);
        }

        System.out.println();
        System.out.println();
        System.out.println("支持生成公钥和私钥的方法");
        for (String s : Security.getAlgorithms("keypairGenerator")){
            System.out.println("name:"+s);
        }

    }
}
