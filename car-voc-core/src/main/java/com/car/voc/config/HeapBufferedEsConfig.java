package com.car.voc.config;

import org.apache.http.HttpResponse;
import org.apache.http.nio.protocol.HttpAsyncResponseConsumer;
import org.elasticsearch.client.HeapBufferedAsyncResponseConsumer;
import org.elasticsearch.client.HttpAsyncResponseConsumerFactory;
import org.elasticsearch.client.RequestOptions;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/25
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@Configuration
public class HeapBufferedEsConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //设置ElasticsearchRestTemplate查询的最大值为300MB 默认为100MB
        registry.addInterceptor(new HandlerInterceptor() {
            private boolean isSetBuffer = false;

            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                //已经设置过
                if (isSetBuffer) {
                    return true;
                }
                //设置es查询buffer大小
                RequestOptions requestOptions = RequestOptions.DEFAULT;
                Class<? extends RequestOptions> aClass = requestOptions.getClass();
                Field aDefault = aClass.getDeclaredField("httpAsyncResponseConsumerFactory");
                aDefault.setAccessible(true);
                //去除final
//                Field modifiersField = Field.class.getDeclaredField("modifiers");
//                modifiersField.setAccessible(true);
//                modifiersField.setInt(aDefault, aDefault.getModifiers() & ~Modifier.FINAL);

                //设置默认的工厂
                aDefault.set(requestOptions, new HttpAsyncResponseConsumerFactory() {
                    /**
                     * Creates the {@link HttpAsyncResponseConsumer}, called once per request attempt.
                     */
                   /* @Override
                    public HttpAsyncResponseConsumer<HttpResponse> createHttpAsyncResponseConsumer() {
                        //300MB
                        return new HeapBufferedAsyncResponseConsumer(3 * 100 * 1024 * 1024);
                    }*/
                    @Override
                    public HttpAsyncResponseConsumer<HttpResponse> createHttpAsyncResponseConsumer() {
                      return new HeapBufferedAsyncResponseConsumer(100 * 100 * 1024 * 1024);
                    }
                });
                //标记
                isSetBuffer = true;

                return true;
            }
        });
    }
}
