package com.car.voc.config.jasypt;

import com.ulisesbocchio.jasyptspringboot.EncryptablePropertyDetector;
import com.ulisesbocchio.jasyptspringboot.EncryptablePropertyResolver;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import com.ulisesbocchio.jasyptspringboot.configuration.EnvCopy;
import com.ulisesbocchio.jasyptspringboot.detector.DefaultPropertyDetector;
import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EncryptablePropertyConfig
 * @Description TODO ckcui
 * @createTime 2023年12月29日 10:41
 * @Copyright voc
 */

//@ConfigurationProperties(prefix = "jasypt.encryptor")
@Configuration
@EnableEncryptableProperties
@Slf4j
public class JasyptConfiguration {

    public JasyptConfiguration() {
        log.info("--->> {}", this.getClass().getSimpleName());
    }

    @Bean("encryptablePropertyDetector")
    public EncryptablePropertyDetector detector(EnvCopy envCopy) {

        final String prefix = envCopy.get().getProperty("jasypt.encryptor.prefix");
        final String suffix = envCopy.get().getProperty("jasypt.encryptor.suffix");

        log.info("prefix: '{}', suffix: '{}'", prefix, suffix);
        return new DefaultPropertyDetector(prefix, suffix);
    }

    @Bean("encryptablePropertyResolver")
    public EncryptablePropertyResolver EncryptablePropertyResolver(
            @Qualifier("encryptablePropertyDetector") EncryptablePropertyDetector detector,
            @Qualifier("jasyptStringEncryptor") StringEncryptor stringEncryptor
    ) {
        return new CustomEncryptablePropertyResolver(detector, stringEncryptor);
    }

    @ConditionalOnProperty(prefix = "jasypt.encryptor", name = "enabled", havingValue = "voc", matchIfMissing = false)
    @Bean("jasyptStringEncryptor")
    public StringEncryptor vocStringEncryptor() {
        return new VocStringEncryptor();
    }

    @ConditionalOnProperty(prefix = "jasypt.encryptor", name = "enabled", havingValue = "pbe", matchIfMissing = false)
    @Bean("jasyptStringEncryptor")
    public StringEncryptor pbeStringEncryptor() {
        return new PBEStringEncryptor();
    }

    /*@Bean("jasyptStringEncryptor")
    public StringEncryptor stringEncryptor() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword("P@ssw0rd!@#123..");
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor;
    }*/

}
