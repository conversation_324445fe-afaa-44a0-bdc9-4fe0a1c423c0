package com.car.voc.config;

import cn.hutool.core.thread.GlobalThreadPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.car.voc.common.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.*;

@Configuration
@EnableAsync(proxyTargetClass = true)
//@EnableAsync
@Slf4j
@EnableScheduling
public class AsyncConfig {
    public static final ExecutorService TEMP_POOL;
    private static final float MEMORY_BYTE;
    private static final float MEMORY_GB;
    private static final int PROCESSOR_COUNT;
    // 调用API使用的线程池，调用API时多数时间在等待，因此可以适当放大
    private static volatile Executor API_POOL;

    static {
        log.info("--->> 初始化全局线程池!");
        MEMORY_BYTE = Runtime.getRuntime().maxMemory();
        MEMORY_GB = MEMORY_BYTE / 1024f / 1024f / 1024f;
        PROCESSOR_COUNT = Runtime.getRuntime().availableProcessors();
        log.info("--->> 全局线程池初始化!核心数={}，可用内存{}G({}byte)", PROCESSOR_COUNT, MEMORY_GB, MEMORY_BYTE);

        TEMP_POOL = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(PROCESSOR_COUNT * 2));
    }


    @Value("${threadpool.async.executor.corePoolSize:160}")
    Integer corePoolSize;
    @Value("${threadpool.async.executor.maxPoolSize:2000}")
    Integer maxPoolSize;
    @Value("${threadpool.async.executor.queueCapacity:3200}")
    Integer queueCapacity;
    @Value("${threadpool.async.executor.threadNamePrefix:voc-}")
    String threadNamePrefix;


    @Bean
    @Primary
    public Executor executor() {
        if (API_POOL == null) {
            synchronized (GlobalThreadPool.class) {
                if (API_POOL == null) {
                    if (ObjectUtil.isNull(maxPoolSize)) {
                        int maxThreads = (int) (MEMORY_GB * 32);

                        ThreadPoolExecutor executor = new ThreadPoolExecutor(maxThreads, maxThreads,
                                60L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
//                        executor.setThreadFactory(r -> new Thread(RunnableWrapper.of(r)));

                        API_POOL = TtlExecutors.getTtlExecutorService(executor);
                        log.info("--->> 线程池初始化，coreSize:{} maxSize:{}", PROCESSOR_COUNT, maxThreads);
                    } else {
                        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

                        executor.setCorePoolSize(corePoolSize);
                        executor.setMaxPoolSize(maxPoolSize);
                        executor.setQueueCapacity(queueCapacity);
                        executor.setThreadNamePrefix(threadNamePrefix);
                        /**
                         * ThreadPoolExecutor.AbortPolicy 默认拒绝策略，丢弃任务并抛出RejectedExecutionException异常
                         * ThreadPoolExecutor.DiscardPolicy 直接丢弃任务，但不抛出异常
                         * ThreadPoolExecutor.DiscardOldestPolicy 丢弃任务队列最先加入的任务，再执行execute方法把新任务加入队列执行
                         * ThreadPoolExecutor.CallerRunsPolicy：由创建了线程池的线程来执行被拒绝的任务
                         */
                        // 设置策略
                        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
//                        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy()); // 设置策略
                        // 是否在任务执行完后关闭线程池
                        executor.setWaitForTasksToCompleteOnShutdown(false);
//                        executor.setThreadFactory(r -> new Thread(RunnableWrapper.of(r)));
                        executor.initialize();
                        log.info("--->> 线程池初始化");
                        log.info("corePoolSize: {}", corePoolSize);
                        log.info("maxPoolSize: {}", maxPoolSize);
                        log.info("queueCapacity: {}", queueCapacity);
                        log.info("threadNamePrefix: {}", threadNamePrefix);

                        API_POOL = TtlExecutors.getTtlExecutor(executor);
                    }
                }
            }
        }
        SpringContextUtils.setExecutor(API_POOL);
        return API_POOL;
    }

}
