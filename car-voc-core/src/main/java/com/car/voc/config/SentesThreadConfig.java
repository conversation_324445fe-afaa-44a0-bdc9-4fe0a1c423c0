package com.car.voc.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date ：Created in 2024/1/27
 * @description：${description}
 * @modified By：
 * @version: $version$
 */
@Configuration
public class SentesThreadConfig {
    @Value("${threadpool.async.executor.corePoolSize:160}")
    Integer corePoolSize;
    @Value("${threadpool.async.executor.maxPoolSize:2000}")
    Integer maxPoolSize;
    @Value("${threadpool.async.executor.queueCapacity:3200}")
    Integer queueCapacity;
    @Value("${threadpool.async.executor.threadNamePrefix:voc-sente}")
    String threadNamePrefix;


    @Bean("vocSentesExecutor")
    public Executor vocSentesExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setThreadFactory(r -> new Thread(RunnableWrapper.of(r)));
        return executor;
    }

}