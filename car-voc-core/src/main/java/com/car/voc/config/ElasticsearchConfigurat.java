package com.car.voc.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.elasticsearch.client.Node;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch配置类
 * 用于配置和创建ES客户端连接
 */
@Slf4j
@Configuration
public class ElasticsearchConfigurat {

    // ES连接配置参数
    @Value("${spring.elasticsearch.rest.uris}")
    private String uris;

    @Value("${spring.elasticsearch.rest.username}")
    private String username;

    @Value("${spring.elasticsearch.rest.password}")
    private String password;

    @Value("${spring.elasticsearch.rest.port}")
    private String port;

    @Value("${spring.elasticsearch.rest.is-ssl}")
    private boolean isSsl;

    // 连接池配置参数
    @Value("${spring.elasticsearch.rest.connection-timeout}")
    private int connectionTimeout;

    @Value("${spring.elasticsearch.rest.socket-timeout}")
    private int socketTimeout;

    @Value("${spring.elasticsearch.rest.max-conn-total}")
    private int maxConnTotal;

    @Value("${spring.elasticsearch.rest.max-conn-per-route}")
    private int maxConnPerRoute;

    @Value("${spring.elasticsearch.rest.keep-alive-minutes}")
    private int keepAliveMinutes;

    /**
     * 创建ES客户端Bean
     * 配置SSL、认证、连接池等参数
     */
    @Bean(destroyMethod = "close")
    public RestHighLevelClient createSimpleElasticClient() {
        try {
            // 验证必要参数
            validateParams();

            // 配置SSL上下文
            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, (x509Certificates, s) -> true)
                    .build();

            // 配置认证信息
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password));

            // 配置失败监听器
            RestClient.FailureListener failureListener = new RestClient.FailureListener() {
                @Override
                public void onFailure(Node node) {
                    log.error("ES node {} connection failed", node);
                }
            };

            // 创建ES客户端
            RestHighLevelClient client = new RestHighLevelClient(
                    RestClient.builder(new HttpHost(uris, Integer.parseInt(port), isSsl ? "https" : "http"))
                            .setHttpClientConfigCallback(httpClientBuilder -> configureHttpClient(
                                    httpClientBuilder, sslContext, credentialsProvider))
                            .setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                                    .setConnectTimeout(connectionTimeout)
                                    .setSocketTimeout(socketTimeout)
                                    .setConnectionRequestTimeout(300000)
                                    .setAuthenticationEnabled(true))
                            .setFailureListener(failureListener)
            );

            // 测试连接
//            testConnection(client);

            return client;
        } catch (Exception e) {
            log.error("Failed to create Elasticsearch client", e);
            throw new IllegalStateException("Could not create Elasticsearch client", e);
        }
    }

    /**
     * 配置HTTP客户端参数
     */
    private HttpAsyncClientBuilder configureHttpClient(
            HttpAsyncClientBuilder httpClientBuilder,
            SSLContext sslContext,
            CredentialsProvider credentialsProvider) {
        return httpClientBuilder
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .setDefaultCredentialsProvider(credentialsProvider)
                .setKeepAliveStrategy((response, context) ->
                        Duration.ofMinutes(keepAliveMinutes).toMillis())
                .setMaxConnTotal(maxConnTotal)
                .setMaxConnPerRoute(maxConnPerRoute);
    }

    /**
     * 验证配置参数
     */
    private void validateParams() {
        if (!StringUtils.hasText(uris)) {
            throw new IllegalArgumentException("Elasticsearch URI cannot be empty");
        }
        if (!StringUtils.hasText(port)) {
            throw new IllegalArgumentException("Elasticsearch port cannot be empty");
        }
        try {
            Integer.parseInt(port);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid port number", e);
        }
    }

    /**
     * 测试ES连接
     * 包括集群健康检查和索引状态检查
     */
    private void testConnection(RestHighLevelClient client) {
        try {
            checkClusterHealth(client);
            checkIndices(client);
        } catch (Exception e) {
            log.error("Elasticsearch health check failed", e);
            throw new IllegalStateException("Elasticsearch health check failed", e);
        }
    }

    /**
     * 检查ES集群健康状态
     */
    private void checkClusterHealth(RestHighLevelClient client) throws IOException {
        Request healthRequest = new Request("GET", "/_cluster/health");
        Response response = client.getLowLevelClient().performRequest(healthRequest);

        try (InputStream is = response.getEntity().getContent()) {
            Map<String, Object> healthMap = new ObjectMapper()
                    .readValue(is, new TypeReference<Map<String, Object>>() {});

            String status = (String) healthMap.get("status");
            int numberOfNodes = (Integer) healthMap.get("number_of_nodes");
            int activeShards = (Integer) healthMap.get("active_shards");

            log.info("Elasticsearch cluster status: {}", status);
            log.info("Number of nodes: {}", numberOfNodes);
            log.info("Active shards: {}", activeShards);

            if ("red".equals(status)) {
                log.error("Elasticsearch cluster is in RED status!");
                throw new IllegalStateException("Elasticsearch cluster is unhealthy");
            }
        }
    }

    /**
     * 检查ES索引状态
     */
    private void checkIndices(RestHighLevelClient client) throws IOException {
        Request indicesRequest = new Request("GET", "/_cat/indices?format=json");
        Response response = client.getLowLevelClient().performRequest(indicesRequest);

        try (InputStream is = response.getEntity().getContent()) {
            List<Map<String, Object>> indices = new ObjectMapper()
                    .readValue(is, new TypeReference<List<Map<String, Object>>>() {});

            log.info("Total number of indices: {}", indices.size());

            // 检查重要索引的状态
            indices.stream()
                    .filter(index -> isImportantIndex(index.get("index").toString()))
                    .forEach(index -> {
                        String indexName = index.get("index").toString();
                        String health = index.get("health").toString();
                        log.info("Index: {}, Health: {}", indexName, health);
                    });
        }
    }

    /**
     * 判断是否为重要索引
     */
    private boolean isImportantIndex(String indexName) {
        return indexName.startsWith("voc_") ||
                indexName.startsWith("important_");
    }
}