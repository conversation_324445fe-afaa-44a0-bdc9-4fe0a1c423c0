package com.car.voc.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * @version 1.0.0
 * @ClassName MybatisPlusConfig.java
 * @Description TODO
 * @createTime 2022年10月09日 10:58
 * @Copyright voc
 */
@Configuration
//@MapperScan(basePackages = {"com.car.*.mapper","com.car.voc.mapper", "com.car.stats.mapper", "com.car.voc.*.mapper", "com.car.report.mapper"})
@MapperScan(basePackages = {"com.car.*.mapper", "com.car.*.*.mapper"})
public class MybatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); //注意使用哪种数据库
        return interceptor;
    }
}
