package com.car.voc.config.jasypt;

import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.StringEncryptor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CustomStringEncryptor
 * @Description TODO ckcui
 * @createTime 2023年12月29日 16:55
 * @Copyright voc
 */
@Slf4j
public class VocStringEncryptor implements StringEncryptor {
//    private final Environment environment;

    public VocStringEncryptor() {
        log.info("--->> {}", this.getClass().getSimpleName());
    }
    @Override
    public String encrypt(String message) {
        return message;
    }

    @Override
    public String decrypt(String encryptedMessage) {

        return encryptedMessage;
    }
}
