package com.car.voc.component;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.*;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ElasticsearchClient {
    @Resource
    private RestHighLevelClient restHighLevelClient;

    private static final String ES_ID_SIGN="_id";

    public void add(String index, String type, Map<String, Object> record) {
        try {
            IndexRequest indexRequest = new IndexRequest(index, type, record.get(ES_ID_SIGN).toString()).source(record);
            IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
            log.info(indexResponse.toString());
        } catch (Exception e) {
            log.error("es add record error {}",e);
            e.printStackTrace();
        }
    }

    public void addBatch(String index, String type, List<Map<String, Object>> records) {
        try {
            BulkRequest bulkRequest=new BulkRequest();
            IndexRequest indexRequest = null;
            for(Map<String, Object> record:records){
                indexRequest=new IndexRequest("post");
                indexRequest.index(index).type(type).id(record.get(ES_ID_SIGN).toString()).source(record);
                bulkRequest.add(indexRequest);
            }
            BulkResponse response = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            log.info(response.toString());
        } catch (Exception e) {
            log.error("es add record error {}",e);
            e.printStackTrace();
        }
    }

    public SearchResponse searchWithQueryBuilder(String index, String type, QueryBuilder queryBuilder) throws IOException {
        log.info("client is {}",restHighLevelClient);
        SearchRequest searchRequest = new SearchRequest().indices(index).types(type);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(queryBuilder).size(100);
        SearchResponse searchResponse = this.restHighLevelClient.search(searchRequest.source(sourceBuilder), RequestOptions.DEFAULT);
        return searchResponse;
    }

    public SearchResponse searchWithAggsBuilder(String index, String type, AggregationBuilder aggregationBuilder) throws IOException {
        log.info("client is {}",restHighLevelClient);
        SearchRequest searchRequest = new SearchRequest().indices(index).types(type);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.aggregation(aggregationBuilder);
        SearchResponse searchResponse = this.restHighLevelClient.search(searchRequest.source(sourceBuilder), RequestOptions.DEFAULT);
        return searchResponse;
    }



    public SearchResponse scrollInit(String index, QueryBuilder queryBuilder, int scrollSeconds, int scrollSize) throws IOException {
        log.info("client is {}",restHighLevelClient);
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.scroll(new Scroll(TimeValue.timeValueSeconds(scrollSeconds)));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(queryBuilder).size(scrollSize);
        SearchResponse searchResponse = this.restHighLevelClient.search(searchRequest.source(sourceBuilder), RequestOptions.DEFAULT);
        return searchResponse;
    }

    public SearchResponse scroll(String scrollId, int scrollSeconds) throws IOException {
        log.info("client is {}",restHighLevelClient);
        SearchScrollRequest request = new SearchScrollRequest(scrollId);
        request.scroll(new Scroll(TimeValue.timeValueSeconds(scrollSeconds)));
        SearchResponse searchResponse = this.restHighLevelClient.scroll(request, RequestOptions.DEFAULT);
        return searchResponse;
    }
    public boolean clearScroll(String scrollId)  {
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        boolean result=false;
        try {
            ClearScrollResponse clearScrollResponse = restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            result=clearScrollResponse.isSucceeded();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;

    }

    public void update(String index, String type, Map<String, Object> record) {
        try {
            UpdateRequest request = new UpdateRequest(index,type, record.get("id").toString()).doc(record);
            UpdateResponse updateResponse = restHighLevelClient.update(request, RequestOptions.DEFAULT);
            System.out.println(updateResponse.toString());
        } catch (Exception e) {
            log.error("es update record error {}",e);
            e.printStackTrace();
        }
    }

    public void delete(String index, String type, String id) {
        try {
            DeleteRequest request = new DeleteRequest(index,type,id);
            DeleteResponse deleteResponse = this.restHighLevelClient.delete(request, RequestOptions.DEFAULT);
            System.out.println(deleteResponse.toString());
        } catch (Exception e) {
            log.error("es delete record error {}",e);
            e.printStackTrace();
        }
    }
}
