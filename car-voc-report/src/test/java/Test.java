//import cn.hutool.crypto.digest.MD5;
//import com.car.report.config.WebDriverConfig;
//import com.car.report.entity.ReportEntity;
//import com.car.report.listener.ImageGenerateListener;
//import com.car.report.model.ReportFilterModel;
//import com.car.report.model.ReportModel;
//import com.car.report.service.api.IReportService;
//import com.car.report.service.converts.ReportConverMapperServiceImpl;
//import com.car.report.service.impl.FileCompressorService;
//import com.car.report.service.impl.GwmReportServieImpl;
//import com.car.report.service.impl.PptServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.ApplicationContext;
//import org.springframework.test.context.ContextConfiguration;
//
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Arrays;
//
///**
// * <AUTHOR>
// * @version 1.0.0
// * @ClassName Test
// * @Description TODO ckcui
// * @createTime 2023年12月06日 15:06
// * @Copyright voc
// */
//@Slf4j
//@ContextConfiguration
//@SpringBootTest(classes = {GwmReportServieImpl.class,
//        FileCompressorService.class,
//        WebDriverConfig.class,
//        ReportConverMapperServiceImpl.class,
//        ImageGenerateListener.class,
//        PptServiceImpl.class})
//public class Test {
//    @Autowired
//    ApplicationContext context;
//    @Autowired
//    IReportService reportService;
//
//    @org.junit.jupiter.api.Test
//    public void test_generate() {
//        ReportFilterModel filter = new ReportFilterModel();
//        //后端可以根据需求，生成默认报告名称
////        filter.setReportName("报告_2023");
//
//        reportService.setImgCodes(Arrays.asList("Q01","G01"));
//        /*reportService.setRequestUrl(Arrays.asList(
//                "http://localhost:80/Q2?q=sdfefewfw="
//                ,"http://localhost:80/Q3?q=sdfefewfw="
//                ,"http://localhost:80/Q4?q=sdfefewfw="
//                ,"http://localhost:80/Q4?q=sdfefewfw="
//        ););*/
//     //   reportService.generate(filter,IReportService.REPORT_FILE_TYPE_PPT);
//    }
//
//    @org.junit.jupiter.api.Test
//    public void test_1() {
//        ReportEntity entity = ReportEntity.builder()
//                .userId("abc")
//                .name("报告")
//                .path("http://")
//                .fileType("ppt")
//                .createTime(LocalDateTime.now())
//                .updateTime(LocalDateTime.now())
//                .status("1")
//                .timeFrameS(LocalDateTime.now())
//                .timeFrameE(LocalDateTime.now())
//                .channels("123,321")
//                .carModel("aaa")
//                .carSeries("ddd,fff")
//                .region("")
//                .province("ffff,ssss")
//                .firstDimensionCode("G11")
//                .secondDimensionCodes("G11,G222")
//                .dataSources("private")
//                .emotion("正面")
//                .intention("抱怨")
//                .build();
//
//        ReportModel model = converTo(entity);
//        System.out.println(model);
//    }
//
//    private ReportModel converTo(ReportEntity e){
//
//        return ReportModel.builder()
//                .id(e.getId())
//                .name(e.getName())
//                .fileType(e.getFileType())
//                .createTime(e.getUpdateTime().format(DateTimeFormatter.ISO_LOCAL_DATE)
//                        .concat(" ").concat(e.getUpdateTime().format(DateTimeFormatter.ISO_LOCAL_TIME)))
//                .status(e.getStatus())
//                .timeFrame(e.getTimeFrameS().format(DateTimeFormatter.ISO_LOCAL_DATE)
//                        .concat("至").concat(e.getTimeFrameS().format(DateTimeFormatter.ISO_LOCAL_DATE)))
//                .channels("")
//                .build();
//    }
//
//
//
//}
