package com.car.report.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 舆情数据(TtPublicOpinion)请求返回实体类
 *
 * <AUTHOR>
 * @since 2024-12-18 17:20:54
 */
@Data
@Builder
@Accessors(chain = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TtPublicOpinion", description = "舆情数据")
public class PublicOpinionModel {
    private String id;

    @ApiModelProperty(value = "")
    private String sid;
    /**
     * 媒体
     */
    @ApiModelProperty(value = "媒体")
    private String media;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 媒体类型
     */
    @ApiModelProperty(value = "媒体类型")
    private String mediaType;
    /**
     * 媒体板块
     */
    @ApiModelProperty(value = "媒体板块")
    private String channel;
    /**
     * 数据源链接
     */
    @ApiModelProperty(value = "数据源链接")
    private Map uri;
    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String imgs;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userScreenName;
    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源")
    private String userSrc;
    /**
     * 是否有粉丝
     */
    @ApiModelProperty(value = "是否有粉丝")
    private String userHasFollowers;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userIdStr;
    /**
     * 阅读数
     */
    @ApiModelProperty(value = "阅读数")
    private String readNum;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String titleTokens;
    @ApiModelProperty(value = "")
    private String imgsCount;
    @ApiModelProperty(value = "")
    private String engagementNum;
    /**
     * 评论量
     */
    @ApiModelProperty(value = "评论量")
    private String commentNum;
    @ApiModelProperty(value = "")
    private String kgInfosNum;
    /**
     * 全文
     */
    @ApiModelProperty(value = "全文")
    private String body;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 倾向
     */
    @ApiModelProperty(value = "倾向")
    private String tendency;
    @ApiModelProperty(value = "")
    private String keywordsInTitle;
    @ApiModelProperty(value = "")
    private Map keywordsDict;
    @ApiModelProperty(value = "")
    private Map topicPaths;
    @ApiModelProperty(value = "")
    private Map tagPaths;
    @ApiModelProperty(value = "")
    private Map tag1Paths;
    @ApiModelProperty(value = "")
    private Map tag2Paths;
    @ApiModelProperty(value = "")
    private Map tag3Paths;
    @ApiModelProperty(value = "")
    private Map productPaths;
    @ApiModelProperty(value = "")
    private Map brandPaths;
    @ApiModelProperty(value = "")
    private Map companyPaths;
    @ApiModelProperty(value = "")
    private Map peoplePaths;
    @ApiModelProperty(value = "")
    private Map regionPaths;
    @ApiModelProperty(value = "")
    private String remark;
    /**
     * created
     */
    @ApiModelProperty(value = "created")
    private String created;
    /**
     * 同步时间
     */
    @ApiModelProperty(value = "同步时间")
    private LocalDateTime syncTime;

    @ApiModelProperty(value = "")
    private Integer pageNo;
    @ApiModelProperty(value = "")
    private Integer pageSize;
}

