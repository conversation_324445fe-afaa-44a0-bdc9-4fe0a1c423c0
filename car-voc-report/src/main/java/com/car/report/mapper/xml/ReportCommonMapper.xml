<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="commonFilter">

	<!--按月统计不计算环比-->
    <sql id="dateMonthFilterCommon">
		WITH common_date_tools AS (
			WITH date_range AS (
				SELECT DATE_SUB( DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 29 DAY) AS startDate, DATE_SUB(now(), INTERVAL 1 DAY) AS endDate
					 , DATEDIFF(DATE_SUB(now(), INTERVAL 1 DAY),DATE_SUB(DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 29 DAY))+1  AS dateNum,0 as ic
				UNION ALL
				SELECT DATE_SUB(DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 59 DAY) AS startDate
					 , DATE_SUB( DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 30 DAY) AS endDate
					 , DATEDIFF(DATE_SUB( DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 30 DAY),DATE_SUB(DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 59 DAY))+1 AS dateNum,1 as ic
			)
			SELECT DATE_FORMAT(endDate, '%Y%m%d') AS date_, endDate AS endDate_, dateNum
				 , -1 AS date_unit, startDate, endDate, 1 AS days,ic
			FROM date_range  where ic=0
		)
    </sql>

	<!--按月统计计算环比-->
	<sql id="dateMonthRateFilterCommon">
		WITH common_date_tools AS (
			WITH date_range AS (
				SELECT DATE_SUB( DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 29 DAY) AS startDate, DATE_SUB(now(), INTERVAL 1 DAY) AS endDate
					 , DATEDIFF(DATE_SUB(now(), INTERVAL 1 DAY),DATE_SUB(DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 29 DAY))+1  AS dateNum,0 as ic
				UNION ALL
				SELECT DATE_SUB(DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 59 DAY) AS startDate
					 , DATE_SUB( DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 30 DAY) AS endDate
					 , DATEDIFF(DATE_SUB( DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 30 DAY),DATE_SUB(DATE_SUB(now(), INTERVAL 1 DAY), INTERVAL 59 DAY))+1 AS dateNum,1 as ic
			)
			SELECT DATE_FORMAT(endDate, '%Y%m%d') AS date_, endDate AS endDate_, dateNum
				 , -1 AS date_unit, startDate, endDate, 1 AS days,ic
			FROM date_range
		)
	</sql>

	<!--前端入参时间自定义查询移动端计算环比-->
	<sql id="dateCustomizeFilterCommon">
		WITH common_date_tools AS (
			WITH date_range AS (
				SELECT DATE(#{endDate}) AS endDate, DATE(#{startDate}) AS startDate
				, DATEDIFF(DATE(#{endDate}), DATE(#{startDate})) + 1 AS dateNum,0 as ic
		UNION ALL
		SELECT DATE_SUB(DATE(#{startDate}), INTERVAL 1 DAY) AS endDate
			 , DATE_SUB(DATE(#{startDate}), INTERVAL DATEDIFF(DATE(#{endDate}), DATE(#{startDate})) + 1 DAY) AS startDate
			 , DATEDIFF(DATE(#{endDate}), DATE(#{startDate})) + 1 AS dateNum ,1 as ic
			)
		SELECT DATE_FORMAT(endDate, '%Y%m%d') AS date_, endDate AS endDate_, dateNum
			 , -1 AS date_unit, startDate, endDate, 1 AS days,ic
		FROM date_range
			)
	</sql>


	<!--前端入参时间自定义查询移动端不计算环比-->
	<sql id="dateCustomizeRateFilterCommon">
		WITH common_date_tools AS (
			WITH date_range AS (
				SELECT DATE(#{endDate}) AS endDate, DATE(#{startDate}) AS startDate
				, DATEDIFF(DATE(#{endDate}), DATE(#{startDate})) + 1 AS dateNum,0 as ic
		UNION ALL
		SELECT DATE_SUB(DATE(#{startDate}), INTERVAL 1 DAY) AS endDate
			 , DATE_SUB(DATE(#{startDate}), INTERVAL DATEDIFF(DATE(#{endDate}), DATE(#{startDate})) + 1 DAY) AS startDate
			 , DATEDIFF(DATE(#{endDate}), DATE(#{startDate})) + 1 AS dateNum ,1 as ic
			)
		SELECT DATE_FORMAT(endDate, '%Y%m%d') AS date_, endDate AS endDate_, dateNum
			 , -1 AS date_unit, startDate, endDate, 1 AS days,ic
		FROM date_range where ic=0
			)
	</sql>

	<!--按周查询-->
	<sql id="dateWeekFilterCommon">
		WITH `common_date_tools` (`date_`,
								  `endDate_`,
								  `dateNum`,
								  `date_unit`,
								  `startDate`,
								  `endDate`,
								  `days`) AS (WITH `date_range` (`startDate`,
																 `endDate`,
																 `dateNum`) AS (
			SELECT
				date_sub(date_sub(now(), INTERVAL 1 DAY), INTERVAL 6 DAY) AS `startDate`,
				date_sub(now(), INTERVAL 1 DAY) AS `endDate`,
				(datediff(date_sub(now(), INTERVAL 1 DAY), date_sub(date_sub(now(), INTERVAL 1 DAY), INTERVAL 6 DAY))) + 1 AS `dateNum`)
				  SELECT
					  date_format(`date_range`.`endDate`, '%Y%m%d') AS `date_`,
					  `date_range`.`endDate` AS `endDate_`,
					  `date_range`.`dateNum`,
					  -1 AS `date_unit`,
					  `date_range`.`startDate`,
					  `date_range`.`endDate`,
					  1 AS `days`
				  FROM
					  `date_range`
				)
	</sql>


	<!--按年查询-->
	<sql id="dateYearFilterCommon">
		WITH `common_date_tools` (`date_`,
								  `endDate_`,
								  `dateNum`,
								  `date_unit`,
								  `startDate`,
								  `endDate`,
								  `days`) AS (WITH `date_range` (`startDate`,
																 `endDate`,
																 `dateNum`) AS (
			SELECT
				makedate(year(curdate()), 1) AS `startDate`,
				date_sub(now(), INTERVAL 1 DAY) AS `endDate`,
				(datediff(date_sub(now(), INTERVAL 1 DAY), makedate(year(curdate()), 1))) + 1 AS `dateNum`)
				  SELECT
					  date_format(`date_range`.`endDate`, '%Y%m%d') AS `date_`,
					  `date_range`.`endDate` AS `endDate_`,
					  `date_range`.`dateNum`,
					  -1 AS `date_unit`,
					  `date_range`.`startDate`,
					  `date_range`.`endDate`,
					  1 AS `days`
				  FROM
					  `date_range`
				  )
	</sql>

</mapper>