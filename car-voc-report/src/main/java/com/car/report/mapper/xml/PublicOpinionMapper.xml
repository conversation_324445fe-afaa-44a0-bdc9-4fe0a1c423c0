<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.report.mapper.PublicOpinionMapper">
    <select id="queryBySelectPage" resultType="com.car.report.vo.PublicOpinionVo">
        WITH work_opinion AS (SELECT vid.wo_num,-- 工单编号
                                     vid.CODE,-- 案例编号
                                     vid.STATUS                                   statusCode,-- 工单状态编号
                                     CASE
                                         WHEN vid.STATUS >= 3 THEN
                                             '关闭'
                                         ELSE '未关闭'
                                         END        AS                            STATUS,-- 工单状态
                                     vid.channel_source_2,-- 舆情分类
                                     vid.channel_source_3,-- 舆情分类
                                     vid.wo_type,-- 工单类型
                                     vid.wo_content,-- 工单内容
                                     vid.brand,-- 品牌
                                     vid.model_name,-- 车型
                                     vid.car_series,-- 车系
                                     opinion.media_type,-- 媒体类型
                                     opinion.media,-- 媒体名称
                                     vid.author,-- 作者
                                     opinion.title,-- 标题
                                     opinion.uri,-- 链接
                                     opinion.file_time,-- 舆情推送时间
                                     vid.first_followup_time,-- 首次回访时间
                                     vid.resolution_status,-- 解决情况
                                     vid.resolution_method,-- 解决方式
                                     vid.customer_satisfaction,-- 满意度信息-客户是否满意
                                     vid.dissatisfaction_category,-- 满意度信息-不满意原因分类
                                    vid.is_followup_customer,
                                    vid.is_one_time_resolution,
                                    vid.reason,
                                     vid.step_name  AS                            handlingMethod,
                                     opinion.remark AS                            customerName,
                                     vid.dlr_first_feedback_time,-- 初步反馈时间
                                     vid.close_time,-- 闭环时间
                                     vid.tag2_paths AS                            rating,-- 舆情等级
                                     vid.create_time,
                                     IF
                                     (opinion.id = vid.source_no, STR_TO_DATE(opinion.created, '%Y/%m/%d %H:%i:%s'),
                                      vid.create_time)                            publisDate,
                                     IF
                                     (opinion.id = vid.source_no, '推送', '人工') sign
                              FROM t317_csv_rescue_wo_i_d vid
                                       INNER JOIN tt_public_opinion opinion ON opinion.id = vid.source_no
                              WHERE vid.channel_source_2 = '舆情')
        select *
        from (SELECT *,
                     IF
                     (create_time IS NOT NULL AND publisDate IS NOT NULL,
                      TIMESTAMPDIFF(SECOND, publisDate, create_time),
                      NULL)                                                             AS warningDuration,-- 预警时长
                     SUM(IF
                         (vid.dlr_first_feedback_time IS NOT NULL, 1, 0)) OVER () /
                     count(1) OVER ()                                                   AS responseRate, -- 计算响应率：计算该工单是否有反馈
                     SUM(IF
                         (vid.close_time IS NOT NULL, 1, 0)) OVER () / count(1) OVER () AS closureRate,-- 完结率
                     AVG(IF(vid.dlr_first_feedback_time IS NOT NULL AND create_time IS NOT NULL,
                            TIMESTAMPDIFF(SECOND, create_time, vid.dlr_first_feedback_time), NULL))
                         OVER ()                                                        AS averageResponseTime -- 平均响应时间
              FROM work_opinion vid
              WHERE 1 = 1
                <if test="model.startDate !=null and model.startDate != '' and model.endDate !=null and model.endDate != ''">
                    and vid.publisDate between DATE_FORMAT(#{model.startDate}, '%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{model.endDate}, '%Y-%m-%d %H:%i:%s')
                </if>
                <if test="model.brandNames != null and model.brandNames.size()>0">
                    and vid.brand in
                    <foreach item="item" collection="model.brandNames" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.carSeriesNames != null and model.carSeriesNames.size()>0">
                    and vid.car_series in
                    <foreach item="item" collection="model.carSeriesNames" separator="," open="(" close=")" index="">
                        #{item}
                    </foreach>
                </if>
                <if test="model.status !=null and model.status !=''">
                    <if test="model.status == 'closed'">
                        AND vid.statusCode <![CDATA[ >= ]]> 3
                    </if>
                    <if test="model.status == 'notClosed'">
                        AND vid.statusCode <![CDATA[ < ]]> 3
                    </if>
                </if>
                <if test="model.publicOpinionGrade != null and model.publicOpinionGrade != ''">
                    and vid.rating = #{model.publicOpinionGrade}
                </if>
                <if test="model.publicOpinionType != null and model.publicOpinionType != ''">
                    and vid.channel_source_3 = #{model.publicOpinionType}
                </if>
              order by vid.publisDate desc) as vid
    </select>
</mapper>

