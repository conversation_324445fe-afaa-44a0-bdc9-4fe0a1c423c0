package com.car.report.mapper;

import com.car.report.vo.*;
import com.car.stats.model.LargeScreenDataModel;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface LargeScreenDataMapper {

    List<BrandListVo> getBrandList(LargeScreenDataModel model);

    List<WorkOrderTrendVo> workOrderTrend(LargeScreenDataModel model);

    List<WorkOrderTypeVo> workOrderTypeDistribution(LargeScreenDataModel model);

    List<WorkOrderStatusVo> workOrderStatusDistribution(LargeScreenDataModel model);

    List<SalesComplaintTopVo> salesComplaintTop(LargeScreenDataModel model);

    List<AfterSalesComplaintTopVo> afterSalesComplaintTop(LargeScreenDataModel model);

    List<ProductQualityComplaintTopVo> productQualityComplaintTop(LargeScreenDataModel model);

    List<ConsultTopVo> consultTop(LargeScreenDataModel model);

    List<PublicSentimentTopVo> publicSentimentTop(LargeScreenDataModel model);

    List<ChannelDistributionVo> channelDistribution(LargeScreenDataModel model);

    List<ChannelTrendVo> channelTrend(LargeScreenDataModel model);

    AffectiveDistributionVo affectiveDistribution(LargeScreenDataModel model);

    List<FocusAttentionVo> focusAttention(LargeScreenDataModel model);

    List<FocusSeriesVo> focusSeries(LargeScreenDataModel model);

    List<ConsultingMentionTopVo> consultingMentionTop(LargeScreenDataModel model);

    List<ConsultingMentionTopVo> consultingSurgeTop(LargeScreenDataModel model);

    List<LabelClassificationTopVo> labelClassificationTop(LargeScreenDataModel model);

    List<UserFavorableCommentVo> userFavorableComment(LargeScreenDataModel model);

    List<UserBadCommentVo> userBadComment(LargeScreenDataModel model);

    String regionLatitudeAndLongitude();

    List<MapTopDataInfoVo> getMapTopData(LargeScreenDataModel model);

    TotalClosureRateVo workOrderTotalAndClosureRate(LargeScreenDataModel model);
}

