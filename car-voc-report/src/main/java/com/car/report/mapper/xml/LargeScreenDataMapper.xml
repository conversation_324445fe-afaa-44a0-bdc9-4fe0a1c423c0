<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.car.report.mapper.LargeScreenDataMapper">


    <select id="getBrandList" resultType="com.car.report.vo.BrandListVo">
        WITH `common_brand_tool` (`brand_code`,
                                  `brand_name`,
                                  `sort`) AS (
            SELECT
                'allBrand' AS `brand_code`,
                '东风乘用车' AS `brand_name`,
                1 AS `sort`
            UNION ALL
            SELECT
                `voc_brand_product_manager_mysql`.`code` AS `brand_code`,
                `voc_brand_product_manager_mysql`.`name` AS `brand_name`,
                2 AS `sort`
            FROM
                `voc_brand_product_manager_mysql`
            WHERE
                `voc_brand_product_manager_mysql`.`code` IN ('A12', 'A13', 'A11')
            ORDER BY
                `brand_name` DESC )
        SELECT
            `common_brand_tool`.`brand_code` as brandCode,
            `common_brand_tool`.`brand_name` as brandName
        FROM
            `common_brand_tool`
        ORDER BY
            `common_brand_tool`.`sort` ASC,
            `common_brand_tool`.`brand_name` DESC ;
    </select>


    <select id="workOrderTrend" resultType="com.car.report.vo.WorkOrderTrendVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateWeekFilterCommon" />
        </if>
        select * from (
                          SELECT
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              `f2`.`create_time`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`create_time`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`,
                                              date(`vid`.`create_time`) AS `create_time`
                                      FROM
            `common_date_tools` AS cdt
        LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                      (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                  WHERE
                                      (`vid`.`date_unit` = '-1')
                                    AND (`vid`.`brand_code` IS NOT NULL)
                                  GROUP BY
                                      date_format(`cdt`.`endDate`, '%Y%m%d'),
                                      `cdt`.`startDate`,
                                      `cdt`.`endDate`,
                                      `vid`.`brand_code`,
                                      `vid`.`brand_name`,
                                      date(`vid`.`create_time`)) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        UNION ALL
        SELECT
            `f2`.`statistic`,
            'allBrand' AS `brand_code`,
            'allBrand' AS `brand_name`,
            `f2`.`create_time`
        FROM
            (
                SELECT
                    `cc`.`date_`,
                    `cc`.`endDate_`,
                    `cc`.`dateNum`,
                    `cc`.`date_unit`,
                    `cc`.`startDate`,
                    `cc`.`endDate`,
                    `cc`.`days`,
                    `cc`.`date_` AS `dateStr`,
                    `f1`.`voice_c` AS `statistic`,
                    `f1`.`create_time`
                FROM
                    `common_date_tools` AS cc
                        LEFT OUTER JOIN (
                        SELECT
                            date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                            `cdt`.`startDate`,
                            `cdt`.`endDate`,
                            count(1) AS `voice_c`,
                            date(`vid`.`create_time`) AS `create_time`
                    FROM
            `common_date_tools` AS cdt
        LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                    (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                WHERE
                    (`vid`.`date_unit` = '-1')
                  AND (`vid`.`brand_code` IS NOT NULL)
                GROUP BY
                    date_format(`cdt`.`endDate`, '%Y%m%d'),
                    `cdt`.`startDate`,
                    `cdt`.`endDate`,
                    date(`vid`.`create_time`)) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        )f3
        where brand_code=#{brandCode}
        order by create_time asc
    </select>


    <select id="workOrderTypeDistribution" resultType="com.car.report.vo.WorkOrderTypeVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateYearFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              `f2`.`name`,
                              round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_`, `f1`.`brand_code` ), 0) AS `statistic_sum`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`wo_type` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              ((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`brand_code` IS NOT NULL))
                                            AND (`vid`.`wo_type` IS NOT NULL)
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          UNION ALL
                          SELECT
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
                              `f2`.`name`,
                              round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_` ), 0) AS `statistic_sum`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`wo_type` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              ((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`brand_code` IS NOT NULL))
                                            AND (`vid`.`wo_type` IS NOT NULL)
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                      )f3
        where brand_code= #{brandCode}
    </select>


    <select id="workOrderStatusDistribution" resultType="com.car.report.vo.WorkOrderStatusVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateYearFilterCommon" />
        </if>
        select * from (
        SELECT
        `f2`.`statistic`,
        'allBrand' AS `brand_code`,
        `f2`.`brand_name`,
        `f2`.`notClosed_c`,
        `f2`.`closed_c`,
        round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
        FROM
        (
        SELECT
        `cc`.`date_`,
        `cc`.`endDate_`,
        `cc`.`dateNum`,
        `cc`.`date_unit`,
        `cc`.`startDate`,
        `cc`.`endDate`,
        `cc`.`days`,
        `cc`.`date_` AS `dateStr`,
        `f1`.`voice_c` AS `statistic`,
        `f1`.`brand_code`,
        `f1`.`brand_name`,
        `f1`.`notClosed_c`,
        `f1`.`closed_c`,
        ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_` ), 0) AS `statistic_sum`
        FROM
        `common_date_tools` AS cc
        LEFT OUTER JOIN (
        SELECT
        date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
        sum(`vid`.`status` &lt;= 3) AS `notClosed_c`,
        sum(`vid`.`status` > 3) AS `closed_c`,
        `cdt`.`endDate`,
        count(1) AS `voice_c`,
        `vid`.`brand_code`,
        `vid`.`brand_name`
        FROM
        `common_date_tools` AS cdt
        LEFT OUTER JOIN `wo_original_data` AS `vid` ON
        (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
        WHERE
        ((`vid`.`date_unit` = '-1')
        AND (`vid`.`brand_code` IS NOT NULL))
        AND (`vid`.`wo_type` IS NOT NULL)
        GROUP BY
        date_format(`cdt`.`endDate`, '%Y%m%d'),
        `cdt`.`startDate`,
        `cdt`.`endDate`,
        `vid`.`brand_code`,
        `vid`.`brand_name`) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        UNION ALL
        SELECT
        `f2`.`statistic`,
        `f2`.`brand_code`,
        `f2`.`brand_name`,
        `f2`.`notClosed_c`,
        `f2`.`closed_c`,
        round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
        FROM
        (
        SELECT
        `cc`.`date_`,
        `cc`.`endDate_`,
        `cc`.`dateNum`,
        `cc`.`date_unit`,
        `cc`.`startDate`,
        `cc`.`endDate`,
        `cc`.`days`,
        `cc`.`date_` AS `dateStr`,
        `f1`.`voice_c` AS `statistic`,
        `f1`.`brand_code`,
        `f1`.`brand_name`,
        `f1`.`notClosed_c`,
        `f1`.`closed_c`,
        ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_`,brand_code ), 0) AS `statistic_sum`
        FROM
        `common_date_tools` AS cc
        LEFT OUTER JOIN (
        SELECT
        date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
        sum(`vid`.`status` &lt;= 3) AS `notClosed_c`,
        sum(`vid`.`status` > 3) AS `closed_c`,
        `cdt`.`endDate`,
        count(1) AS `voice_c`,
        `vid`.`brand_code`,
        `vid`.`brand_name`
        FROM
        `common_date_tools` AS cdt
        LEFT OUTER JOIN `wo_original_data` AS `vid` ON
        (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
        WHERE
        ((`vid`.`date_unit` = '-1')
        AND (`vid`.`brand_code` IS NOT NULL))
        AND (`vid`.`wo_type` IS NOT NULL)
        GROUP BY
        date_format(`cdt`.`endDate`, '%Y%m%d'),
        `cdt`.`startDate`,
        `cdt`.`endDate`,
        `vid`.`brand_code`,
        `vid`.`brand_name`) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        )f3
        where brand_code=#{brandCode}
    </select>


    <select id="salesComplaintTop" resultType="com.car.report.vo.SalesComplaintTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * FROM (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                               ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name`,brand_code ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                             `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`first_dimension_name` = '销售服务')
                                            and wo_type="投诉"
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`first_dimension_name` = '销售服务')
                                            and wo_type="投诉"
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code=#{brandCode} order by statistic desc limit 5
    </select>


    <select id="afterSalesComplaintTop" resultType="com.car.report.vo.AfterSalesComplaintTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * FROM (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name`,brand_code ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`first_dimension_name` = '售后服务')
                                            and wo_type="投诉"
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`first_dimension_name` = '售后服务')
                                            and wo_type="投诉"
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code=#{brandCode}  order by statistic desc limit 5
    </select>

    <select id="productQualityComplaintTop" resultType="com.car.report.vo.ProductQualityComplaintTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * FROM (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`first_dimension_code`  LIKE 'Q100%')
                                            and wo_type="投诉"
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`first_dimension_code`  LIKE 'Q100%')
                                            and wo_type="投诉"
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code=#{brandCode} order by statistic desc limit 5
    </select>

    <select id="consultTop" resultType="com.car.report.vo.ConsultTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * FROM (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`wo_type` = '咨询')
                                            and topic_name is not null
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `wo_original_data` AS `vid` ON
                                                  (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`wo_type` = '咨询')
                                            and topic_name is not null
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code=#{brandCode} order by statistic desc limit 5
    </select>

    <select id="publicSentimentTop" resultType="com.car.report.vo.PublicSentimentTopVo">
        WITH work_opinion AS (
            select
                `product`.`code` AS `brand_code`,
                `product`.`name` AS `brand_name`,
                CASE
                    WHEN (f1.tag_code_random LIKE 'B%') THEN `business4`.`name`
                    ELSE `quality4`.`name`
                    END AS `topic_name`,
                publisDate
            from
                (
                    SELECT
                        brand,
                        tag_code_random,
                        IF(opinion.id = vid.source_no, STR_TO_DATE(opinion.created, '%Y/%m/%d %H:%i:%s'),
                           vid.create_time)     publisDate
                    FROM t317_csv_rescue_wo_i_d_mysql vid
                             INNER JOIN tt_public_opinion_id_mysql opinion ON opinion.id = vid.source_no
                    WHERE vid.channel_source_2 = '舆情'

                )f1
                    LEFT OUTER JOIN `voc_business_tag_mysql` AS `business4` ON
                    `business4`.`tag_code` = `f1`.`tag_code_random`
                    LEFT OUTER JOIN `voc_fault_problem_mysql` AS `quality4` ON
                    `quality4`.`code` = `f1`.`tag_code_random`
                    LEFT OUTER JOIN `voc_brand_product_manager_mysql` AS `product` ON
                    (`product`.`alias` LIKE (concat('%', `f1`.`brand`, '%')))
                        AND (`product`.`p_id` = 0)
        )


        select * FROM (

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              topic_name AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              brand_code,
                                              brand_name
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN work_opinion AS `vid` ON
                                                  (date(publisDate)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              topic_name IS NOT NULL
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              brand_code,
                                              brand_name) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              topic_name AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN work_opinion AS `vid` ON
                                                  (date(publisDate)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              topic_name IS NOT NULL
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code=#{brandCode} order by statistic desc limit 5
    </select>


    <select id="channelDistribution" resultType="com.car.report.vo.ChannelDistributionVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthFilterCommon" />
        </if>
        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`name`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_`, `f1`.`brand_code` ), 0) AS `statistic_sum`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`channel2_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
                              round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`name`,
                                      `f1`.`voice_c` AS `statistic`,
                                      ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_` ), 0) AS `statistic_sum`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`channel2_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                      )f3
        where name is not null and name &lt;>"" and brand_code=#{brandCode}
    </select>

    <select id="channelTrend" resultType="com.car.report.vo.ChannelTrendVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              `f2`.`publish_date`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`name`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`publish_date`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`channel2_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`,
                                              date(`vid`.`publish_date`) AS `publish_date`
                                      FROM
            `common_date_tools` AS cdt
        LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                      (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                  WHERE
                                      (`vid`.`date_unit` = '-1')
                                    AND (`vid`.`tag_type` = '1')
                                  GROUP BY
                                      date_format(`cdt`.`endDate`, '%Y%m%d'),
                                      `name`,
                                      `cdt`.`startDate`,
                                      `cdt`.`endDate`,
                                      `vid`.`brand_code`,
                                      `vid`.`brand_name`,
                                      date(`vid`.`publish_date`)) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        UNION ALL
        SELECT
            `f2`.`name`,
            `f2`.`statistic`,
            'allBrand' AS `brand_code`,
            'allBrand' AS `brand_name`,
            `f2`.`publish_date`
        FROM
            (
                SELECT
                    `cc`.`date_`,
                    `cc`.`endDate_`,
                    `cc`.`dateNum`,
                    `cc`.`date_unit`,
                    `cc`.`startDate`,
                    `cc`.`endDate`,
                    `cc`.`days`,
                    `cc`.`ic`,
                    `cc`.`date_` AS `dateStr`,
                    `f1`.`name`,
                    `f1`.`voice_c` AS `statistic`,
                    `f1`.`publish_date`
                FROM
                    `common_date_tools` AS cc
                        LEFT OUTER JOIN (
                        SELECT
                            date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                            `vid`.`channel2_name` AS `name`,
                            `cdt`.`startDate`,
                            `cdt`.`endDate`,
                            count(1) AS `voice_c`,
                            date(`vid`.`publish_date`) AS `publish_date`
                    FROM
            `common_date_tools` AS cdt
        LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                    (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                WHERE
                    (`vid`.`date_unit` = '-1')
                  AND (`vid`.`tag_type` = '1')
                GROUP BY
                    date_format(`cdt`.`endDate`, '%Y%m%d'),
                    `name`,
                    `cdt`.`startDate`,
                    `cdt`.`endDate`,
                    date(`vid`.`publish_date`)) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        ORDER BY
            `f2`.`publish_date` ASC
            )f3
        where name is not null and name &lt;>"" and  brand_code=#{brandCode}
        order by name asc,publish_date asc
    </select>


    <select id="affectiveDistribution" resultType="com.car.report.vo.AffectiveDistributionVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              `f2`.`positive_c`,
                              `f2`.`negative_c`,
                              `f2`.`neutral_c`,
                              round(`f2`.`nsr_c`, 2) as nsr_c,
                              round(ifnull((`f2`.`positive_c` / `f2`.`statistic`) * 100, 0), 2) AS `positive_p`,
                              round(ifnull((`f2`.`negative_c` / `f2`.`statistic`) * 100, 0), 2) AS `negative_p`,
                              round(ifnull((`f2`.`neutral_c` / `f2`.`statistic`) * 100, 0), 2) AS `neutral_p`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`positive_c`,
                                      `f1`.`negative_c`,
                                      `f1`.`neutral_c`,
                                      ifnull(((ifnull((ifnull(`f1`.`positive_c`, 0)) - (ifnull(`f1`.`negative_c`, 0)), 0)) / (ifnull((ifnull(`f1`.`positive_c`, 0)) + (ifnull(`f1`.`negative_c`, 0)), 0))) * 100, 0) AS `nsr_c`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`,
                                              sum(`vid`.`dimension_emotion` = '正面') AS `positive_c`,
                                              sum(`vid`.`dimension_emotion` = '负面') AS `negative_c`,
                                              sum(`vid`.`dimension_emotion` = '中性') AS `neutral_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          UNION ALL
                          SELECT
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_code`,
                              `f2`.`positive_c`,
                              `f2`.`negative_c`,
                              `f2`.`neutral_c`,
                              round(`f2`.`nsr_c`, 2) as nsr_c,
                              round(ifnull((`f2`.`positive_c` / `f2`.`statistic`) * 100, 0), 2) AS `positive_p`,
                              round(ifnull((`f2`.`negative_c` / `f2`.`statistic`) * 100, 0), 2) AS `negative_p`,
                              round(ifnull((`f2`.`neutral_c` / `f2`.`statistic`) * 100, 0), 2) AS `neutral_p`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`positive_c`,
                                      `f1`.`negative_c`,
                                      `f1`.`neutral_c`,
                                      ifnull(((ifnull((ifnull(`f1`.`positive_c`, 0)) - (ifnull(`f1`.`negative_c`, 0)), 0)) / (ifnull((ifnull(`f1`.`positive_c`, 0)) + (ifnull(`f1`.`negative_c`, 0)), 0))) * 100, 0) AS `nsr_c`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              sum(`vid`.`dimension_emotion` = '正面') AS `positive_c`,
                                              sum(`vid`.`dimension_emotion` = '负面') AS `negative_c`,
                                              sum(`vid`.`dimension_emotion` = '中性') AS `neutral_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`

                      )f3
        where brand_code=#{brandCode}
    </select>


    <select id="focusAttention" resultType="com.car.report.vo.FocusAttentionVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthFilterCommon" />
        </if>
        select * from (
                          SELECT
                              `f2`.`name` AS `label_name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_`, `f1`.`brand_code` ), 0) AS `statistic_sum`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`first_dimension_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          UNION ALL
                          SELECT
                              `f2`.`name` AS `label_name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
                              round(ifnull((`f2`.`statistic` / `f2`.`statistic_sum`) * 100, 0), 2) AS `statisticP`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_` ), 0) AS `statistic_sum`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`first_dimension_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`

                      )f3
        where brand_code=#{brandCode}
    </select>


    <select id="focusSeries" resultType="com.car.report.vo.FocusSeriesVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * from (
        SELECT
        `f2`.`name`,
        get_json_string(`f3`.`json_show_img`,
        `f2`.`name`) AS `series_img`,
        `f2`.`statistic`,
        `f2`.`brand_code`,
        `f2`.`brand_name`,
        round( `f2`.`nsr_c`,2) as nsr_c,
        case
        when cast(nsr_c as FLOAT) >80 then "https://cc.dfes.com.cn/voc/api/sys/common/static/1_1746769868964.png"
        when cast(nsr_c as FLOAT) >=0 and cast(nsr_c as FLOAT) &lt;80 then "https://cc.dfes.com.cn/voc/api/sys/common/static/2_1746769915146.png"
        when cast(nsr_c as FLOAT)  &lt;0 then "https://cc.dfes.com.cn/voc/api/sys/common/static/3_1746769957328.png "
        else round(nsr_c, 2)
        end as nsrG,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
        FROM
        (
        SELECT
        `cc`.`date_`,
        `cc`.`endDate_`,
        `cc`.`dateNum`,
        `cc`.`date_unit`,
        `cc`.`startDate`,
        `cc`.`endDate`,
        `cc`.`days`,
        `cc`.`ic`,
        `cc`.`date_` AS `dateStr`,
        `f1`.`voice_c` AS `statistic`,
        `f1`.`brand_code`,
        `f1`.`brand_name`,
        `f1`.`name`,
        ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`,
        ifnull(((ifnull((ifnull(`f1`.`positive_c`, 0)) - (ifnull(`f1`.`negative_c`, 0)), 0)) / (ifnull((ifnull(`f1`.`positive_c`, 0)) + (ifnull(`f1`.`negative_c`, 0)), 0))) * 100, 0) AS `nsr_c`
        FROM
        `common_date_tools` AS `cc`
        LEFT OUTER JOIN (
        SELECT
        date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
        `vid`.`car_series_name` AS `name`,
        `cdt`.`startDate`,
        `cdt`.`endDate`,
        count(1) AS `voice_c`,
        `vid`.`brand_code`,
        `vid`.`brand_name`,
        sum(`vid`.`dimension_emotion` = '正面') AS `positive_c`,
        sum(`vid`.`dimension_emotion` = '负面') AS `negative_c`,
        sum(`vid`.`dimension_emotion` = '中性') AS `neutral_c`
        FROM
        `common_date_tools` AS `cdt`
        LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
        (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
        WHERE
        (`vid`.`date_unit` = '-1')
        GROUP BY
        date_format(`cdt`.`endDate`, '%Y%m%d'),
        `name`,
        `cdt`.`startDate`,
        `cdt`.`endDate`,
        `vid`.`brand_code`,
        `vid`.`brand_name`) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2` ,
        (
        SELECT
        to_json(map_from_arrays(array_agg(`c1`.`name`),
        array_agg(concat('https://cc.dfes.com.cn/voc/api/sys/common/static/', `c1`.`show_img`)))) AS `json_show_img`
        FROM
        (
        SELECT
        `voc_brand_product_manager_mysql`.`name`,
        `voc_brand_product_manager_mysql`.`show_img`
        FROM
        `voc_brand_product_manager_mysql`
        GROUP BY
        `voc_brand_product_manager_mysql`.`name`,
        `voc_brand_product_manager_mysql`.`show_img`) `c1`) `f3`
        WHERE
        `f2`.`ic` = 0
        UNION ALL
        SELECT
        `f2`.`name`,
        get_json_string(`f3`.`json_show_img`,
        `f2`.`name`) AS `series_img`,
        `f2`.`statistic`,
        'allBrand' AS `brand_code`,
        'allBrand' AS `brand_name`,
        round( `f2`.`nsr_c`,2) as nsr_c,
        case
        when cast(nsr_c as FLOAT) >80 then "https://cc.dfes.com.cn/voc/api/sys/common/static/1_1746769868964.png"
        when cast(nsr_c as FLOAT) >=0 and cast(nsr_c as FLOAT) &lt;80 then "https://cc.dfes.com.cn/voc/api/sys/common/static/2_1746769915146.png"
        when cast(nsr_c as FLOAT)  &lt;0 then "https://cc.dfes.com.cn/voc/api/sys/common/static/3_1746769957328.png "
        else round(nsr_c, 2)
        end as nsrG,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
        FROM
        (
        SELECT
        `cc`.`date_`,
        `cc`.`endDate_`,
        `cc`.`dateNum`,
        `cc`.`date_unit`,
        `cc`.`startDate`,
        `cc`.`endDate`,
        `cc`.`days`,
        `cc`.`ic`,
        `cc`.`date_` AS `dateStr`,
        `f1`.`voice_c` AS `statistic`,
        `f1`.`name`,
        ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`,
        ifnull(((ifnull((ifnull(`f1`.`positive_c`, 0)) - (ifnull(`f1`.`negative_c`, 0)), 0)) / (ifnull((ifnull(`f1`.`positive_c`, 0)) + (ifnull(`f1`.`negative_c`, 0)), 0))) * 100, 0) AS `nsr_c`
        FROM
        `common_date_tools` AS `cc`
        LEFT OUTER JOIN (
        SELECT
        date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
        `vid`.`car_series_name` AS `name`,
        `cdt`.`startDate`,
        `cdt`.`endDate`,
        count(1) AS `voice_c`,
        sum(`vid`.`dimension_emotion` = '正面') AS `positive_c`,
        sum(`vid`.`dimension_emotion` = '负面') AS `negative_c`,
        sum(`vid`.`dimension_emotion` = '中性') AS `neutral_c`
        FROM
        `common_date_tools` AS `cdt`
        LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
        (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
        WHERE
        (`vid`.`date_unit` = '-1')
        GROUP BY
        date_format(`cdt`.`endDate`, '%Y%m%d'),
        `name`,
        `cdt`.`startDate`,
        `cdt`.`endDate`) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2` ,
        (
        SELECT
        to_json(map_from_arrays(array_agg(`c1`.`name`),
        array_agg(concat('https://cc.dfes.com.cn/voc/api/sys/common/static/', `c1`.`show_img`)))) AS `json_show_img`
        FROM
        (
        SELECT
        `voc_brand_product_manager_mysql`.`name`,
        `voc_brand_product_manager_mysql`.`show_img`
        FROM
        `voc_brand_product_manager_mysql`
        GROUP BY
        `voc_brand_product_manager_mysql`.`name`,
        `voc_brand_product_manager_mysql`.`show_img`) `c1`) `f3`
        WHERE
        `f2`.`ic` = 0
        ORDER BY
        `f2`.`statistic` DESC,
        `f2`.`brand_code` ASC

        )f3
        where brand_code= #{brandCode} order by statistic desc limit 5
    </select>


    <select id="consultingMentionTop" resultType="com.car.report.vo.ConsultingMentionTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              ((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                            AND (`vid`.`wo_type` = '咨询')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              ((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                            AND (`vid`.`wo_type` = '咨询')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code =#{brandCode} order by statistic desc limit 5
    </select>

    <select id="consultingSurgeTop" resultType="com.car.report.vo.ConsultingMentionTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              ((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                            AND (`vid`.`wo_type` = '咨询')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`name` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`
                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`topic_name` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              ((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                            AND (`vid`.`wo_type` = '咨询')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              `f2`.`ic` = 0
                      )f3
        where brand_code =#{brandCode} and statisticR!='--'  order by statisticR  desc limit 5
    </select>


    <select id="labelClassificationTop" resultType="com.car.report.vo.LabelClassificationTopVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthRateFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`threeName`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`,
                              round( `f2`.`nsr_c`,2) as nsr_c,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`,
                                      `f1`.`threeName`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`threeName`,brand_code ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`,
                                      row_number() OVER (PARTITION BY `f1`.`brand_code`,
        `f1`.`name`
    ORDER BY
        `f1`.`voice_c` DESC ) AS `row_num`,
                                          ifnull(((ifnull((ifnull(`f1`.`positive_c`, 0)) - (ifnull(`f1`.`negative_c`, 0)), 0)) / (ifnull((ifnull(`f1`.`positive_c`, 0)) + (ifnull(`f1`.`negative_c`, 0)), 0))) * 100, 0) AS `nsr_c`

                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`first_dimension_name` AS `name`,
                                              `vid`.`three_dimension_name` AS `threeName`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`,
                                              sum(`vid`.`dimension_emotion` = '正面') AS `positive_c`,
                                              sum(`vid`.`dimension_emotion` = '负面') AS `negative_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `threeName`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              (`f2`.`ic` = 0)
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`threeName`,
                              `f2`.`statistic`,
                              'allBrand' AS `brand_code`,
                              'allBrand' AS `brand_name`,
                              round( `f2`.`nsr_c`,2) as nsr_c,
        ifnull(round(CASE WHEN ((`f2`.`voice_r` = 0) AND ((`f2`.`statistic` - `f2`.`voice_r`) != 0)) THEN '--' ELSE (ifnull(((`f2`.`statistic` - `f2`.`voice_r`) / (if(`f2`.`voice_r` = 0, 1, `f2`.`voice_r`))) * 100, 0)) END, 2),'--') AS `statisticR`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`,
                                      `f1`.`threeName`,
                                      ifnull(lead(`f1`.`voice_c`) OVER (PARTITION BY `f1`.`threeName` ORDER BY `cc`.`date_` DESC ), 0) AS `voice_r`,
                                      row_number() OVER (PARTITION BY `f1`.`name`
    ORDER BY
        `f1`.`voice_c` DESC ) AS `row_num`,
                                          ifnull(((ifnull((ifnull(`f1`.`positive_c`, 0)) - (ifnull(`f1`.`negative_c`, 0)), 0)) / (ifnull((ifnull(`f1`.`positive_c`, 0)) + (ifnull(`f1`.`negative_c`, 0)), 0))) * 100, 0) AS `nsr_c`

                                  FROM
                                      `common_date_tools` AS `cc`
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`first_dimension_name` AS `name`,
                                              `vid`.`three_dimension_name` AS `threeName`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              sum(`vid`.`dimension_emotion` = '正面') AS `positive_c`,
                                              sum(`vid`.`dimension_emotion` = '负面') AS `negative_c`
                                          FROM
                                              `common_date_tools` AS `cdt`
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (`vid`.`date_unit` = '-1')
                                            AND (`vid`.`tag_type` = '1')
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `threeName`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              (`f2`.`ic` = 0)
                      )f3
        where brand_code=#{brandCode} order by statistic desc
    </select>


    <select id="userFavorableComment" resultType="com.car.report.vo.UserFavorableCommentVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthFilterCommon" />
        </if>
        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`emotion_keyword` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                                  AND (`vid`.`dimension_emotion` = '正面'))
                                            AND (`vid`.`emotion_keyword` IS NOT NULL)
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              (`f2`.`ic` = 0)
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              "allBrand" as `brand_code`,
                              "allBrand" as `brand_name`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`emotion_keyword` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                                  AND (`vid`.`dimension_emotion` = '正面'))
                                            AND (`vid`.`emotion_keyword` IS NOT NULL)
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              (`f2`.`ic` = 0)
                      )f3
        where brand_code=#{brandCode}  order by  statistic desc limit 10
    </select>


    <select id="userBadComment" resultType="com.car.report.vo.UserBadCommentVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateMonthFilterCommon" />
        </if>

        select * from (
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              `f2`.`brand_code`,
                              `f2`.`brand_name`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`brand_code`,
                                      `f1`.`brand_name`,
                                      `f1`.`name`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`emotion_keyword` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                                  AND (`vid`.`dimension_emotion` = '负面'))
                                            AND (`vid`.`emotion_keyword` IS NOT NULL)
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              `vid`.`brand_code`,
                                              `vid`.`brand_name`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              (`f2`.`ic` = 0)
                          UNION ALL
                          SELECT
                              `f2`.`name`,
                              `f2`.`statistic`,
                              "allBrand" as `brand_code`,
                              "allBrand" as `brand_name`
                          FROM
                              (
                                  SELECT
                                      `cc`.`date_`,
                                      `cc`.`endDate_`,
                                      `cc`.`dateNum`,
                                      `cc`.`date_unit`,
                                      `cc`.`startDate`,
                                      `cc`.`endDate`,
                                      `cc`.`days`,
                                      `cc`.`ic`,
                                      `cc`.`date_` AS `dateStr`,
                                      `f1`.`voice_c` AS `statistic`,
                                      `f1`.`name`
                                  FROM
                                      `common_date_tools` AS cc
                                          LEFT OUTER JOIN (
                                          SELECT
                                              date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
                                              `vid`.`emotion_keyword` AS `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`,
                                              count(1) AS `voice_c`
                                          FROM
                                              `common_date_tools` AS cdt
                                                  LEFT OUTER JOIN `voc_sentence_all` AS `vid` ON
                                                  (date(`vid`.`publish_date`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
                                          WHERE
                                              (((`vid`.`date_unit` = '-1')
                                                  AND (`vid`.`tag_type` = '1'))
                                                  AND (`vid`.`dimension_emotion` = '负面'))
                                            AND (`vid`.`emotion_keyword` IS NOT NULL)
                                          GROUP BY
                                              date_format(`cdt`.`endDate`, '%Y%m%d'),
                                              `name`,
                                              `cdt`.`startDate`,
                                              `cdt`.`endDate`) `f1` ON
                                          `cc`.`date_` = `f1`.`date_str`) `f2`
                          WHERE
                              (`f2`.`ic` = 0)
                      )f3
        where brand_code=#{brandCode}  order by  statistic desc limit 10
    </select>


    <select id="regionLatitudeAndLongitude" resultType="java.lang.String">
        select latitude_and_longitude from region_latitude_and_longitude
    </select>

    <select id="getMapTopData" resultType="com.car.report.vo.MapTopDataInfoVo">
        SELECT
        CASE
        WHEN province_name LIKE '%省' THEN REPLACE(province_name, '省', '')
        WHEN province_name LIKE '%市' THEN REPLACE(province_name, '市', '')
        ELSE province_name
        END AS regionName,
        COUNT(*) as total,
        SUM(CASE WHEN intention_type = '咨询' THEN 1 ELSE 0 END) AS consultingCount,
        SUM(CASE WHEN intention_type = '投诉' THEN 1 ELSE 0 END) AS complaintCount,
        SUM(CASE WHEN intention_type = '建议' THEN 1 ELSE 0 END) AS suggestionCount,
        SUM(CASE WHEN intention_type = '表扬' THEN 1 ELSE 0 END) AS praiseCount,
        SUM(CASE WHEN intention_type = '其他' THEN 1 ELSE 0 END) AS otherCount
        FROM voc_sentence_all vid
        WHERE province_name IS NOT NULL
        <if test="brandCode !=null and brandCode != '' ">
            and brand_code= #{brandCode}
        </if>
        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            and (date(`vid`.`publish_date`)) BETWEEN (date(#{startDate}))
            AND (date(#{endDate}))
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            and (date(`vid`.`publish_date`)) BETWEEN (date(date_sub(date_sub(now(), INTERVAL 1 DAY), INTERVAL 6 DAY)))
            AND (date(date_sub(now(), INTERVAL 1 DAY)))
        </if>
        GROUP BY
        CASE
        WHEN province_name LIKE '%省' THEN REPLACE(province_name, '省', '')
        WHEN province_name LIKE '%市' THEN REPLACE(province_name, '市', '')
        ELSE province_name
        END
        order by total desc limit 5
    </select>



    <select id="workOrderTotalAndClosureRate" resultType="com.car.report.vo.TotalClosureRateVo">

        <if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
            <include refid="commonFilter.dateCustomizeRateFilterCommon" />
        </if>

        <if test="startDate ==null or startDate == '' and endDate ==null or endDate == ''">
            <include refid="commonFilter.dateYearFilterCommon" />
        </if>
        select * from (
        SELECT
        `f2`.`statistic_sum`,
        'allBrand' AS `brand_code`,
        `f2`.`notClosed_c`,
        `f2`.`closed_c`,
        round(ifnull((`f2`.`closed_c` / `f2`.`statistic_sum`) * 100, 0), 2) AS `closed_rate`
        FROM
        (
        SELECT
        `cc`.`date_`,
        `cc`.`endDate_`,
        `cc`.`dateNum`,
        `cc`.`date_unit`,
        `cc`.`startDate`,
        `cc`.`endDate`,
        `cc`.`days`,
        `cc`.`date_` AS `dateStr`,
        `f1`.`voice_c` AS `statistic`,
        `f1`.`notClosed_c`,
        `f1`.`closed_c`,
        ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_` ), 0) AS `statistic_sum`
        FROM
        `common_date_tools` AS cc
        LEFT OUTER JOIN (
        SELECT
        date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
        sum(`vid`.`status`  &lt;= 3) AS `notClosed_c`,
        sum(`vid`.`status` > 3) AS `closed_c`,
        `cdt`.`endDate`,
        count(1) AS `voice_c`
        FROM
        `common_date_tools` AS cdt
        LEFT OUTER JOIN `wo_original_data` AS `vid` ON
        (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
        WHERE
        ((`vid`.`date_unit` = '-1')
        AND (`vid`.`brand_code` IS NOT NULL))
        AND (`vid`.`wo_type` IS NOT NULL)
        GROUP BY
        date_format(`cdt`.`endDate`, '%Y%m%d'),
        `cdt`.`startDate`,
        `cdt`.`endDate`) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        UNION ALL
        SELECT
        `f2`.`statistic_sum`,
        `f2`.`brand_code`,
        `f2`.`notClosed_c`,
        `f2`.`closed_c`,
        round(ifnull((`f2`.`closed_c` / `f2`.`statistic_sum`) * 100, 0), 2) AS `closed_rate`
        FROM
        (
        SELECT
        `cc`.`date_`,
        `cc`.`endDate_`,
        `cc`.`dateNum`,
        `cc`.`date_unit`,
        `cc`.`startDate`,
        `cc`.`endDate`,
        `cc`.`days`,
        `cc`.`date_` AS `dateStr`,
        `f1`.`voice_c` AS `statistic`,
        `f1`.`brand_code`,
        `f1`.`brand_name`,
        `f1`.`notClosed_c`,
        `f1`.`closed_c`,
        ifnull(sum(`f1`.`voice_c`) OVER (PARTITION BY `cc`.`date_`,brand_code ), 0) AS `statistic_sum`
        FROM
        `common_date_tools` AS cc
        LEFT OUTER JOIN (
        SELECT
        date_format(`cdt`.`endDate`, '%Y%m%d') AS `date_str`,
        sum(`vid`.`status`  &lt;= 3) AS `notClosed_c`,
        sum(`vid`.`status` > 3) AS `closed_c`,
        `cdt`.`endDate`,
        count(1) AS `voice_c`,
        `vid`.`brand_code`,
        `vid`.`brand_name`
        FROM
        `common_date_tools` AS cdt
        LEFT OUTER JOIN `wo_original_data` AS `vid` ON
        (date(`vid`.`create_time`)) BETWEEN (date(`cdt`.`startDate`)) AND (date(`cdt`.`endDate`))
        WHERE
        ((`vid`.`date_unit` = '-1')
        AND (`vid`.`brand_code` IS NOT NULL))
        AND (`vid`.`wo_type` IS NOT NULL)
        GROUP BY
        date_format(`cdt`.`endDate`, '%Y%m%d'),
        `cdt`.`startDate`,
        `cdt`.`endDate`,
        `vid`.`brand_code`,
        `vid`.`brand_name`) `f1` ON
        `cc`.`date_` = `f1`.`date_str`) `f2`
        )f3
        where brand_code=#{brandCode}
    </select>


</mapper>

