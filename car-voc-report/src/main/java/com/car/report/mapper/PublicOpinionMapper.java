package com.car.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.report.entity.PublicOpinionEntity;
import com.car.report.vo.PublicOpinionVo;
import com.car.stats.model.FilterCriteriaModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 舆情数据(TtPublicOpinion)表持久层
 *
 * <AUTHOR>
 * @since 2024-12-18 17:20:53
 */
@Mapper
@Repository
public interface PublicOpinionMapper extends BaseMapper<PublicOpinionEntity> {

    IPage<PublicOpinionVo> queryBySelectPage(IPage<PublicOpinionVo> page, @Param("model") FilterCriteriaModel model);
}

