package com.car.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.car.voc.annotation.DataDesensitization;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 舆情数据(TtPublicOpinion)实体类
 *
 * <AUTHOR>
 * @since 2024-12-19 14:18:08
 */
@Data
@Builder
@Accessors(chain = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tt_public_opinion", autoResultMap = true)
public class PublicOpinionEntity {
    private String id;

    @TableField(value = "sid")
    private String sid;
    /**
     * 媒体
     */
    @TableField(value = "media")
    private String media;
    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;
    /**
     * 媒体类型
     */
    @TableField(value = "media_type")
    private String mediaType;
    /**
     * 媒体板块
     */
    @TableField(value = "channel")
    private String channel;
    /**
     * 数据源链接
     */
    @TableField(value = "uri")
    private String uri;
    /**
     * 图片
     */
    @TableField(value = "imgs", typeHandler = JacksonTypeHandler.class)
    private Map imgs;
    /**
     * 用户名
     */
    @TableField(value = "user_screen_name")
    private String userScreenName;
    /**
     * 用户来源
     */
    @TableField(value = "user_src")
    private String userSrc;
    /**
     * 是否有粉丝
     */
    @TableField(value = "user_has_followers")
    private String userHasFollowers;
    /**
     * 用户id
     */
    @TableField(value = "user_id_str")
    private String userIdStr;
    /**
     * 阅读数
     */
    @TableField(value = "read_num")
    private String readNum;
    /**
     * 标题
     */
    @TableField(value = "title_tokens")
    private String titleTokens;
    @TableField(value = "imgs_count")
    private String imgsCount;
    @TableField(value = "engagement_num")
    private String engagementNum;
    /**
     * 评论量
     */
    @TableField(value = "comment_num")
    private String commentNum;
    @TableField(value = "kg_infos_num")
    private String kgInfosNum;
    /**
     * 全文
     */
    @TableField(value = "body")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE,DataDesensitization.DesensitizationType.VIN})
    private String body;
    /**
     * 摘要
     */
    @TableField(value = "summary")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE,DataDesensitization.DesensitizationType.VIN})
    private String summary;
    /**
     * 倾向
     */
    @TableField(value = "tendency")
    private String tendency;
    @TableField(value = "keywords_in_title")
    private String keywordsInTitle;
    @TableField(value = "keywords_dict", typeHandler = JacksonTypeHandler.class)
    private Map keywordsDict;
    @TableField(value = "topic_paths", typeHandler = JacksonTypeHandler.class)
    private Map topicPaths;
    @TableField(value = "tag_paths", typeHandler = JacksonTypeHandler.class)
    private Map tagPaths;
    @TableField(value = "tag1_paths", typeHandler = JacksonTypeHandler.class)
    private Map tag1Paths;
    @TableField(value = "tag2_paths", typeHandler = JacksonTypeHandler.class)
    private Map tag2Paths;
    @TableField(value = "tag3_paths", typeHandler = JacksonTypeHandler.class)
    private Map tag3Paths;
    @TableField(value = "product_paths", typeHandler = JacksonTypeHandler.class)
    private Map productPaths;
    @TableField(value = "brand_paths", typeHandler = JacksonTypeHandler.class)
    private Map brandPaths;
    @TableField(value = "company_paths", typeHandler = JacksonTypeHandler.class)
    private Map companyPaths;
    @TableField(value = "people_paths", typeHandler = JacksonTypeHandler.class)
    private Map peoplePaths;
    @TableField(value = "region_paths", typeHandler = JacksonTypeHandler.class)
    private Map regionPaths;
    @TableField(value = "remark")
    private String remark;
    /**
     * created
     */
    @TableField(value = "created")
    private String created;
    /**
     * 同步时间
     */
    @TableField(value = "sync_time")
    private LocalDateTime syncTime;
    /**
     * 媒体平台
     */
    @TableField(value = "media_platform")
    private String mediaPlatform;
    /**
     * 同步状态
     */
    @TableField(value = "status")
    private String status;

}

