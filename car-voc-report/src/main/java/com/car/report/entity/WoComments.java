package com.car.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

/**
 * 工单评论
 *
 * <AUTHOR>
 */
@Data
@ToString
@TableName(value = "tt_workorder_comments")
public class WoComments {

    private String uuid;

    private String woNum;

    private String creator;

    private String createTime;

    private String creatorName;

    private String creatorAccount;

    private String remark;
}
