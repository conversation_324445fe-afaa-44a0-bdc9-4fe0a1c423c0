package com.car.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.car.report.mapper.LargeScreenDataMapper;
import com.car.report.service.LargeScreenDataService;
import com.car.report.vo.*;
import com.car.stats.model.LargeScreenDataModel;
import com.car.voc.common.constant.RegionLatitudeAndLongitude;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@DS("starrocks1")
@Slf4j
public class LargeScreenDataServiceImpl implements LargeScreenDataService {


    @Resource
    LargeScreenDataMapper largeScreenDataMapper;

    @Resource
    CCLargeScreenDataUtil ccLargeScreenDataUtil;

    @Override
    public List<BrandListVo> getBrandList(LargeScreenDataModel model) {
        return largeScreenDataMapper.getBrandList(model);
    }


    @Override
    public String getUpdateTime(LargeScreenDataModel model) {
        return LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public BusinessCountVo getBusinessCount(LargeScreenDataModel model) {
        BusinessCountVo businessCountVo = new BusinessCountVo();
        log.info("getBusinessCount接口入参:{}", model);
        MonitorDataVo monitorDataVo = ccLargeScreenDataUtil.vocMonitorData(convertBrandCode(model.getBrandCode()));
        if(ObjectUtils.isEmpty(monitorDataVo)){
            return businessCountVo;
        }
        log.info("getBusinessCount接口返回信息:{}", monitorDataVo);
        businessCountVo.setBusinessCount(monitorDataVo.getTotal_cnt());
        businessCountVo.setOrderCount(monitorDataVo.getWorkorder_cnt());
        businessCountVo.setDispatchCount(monitorDataVo.getOrder_dispatch_cnt());
        businessCountVo.setDispatchRate(parsePercentage(monitorDataVo.getDispatch_rate()) + "");
        businessCountVo.setSatisfaction(parsePercentage(monitorDataVo.getSatisfied_rate()) + "");
        return businessCountVo;
    }


    public static double parsePercentage(String percentageStr) {
        if (percentageStr == null || percentageStr.isEmpty() || percentageStr.equals("-")) {
            return 0.0;
        }
        return Double.parseDouble(percentageStr.replace("%", ""));
    }

    /**
     * 将品牌编码转换为对应的品牌名称，若无匹配则返回空字符串。
     *
     * @param brandCode 品牌编码
     * @return 对应的品牌名称或空字符串
     */
    private String convertBrandCode(String brandCode) {
        if ("allBrand".equals(brandCode)) {
            return "";
        } else if ("A12".equals(brandCode)) {
            return "aeolus";
        } else if ("A13".equals(brandCode)) {
            return "nammi";
        } else if ("A11".equals(brandCode)) {
            return "epi";
        }
        return ""; // 默认情况返回空字符串
    }

    @Override
    public HotlineDataVo getHotlineData(LargeScreenDataModel model) {
        HotlineDataVo hotlineDataVo = new HotlineDataVo();
        log.info("getHotlineData接口入参:{}", model);
        MonitorDataVo monitorDataVo = ccLargeScreenDataUtil.vocMonitorData(convertBrandCode(model.getBrandCode()));
        if(ObjectUtils.isEmpty(monitorDataVo)){
            return hotlineDataVo;
        }
        log.info("getHotlineData接口返回信息:{}", monitorDataVo);
        hotlineDataVo.setCallCount(monitorDataVo.getIvr_cnt());
        hotlineDataVo.setCallCompletionRate(parsePercentage(monitorDataVo.getCti_connect_rate()) + "");
        hotlineDataVo.setOrderCount(monitorDataVo.getCti_workorder_cnt());
        hotlineDataVo.setDispatchRate(parsePercentage(monitorDataVo.getCti_dispatch_rate()) + "");
        hotlineDataVo.setDispatchCount(monitorDataVo.getCti_order_dispatch_cnt());
        hotlineDataVo.setSatisfaction(parsePercentage(monitorDataVo.getCti_satisfied_rate())+"");
        return hotlineDataVo;
    }

    @Override
    public OnlineServiceDataVo getOnlineServiceData(LargeScreenDataModel model) {
        OnlineServiceDataVo onlineServiceDataVo = new OnlineServiceDataVo();
        log.info("getOnlineServiceData接口入参:{}", model);
        MonitorDataVo monitorDataVo = ccLargeScreenDataUtil.vocMonitorData(convertBrandCode(model.getBrandCode()));
        if(ObjectUtils.isEmpty(monitorDataVo)){
            return onlineServiceDataVo;
        }
        log.info("getOnlineServiceData接口返回信息:{}", monitorDataVo);
        onlineServiceDataVo.setCallCount(monitorDataVo.getReception_cnt());
        onlineServiceDataVo.setPassRate(parsePercentage(monitorDataVo.getFirst_response_Qualified_rate()) + "");
        onlineServiceDataVo.setOrderCount(monitorDataVo.getChat_workorder_cnt());
        onlineServiceDataVo.setDispatchRate(parsePercentage(monitorDataVo.getChat_dispatch_rate()) + "");
        onlineServiceDataVo.setDispatchCount(monitorDataVo.getChat_order_dispatch_cnt());
        onlineServiceDataVo.setSatisfaction(parsePercentage(monitorDataVo.getChat_satisfied_rate())+"");
        return onlineServiceDataVo;
    }

    @Override
    public TotalDataVo getTotalData(LargeScreenDataModel model) {
        TotalDataVo totalDataVo = new TotalDataVo();
        log.info("getTotalData接口入参:{}", model);
        MonitorDataVo monitorDataVo = ccLargeScreenDataUtil.vocMonitorData(convertBrandCode(model.getBrandCode()));
        if(ObjectUtils.isEmpty(monitorDataVo)){
            return totalDataVo;
        }
        log.info("getTotalData接口返回信息:{}", monitorDataVo);
        totalDataVo.setBusinessYearCount(monitorDataVo.getTotal_cnt_realtime());
        totalDataVo.setTodayCount(monitorDataVo.getCnt_realtime());
        return totalDataVo;
    }

    @Override
    public MapTopDataVo getMapTopData(LargeScreenDataModel model) {
        if (model.getBrandCode().equals("allBrand")) {
            model.setBrandCode("");
        }
        MapTopDataVo mapTopDataVo = new MapTopDataVo();
        List<MapTopDataInfoVo> mapTopDataInfoVoList = largeScreenDataMapper.getMapTopData(model);
        if (!CollectionUtil.isEmpty(mapTopDataInfoVoList)) {
            for (int i = 0; i < mapTopDataInfoVoList.size(); i++) {
                MapTopDataInfoVo topDataInfoVo = mapTopDataInfoVoList.get(i);
                String[] imageUrls = {
                        "1_1747707764109.png",  // index 0
                        "top2_1747041599530.png",  // index 1
                        "top3_1747041623714.png",  // index 2
                        "top4_1747041647745.png",  // index 3
                        "top5_1747041667968.png"   // index 4
                };

                String[] activeImageUrls = {
                        "a_1747708173644.png",
                        "b_1747708305755.png",
                        "c_1747708345309.png",
                        "d_1747708382228.png",
                        "e_1747708412106.png"
                };

                if (i < imageUrls.length) {
                    String baseUrl = "https://cc.dfes.com.cn/voc/api/sys/common/static/";
                    topDataInfoVo.setSymbol("image://" + baseUrl + imageUrls[i]);
                    topDataInfoVo.setNormalSymbol("image://" + baseUrl + imageUrls[i]);
                    topDataInfoVo.setActiveSymbol("image://" + baseUrl + activeImageUrls[i]);
                    topDataInfoVo.setSymbolSize(Arrays.asList(20, 30));
                }
            }
        }
        //获取省
        String latitudeAndLongitude = largeScreenDataMapper.regionLatitudeAndLongitude();
        log.info("获取地图经纬度：{}", latitudeAndLongitude.length());
        if (CollectionUtil.isNotEmpty(mapTopDataInfoVoList) && StringUtils.isNotBlank(latitudeAndLongitude)) {
            for (MapTopDataInfoVo mapTopDataInfoVo : mapTopDataInfoVoList) {
                List<String> centerByName = getCenterByName(latitudeAndLongitude, mapTopDataInfoVo.getRegionName());
                if (CollectionUtil.isNotEmpty(centerByName)) {
                    mapTopDataInfoVo.setValue(centerByName);
                }
            }
            mapTopDataVo.setMapTopDataInfoVoList(mapTopDataInfoVoList);
        }
        mapTopDataVo.setRegionJson(latitudeAndLongitude);
        return mapTopDataVo;
    }


    public List<String> getCenterByName(String jsonData, String targetName) {
        try {
            Gson gson = new Gson();
            FeatureCollectionVo collection = gson.fromJson(jsonData, FeatureCollectionVo.class);
            return collection.getFeatures().stream()
                    .filter(feature -> targetName.equals(feature.getProperties().getName()))
                    .findFirst()
                    .map(feature -> feature.getProperties().getCenter())
                    .orElse(null);
        } catch (Exception e) {
            log.error("Error processing JSON data", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<WorkOrderTrendVo> workOrderTrend(LargeScreenDataModel model) {
        return largeScreenDataMapper.workOrderTrend(model);
    }

    @Override
    public List<WorkOrderTypeVo> workOrderTypeDistribution(LargeScreenDataModel model) {
        return largeScreenDataMapper.workOrderTypeDistribution(model);
    }

    @Override
    public WorkOrderStatusDataVo workOrderStatusDistribution(LargeScreenDataModel model) {
        List<WorkOrderStatusVo> workOrderStatusVos = largeScreenDataMapper.workOrderStatusDistribution(model);
        WorkOrderStatusDataVo workOrderStatusDataVo = WorkOrderStatusDataVo.builder()
                .workOrderStatusVos1(new ArrayList<>())
                .workOrderStatusVos2(new ArrayList<>())
                .workOrderStatusVos3(new ArrayList<>())
                .build();

        if (CollectionUtil.isEmpty(workOrderStatusVos)) {
            return workOrderStatusDataVo;
        }
        for (WorkOrderStatusVo vo : workOrderStatusVos) {
            List<OrderStatusVo> list = createOrderStatusList(vo);
            if ("allBrand".equals(vo.getBrandCode())) {
                switch (vo.getBrandName()) {
                    case "东风奕派":
                        workOrderStatusDataVo.setWorkOrderStatusVos1(list);
                        break;
                    case "东风风神":
                        workOrderStatusDataVo.setWorkOrderStatusVos2(list);
                        break;
                    case "东风纳米":
                        workOrderStatusDataVo.setWorkOrderStatusVos3(list);
                        break;
                    default:
                        log.warn("Unknown brand name: {}", vo.getBrandName());
                }
            } else {
                workOrderStatusDataVo.setWorkOrderStatusVos1(list);
            }
        }
        return workOrderStatusDataVo;
    }


    private List<OrderStatusVo> createOrderStatusList(WorkOrderStatusVo vo) {
        return Arrays.asList(
                new OrderStatusVo(vo.getStatistic(), vo.getBrandCode(), vo.getBrandName(), "关闭", vo.getClosedC(), vo.getStatisticP()),
                new OrderStatusVo(vo.getStatistic(), vo.getBrandCode(), vo.getBrandName(), "未关闭", vo.getNotClosedC(), vo.getStatisticP())
        );
    }

    @Override
    public List<SalesComplaintTopVo> salesComplaintTop(LargeScreenDataModel model) {
        return largeScreenDataMapper.salesComplaintTop(model);
    }

    @Override
    public List<AfterSalesComplaintTopVo> afterSalesComplaintTop(LargeScreenDataModel model) {
        return largeScreenDataMapper.afterSalesComplaintTop(model);
    }

    @Override
    public List<ProductQualityComplaintTopVo> productQualityComplaintTop(LargeScreenDataModel model) {
        return largeScreenDataMapper.productQualityComplaintTop(model);
    }

    @Override
    public List<ConsultTopVo> consultTop(LargeScreenDataModel model) {
        return largeScreenDataMapper.consultTop(model);
    }

    @Override
    public List<PublicSentimentTopVo> publicSentimentTop(LargeScreenDataModel model) {
        return largeScreenDataMapper.publicSentimentTop(model);
    }

    @Override
    public List<ChannelDistributionVo> channelDistribution(LargeScreenDataModel model) {
        return largeScreenDataMapper.channelDistribution(model);
    }

    @Override
    public List<ChannelTrendVo> channelTrend(LargeScreenDataModel model) {
        return largeScreenDataMapper.channelTrend(model);
    }

    @Override
    public List<AffectiveDistributionDataVo> affectiveDistribution(LargeScreenDataModel model) {
        AffectiveDistributionVo affectiveDistributionVo = largeScreenDataMapper.affectiveDistribution(model);
        if (Objects.nonNull(affectiveDistributionVo)) {
            return Arrays.asList(
                    new AffectiveDistributionDataVo(affectiveDistributionVo.getBrandCode(), affectiveDistributionVo.getBrandName(), "正面", affectiveDistributionVo.getPositiveC(), affectiveDistributionVo.getNsrC(), affectiveDistributionVo.getPositiveP()),
                    new AffectiveDistributionDataVo(affectiveDistributionVo.getBrandCode(), affectiveDistributionVo.getBrandName(), "中性", affectiveDistributionVo.getNeutralC(), affectiveDistributionVo.getNsrC(), affectiveDistributionVo.getNeutralP()),
                    new AffectiveDistributionDataVo(affectiveDistributionVo.getBrandCode(), affectiveDistributionVo.getBrandName(), "负面", affectiveDistributionVo.getNegativeC(), affectiveDistributionVo.getNsrC(), affectiveDistributionVo.getNegativeP()));
        }
        return new ArrayList<>();
    }

    @Override
    public List<FocusAttentionVo> focusAttention(LargeScreenDataModel model) {
        return largeScreenDataMapper.focusAttention(model);
    }

    @Override
    public List<FocusSeriesVo> focusSeries(LargeScreenDataModel model) {
        return largeScreenDataMapper.focusSeries(model);
    }

    @Override
    public List<ConsultingMentionTopVo> consultingMentionTop(LargeScreenDataModel model) {
        if (StringUtils.isNotBlank(model.getType()) && "1".equals(model.getType())) {
            return largeScreenDataMapper.consultingMentionTop(model);
        } else {
            return largeScreenDataMapper.consultingSurgeTop(model);
        }
    }

    @Override
    public List<LabelClassificationTopVo> labelClassificationTop(LargeScreenDataModel model) {
        List<LabelClassificationTopVo> dataList = largeScreenDataMapper.labelClassificationTop(model);
        Map<String, Optional<LabelClassificationTopVo>> resultMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        LabelClassificationTopVo::getName,
                        Collectors.maxBy(Comparator.comparingInt(LabelClassificationTopVo::getStatistic))
                ));
        List<LabelClassificationTopVo> finalResult = resultMap.values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get).sorted(Comparator.comparingInt(LabelClassificationTopVo::getStatistic).reversed()).collect(Collectors.toList());
        return finalResult;
    }

    @Override
    public List<UserFavorableCommentVo> userFavorableComment(LargeScreenDataModel model) {
        return largeScreenDataMapper.userFavorableComment(model);
    }

    @Override
    public List<UserBadCommentVo> userBadComment(LargeScreenDataModel model) {
        return largeScreenDataMapper.userBadComment(model);
    }
}
