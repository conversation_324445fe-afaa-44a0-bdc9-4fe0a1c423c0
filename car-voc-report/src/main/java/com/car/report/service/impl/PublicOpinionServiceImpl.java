package com.car.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.car.report.entity.PublicOpinionEntity;
import com.car.report.entity.WoComments;
import com.car.report.mapper.PublicOpinionMapper;
import com.car.report.mapper.WOCommentsMapper;
import com.car.report.model.PublicOpinionModel;
import com.car.report.service.PublicOpinionService;
import com.car.report.service.converts.PublicOpinionConvertService;
import com.car.report.vo.PublicOpinionVo;
import com.car.stats.model.FilterCriteriaModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 舆情数据(TtPublicOpinion)表业务层
 *
 * <AUTHOR>
 * @since 2024-12-18 17:20:54
 */
@Service
@AllArgsConstructor
public class PublicOpinionServiceImpl extends ServiceImpl<PublicOpinionMapper, PublicOpinionEntity> implements PublicOpinionService {

    private final PublicOpinionConvertService convertService;

    private final WOCommentsMapper woCommentsMapper;

    private QueryWrapper<PublicOpinionEntity> createQueryWrapper(PublicOpinionModel model) {
        PublicOpinionEntity entity = convertService.convertTo(model);
        QueryWrapper<PublicOpinionEntity> queryWrapper = new QueryWrapper<>(entity);
        LambdaQueryWrapper<PublicOpinionEntity> lambdaQueryWrapper = queryWrapper.lambda();
        return queryWrapper;
    }

    @Override
    public IPage<PublicOpinionVo> queryBySelect(FilterCriteriaModel model) {
        IPage<PublicOpinionVo> page = new Page<>(model.getPageNo(), model.getPageSize());
        IPage<PublicOpinionVo> entityList = this.baseMapper.queryBySelectPage(page, model);
        if (CollUtil.isEmpty(entityList.getRecords())) {
            return entityList;
        }
        List<String> ids = entityList.getRecords().stream().map(PublicOpinionVo::getWoNum).collect(Collectors.toList());
        List<WoComments> woComments = new ArrayList<>();

        if (ids.size() > 1000) {
            // 分批查询
            int batch = ids.size() / 1000 + 1;
            for (int i = 0; i < batch; i++) {
                List<String> subList = ids.subList(i * 1000, (i + 1) * 1000);
                LambdaQueryWrapper<WoComments> queryWrapper = new QueryWrapper<WoComments>().lambda();
                queryWrapper.in(WoComments::getWoNum, subList);
                woComments.addAll(woCommentsMapper.selectList(queryWrapper));
            }
        } else {
            LambdaQueryWrapper<WoComments> queryWrapper = new QueryWrapper<WoComments>().lambda();
            queryWrapper.in(WoComments::getWoNum, ids);
            woComments.addAll(woCommentsMapper.selectList(queryWrapper));
        }
        AtomicInteger count = new AtomicInteger(1);
        entityList.getRecords().forEach(e -> {
            List<WoComments> comments = woComments.stream().filter(c -> c.getWoNum().equals(e.getWoNum())).collect(Collectors.toList());
            e.setWoComments(comments);
            e.setIndex(count.getAndIncrement());
        });
        return entityList;
    }


}
