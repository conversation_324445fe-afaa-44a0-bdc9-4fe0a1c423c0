package com.car.report.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.car.report.vo.MonitorDataVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @创建者: fanrong
 * @创建时间: 2025/6/3 17:50
 * @描述:
 **/
@Slf4j
@Service
public class CCLargeScreenDataUtil {
    @Value("${configuration.tokenCheckToken.clientId}")
    String clientId;
    @Value("${configuration.tokenCheckToken.clientSecret}")
    String clientSecret;
    @Value("${configuration.tokenCheckToken.baseUrl}")
    String baseUrl;
    @Value("${large.url}")
    String largeUrl;



    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private String GetToken() {
        String url = baseUrl + "/oauth2/oauth/token";
        String requestBody = "grant_type=client_credentials&client_id=" + clientId + "&client_secret=" + clientSecret;
        HttpRequest request = HttpUtil.createPost(url);
        request.header("Content-Type", "application/x-www-form-urlencoded");
        request.body(requestBody);
        HttpResponse response = request.execute();
        log.info("获取token返回结果：{}", JSONUtil.parseObj(response.body()));
        return JSONUtil.parseObj(response.body()).getStr("access_token");
    }


    public MonitorDataVo vocMonitorData(String brandCode) {
        try {
            String cacheKey = "voc_monitor_data:brandCode:" + brandCode;
            // 先从 Redis 获取缓存数据
            String cached = stringRedisTemplate.opsForValue().get(cacheKey);
            if (!StringUtils.isEmpty(cached)) {
                MonitorDataVo monitorDataVo = JSONUtil.toBean(cached, MonitorDataVo.class);
                log.info("从缓存中获取数据：{}", monitorDataVo);
                return monitorDataVo;
            }
            String url = largeUrl + "/api/dashboard/voc-monitor-data";
            HttpRequest request = HttpUtil.createPost(url);
            request.header("Content-Type", "application/json");
            request.header("Authorization", "Bearer " + GetToken());
            Map<String, Object> requestBody = JSONUtil.createObj()
                    .put("brandCode", brandCode);
            String jsonBody = JSONUtil.toJsonStr(requestBody);
            request.body(jsonBody);
            HttpResponse response = request.execute();
            log.info("获取vocMonitorData返回结果：{}", JSONUtil.parseObj(response.body()));
            String body = response.body();
            MonitorDataVo monitorDataVo = JSONUtil.toBean(body, MonitorDataVo.class);
            // 将结果写入 Redis，缓存 5 分钟
            stringRedisTemplate.opsForValue().set(cacheKey, JSONUtil.toJsonStr(monitorDataVo), 5, TimeUnit.MINUTES);
            return monitorDataVo;
        } catch (Exception e) {
            log.error("获取vocMonitorData异常", e);
            return null;
        }
    }
}
