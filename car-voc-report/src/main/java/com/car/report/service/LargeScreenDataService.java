package com.car.report.service;

import com.car.report.vo.*;
import com.car.stats.model.LargeScreenDataModel;

import java.util.List;

public interface LargeScreenDataService {

    List<BrandListVo> getBrandList(LargeScreenDataModel model);


    String getUpdateTime(LargeScreenDataModel model);

    BusinessCountVo getBusinessCount(LargeScreenDataModel model);

    HotlineDataVo getHotlineData(LargeScreenDataModel model);

    OnlineServiceDataVo getOnlineServiceData(LargeScreenDataModel model);

    TotalDataVo getTotalData(LargeScreenDataModel model);

    MapTopDataVo getMapTopData(LargeScreenDataModel model);

    List<WorkOrderTrendVo> workOrderTrend(LargeScreenDataModel model);

    List<WorkOrderTypeVo> workOrderTypeDistribution(LargeScreenDataModel model);

    WorkOrderStatusDataVo workOrderStatusDistribution(LargeScreenDataModel model);

    List<SalesComplaintTopVo> salesComplaintTop(LargeScreenDataModel model);

    List<AfterSalesComplaintTopVo> afterSalesComplaintTop(LargeScreenDataModel model);

    List<ProductQualityComplaintTopVo> productQualityComplaintTop(LargeScreenDataModel model);

    List<ConsultTopVo> consultTop(LargeScreenDataModel model);

    List<PublicSentimentTopVo> publicSentimentTop(LargeScreenDataModel model);

    List<ChannelDistributionVo> channelDistribution(LargeScreenDataModel model);

    List<ChannelTrendVo> channelTrend(LargeScreenDataModel model);

    List<AffectiveDistributionDataVo> affectiveDistribution(LargeScreenDataModel model);

    List<FocusAttentionVo> focusAttention(LargeScreenDataModel model);

    List<FocusSeriesVo> focusSeries(LargeScreenDataModel model);

    List<ConsultingMentionTopVo> consultingMentionTop(LargeScreenDataModel model);

    List<LabelClassificationTopVo> labelClassificationTop(LargeScreenDataModel model);

    List<UserFavorableCommentVo> userFavorableComment(LargeScreenDataModel model);

    List<UserBadCommentVo> userBadComment(LargeScreenDataModel model);

    TotalClosureRateVo workOrderTotalAndClosureRate(LargeScreenDataModel model);

}
