package com.car.report.service.converts;


import com.car.report.entity.PublicOpinionEntity;
import com.car.report.model.PublicOpinionModel;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.List;


/**
 * 舆情数据(TtPublicOpinion)转换类
 *
 * <AUTHOR>
 * @since 2024-12-18 17:20:53
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface PublicOpinionConvertService {

    PublicOpinionModel convertTo(PublicOpinionEntity o);

    PublicOpinionEntity convertTo(PublicOpinionModel o);

    List<PublicOpinionEntity> convertModelToList(List<PublicOpinionModel> model);

    List<PublicOpinionModel> convertEntityToList(List<PublicOpinionEntity> entity);



}

