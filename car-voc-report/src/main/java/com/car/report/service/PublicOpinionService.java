package com.car.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.car.report.model.PublicOpinionModel;
import com.car.report.vo.PublicOpinionVo;
import com.car.stats.model.FilterCriteriaModel;

/**
 * 舆情数据(TtPublicOpinion)表接口服务层
 *
 * <AUTHOR>
 * @since 2024-12-18 17:20:54
 */
public interface PublicOpinionService {


    /**
     * 分页查询
     *
     * @param model 筛选条件
     * @return 查询结果
     */
    IPage<PublicOpinionVo> queryBySelect(FilterCriteriaModel model);


}
