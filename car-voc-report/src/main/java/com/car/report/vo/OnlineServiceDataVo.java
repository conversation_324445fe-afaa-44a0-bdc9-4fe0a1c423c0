package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "OnlineServiceDataVo", description = "在线客服")
public class OnlineServiceDataVo implements Serializable {


    @ApiModelProperty(value = "来电量")
    private String callCount;

    @ApiModelProperty(value = "首响合格率")
    private String passRate;

    @ApiModelProperty(value = "工单量")
    private String orderCount;

    @ApiModelProperty(value = "派单率")
    private String dispatchRate;

    @ApiModelProperty(value = "派单量")
    private String dispatchCount;

    @ApiModelProperty(value = "满意度")
    private String satisfaction;


}
