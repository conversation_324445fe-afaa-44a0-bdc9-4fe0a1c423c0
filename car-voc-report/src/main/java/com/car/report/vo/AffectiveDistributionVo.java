package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AffectiveDistributionVo", description = "情感分布")
public class AffectiveDistributionVo implements Serializable {


    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "正面数量")
    private String positiveC;

    @ApiModelProperty(value = "负面数量")
    private String negativeC;

    @ApiModelProperty(value = "中性数量")
    private String neutralC;

    @ApiModelProperty(value = "体验指数")
    private String nsrC;

    @ApiModelProperty(value = "正面占比")
    private String positiveP;

    @ApiModelProperty(value = "负面占比")
    private String negativeP;

    @ApiModelProperty(value = "中性占比")
    private String neutralP;
}
