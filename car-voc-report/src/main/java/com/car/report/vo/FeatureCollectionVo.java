package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FeatureCollectionVo", description = "省经纬度")
public class FeatureCollectionVo implements Serializable {

    private List<Feature> features;

    private String type;

    @Data
    public static class Feature {
        private Properties properties;
    }

    @Data
    public static class Properties {
        private String name;

        private List<String> center;
    }

}
