package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FocusSeriesVo", description = "关注车系排行")
public class FocusSeriesVo implements Serializable {


    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "工单数量")
    private String statistic;

    @ApiModelProperty(value = "工单数量")
    private String seriesImg;

    @ApiModelProperty(value = "笑脸图片")
    private String nsrG;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "占比")
    private String statisticR;

    @ApiModelProperty(value = "体验指数")
    private String nsrC;
}
