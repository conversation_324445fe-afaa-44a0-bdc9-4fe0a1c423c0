package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MapTopDataVo", description = "地图Top")
public class MapTopDataVo implements Serializable {

    @ApiModelProperty(value = "区域经纬度")
    private String regionJson;

    @ApiModelProperty(value = "地图明细数据")
    private List<MapTopDataInfoVo> mapTopDataInfoVoList;

}
