package com.car.report.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorDataVo implements Serializable {


    @ApiModelProperty(value = "在线客服-派单率")
    private String chat_dispatch_rate;

    @ApiModelProperty(value = "在线客服-派单量")
    private String chat_order_dispatch_cnt;

    @ApiModelProperty(value = "在线客服-满意度")
    private String chat_satisfied_rate;

    @ApiModelProperty(value = "在线客服-工单量")
    private String chat_workorder_cnt;

    @ApiModelProperty(value = "当日总量")
    private String cnt_realtime;
    @ApiModelProperty(value = "400热线-接通率")
    private String cti_connect_rate;
    @ApiModelProperty(value = "400热线-派单率")
    private String cti_dispatch_rate;
    @ApiModelProperty(value = "400热线-派单量")
    private String cti_order_dispatch_cnt;
    @ApiModelProperty(value = "400热线-满意度")
    private String cti_satisfied_rate;
    @ApiModelProperty(value = "400热线-工单量")
    private String cti_workorder_cnt;
    @ApiModelProperty(value = "业务总量-派单率")
    private String dispatch_rate;
    @ApiModelProperty(value = "在线客服-首响合格率")
    private String first_response_Qualified_rate;
    @ApiModelProperty(value = "400热线-来电量")
    private String ivr_cnt;
    @ApiModelProperty(value = "业务总量-派单量")
    private String order_dispatch_cnt;
    @ApiModelProperty(value = "在线客服-接待量")
    private String reception_cnt;
    @ApiModelProperty(value = "业务总量-满章度")
    private String satisfied_rate;
    @ApiModelProperty(value = "业务总量-业务总量")
    private String total_cnt;
    @ApiModelProperty(value = "业务总量年度")
    private String total_cnt_realtime;
    @ApiModelProperty(value = "业务总量-工单量")
    private String workorder_cnt;

}
