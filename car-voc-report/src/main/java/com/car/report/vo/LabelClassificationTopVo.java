package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "LabelClassificationTopVo", description = "分类标签TOP")
public class LabelClassificationTopVo implements Serializable {


    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "工单数量")
    private Integer statistic;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "三级名称")
    private String threeName;

    @ApiModelProperty(value = "占比")
    private String statisticR;

    @ApiModelProperty(value = "占比")
    private String nsrC;
}
