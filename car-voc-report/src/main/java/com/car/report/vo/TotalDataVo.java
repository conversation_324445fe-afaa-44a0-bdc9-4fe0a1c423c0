package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TotalDataVo", description = "业务总量年度,当日总量")
public class TotalDataVo implements Serializable {


    @ApiModelProperty(value = "业务总量年度")
    private String  businessYearCount;

    @ApiModelProperty(value = "当日总量")
    private String todayCount;


}
