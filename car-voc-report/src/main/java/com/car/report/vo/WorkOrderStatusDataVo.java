package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "WorkOrderStatusDataVo", description = "工单状态分布数据")
public class WorkOrderStatusDataVo implements Serializable {

    private List<OrderStatusVo> workOrderStatusVos1;

    private List<OrderStatusVo> workOrderStatusVos2;

    private List<OrderStatusVo> workOrderStatusVos3;

}
