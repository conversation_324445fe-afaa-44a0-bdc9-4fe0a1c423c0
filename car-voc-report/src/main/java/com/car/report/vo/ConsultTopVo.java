package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ConsultTopVo", description = "咨询工单问题TOP")
public class ConsultTopVo implements Serializable {

    @ApiModelProperty(value = "工单数量")
    private String statistic;

    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "占比")
    private String statisticR;
}
