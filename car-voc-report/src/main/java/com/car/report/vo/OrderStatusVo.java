package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "WorkOrderStatusVo", description = "工单状态分布")
public class OrderStatusVo implements Serializable {

    @ApiModelProperty(value = "工单数量")
    private String totalStatistic;

    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "关闭数量")
    private String Statistic;

    @ApiModelProperty(value = "占比")
    private String statisticP;

}
