package com.car.report.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.car.report.entity.WoComments;
import com.car.voc.annotation.DataDesensitization;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.joda.time.DateTimeConstants.SECONDS_PER_HOUR;
import static org.joda.time.DateTimeConstants.SECONDS_PER_MINUTE;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TtPublicOpinion", description = "舆情数据")
public class PublicOpinionVo implements Serializable {


    /**
     * 工单编号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号")
    private Integer index;
    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @ExcelIgnore
    private String woNum;


    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    @ExcelProperty(value = "品牌")
    private String brand;


    /**
     * 车系
     */
    @ApiModelProperty(value = "车系")
    @ExcelProperty(value = "车系")
    private String carSeries;


    /**
     * 工单状态（全部/未关闭/已关闭）
     */
    @ApiModelProperty(value = "工单状态（全部/未关闭/已关闭）")
    @ExcelProperty(value = "工单状态")
    private String status;


    /**
     * 舆情分类（客诉舆情/媒体舆情）
     */
    @ApiModelProperty(value = "舆情分类（客诉舆情/媒体舆情）")
    @ExcelProperty(value = "舆情分类")
    private String channelSource3;

    /**
     * 处理方式（电话、在线等）
     */
    @ApiModelProperty(value = "处理节点")
    @ExcelProperty(value = "处理节点")
    private String handlingMethod;

    /**
     * 处理方式（电话、在线等）
     */
    @ApiModelProperty(value = "满意度信息-客户是否满意")
    @ExcelIgnore
    private String customerSatisfaction;

    @ApiModelProperty(value = "满意度信息-不满意原因分类")
    @ExcelIgnore
    private String dissatisfactionCategory;

    @ApiModelProperty(value = "满意度信息")
    @ExcelProperty(value = "满意度信息")
    private String customerSatisfactionInfo;


    @ApiModelProperty(value = "回访信息-是否回访客户")
    @ExcelIgnore
    private String isFollowupCustomer;

    @ApiModelProperty(value = "回访信息-首次回访时间")
    @ExcelIgnore
    private String firstFollowupTime;

    @ApiModelProperty(value = "回访信息-解决情况")
    @ExcelIgnore
    private String resolutionStatus;

    @ApiModelProperty(value = "回访信息-是否一次解决")
    @ExcelIgnore
    private String isOneTimeResolution;

    @ApiModelProperty(value = "回访信息-解决方式")
    @ExcelIgnore
    private String resolutionMethod;

    @ApiModelProperty(value = "回访信息-未一次解决原因")
    @ExcelIgnore
    private String reason;

    @ApiModelProperty(value = "回访信息")
    @ExcelProperty(value = "回访信息")
    private String followupInfo;

    @ApiModelProperty(value = "工单评论（全部工单评论信息，需要时间信息，用：分割不同时间，不同人的评论，倒序）暂无")
    @ExcelProperty(value = "工单评论")
    private String woComment;

    @ApiModelProperty(value = "工单评论（全部工单评论信息，需要时间信息，用：分割不同时间，不同人的评论，倒序）暂无")
    @ExcelIgnore
    private List<WoComments> woComments;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 工单内容
     */
    @ApiModelProperty(value = "工单内容")
    @ExcelProperty(value = "工单内容")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    private String woContent;


    /**
     * 客户背景（姓名、电话、车架号、车牌等）
     */
    @ApiModelProperty(value = "客户背景:姓名（姓名、电话、车架号、车牌等）")
    @ExcelProperty(value = "客户背景")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.PHONE, DataDesensitization.DesensitizationType.VIN})
    private String customerName;

    /**
     * 媒体名称（如抖音、车质网等）
     */
    @ApiModelProperty(value = "媒体名称（如抖音、车质网等）")
    @ExcelProperty(value = "媒体名称")
    private String media;


    /**
     * 作者（舆情内容发布人）
     */
    @ApiModelProperty(value = "作者（舆情内容发布人）")
    @ExcelProperty(value = "作者")
    @DataDesensitization(type = {DataDesensitization.DesensitizationType.NAME})
    private String author;

    /**
     * 链接（舆情来源链接URL）
     */
    @ApiModelProperty(value = "链接（舆情来源链接URL）")
    @ExcelProperty(value = "链接")
    private String uri;


    /**
     * 媒体类型（如网站、APP等）
     */
    @ApiModelProperty(value = "媒体类型（如网站、APP等）")
    @ExcelProperty(value = "媒体类型")
    private String mediaType;

    /**
     * 评级（如全部、一级、二级、三级）
     */
    @ApiModelProperty(value = "评级（如全部、一级、二级、三级）")
    @ExcelProperty(value = "评级")
    private String rating;

    /**
     * 用户发布时间（涉及时间均改为24小时制）
     */
    @ApiModelProperty(value = "用户发布时间（涉及时间均改为24小时制）")
    @ExcelProperty(value = "用户发布时间")
    private Date publisDate;

    /**
     * 预警发出时间（舆情创建工单后派发出去的时间）
     */
    @ApiModelProperty(value = "舆情推送时间（新智）")
    @ExcelProperty(value = "舆情推送时间（新智）")
    private Date fileTime;

    /**
     * 预警发出时间（舆情创建工单后派发出去的时间）
     */
    @ApiModelProperty(value = "预警发出时间（取系统中建工单的时间）")
    @ExcelProperty(value = "预警发出时间")
    private Date createTime;

    /**
     * 预警时长（预警发出时间 - 发布时间）
     */
    @ApiModelProperty(value = "预警时长（预警发出时间 - 用户发布时间）")
    @ExcelIgnore
    private Long warningDuration;

    /**
     * 预警时长（预警发出时间 - 发布时间）
     */
    @ApiModelProperty(value = "预警时长（预警发出时间 - 发布时间）")
    @ExcelProperty(value = "预警时长")
    private String warningDurationStr;

    /**
     * 初步反馈时间（工单派发后，处理人员第一次反馈时间）
     */
    @ApiModelProperty(value = "初步反馈时间（工单派发后，处理人员第一次反馈时间）")
    @ExcelProperty(value = "初步反馈时间")
    private Date dlrFirstFeedbackTime;


    /**
     * 闭环时间（工单关闭时间）
     */
    @ApiModelProperty(value = "闭环时间（工单关闭时间）")
    @ExcelProperty(value = "闭环时间")
    private Date closeTime;

    /**
     * 平均响应时长
     */
    @ApiModelProperty(value = "平均响应时长")
    @ExcelIgnore
    private Long averageResponseTime;

    @ApiModelProperty(value = "平均响应时长")
    @ExcelProperty(value = "平均响应时长")
    private String averageResponseTimeStr;

    /**
     * 响应率（初步反馈工单占比）
     */
    @ApiModelProperty(value = "响应率（初步反馈工单占比）")
    @ExcelIgnore
    private BigDecimal responseRate;

    @ExcelProperty(value = "响应率")
    private String responseRateStr;

    /**
     * 完结率（已关闭工单占比）
     */
    @ApiModelProperty(value = "完结率（已关闭工单占比）")
    @ExcelIgnore
    private BigDecimal closureRate;

    @ExcelProperty(value = "完结率")
    private String closureRateStr;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "工单编号")
    private String code;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    @ExcelProperty(value = "车型")
    private String modelName;


    public String getCustomerSatisfactionInfo() {
        if (StrUtil.isBlank(customerSatisfaction) && StrUtil.isBlank(dissatisfactionCategory)) {
            return "";
        }
        return String.format("客户是否满意：%s；不满意原因分类：%s；",
                Objects.toString(customerSatisfaction, " "),
                Objects.toString(dissatisfactionCategory, " "));
    }

    public String getFollowupInfo() {
        if (StrUtil.isBlank(isFollowupCustomer) && StrUtil.isBlank(firstFollowupTime) && StrUtil.isBlank(resolutionStatus) &&
                StrUtil.isBlank(isOneTimeResolution) && StrUtil.isBlank(resolutionMethod) && StrUtil.isBlank(reason)) {
            return "";
        }
        return String.format("是否回访客户：%s；首次回访时间：%s；解决情况：%s；是否一次解决：%s；解决方式：%s；未一次解决原因：%s；",
                Objects.toString(isFollowupCustomer, " "),
                Objects.toString(firstFollowupTime, " "),
                Objects.toString(resolutionStatus, " "),
                Objects.toString(isOneTimeResolution, " "),
                Objects.toString(resolutionMethod, " "),
                Objects.toString(reason, " ")
        );
    }

    public String getWoComment() {
        if (CollUtil.isEmpty(woComments)) {
            return "";
        }
        List<WoComments> collect = woComments.stream()
                .sorted(Comparator.comparing(WoComments::getCreateTime).reversed())
                .collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        collect.forEach(e -> {
            sb.append(e.getCreatorName()).append(":");
            sb.append(e.getCreateTime()).append(":");
            sb.append(e.getRemark());
            sb.append(";");
        });
        return sb.toString();
    }

    public String getAverageResponseTimeStr() {
        return getStringDate(averageResponseTime);
    }

    private String getStringDate(Long date) {
        if (date == null) {
            return "";
        }
        // 直接计算总小时数
        long hours = date / SECONDS_PER_HOUR;
        long minutes = (date % SECONDS_PER_HOUR) / SECONDS_PER_MINUTE;
        long seconds = date % SECONDS_PER_MINUTE;

        if (hours > 0) {
            return String.format("%d'%02d'%02d", hours, minutes, seconds);
        }
        if (minutes > 0) {
            return String.format("%02d'%02d", minutes, seconds);
        }
        if (seconds > 0) {
            return String.format("%02d", seconds);
        }
        return "";
    }

    public String getWarningDurationStr() {
        return getStringDate(warningDuration);
    }

    public String getResponseRateStr() {
        if (responseRate == null) {
            return "";
        }
        DecimalFormat df = new DecimalFormat("0.00%");
        return df.format(responseRate);
    }

    public String getClosureRateStr() {
        if (closureRate == null) {
            return "";
        }
        DecimalFormat df = new DecimalFormat("0.00%");
        return df.format(closureRate);
    }

}
