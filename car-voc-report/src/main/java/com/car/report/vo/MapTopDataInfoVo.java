package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.swing.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MapTopDataInfoVo", description = "地图明细Top")
public class MapTopDataInfoVo implements Serializable {

    @ApiModelProperty(value = "区域名称")
    private String regionName;

    @ApiModelProperty(value = "声音总量")
    private String total;

    @ApiModelProperty(value = "咨询数量")
    private String consultingCount;

    @ApiModelProperty(value = "投诉数量")
    private String complaintCount;

    @ApiModelProperty(value = "建议数量")
    private String suggestionCount;

    @ApiModelProperty(value = "表扬数量")
    private String praiseCount;

    @ApiModelProperty(value = "其他数量")
    private String otherCount;

    @ApiModelProperty(value = "经纬度")
    private List<String> value;

    private String symbol;

    private List<Integer> symbolSize;

    private String activeSymbol;

    private String normalSymbol;

}
