package com.car.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TotalClosureRateVo", description = "工单状态分布总工单量,关闭率")
public class TotalClosureRateVo implements Serializable {


    @ApiModelProperty(value = "品牌Code")
    private String brandCode;

    @ApiModelProperty(value = "工单总量")
    private String statisticSum;

    @ApiModelProperty(value = "关闭率")
    private String closedRate;
}
