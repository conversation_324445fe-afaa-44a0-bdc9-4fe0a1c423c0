package com.car.report.controller;


import com.car.report.service.LargeScreenDataService;
import com.car.report.vo.*;
import com.car.stats.model.LargeScreenDataModel;
import com.car.voc.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "大屏数据展示")
@RequestMapping("/largeScreenData")
@AllArgsConstructor
public class LargeScreenDataController {
    /**
     * 服务对象
     */
    @Resource
    private LargeScreenDataService largeScreenDataService;

    @ApiOperation(value = "东风卓联大屏-品牌下拉")
    @PostMapping("/getBrandList")
    public Result<List<BrandListVo>> getBrandList(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getBrandList(model));
    }


    @ApiOperation(value = "东风卓联大屏-获取更新时间")
    @PostMapping("/getUpdateTime")
    public Result<String> getUpdateTime(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getUpdateTime(model));
    }

    @ApiOperation(value = "东风卓联大屏-业务总量")
    @PostMapping("/getBusinessCount")
    public Result<BusinessCountVo> getBusinessCount(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getBusinessCount(model));
    }


    @ApiOperation(value = "东风卓联大屏-400热线")
    @PostMapping("/getHotlineData")
    public Result<HotlineDataVo> getHotlineData(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getHotlineData(model));
    }


    @ApiOperation(value = "东风卓联大屏-在线客服")
    @PostMapping("/getOnlineServiceData")
    public Result<OnlineServiceDataVo> getOnlineServiceData(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getOnlineServiceData(model));
    }


    @ApiOperation(value = "东风卓联大屏-业务总量年度,当日总量")
    @PostMapping("/getTotalData")
    public Result<TotalDataVo> getTotalData(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getTotalData(model));
    }


    @ApiOperation(value = "东风卓联大屏-地图Top")
    @PostMapping("/getMapTopData")
    public Result<MapTopDataVo> getMapTopData(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.getMapTopData(model));
    }


    @ApiOperation(value = "东风卓联大屏-工单趋势变化")
    @PostMapping("/workOrderTrend")
    public Result<List<WorkOrderTrendVo>> workOrderTrend(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.workOrderTrend(model));
    }

    @ApiOperation(value = "东风卓联大屏-工单类型分布")
    @PostMapping("/workOrderTypeDistribution")
    public Result<List<WorkOrderTypeVo>> workOrderTypeDistribution(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.workOrderTypeDistribution(model));
    }

    @ApiOperation(value = "东风卓联大屏-工单状态分布")
    @PostMapping("/workOrderStatusDistribution")
    public Result<WorkOrderStatusDataVo> workOrderStatusDistribution(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.workOrderStatusDistribution(model));
    }

    @ApiOperation(value = "东风卓联大屏-售时投诉工单问题TOP")
    @PostMapping("/salesComplaintTop")
    public Result<List<SalesComplaintTopVo>> salesComplaintTop(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.salesComplaintTop(model));
    }


    @ApiOperation(value = "东风卓联大屏-售后投诉工单问题TOP")
    @PostMapping("/afterSalesComplaintTop")
    public Result<List<AfterSalesComplaintTopVo>> afterSalesComplaintTop(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.afterSalesComplaintTop(model));
    }


    @ApiOperation(value = "东风卓联大屏-产品质量投诉工单问题TOP")
    @PostMapping("/productQualityComplaintTop")
    public Result<List<ProductQualityComplaintTopVo>> productQualityComplaintTop(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.productQualityComplaintTop(model));
    }


    @ApiOperation(value = "东风卓联大屏-咨询工单问题TOP")
    @PostMapping("/consultTop")
    public Result<List<ConsultTopVo>> consultTop(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.consultTop(model));
    }


    @ApiOperation(value = "东风卓联大屏-舆情工单问题TOP")
    @PostMapping("/publicSentimentTop")
    public Result<List<PublicSentimentTopVo>> publicSentimentTop(@RequestBody @Validated LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.publicSentimentTop(model));
    }


    @ApiOperation(value = "东风卓联大屏-渠道分布")
    @PostMapping("/channelDistribution")
    public Result<List<ChannelDistributionVo>> channelDistribution(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.channelDistribution(model));
    }

    @ApiOperation(value = "东风卓联大屏-渠道分布趋势图")
    @PostMapping("/channelTrend")
    public Result<List<ChannelTrendVo>> channelTrend(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.channelTrend(model));
    }


    @ApiOperation(value = "东风卓联大屏-情感分布")
    @PostMapping("/affectiveDistribution")
    public Result<List<AffectiveDistributionDataVo>> affectiveDistribution(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.affectiveDistribution(model));
    }

    @ApiOperation(value = "东风卓联大屏-聚焦关注分类")
    @PostMapping("/focusAttention")
    public Result<List<FocusAttentionVo>> focusAttention(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.focusAttention(model));
    }


    @ApiOperation(value = "东风卓联大屏-关注车系排行")
    @PostMapping("/focusSeries")
    public Result<List<FocusSeriesVo>> focusSeries(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.focusSeries(model));
    }


    @ApiOperation(value = "东风卓联大屏-咨询排行TOP")
    @PostMapping("/consultingMentionTop")
    public Result<List<ConsultingMentionTopVo>> consultingMentionTop(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.consultingMentionTop(model));
    }

    @ApiOperation(value = "东风卓联大屏-分类标签TOP")
    @PostMapping("/labelClassificationTop")
    public Result<List<LabelClassificationTopVo>> labelClassificationTop(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.labelClassificationTop(model));
    }


    @ApiOperation(value = "东风卓联大屏-用户好评TOP")
    @PostMapping("/userFavorableComment")
    public Result<List<UserFavorableCommentVo>> userFavorableComment(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.userFavorableComment(model));
    }


    @ApiOperation(value = "东风卓联大屏-用户差评TOP")
    @PostMapping("/userBadComment")
    public Result<List<UserBadCommentVo>> userBadComment(@RequestBody LargeScreenDataModel model) {
        return Result.OK(largeScreenDataService.userBadComment(model));
    }

}

