package com.car.report.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.report.service.PublicOpinionService;
import com.car.report.vo.PublicOpinionVo;
import com.car.stats.model.FilterCriteriaModel;
import com.car.stats.vo.popvo.UserListInfoVo;
import com.car.voc.common.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.SpreadsheetVersion;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.List;

/**
 * 舆情数据(TtPublicOpinion)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-18 17:20:53
 */
@RestController
@Api(tags = "舆情数据")
@RequestMapping("/ttPublicOpinion")
@AllArgsConstructor
public class PublicOpinionController {
    /**
     * 服务对象
     */
    private final PublicOpinionService publicOpinionService;

    /**
     * 分页查询所有数据
     *
     * @param model 查询实体
     * @return 所有数据
     */
    @ApiOperation(value = "舆情数据-分页查询")
    @PostMapping("/list")
    public Result<IPage<PublicOpinionVo>> selectAll(@RequestBody FilterCriteriaModel model) {
        return Result.OK(this.publicOpinionService.queryBySelect(model));
    }

    @ApiOperation(value = "舆情数据-导出数据", produces = "application/octet-stream")
    @PostMapping("/listExportXls")
    public void listExportXls(@RequestBody FilterCriteriaModel model, HttpServletResponse response) throws JsonProcessingException {
        model.setPageSize(500000);
        IPage<PublicOpinionVo> res = this.publicOpinionService.queryBySelect(model);
        ObjectMapper objectMapper = new ObjectMapper();
        String s = objectMapper.writeValueAsString(res);
        Page<PublicOpinionVo> popUpVo = objectMapper.readValue(s, new TypeReference<Page<PublicOpinionVo>>(){});
        excelExportXls(popUpVo.getRecords(), "舆情数据", response, PublicOpinionVo.class);
    }

    private void excelExportXls(List records, String filename, HttpServletResponse response, Class head) {
        response.setCharacterEncoding("utf-8");
        String fileName;
        // 导出数据前调用
        initCellMaxTextLength();
        try {
            response.setContentType("application/vnd.ms-excel");
            fileName = URLEncoder.encode(filename, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), head).excelType(ExcelTypeEnum.XLSX).sheet(filename).doWrite(records);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void initCellMaxTextLength() {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}

