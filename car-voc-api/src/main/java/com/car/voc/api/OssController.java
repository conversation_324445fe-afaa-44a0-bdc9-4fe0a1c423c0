package com.car.voc.api;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.car.voc.common.Result;
import com.car.voc.common.oss.OssBootUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api(tags="阿里云oss获取stsToken")
@RequestMapping("/sys/oss")
public class OssController {



	@GetMapping("/getStsToken")
	public Result<?> getStsToken()  {

		try {
			return Result.OK(Base64.encode(JSONUtil.toJsonStr(OssBootUtil.stsToken()),"UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return Result.error("失败！");
	}



}
