package com.car.voc.api;

import cn.hutool.core.util.RandomUtil;
import com.car.voc.common.Result;
import com.car.voc.common.constant.GlobalConstants;
import com.car.voc.common.util.MD5Util;
import com.car.voc.common.util.RandImageUtil;
import com.car.voc.common.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 *
 * @version 1.0.0
 * @ClassName VerificationCodeController.java
 * @Description TODO
 * @createTime 2022年09月07日 09:23
 * @Copyright voc
 */
@RestController
@RequestMapping("/sys")
@Api(tags = "验证码")
@Slf4j
public class VerificationCodeController {
    @Autowired
    RedisUtil redisUtil;

    /**
     * 后台生成图形验证码 ：有效
     *
     * @param response
     * @param key
     */
    @ApiOperation("获取验证码")
    @CrossOrigin(origins = "http://************:30325")
    @GetMapping(value = "/randomImage/{key}")
    public Result<String> randomImage(HttpServletResponse response, @PathVariable String key) {
        Result<String> res = new Result<String>();
        try {
            String code = RandomUtil.randomString(GlobalConstants.BASE_CHECK_CODES, 4);
            String lowerCaseCode = code.toLowerCase(Locale.ROOT);
            String realKey = MD5Util.MD5Encode(lowerCaseCode + key, "utf-8");
            redisUtil.set(realKey, lowerCaseCode, 60);
            String base64 = RandImageUtil.generate(code);
            res.setSuccess(true);
            res.setResult(base64);
        } catch (Exception e) {
            res.error500("获取验证码出错" + e.getMessage());
            e.printStackTrace();
        }
        return res;
    }
}
