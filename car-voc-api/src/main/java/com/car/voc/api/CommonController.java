package com.car.voc.api;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.oss.OssBootUtil;
import com.car.voc.common.util.CommonUtils;
import com.car.voc.common.util.SvwDate;
import com.car.voc.vo.DictVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

@Slf4j
@RestController
@Api(tags="文件相关")
@RequestMapping("/sys/common")
public class CommonController {


	@Value(value = "${configuration.path.upload}")
	private String uploadpath;

	@Value(value = "${configuration.uploadType}")
	private String uploadType="local";


	@GetMapping("/403")
	public Result<?> noauth()  {
		return Result.error("没有权限，请联系管理员授权");
	}

	/**
	 * 文件上传统一方法
	 * @param request
	 * @param response
	 * @return
	 */
	@ApiOperation(value="文件上传统一方法", notes="文件上传统一方法")
	@PostMapping(value = "/upload")
	public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
		Result<?> result = new Result<>();
		String savePath = "";
		String bizPath = request.getParameter("biz");
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
		if(StrUtil.isEmpty(bizPath)){
			if(CommonConstant.UPLOAD_TYPE_OSS.equals(uploadType)){
				//未指定目录，则用阿里云默认目录 upload
				bizPath = "upload";
				OssBootUtil.upload(file,bizPath);
				//result.setMessage("使用阿里云文件上传时，必须添加目录！");
				//result.setSuccess(false);
				//return result;
			}else{
				bizPath = "";
			}
		}
		if(CommonConstant.UPLOAD_TYPE_LOCAL.equals(uploadType)){
			//针对jeditor编辑器如何使 lcaol模式，采用 base64格式存储
			String jeditor = request.getParameter("jeditor");
			if(StrUtil.isNotEmpty(jeditor)){
				result.setMessage(CommonConstant.UPLOAD_TYPE_LOCAL);
				result.setSuccess(true);
				return result;
			}else{
				savePath = this.uploadLocal(file,bizPath);
			}
		}
		if(StrUtil.isNotEmpty(savePath)){
			result.setMessage(savePath);
			result.setSuccess(true);
		}else {
			result.setMessage("上传失败！");
			result.setSuccess(false);
		}
		return result;
	}

	/*@ApiOperation(value="文件上传统一方法", notes="阿里文件上传统一方法")
	@PostMapping(value = "/upload")
	public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
		Result<String> result = new Result<>();
		String savePath = "";
		String bizPath = request.getParameter("biz");
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
		savePath=OssBootUtil.upload(file,"dfmc-oss/");
		String name=savePath.substring(savePath.lastIndexOf("/")+1);
		result.setResult(name);
		if(StrUtil.isNotEmpty(name)){
			result.setMessage(name);
			result.setSuccess(true);
		}else {
			result.setMessage("上传失败！");
			result.setSuccess(false);
		}
		return result;
	}
*/


/*
	@ApiOperation(value="阿里文件上传统一方法", notes="阿里文件上传统一方法")
	@PostMapping(value = "/uploadOss")
	public Result<?> uploadOss(HttpServletRequest request, HttpServletResponse response) {
		Result<String> result = new Result<>();
		String savePath = "";
		String bizPath = request.getParameter("biz");
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
		savePath=OssBootUtil.upload(file,bizPath);
		result.setResult(savePath);
		if(StrUtil.isNotEmpty(savePath)){
			result.setMessage(savePath);
			result.setSuccess(true);
		}else {
			result.setMessage("上传失败！");
			result.setSuccess(false);
		}
		return result;
	}
*/


	/**
	 * 本地文件上传
	 * @param mf 文件
	 * @param bizPath  自定义路径
	 * @return
	 */
	private String uploadLocal(MultipartFile mf,String bizPath){
		try {
			String ctxPath = uploadpath;
			String fileName = null;
			File file = new File(ctxPath + File.separator + bizPath + File.separator );
			if (!file.exists()) {
				file.mkdirs();// 创建文件根目录
			}
			String orgName = mf.getOriginalFilename();// 获取文件名
			orgName = CommonUtils.getFileName(orgName);
			if(orgName.indexOf(".")!=-1){
				fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.indexOf("."));
			}else{
				fileName = orgName+ "_" + System.currentTimeMillis();
			}
			String savePath = file.getPath() + File.separator + fileName;
			File savefile = new File(savePath);
			FileCopyUtils.copy(mf.getBytes(), savefile);
			String dbpath = null;
			if(StrUtil.isNotEmpty(bizPath)){
				dbpath = bizPath + File.separator + fileName;
			}else{
				dbpath = fileName;
			}
			if (dbpath.contains("\\")) {
				dbpath = dbpath.replace("\\", "/");
			}
			return dbpath;
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
		return "";
	}




	@ApiOperation(value="阿里下载文件", notes="阿里下载文件")
	@GetMapping(value = "/getOssUrl/**")
	public Result<Object> getOssUrl(HttpServletRequest request, HttpServletResponse response) {
		String imgPath = extractPathFromPattern(request);
		if(StrUtil.isEmpty(imgPath) || imgPath=="null"){
			return Result.error("失败！");
		}else {
			return Result.OK(OssBootUtil.getObjectURL(null,imgPath, DateUtil.offsetDay(new DateTime(),+1)));
		}
	}


	@ApiOperation(value="阿里预览文件", notes="阿里预览文件")
	@GetMapping(value = "/getOssView/**")
	public void getOssView(HttpServletRequest request, HttpServletResponse response) {
		// ISO-8859-1 ==> UTF-8 进行编码转换
		String imgPath = extractPathFromPattern(request);
		if(StrUtil.isEmpty(imgPath) || imgPath=="null"){
			return;
		}
		// 其余处理略
		InputStream inputStream = null;
		OutputStream outputStream = null;
		try {
			imgPath = imgPath.replace("..", "");
			if (imgPath.endsWith(",")) {
				imgPath = imgPath.substring(0, imgPath.length() - 1);
			}
			response.setContentType("application/octet-stream");// 设置强制下载不打开
			response.addHeader("Content-Disposition", "attachment;fileName=" + new String(imgPath.substring(imgPath.lastIndexOf("/")+1).getBytes("UTF-8"),"iso-8859-1"));
			inputStream = OssBootUtil.getOssFile(imgPath);
			outputStream = response.getOutputStream();

			byte[] buf = new byte[1024*10];
			int len;
			while ((len = inputStream.read(buf)) > 0) {
				outputStream.write(buf, 0, len);
			}
			response.flushBuffer();

		} catch (IOException e) {
			log.error("预览文件失败" + e.getMessage());
			response.setStatus(404);
			e.printStackTrace();
		}

	}


/*

	@ApiOperation(value="阿里预览文件", notes="阿里预览文件")
	@GetMapping(value = "/static/**")
	public void view(HttpServletRequest request, HttpServletResponse response) {
		// ISO-8859-1 ==> UTF-8 进行编码转换
		String imgPath = extractPathFromPattern(request);
		imgPath="dfmc-oss/"+imgPath;
		if(StrUtil.isEmpty(imgPath) || imgPath=="null"){
			return;
		}
		// 其余处理略
		InputStream inputStream = null;
		OutputStream outputStream = null;
		try {
			imgPath = imgPath.replace("..", "");
			if (imgPath.endsWith(",")) {
				imgPath = imgPath.substring(0, imgPath.length() - 1);
			}
			response.setContentType("application/octet-stream");// 设置强制下载不打开
			response.addHeader("Content-Disposition", "attachment;fileName=" + new String(imgPath.substring(imgPath.lastIndexOf("/")+1).getBytes("UTF-8"),"iso-8859-1"));
			inputStream = OssBootUtil.getOssFile(imgPath);
			outputStream = response.getOutputStream();

			byte[] buf = new byte[1024*10];
			int len;
			while ((len = inputStream.read(buf)) > 0) {
				outputStream.write(buf, 0, len);
			}
			response.flushBuffer();

		} catch (Exception e) {
			log.error("预览文件失败:" +e.getMessage() );
			response.setStatus(404);
//			e.printStackTrace();
		}

	}
*/




	/**
	 * 预览图片&下载文件
	 * 请求地址：http://localhost:8080/common/static/{user/20190119/e1fe9925bc315c60addea1b98eb1cb1349547719_1547866868179.jpg}
	 *
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="预览图片&下载文件", notes="预览图片&下载文件")
	@GetMapping(value = "/static/**")
	public void view(HttpServletRequest request, HttpServletResponse response) {
		// ISO-8859-1 ==> UTF-8 进行编码转换
		String imgPath = extractPathFromPattern(request);
		if(StrUtil.isEmpty(imgPath) || imgPath=="null"){
			return;
		}
		// 其余处理略
		InputStream inputStream = null;
		OutputStream outputStream = null;
		try {
			imgPath = imgPath.replace("..", "");
			if (imgPath.endsWith(",")) {
				imgPath = imgPath.substring(0, imgPath.length() - 1);
			}
			String filePath = uploadpath + File.separator + imgPath;
			File file = new File(filePath);
			if(!file.exists()){
				response.setStatus(404);
				throw new RuntimeException("文件不存在..");
			}
			response.setContentType("application/force-download");// 设置强制下载不打开
			response.addHeader("Content-Disposition", "attachment;fileName=" + new String(file.getName().getBytes("UTF-8"),"iso-8859-1"));

			inputStream = new BufferedInputStream(new FileInputStream(filePath));
			outputStream = response.getOutputStream();
			byte[] buf = new byte[1024];
			int len;
			while ((len = inputStream.read(buf)) > 0) {
				outputStream.write(buf, 0, len);
			}
			response.flushBuffer();
		} catch (IOException e) {
			log.error("预览文件失败" + e.getMessage());
			response.setStatus(404);
			e.printStackTrace();
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					log.error(e.getMessage(), e);
				}
			}
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException e) {
					log.error(e.getMessage(), e);
				}
			}
		}

	}


	/**
	  *  把指定URL后的字符串全部截断当成参数
	  *  这么做是为了防止URL中包含中文或者特殊字符（/等）时，匹配不了的问题
	 * @param request
	 * @return
	 */
	private static String extractPathFromPattern(final HttpServletRequest request) {
		String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
		String bestMatchPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
		return new AntPathMatcher().extractPathWithinPattern(bestMatchPattern, path);
	}

}
