package com.car.voc.api;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CacheConstant;
import com.car.voc.entity.SysDepart;
import com.car.voc.entity.SysDictItem;
import com.car.voc.service.IProvinceAreaService;
import com.car.voc.service.ISysDictItemService;
import com.car.voc.vo.DictVo;
import com.car.voc.vo.EnergyClassificationVo;
import com.car.voc.vo.NewDictVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import wiremock.org.apache.commons.lang3.StringUtils;
import wiremock.org.eclipse.jetty.util.StringUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;


@Api(tags = "数据字典明细")
@RestController
@RequestMapping("/sys/dictItem")
@Slf4j
public class SysDictItemController {

	@Autowired
	private ISysDictItemService sysDictItemService;




	@ApiOperation("所有数据字典")
	@RequestMapping(value = "/sysAllDictItems", method = RequestMethod.POST)
	public Result<JSONObject> sysAllDictItems() {
		return sysDictItemService.sysAllDictItems();
	}


	@ApiOperation(value="根据dictId获取item数据", notes="根据dictId获取item数据")
	@RequestMapping(value = "/getSysDictItemByDictId", method = RequestMethod.GET)
	public Result<?> getSysDictItemByDictId(@RequestParam(name="dictId",required=true) String dictId, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											@RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		Result result = new Result<>();
		if(StrUtil.isBlank(dictId)) {
			result.error500("参数不能为空！");
			return result;
		}
		Result<IPage<SysDictItem>> result1 = new Result<>();
		QueryWrapper<SysDictItem> queryWrapper =new QueryWrapper<>();
		queryWrapper.lambda().eq(SysDictItem::getDictId,dictId);
		queryWrapper.orderByDesc("id");
		Page<SysDictItem> page = new Page<>(pageNo, pageSize);
		IPage<SysDictItem> pageList = sysDictItemService.page(page, queryWrapper);
		result1.setSuccess(true);
		result1.setResult(pageList);
		return result1;
	}


	/**
	 * @功能：查询字典数据
	 * @param sysDictItem
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@ApiOperation(value="查询字典数据列表", notes="查询字典数据列表")
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public Result<IPage<SysDictItem>> queryPageList(SysDictItem sysDictItem,@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,HttpServletRequest req) {
		Result<IPage<SysDictItem>> result = new Result<IPage<SysDictItem>>();
		QueryWrapper<SysDictItem> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().like(StrUtil.isNotBlank(sysDictItem.getItemText()),SysDictItem::getItemText,sysDictItem.getItemText());
		queryWrapper.lambda().eq(!StrUtil.isBlankIfStr(sysDictItem.getStatus()),SysDictItem::getStatus,sysDictItem.getStatus());
		queryWrapper.lambda().eq(StrUtil.isNotBlank(sysDictItem.getDictId()),SysDictItem::getDictId,sysDictItem.getDictId());
		queryWrapper.orderByAsc("sort_order");
		Page<SysDictItem> page = new Page<SysDictItem>(pageNo, pageSize);
		IPage<SysDictItem> pageList = sysDictItemService.page(page, queryWrapper);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}

	@Autowired
	IProvinceAreaService provinceAreaService;
	/**
	 * @功能：查询字典数据
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@ApiOperation(value="查询区域省份列表", notes="查询区域省份列表")
	@RequestMapping(value = "/arealist", method = RequestMethod.GET)
	public Result<Page<SysDictItem>> arealist(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
											   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
											   @RequestParam(name = "brandCode", required = false) String brandCode,
											   HttpServletRequest req) {
		Result<Page<SysDictItem>> result = new Result<>();
		Page<SysDictItem> page = new Page<>(pageNo, pageSize);
		List<NewDictVo> newDictVoList = new ArrayList<>();

		List<NewDictVo> newDictVos = provinceAreaService.newQueryProvinceByAreaCode(brandCode);

		int total = newDictVos.size();
		List<List<NewDictVo>> lists = com.google.common.collect.Lists.partition(newDictVos, pageSize);
		if (pageNo <= lists.size() && pageNo >= 1) {
			newDictVoList = lists.get(pageNo - 1);
		}
		page.setTotal(total);

		List<SysDictItem> sysDictItems = new ArrayList<>();
		for (NewDictVo dictVo:newDictVoList){

			SysDictItem sysDictItem = new SysDictItem();
			sysDictItem.setItemText(dictVo.getItemText());
			sysDictItem.setItemValue(dictVo.getAreaCode());
			if (StringUtils.isNotEmpty(dictVo.getBrandCode())){
				List<String> list = Arrays.asList(dictVo.getBrandCode().split(","));
				sysDictItem.setBrandCodeList(list);
			}
			List<DictVo> provinces = new ArrayList<>();
			String provinceCodes = dictVo.getProvinceCodes();
			String provinceTexts = dictVo.getProvinceTexts();
			if (StringUtils.isNotEmpty(provinceCodes)&&StringUtils.isNotEmpty(provinceTexts)){
				List<String> codes = Arrays.asList(provinceCodes.split(","));
				List<String> texts = Arrays.asList(provinceTexts.split(","));
				for(int i=0;i<codes.size();i++){
					DictVo dictVo1 = new DictVo();
					dictVo1.setText(texts.get(i));
					dictVo1.setValue(codes.get(i));
					provinces.add(dictVo1);
				}
				sysDictItem.setProvinces(provinces);
			}
			sysDictItems.add(sysDictItem);
		}
		page.setRecords(sysDictItems);

		result.setSuccess(true);
		result.setResult(page);
		return result;
	}
	@ApiOperation(value="能源分类", notes="能源分类列表")
	@RequestMapping(value = "/energyClassification", method = RequestMethod.GET)
	public Result<?> energyClassification(HttpServletRequest req) {
		List<EnergyClassificationVo> newDictVoList =sysDictItemService.energyClassification();
		return Result.OK(newDictVoList);
	}
	@ApiOperation(value="能源分类树", notes="能源分类树")
	@RequestMapping(value = "/energyClassificationTree", method = RequestMethod.GET)
	public Result<?> energyClassificationTree(HttpServletRequest req) {
		try {
			long startTime = System.currentTimeMillis();

			// 1. 获取分类数据
			List<EnergyClassificationVo> classifications = sysDictItemService.energyClassification();
			if (CollUtil.isEmpty(classifications)) {
				return Result.OK(Collections.emptyList());
			}

			// 2. 使用TreeMap进行有序分组
			Map<String, List<EnergyClassificationVo>> map = classifications.stream()
					.collect(Collectors.groupingBy(
							EnergyClassificationVo::getType1Name,
							TreeMap::new,  // 使用TreeMap保证key的自然顺序
							Collectors.toList()
					));

			// 3. 构建树结构
			List<DictVo> trees = new ArrayList<>(map.size());

			map.forEach((type1Name, items) -> {
				DictVo parentVo = new DictVo();
				parentVo.setText(type1Name);
				parentVo.setValue(type1Name);

				List<DictVo> children = items.stream()
						.map(item -> {
							DictVo childVo = new DictVo();
							childVo.setText(item.getType2Name());
							childVo.setValue(item.getType2Name());
							return childVo;
						})
						.collect(Collectors.toList());

				parentVo.setChildes(children);
				trees.add(parentVo);
			});
			Collections.reverse(trees);
			long endTime = System.currentTimeMillis();
			log.info("构建能源分类树耗时：{}秒{}毫秒",
					(endTime - startTime) / 1000,
					(endTime - startTime) % 1000);

			return Result.OK(trees);

		} catch (Exception e) {
			log.error("构建能源分类树异常", e);
			return Result.error("构建能源分类树失败：" + e.getMessage());
		}
	}
	/**
	 * @功能：新增
	 * @return
	 */
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@CacheEvict(value= CacheConstant.SYS_DICT_CACHE, allEntries=true)
	public Result<SysDictItem> add(@RequestBody SysDictItem sysDictItem) {
		Result<SysDictItem> result = new Result<SysDictItem>();
		sysDictItem.setId(null);
		try {
			sysDictItem.setCreateTime(new Date());
			sysDictItemService.save(sysDictItem);
			result.success("保存成功！");
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			result.error500("操作失败");
		}
		return result;
	}

	/**
	 * @功能：编辑
	 * @param sysDictItem
	 * @return
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.PUT)
	@CacheEvict(value=CacheConstant.SYS_DICT_CACHE, allEntries=true)
	public Result<SysDictItem> edit(@RequestBody SysDictItem sysDictItem) {
		Result<SysDictItem> result = new Result<SysDictItem>();
		SysDictItem sysdict = sysDictItemService.getById(sysDictItem.getId());
		if(sysdict==null) {
			result.error500("未找到对应实体");
		}else {
			sysDictItem.setUpdateTime(new Date());
			boolean ok = sysDictItemService.updateById(sysDictItem);
			//TODO 返回false说明什么？
			if(ok) {
				result.success("编辑成功!");
			}
		}
		return result;
	}

	/**
	 * @功能：删除字典数据
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.DELETE)
	@CacheEvict(value=CacheConstant.SYS_DICT_CACHE, allEntries=true)
	public Result<SysDictItem> delete(@RequestParam(name="id",required=true) String id) {
		Result<SysDictItem> result = new Result<SysDictItem>();
		SysDictItem joinSystem = sysDictItemService.getById(id);
		if(joinSystem==null) {
			result.error500("未找到对应实体");
		}else {
			boolean ok = sysDictItemService.removeById(id);
			if(ok) {
				result.success("删除成功!");
			}
		}
		return result;
	}

	/**
	 * @功能：批量删除字典数据
	 * @param ids
	 * @return
	 */
	@RequestMapping(value = "/deleteBatch", method = RequestMethod.DELETE)
	@CacheEvict(value= CacheConstant.SYS_DICT_CACHE, allEntries=true)
	public Result<SysDictItem> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		Result<SysDictItem> result = new Result<SysDictItem>();
		if(ids==null || "".equals(ids.trim())) {
			result.error500("参数不识别！");
		}else {
			this.sysDictItemService.removeByIds(Arrays.asList(ids.split(",")));
			result.success("删除成功!");
		}
		return result;
	}



}
