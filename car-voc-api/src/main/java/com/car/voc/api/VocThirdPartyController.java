package com.car.voc.api;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.exceptions.ApiException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.common.util.RedisUtil;
import com.car.voc.common.util.SqlInjectionUtils;
import com.car.voc.model.VocBusinessTagListQueryModel;
import com.car.voc.service.IVocBusinessTagService;
import com.car.voc.vo.thirdVo.PageVocTag;
import com.car.voc.vo.thirdVo.VocTagVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.util.Objects;


@RestController
@RequestMapping("/third-party")
@Api(tags = "voc对外提供接口")
@Slf4j
public class VocThirdPartyController {
    @Autowired
    RedisUtil redisUtil;
    @Value("${configuration.thirdPartyToken}")
    String thirdPartyToken;
    @Autowired
    private IVocBusinessTagService vocBusinessTagService;

    @ApiOperation(value = "voc标签体系", notes = "voc标签体系查询")
    @GetMapping(value = "/vocTags")
    public Result<?> vocTags(
            @ApiParam(value = "搜索关键词", example = "体验")
            @RequestParam(name = "searchKeyword" ,required = false)
            String searchKeyword,
            @Min(value = 1, message = "页码必须大于0")
            @ApiParam(value = "页码", example = "1")
            @RequestParam(name = "pageNo", defaultValue = "1",required = false) Integer pageNo,
            @Min(value = 1, message = "每页条数必须大于0")
            @ApiParam(value = "每页条数", example = "10")
            @RequestParam(name = "pageSize", defaultValue = "10",required = false) Integer pageSize,
            HttpServletRequest req) {
        String token = req.getHeader(CommonConstant.X_ACCESS_TOKEN);
        if (StrUtil.isBlankIfStr(token) || !Objects.equals(thirdPartyToken, token)) {
            return Result.error("无效的访问令牌");
        }
        // 检查SQL注入
        if (StrUtil.isNotBlank(searchKeyword) && !SqlInjectionUtils.checkSqlInjection(searchKeyword)) {
            return Result.error("检测到SQL注入风险!");
        }
        // 验证分页参数
        if (pageNo < 1 || pageSize < 1) {
            return Result.error("非法的分页参数");
        }
        VocBusinessTagListQueryModel vocBusinessTag = new VocBusinessTagListQueryModel();
        if (StrUtil.isNotBlank(searchKeyword)) {
            String cleanInput = SqlInjectionUtils.cleanSqlInject(searchKeyword);
            cleanInput = SqlInjectionUtils.escapeSql(cleanInput);
            cleanInput = SqlInjectionUtils.safeLike(cleanInput);
            vocBusinessTag.setSearchKeyword(cleanInput);
        }
        Page<VocTagVo> page = new Page<>(pageNo, pageSize);
        IPage<VocTagVo> voIPage = vocBusinessTagService.vocTags(page, vocBusinessTag, req);
        PageVocTag re=new PageVocTag(voIPage);
        return Result.OK(re);
    }

}
