package com.car.voc.api;

import com.car.voc.common.Result;
import com.car.voc.model.TokenCheckTokenVaildModel;
import com.car.voc.service.ISysDictService;
import com.car.voc.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cc")
@Api(tags = "a-第三方调用接口")
public class CCController {

    @Value("${spring.profiles.active}")
    private String active;


    @Autowired
    private ISysUserService sysUserService;

    @Value("${configuration.tokenCheckToken.ccDictData}")
    private String ccDictData;

    @Autowired
    ISysDictService dictService;

    @ApiOperation("获取字典数据")
    @RequestMapping(value = "/dataSources", method = RequestMethod.POST)
    public Result<?> dataSources(@RequestBody TokenCheckTokenVaildModel checkTokenValid) {
        List<String> split = Arrays.asList(ccDictData.split(","));
        if ("test".equals(active) || "pord".equals(active)) {
            return sysUserService.dataSources(checkTokenValid,split);
        }
        return Result.OK(dictService.getCcList(split));
    }

}
