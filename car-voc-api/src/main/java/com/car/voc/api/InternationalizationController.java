package com.car.voc.api;

import com.car.voc.common.Result;
import com.car.voc.service.InternationalSerVice;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @version 1.0.0
 * @ClassName InternationalizationController.java
 * @Description TODO
 * @createTime 2023年01月09日 14:59
 * @Copyright voc
 */

@Slf4j
@RestController
@Api(tags = "国际化映射接口")
@RequestMapping("/sys/common/international")
public class InternationalizationController {

    @Resource
    InternationalSerVice internationalSerVice;

    @ApiOperation(value = "国际化", notes = "国际化")
    @RequestMapping(value = "/internationalAll", method = RequestMethod.GET)
    public Result<?> internationalList() {
        return Result.OK(internationalSerVice.internationalList());
    }

}
