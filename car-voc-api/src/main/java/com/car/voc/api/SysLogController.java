package com.car.voc.api;


import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.car.voc.common.Result;
import com.car.voc.common.util.SpringContextUtils;
import com.car.voc.entity.SysLog;
import com.car.voc.entity.SysRole;
import com.car.voc.model.LoginUser;
import com.car.voc.service.ISysLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;

/**
 * <p>
 * 系统日志表 前端控制器
 * </p>
 *
 * @since 2018-12-26
 */
@RestController
@Api(tags = "系统操作日志")
@RequestMapping("/sys/log")
@Slf4j
public class SysLogController {

    @Autowired
    private ISysLogService sysLogService;

//	/**
//	 * @功能：查询日志记录
//	 * @param syslog
//	 * @param pageNo
//	 * @param pageSize
//	 * @param req
//	 * @return
//	 */
//	@ApiOperation(value="查询日志记录", notes="查询日志记录")
//	@RequestMapping(value = "/list_old", method = RequestMethod.GET)
//	public Result<IPage<SysLog>> queryPageList(SysLog syslog, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//											   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletRequest req) {
//		Result<IPage<SysLog>> result = new Result<IPage<SysLog>>();
//		QueryWrapper<SysLog> queryWrapper = new QueryWrapper<>();
//		Page<SysLog> page = new Page<SysLog>(pageNo, pageSize);
//		queryWrapper.lambda().ne(SysLog::getUsername,"dfmc-admin");
//		queryWrapper.lambda().eq(StrUtil.isNotBlank(syslog.getUserid()),SysLog::getUserid,syslog.getUserid());
//		queryWrapper.lambda().apply((syslog.getStartDate()!=null&&syslog.getEndDate()!=null),"(slog.create_time BETWEEN {0} AND {1})", syslog.getStartDate(),syslog.getEndDate());
//		queryWrapper.lambda().apply((syslog.getDepartid()!=null&&syslog.getDepartid()!=""),"(sud.DEP_ID= {0})", syslog.getDepartid());
//		queryWrapper.lambda().orderByDesc(SysLog::getCreateTime);
//
//		//创建时间/创建人的赋值
//		IPage<SysLog> pageList = sysLogService.queryPageList(page, queryWrapper);
//		result.setSuccess(true);
//		result.setResult(pageList);
//		return result;
//	}


    /**
     * @param syslog
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     * @功能：查询日志记录
     */
    @ApiOperation(value = "查询日志记录", notes = "查询日志记录")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<SysLog>> newQueryPageList(SysLog syslog, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
        Result<IPage<SysLog>> result = new Result<IPage<SysLog>>();
        Page<SysLog> page = new Page<SysLog>(pageNo, pageSize);
        page.setSize(pageSize);
        IPage<SysLog> pageList = sysLogService.queryPageList(page, syslog);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    @ApiOperation(value = "查询日志记录导出", notes = "查询日志记录导出")
    @RequestMapping(value = "/listExportXls", method = RequestMethod.GET)
    public void newListExportXls(SysLog syslog, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletResponse response, HttpServletRequest req) {
        Page<SysLog> page = new Page<>(pageNo, 5000);
        //创建时间/创建人的赋值
        IPage<SysLog> pageList = sysLogService.queryPageList(page, syslog);
        response.setCharacterEncoding("utf-8");
        String fileName;
        try {
            fileName = URLEncoder.encode("日志记录", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), SysLog.class).excelType(ExcelTypeEnum.XLSX).sheet("日志记录").doWrite(pageList.getRecords());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


//	@ApiOperation(value="查询日志记录导出", notes="查询日志记录导出")
//	@RequestMapping(value = "/listExportXls_old", method = RequestMethod.GET)
//	public void listExportXls(SysLog syslog, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//							  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize, HttpServletResponse response, HttpServletRequest req) {
//		QueryWrapper<SysLog> queryWrapper = new QueryWrapper<>();
//		Page<SysLog> page = new Page<>(pageNo, 5000);
//		queryWrapper.lambda().eq(StrUtil.isNotBlank(syslog.getUserid()),SysLog::getUserid,syslog.getUserid());
//		queryWrapper.lambda().apply((syslog.getStartDate()!=null&&syslog.getEndDate()!=null),"(slog.create_time BETWEEN {0} AND {1})", syslog.getStartDate(),syslog.getEndDate());
//		queryWrapper.lambda().apply((syslog.getDepartid()!=null&&syslog.getDepartid()!=""),"(sud.DEP_ID= {0})", syslog.getDepartid());
//		queryWrapper.lambda().orderByDesc(SysLog::getCreateTime);
//        queryWrapper.lambda().ne(SysLog::getUsername,"dfmc-admin");
//        //创建时间/创建人的赋值
//		IPage<SysLog> pageList = sysLogService.queryPageList(page, queryWrapper);
//		response.setCharacterEncoding("utf-8");
//		String fileName;
//		try {
//			fileName = URLEncoder.encode("日志记录", "UTF-8").replaceAll("\\+", "%20");
//			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
//			EasyExcel.write(response.getOutputStream(),SysLog.class).excelType(ExcelTypeEnum.XLSX).sheet("日志记录").doWrite(pageList.getRecords());
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//	}


    /**
     * @param id
     * @return
     * @功能：删除单个日志记录
     */
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public Result<SysLog> delete(@RequestParam(name = "id", required = true) String id) {
        Result<SysLog> result = new Result<SysLog>();
        SysLog sysLog = sysLogService.getById(id);
        if (sysLog == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = sysLogService.removeById(id);
            if (ok) {
                result.success("删除成功!");
            }
        }
        return result;
    }

    /**
     * @param ids
     * @return
     * @功能：批量，全部清空日志记录
     */
    @RequestMapping(value = "/deleteBatch", method = RequestMethod.DELETE)
    public Result<SysRole> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<SysRole> result = new Result<SysRole>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            if ("allclear".equals(ids)) {
                this.sysLogService.removeAll();
                result.success("清除成功!");
            }
            this.sysLogService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    @RequestMapping(value = "/save", method = RequestMethod.GET)
    public Result<SysLog> save(@RequestParam(name = "content", required = true) String content) {
        Result<SysLog> result = new Result<SysLog>();
        SysLog sysLog = new SysLog();
        sysLog.setLogContent(content);
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        sysLog.setIp(ServletUtil.getClientIP(request));
        //获取登录用户信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            sysLog.setUserid(sysUser.getId());
            sysLog.setUsername(sysUser.getUsername());

        }
        sysLog.setCreateTime(new Date());
        sysLog.setLogType(2);
        sysLogService.save(sysLog);
        result.success("成功");
        return result;
    }

}
