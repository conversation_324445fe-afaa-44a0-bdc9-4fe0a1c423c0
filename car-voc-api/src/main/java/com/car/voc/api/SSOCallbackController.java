package com.car.voc.api;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.car.voc.common.Result;
import com.car.voc.common.constant.CommonConstant;
import com.car.voc.entity.SysUser;
import com.car.voc.service.ISysUserService;
import io.netty.util.CharsetUtil;
import io.swagger.annotations.Api;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SSOCallbackController.java
 * @Description TODO
 * @createTime 2023年08月09日 14:38
 * @Copyright voc
 */

@Slf4j
@RestController
@Api(tags = "接收工作平台打开时传递token的接口")
@RequestMapping("/sso")
@Data
public class SSOCallbackController {

    @Value("${configuration.sso.platform-code}")
    String platformCode;
    @Value("${configuration.sso.SSOExchangeUrl}")
    String SSOExchangeUrl;
    @Value("${configuration.sso.rediectUrl}")
    String rediectUrl;
    @Value("${configuration.sso.rediectErrorUrl}")
    String rediectErrorUrl;
    @Autowired
    ISysUserService sysUserService;

    @GetMapping("/login/callback")
    public void SSOLoginCallback(String token, HttpServletResponse response) {
        log.error("token:"+token);
        String msg="%E7%99%BB%E5%BD%95%E5%A4%B1%E8%B4%A5";
        try {
             msg=URLEncoder.encode("没有登录权限，如需登录，请联系管理员开通账号权限", "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if (StrUtil.isNotBlank(token)) {
            RestTemplate restTemplate = new RestTemplate();
            JSONObject ssorep = null;
            String re=null;
            try {
                re= HttpUtil.get(SSOExchangeUrl+token);
                log.error("请求sso认证中心校验token返回数据1:"+re);
                ssorep = restTemplate.getForObject(SSOExchangeUrl + token, JSONObject.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(ssorep)){
                log.error("请求sso认证中心校验token返回数据2:"+ssorep!=null?ssorep.toString():"null");
            }

            if (ssorep != null) {
                    if ("S_0000".equals(ssorep.getStr("key"))) {
                        JSONObject userInfo = ssorep.getJSONObject("result");
                        String userCode = userInfo.getStr("user_code");
                        String userName = userInfo.getStr("user_name");
                        Result checkSystemPermissions = this.checkSystenUserPermissions((userCode));
                        if (checkSystemPermissions.getCode().equals(CommonConstant.SC_OK_200)) {
                           Result<JSONObject> result=checkSystemPermissions;
                           String ken=result.getResult().getStr("token");
                            try {
                                response.sendRedirect(rediectUrl+ken);
                                return;
                            } catch (IOException e) {
                                e.printStackTrace();
                            }

                        }else {

                            try {
                                response.sendRedirect(rediectErrorUrl+msg);
                                return;
                            } catch (IOException e) {
                                e.printStackTrace();
                            }


                        }
                    }

            }


        }

        try {
            response.sendRedirect(rediectErrorUrl+msg);
            return;
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public static void main(String[] args) {
//        System.out.println(URLUtil.encode("http://10.250.11.40:9996/#/login?error=登录失败", CharsetUtil.UTF_8));
        try {
            System.out.println(URLEncoder.encode("没有登录权限，如需登录，请联系管理员开通账号权限", "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    private Result<?> checkSystenUserPermissions(String userCode) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, userCode);
        queryWrapper.eq(SysUser::getStatus,1);
        queryWrapper.eq(SysUser::getDelFlag,0);
        SysUser sysUser = sysUserService.getOne(queryWrapper);
        if (sysUser==null){
            return Result.error(5003,"工号不存在！");
        }
        Result<JSONObject> result=new Result<>();
        return sysUserService.userInfo(sysUser,result);
    }
}
