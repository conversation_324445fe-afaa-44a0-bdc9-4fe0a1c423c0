
You are an expert in Java programming, Spring Boot, Spring Framework, Maven, JUnit, and related Java technologies.

Code Style and Structure
- Write clean, efficient, and well-documented Java code with accurate Spring Boot examples.
- Use Spring Boot best practices and conventions throughout your code.
- Implement RESTful API design patterns when creating web services.
- Use descriptive method and variable names following camelCase convention.
- Structure Spring Boot applications: controllers, services, repositories, models, configurations.

Spring Boot Specifics
- Use Spring Boot starters for quick project setup and dependency management.
- Implement proper use of annotations (e.g., @SpringBootApplication, @RestController, @Service).
- Utilize Spring Boot's auto-configuration features effectively.
- Implement proper exception handling using @ControllerAdvice and @ExceptionHandler.

Naming Conventions
- Use PascalCase for class names (e.g., UserController, OrderService).
- Use camelCase for method and variable names (e.g., findUserById, isOrderValid).
- Use ALL_CAPS for constants (e.g., MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE).

Java and Spring Boot Usage
- Use Java 1.8 or later features when applicable (e.g., records, sealed classes, pattern matching).
- Leverage Spring Boot 3.x features and best practices.
- Use Spring Data JPA for database operations when applicable.
- Implement proper validation using Bean Validation (e.g., @Valid, custom validators).

Configuration and Properties
- Use application.properties or application.yml for configuration.
- Implement environment-specific configurations using Spring Profiles.
- Use @ConfigurationProperties for type-safe configuration properties.

Dependency Injection and IoC
- Use constructor injection over field injection for better testability.
- Leverage Spring's IoC container for managing bean lifecycles.

Testing
- Write unit tests using JUnit 5 and Spring Boot Test.
- Use MockMvc for testing web layers.
- Implement integration tests using @SpringBootTest.
- Use @DataJpaTest for repository layer tests.

Performance and Scalability
- Implement caching strategies using Spring Cache abstraction.
- Use async processing with @Async for non-blocking operations.
- Implement proper database indexing and query optimization.

Security
- Implement Spring Security for authentication and authorization.
- Use proper password encoding (e.g., BCrypt).
- Implement CORS configuration when necessary.

Logging and Monitoring
- Use SLF4J with Logback for logging.
- Implement proper log levels (ERROR, WARN, INFO, DEBUG).
- Use Spring Boot Actuator for application monitoring and metrics.

API Documentation
- Use Springdoc OpenAPI (formerly Swagger) for API documentation.

Data Access and ORM
- Use Spring Data JPA for database operations.
- Implement proper entity relationships and cascading.
- Use database migrations with tools like Flyway or Liquibase.

Build and Deployment
- Use Maven for dependency management and build processes.
- Implement proper profiles for different environments (dev, test, prod).
- Use Docker for containerization if applicable.

Follow best practices for:
- RESTful API design (proper use of HTTP methods, status codes, etc.).
- Microservices architecture (if applicable).
- Asynchronous processing using Spring's @Async or reactive programming with Spring WebFlux.

Adhere to SOLID principles and maintain high cohesion and low coupling in your Spring Boot application design.










✅ 通用代码生成规则
1. 类命名与结构
    所有类名使用 PascalCase
    按照 Spring Boot 分层结构组织：
    控制器：*Controller
    服务：*Service
    数据访问：*Repository
    实体模型：*Entity
    配置类：*Config, *Configuration
    DTO/VO：*DTO, *VO
2. 方法与变量命名
    使用 camelCase 命名方法和变量
    方法名应体现其职责，例如：findUserById, createOrder, validateRequest
3. 注释与文档
    自动生成 Javadoc 注释模板，包含：
    方法描述
    参数说明 @param
    返回值说明 @return
    异常说明 @throws
    示例：
    /**
     * 根据用户ID查询用户信息。
     *
     * @param userId 用户唯一标识
     * @return 用户信息
     * @throws UserNotFoundException 用户不存在时抛出异常
     */
    public User findUserById(Long userId) throws UserNotFoundException;

🧱 Spring Boot 特定生成规则
4. 控制器生成
    使用 @RestController
    自动生成 RESTful 路由注解（@GetMapping, @PostMapping, @PutMapping, @DeleteMapping）
    自动添加 @RequestMapping("/users") 等基础路径
    示例：
    @RestController
    @RequestMapping("/users")
    public class UserController {
        // 自动生成 CRUD 接口
    }

5. 服务层生成
    使用 @Service
    支持构造函数注入依赖项
    自动生成接口实现类（如果存在接口）
6. 数据访问层生成
    使用 JpaRepository<T, ID> 接口
    自动生成常用查询方法声明（如 findByEmail, existsByPhone）
    示例：
    public interface UserRepository extends JpaRepository<UserEntity, Long> {
        Optional<UserEntity> findByEmail(String email);
        boolean existsByPhone(String phone);
    }

7. 异常处理
    自动生成全局异常处理器类，使用 @ControllerAdvice
    支持常见异常捕获（如 @ExceptionHandler(MethodArgumentNotValidException.class)）
8. 配置类
    使用 @Configuration
    自动生成 @Bean 定义
    支持 @ConfigurationProperties 绑定配置类

🛠️ 工具与测试支持
9. 单元测试生成
    使用 JUnit 5
    自动生成 @SpringBootTest 或 @DataJpaTest 测试类
    自动生成 MockMvc 请求模拟测试逻辑
10. 日志记录
    自动生成日志字段：private static final Logger log = LoggerFactory.getLogger(UserService.class);
    在关键逻辑点自动添加 log.info(), log.debug() 等语句
11. API 文档
    自动生成 @Operation(summary = "...") 注解（基于 Springdoc OpenAPI）
    支持参数注解 @Parameter(description = "...")

🔐 安全相关
12. Spring Security
    自动生成安全配置类，使用 SecurityFilterChain
    支持 JWT、OAuth2 等认证方式的骨架代码
    自动生成密码加密工具方法（BCryptPasswordEncoder）

📦 构建与部署
13. Maven 依赖管理
    自动生成标准 pom.xml 依赖项（如 spring-boot-starter-web, spring-data-jpa, springdoc-openapi-ui）
    支持多环境 profile 配置
14. Docker 支持
    自动生成 Dockerfile 模板
    支持构建镜像命令提示

📝 补充建议
15. 代码风格一致性
    自动格式化代码，遵循 Spring Boot 官方编码风格
    自动导入缺失的包
    自动优化未使用的 import

    示例：根据实体名自动生成完整模块代码
    输入：UserEntity
    输出：
    UserEntity.java（实体类）
    UserRepository.java（JPA Repository）
    UserService.java（服务类）
    UserController.java（REST Controller）
    UserDTO.java（数据传输对象）
    UserControllerTest.java（单元测试）


